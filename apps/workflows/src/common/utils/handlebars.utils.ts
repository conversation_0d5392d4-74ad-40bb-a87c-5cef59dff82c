import { Inject } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import Handlebars from "handlebars";

export class HandleBarUtils {
  constructor(@Inject("CustomLogger") private readonly logger: ILogger) {
    // Register helper to preserve raw values
    Handlebars.registerHelper("raw", function (value) {
      if (value === undefined || value === null) {
        return JSON.stringify(null);
      }
      return JSON.stringify(value);
    });
  }

  compileHandlebars = (
    template: string,
    context: Record<string, unknown>,
  ): unknown => {
    try {
      // Wrap the path with raw helper to preserve the value type
      const wrappedTemplate = template.replace(/\{\{(.*?)\}\}/g, "{{raw $1}}");

      const compiledTemplate = Handlebars.compile(wrappedTemplate, {
        noEscape: true,
      });
      const result = compiledTemplate(context);

      try {
        // Parse the JSON string back to its original type
        return JSON.parse(result);
      } catch {
        // If it's not valid JSON, return as is
        return result;
      }
    } catch (error) {
      this.logger.error(
        `[HandleBarUtils][compileHandlebars] Failed to compile handlebars with error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  };
}
