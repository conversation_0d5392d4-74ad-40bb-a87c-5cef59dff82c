import {
  Inject,
  Injectable,
  OnM<PERSON>ule<PERSON><PERSON>roy,
  OnModuleInit,
} from "@nestjs/common";
import { BaseGrpcClient } from "@repo/nestjs-commons/grpc";
import { ILogger } from "@repo/nestjs-commons/logger";
import { users } from "@repo/shared-proto";
import { ConfigKeys, ConfigService } from "../../config/config.service";

@Injectable()
export class UsersGrpcClient
  extends BaseGrpcClient
  implements OnModuleInit, OnModuleDestroy
{
  constructor(
    @Inject("CustomLogger") logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    super(logger, "users");
  }

  onModuleInit() {
    this.initializeClient("users", users.USER_SERVICE_NAME);
  }

  protected getServiceUrl(): string {
    return this.configService.get(ConfigKeys.PLATFORM_GRPC_SERVICE_URL);
  }

  fetchWorkflowBot(
    userId: string,
    orgId: string,
  ): Promise<users.FetchWorkflowBotResponse> {
    return this.makeGrpcRequest<users.Empty, users.FetchWorkflowBotResponse>(
      "FetchWorkflowBot",
      {},
      {
        user_id: userId,
        org_id: orgId,
      },
    );
  }
}
