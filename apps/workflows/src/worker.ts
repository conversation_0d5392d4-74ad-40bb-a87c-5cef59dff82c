import dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: ".env" });

import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import type { NestFastifyApplication } from "@nestjs/platform-fastify";
import { FastifyAdapter } from "@nestjs/platform-fastify";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import {
  DatabaseExceptionFilter,
  HttpExceptionFilter,
  SENTRY_SERVICE_TOKEN,
  SentryExceptionFilter,
} from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import * as Sentry from "@sentry/node";
import * as rTracer from "cls-rtracer";
import cors from "cors";
import pino from "pino";
import { v4 as uuidv4 } from "uuid";
import { WorkerModule } from "./worker.module";

async function bootstrap() {
  try {
    const app = await NestFactory.create<NestFastifyApplication>(
      WorkerModule,
      new FastifyAdapter({
        logger: {
          level: "info",
          base: { app: process.env.APP_TAG, service: process.env.SERVICE_TAG },
          timestamp: pino.stdTimeFunctions.isoTime,
        },
        trustProxy: ["loopback"],
        genReqId: () => uuidv4(),
      }),
      { bufferLogs: true },
    );

    // Initialize Sentry
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
    });

    const logger = app.get<ILogger>("CustomLogger");

    app.useLogger(logger);

    // Add validation pipe to the app
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    app.useGlobalFilters(
      new HttpExceptionFilter(),
      new DatabaseExceptionFilter(),
    );

    app.use(
      rTracer.fastifyMiddleware({
        echoHeader: true,
        requestIdFactory: (req) => ({
          reqId: req.id,
          context: {},
        }),
      }),
    );

    // Register Sentry Exception Filter
    const sentryService = app.get(SENTRY_SERVICE_TOKEN);
    app.useGlobalFilters(new SentryExceptionFilter(sentryService));

    // Enable CORS with default options
    app.use(cors());

    // Start listening to server termination signals
    app.enableShutdownHooks();

    // Alternatively, customize CORS options
    app.enableCors({
      origin: "*", // your frontend URLs
      methods: ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE"],
      allowedHeaders: "*",
      credentials: true,
    });

    // Add Swagger documentation to the app
    const swaggerConfig = new DocumentBuilder()
      .setTitle("Thena Platform")
      .setDescription("The Thena Platform API description")
      .setVersion("1.0")
      .addTag("Thena Platform APIs")
      .build();

    // To check swagger head over to your configured base url and add `/docs` to it
    // Example: http://localhost:8000/docs
    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup("docs", app, document);

    await app.listen(
      { port: parseInt(process.env.WORKER_PORT || "8002"), host: "0.0.0.0" },
      (err, address) => {
        if (err) {
          process.exit(1);
        }

        logger.log(`Server listening at ${address}`);
      },
    );

    [
      "beforeExit",
      "uncaughtException",
      "unhandledRejection",
      "rejectionHandled",
      "exit",
    ].forEach((event) => {
      process.on(event, (error) => {
        logger.error(
          `🚨🚨🚨 Mayday mayday! Received a ${event} signal! Initiating graceful shutdown`,
        );
        logger.error(
          `Shutting down due to Error message: ${error.message}, Error stack: ${error.stack}`,
        );
      });
    });

    const signals = ["SIGTERM", "SIGINT"];
    signals.forEach((signal) => {
      process.on(signal, async () => {
        logger.log(
          `🚨🚨🚨 Mayday mayday! Received ${signal}. Starting graceful shutdown...`,
        );

        try {
          await app.close();
          logger.log("Application gracefully closed.");
          process.exit(0);
        } catch (error) {
          if (error instanceof Error) {
            logger.error(
              `Error during graceful shutdown: ${error.message}, Error stack: ${error.stack}`,
            );
          }

          process.exit(1);
        }
      });
    });
  } catch (error) {
    if (error instanceof Error) {
      console.error(
        "Failed to start the application:",
        error.message,
        error.stack,
      );
    }

    process.exit(1);
  }
}

bootstrap();
