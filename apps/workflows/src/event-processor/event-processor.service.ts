import { Inject, Injectable } from "@nestjs/common";
import { SQSMessage } from "@repo/nestjs-commons/aws-utils/sqs";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  Workflow,
  WorkflowEvent,
  WorkflowEventLog,
  WorkflowInstanceStatusEnum,
} from "@repo/thena-platform-entities";
import { AppsPlatformEvents } from "@repo/thena-shared-interfaces";
import { WorkflowClientService } from "../client/client.service";
import { POSTGRES_ERROR_CODES } from "../common/constants/postgres-errors.constants";
import { WORKFLOW_MANUAL_TRIGGER_EVENT } from "../workflows/core/events/constants";
import { WorkflowInstanceService } from "../workflows/core/workflow-instance/workflow-instance.service";
import { WorkflowSeeder } from "../workflows/seeds/workflow.seeder";
import { EventLogActionService } from "../workflows/services/event-log.action.service";
import { EventActionService } from "../workflows/services/event.action.service";
import { WorkflowInstanceActionService } from "../workflows/services/workflow-instance.action.service";
import { WorkflowActionService } from "../workflows/services/workflow.action.service";
import { EventAnnotatorService } from "./services/event-annotator.service";
import { EventFilterService } from "./services/event-filter.service";
import { EventValidatorService } from "./services/event-validator.service";

/**
 * Service responsible for processing workflow events from SQS messages.
 * - Validates and processes incoming event messages
 * - Performs event schema validation and annotation
 * - Identifies and filters matching workflows
 * - Manages workflow instance creation and execution
 * - Handles duplicate events
 *
 * @export
 * @class EventProcessorService
 */
@Injectable()
export class EventProcessorService {
  private spanId: string;
  private workflowInstanceService: WorkflowInstanceService;

  constructor(
    private eventValidatorService: EventValidatorService,
    private eventAnnotatorService: EventAnnotatorService,
    private eventFilterService: EventFilterService,

    private eventActionService: EventActionService,
    private eventLogActionService: EventLogActionService,
    private workflowActionService: WorkflowActionService,
    private workflowInstanceActionService: WorkflowInstanceActionService,

    private workflowSeeder: WorkflowSeeder,

    private workflowClientService: WorkflowClientService,

    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {
    this.spanId = `[EventProcessorService]`;
    // Create a single reusable WorkflowInstanceService instance
    this.workflowInstanceService = new WorkflowInstanceService(
      this.workflowInstanceActionService,
      this.logger,
      this.sentryService,
    );
  }

  /**
   * Extracts event type and organization ID from message attributes.
   * @private
   * @param {SQSMessage} message - The SQS message containing event information
   * @returns {{ eventType: string; organizationId: string }} Extracted event metadata
   */
  private extractEventTypeAndOrganization(message: SQSMessage): {
    eventType: string;
    organizationId: string;
  } {
    return {
      eventType: message.messageAttributes.event_name,
      organizationId: message.messageAttributes
        ?.context_organization_id as string,
    };
  }

  /**
   * Retrieves event configuration from the registry.
   * @private
   * @param {string} eventType - The type of event to look up
   * @returns {Promise<WorkflowEvent>} Trigger event from the registry
   * @throws {Error} If event lookup fails
   */
  private async findEventFromRegistry(
    eventType: string,
    organizationId: string,
  ): Promise<WorkflowEvent> {
    try {
      return await this.eventActionService.findEventByEventType(
        eventType,
        organizationId,
      );
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to find event registry for this message > Error message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Creates or retrieves an event log entry.
   * @private
   * @param {ParsedSQSMessage} message - The parsed event message
   * @param {WorkflowEvent} event - The trigger event
   * @returns {Promise<WorkflowEventLog>} Created or existing event log
   * @throws {Error} If event log creation fails
   */
  private async createEventLog(
    message: SQSMessage,
    event: WorkflowEvent,
  ): Promise<WorkflowEventLog> {
    try {
      return await this.eventLogActionService.createEventLog({
        eventId: event.id,
        eventTraceIdentifier: message.messageAttributes.event_id,
        eventData: message as unknown as Record<string, unknown>,
        eventTimestamp: new Date(
          parseInt(message.messageAttributes.event_timestamp as string),
        ),
        organizationId: message.messageAttributes
          .context_organization_id as string,
      });
    } catch (error) {
      if (error?.code === POSTGRES_ERROR_CODES.DUPLICATE_ENTRY) {
        // PostgreSQL duplicate entry on event trace identifier
        this.logger.warn(
          `${this.spanId} Duplicate event trace identifier detected: ${message.messageAttributes.event_id}`,
        );
        return await this.eventLogActionService.findEventLogByTraceId(
          message.messageAttributes.event_id,
        );
      }
      this.logger.error(
        `${this.spanId} Failed to log the event > Error message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validates event data against its schema definition.
   * @private
   * @param {WorkflowEvent} event - The trigger event
   * @param {WorkflowEventLog} eventLog - The event log containing data to validate
   * @throws {Error} If validation fails
   */
  private validateEventSchema(
    event: WorkflowEvent,
    eventLog: WorkflowEventLog,
  ) {
    const { isValid, error } = this.eventValidatorService.validateEventSchema(
      event,
      eventLog,
    );
    if (!isValid) {
      this.logger.error(
        `${this.spanId} Invalid event schema. Failed with error - ${error}`,
      );
      throw error;
    }
  }

  /**
   * Annotates event data with additional context.
   * @private
   * @param {Workflow} workflow - The workflow
   * @param {WorkflowEvent} event - The trigger event
   * @param {WorkflowEventLog} eventLog - The event log to annotate
   * @returns {Record<string, unknown>} Annotated event data
   * @throws {Error} If annotation fails
   */
  private async annotateEventData(
    workflow: Workflow,
    event: WorkflowEvent,
    eventLog: WorkflowEventLog,
  ): Promise<Record<string, unknown>> {
    try {
      return await this.eventAnnotatorService.annotateEventData(
        workflow,
        event,
        eventLog,
      );
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to annotate event data > Error message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Finds workflows that match the event trigger.
   * @private
   * @param {WorkflowEvent} event - The trigger event
   * @param {WorkflowEventLog} eventLog - The event log
   * @returns {Promise<Workflow[]>} Array of matching workflows
   * @throws {Error} If workflow lookup fails
   */
  private async findMatchingWorkflows(
    event: WorkflowEvent,
    eventLog: WorkflowEventLog,
  ): Promise<Workflow[]> {
    try {
      // Handle manual trigger event.
      if (event.eventType === WORKFLOW_MANUAL_TRIGGER_EVENT) {
        return [
          await this.workflowActionService.findWorkflowByUID(
            eventLog.eventData.workflowUID as string,
          ),
        ];
      }

      return await this.workflowActionService.findActiveWorkflowsWithTriggerEvent(
        event.id,
        eventLog.organizationId,
      );
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to find workflows for trigger event - ${event.id} > Error message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validates event data against workflow filters.
   * @private
   * @param {Record<string, unknown>} workflowContext - The workflow context
   * @param {Workflow} workflow - The workflow configuration with filters
   * @returns {boolean} Whether the event matches the workflow filters
   */
  private validateEventFilters(
    workflowContext: Record<string, unknown>,
    workflow: Workflow,
  ): boolean {
    try {
      return this.eventFilterService.validateEventFilters(
        workflowContext,
        workflow.filters,
      );
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to validate event filters. Workflow id: ${workflow.id} > Error message: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Checks if the event type is an apps platform event.
   *
   * This is need for seeding of workflows at the time of app installation.
   *
   * @param {string} eventType - The event type
   * @returns {boolean} Whether the event type is an apps platform event
   */
  private isAppsPlatformEvent(eventType: string): boolean {
    return Object.values(AppsPlatformEvents).includes(
      eventType as AppsPlatformEvents,
    );
  }

  /**
   * Processes apps platform events.
   * Seeds workflows on app installation and on new team addition.
   * @param {string} eventType - The event type
   * @param {string} organizationId - The organization id
   * @param {Record<string, unknown>} payload - The payload
   * @returns {Promise<void>}
   */
  private async processAppsPlatformEvent(
    eventType: string,
    organizationId: string,
    payload: Record<string, unknown>,
  ): Promise<void> {
    this.logger.log(
      `${this.spanId} Processing apps platform event: ${eventType} for organization: ${organizationId}`,
    );

    try {
      switch (eventType) {
        case AppsPlatformEvents.APP_INSTALLED:
        case AppsPlatformEvents.APP_TEAM_ADDED: {
          const {
            application_id: appId,
            application_slug: appSlug,
            team_ids: teamIds,
          } = payload;

          this.logger.log(
            `${
              this.spanId
            } Apps platform event details - appId: ${appId}, appSlug: ${appSlug}, teamIds: ${JSON.stringify(
              teamIds,
            )}`,
          );

          if (appId && appSlug === "Slack") {
            this.logger.log(
              `${this.spanId} Processing Slack app installation/team addition`,
            );

            await this.workflowSeeder.seedOnSlackAppInstallation(
              organizationId,
              teamIds as string[],
              appId as string,
              eventType,
            );

            this.logger.log(
              `${this.spanId} Successfully processed Slack app installation event ${eventType} for organization: ${organizationId}`,
            );
          } else {
            this.logger.log(
              `${
                this.spanId
              } Skipping apps platform event processing - not a Slack app or missing required fields. appId: ${appId}, appSlug: ${appSlug}, teamIds: ${JSON.stringify(
                teamIds,
              )}`,
            );
          }
          break;
        }
        default: {
          this.logger.log(
            `${this.spanId} Apps platform event type ${eventType} is not handled by workflow seeding`,
          );
          break;
        }
      }

      this.logger.log(
        `${this.spanId} Completed processing apps platform event: ${eventType}`,
      );
      return;
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to process apps platform event ${eventType} > Error message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Processes a workflow event from an SQS message.
   * Handles the complete lifecycle from validation to workflow execution.
   * @param {SQSMessage} message - The SQS message containing the event
   * @returns {Promise<void>}
   * @throws {Error} If processing fails
   */
  async processWorkflowEvent(message: SQSMessage): Promise<void> {
    this.spanId = `[EventProcessorService][mid=${message.id}]`;
    try {
      // Extracts event type and organization id from sqs message attributes
      const { eventType, organizationId } =
        this.extractEventTypeAndOrganization(message);
      if (!eventType || !organizationId) {
        this.logger.error(
          `${this.spanId} Event type or organization id not found in message attributes`,
        );
        return;
      }

      if (this.isAppsPlatformEvent(eventType)) {
        await this.processAppsPlatformEvent(
          eventType,
          organizationId,
          message.message as Record<string, unknown>,
        );
      }

      // Gets the event type from sqs message and finds it in event registry
      const event = await this.findEventFromRegistry(eventType, organizationId);

      if (!event) {
        this.logger.error(
          `${this.spanId} Event not found in registry. Event type: ${eventType}`,
        );
        return;
      }

      // Create an event log with sqs message
      const eventLog = await this.createEventLog(message, event);
      this.logger.log(`${this.spanId} Event log id: ${eventLog.id} created`);

      // Validate sqs message against the schema provided in event registry
      this.validateEventSchema(event, eventLog);
      this.logger.log(
        `${this.spanId} Event log id: ${eventLog.id} Schema validated`,
      );

      // Finds all the workflows which are active and has trigger event as the Event
      // or workflow from the event data when the event is manual trigger
      const matchingWorkflows = await this.findMatchingWorkflows(
        event,
        eventLog,
      );
      this.logger.log(
        `${this.spanId} Event log id: ${eventLog.id} Found matching workflows: ${matchingWorkflows.length}`,
      );

      const filteredWorkflows: Map<
        Workflow,
        Record<string, unknown>
      > = new Map();

      // For each workflow, which have the trigger event, validate the filters
      await Promise.all(
        matchingWorkflows.map(async (workflow) => {
          // Annotates entity dto if required and creates a workflow context (sqs message + annotated entity dto)
          const workflowContext = await this.annotateEventData(
            workflow,
            event,
            eventLog,
          );

          this.logger.log(
            `${this.spanId} Event log id: ${eventLog.id} Annotated data for workflow id: ${workflow.uid}`,
          );

          // Validates event filters
          const filtered = this.validateEventFilters(workflowContext, workflow);

          if (filtered) filteredWorkflows.set(workflow, workflowContext);
        }),
      );

      this.logger.log(
        `${this.spanId} Event log id: ${eventLog.id} Found filtered workflows: ${filteredWorkflows.size}`,
      );

      for (const [workflow, workflowContext] of filteredWorkflows.entries()) {
        // Initializes workflow instance.
        // Creates if not already exists or returns existing one.
        const instance = await this.workflowInstanceService.initializeInstance({
          workflow,
          eventLog,
          context: workflowContext,
        });

        this.logger.log(
          `${this.spanId} Event log id: ${eventLog.id} Workflow id: ${workflow.id} Workflow instance uid: ${instance.uid}`,
        );

        // To handle the case where sqs message comes again but the instance has already started.
        if (instance.status !== WorkflowInstanceStatusEnum.PENDING) {
          continue;
        }

        const {
          engineConfigUID,
          workflowDefinitionType,
          workflowInstanceUID,
          args,
        } = this.workflowInstanceService.getExecutionParameters();

        this.logger.log(
          `${this.spanId} Event log id: ${eventLog.id} Workflow id: ${workflow.id} Workflow instance uid: ${workflowInstanceUID} Workflow execution triggered`,
        );

        // Triggers execution of the workflow
        this.workflowClientService.executeWorkflow(
          engineConfigUID,
          workflowDefinitionType,
          workflowInstanceUID,
          args,
        );

        // Marks the workflow instance as scheduled.
        // Ensures workflow is not triggered multiple times even if the sqs message comes again.
        await this.workflowInstanceService.markAsScheduled();

        this.logger.log(
          `${this.spanId} Event log id: ${eventLog.id} Workflow id: ${workflow.id} Workflow instance uid: ${workflowInstanceUID} scheduled for execution`,
        );
      }

      // Explicit cleanup to prevent memory accumulation
      filteredWorkflows.clear();
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to process workflow event > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_EVENTS_PROCESSOR",
        fn: "processWorkflowEvent",
        event: message,
        error,
      });

      // Throw error to be caught by sqs consumer - to reprocess the message or send it to DLQ
      throw error;
    }
  }
}
