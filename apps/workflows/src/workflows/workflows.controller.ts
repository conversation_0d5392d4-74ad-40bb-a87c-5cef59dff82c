import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UnprocessableEntityException,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { CurrentUser, UserType } from "@repo/nestjs-commons/utils";
import {
  FILTER_OPERATORS,
  LOGICAL_OPERATORS,
  Workflow,
  WorkflowSubType,
  WorkflowType,
} from "@repo/thena-platform-entities";
import * as rTracer from "cls-rtracer";
import { v4 as uuidv4 } from "uuid";
import { SchemaValidator } from "../common";
import { UsersGrpcClient } from "../common/grpc/users-grpc.client";
import { DirectActivityExecutor } from "./core/activities/direct.activity-executor";
import { GrpcActivityExecutor } from "./core/activities/grpc.activity-executor";
import { HttpActivityExecutor } from "./core/activities/http.activity-executor";
import { IActivityExecutor } from "./core/activities/transport.activity-executor.interface";
import {
  ActivityResponseDto,
  GetActivityRegistryResponse,
  GetAvailableWorkflowFiltersResponse,
  GetRegistryDto,
} from "./dtos/activity.dto";
import { EventResponseDto, GetEventRegistryResponse } from "./dtos/event.dto";
import {
  DeleteWorkflowResponse,
  TriggerActivityResponseDto as ExecuteActivityResponseDto,
  GetWorkflowExecutionsResponse,
  GetWorkflowExecutionTaskResponseDto,
  GetWorkflowsResponse,
  WorkflowResponseDto,
} from "./dtos/workflow-response.dto";
import {
  CreateWorkflowDto,
  GetWorkflowByUniqueIdentifierDto,
  GetWorkflowExecutionsDto,
  GetWorkflowsDto,
  ToggleWorkflowDto,
  UpdateWorkflowDto,
} from "./dtos/workflow.dto";
import {
  CreateWorkflowOptions,
  UpdateWorkflowOptions,
} from "./interfaces/workflow.interface";
import { ActivityActionService } from "./services/activity.action.service";
import { EventActionService } from "./services/event.action.service";
import { WorkflowEngineActionService } from "./services/workflow-engine.action.service";
import { WorkflowInstanceActivityActionService } from "./services/workflow-instance-activity.action.service";
import { WorkflowInstanceActionService } from "./services/workflow-instance.action.service";
import { WorkflowActionService } from "./services/workflow.action.service";

@ApiTags("Workflows")
@Controller("api/v1/workflows")
export class WorkflowsController {
  private readonly logSpanId = "[WorkflowsController]";

  constructor(
    private eventActionService: EventActionService,
    private activityActionService: ActivityActionService,
    private workflowActionService: WorkflowActionService,
    private workflowEngineActionService: WorkflowEngineActionService,
    private workflowInstanceActionService: WorkflowInstanceActionService,
    private workflowInstanceActivityActionService: WorkflowInstanceActivityActionService,

    private usersGrpcClient: UsersGrpcClient,

    private schemaValidator: SchemaValidator,
    private httpActivityExecutor: HttpActivityExecutor,
    private grpcActivityExecutor: GrpcActivityExecutor,
    private directActivityExecutor: DirectActivityExecutor,

    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  private async getActivities(workflows: Workflow[]) {
    const activities =
      await this.activityActionService.findActivitiesByUniqueIdentifiers(
        workflows.flatMap((workflow) =>
          workflow.workflowDefinition.map((step) => ({
            uniqueIdentifier: step.activity.uniqueIdentifier,
            ...(step.activity.version && { version: step.activity.version }),
          })),
        ),
      );

    return activities;
  }

  @Get("/registry/events")
  @ApiResponseMessage("Event registry fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get event registry",
    responseType: GetEventRegistryResponse,
  })
  async getEventRegistry(
    @CurrentUser() currentUser: CurrentUser,
    @Query() query: GetRegistryDto,
  ) {
    try {
      const platformEvents = await this.eventActionService.findPlatformEvents(
        query.teamId,
      );

      const registeredEvents =
        await this.eventActionService.findRegisteredEvents(
          currentUser.orgUid,
          query.teamId,
        );

      const results = [...platformEvents, ...registeredEvents];

      const formattedResults = results.map((event) =>
        EventResponseDto.fromEntity(event),
      );

      return {
        results: formattedResults,
      };
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching event registry. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "getEvent",
        user: currentUser,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("/registry/activities")
  @ApiResponseMessage("Activity registry fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get activity registry",
    responseType: GetActivityRegistryResponse,
  })
  async getActivityRegistry(
    @CurrentUser() currentUser: CurrentUser,
    @Query() query: GetRegistryDto,
  ) {
    try {
      const platformActivities =
        await this.activityActionService.findPlatformActivities(query.teamId);

      const registeredActivities =
        await this.activityActionService.findRegisteredActivities(
          currentUser.orgUid,
          query.teamId,
        );

      const results = [...platformActivities, ...registeredActivities];

      const formattedResults = results.map((activity) =>
        ActivityResponseDto.fromEntity(activity),
      );

      return {
        results: formattedResults,
      };
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching activity registry. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "getActivity",
        user: currentUser,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("/registry/filters")
  @ApiResponseMessage(
    "Available workflow filter operators fetched successfully!",
  )
  @ApiGetEndpoint({
    summary:
      "Available filter operators and logical operators to use in workflow filters",
    responseType: GetAvailableWorkflowFiltersResponse,
  })
  getAvailableWorkflowFilters(@CurrentUser() currentUser: CurrentUser) {
    try {
      return {
        filter_operators: FILTER_OPERATORS,
        logical_operators: LOGICAL_OPERATORS,
      };
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching available workflow filters > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "getAvailableWorkflowFilters",
        user: currentUser,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("")
  @ApiResponseMessage("Workflows fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all the workflows defined by the organization",
    responseType: GetWorkflowsResponse,
  })
  async getWorkflows(
    @CurrentUser() currentUser: CurrentUser,
    @Query() query: GetWorkflowsDto,
  ) {
    try {
      const { page, limit } = query;

      const validatedLimit = Math.min(limit ?? 50, 100);
      const validatedPage = Math.max(page ?? 0, 0);

      const subTypes = query.subTypes
        ? query.subTypes.split(",").map((subType) => subType.trim())
        : [];

      for (const subType of subTypes) {
        if (
          !Object.values(WorkflowSubType).includes(subType as WorkflowSubType)
        ) {
          throw new UnprocessableEntityException(
            `Invalid sub type: ${subType}. Valid sub types are: ${Object.values(
              WorkflowSubType,
            ).join(", ")}`,
          );
        }
      }

      const { results, total } =
        await this.workflowActionService.findAllWorkflowsByOrg(
          validatedPage,
          validatedLimit,
          currentUser.orgUid,
          query.teamId,
          query.type,
          subTypes as WorkflowSubType[],
        );

      const activities = await this.getActivities(results);

      const formattedResults = results.map((workflow) =>
        WorkflowResponseDto.fromEntity(workflow, activities),
      );

      return {
        results: formattedResults,
        total,
      };
    } catch (error) {
      this.logger.error(
        `${
          this.logSpanId
        } Error encountered while fetching workflows. Query: ${JSON.stringify(
          query,
        )} > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "getWorkflows",
        user: currentUser,
        query,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("/:workflowUniqueIdentifier")
  @ApiResponseMessage("Workflows fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all the workflows defined by the organization",
    responseType: GetWorkflowsResponse,
  })
  async getWorkflowByUniqueIdentifier(
    @CurrentUser() currentUser: CurrentUser,
    @Param("workflowUniqueIdentifier") workflowUniqueIdentifier: string,
    @Query() query: GetWorkflowByUniqueIdentifierDto,
  ) {
    try {
      let workflow: Workflow;

      if (query.version) {
        workflow =
          await this.workflowActionService.findWorkflowByUniqueIdentifierAndVersion(
            workflowUniqueIdentifier,
            query.version,
          );
      } else {
        workflow =
          await this.workflowActionService.findLatestWorkflowByUniqueIdentifier(
            workflowUniqueIdentifier,
          );
      }

      if (!workflow) {
        throw new NotFoundException("Workflow not found");
      }

      const activities = await this.getActivities([workflow]);

      return WorkflowResponseDto.fromEntity(workflow, activities);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching workflow by unique identifier. Workflow unique identifier: ${workflowUniqueIdentifier}, Version: ${query.version} > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "getWorkflowByUniqueIdentifier",
        user: currentUser,
        params: {
          workflowUniqueIdentifier,
        },
        query,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post("")
  @ApiResponseMessage("Workflow created successfully!")
  @ApiCreateEndpoint({
    summary: "Create a new workflow",
    responseType: WorkflowResponseDto,
  })
  async createWorkflow(
    @CurrentUser() currentUser: CurrentUser,
    @Body() body: CreateWorkflowDto,
  ) {
    try {
      let workflowType: WorkflowType = body.type ?? WorkflowType.MANUAL;
      let workflowSubType: WorkflowSubType =
        body.subType ?? WorkflowSubType.WORKFLOW;

      if (currentUser.userType === UserType.BOT_USER) {
        workflowType = body.type ?? WorkflowType.AUTOMATED;
        workflowSubType = body.subType ?? WorkflowSubType.REGISTERED_APP;
      }

      let executingAgent: string;

      if (!body.executingAgent) {
        const workflowBot = await this.usersGrpcClient.fetchWorkflowBot(
          currentUser.uid,
          currentUser.orgUid,
        );

        executingAgent = workflowBot.id;
      } else {
        executingAgent = body.executingAgent;
      }

      const triggerEvent = await this.eventActionService.findEventByUID(
        body.triggerEvent,
      );
      if (!triggerEvent) {
        throw new BadRequestException("Trigger event not found");
      }

      // TODO FOR LATER: Taking the default workflow engine for now. Have to decide on a logic on how to select a workflow engine.
      const workflowEngine =
        await this.workflowEngineActionService.findDefaultWorkflowEngine();

      // ALERT: This is the only workflow type right now. If we add more types, we need to update this.
      const workflowDefinitionType = "dynamicWorkflow";

      const createWorkflowOptions: CreateWorkflowOptions = {
        name: body.name,
        type: workflowType,
        subType: workflowSubType,
        triggerEvent,
        filters: body.filters,
        executingAgent,
        workflowDefinition: body.workflowDefinition,
        workflowEngineId: workflowEngine.id,
        workflowDefinitionType,
        organizationId: currentUser.orgUid,
        createdBy: currentUser.uid,
        teamId: body.teamId,
        annotations: body.annotations,
        metadata: body.metadata,
      };

      const result = await this.workflowActionService.createWorkflow(
        createWorkflowOptions,
      );

      const activities = await this.getActivities([result]);

      return WorkflowResponseDto.fromEntity(result, activities);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while creating workflow: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "createWorkflow",
        user: currentUser,
        body,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Patch("/:workflowUniqueIdentifier")
  @ApiResponseMessage("Workflow updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update a workflow",
    responseType: WorkflowResponseDto,
  })
  async updateWorkflow(
    @CurrentUser() currentUser: CurrentUser,
    @Param("workflowUniqueIdentifier") workflowUniqueIdentifier: string,
    @Body() body: UpdateWorkflowDto,
  ) {
    try {
      const updateWorkflowOptions: UpdateWorkflowOptions = {
        workflowUniqueIdentifier,
        organizationId: currentUser.orgUid,
        createdBy: currentUser.uid,

        ...(body.name && { name: body.name }),
        ...(body.filters && { filters: body.filters }),
        ...(body.executingAgent && { executingAgent: body.executingAgent }),
        ...(body.workflowDefinition && {
          workflowDefinition: body.workflowDefinition,
        }),
        ...(body.teamId && { teamId: body.teamId }),
        ...(body.metadata && { metadata: body.metadata }),
        ...(body.annotations && { annotations: body.annotations }),
      };

      if (body.triggerEvent) {
        const triggerEvent = await this.eventActionService.findEventByUID(
          body.triggerEvent,
        );
        if (!triggerEvent) {
          throw new BadRequestException("Trigger event not found");
        }

        updateWorkflowOptions.triggerEvent = triggerEvent;
      }

      const result = await this.workflowActionService.updateWorkflow(
        updateWorkflowOptions,
      );

      const activities = await this.getActivities([result]);

      return WorkflowResponseDto.fromEntity(result, activities);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while updating workflow: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "updateWorkflow",
        user: currentUser,
        body,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post("/:workflowUniqueIdentifier/toggle")
  @ApiResponseMessage("Workflow toggled successfully!")
  @ApiUpdateEndpoint({
    summary: "Toggle a workflow",
    responseType: DeleteWorkflowResponse,
  })
  async toggleWorkflow(
    @CurrentUser() currentUser: CurrentUser,
    @Param("workflowUniqueIdentifier") workflowUniqueIdentifier: string,
    @Body() body: ToggleWorkflowDto,
  ) {
    try {
      const { isActive } = body;

      const result = await this.workflowActionService.toggleWorkflow(
        workflowUniqueIdentifier,
        currentUser.orgUid,
        isActive,
      );

      const activities = await this.getActivities([result]);

      return WorkflowResponseDto.fromEntity(result, activities);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while toggling workflow: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "toggleWorkflow",
        user: currentUser,
        workflowUniqueIdentifier,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Delete("/:workflowUniqueIdentifier")
  @ApiResponseMessage("Workflow deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Delete a workflow",
  })
  async deleteWorkflow(
    @CurrentUser() currentUser: CurrentUser,
    @Param("workflowUniqueIdentifier") workflowUniqueIdentifier: string,
  ) {
    try {
      await this.workflowActionService.deleteWorkflow(
        workflowUniqueIdentifier,
        currentUser.orgUid,
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while deleting workflow: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "deleteWorkflow",
        user: currentUser,
        workflowUniqueIdentifier,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("/executions")
  @ApiResponseMessage("Workflow executions fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all the executions of a workflow",
    responseType: GetWorkflowExecutionsResponse,
  })
  @UseInterceptors(ResponseTransformInterceptor)
  async getWorkflowExecutions(
    @CurrentUser() currentUser: CurrentUser,
    @Query() query: GetWorkflowExecutionsDto,
  ) {
    try {
      const { workflowId, version, status, from, to } = query;

      const workflowInstances =
        await this.workflowInstanceActionService.findWorkflowInstances({
          workflowUniqueIdentifier: workflowId,
          organizationId: currentUser.orgUid,
          version,
          status,
          from,
          to,
        });

      return workflowInstances.map((workflowInstance) =>
        GetWorkflowExecutionsResponse.fromEntity(workflowInstance),
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching workflow executions: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "getWorkflowExecutions",
        user: currentUser,
        query,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get(":workflowInstanceId/tasks")
  @ApiResponseMessage("Workflow execution tasks fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all the tasks of a workflow execution",
    responseType: GetWorkflowExecutionTaskResponseDto,
  })
  @UseInterceptors(ResponseTransformInterceptor)
  async getWorkflowExecutionTasks(
    @CurrentUser() currentUser: CurrentUser,
    @Param("workflowInstanceId") workflowInstanceId: string,
  ) {
    try {
      const workflowInstanceActivities =
        await this.workflowInstanceActivityActionService.findWorkflowInstanceActivitiesByWorkflowInstance(
          workflowInstanceId,
          currentUser.orgUid,
        );

      return workflowInstanceActivities.map((activity) =>
        GetWorkflowExecutionTaskResponseDto.fromEntity(activity),
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching workflow execution tasks: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "getWorkflowExecutionTasks",
        user: currentUser,
        workflowInstanceId,
        error,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Post(":activityId/execute")
  @ApiResponseMessage("Activity executed successfully!")
  @ApiUpdateEndpoint({
    summary: "Execute an activity",
    responseType: ExecuteActivityResponseDto,
  })
  async executeActivity(
    @CurrentUser() currentUser: CurrentUser,
    @Param("activityId") activityId: string,
    @Body() body: any,
  ): Promise<ExecuteActivityResponseDto> {
    let spanId = "[MANUAL_ACTIVITY_EXECUTION]";

    try {
      // Get activity
      const activity =
        await this.activityActionService.findLatestActivityBySourceIdentifier(
          activityId,
          currentUser.orgUid,
        );
      if (!activity) {
        throw new NotFoundException("Activity not found");
      }

      // Validate input data
      const { isValid, error } = this.schemaValidator.validateAgainstJsonSchema(
        body,
        activity.requestSchema,
      );
      if (!isValid) {
        this.logger.error(
          `${spanId} Invalid input data: ${JSON.stringify(error)}`,
        );
        throw new BadRequestException(error);
      }

      spanId = `[MANUAL_ACTIVITY_EXECUTION] ${activity.uid}`;

      // Get activity executor
      let activityExecutor: IActivityExecutor;
      switch (activity.connectionDetails.transport) {
        case "HTTP":
          activityExecutor = this.httpActivityExecutor;
          break;
        case "GRPC":
          activityExecutor = this.grpcActivityExecutor;
          break;
        case "DIRECT_CALL":
          activityExecutor = this.directActivityExecutor;
          break;
      }

      // Execute activity
      this.logger.log(
        `${spanId} Executing activity with input: ${JSON.stringify(body)}`,
      );

      const traceId = rTracer.id() as { reqId: string };

      const activityResult = await activityExecutor.executeActivity(
        spanId,
        body,
        activity,
        {
          userId: currentUser.uid,
          orgId: currentUser.orgUid,
          workflowId: "MANUAL_ACTIVITY_EXECUTION",
          workflowInstanceId: "MANUAL_ACTIVITY_EXECUTION",
          workflowInstanceActivityId: `${traceId?.reqId || uuidv4()}`,
        },
      );

      this.logger.log(
        `${spanId} Activity executed successfully with result: ${JSON.stringify(
          activityResult,
        )}`,
      );

      return {
        success: true,
        message: "Activity executed successfully",
        data: activityResult,
      };
    } catch (error) {
      this.logger.error(
        `${spanId} Error encountered while executing activity: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_CONTROLLER",
        fn: "executeActivity",
        user: currentUser,
        activityId,
        body,
        error,
      });

      throw error;
    }
  }
}
