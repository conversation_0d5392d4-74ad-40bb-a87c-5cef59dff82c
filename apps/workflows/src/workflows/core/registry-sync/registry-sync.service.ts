import { BadRequestException, Inject, Injectable } from "@nestjs/common";
import { RedisCacheProvider } from "@repo/nestjs-commons/cache";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { workflows } from "@repo/shared-proto";
import {
  TransactionService,
  WorkflowActivity,
  WorkflowActivitySourceEnum,
  WorkflowEvent,
  WorkflowEventSourceEnum,
} from "@repo/thena-platform-entities";
import { ActivitySignature, EventSignature } from "@repo/workflow-engine";
import { DeepPartial } from "typeorm";
import { v4 as uuidv4 } from "uuid";
import { HashUtils } from "../../../common/utils/hash.utils";
import { ActivityActionService } from "../../services/activity.action.service";
import { EventActionService } from "../../services/event.action.service";

interface TransactionState {
  operation: "register" | "delete";
  type: "activity" | "event";
  data: {
    created?: {
      activities?: { uniqueIdentifier: string; version: number }[];
      events?: { eventType: string; organizationId: string }[];
    };
    updated?: {
      activities?: {
        uniqueIdentifier: string;
        version: number;
        previousState: Partial<WorkflowActivity>;
      }[];
      events?: {
        eventType: string;
        organizationId: string;
        previousState: Partial<WorkflowEvent>;
      }[];
    };
    deleted?: {
      activities?: { identifier: string; organizationId: string };
      events?: { eventType: string; organizationId: string };
    };
  };
}

/**
 * Service responsible for synchronizing workflow activities and events from platform and other registered apps.
 * Handles the discovery, registration, and lifecycle management of activities and events.
 *
 * @export
 * @class RegistrySyncService
 */
@Injectable()
export class RegistrySyncService {
  private readonly TRANSACTION_REDIS_KEY_PREFIX =
    "workflow:registry:transaction:";
  private readonly TRANSACTION_REDIS_KEY_TTL = 900; // 15 minutes in seconds

  constructor(
    private readonly activityActionService: ActivityActionService,
    private readonly eventActionService: EventActionService,

    private readonly cacheProvider: RedisCacheProvider,
    private readonly transactionService: TransactionService,
    private readonly hashUtils: HashUtils,

    @Inject("Sentry") private readonly sentry: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  /**
   * Forms the activity signature from the activity (our entity) and its compensation activity.
   * Activity signature is the schema that is sent in the gRPC call and how other apps will interact with the activity.
   *
   * @param {WorkflowActivity} activity - The activity
   * @param {WorkflowActivity} compensationActivity - The compensation activity
   * @returns {ActivitySignature}
   */
  private getActivitySignature(
    activity: WorkflowActivity,
    compensationActivity: WorkflowActivity,
  ): ActivitySignature {
    return {
      activityName: activity.name,
      description: activity.description,
      identifier: activity.sourceIdentifier,
      requestSchema: JSON.stringify(activity.requestSchema),
      responseSchema: JSON.stringify(activity.responseSchema),
      connectionDetails: activity.connectionDetails,
      accessibleToTeam: activity.accessibleToTeam || false,
      teamIds: activity.teamIds || null,
      metadata: JSON.stringify(activity.metadata),
      isCompensable: activity.compensationActivityId ? true : false,
      compensationActivity: activity.compensationActivityId
        ? {
            identifier: compensationActivity.sourceIdentifier,
          }
        : null,
    };
  }

  /**
   * Prepares the activity (our entity) from the activity signature (the schema in gRPC call).
   * This is used to create a new activity or update an existing activity.
   *
   * @param {ActivitySignature} activity - The activity signature
   * @param {ActivitySource} source - The source of the activity
   * @param {number} version - The version of the activity
   * @param {string} [organizationId] - The organization ID (optional for platform_app activities)
   * @returns {DeepPartial<WorkflowActivity>}
   */
  private prepareActivityFromSignature(
    activity: ActivitySignature,
    source: WorkflowActivitySourceEnum,
    uniqueIdentifier: string,
    version: number,
    organizationId?: string,
  ): DeepPartial<WorkflowActivity> {
    return {
      name: activity.activityName,
      description: activity.description,
      source,
      organizationId,
      sourceIdentifier: activity.identifier,
      uniqueIdentifier,
      version,
      connectionDetails: activity.connectionDetails,
      accessibleToTeam: activity.accessibleToTeam || false,
      teamIds: activity.teamIds || null,
      requestSchema: JSON.parse(activity.requestSchema),
      responseSchema: JSON.parse(activity.responseSchema),
      metadata: JSON.parse(activity.metadata || "{}"),
    };
  }

  /**
   * Forms the event signature from the event (our entity).
   * Event signature is the schema that is sent in the gRPC call and how other apps will interact with the event.
   *
   * @param {WorkflowEvent} event - The event
   * @returns {EventSignature}
   */
  private getEventSignature(event: WorkflowEvent): EventSignature {
    return {
      eventName: event.name,
      description: event.description,
      eventType: event.eventType,
      schema: JSON.stringify(event.schema),
      accessibleToTeam: event.accessibleToTeam || false,
      teamIds: event.teamIds || null,
      metadata: JSON.stringify(event.metadata),
    };
  }

  /**
   * Prepares the event (our entity) from the event signature (the schema in gRPC call).
   * This is used to create a new event or update an existing event.
   *
   * @param {EventSignature} event - The event signature
   * @param {WorkflowEventSourceEnum} source - The source of the event
   * @param {string} [organizationId] - The organization ID (optional for platform_app events)
   * @returns {DeepPartial<WorkflowEvent>}
   */
  private prepareEventFromSignature(
    event: EventSignature,
    source: WorkflowEventSourceEnum,
    organizationId?: string,
  ): DeepPartial<WorkflowEvent> {
    return {
      name: event.eventName,
      description: event.description,
      eventType: event.eventType,
      source,
      organizationId,
      schema: JSON.parse(event.schema),
      metadata: JSON.parse(event.metadata),
      accessibleToTeam: event.accessibleToTeam || false,
      teamIds: event.teamIds || null,
    };
  }

  /**
   * Maps the source (from gRPC call) to the activity source (how our entity understands it).
   *
   * @param {workflows.Source} source - The source of the activities
   * @returns {ActivitySource}
   */
  private mapSourceToActivitySource(
    source: workflows.Source,
  ): WorkflowActivitySourceEnum {
    switch (source) {
      case workflows.Source.PLATFORM_APP:
        return WorkflowActivitySourceEnum.PLATFORM_APP;
      case workflows.Source.REGISTERED_APP:
        return WorkflowActivitySourceEnum.REGISTERED_APP;
    }
  }

  /**
   * Maps the source (from gRPC call) to the event source (how our entity understands it).
   *
   * @param {workflows.Source} source - The source of the events
   * @returns {WorkflowEventSourceEnum}
   */
  private mapSourceToEventSource(
    source: workflows.Source,
  ): WorkflowEventSourceEnum {
    switch (source) {
      case workflows.Source.PLATFORM_APP:
        return WorkflowEventSourceEnum.PLATFORM_APP;
      case workflows.Source.REGISTERED_APP:
        return WorkflowEventSourceEnum.REGISTERED_APP;
    }
  }

  private generateActivityUniqueIdentifier(
    sourceIdentifier: string,
    organizationId?: string,
  ): string {
    if (organizationId) {
      return `${sourceIdentifier}-${organizationId}`;
    }
    return `${sourceIdentifier}-platform`;
  }

  private async storeTransactionState(
    state: TransactionState,
  ): Promise<string> {
    const transactionId = uuidv4();
    const key = `${this.TRANSACTION_REDIS_KEY_PREFIX}${transactionId}`;
    await this.cacheProvider.set(
      key,
      JSON.stringify(state),
      this.TRANSACTION_REDIS_KEY_TTL,
    );
    return transactionId;
  }

  private async getTransactionState(
    transactionId: string,
  ): Promise<TransactionState | null> {
    const key = `${this.TRANSACTION_REDIS_KEY_PREFIX}${transactionId}`;
    const state = await this.cacheProvider.get<string>(key);
    return state ? JSON.parse(state) : null;
  }

  /**
   * Registers activities from the registry.
   * - Checks if the activity is already registered (including soft-deleted ones)
   * - If not, creates a new activity
   * - If the activity exists and has changed, creates a new version of the activity
   * - If the activity was soft-deleted, restores it with updated properties
   *
   * @param {workflows.Source} source - The source of the activities
   * @param {ActivitySignature[]} activities - The activities to register
   * @param {string} organizationId - The organization ID
   * @returns {Promise<string>}
   */
  async registerActivities(
    source: workflows.Source,
    activities: ActivitySignature[],
    organizationId?: string,
  ): Promise<string> {
    const transactionState: TransactionState = {
      operation: "register",
      type: "activity",
      data: {
        created: { activities: [] },
        updated: { activities: [] },
      },
    };

    const activityIdentifiers = activities.map(
      (activity) => activity.identifier,
    );

    const compensationIdentifiers = activities
      .filter((activity) => activity.isCompensable)
      .map((activity) => activity.compensationActivity.identifier);

    // Include soft-deleted activities in the search by passing true for includeInactive
    const existingActivities =
      await this.activityActionService.findActivitiesBySourceIdentifiers(
        [...activityIdentifiers, ...compensationIdentifiers],
        organizationId ?? null,
        true, // Include inactive (soft-deleted) activities
      );

    const activitiesToCreate: DeepPartial<WorkflowActivity>[] = [];
    const activitiesToUpdate: WorkflowActivity[] = [];

    // Prepares activities to create and update
    for (const activity of activities) {
      // Finds the latest version of the activity
      const existingActivity = existingActivities.find(
        (a) => a.sourceIdentifier === activity.identifier,
      );

      // Finds the latest version of the compensation activity
      const existingCompensationActivity = activity.isCompensable
        ? existingActivities.find(
            (a) =>
              a.sourceIdentifier === activity.compensationActivity.identifier,
          )
        : null;

      if (existingActivity) {
        // Check if the activity was previously soft-deleted
        const wasInactive = !existingActivity.isActive;

        // If activity was inactive (soft-deleted), we need to restore it
        if (wasInactive) {
          this.logger.log(
            `Activity ${activity.identifier} was inactive and will be restored.`,
          );

          // Store previous state for compensation
          transactionState.data.updated.activities.push({
            uniqueIdentifier: existingActivity.uniqueIdentifier,
            version: existingActivity.version,
            previousState: {
              isActive: existingActivity.isActive,
            },
          });

          // Restore the activity
          existingActivity.isActive = true;
          activitiesToUpdate.push(existingActivity);
          continue;
        }

        // For active activities, check if they've changed
        const activityHash = this.hashUtils.calculateHash(activity);
        const existingActivitySignature = this.getActivitySignature(
          existingActivity,
          existingCompensationActivity,
        );
        const existingActivityHash = this.hashUtils.calculateHash(
          existingActivitySignature,
        );

        if (activityHash === existingActivityHash) {
          this.logger.log(`Activity ${activity.identifier} is unchanged.`);
          continue;
        }

        this.logger.log(`Activity ${activity.identifier} has changed.`);
      }

      this.logger.log(
        `Creating activity ${activity.identifier} with version ${
          existingActivity ? existingActivity.version + 1 : 1
        }`,
      );

      const uniqueIdentifier = existingActivity
        ? existingActivity.uniqueIdentifier
        : this.generateActivityUniqueIdentifier(
            activity.identifier,
            organizationId,
          );

      const newVersion = existingActivity ? existingActivity.version + 1 : 1;

      activitiesToCreate.push(
        this.prepareActivityFromSignature(
          activity,
          this.mapSourceToActivitySource(source),
          uniqueIdentifier,
          newVersion,
          organizationId,
        ),
      );

      // Track created activity for compensation
      transactionState.data.created.activities.push({
        uniqueIdentifier,
        version: newVersion,
      });
    }

    const transactionId = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Update existing activities that were soft-deleted
        if (activitiesToUpdate.length > 0) {
          this.logger.log(
            `Updating ${activitiesToUpdate.length} existing activities`,
          );
          await this.activityActionService.updateMany(
            activitiesToUpdate,
            txnContext,
          );
        }

        // Create new activities or new versions
        if (activitiesToCreate.length > 0) {
          this.logger.log(
            `Creating ${activitiesToCreate.length} new activities`,
          );
          await this.activityActionService.createMany(
            activitiesToCreate,
            txnContext,
          );
        }

        // Store transaction state
        const transactionId = await this.storeTransactionState(
          transactionState,
        );

        return transactionId;
      },
    );

    try {
      // Add compensation activities.
      // This has to be outside transaction as this requires activities to be created.
      for (const activity of activitiesToCreate) {
        const activitySignature = activities.find(
          (a) => a.identifier === activity.sourceIdentifier,
        );

        if (activitySignature.isCompensable) {
          // Find the compensation activity in the list of activities created.
          const compensationActivity = activitiesToCreate.find(
            (a) =>
              a.sourceIdentifier ===
              activitySignature.compensationActivity.identifier,
          );

          // Add compensation activity to the activity
          await this.activityActionService.addCompensationActivity(
            activity.uniqueIdentifier,
            compensationActivity.uniqueIdentifier,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `[RegistrySyncService][registerActivities] Failed to add compensation activities`,
        error.stack,
      );

      // Compensate the transaction and throw error to the caller
      await this.compensateRegisterActivities(transactionId);
      throw error;
    }

    // Return the transaction ID to the caller
    return transactionId;
  }

  /**
   * Registers events from the registry.
   * - Checks if the event is already registered (including soft-deleted ones)
   * - If not, creates a new event
   * - If the event exists and has changed, updates it
   * - If the event was soft-deleted, restores it with updated properties
   *
   * @param {workflows.Source} source - The source of the events
   * @param {EventSignature[]} events - The events to register
   * @param {string} organizationId - The organization ID
   * @returns {Promise<string>}
   */
  async registerEvents(
    source: workflows.Source,
    events: EventSignature[],
    organizationId?: string,
  ): Promise<string> {
    const transactionState: TransactionState = {
      operation: "register",
      type: "event",
      data: {
        created: { events: [] },
        updated: { events: [] },
      },
    };

    const eventTypes = events.map((event) => event.eventType);

    // Include soft-deleted events in the search
    const existingEvents = await this.eventActionService.findEventsByEventTypes(
      eventTypes,
      organizationId ?? null,
      true, // Include inactive (soft-deleted) events
    );

    const eventsToCreate: DeepPartial<WorkflowEvent>[] = [];
    const eventsToUpdate: WorkflowEvent[] = [];

    // Prepares events to create and update
    for (const event of events) {
      const existingEvent = existingEvents.find(
        (e) => e.eventType === event.eventType,
      );

      if (existingEvent) {
        // Calculate MD5 hash of the event signature
        const eventHash = this.hashUtils.calculateHash(event);
        const existingEventSignature = this.getEventSignature(existingEvent);
        const existingEventHash = this.hashUtils.calculateHash(
          existingEventSignature,
        );

        // Check if event was previously soft-deleted
        const wasInactive = !existingEvent.isActive;

        // Store previous state for compensation (including isActive status)
        transactionState.data.updated.events.push({
          eventType: event.eventType,
          organizationId: existingEvent.organizationId,
          previousState: {
            description: existingEvent.description,
            schema: existingEvent.schema,
            metadata: existingEvent.metadata,
            isActive: existingEvent.isActive,
            accessibleToTeam: existingEvent.accessibleToTeam,
            teamIds: existingEvent.teamIds,
          },
        });

        // If event was inactive or hash changed, we need to update it
        if (wasInactive || eventHash !== existingEventHash) {
          if (wasInactive) {
            this.logger.log(
              `Event ${event.eventType} was inactive and will be restored.`,
            );
            existingEvent.isActive = true;
          } else {
            this.logger.log(`Event ${event.eventType} has changed.`);
          }

          existingEvent.accessibleToTeam = event.accessibleToTeam || false;
          existingEvent.teamIds = event.teamIds || null;
          existingEvent.description = event.description;
          existingEvent.schema = JSON.parse(event.schema);
          existingEvent.metadata = JSON.parse(event.metadata || "{}");
          eventsToUpdate.push(existingEvent);
        } else {
          this.logger.log(`Event ${event.eventType} is unchanged.`);
        }
      } else {
        this.logger.log(`Creating event ${event.eventType}`);

        eventsToCreate.push(
          this.prepareEventFromSignature(
            event,
            this.mapSourceToEventSource(source),
            organizationId,
          ),
        );

        // Track created event for compensation
        transactionState.data.created.events.push({
          eventType: event.eventType,
          organizationId,
        });
      }
    }

    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create new events
        if (eventsToCreate.length > 0) {
          this.logger.log(`Creating ${eventsToCreate.length} new events`);
          await this.eventActionService.createMany(eventsToCreate, txnContext);
        }

        // Update existing events (including restoring soft-deleted ones)
        if (eventsToUpdate.length > 0) {
          this.logger.log(`Updating ${eventsToUpdate.length} existing events`);
          await this.eventActionService.updateMany(eventsToUpdate, txnContext);
        }

        // Store transaction state for possible compensation
        const transactionId = await this.storeTransactionState(
          transactionState,
        );

        return transactionId;
      },
    );
  }

  /**
   * Retrieves activities from the registry.
   *
   * @param {workflows.Source} source - The source of the activities
   * @param {string} organizationId - The organization ID (optional for platform_app activities)
   * @returns {Promise<ActivitySignature[]>}
   */
  async getActivities(
    source: workflows.Source,
    organizationId?: string,
  ): Promise<ActivitySignature[]> {
    let activities: WorkflowActivity[];
    if (source === workflows.Source.PLATFORM_APP) {
      activities = await this.activityActionService.findPlatformActivities();
      return activities.map((activity) =>
        this.getActivitySignature(activity, activity),
      );
    }
    if (!organizationId) {
      throw new BadRequestException("Organization ID is required");
    }

    activities = await this.activityActionService.findRegisteredActivities(
      organizationId,
    );

    return activities.map((activity) =>
      this.getActivitySignature(activity, activity),
    );
  }

  /**
   * Retrieves events from the registry.
   *
   * @param {workflows.Source} source - The source of the events
   * @param {string} organizationId - The organization ID (optional for platform_app events)
   * @returns {Promise<EventSignature[]>}
   */
  async getEvents(
    source: workflows.Source,
    organizationId?: string,
  ): Promise<EventSignature[]> {
    let events: WorkflowEvent[];
    if (source === workflows.Source.PLATFORM_APP) {
      events = await this.eventActionService.findPlatformEvents();
    }
    if (!organizationId) {
      throw new BadRequestException("Organization ID is required");
    }

    events = await this.eventActionService.findRegisteredEvents(organizationId);

    return events.map((event) => this.getEventSignature(event));
  }

  /**
   * Soft deletes activities by their source identifier and organizationId.
   *
   * Only applicable for registered activities. Hence, organizationId is required.
   *
   * @param {string} identifier - The source identifier of the activity
   * @param {string} organizationId - The organization ID
   * @returns {Promise<string>}
   */
  async deleteActivities(
    identifier: string,
    organizationId: string,
  ): Promise<string> {
    // Store transaction state before making changes
    const transactionId = await this.storeTransactionState({
      operation: "delete",
      type: "activity",
      data: {
        deleted: {
          activities: { identifier, organizationId },
        },
      },
    });

    await this.activityActionService.updateActivitiesState(
      identifier,
      organizationId,
      false,
    );

    return transactionId;
  }

  /**
   * Soft deletes events by their eventType and organizationId.
   *
   * @param {string} eventType - The eventType of the event
   * @param {string} organizationId - The organization ID
   * @returns {Promise<string>}
   */
  async deleteEvents(
    eventType: string,
    organizationId: string,
  ): Promise<string> {
    // Store transaction state before making changes
    const transactionId = await this.storeTransactionState({
      operation: "delete",
      type: "event",
      data: { deleted: { events: { eventType, organizationId } } },
    });

    await this.eventActionService.updateEventsState(
      eventType,
      organizationId,
      false,
    );

    return transactionId;
  }

  /**
   * Compensates the registration of activities.
   *
   * @param {string} transactionId - The transaction ID
   * @returns {Promise<void>}
   */
  async compensateRegisterActivities(transactionId: string): Promise<void> {
    const state = await this.getTransactionState(transactionId);
    if (!state || state.type !== "activity" || state.operation !== "register") {
      throw new Error("Invalid or expired transaction");
    }

    const { created, updated } = state.data;

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Remove newly created activities
      if (created?.activities?.length > 0) {
        await this.activityActionService.removeActivities(
          created.activities,
          txnContext,
        );
      }

      // Restore previous state of updated activities
      if (updated?.activities?.length > 0) {
        await this.activityActionService.updateActivitiesWithPreviousState(
          updated.activities,
          txnContext,
        );
      }
    });
  }

  /**
   * Compensates the registration of events.
   * Restores events to their previous state or removes newly created events.
   *
   * @param {string} transactionId - The transaction ID
   * @returns {Promise<void>}
   */
  async compensateRegisterEvents(transactionId: string): Promise<void> {
    const state = await this.getTransactionState(transactionId);
    if (!state || state.type !== "event" || state.operation !== "register") {
      throw new Error("Invalid or expired transaction");
    }

    const { created, updated } = state.data;

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Remove newly created events
      if (created?.events?.length > 0) {
        await this.eventActionService.removeEvents(created.events, txnContext);
      }

      if (updated?.events?.length > 0) {
        await this.eventActionService.updateEventsWithPreviousState(
          updated.events,
          txnContext,
        );
      }
    });
  }

  /**
   * Compensates the deletion of activities.
   *
   * @param {string} transactionId - The transaction ID
   * @returns {Promise<void>}
   */
  async compensateDeleteActivities(transactionId: string): Promise<void> {
    const state = await this.getTransactionState(transactionId);
    if (!state || state.type !== "activity" || state.operation !== "delete") {
      throw new Error("Invalid or expired transaction");
    }

    const { identifier, organizationId } = state.data.deleted.activities;

    // Restore the deleted activity by marking it as not deleted
    await this.activityActionService.updateActivitiesState(
      identifier,
      organizationId,
      true,
    );
  }

  /**
   * Compensates the deletion of events.
   *
   * @param {string} transactionId - The transaction ID
   * @returns {Promise<void>}
   */
  async compensateDeleteEvents(transactionId: string): Promise<void> {
    const state = await this.getTransactionState(transactionId);
    if (!state || state.type !== "event" || state.operation !== "delete") {
      throw new Error("Invalid or expired transaction");
    }

    const { eventType, organizationId } = state.data.deleted.events;

    // Restore the deleted event by marking it as not deleted
    await this.eventActionService.updateEventsState(
      eventType,
      organizationId,
      true,
    );
  }

  /**
   * Updates the accessible teams for activities.
   *
   * @param {string[]} activityIdentifiers - The identifiers of the activities
   * @param {string[]} teamIds - The team IDs
   * @returns {Promise<void>}
   */
  async updateAccessibleTeamsForActivities(
    activityIdentifiers: string[],
    organizationId: string,
    teamIds: string[],
  ): Promise<void> {
    await this.activityActionService.updateAccessibleTeamsForActivities(
      activityIdentifiers,
      organizationId,
      teamIds,
    );
  }

  /**
   * Updates the accessible teams for events.
   *
   * @param {string[]} eventTypes - The event types
   * @param {string[]} teamIds - The team IDs
   * @returns {Promise<void>}
   */
  async updateAccessibleTeamsForEvents(
    eventTypes: string[],
    organizationId: string,
    teamIds: string[],
  ): Promise<void> {
    await this.eventActionService.updateAccessibleTeamsForEvents(
      eventTypes,
      organizationId,
      teamIds,
    );
  }
}
