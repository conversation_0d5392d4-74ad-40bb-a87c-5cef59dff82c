import { EnforcedMessageAttributes } from "@repo/nestjs-commons/aws-utils/sqs";
import { ILogger } from "@repo/nestjs-commons/logger";
import { AbstractWorkflowActivity } from "@repo/workflow-engine";
import { v4 as uuidv4 } from "uuid";
import { EventProducerService } from "../../../../event-producer/event-producer.service";
import { WorkflowActionService } from "../../../services/workflow.action.service";
import { WORKFLOW_MANUAL_TRIGGER_EVENT } from "../../events/constants";

export class TriggerWorkflowActivityV1 extends AbstractWorkflowActivity {
  private readonly logger: ILogger;
  private readonly eventProducerService: EventProducerService;
  private readonly workflowActionService: WorkflowActionService;

  constructor(injectableContext: {
    eventProducerService: EventProducerService;
    logger: ILogger;
    workflowActionService: WorkflowActionService;
  }) {
    super();
    this.eventProducerService =
      injectableContext.eventProducerService as EventProducerService;
    this.logger = injectableContext.logger as ILogger;
    this.workflowActionService =
      injectableContext.workflowActionService as WorkflowActionService;
  }

  static override activityName = "Trigger Workflow";

  static override description = "Triggers another workflow";

  static override identifier = "workflows:trigger-workflow";

  static override requestSchema = {
    type: "object",
    properties: {
      workflowUniqueIdentifier: { type: "string" },
      data: { type: "object" },
    },
    required: ["workflowUniqueIdentifier", "data"],
    additionalProperties: false,
  };

  static override responseSchema = {
    type: "object",
    properties: {
      data: { type: "string" },
      status: { type: "number" },
    },
    required: ["data", "status"],
    additionalProperties: false,
  };

  static override connectionDetails = {
    transport: "DIRECT_CALL" as const,
    directConfig: {
      className: "TriggerWorkflowActivityV1",
    },
  };

  static override metadata = {};

  static override accessibleToTeam = true;

  async execute<I = { name: string }, R = { data: string; status: number }>(
    input: I,
  ): Promise<R> {
    try {
      const typedInput = input as {
        workflowUniqueIdentifier: string;
        data: object;
      };

      // Find latest version of the workflow
      const workflow =
        await this.workflowActionService.findLatestWorkflowByUniqueIdentifier(
          typedInput.workflowUniqueIdentifier,
        );

      if (!workflow) {
        this.logger.error(
          `Workflow with unique identifier ${typedInput.workflowUniqueIdentifier} not found`,
        );
        return {
          data: `Workflow with unique identifier ${typedInput.workflowUniqueIdentifier} not found`,
          status: 404,
        } as R;
      }

      // Produce an SQS message for manual trigger of a workflow
      const messageBody = {
        workflowUID: workflow.uid,
        data: typedInput.data,
      };

      const messageAttributes: EnforcedMessageAttributes = {
        event_name: {
          DataType: "String",
          StringValue: WORKFLOW_MANUAL_TRIGGER_EVENT,
        },
        event_id: {
          DataType: "String",
          StringValue: uuidv4(),
        },
        event_timestamp: {
          DataType: "String",
          StringValue: new Date().toISOString(),
        },
        context_user_id: {
          DataType: "String",
          StringValue: workflow.executingAgent,
        },
        context_user_type: {
          DataType: "String",
          StringValue: "USER",
        },
        context_organization_id: {
          DataType: "String",
          StringValue: workflow.organizationId,
        },
      };

      this.eventProducerService.sendMessage(
        JSON.stringify(messageBody),
        messageAttributes,
      );

      this.logger.log(
        `Triggered workflow with unique identifier ${
          typedInput.workflowUniqueIdentifier
        } and data: ${JSON.stringify(typedInput.data)}`,
      );

      await Promise.resolve();
      return {
        data: `Triggered workflow`,
        status: 200,
      } as R;
    } catch (error) {
      this.logger.error(
        `Error triggering workflow with unique identifier > Error message: ${error.message}`,
        error.stack,
      );
      return {
        data: `Error triggering workflow: ${error.message}`,
        status: 500,
      } as R;
    }
  }
}
