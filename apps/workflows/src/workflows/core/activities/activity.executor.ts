import { Inject, Injectable, UnauthorizedException } from "@nestjs/common";
import { RpcException } from "@nestjs/microservices";
import { RedisLockProvider } from "@repo/nestjs-commons/cache";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { ThrottlerService } from "@repo/nestjs-commons/providers";
import {
  WorkflowActivity,
  WorkflowInstance,
  WorkflowInstanceActivity,
  WorkflowInstanceActivityStatusEnum,
  WorkflowInstanceStatusEnum,
  WorkflowStep,
} from "@repo/thena-platform-entities";
import { ActivityConnectionDetails } from "@repo/workflow-engine";
import { AxiosError } from "axios";
import { isNotEmpty } from "class-validator";
import { TimeoutError } from "rxjs";
import {
  HandleBarUtils,
  HashUtils,
  SchemaFiltersUtils,
  SchemaValidator,
} from "../../../common";
import {
  CreateWorkflowInstanceActivityOptions,
  UpdateWorkflowInstanceActivityOptions,
} from "../../interfaces/workflow-instance-activity.interface";
import { ActivityActionService } from "../../services/activity.action.service";
import { WorkflowInstanceActivityActionService } from "../../services/workflow-instance-activity.action.service";
import { WorkflowInstanceActionService } from "../../services/workflow-instance.action.service";
import { DirectActivityExecutor } from "./direct.activity-executor";
import { GrpcActivityExecutor } from "./grpc.activity-executor";
import { HttpActivityExecutor } from "./http.activity-executor";
import { IActivityExecutor } from "./transport.activity-executor.interface";

/**
 * Core service for executing workflow activities.
 * Manages the complete lifecycle of activity execution including:
 * - Input validation and transformation
 * - Activity execution through appropriate transport
 * - Context management and updates
 * - Activity status tracking and logging
 *
 * @export
 * @class ActivityExecutor
 */
@Injectable()
export class ActivityExecutor {
  private spanId: string;

  constructor(
    private activityActionService: ActivityActionService,
    private workflowInstanceActionService: WorkflowInstanceActionService,
    private workflowInstanceActivityActionService: WorkflowInstanceActivityActionService,

    private handleBarUtils: HandleBarUtils,
    private schemaValidator: SchemaValidator,
    private schemaFiltersUtils: SchemaFiltersUtils,
    private hashUtils: HashUtils,

    private httpActivityExecutor: HttpActivityExecutor,
    private grpcActivityExecutor: GrpcActivityExecutor,
    private directActivityExecutor: DirectActivityExecutor,

    private throttlerService: ThrottlerService,
    private redisLockProvider: RedisLockProvider,

    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {
    this.spanId = `[ActivityExecutor]`;
  }

  /**
   * Finds a workflow instance by its uid
   *
   * @param {string} workflowInstanceUID - uid of the workflow instance
   * @returns {Promise<WorkflowInstance>} - Workflow instance
   */
  private async findWorkflowInstance(
    workflowInstanceUID: string,
  ): Promise<WorkflowInstance> {
    try {
      const workflowInstance =
        await this.workflowInstanceActionService.findWorkflowInstanceByUID(
          workflowInstanceUID,
        );
      return workflowInstance;
    } catch (error) {
      this.logger.error(
        `${this.spanId} Could not find workflow instance > Error message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Finds an activity from the activity registry by its unique identifier and version.
   * If no version is provided, it finds the latest version of the activity.
   *
   * @param {WorkflowStep["activity"]} activityOptions - Activity options
   * @returns {Promise<WorkflowActivity>} - Activity
   */
  private async findActivity(
    activityOptions: WorkflowStep["activity"],
  ): Promise<WorkflowActivity> {
    try {
      if (!activityOptions.autoUpgradeToLatestVersion) {
        const activity =
          await this.activityActionService.findActivityByUniqueIdentifier(
            activityOptions.uniqueIdentifier,
            activityOptions.version,
          );
        return activity;
      }
      const activity =
        await this.activityActionService.findActivityByUniqueIdentifier(
          activityOptions.uniqueIdentifier,
        );
      return activity;
    } catch (error) {
      this.logger.error(
        `${this.spanId} Could not find activity > Error message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Compiles the input for an activity using handlebars.
   *
   * @param {Record<string, unknown>} input - Input data for the activity
   * @param {Record<string, unknown>} context - Context of the workflow instance
   * @returns {Record<string, unknown>} - Compiled input
   */
  private getCompiledInput(
    input: Record<string, unknown>,
    context: Record<string, unknown>,
  ): Record<string, unknown> {
    const compiledInput = {};
    for (const [key, value] of Object.entries(input)) {
      if (typeof value === "string" && value.includes("{{")) {
        compiledInput[key] = this.handleBarUtils.compileHandlebars(value, {
          context,
        });
      } else {
        compiledInput[key] = value;
      }
    }
    return compiledInput;
  }

  /**
   * Validates the input data against the activity schema using ajv utils.
   *
   * @param {Record<string, unknown>} data - Input data to validate
   * @param {Record<string, unknown>} schema - Schema to validate against
   */
  private validateJsonSchema(
    data: Record<string, unknown>,
    schema: Record<string, unknown>,
  ) {
    const { isValid, error } = this.schemaValidator.validateAgainstJsonSchema(
      data,
      schema,
    );
    if (!isValid) {
      this.logger.error(`${this.spanId} Invalid input data: ${error}`);
      throw error;
    }
  }

  /**
   * Finds a workflow instance activity by its workflow instance id and activity id.
   *
   * @param {string} workflowInstanceId - id of the workflow instance
   * @param {string} activityId - id of the activity
   * @returns {Promise<WorkflowInstanceActivity>} - Workflow instance activity
   */
  private findWorkflowInstanceActivity(
    workflowInstanceId: string,
    activityId: string,
  ): Promise<WorkflowInstanceActivity> {
    return this.workflowInstanceActivityActionService.findWorkflowInstanceActivityByInstanceIdAndActivityId(
      workflowInstanceId,
      activityId,
    );
  }

  /**
   * Creates a workflow instance activity.
   *
   * @param {string} activityId - id of the activity
   * @param {string} workflowInstanceId - id of the workflow instance
   * @param {number} workflowStepIdentifier - id of the workflow step
   * @param {boolean} isCompensation - whether the activity is a compensation
   * @returns {Promise<WorkflowInstanceActivity>} - Workflow instance activity
   */
  private createWorkflowInstanceActivity(
    activityId: string,
    workflowInstanceId: string,
    workflowStepIdentifier: number,
    input: Record<string, unknown>,
    isCompensation: boolean,
    isRetry: boolean,
  ): Promise<WorkflowInstanceActivity> {
    const createOptions: CreateWorkflowInstanceActivityOptions = {
      activityId,
      workflowInstanceId,
      input,
      workflowStepIdentifier,
      status: WorkflowInstanceActivityStatusEnum.SCHEDULED,
      isCompensation,
      isRetry,
    };

    return this.workflowInstanceActivityActionService.createWorkflowInstanceActivity(
      createOptions,
    );
  }

  private async updateWorkflowInstanceActivityRun(
    workflowInstanceActivityId: string,
    status: WorkflowInstanceActivityStatusEnum,
    result: Record<string, unknown>,
    executionTime: string,
  ): Promise<WorkflowInstanceActivity> {
    const updateOptions: UpdateWorkflowInstanceActivityOptions = {
      status,
      result,
      executionTime,
    };
    const updatedWorkflowInstanceActivity =
      await this.workflowInstanceActivityActionService.updateWorkflowInstanceActivity(
        workflowInstanceActivityId,
        updateOptions,
      );
    return updatedWorkflowInstanceActivity;
  }

  /**
   * Updates the context of a workflow instance.
   *
   * @param {string} workflowInstanceUID - uid of the workflow instance
   * @param {Record<string, unknown>} context - Context to update
   * @returns {Promise<WorkflowInstance>} - Updated workflow instance
   */
  private async updateWorkflowInstanceContext(
    workflowInstanceUID: string,
    context: Record<string, unknown>,
  ): Promise<WorkflowInstance> {
    try {
      const updatedWorkflowInstance =
        await this.workflowInstanceActionService.updateWorkflowInstanceContextByUID(
          workflowInstanceUID,
          context,
        );
      return updatedWorkflowInstance;
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to update workflow instance context > Error message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Returns the appropriate activity executor {@link IActivityExecutor} based on the transport type.
   *
   * @param {ActivityConnectionDetails["transport"]} transport - Transport type
   * @returns {IActivityExecutor} - Activity executor
   */
  private getActivityExecutor(
    transport: ActivityConnectionDetails["transport"],
  ): IActivityExecutor {
    switch (transport) {
      case "HTTP":
        return this.httpActivityExecutor;
      case "GRPC":
        return this.grpcActivityExecutor;
      case "DIRECT_CALL":
        return this.directActivityExecutor;
      default:
        throw new Error(`Unsupported transport: ${transport}`);
    }
  }

  private async hasActivityThrottled(
    activity: WorkflowActivity,
    input: Record<string, unknown>,
    organizationId: string,
  ) {
    const activityThrottlerConfig = activity.metadata?.throttler as {
      enabled: boolean;
      limit: number;
      ttl: number;
      argsToUseForKey: string[];
    };

    if (!activityThrottlerConfig?.enabled) {
      return false;
    }

    const key = this.hashUtils.calculateHash({
      activityUniqueIdentifier: activity.uniqueIdentifier,
      organizationId,
      args: (activityThrottlerConfig.argsToUseForKey || []).map(
        (arg) => input[arg],
      ),
    });

    const isThrottled = await this.throttlerService.shouldThrottle({
      enabled: activityThrottlerConfig.enabled,
      key,
      limit: activityThrottlerConfig.limit,
      ttl: activityThrottlerConfig.ttl,
    });

    return isThrottled;
  }

  /**
   * Activity executor utility function. Used by startActivity and compensateActivity.
   * - Creates a workflow instance activity
   * - Executes the activity
   * - Validates the activity result schema
   * - Updates the workflow instance activity
   *
   * @param {WorkflowStep} step - the workflow step
   * @param {WorkflowInstance} workflowInstance - Workflow instance
   * @param {WorkflowActivity} activity - Activity to execute
   * @param {Record<string, unknown>} input - Input data for the activity
   * @param {boolean} isRetry - Whether the activity is a retry
   * @param {boolean} isCompensation - Whether the activity is a compensation
   * @returns {Promise<Record<string, unknown>>} - Result of the activity
   */
  private async executeActivity(
    step: WorkflowStep,
    workflowInstance: WorkflowInstance,
    activity: WorkflowActivity,
    input: Record<string, unknown>,
    isRetry: boolean,
    isCompensation: boolean,
  ): Promise<Record<string, unknown>> {
    // Validate input schema
    this.validateJsonSchema(input, activity.requestSchema);

    // Create workflow instance activity
    const workflowInstanceActivity = await this.createWorkflowInstanceActivity(
      activity.id,
      workflowInstance.id,
      step.stepIdentifier,
      input,
      isCompensation,
      isRetry,
    );

    const isThrottled = await this.hasActivityThrottled(
      activity,
      input,
      workflowInstance.workflow.organizationId,
    );

    if (isThrottled) {
      this.logger.error(
        `${this.spanId} Activity is throttled > Activity: ${activity.uniqueIdentifier}`,
      );
      await this.updateWorkflowInstanceActivityRun(
        workflowInstanceActivity.id,
        WorkflowInstanceActivityStatusEnum.FAILED,
        {
          error: "Activity is throttled",
        },
        "0",
      );

      throw new Error("Activity is throttled");
    }

    const workflow = workflowInstance.workflow;

    let activityResult: Record<string, unknown>;

    const startTime = Date.now();

    try {
      // Execute activity
      const activityExecutor = this.getActivityExecutor(
        activity.connectionDetails.transport,
      );

      activityResult = await activityExecutor.executeActivity(
        this.spanId,
        input,
        activity,
        {
          userId: workflow.executingAgent,
          orgId: workflow.organizationId,
          workflowId: workflow.uid,
          workflowInstanceId: workflowInstance.uid,
          workflowInstanceActivityId: workflowInstanceActivity.uid,
        },
        step.executionTimeout,
      );
    } catch (error) {
      const endTime = Date.now();
      const executionTime = (endTime - startTime).toString();

      if (error instanceof UnauthorizedException) {
        await this.updateWorkflowInstanceActivityRun(
          workflowInstanceActivity.id,
          WorkflowInstanceActivityStatusEnum.UNAUTHORIZED,
          {
            error: error.message,
          },
          executionTime,
        );
        throw error;
      }

      await this.updateWorkflowInstanceActivityRun(
        workflowInstanceActivity.id,
        WorkflowInstanceActivityStatusEnum.FAILED,
        {
          error: error.message,
        },
        executionTime,
      );
      throw error;
    }

    const endTime = Date.now();
    const executionTime = (endTime - startTime).toString();

    // Validate activity result schema
    this.validateJsonSchema(activityResult, activity.responseSchema);

    // Update workflow instance activity
    await this.updateWorkflowInstanceActivityRun(
      workflowInstanceActivity.id,
      WorkflowInstanceActivityStatusEnum.FINISHED,
      activityResult,
      executionTime,
    );

    return activityResult;
  }

  /**
   * Starts a workflow activity with the provided input and configuration.
   * Handles the complete lifecycle including validation, execution, and status updates.
   * - Finds the workflow instance
   * - Checks if the workflow instance is suspended
   * - Finds the activity from the activity registry {@link findActivity}
   * - Compiles the input
   * - Finds if a previous activity run exists to determine if this is a retry
   * - Executes the activity {@link executeActivity}
   * - Appends the activity result to the workflow instance context
   *
   * @param {string} workflowInstanceUID - Unique identifier of the workflow instance
   * @param {WorkflowStep} step - Step configuration containing activity details
   * @returns {Promise<Record<string, unknown>>} - returns the result of the activity
   * @throws {Error} If activity execution fails
   */
  async startActivity(
    workflowInstanceUID: string,
    step: WorkflowStep,
  ): Promise<Record<string, unknown>> {
    const activityOptions = step.activity;
    const input = step.input;

    this.spanId = `[ActivityExecutor][${workflowInstanceUID}][${
      activityOptions.uniqueIdentifier
    }@${activityOptions?.version || "latest"}]`;
    try {
      // Get workflow instance
      const workflowInstance = await this.findWorkflowInstance(
        workflowInstanceUID,
      );

      // Check if workflow instance is suspended
      if (workflowInstance.status === WorkflowInstanceStatusEnum.SUSPENDED) {
        this.logger.error(`${this.spanId} Workflow instance was suspended`);
        throw new Error("Workflow instance got suspended");
      }

      // Get activity from registry
      const activity = await this.findActivity(activityOptions);

      // Compile input for activity
      const compiledInput = this.getCompiledInput(
        input,
        workflowInstance.context,
      );

      // Find if previous activity run exists to determine if this is a retry
      const previousActivityRun = await this.findWorkflowInstanceActivity(
        workflowInstance.id,
        activity.id,
      );

      // Execute activity
      const activityResult = await this.executeActivity(
        step,
        workflowInstance,
        activity,
        compiledInput,
        previousActivityRun ? true : false,
        false,
      );

      // Acquire redis lock
      const lockResult = await this.redisLockProvider.acquireLock({
        key: `workflow:context:lock:${workflowInstanceUID}`,
        ttl: 2,
        maxRetries: 5,
        retryDelay: 100,
      });

      if (!lockResult.acquired) {
        // If we can't acquire the lock, throw an error to retry later
        throw new Error(
          `Failed to acquire lock for workflow context update: ${workflowInstanceUID}. ${lockResult.error}`,
        );
      }

      try {
        // Get latest workflow instance for updated context
        const workflowInstanceLatest = await this.findWorkflowInstance(
          workflowInstanceUID,
        );

        // Append activity result to context
        const updatedWorkflowContext = {
          ...workflowInstance.context,
          outputs: {
            ...((workflowInstanceLatest.context.outputs as Record<
              string,
              unknown
            >) || {}),
            [step.stepIdentifier]: activityResult,
          },
        };

        await this.updateWorkflowInstanceContext(
          workflowInstanceUID,
          updatedWorkflowContext,
        );

        this.logger.log(
          `${this.spanId} Successfully updated workflow context for ${workflowInstanceUID} with activity result from step ${step.stepIdentifier}`,
        );
      } finally {
        // Always release the lock
        await this.redisLockProvider.releaseLock({
          key: `workflow:context:lock:${workflowInstanceUID}`,
          lockValue: lockResult.lockValue,
        });
      }

      return activityResult;
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to execute activity > Error message: ${error.message}`,
        error.stack,
      );

      if (
        error instanceof RpcException ||
        error instanceof TimeoutError ||
        error instanceof AxiosError
      ) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "ACTIVITY_EXECUTOR",
        fn: "executeActivity",
      });
      throw error;
    }
  }

  /**
   * Starts a compensation activity for a failed activity.
   * Finds the compensation activity from the activity registry and executes it.
   * - Finds the workflow instance
   * - Checks if the workflow instance is compensating
   * - Finds the compensation activity if provided by the agent from the activity registry {@link findActivity}
   * - Finds the default compensation activity linked with the executed activity, if agent has not provided one
   * - Compiles the input
   * - Executes the activity {@link executeActivity}
   *
   * @param {string} workflowInstanceUID - Unique identifier of the workflow instance
   * @param {WorkflowStep} step - Step configuration containing activity details
   * @returns {Promise<Record<string, unknown>>} - returns the result of the activity
   * @throws {Error} If activity execution fails
   */
  async compensateActivity(
    workflowInstanceUID: string,
    step: WorkflowStep,
  ): Promise<Record<string, unknown>> {
    const activityOptions = step.activity;

    this.spanId = `[ActivityExecutor][${workflowInstanceUID}][Compensation][${
      activityOptions.uniqueIdentifier
    }@${activityOptions.version || "latest"}]`;

    // Get workflow instance
    const workflowInstance = await this.findWorkflowInstance(
      workflowInstanceUID,
    );

    // Check if workflow instance is compensating
    if (workflowInstance.status !== WorkflowInstanceStatusEnum.COMPENSATING) {
      this.logger.error(`${this.spanId} Workflow instance is not compensating`);
      throw new Error("Workflow instance is not compensating");
    }

    let compensationActivity: WorkflowActivity;
    let compensationInput: Record<string, unknown>;

    if (isNotEmpty(step.compensationActivity)) {
      // If agent provided a compensation activity, use it
      compensationActivity = await this.findActivity(
        step.compensationActivity.activity,
      );

      compensationInput = this.getCompiledInput(
        step.compensationActivity.input,
        workflowInstance.context,
      );
    } else {
      // If agent did not provide a compensation activity, use the default one
      // Get activity from registry
      const activity = await this.findActivity(activityOptions);

      // Get compensation activity
      compensationActivity = activity.compensationActivity;

      // Find the previous activity run
      const previousActivityRun = await this.findWorkflowInstanceActivity(
        workflowInstance.id,
        activity.id,
      );

      compensationInput = {
        executionToCompensate: previousActivityRun.uid,
      };
    }

    if (!compensationActivity) {
      this.logger.error(`${this.spanId} Activity is not compensable`);
      throw new Error("Activity is not compensable");
    }

    // Execute compensation activity and return result
    return this.executeActivity(
      step,
      workflowInstance,
      compensationActivity,
      compensationInput,
      false,
      true,
    );
  }

  /**
   * Validates filters for fan out operations to the workflow instance context.
   *
   * @param {string} workflowInstanceUID - Unique identifier of the workflow instance
   * @param {WorkflowStep["filters"]} filters - Filters to apply
   * @returns {Promise<boolean>} - returns true if filters are applied successfully
   */
  async validateFanOutFilters(
    workflowInstanceUID: string,
    filters: WorkflowStep["filters"],
  ): Promise<boolean> {
    // Get workflow instance
    const workflowInstance = await this.findWorkflowInstance(
      workflowInstanceUID,
    );
    try {
      return this.schemaFiltersUtils.validateEventFilterConditions(
        workflowInstance.context,
        filters,
      );
    } catch (error) {
      this.logger.error(
        `${this.spanId} Failed to validate fan out filters > Error message: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
