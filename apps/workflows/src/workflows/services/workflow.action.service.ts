import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from "@nestjs/common";
import {
  CachedWorkflowRepository,
  PaginationResultsInterface,
  TransactionService,
  Workflow,
  WorkflowRepository,
  WorkflowSubType,
  WorkflowType,
} from "@repo/thena-platform-entities";
import { FindOptionsWhere, In } from "typeorm";
import { DtoValidator } from "../dtos/dto.validator";
import {
  CreateWorkflowOptions,
  UpdateWorkflowOptions,
} from "../interfaces/workflow.interface";

/**
 * Service responsible for managing workflow entities in the system.
 * @export
 * @class WorkflowActionService
 */
@Injectable()
export class WorkflowActionService {
  constructor(
    private cachedWorkflowRepository: CachedWorkflowRepository,
    private workflowRepository: WorkflowRepository,
    private transactionService: TransactionService,

    private dtoValidator: DtoValidator,
  ) {}

  /**
   * Finds all active workflows triggered by a specific event within an organization.
   * Uses cached repository for better performance.
   *
   * @param {string} eventId - ID of the trigger event
   * @param {string} organizationId - ID of the organization
   * @returns {Promise<Workflow[]>} Array of matching workflows with their engine configurations
   */
  async findActiveWorkflowsWithTriggerEvent(
    eventId: string,
    organizationId: string,
  ): Promise<Workflow[]> {
    const results = await this.workflowRepository.findWithRelations({
      where: {
        isActive: true,
        isDeleted: false,
        triggerEventId: eventId,
        organizationId: organizationId,
      },
      relations: ["workflowEngine"],
    });
    return results;
  }

  /**
   * Finds the latest version of a workflow by its unique identifier.
   * Only returns active and non-deleted workflows.
   *
   * @param {string} workflowUniqueIdentifier - Unique identifier of the workflow
   * @returns {Promise<Workflow | null>} Latest workflow version or null if not found
   */
  async findLatestWorkflowByUniqueIdentifier(
    workflowUniqueIdentifier: string,
  ): Promise<Workflow> {
    const workflow = await this.workflowRepository.findByCondition({
      where: {
        uniqueIdentifier: workflowUniqueIdentifier,
      },
      order: {
        version: "DESC",
      },
      relations: ["triggerEvent"],
    });
    return workflow && workflow.isActive && !workflow.isDeleted
      ? workflow
      : null;
  }

  /**
   * Finds a workflow by its unique identifier and version.
   *
   * @param {string} workflowUniqueIdentifier - Unique identifier of the workflow
   * @param {number} version - Version of the workflow
   * @returns {Promise<Workflow>} Workflow with engine config or null if not found
   */
  async findWorkflowByUniqueIdentifierAndVersion(
    workflowUniqueIdentifier: string,
    version: number,
  ): Promise<Workflow> {
    const workflow = await this.workflowRepository.findByCondition({
      where: {
        uniqueIdentifier: workflowUniqueIdentifier,
        version,
      },
      relations: ["triggerEvent"],
    });
    return workflow;
  }

  /**
   * Finds an active workflow by its UID.
   *
   * @param {string} workflowUID - UID of the workflow
   * @returns {Promise<Workflow | null>} Workflow with engine config or null if not found
   */
  async findWorkflowByUID(workflowUID: string): Promise<Workflow> {
    const workflow = await this.workflowRepository.findByCondition({
      where: {
        isActive: true,
        isDeleted: false,
        uid: workflowUID,
      },
      relations: ["workflowEngine"],
    });
    return workflow && workflow.isActive && !workflow.isDeleted
      ? workflow
      : null;
  }

  /**
   * Finds all workflows defined in an organization.
   * Includes trigger event information in the results.
   *
   * @param {number} page - Page number for pagination
   * @param {number} limit - Number of items per page
   * @param {string} organizationId - ID of the organization
   * @param {string} teamId - ID of the team
   * @param {WorkflowType} type - Type of the workflow
   * @param {WorkflowSubType} subType - Sub type of the workflow
   * @returns {Promise<PaginationResultsInterface<Workflow>>} Paginated workflow results
   */
  async findAllWorkflowsByOrg(
    page: number,
    limit: number,
    organizationId: string,
    teamId?: string,
    type?: WorkflowType,
    subTypes?: WorkflowSubType[],
  ): Promise<PaginationResultsInterface<Workflow>> {
    const whereConditions: FindOptionsWhere<Workflow> = {
      isDeleted: false,
      organizationId,
    };

    if (teamId) {
      whereConditions.teamId = teamId;
    }
    if (subTypes && subTypes.length > 0) {
      whereConditions.subType = In(subTypes);
    }
    if (type) {
      whereConditions.type = type;
    }

    return await this.workflowRepository.fetchPaginatedResults(
      { page, limit },
      {
        where: whereConditions,
        relations: ["triggerEvent"],
        order: {
          id: "DESC",
        },
      },
    );
  }

  /**
   * Toggles the active status of a latest version of the workflow by its unique identifier.
   *
   * @param {string} workflowUniqueIdentifier - Unique identifier of the workflow
   * @param {string} organizationId - ID of the organization
   * @param {boolean} isActive - New active status
   * @returns {Promise<Workflow>} The updated workflow object
   */
  async toggleWorkflow(
    workflowUniqueIdentifier: string,
    organizationId: string,
    isActive: boolean,
  ): Promise<Workflow> {
    const workflow = await this.workflowRepository.findByCondition({
      where: {
        uniqueIdentifier: workflowUniqueIdentifier,
        organizationId,
      },
      order: {
        version: "DESC",
      },
      relations: ["triggerEvent"],
    });

    if (!workflow) {
      throw new BadRequestException("Workflow not found");
    }

    workflow.isActive = isActive;
    const updatedWorkflow = await this.workflowRepository.save(workflow);
    return updatedWorkflow;
  }

  /**
   * Deletes the latest version of the workflow by its unique identifier.
   *
   * @param {string} workflowUniqueIdentifier - Unique identifier of the workflow
   * @param {string} organizationId - ID of the organization
   * @returns {Promise<void>}
   */
  async deleteWorkflow(
    workflowUniqueIdentifier: string,
    organizationId: string,
  ): Promise<void> {
    const workflows = await this.workflowRepository.findAll({
      where: {
        uniqueIdentifier: workflowUniqueIdentifier,
        organizationId,
      },
    });

    if (!workflows || !Array.isArray(workflows) || workflows.length === 0) {
      throw new BadRequestException("Workflow not found");
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      await Promise.all(
        workflows.map(async (workflow) => {
          await this.workflowRepository.updateWithTxn(
            txnContext,
            {
              id: workflow.id,
            },
            {
              isDeleted: true,
            },
          );
        }),
      );
    });
  }

  /**
   * Creates a new workflow.
   *
   * @param {CreateWorkflowOptions} createWorkflowOptions - Options bag for the new workflow
   * @returns {Promise<Workflow>} The created workflow object
   */
  async createWorkflow(
    createWorkflowOptions: CreateWorkflowOptions,
  ): Promise<Workflow> {
    const workflow = new Workflow();

    let updatedFilters = createWorkflowOptions.filters ?? {};

    if (
      createWorkflowOptions.teamId &&
      createWorkflowOptions.triggerEvent.metadata?.pathToTeamId
    ) {
      const teamIdPath =
        createWorkflowOptions.triggerEvent.metadata.pathToTeamId;

      updatedFilters = {
        "~and": [
          {
            [`{{${teamIdPath}}}`]: {
              "~eq": createWorkflowOptions.teamId,
            },
          },
          updatedFilters,
        ],
      };
    }

    const {
      steps: validatedWorkflowDefinition,
      annotations: validatedAnnotations,
    } = await this.dtoValidator.validateAndConvertWorkflowDefinition({
      workflowDefinition: createWorkflowOptions.workflowDefinition,
      triggerEvent: createWorkflowOptions.triggerEvent,
      filters: updatedFilters,
      annotations: createWorkflowOptions.annotations ?? [],
      userId: createWorkflowOptions.createdBy,
      organizationId: createWorkflowOptions.organizationId,
    });

    workflow.version = 1;
    workflow.name = createWorkflowOptions.name;
    workflow.type = createWorkflowOptions.type ?? WorkflowType.MANUAL;
    workflow.subType =
      createWorkflowOptions.subType ?? WorkflowSubType.WORKFLOW;
    workflow.triggerEvent = createWorkflowOptions.triggerEvent;
    workflow.filters = updatedFilters;
    workflow.annotations = validatedAnnotations;
    workflow.workflowDefinitionType =
      createWorkflowOptions.workflowDefinitionType;
    workflow.workflowDefinition = validatedWorkflowDefinition;
    workflow.workflowEngineId = createWorkflowOptions.workflowEngineId;
    workflow.executingAgent = createWorkflowOptions.executingAgent;
    workflow.organizationId = createWorkflowOptions.organizationId;
    workflow.teamId = createWorkflowOptions.teamId;
    workflow.createdBy = createWorkflowOptions.createdBy;
    workflow.metadata = createWorkflowOptions.metadata ?? {};

    return await this.workflowRepository.save(workflow);
  }

  /**
   * Creates a new version of the existing workflow with updated trigger event / filters / workflow engine config / agent configuration.
   * Marks the old version as inactive.
   *
   * @param {UpdateWorkflowOptions} updateWorkflowOptions - Options bag for the updated workflow
   * @returns {Promise<Workflow>} The updated workflow object
   */
  async updateWorkflow(
    updateWorkflowOptions: UpdateWorkflowOptions,
  ): Promise<Workflow> {
    const existingWorkflow = await this.workflowRepository.findByCondition({
      where: {
        uniqueIdentifier: updateWorkflowOptions.workflowUniqueIdentifier,
        organizationId: updateWorkflowOptions.organizationId,
      },
      order: {
        version: "DESC",
      },
      relations: ["triggerEvent"],
    });
    if (!existingWorkflow) {
      throw new BadRequestException("Workflow not found with provided UID");
    }

    if (
      updateWorkflowOptions.organizationId !== existingWorkflow.organizationId
    ) {
      throw new UnauthorizedException(
        "User from different organization cannot update this workflow",
      );
    }

    const newWorkflow = new Workflow();

    newWorkflow.uniqueIdentifier = existingWorkflow.uniqueIdentifier;
    newWorkflow.organizationId = existingWorkflow.organizationId;
    newWorkflow.version = existingWorkflow.version + 1;
    newWorkflow.type = existingWorkflow.type;
    newWorkflow.subType = existingWorkflow.subType;

    newWorkflow.name = updateWorkflowOptions.name ?? existingWorkflow.name;
    newWorkflow.triggerEvent =
      updateWorkflowOptions.triggerEvent ?? existingWorkflow.triggerEvent;
    newWorkflow.workflowDefinitionType =
      updateWorkflowOptions.workflowDefinitionType ??
      existingWorkflow.workflowDefinitionType;
    newWorkflow.workflowEngineId =
      updateWorkflowOptions.workflowEngineId ??
      existingWorkflow.workflowEngineId;
    newWorkflow.executingAgent =
      updateWorkflowOptions.executingAgent ?? existingWorkflow.executingAgent;
    newWorkflow.createdBy = updateWorkflowOptions.createdBy;
    newWorkflow.createdAt = existingWorkflow.createdAt;
    newWorkflow.metadata =
      updateWorkflowOptions.metadata ?? existingWorkflow.metadata;
    newWorkflow.teamId =
      updateWorkflowOptions.teamId ?? existingWorkflow.teamId;

    existingWorkflow.isActive = false;

    if (
      updateWorkflowOptions.workflowDefinition ||
      updateWorkflowOptions.annotations ||
      updateWorkflowOptions.filters
    ) {
      const {
        steps: validatedWorkflowDefinition,
        annotations: validatedAnnotations,
      } = await this.dtoValidator.validateAndConvertWorkflowDefinition({
        workflowDefinition:
          updateWorkflowOptions.workflowDefinition ??
          existingWorkflow.workflowDefinition,
        triggerEvent:
          updateWorkflowOptions.triggerEvent ?? existingWorkflow.triggerEvent,
        filters: updateWorkflowOptions.filters ?? existingWorkflow.filters,
        annotations:
          updateWorkflowOptions.annotations ?? existingWorkflow.annotations,
        userId: updateWorkflowOptions.createdBy,
        organizationId: existingWorkflow.organizationId,
      });

      newWorkflow.workflowDefinition = validatedWorkflowDefinition;
      newWorkflow.annotations = validatedAnnotations;
      newWorkflow.filters =
        updateWorkflowOptions.filters ?? existingWorkflow.filters;
    }

    if (
      updateWorkflowOptions.filters &&
      updateWorkflowOptions.teamId &&
      existingWorkflow.triggerEvent.metadata?.pathToTeamId
    ) {
      const teamIdPath = existingWorkflow.triggerEvent.metadata.pathToTeamId;

      const updatedFilters = {
        "~and": [
          {
            [`{{${teamIdPath}}}`]: {
              "~eq": updateWorkflowOptions.teamId,
            },
          },
          newWorkflow.filters,
        ],
      };

      newWorkflow.filters = updatedFilters;
    }

    const updatedWorkflow = await this.transactionService.runInTransaction(
      async (txnContext) => {
        await this.workflowRepository.saveWithTxn(txnContext, existingWorkflow);
        return await this.workflowRepository.saveWithTxn(
          txnContext,
          newWorkflow,
        );
      },
    );

    return updatedWorkflow;
  }
}
