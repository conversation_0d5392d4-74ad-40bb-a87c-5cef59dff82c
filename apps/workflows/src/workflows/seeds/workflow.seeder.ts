import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  UserType,
  WorkflowEvent,
  WorkflowSubType,
  WorkflowType,
} from "@repo/thena-platform-entities";
import {
  AppsPlatformEvents,
  TicketEvents,
} from "@repo/thena-shared-interfaces";
import { UsersGrpcClient } from "../../common/grpc/users-grpc.client";
import { CreateWorkflowDto } from "../dtos/workflow.dto";
import { CreateWorkflowOptions } from "../interfaces/workflow.interface";
import { EventActionService } from "../services/event.action.service";
import { WorkflowEngineActionService } from "../services/workflow-engine.action.service";
import { WorkflowActionService } from "../services/workflow.action.service";
@Injectable()
export class WorkflowSeeder {
  private eventTypeToEventMap: Map<string, WorkflowEvent> = new Map();

  private organizationId: string;
  private teamIds: string[];
  private appId: string;
  private workflowBotUser: string;

  constructor(
    private readonly workflowActionService: WorkflowActionService,
    private readonly eventActionService: EventActionService,
    private readonly workflowEngineActionService: WorkflowEngineActionService,
    private readonly usersGrpcClient: UsersGrpcClient,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  /**
   * Initialize the seeder by loading events from registry
   */
  private async initialize(
    organizationId: string,
    teamIds: string[],
    appId?: string,
  ): Promise<void> {
    this.organizationId = organizationId;
    this.teamIds = teamIds;
    this.appId = appId;

    // Fetch workflow bot user
    const workflowBotUser = await this.usersGrpcClient.fetchWorkflowBot(
      this.organizationId,
    );
    this.workflowBotUser = workflowBotUser.id;

    // Load platform and registered events
    const platformEvents = await this.eventActionService.findPlatformEvents();
    const registeredEvents = await this.eventActionService.findRegisteredEvents(
      this.organizationId,
    );
    const allEvents = [...platformEvents, ...registeredEvents];
    this.eventTypeToEventMap = new Map(
      allEvents.map((event) => [event.eventType, event]),
    );
  }

  /**
   * Seed workflows at the team level when a team is created
   */
  async seedOnTeamCreation(
    organizationId: string,
    teamId: string,
  ): Promise<void> {
    await this.initialize(organizationId, [teamId]);

    const workflowPromises = await Promise.allSettled([
      this.seedTicketAcknowledgement(),
      this.seedReOpenOnCustomerReply(),
      this.seedStatusChangeOnCustomerReply(),
      this.seedStatusChangeOnInternalMemberReply(),
      this.seedBumpAndClose(),
      this.seedFollowUpWithCustomer(),
      this.seedAutoCloseInactiveTickets(),
      this.seedAutoAssignTicketToAgent(),
    ]);

    workflowPromises.forEach((workflowPromise) => {
      if (workflowPromise.status !== "fulfilled") {
        this.logger.error(
          `[WorkflowSeeder] Failed to seed workflow for teams ${JSON.stringify(
            this.teamIds,
          )} - ${workflowPromise.reason}`,
        );
      }
    });

    this.logger.log(
      `[WorkflowSeeder] Seeded workflows for teams ${JSON.stringify(
        this.teamIds,
      )}`,
    );
  }

  /**
   * Seed workflows at the organization level when an slack app is installed
   * Workflows containing slack events
   */
  async seedOnSlackAppInstallation(
    organizationId: string,
    teamIds: string[],
    appId: string,
    installationEventType:
      | AppsPlatformEvents.APP_INSTALLED
      | AppsPlatformEvents.APP_TEAM_ADDED,
  ): Promise<void> {
    if (!appId) {
      throw new Error(
        `[WorkflowSeeder] No app id provided to seed workflows for organization ${organizationId}`,
      );
    }

    await this.initialize(organizationId, teamIds, appId);

    const workflowsToCreate: Promise<void>[] = [];

    if (installationEventType === AppsPlatformEvents.APP_INSTALLED) {
      workflowsToCreate.push(
        this.seedAutoJoinSlackChannel(),
        this.seedNotifyCustomerJoinedSlackChannel(),
        this.seedNotifyCustomerLeftSlackChannel(),
      );
    } else if (installationEventType === AppsPlatformEvents.APP_TEAM_ADDED) {
      workflowsToCreate.push(
        this.seedNotifySLABreach(),
        this.seedNotifyTicketEscalation(),
      );
    }

    const workflowPromises = await Promise.allSettled(workflowsToCreate);

    workflowPromises.forEach((workflowPromise) => {
      if (workflowPromise.status !== "fulfilled") {
        this.logger.error(
          `[WorkflowSeeder] Failed to seed workflow for organization ${this.organizationId} on slack app installation - ${workflowPromise.reason}`,
        );
      }
    });

    this.logger.log(
      `[WorkflowSeeder] Seeded workflows for organization ${this.organizationId} on slack app installation`,
    );
  }

  /**
   * Seed workflow for ticket acknowledgement
   */
  private async seedTicketAcknowledgement(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Ticket Acknowledgement",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.CREATED,
      filters: {},
      workflowDefinition: [
        // Add comment to ticket's first comment / directly to ticket
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            content:
              "Ticket #{{context.event.message.payload.ticket.ticketId}} created successfully",
            contentHtml:
              "<p>Ticket #{{context.event.message.payload.ticket.ticketId}} created successfully</p>",
            contentJson: JSON.stringify({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  attrs: { textAlign: "left" },
                  content: [
                    {
                      type: "text",
                      text: "Ticket #{{context.event.message.payload.ticket.ticketId}} created successfully",
                    },
                  ],
                },
              ],
            }),
            commentType: "comment",
            commentVisibility: "public",
            parentCommentId: "{{context.event.message.payload.comment.id}}",
            shouldSendEmail: true,
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for re-opening tickets when customer replies
   */
  private async seedReOpenOnCustomerReply(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Re-Open on Customer Reply",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.COMMENT_ADDED,
      filters: {
        "~and": [
          // Only trigger on comments
          {
            "{{context.event.message.payload.comment.commentType}}": {
              "~eq": "comment",
            },
          },
          // Only trigger on public comments
          {
            "{{context.event.message.payload.comment.commentVisibility}}": {
              "~eq": "public",
            },
          },
          // Only trigger on comments from a customer
          {
            "{{context.event.message.payload.comment.customerContact}}": {
              "~isempty": false,
            },
          },
          // Only trigger when ticket status is closed
          {
            "~or": [
              {
                "{{context.event.message.payload.ticket.status.name}}": {
                  "~eq": "Closed",
                },
              },
              {
                "{{context.event.message.payload.ticket.status.parentStatus.name}}":
                  {
                    "~eq": "Closed",
                  },
              },
            ],
          },
        ],
      },
      workflowDefinition: [
        // Move to open
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: "tickets:update-ticket-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            id: "{{context.event.message.payload.ticket.id}}",
            statusName: "Open",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for moving ticket status to "Waiting on us" if it is in "Waiting on customer" and customer replies
   */
  private async seedStatusChangeOnCustomerReply(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Move to Waiting on us if ticket is in Waiting on customer and customer replies",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.COMMENT_ADDED,
      filters: {
        "~and": [
          // Only trigger on comments
          {
            "{{context.event.message.payload.comment.commentType}}": {
              "~eq": "comment",
            },
          },
          // Only trigger on public comments
          {
            "{{context.event.message.payload.comment.commentVisibility}}": {
              "~eq": "public",
            },
          },
          // Only trigger on comments from a customer
          {
            "{{context.event.message.payload.comment.customerContact}}": {
              "~isempty": false,
            },
          },
          // Only trigger when ticket status is waiting on customer
          {
            "{{context.event.message.payload.ticket.status.name}}": {
              "~eq": "Waiting on customer",
            },
          },
        ],
      },
      workflowDefinition: [
        // Move to waiting on us
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: "tickets:update-ticket-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            id: "{{context.event.message.payload.ticket.id}}",
            statusName: "Waiting on us",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for moving ticket status to "Waiting on customer" if it is in "Waiting on us" and internal member replies
   */
  private async seedStatusChangeOnInternalMemberReply(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Move to Waiting on customer if ticket is in Waiting on us and internal member replies",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.COMMENT_ADDED,
      filters: {
        "~and": [
          // Only trigger on comments
          {
            "{{context.event.message.payload.comment.commentType}}": {
              "~eq": "comment",
            },
          },
          // Only trigger on public comments
          {
            "{{context.event.message.payload.comment.commentVisibility}}": {
              "~eq": "public",
            },
          },
          // Only trigger on comments from a internal member
          {
            "{{context.event.message.payload.comment.customerContact}}": {
              "~isempty": true,
            },
          },
          // Only trigger when ticket status is waiting on us
          {
            "{{context.event.message.payload.ticket.status.name}}": {
              "~eq": "Waiting on us",
            },
          },
        ],
      },
      workflowDefinition: [
        // Move to waiting on customer
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: "tickets:update-ticket-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            id: "{{context.event.message.payload.ticket.id}}",
            statusName: "Waiting on customer",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for bumping and closing tickets if no customer comment is found
   */
  private async seedBumpAndClose(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Bump and Close",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.COMMENT_ADDED,
      filters: {
        "~and": [
          // Only trigger on comments
          {
            "{{context.event.message.payload.comment.commentType}}": {
              "~eq": "comment",
            },
          },
          // Only trigger on public comments
          {
            "{{context.event.message.payload.comment.commentVisibility}}": {
              "~eq": "public",
            },
          },
          // Only trigger on comments from a agent
          {
            "{{context.event.message.payload.comment.customerContact}}": {
              "~isempty": true,
            },
          },
          // Only trigger on comments from a agent
          {
            "{{context.event.message.actor.type}}": {
              "~in": [UserType.USER, UserType.ORG_ADMIN],
            },
          },
        ],
      },
      workflowDefinition: [
        // Sleep for 2 days
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: "workflows:sleep-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            duration: 60 * 60 * 24 * 2, // 2 days
          },
        },
        // Check if there is a customer comment
        {
          stepIdentifier: 2,
          activity: {
            uniqueIdentifier: "communications:has-customer-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            since: "{{context.event.message.payload.comment.createdAt}}",
          },
          dependencies: [1],
        },
        // Check if there is a internal member comment
        {
          stepIdentifier: 3,
          activity: {
            uniqueIdentifier:
              "communications:has-internal-member-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            since: "{{context.event.message.payload.comment.createdAt}}",
            commentType: "comment",
            commentVisibility: "public",
          },
          dependencies: [1],
        },
        // Sleep for 7 days if no internal member or customer comment was found
        {
          filters: {
            "~and": [
              {
                "{{context.outputs.2.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.outputs.3.hasComment}}": {
                  "~eq": false,
                },
              },
            ],
          },
          stepIdentifier: 4,
          activity: {
            uniqueIdentifier: "workflows:sleep-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            duration: 60 * 60 * 24 * 7, // 7 days
          },
          dependencies: [2, 3],
        },
        // Bump if no internal member or customer comment was found
        {
          filters: {
            "~and": [
              {
                "{{context.outputs.2.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.outputs.3.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isnull": false,
                },
              },
            ],
          },
          stepIdentifier: 5,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            content: "Just checking in — let us know if you still need help",
            contentHtml:
              "<p>Just checking in — let us know if you still need help</p>",
            contentJson: JSON.stringify({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  attrs: { textAlign: "left" },
                  content: [
                    {
                      type: "text",
                      text: "Just checking in — let us know if you still need help",
                    },
                  ],
                },
              ],
            }),
            commentType: "comment",
            commentVisibility: "public",
            parentCommentId:
              "{{context.event.message.payload.comment.parentCommentId}}",
            shouldSendEmail: true,
          },
          dependencies: [2, 3],
        },
        // Bump if no internal member or customer comment was found
        {
          filters: {
            "~and": [
              {
                "{{context.outputs.2.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.outputs.3.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isnull": true,
                },
              },
            ],
          },
          stepIdentifier: 6,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            content: "Just checking in — let us know if you still need help",
            contentHtml:
              "<p>Just checking in — let us know if you still need help</p>",
            contentJson: JSON.stringify({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  attrs: { textAlign: "left" },
                  content: [
                    {
                      type: "text",
                      text: "Just checking in — let us know if you still need help",
                    },
                  ],
                },
              ],
            }),
            commentType: "comment",
            commentVisibility: "public",
            parentCommentId: "{{context.event.message.payload.comment.id}}",
            shouldSendEmail: true,
          },
          dependencies: [2, 3],
        },
        // Check if there is a customer comment
        {
          stepIdentifier: 7,
          activity: {
            uniqueIdentifier: "communications:has-customer-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            since: "{{context.event.message.payload.comment.createdAt}}",
          },
          dependencies: [4],
        },
        // Check if there is a internal member comment
        {
          stepIdentifier: 8,
          activity: {
            uniqueIdentifier:
              "communications:has-internal-member-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            since: "{{context.event.message.payload.comment.createdAt}}",
            commentType: "comment",
            commentVisibility: "public",
          },
          dependencies: [4],
        },
        // Bump if no customer comment was found
        {
          filters: {
            "~and": [
              {
                "{{context.outputs.7.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.outputs.8.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isnull": false,
                },
              },
            ],
          },
          stepIdentifier: 9,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            content:
              "Closing this ticket as we haven't heard from you in a while",
            contentHtml:
              "<p>Closing this ticket as we haven't heard from you in a while</p>",
            contentJson: JSON.stringify({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  attrs: { textAlign: "left" },
                  content: [
                    {
                      type: "text",
                      text: "Closing this ticket as we haven't heard from you in a while",
                    },
                  ],
                },
              ],
            }),
            commentType: "comment",
            commentVisibility: "public",
            parentCommentId:
              "{{context.event.message.payload.comment.parentCommentId}}",
            shouldSendEmail: true,
          },
          dependencies: [7, 8],
        },
        // Bump if no new comment was found
        {
          filters: {
            "~and": [
              {
                "{{context.outputs.7.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.outputs.8.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isnull": true,
                },
              },
            ],
          },
          stepIdentifier: 10,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            content:
              "Closing this ticket as we haven't heard from you in a while",
            contentHtml:
              "<p>Closing this ticket as we haven't heard from you in a while</p>",
            contentJson: JSON.stringify({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  attrs: { textAlign: "left" },
                  content: [
                    {
                      type: "text",
                      text: "Closing this ticket as we haven't heard from you in a while",
                    },
                  ],
                },
              ],
            }),
            commentType: "comment",
            commentVisibility: "public",
            parentCommentId: "{{context.event.message.payload.comment.id}}",
            shouldSendEmail: true,
          },
          dependencies: [7, 8],
        },
        // Close if no new comment was found
        {
          filters: {
            "~and": [
              {
                "{{context.outputs.7.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.outputs.8.hasComment}}": {
                  "~eq": false,
                },
              },
            ],
          },
          stepIdentifier: 11,
          activity: {
            uniqueIdentifier: "tickets:update-ticket-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            id: "{{context.event.message.payload.ticket.id}}",
            statusName: "Closed",
          },
          dependencies: [7, 8],
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for following up with customer if no customer reply was found
   */
  private async seedFollowUpWithCustomer(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Follow up with customer if no customer reply was found",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.COMMENT_ADDED,
      filters: {
        "~and": [
          // Only trigger on comments
          {
            "{{context.event.message.payload.comment.commentType}}": {
              "~eq": "comment",
            },
          },
          // Only trigger on public comments
          {
            "{{context.event.message.payload.comment.commentVisibility}}": {
              "~eq": "public",
            },
          },
          // Only trigger on comments from a agent
          {
            "{{context.event.message.payload.comment.customerContact}}": {
              "~isempty": true,
            },
          },
          // Only trigger on comments from a agent
          {
            "{{context.event.message.actor.type}}": {
              "~in": [UserType.USER, UserType.ORG_ADMIN],
            },
          },
        ],
      },
      workflowDefinition: [
        // Sleep for 2 days
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: "workflows:sleep-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            duration: 60 * 60 * 24 * 2, // 2 days
          },
        },
        // Check if there is a customer comment
        {
          stepIdentifier: 2,
          activity: {
            uniqueIdentifier: "communications:has-customer-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            since: "{{context.event.message.payload.comment.createdAt}}",
          },
          dependencies: [1],
        },
        // Check if there is a internal member comment
        {
          stepIdentifier: 3,
          activity: {
            uniqueIdentifier:
              "communications:has-internal-member-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            since: "{{context.event.message.payload.comment.createdAt}}",
            commentType: "comment",
            commentVisibility: "public",
          },
          dependencies: [1],
        },
        // Bump if no customer comment or internal member comment was found
        {
          filters: {
            "~and": [
              {
                "{{context.outputs.2.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.outputs.3.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isnull": false,
                },
              },
            ],
          },
          stepIdentifier: 4,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            content: "Just checking in — let us know if you still need help",
            contentHtml:
              "<p>Just checking in — let us know if you still need help</p>",
            contentJson: JSON.stringify({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  attrs: { textAlign: "left" },
                  content: [
                    {
                      type: "text",
                      text: "Just checking in — let us know if you still need help",
                    },
                  ],
                },
              ],
            }),
            commentType: "comment",
            commentVisibility: "public",
            parentCommentId:
              "{{context.event.message.payload.comment.parentCommentId}}",
            shouldSendEmail: true,
          },
          dependencies: [2, 3],
        },
        // Bump if no customer comment was found
        {
          filters: {
            "~and": [
              {
                "{{context.outputs.2.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.outputs.3.hasComment}}": {
                  "~eq": false,
                },
              },
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isnull": true,
                },
              },
            ],
          },
          stepIdentifier: 5,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            entityType: "ticket",
            entityId: "{{context.event.message.payload.ticket.id}}",
            content: "Just checking in — let us know if you still need help",
            contentHtml:
              "<p>Just checking in — let us know if you still need help</p>",
            contentJson: JSON.stringify({
              type: "doc",
              content: [
                {
                  type: "paragraph",
                  attrs: { textAlign: "left" },
                  content: [
                    {
                      type: "text",
                      text: "Just checking in — let us know if you still need help",
                    },
                  ],
                },
              ],
            }),
          },
          dependencies: [2, 3],
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for auto closing inactive tickets
   */
  private async seedAutoCloseInactiveTickets(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Auto close inactive tickets",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.UPDATED,
      filters: {
        // Only trigger on tickets that are pending close
        "{{context.event.message.payload.ticket.status.name}}": {
          "~eq": "Pending close",
        },
      },
      workflowDefinition: [
        // Sleep for 30 days
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: "workflows:sleep-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            duration: 60 * 60 * 24 * 30, // 30 days
          },
        },
        // Check if ticket status has remained same
        {
          stepIdentifier: 2,
          activity: {
            uniqueIdentifier:
              "tickets:check-if-ticket-status-remained-same-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            ticketId: "{{context.event.message.payload.ticket.id}}",
            duration: 60 * 60 * 24 * 30, // 30 days
          },
          dependencies: [1],
        },
        // Close the ticket if status has remained same
        {
          filters: {
            "{{context.outputs.2.statusRemainedSame}}": {
              "~eq": true,
            },
          },
          stepIdentifier: 3,
          activity: {
            uniqueIdentifier: "tickets:update-ticket-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            id: "{{context.event.message.payload.ticket.id}}",
            statusName: "Closed",
          },
          dependencies: [2],
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for auto assigning an unassigned ticket to the responding agent
   */
  private async seedAutoAssignTicketToAgent(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Auto assign an unassigned ticket to the responding agent",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.COMMENT_ADDED,
      filters: {
        "~and": [
          // Only trigger on comments
          {
            "{{context.event.message.payload.comment.commentType}}": {
              "~eq": "comment",
            },
          },
          // Only trigger on public comments
          {
            "{{context.event.message.payload.comment.commentVisibility}}": {
              "~eq": "public",
            },
          },
          // Only trigger on comments from a agent
          {
            "{{context.event.message.payload.comment.customerContact}}": {
              "~isempty": true,
            },
          },
          // Only trigger on tickets that are unassigned
          {
            "{{context.event.message.payload.ticket.assignedAgent.id}}": {
              "~isnull": true,
            },
          },
        ],
      },
      workflowDefinition: [
        // Assign the ticket to the responding agent
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: "tickets:assign-ticket-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            ticketId: "{{context.event.message.payload.ticket.id}}",
            agentId: "{{context.event.message.payload.comment.author.id}}",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for notifying SLA breach
   * ! Involves slack activity
   */
  private async seedNotifySLABreach(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Notify SLA breach on slack and email",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.SLA_BREACHED,
      filters: {},
      workflowDefinition: [
        // Send slack message
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: `slack.postMessage-${this.appId}-${this.organizationId}`,
            autoUpgradeToLatestVersion: true,
          },
          input: {
            channel: "CXXXXXXX",
            text: "{{context.event.message.payload.sla.metric}} SLA breach detected on ticket {{context.event.message.payload.ticket.title}}",
          },
        },
        // Send email
        {
          stepIdentifier: 2,
          activity: {
            uniqueIdentifier: "email:send-email-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            from: "<EMAIL>",
            to: "<EMAIL>",
            subject: "SLA breach detected",
            textBody:
              "{{context.event.message.payload.sla.metric}} SLA breach detected on ticket {{context.event.message.payload.ticket.title}}",
            htmlBody:
              "<p>{{context.event.message.payload.sla.metric}} SLA breach detected on ticket {{context.event.message.payload.ticket.title}}</p>",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for notifying ticket escalation
   * ! Involves slack activity
   */
  private async seedNotifyTicketEscalation(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Notify ticket escalation",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: TicketEvents.ESCALATED,
      filters: {},
      workflowDefinition: [
        // Send slack message
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: `slack.postMessage-${this.appId}-${this.organizationId}`,
            autoUpgradeToLatestVersion: true,
          },
          input: {
            channel: "CXXXXXXX",
            text: "Ticket {{context.event.message.payload.ticket.title}} has been escalated",
          },
        },
        // Send email
        {
          stepIdentifier: 2,
          activity: {
            uniqueIdentifier: "email:send-email-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            from: "<EMAIL>",
            to: "<EMAIL>",
            subject: "Ticket escalation",
            textBody:
              "Ticket {{context.event.message.payload.ticket.title}} has been escalated",
            htmlBody:
              "<p>Ticket {{context.event.message.payload.ticket.title}} has been escalated</p>",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto);
  }

  /**
   * Seed workflow for auto joining slack channel
   * ! Involves slack event and activity
   */
  private async seedAutoJoinSlackChannel(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Auto join slack channel with name starts with ext",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: "slack:channel:created",
      filters: {
        // Only trigger on channels that start with "ext"
        "{{context.event.message.channel.name}}": {
          "~starts": "ext",
        },
      },
      workflowDefinition: [
        // Join slack channel
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: `slack.joinChannel-${this.appId}-${this.organizationId}`,
            autoUpgradeToLatestVersion: true,
          },
          input: {
            channel: "{{context.event.message.channel.id}}",
            platformTeam: "TXXXXXXXX",
            channelType: "customer_channel",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto, true);
  }

  /**
   * Seed workflow for notifying customer joined slack channel
   * ! Involves slack event and activity
   */
  private async seedNotifyCustomerJoinedSlackChannel(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Notify customer joined slack channel",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: "slack:member:joined",
      filters: {
        "{{context.event.message.userType}}": {
          "~eq": "customer",
        },
      },
      workflowDefinition: [
        // Send slack message
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: `slack.postMessage-${this.appId}-${this.organizationId}`,
            autoUpgradeToLatestVersion: true,
          },
          input: {
            channel: "CXXXXXXX",
            text: "{{context.event.message.userInfo.name}} joined slack channel {{context.event.message.channel_name}}",
            // attachments: [
            //   {
            //     color: "#f2c744",
            //     blocks: [
            //       {
            //         type: "header",
            //         text: {
            //           type: "plain_text",
            //           text: "User Joined Channel",
            //           emoji: true,
            //         },
            //       },
            //       {
            //         type: "context",
            //         elements: [
            //           {
            //             type: "image",
            //             image_url: "{{context.event.message.userInfo.avatar}}",
            //           },
            //           {
            //             type: "mrkdwn",
            //             text: "*{{context.event.message.userInfo.name}}}*",
            //             verbatim: false,
            //           },
            //           {
            //             type: "mrkdwn",
            //             text: `Joined #{{context.event.message.channel_name}}`,
            //             verbatim: false,
            //           },
            //         ],
            //       },
            //       {
            //         type: "context",
            //         elements: [
            //           {
            //             type: "mrkdwn",
            //             text: "\n*Reason*: `Joined Channel`",
            //             verbatim: false,
            //           },
            //         ],
            //       },
            //       {
            //         type: "divider",
            //       },
            //       {
            //         type: "context",
            //         elements: [
            //           {
            //             type: "mrkdwn",
            //             text: `Joined on {{context.event.message.timestamp}}`,
            //             verbatim: false,
            //           },
            //         ],
            //       },
            //     ],
            //   },
            // ],
          },
        },
        // Send email
        {
          stepIdentifier: 2,
          activity: {
            uniqueIdentifier: "email:send-email-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            from: "<EMAIL>",
            to: "<EMAIL>",
            subject: "New customer joined slack channel",
            textBody:
              "{{context.event.message.userInfo.name}} joined slack channel {{context.event.message.channel_name}}",
            htmlBody:
              "<p>{{context.event.message.userInfo.name}} joined slack channel {{context.event.message.channel_name}}</p>",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto, true);
  }

  /**
   * Seed workflow for notifying customer joined slack channel
   * ! Involves slack event and activity
   */
  private async seedNotifyCustomerLeftSlackChannel(): Promise<void> {
    const workflowDto: CreateWorkflowDto = {
      name: "Notify customer left slack channel",
      type: WorkflowType.MANUAL,
      subType: WorkflowSubType.SEEDED,
      triggerEvent: "slack:member:left",
      filters: {
        "{{context.event.message.userType}}": {
          "~eq": "customer",
        },
      },
      workflowDefinition: [
        // Send slack message
        {
          stepIdentifier: 1,
          activity: {
            uniqueIdentifier: `slack.postMessage-${this.appId}-${this.organizationId}`,
            autoUpgradeToLatestVersion: true,
          },
          input: {
            channel: "CXXXXXXX",
            text: "{{context.event.message.userInfo.name}} left slack channel {{context.event.message.channel_name}}",
            // attachments: [
            //   {
            //     color: "#f2c744",
            //     blocks: [
            //       {
            //         type: "header",
            //         text: {
            //           type: "plain_text",
            //           text: "User Left Channel",
            //           emoji: true,
            //         },
            //       },
            //       {
            //         type: "context",
            //         elements: [
            //           {
            //             type: "image",
            //             image_url: "{{context.event.message.userInfo.avatar}}",
            //           },
            //           {
            //             type: "mrkdwn",
            //             text: "*{{context.event.message.userInfo.name}}}*",
            //             verbatim: false,
            //           },
            //           {
            //             type: "mrkdwn",
            //             text: `Left #{{context.event.message.channel_name}}`,
            //             verbatim: false,
            //           },
            //         ],
            //       },
            //       {
            //         type: "context",
            //         elements: [
            //           {
            //             type: "mrkdwn",
            //             text: "\n*Reason*: `Left Channel`",
            //             verbatim: false,
            //           },
            //         ],
            //       },
            //       {
            //         type: "divider",
            //       },
            //       {
            //         type: "context",
            //         elements: [
            //           {
            //             type: "mrkdwn",
            //             text: `Left on {{context.event.message.timestamp}}`,
            //             verbatim: false,
            //           },
            //         ],
            //       },
            //     ],
            //   },
            // ],
          },
        },
        // Send email
        {
          stepIdentifier: 2,
          activity: {
            uniqueIdentifier: "email:send-email-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            from: "<EMAIL>",
            to: "<EMAIL>",
            subject: "Customer left slack channel",
            textBody:
              "{{context.event.message.userInfo.name}} left slack channel {{context.event.message.channel_name}}",
            htmlBody:
              "<p>{{context.event.message.userInfo.name}} left slack channel {{context.event.message.channel_name}}</p>",
          },
        },
      ],
    };

    await this.createAndDeactivateWorkflow(workflowDto, true);
  }

  /**
   * Helper method to create and deactivate a workflow
   */
  private async createAndDeactivateWorkflow(
    workflowDto: CreateWorkflowDto,
    orgLevelWorkflow: boolean = false,
  ): Promise<void> {
    const triggerEvent = this.eventTypeToEventMap.get(workflowDto.triggerEvent);
    if (!triggerEvent) {
      throw new Error(
        `Trigger event ${workflowDto.triggerEvent} not found in registry`,
      );
    }

    // TODO FOR LATER: Taking the default workflow engine for now. Have to decide on a logic on how to select a workflow engine.
    const workflowEngine =
      await this.workflowEngineActionService.findDefaultWorkflowEngine();

    // ALERT: This is the only workflow type right now. If we add more types, we need to update this.
    const workflowDefinitionType = "dynamicWorkflow";

    const createOptions: CreateWorkflowOptions = {
      ...workflowDto,
      organizationId: this.organizationId,
      triggerEvent,
      createdBy: this.workflowBotUser,
      executingAgent: this.workflowBotUser,
      workflowEngineId: workflowEngine.id,
      workflowDefinitionType,
    };

    if (orgLevelWorkflow) {
      const workflow = await this.workflowActionService.createWorkflow(
        createOptions,
      );
      await this.workflowActionService.toggleWorkflow(
        workflow.uniqueIdentifier,
        this.organizationId,
        false,
      );
    } else {
      await Promise.all(
        this.teamIds.map(async (teamId) => {
          const createTeamWorkflowOptions: CreateWorkflowOptions = {
            ...createOptions,
            teamId,
          };

          const workflow = await this.workflowActionService.createWorkflow(
            createTeamWorkflowOptions,
          );
          await this.workflowActionService.toggleWorkflow(
            workflow.uniqueIdentifier,
            this.organizationId,
            false,
          );
        }),
      );
    }
  }
}
