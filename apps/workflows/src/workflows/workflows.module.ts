import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { RedisCacheProvider } from "@repo/nestjs-commons/cache";
import { GrpcClient, ThrottlerService } from "@repo/nestjs-commons/providers";
import {
  CachedWorkflowEngineRepository,
  CachedWorkflowEventRepository,
  CachedWorkflowRepository,
  TransactionService,
  Workflow,
  WorkflowActivity,
  WorkflowActivityRepository,
  WorkflowEngine,
  WorkflowEngineRepository,
  WorkflowEvent,
  WorkflowEventLog,
  WorkflowEventLogRepository,
  WorkflowEventRepository,
  WorkflowInstance,
  WorkflowInstanceActivity,
  WorkflowInstanceActivityRepository,
  WorkflowInstanceRepository,
  WorkflowRepository,
} from "@repo/thena-platform-entities";
import { AuthModule } from "../auth/auth.module";
import { UtilsModule } from "../common";
import { CommonModule } from "../common/common.module";
import { ConfigModule } from "../config/config.module";
import { ConfigService } from "../config/config.service";
import { getWorkflowsDBConfig } from "../config/db.config";
import { EventProducerModule } from "../event-producer/event-producer.module";
import * as activities from "./core/activities";
import { DirectActivityExecutor } from "./core/activities/direct.activity-executor";
import { GrpcActivityExecutor } from "./core/activities/grpc.activity-executor";
import { HttpActivityExecutor } from "./core/activities/http.activity-executor";
import { DirectRegistryFetcher } from "./core/registry-sync/direct.registry-fetcher";
import { RegistrySyncService } from "./core/registry-sync/registry-sync.service";
import { WorkflowEngineFactory } from "./core/workflow-engines/workflow-engine.factory";
import { WorkflowInstanceService } from "./core/workflow-instance/workflow-instance.service";
import { DtoValidator } from "./dtos/dto.validator";
import { ActivityActionService } from "./services/activity.action.service";
import { EventLogActionService } from "./services/event-log.action.service";
import { EventActionService } from "./services/event.action.service";
import { WorkflowEngineActionService } from "./services/workflow-engine.action.service";
import { WorkflowInstanceActivityActionService } from "./services/workflow-instance-activity.action.service";
import { WorkflowInstanceActionService } from "./services/workflow-instance.action.service";
import { WorkflowActionService } from "./services/workflow.action.service";
import { WorkflowsGrpcController } from "./workflows-grpc.controller";
import { WorkflowsController } from "./workflows.controller";

@Module({
  imports: [
    ConfigModule,
    AuthModule,
    CommonModule,
    UtilsModule,
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) =>
        getWorkflowsDBConfig(configService),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      // Event Registry
      WorkflowEvent,
      WorkflowEventRepository,
      // Activity Registry
      WorkflowActivity,
      WorkflowActivityRepository,
      // Workflow Engine Registry
      WorkflowEngine,
      WorkflowEngineRepository,
      // Event Log
      WorkflowEventLog,
      WorkflowEventLogRepository,
      // Workflow
      Workflow,
      WorkflowRepository,
      // Workflow Instance
      WorkflowInstance,
      WorkflowInstanceRepository,
      // Workflow Instance Activity
      WorkflowInstanceActivity,
      WorkflowInstanceActivityRepository,
    ]),
    HttpModule,
    EventProducerModule,
  ],
  providers: [
    // gRPC Clients
    GrpcClient,

    // Core
    ...Object.values(activities),

    // Activity Executors
    HttpActivityExecutor,
    GrpcActivityExecutor,
    DirectActivityExecutor,

    // Registry sync service
    RegistrySyncService,
    DirectRegistryFetcher,

    WorkflowEngineFactory,
    WorkflowInstanceService,

    // DTO Validator
    DtoValidator,

    // Transaction service
    TransactionService,

    // Redis cache provider
    RedisCacheProvider,

    // Throttler service
    ThrottlerService,

    // Services
    EventActionService,
    ActivityActionService,
    WorkflowEngineActionService,
    EventLogActionService,
    WorkflowActionService,
    WorkflowInstanceActionService,
    WorkflowInstanceActivityActionService,

    // Repositories
    WorkflowEventRepository,
    CachedWorkflowEventRepository,
    WorkflowActivityRepository,
    WorkflowEngineRepository,
    CachedWorkflowEngineRepository,
    WorkflowEventLogRepository,
    WorkflowRepository,
    CachedWorkflowRepository,
    WorkflowInstanceRepository,
    WorkflowInstanceActivityRepository,
  ],
  controllers: [WorkflowsController, WorkflowsGrpcController],
  exports: [
    // Core
    ...Object.values(activities),

    // Activity Executors
    HttpActivityExecutor,
    GrpcActivityExecutor,
    DirectActivityExecutor,

    ThrottlerService,

    WorkflowEngineFactory,
    WorkflowInstanceService,

    // Services
    EventActionService,
    ActivityActionService,
    WorkflowEngineActionService,
    EventLogActionService,
    WorkflowActionService,
    WorkflowInstanceActionService,
    WorkflowInstanceActivityActionService,
  ],
})
export class WorkflowsModule {}
