import { BadRequestException, Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  EntityType,
  EventFilters,
  WorkflowActivity,
  WorkflowAnnotation,
  WorkflowEvent,
  WorkflowStep,
} from "@repo/thena-platform-entities";
import { AnnotatorGrpcClient } from "../../common/grpc/annotator-grpc.client";
import { ActivityActionService } from "../services/activity.action.service";
import { WorkflowStepDto } from "./workflow.dto";

/**
 * Options for validating a workflow definition.
 * @interface ValidateWorkflowDefinitionOptions
 */
interface ValidateWorkflowDefinitionOptions {
  /** Array of workflow steps to validate */
  workflowDefinition: WorkflowStepDto[];
  /** Event that triggers this workflow */
  triggerEvent: WorkflowEvent;
  /** Filters for the workflow */
  filters: EventFilters;
  /** Annotations for the workflow */
  annotations?: WorkflowAnnotation[];
  /** User ID for the request */
  userId: string;
  /** Organization ID for the request */
  organizationId: string;
}

/**
 * Options for validating a workflow step's input.
 * @interface ValidateWorkflowStepInputOptions
 */
interface ValidateWorkflowStepInputOptions {
  /** Input parameters for the workflow step */
  currentActivityInput: Record<string, unknown>;
  /** Activity associated with the current step */
  currentActivity: WorkflowActivity;
  /** Filters for current activity fan out */
  fanOutFilters: EventFilters;
  /** Map of all available activities */
  activities: Map<string, WorkflowActivity>;
  /** Event that triggers this workflow */
  triggerEvent: WorkflowEvent;
  /** IDs of steps this step depends on */
  dependencies: number[];
  /** ID of the current step */
  currentStepId: number;
  /** Map of all workflow steps by their IDs */
  stepsByIdentifier: Map<number, WorkflowStepDto>;
  /** Annotations for the workflow */
  annotations?: WorkflowAnnotation[];
}

@Injectable()
export class DtoValidator {
  private userId: string;
  private organizationId: string;

  constructor(
    private readonly activityActionService: ActivityActionService,
    private readonly annotatorGrpcClient: AnnotatorGrpcClient,

    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  /**
   * Validates and converts a workflow definition into executable workflow steps.
   * Ensures all activities exist and inputs are valid.
   *
   * @param {ValidateWorkflowDefinitionOptions} options - Options containing workflow definition and trigger event
   * @returns {Promise<WorkflowStep[]>} Array of validated and converted workflow steps
   * @throws {BadRequestException} If validation fails
   */
  async validateAndConvertWorkflowDefinition({
    workflowDefinition,
    triggerEvent,
    filters,
    annotations,
    userId,
    organizationId,
  }: ValidateWorkflowDefinitionOptions): Promise<{
    steps: WorkflowStep[];
    annotations: WorkflowAnnotation[];
  }> {
    this.userId = userId;
    this.organizationId = organizationId;

    const convertedSteps: WorkflowStep[] = [];

    const activityOptions = workflowDefinition.map((step) => step.activity);

    const compensationActivityOptions = workflowDefinition
      .filter((step) => step.compensationActivity?.activity !== undefined)
      .map((step) => step.compensationActivity.activity);

    const activities =
      await this.activityActionService.findActivitiesByUniqueIdentifiers([
        ...activityOptions,
        ...compensationActivityOptions,
      ]);

    const activitiesMap = new Map(
      activities.map((activity) => [activity.uniqueIdentifier, activity]),
    );

    const stepsByIdentifier = new Map(
      workflowDefinition.map((step) => [step.stepIdentifier, step]),
    );

    for (const step of workflowDefinition) {
      step.activity.autoUpgradeToLatestVersion =
        step.activity.autoUpgradeToLatestVersion ?? true;

      if (
        step.activity.autoUpgradeToLatestVersion === false &&
        !step.activity.version
      ) {
        throw new BadRequestException(
          `Activity ${step.activity.uniqueIdentifier}: Auto upgrade to latest version is disabled but no version is specified`,
        );
      }
      // Checks if all activities exist in our registry
      const activity = activitiesMap.get(step.activity.uniqueIdentifier);
      if (!activity) {
        throw new BadRequestException(
          `Activity ${step.activity.uniqueIdentifier}@${
            step.activity.version || "latest"
          } not found`,
        );
      }

      const compensationActivity = step.compensationActivity?.activity
        ? activitiesMap.get(step.compensationActivity.activity.uniqueIdentifier)
        : null;
      if (
        compensationActivity &&
        step.compensationActivity.activity.autoUpgradeToLatestVersion ===
          false &&
        !step.compensationActivity?.activity?.version
      ) {
        throw new BadRequestException(
          `Compensation activity ${step.compensationActivity.activity.uniqueIdentifier}: Auto upgrade to latest version is disabled but no version is specified`,
        );
      }
      if (
        step.compensationActivity?.activity?.uniqueIdentifier &&
        !compensationActivity
      ) {
        throw new BadRequestException(
          `Compensation activity ${
            step.compensationActivity.activity.uniqueIdentifier
          }@${
            step.compensationActivity.activity.version || "latest"
          } not found`,
        );
      }
      if (step.compensationActivity?.activity?.uniqueIdentifier) {
        step.compensationActivity.activity.autoUpgradeToLatestVersion =
          step.compensationActivity.activity?.autoUpgradeToLatestVersion ??
          true;
      }
    }

    this.checkCyclicDependenciesInWorkflowSteps(workflowDefinition);

    // Validate workflow filters
    annotations = await this.validateEventFilters(
      filters,
      activitiesMap,
      triggerEvent,
      stepsByIdentifier,
      annotations,
    );

    for (const [index, step] of workflowDefinition.entries()) {
      const stepIdentifier = step.stepIdentifier ?? index;

      // Parse and validate input
      annotations = await this.validateWorkflowStepInput({
        currentActivityInput: step.input,
        currentActivity: activitiesMap.get(step.activity.uniqueIdentifier),
        fanOutFilters: step.filters || {},
        activities: activitiesMap,
        triggerEvent,
        dependencies: step.dependencies || [],
        currentStepId: stepIdentifier,
        stepsByIdentifier,
        annotations,
      });

      if (step.compensationActivity?.activity) {
        annotations = await this.validateWorkflowStepInput({
          currentActivityInput: step.compensationActivity.input,
          currentActivity: activitiesMap.get(
            step.compensationActivity.activity.uniqueIdentifier,
          ),
          fanOutFilters: {},
          activities: activitiesMap,
          triggerEvent,
          dependencies: step.dependencies || [],
          currentStepId: stepIdentifier,
          stepsByIdentifier,
          annotations,
        });
      }

      // Convert dto to WorkflowStep
      const convertedStep: WorkflowStep = {
        stepIdentifier: stepIdentifier,
        activity: step.activity,
        input: step.input,
        retryPolicy: {
          maximumAttempts: step?.retryPolicy?.maximumAttempts || 3,
          initialInterval: step?.retryPolicy?.initialInterval || 5,
          backoffCoefficient: step?.retryPolicy?.backoffCoefficient || 1.5,
        },
        onFailure: step?.onFailure || "ABORT",
        executionTimeout: step?.executionTimeout || 5,
        isSleepActivity:
          (
            activitiesMap.get(step.activity.uniqueIdentifier)?.metadata as {
              isSleepActivity: boolean;
            }
          )?.isSleepActivity ?? false,
        dependencies: step?.dependencies || [],
        filters: step?.filters || {},
        ...(step.approver && {
          approver: step.approver,
        }),
        ...(step.compensationActivity?.activity && {
          compensationActivity: {
            activity: step.compensationActivity.activity,
            input: step.compensationActivity.input,
          },
        }),
      };

      convertedSteps.push(convertedStep);
    }

    return { steps: convertedSteps, annotations };
  }

  private validateKeyInSchema(key: string, schema: any): boolean {
    if (!schema) return false;

    // Handle direct type schemas (e.g., { "type": "string" })
    if (schema.type && schema.type !== "object") {
      return true; // If schema is a direct type, any key is valid
    }

    // Handle array type schemas
    if (schema.type === "array") {
      return true; // Array inputs can accept any key as it represents an index
    }

    if (key.includes(".")) {
      const [firstKey, ...rest] = key.split(".");
      if (schema.type === "object" && schema.properties?.[firstKey]) {
        return this.validateKeyInSchema(
          rest.join("."),
          schema.properties[firstKey],
        );
      }
      return false;
    }

    // Handle object type schemas
    if (schema.type === "object") {
      return !!schema.properties?.[key];
    }

    return false;
  }

  private getSchemaTypeForKey(key: string, schema: any): string | null {
    if (!schema) return null;

    // Handle nested object properties using dot notation
    if (key.includes(".")) {
      const [firstKey, ...rest] = key.split(".");
      if (schema.type === "object" && schema.properties?.[firstKey]) {
        return this.getSchemaTypeForKey(
          rest.join("."),
          schema.properties[firstKey],
        );
      }
      return null;
    }

    if (schema.type === "object" && schema.properties?.[key]) {
      return Array.isArray(schema.properties[key].type)
        ? schema.properties[key].type[0]
        : schema.properties[key].type;
    }

    return Array.isArray(schema.type) ? schema.type[0] : schema.type || null;
  }

  private validateValueType(value: unknown, expectedType: string): boolean {
    switch (expectedType) {
      case "string":
        return typeof value === "string";
      case "number":
        return typeof value === "number";
      case "boolean":
        return typeof value === "boolean";
      case "array":
        return Array.isArray(value);
      case "object":
        return (
          typeof value === "object" && value !== null && !Array.isArray(value)
        );
      default:
        return false;
    }
  }

  private async validateLiquidTagInEventSchema(
    key: string,
    triggerEvent: WorkflowEvent,
    expectedType?: string,
  ): Promise<{ isValid: boolean; relations?: string[] }> {
    if (!key || !triggerEvent) return { isValid: false };

    // First check if the key exists directly in the event schema
    if (this.validateKeyInSchema(key, triggerEvent.schema)) {
      return { isValid: true };
    }

    // Split the key into parts to check against annotations
    const keyParts = ["context", "event", ...key.split(".")];

    // Check for default annotation.
    if (triggerEvent.metadata?.pathToAnnotate) {
      const basePathParts = (
        triggerEvent.metadata?.pathToAnnotate as string
      ).split(".");

      if (this.isPathPrefix(basePathParts, keyParts)) {
        const remainingParts = keyParts.slice(basePathParts.length);
        const remainingPath = remainingParts.join(".");

        const relations = [];
        let currentPath = "";

        for (const part of remainingParts.slice(0, -1)) {
          currentPath = currentPath ? `${currentPath}.${part}` : part;
          relations.push(currentPath);
        }

        try {
          // Use annotator client to verify if the remaining path is valid for this entity type
          const fieldInfo = await this.annotatorGrpcClient.getEntityFieldInfo(
            {
              entityType: triggerEvent.metadata.entityType as EntityType,
              relations,
              path: remainingPath,
            },
            {
              user_id: this.userId,
              org_id: this.organizationId,
            },
          );

          if (
            fieldInfo.exists &&
            (!expectedType || fieldInfo.data.type === expectedType)
          ) {
            return { isValid: true, relations };
          }
        } catch (error) {
          this.logger.error(
            `Error validating key ${key} in event ${triggerEvent.uid}: ${error}`,
          );
        }
      }
    }

    return { isValid: false };
  }

  /**
   * Helper method to check if one path is a prefix of another
   * e.g., ["message", "payload", "ticket"] is a prefix of ["message", "payload", "ticket", "account", "classification"]
   */
  private isPathPrefix(prefix: string[], fullPath: string[]): boolean {
    if (prefix.length > fullPath.length) {
      return false;
    }

    return prefix.every((part, index) => part === fullPath[index]);
  }

  private async validateEventFilters(
    filters: EventFilters,
    activitiesMap: Map<string, WorkflowActivity>,
    triggerEvent: WorkflowEvent,
    stepsByIdentifier: Map<number, WorkflowStepDto>,
    annotations?: WorkflowAnnotation[],
  ): Promise<WorkflowAnnotation[]> {
    let updatedAnnotations = annotations || [];

    // Handle logical operators
    if ("~or" in filters) {
      const orConditions = filters["~or"] as EventFilters[];

      for (const condition of orConditions) {
        const result = await this.validateEventFilters(
          condition,
          activitiesMap,
          triggerEvent,
          stepsByIdentifier,
          updatedAnnotations,
        );

        if (result) {
          for (const annotation of result) {
            updatedAnnotations = this.addDefaultAnnotationToWorkflow(
              triggerEvent,
              annotation.relations,
              updatedAnnotations,
            );
          }
        }
      }

      return updatedAnnotations;
    }

    if ("~and" in filters) {
      const andConditions = filters["~and"] as EventFilters[];

      for (const condition of andConditions) {
        const result = await this.validateEventFilters(
          condition,
          activitiesMap,
          triggerEvent,
          stepsByIdentifier,
          updatedAnnotations,
        );

        if (result) {
          for (const annotation of result) {
            updatedAnnotations = this.addDefaultAnnotationToWorkflow(
              triggerEvent,
              annotation.relations,
              updatedAnnotations,
            );
          }
        }
      }

      return updatedAnnotations;
    }

    // Handle direct key-value filters
    for (const [key, _value] of Object.entries(filters)) {
      // Check for event context references
      if (key.includes("context.event.")) {
        const eventMatches = [
          ...key.matchAll(/context\.event\.(.+?)(?:}}|$)/g),
        ];
        for (const match of eventMatches) {
          const eventKey = match[1].trim();

          // Verify key exists in event schema
          const validationResult = await this.validateLiquidTagInEventSchema(
            eventKey,
            triggerEvent,
          );

          if (!validationResult.isValid) {
            throw new BadRequestException(
              `Invalid filters: Event ${triggerEvent.uid} does not contain key: ${eventKey} in its schema`,
            );
          }

          // Add relations to annotations if validation was successful with default annotation
          if (validationResult.relations) {
            updatedAnnotations = this.addDefaultAnnotationToWorkflow(
              triggerEvent,
              validationResult.relations,
              updatedAnnotations,
            );
          }
        }
      }

      // Check for previous step output references
      if (key.includes("context.outputs.")) {
        const outputMatches = [
          ...key.matchAll(/context\.outputs\.(.+?)\.(.+?)(?:}}|$)/g),
        ];
        for (const match of outputMatches) {
          const [, previousStepIdStr, outputKey] = match;
          const previousStepId = Number(previousStepIdStr);
          // Verify it's a valid number
          if (isNaN(previousStepId)) {
            throw new BadRequestException(
              `Invalid filters: ${previousStepIdStr} is not a valid step identifier.`,
            );
          }

          // Verify previous step exists
          const previousStep = stepsByIdentifier.get(previousStepId);
          if (!previousStep) {
            throw new BadRequestException(
              `Invalid filters: Previous step ${previousStepId} not found`,
            );
          }

          // Get the activity for the previous step
          const previousActivity = activitiesMap.get(
            previousStep.activity.uniqueIdentifier,
          );
          if (!previousActivity) {
            throw new BadRequestException(
              `Invalid filters: Activity ${previousStep.activity} not found for step ${previousStepId}`,
            );
          }

          // Verify output key exists in previous activity's response schema
          if (
            !this.validateKeyInSchema(
              outputKey,
              previousActivity.responseSchema,
            )
          ) {
            throw new BadRequestException(
              `Invalid filters: Activity ${previousActivity.uid} does not output key: ${outputKey} as per its response schema`,
            );
          }
        }
      }
    }

    return updatedAnnotations;
  }

  private async validateWorkflowStepInput({
    currentActivity,
    currentActivityInput,
    fanOutFilters,
    activities,
    triggerEvent,
    dependencies,
    currentStepId,
    stepsByIdentifier,
    annotations,
  }: ValidateWorkflowStepInputOptions): Promise<WorkflowAnnotation[]> {
    // Validate that all required fields from the activity's request schema are present in the input
    if (
      currentActivity.requestSchema &&
      typeof currentActivity.requestSchema === "object"
    ) {
      const schema = currentActivity.requestSchema as any;
      const requiredFields = schema.required || [];

      for (const requiredField of requiredFields) {
        if (!(requiredField in currentActivityInput)) {
          throw new BadRequestException(
            `Invalid step ${currentStepId}: Required field '${requiredField}' is missing from input for activity ${currentActivity.uid}`,
          );
        }
      }
    }

    for (const [key, value] of Object.entries(currentActivityInput)) {
      // Verify current activity can accept this input
      if (!this.validateKeyInSchema(key, currentActivity.requestSchema)) {
        throw new BadRequestException(
          `Activity ${currentActivity.uid} cannot accept input: ${key} as per its request schema`,
        );
      }

      // Get expected type for this key from activity's request schema
      const expectedType = this.getSchemaTypeForKey(
        key,
        currentActivity.requestSchema,
      );
      if (!expectedType) {
        throw new BadRequestException(
          `Invalid step ${currentStepId}: Could not determine expected type for key ${key} in activity ${currentActivity.uid}`,
        );
      }

      if (typeof value !== "string") {
        // Validate non-string value type matches schema
        if (!this.validateValueType(value, expectedType)) {
          throw new BadRequestException(
            `Invalid step ${currentStepId}: Invalid type for key ${key}. Expected ${expectedType} but got ${typeof value}`,
          );
        }
        continue;
      }

      // Check for event context references
      if (value.includes("context.event.")) {
        const eventMatches = [
          ...value.matchAll(/context\.event\.(.+?)(?:}}|$)/g),
        ];
        for (const match of eventMatches) {
          const eventKey = match[1].trim();
          if (!eventKey) {
            throw new BadRequestException(
              `Invalid step ${currentStepId}: Invalid event reference: ${value}`,
            );
          }

          // Verify key exists in event schema
          const validationResult = await this.validateLiquidTagInEventSchema(
            eventKey,
            triggerEvent,
            expectedType,
          );

          if (!validationResult.isValid) {
            throw new BadRequestException(
              `Invalid step ${currentStepId}: Event ${triggerEvent.uid} does not contain key: ${eventKey} in its schema`,
            );
          }

          if (validationResult.relations) {
            annotations = this.addDefaultAnnotationToWorkflow(
              triggerEvent,
              validationResult.relations,
              annotations,
            );
          }
        }
      }

      // Check for previous step output references
      if (value.includes("context.outputs.")) {
        const outputMatches = [
          ...value.matchAll(/context\.outputs\.(.+?)\.(.+?)(?:}}|$)/g),
        ];
        for (const match of outputMatches) {
          const [, previousStepIdStr, outputKey] = match;
          const previousStepId = Number(previousStepIdStr);
          // Verify it's a valid number
          if (isNaN(previousStepId)) {
            throw new BadRequestException(
              `Invalid step ${currentStepId}: ${previousStepIdStr} is not a valid dependency.`,
            );
          }

          // Verify previous activity is a dependency
          if (!dependencies.includes(previousStepId)) {
            throw new BadRequestException(
              `Invalid step ${currentStepId}: Activity ${previousStepId} must be listed as a dependency to use its output`,
            );
          }

          // Verify previous step exists
          const previousStep = stepsByIdentifier.get(previousStepId);
          if (!previousStep) {
            throw new BadRequestException(
              `Invalid step ${currentStepId}: Previous step ${previousStepId} not found`,
            );
          }

          // Get the activity for the previous step
          const previousActivity = activities.get(
            previousStep.activity.uniqueIdentifier,
          );
          if (!previousActivity) {
            throw new BadRequestException(
              `Invalid step ${currentStepId}: Activity ${previousStep.activity} not found for step ${previousStepId}`,
            );
          }

          // Verify output key exists in previous activity's response schema
          if (
            !this.validateKeyInSchema(
              outputKey,
              previousActivity.responseSchema,
            )
          ) {
            throw new BadRequestException(
              `Invalid step ${currentStepId}: Activity ${previousActivity.uid} does not output key: ${outputKey} as per its response schema`,
            );
          }

          // Verify previous activity output type matches current activity input type
          const prevActivityOutputType = this.getSchemaTypeForKey(
            outputKey,
            previousActivity.responseSchema,
          );
          if (prevActivityOutputType !== expectedType) {
            throw new BadRequestException(
              `Invalid step ${currentStepId}: Type mismatch: Activity ${previousActivity.uid} outputs ${prevActivityOutputType} for key ${outputKey}, but activity ${currentActivity.uid} expects ${expectedType} for key ${key}`,
            );
          }
        }
      }

      // Static value - only if no liquid template references were found
      if (
        !value.includes("context.event.") &&
        !value.includes("context.outputs.")
      ) {
        // For static strings, verify the activity expects a string
        if (expectedType !== "string") {
          throw new BadRequestException(
            `Type mismatch: Static string value provided for key ${key}, but activity ${currentActivity.uid} expects ${expectedType}`,
          );
        }
      }
    }

    // Validate fanOutFilters
    if (Object.keys(fanOutFilters).length > 0) {
      annotations = await this.validateEventFilters(
        fanOutFilters,
        activities,
        triggerEvent,
        stepsByIdentifier,
        annotations,
      );
    }

    return annotations;
  }

  private checkCyclicDependenciesInWorkflowSteps(
    workflowDefinition: WorkflowStepDto[],
  ) {
    // 1. First, create a map of each step and its dependencies
    const dependencyGraph = new Map<number, Set<number>>();

    for (const step of workflowDefinition) {
      dependencyGraph.set(
        step.stepIdentifier,
        new Set(step.dependencies || []),
      );
    }

    // 2. DFS helper function to detect cycles
    const detectCycle = (
      stepIdentifier: number, // Current step we're checking
      visited: Set<number>, // All steps we've seen
      recursionStack: Set<number>, // steps in current path
    ): boolean => {
      visited.add(stepIdentifier);
      recursionStack.add(stepIdentifier);

      // Get all dependencies of current step
      const dependencies = dependencyGraph.get(stepIdentifier) || new Set();

      for (const dependency of dependencies) {
        // If dependency doesn't exist in workflow at all
        if (!dependencyGraph.has(dependency)) {
          throw new BadRequestException(
            `Invalid step ${stepIdentifier}: Dependency ${dependency} referenced in step ${stepIdentifier} not found in workflow`,
          );
        }

        if (!visited.has(dependency)) {
          // If we haven't visited this dependency, check it
          if (detectCycle(dependency, visited, recursionStack)) {
            throw new BadRequestException(
              `Invalid step ${stepIdentifier}: Cyclic dependency detected: step: #${stepIdentifier} -> step: #${dependency}`,
            );
          }
        }
        // If we find a dependency that's already in our current path, it's a cycle
        else if (recursionStack.has(dependency)) {
          throw new BadRequestException(
            `Invalid step ${stepIdentifier}: Cyclic dependency detected: step: #${stepIdentifier} -> step: #${dependency}`,
          );
        }
      }

      // Remove from recursion stack as we're done with this path
      recursionStack.delete(stepIdentifier);
      return false;
    };

    // 3. Start checking from each step
    const visited = new Set<number>();
    for (const stepIdentifier of dependencyGraph.keys()) {
      if (!visited.has(stepIdentifier)) {
        detectCycle(stepIdentifier, visited, new Set());
      }
    }
  }

  private addDefaultAnnotationToWorkflow(
    triggerEvent: WorkflowEvent,
    relations: string[],
    existingAnnotations: WorkflowAnnotation[] = [],
  ): WorkflowAnnotation[] {
    if (
      !triggerEvent.metadata?.pathToAnnotate ||
      !triggerEvent.metadata?.entityType
    ) {
      return existingAnnotations;
    }

    // Check if we already have an annotation for this path
    const existingAnnotation = existingAnnotations.find(
      (a) => a.pathToAnnotate === triggerEvent.metadata.pathToAnnotate,
    );

    if (existingAnnotation) {
      // Merge relations if annotation exists
      existingAnnotation.relations = [
        ...new Set([...existingAnnotation.relations, ...relations]),
      ];
      return existingAnnotations;
    }

    // Create new annotation if none exists
    const newAnnotation: WorkflowAnnotation = {
      pathToAnnotate: triggerEvent.metadata.pathToAnnotate as string,
      entityType: triggerEvent.metadata.entityType as EntityType,
      relations,
    };

    return [...existingAnnotations, newAnnotation];
  }
}
