import { ApiProperty } from "@nestjs/swagger";

import {
  FilterOperator,
  LogicalOperator,
  WorkflowActivity,
  WorkflowActivitySourceEnum,
} from "@repo/thena-platform-entities";
import { IsOptional, IsString } from "class-validator";

export class ActivityResponseDto {
  @ApiProperty({ description: "The unique identifier of the activity" })
  uid: string;

  @ApiProperty({ description: "The description of the activity" })
  description: string;

  @ApiProperty({ description: "The name of the activity" })
  name: string;

  @ApiProperty({ description: "The unique identifier of the activity" })
  uniqueIdentifier: string;

  @ApiProperty({ description: "The version of the activity" })
  version: number;

  @ApiProperty({
    description: "The source of the activity",
    enum: ["platform_app", "registered_app"],
    enumName: "ActivitySourceEnum",
  })
  source: WorkflowActivitySourceEnum;

  @ApiProperty({
    description: "The request schema of the activity",
    type: Object,
  })
  requestSchema: Record<string, unknown>;

  @ApiProperty({
    description: "The response schema of the activity",
    type: Object,
  })
  responseSchema: Record<string, unknown>;

  @ApiProperty({
    description: "The metadata of the activity",
    type: Object,
  })
  metadata: Record<string, unknown>;

  @ApiProperty({
    description: "The compensation activity of the activity",
    // eslint-disable-next-line no-use-before-define
    type: ActivityResponseDto,
  })
  // eslint-disable-next-line no-use-before-define
  compensationActivity: ActivityResponseDto;

  static fromEntity(entity: WorkflowActivity): ActivityResponseDto {
    const dto = new ActivityResponseDto();

    dto.uid = entity.uid;
    dto.description = entity.description;
    dto.name = entity.name;
    dto.uniqueIdentifier = entity.uniqueIdentifier;
    dto.version = entity.version;
    dto.source = entity.source;
    dto.requestSchema = entity.requestSchema;
    dto.responseSchema = entity.responseSchema;
    dto.metadata = entity.metadata;
    dto.compensationActivity = entity.compensationActivity
      ? ActivityResponseDto.fromEntity(entity.compensationActivity)
      : undefined;
    return dto;
  }
}

export class GetActivityRegistryResponse {
  @ApiProperty({
    description: "The results of the activity registry",
    type: [ActivityResponseDto],
  })
  results: ActivityResponseDto[];
}

export class GetAvailableWorkflowFiltersResponse {
  @ApiProperty({ description: "The filter operators" })
  filter_operators: FilterOperator[];

  @ApiProperty({ description: "The logical operators" })
  logical_operators: LogicalOperator[];
}

export class GetRegistryDto {
  @ApiProperty({ description: "The team ID" })
  @IsString()
  @IsOptional()
  teamId?: string;
}
