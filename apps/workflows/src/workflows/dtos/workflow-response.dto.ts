import { ApiProperty } from "@nestjs/swagger";
import {
  EventFilters,
  Workflow,
  WorkflowActivity,
  WorkflowAnnotation,
  WorkflowInstance,
  WorkflowInstanceActivity,
  WorkflowInstanceActivityStatusEnum,
  WorkflowInstanceStatusEnum,
  WorkflowStep,
  WorkflowSubType,
  WorkflowType,
} from "@repo/thena-platform-entities";
import { EventResponseDto } from "./event.dto";

export class WorkflowStepActivityResponseDto {
  @ApiProperty({ description: "The name of the activity" })
  name: string;

  @ApiProperty({ description: "The unique identifier of the activity" })
  uniqueIdentifier: string;

  @ApiProperty({ description: "The version of the activity" })
  version: number;

  @ApiProperty({
    description: "The auto upgrade to latest version of the activity",
  })
  autoUpgradeToLatestVersion: boolean;

  static fromEntity(
    entity: WorkflowActivity,
    activityStep: WorkflowStep["activity"],
  ): WorkflowStepActivityResponseDto {
    const dto = new WorkflowStepActivityResponseDto();
    if (!entity) {
      return dto;
    }

    dto.name = entity.name;
    dto.uniqueIdentifier = entity.uniqueIdentifier;
    dto.version = entity.version;
    dto.autoUpgradeToLatestVersion = activityStep.autoUpgradeToLatestVersion;

    return dto;
  }
}

export class WorkflowStepResponseDto {
  @ApiProperty({ description: "The step identifier of the workflow step" })
  stepIdentifier: number;

  @ApiProperty({ description: "The activity of the workflow step" })
  activity: WorkflowStepActivityResponseDto;

  @ApiProperty({ description: "The input to the activity" })
  input: Record<string, unknown>;

  @ApiProperty({
    description: "The retry policy of the activity",
    type: {
      maximumAttempts: { type: "number" },
      initialInterval: { type: "number" },
      backoffCoefficient: { type: "number" },
    },
  })
  retryPolicy?: {
    maximumAttempts: number;
    initialInterval: number;
    backoffCoefficient: number;
  };

  @ApiProperty({ description: "The action to take on failure of the activity" })
  onFailure: "CONTINUE" | "ABORT" | "COMPENSATE";

  @ApiProperty({ description: "Whether the activity is a sleep activity" })
  isSleepActivity: boolean;

  @ApiProperty({ description: "The execution timeout of the activity" })
  executionTimeout: number;

  @ApiProperty({
    description: "The approver of the activity",
    type: {
      type: { type: "string", enum: ["TEAM", "USER"] },
      uid: { type: "string" },
      timeout: { type: "number" },
    },
  })
  approver?: {
    type: "TEAM" | "USER";
    uid: string;
    timeout: number;
  };

  @ApiProperty({ description: "The dependencies of the activity" })
  dependencies: number[];

  @ApiProperty({ description: "The filters of the activity" })
  filters?: EventFilters;

  @ApiProperty({ description: "The compensation activity of the activity" })
  compensationActivity?: {
    activity: WorkflowStepActivityResponseDto;
    input: Record<string, unknown>;
  };

  static fromEntity(step: WorkflowStep, activities: WorkflowActivity[]) {
    const dto = new WorkflowStepResponseDto();

    const activity = activities.find(
      (activity) =>
        activity.uniqueIdentifier === step.activity.uniqueIdentifier,
    );

    if (step.compensationActivity && step.compensationActivity.activity) {
      const compensationActivity = activities.find(
        (activity) =>
          activity.uniqueIdentifier ===
          step.compensationActivity.activity.uniqueIdentifier,
      );

      dto.compensationActivity = {
        activity: WorkflowStepActivityResponseDto.fromEntity(
          compensationActivity,
          step.compensationActivity.activity,
        ),
        input: step.compensationActivity.input,
      };
    }

    dto.stepIdentifier = step.stepIdentifier;
    dto.activity = WorkflowStepActivityResponseDto.fromEntity(
      activity,
      step.activity,
    );
    dto.input = step.input;
    dto.retryPolicy = step.retryPolicy;
    dto.onFailure = step.onFailure;
    dto.isSleepActivity = step.isSleepActivity;
    dto.executionTimeout = step.executionTimeout;
    dto.approver = step.approver;
    dto.dependencies = step.dependencies;
    dto.filters = step.filters;

    return dto;
  }
}

export class WorkflowResponseDto {
  @ApiProperty({
    description: "The identifier for current version of the workflow",
  })
  uid: string;

  @ApiProperty({ description: "The type of the activity" })
  type: WorkflowType;

  @ApiProperty({ description: "The sub type of the activity" })
  subType: WorkflowSubType;

  @ApiProperty({ description: "The unique identifier of the workflow" })
  uniqueIdentifier: string;

  @ApiProperty({ description: "The name identifier of the workflow" })
  name: string;

  @ApiProperty({ description: "The version of the workflow", type: Number })
  version: number;

  @ApiProperty({
    description: "The trigger event of the workflow",
    type: EventResponseDto,
  })
  triggerEvent: EventResponseDto;

  @ApiProperty({ description: "The filters of the workflow", type: Object })
  filters: EventFilters;

  @ApiProperty({
    description: "The annotation for the workflow",
    type: Array<{
      entityType: string;
      data: Record<string, unknown>;
      relations: string[];
    }>,
  })
  annotations?: WorkflowAnnotation[];

  @ApiProperty({
    description: "The workflow definition of the workflow",
    type: [WorkflowStepResponseDto],
  })
  workflowDefinition: WorkflowStepResponseDto[];

  @ApiProperty({ description: "The executing agent of the workflow" })
  executingAgent: string;

  @ApiProperty({ description: "The status of the workflow" })
  isActive: boolean;

  @ApiProperty({ description: "The created at date of the workflow" })
  createdAt: Date;

  @ApiProperty({ description: "The updated at date of the workflow" })
  updatedAt: Date;

  @ApiProperty({ description: "The created by of the workflow" })
  createdBy: string;

  @ApiProperty({ description: "The team id of the workflow" })
  teamId: string;

  @ApiProperty({ description: "The metadata for the workflow" })
  metadata: Record<string, unknown>;

  // Here trigger event needs to be preloaded
  static fromEntity(
    entity: Workflow,
    activities: WorkflowActivity[],
  ): WorkflowResponseDto {
    const dto = new WorkflowResponseDto();

    dto.uid = entity.uid;
    dto.type = entity.type;
    dto.subType = entity.subType;
    dto.uniqueIdentifier = entity.uniqueIdentifier;
    dto.name = entity.name;
    dto.version = entity.version;
    dto.triggerEvent = EventResponseDto.fromEntity(entity.triggerEvent);
    dto.annotations = entity.annotations;
    dto.filters = entity.filters;
    dto.workflowDefinition = entity.workflowDefinition.map((step) =>
      WorkflowStepResponseDto.fromEntity(step, activities),
    );
    dto.executingAgent = entity.executingAgent;
    dto.isActive = entity.isActive;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    dto.createdBy = entity.createdBy;
    dto.metadata = entity.metadata;
    dto.teamId = entity.teamId;

    return dto;
  }
}

export class GetWorkflowsResponse {
  @ApiProperty({
    description: "The results of the workflows",
    type: [WorkflowResponseDto],
  })
  results: WorkflowResponseDto[];

  @ApiProperty({ description: "The total number of workflows" })
  total: number;
}

export class DeleteWorkflowResponse {
  @ApiProperty({ description: "The success of the workflow deletion" })
  success: boolean;
}

export class GetWorkflowExecutionsResponse {
  @ApiProperty({ description: "The identifier of the workflow instance" })
  id: string;

  @ApiProperty({ description: "The identifier of the workflow" })
  workflowId: string;

  @ApiProperty({ description: "The version of the workflow" })
  version: number;

  @ApiProperty({ description: "The status of the workflow instance" })
  status: WorkflowInstanceStatusEnum;

  @ApiProperty({ description: "The context of the workflow instance" })
  context: Record<string, unknown>;

  @ApiProperty({
    description: "The start date of the workflow instance (in ISO format)",
  })
  startedAt: Date;

  @ApiProperty({
    description:
      "The last updated date of the workflow instance (in ISO format)",
  })
  lastUpdatedAt: Date;

  static fromEntity(entity: WorkflowInstance): GetWorkflowExecutionsResponse {
    const dto = new GetWorkflowExecutionsResponse();

    dto.id = entity.uid;
    dto.workflowId = entity.workflow.uid;
    dto.version = entity.workflow.version;
    dto.status = entity.status;
    dto.context = entity.context;
    dto.startedAt = entity.createdAt;
    dto.lastUpdatedAt = entity.updatedAt;

    return dto;
  }
}

export class GetWorkflowExecutionTaskResponseDto {
  @ApiProperty({
    description: "The identifier of the activity of a workflow instance",
  })
  id: string;

  @ApiProperty({
    description:
      "The step identifier of the activity of a workflow instance as present in workflow definition",
  })
  stepIdentifier: number;

  @ApiProperty({
    description: "The activity of the workflow instance",
  })
  activity: string;

  @ApiProperty({
    description: "The status of the activity of a workflow instance",
    enum: WorkflowInstanceActivityStatusEnum,
  })
  status: WorkflowInstanceActivityStatusEnum;

  @ApiProperty({
    description: "The input of the activity of a workflow instance",
  })
  input: Record<string, unknown>;

  @ApiProperty({
    description: "The result of the activity of a workflow instance",
  })
  result: Record<string, unknown>;

  @ApiProperty({ description: "If the activity is a retry" })
  isRetry: boolean;

  @ApiProperty({ description: "If the activity is a compensation" })
  isCompensation: boolean;

  @ApiProperty({
    description: "The execution time of the activity (in seconds)",
  })
  executionTime: number;

  @ApiProperty({
    description: "The start date of the activity (in ISO format)",
  })
  startedAt: Date;

  @ApiProperty({
    description: "The last updated date of the activity (in ISO format)",
  })
  lastUpdatedAt: Date;

  static fromEntity(
    entity: WorkflowInstanceActivity,
  ): GetWorkflowExecutionTaskResponseDto {
    const dto = new GetWorkflowExecutionTaskResponseDto();

    dto.id = entity.uid;
    dto.stepIdentifier = entity.workflowStepIdentifier;
    dto.activity = entity.activity.uid;
    dto.status = entity.status;
    dto.input = entity.input;
    dto.result = entity.result;
    dto.isRetry = entity.isRetry;
    dto.isCompensation = entity.isCompensation;
    dto.executionTime = parseInt(entity.executionTime) || 0;
    dto.startedAt = entity.createdAt;
    dto.lastUpdatedAt = entity.updatedAt;

    return dto;
  }
}

export class TriggerActivityResponseDto {
  @ApiProperty({ description: "The success of the activity trigger" })
  success: boolean;

  @ApiProperty({ description: "The message of the activity trigger" })
  message: string;

  @ApiProperty({
    description: "The response data of the activity trigger",
  })
  data: Record<string, unknown>;
}
