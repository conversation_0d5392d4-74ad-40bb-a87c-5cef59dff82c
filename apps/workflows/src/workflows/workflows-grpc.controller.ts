import { <PERSON>ada<PERSON> } from "@grpc/grpc-js";
import { Controller, Inject, UseGuards } from "@nestjs/common";
import { GrpcMethod, RpcException } from "@nestjs/microservices";
import { SentryService } from "@repo/nestjs-commons/filters/sentry-alerts.filter";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import { ILogger } from "@repo/nestjs-commons/logger";
import { extractUserMetadata } from "@repo/nestjs-commons/utils";
import { workflows } from "@repo/shared-proto";
import { ActivitySignature, EventSignature } from "@repo/workflow-engine";
import { isEmpty } from "lodash";
import { RegistrySyncService } from "./core/registry-sync/registry-sync.service";

@UseGuards(GrpcAuthGuard)
@Controller("v1/workflows")
export class WorkflowsGrpcController {
  constructor(
    private readonly registrySyncService: RegistrySyncService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "registerActivities")
  async registerActivities(data: workflows.RegisterActivitiesRequest) {
    if (
      data.source !== workflows.Source.PLATFORM_APP &&
      isEmpty(data.organizationId)
    ) {
      throw new RpcException(new Error("Organization ID is required"));
    }

    try {
      const transactionId = await this.registrySyncService.registerActivities(
        data.source,
        data.activities as ActivitySignature[],
        data.organizationId,
      );

      return {
        success: true,
        transactionId,
      };
    } catch (error) {
      this.logger.error(
        `Error encountered while registering activities. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "registerActivities",
        organizationId: data.organizationId,
        source: data.source,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "registerEvents")
  async registerEvents(data: workflows.RegisterEventsRequest) {
    if (
      data.source !== workflows.Source.PLATFORM_APP &&
      isEmpty(data.organizationId)
    ) {
      throw new RpcException(new Error("Organization ID is required"));
    }

    try {
      const transactionId = await this.registrySyncService.registerEvents(
        data.source,
        data.events as EventSignature[],
        data.organizationId,
      );

      return {
        success: true,
        transactionId,
      };
    } catch (error) {
      this.logger.error(
        `Error encountered while registering events. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "registerEvents",
        organizationId: data.organizationId,
        source: data.source,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "getActivities")
  async getActivities(data: workflows.GetActivitiesRequest) {
    if (
      data.source !== workflows.Source.PLATFORM_APP &&
      isEmpty(data.organizationId)
    ) {
      throw new RpcException(new Error("Organization ID is required"));
    }

    try {
      const activities = await this.registrySyncService.getActivities(
        data.source,
        data.organizationId,
      );

      return { activities };
    } catch (error) {
      this.logger.error(
        `Error encountered while fetching activities. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "getActivities",
        organizationId: data.organizationId,
        source: data.source,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "getEvents")
  async getEvents(data: workflows.GetEventsRequest) {
    if (
      data.source !== workflows.Source.PLATFORM_APP &&
      isEmpty(data.organizationId)
    ) {
      throw new RpcException(new Error("Organization ID is required"));
    }

    try {
      const events = await this.registrySyncService.getEvents(
        data.source,
        data.organizationId,
      );

      return { events };
    } catch (error) {
      this.logger.error(
        `Error encountered while fetching events. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "getEvents",
        organizationId: data.organizationId,
        source: data.source,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "deleteActivities")
  async deleteActivities(data: workflows.DeleteActivitiesRequest) {
    if (isEmpty(data.identifier) || isEmpty(data.organizationId)) {
      throw new RpcException(
        new Error("Identifier and Organization ID are required"),
      );
    }

    try {
      const transactionId = await this.registrySyncService.deleteActivities(
        data.identifier,
        data.organizationId,
      );

      return {
        success: true,
        transactionId,
      };
    } catch (error) {
      this.logger.error(
        `Error encountered while deleting activity. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "deleteActivity",
        identifier: data.identifier,
        organizationId: data.organizationId,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "deleteEvents")
  async deleteEvents(data: workflows.DeleteEventsRequest) {
    if (isEmpty(data.eventType) || isEmpty(data.organizationId)) {
      throw new RpcException(
        new Error("Event Type and Organization ID are required"),
      );
    }

    try {
      const transactionId = await this.registrySyncService.deleteEvents(
        data.eventType,
        data.organizationId,
      );

      return {
        success: true,
        transactionId,
      };
    } catch (error) {
      this.logger.error(
        `Error encountered while deleting event. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "deleteEvent",
        eventType: data.eventType,
        organizationId: data.organizationId,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "compensateRegisterActivities")
  async compensateRegisterActivities(
    data: workflows.CompensateRegisterActivitiesRequest,
  ) {
    if (isEmpty(data.transactionId)) {
      throw new RpcException(new Error("Transaction ID is required"));
    }

    try {
      await this.registrySyncService.compensateRegisterActivities(
        data.transactionId,
      );
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error encountered while compensating register activities. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "compensateRegisterActivities",
        transactionId: data.transactionId,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "compensateRegisterEvents")
  async compensateRegisterEvents(
    data: workflows.CompensateRegisterEventsRequest,
  ) {
    if (isEmpty(data.transactionId)) {
      throw new RpcException(new Error("Transaction ID is required"));
    }

    try {
      await this.registrySyncService.compensateRegisterEvents(
        data.transactionId,
      );
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error encountered while compensating register events. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "compensateRegisterEvents",
        transactionId: data.transactionId,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "compensateDeleteActivities")
  async compensateDeleteActivities(
    data: workflows.CompensateDeleteActivitiesRequest,
  ) {
    if (isEmpty(data.transactionId)) {
      throw new RpcException(new Error("Transaction ID is required"));
    }

    try {
      await this.registrySyncService.compensateDeleteActivities(
        data.transactionId,
      );
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error encountered while compensating delete activity. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "compensateDeleteActivities",
        transactionId: data.transactionId,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(workflows.WORKFLOWS_SERVICE_NAME, "compensateDeleteEvents")
  async compensateDeleteEvents(data: workflows.CompensateDeleteEventsRequest) {
    if (isEmpty(data.transactionId)) {
      throw new RpcException(new Error("Transaction ID is required"));
    }

    try {
      await this.registrySyncService.compensateDeleteEvents(data.transactionId);
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error encountered while compensating delete event. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "compensateDeleteEvents",
        transactionId: data.transactionId,
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(
    workflows.WORKFLOWS_SERVICE_NAME,
    "updateAccessibleTeamsForActivities",
  )
  async updateAccessibleTeamsForActivities(
    data: workflows.UpdateAccessibleTeamsForActivitiesRequest,
    metadata: Metadata,
  ) {
    const { orgUid } = extractUserMetadata(metadata);

    if (isEmpty(data.activityIdentifiers)) {
      throw new RpcException(new Error("Activity identifiers are required"));
    }

    try {
      await this.registrySyncService.updateAccessibleTeamsForActivities(
        data.activityIdentifiers,
        orgUid,
        data.teamIds ?? [],
      );
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error encountered while updating accessible teams for activities. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "updateAccessibleTeamsForActivities",
        body: JSON.stringify(data),
      });

      throw new RpcException(error);
    }
  }

  @GrpcMethod(
    workflows.WORKFLOWS_SERVICE_NAME,
    "updateAccessibleTeamsForEvents",
  )
  async updateAccessibleTeamsForEvents(
    data: workflows.UpdateAccessibleTeamsForEventsRequest,
    metadata: Metadata,
  ) {
    const { orgUid } = extractUserMetadata(metadata);

    if (isEmpty(data.eventTypes)) {
      throw new RpcException(new Error("Event types are required"));
    }

    try {
      await this.registrySyncService.updateAccessibleTeamsForEvents(
        data.eventTypes,
        orgUid,
        data.teamIds,
      );
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error encountered while updating accessible teams for events. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "WORKFLOW_GRPC_CONTROLLER",
        fn: "updateAccessibleTeamsForEvents",
        body: JSON.stringify(data),
      });

      throw new RpcException(error);
    }
  }
}
