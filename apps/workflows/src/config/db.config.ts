import { TypeOrmModuleOptions } from "@nestjs/typeorm";
import { getCertificateFromValut } from "@repo/nestjs-commons/utils";
import {
  Workflow,
  WorkflowActivity,
  WorkflowEngine,
  WorkflowEvent,
  WorkflowEventLog,
  WorkflowInstance,
  WorkflowInstanceActivity,
} from "@repo/thena-platform-entities";
import { ConfigKeys, ConfigService } from "./config.service";

const getConnectionUrl = (configService: ConfigService) => {
  const dbConfig = {
    host: configService.get(ConfigKeys.THENA_WORKFLOWS_DB_HOST),
    port: configService.get(ConfigKeys.THENA_WORKFLOWS_DB_PORT),
    database: configService.get(ConfigKeys.THENA_WORKFLOWS_DB_NAME),
    username: configService.get(ConfigKeys.THENA_WORKFLOWS_DB_USER),
    password: configService.get(ConfigKeys.THENA_WORKFLOWS_DB_PASSWORD),
  };
  return `postgresql://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
};

async function getSSLConfig(
  configService: ConfigService,
  isDevelopment: boolean,
) {
  if (isDevelopment) {
    return false;
  }

  return await getCertificateFromValut({
    url: configService.get(ConfigKeys.VAULT_URL),
    token: configService.get(ConfigKeys.VAULT_TOKEN),
    certPath: configService.get(ConfigKeys.CERT_PATH),
  });
}

export const getWorkflowsDBConfig = async (
  configService: ConfigService,
): Promise<TypeOrmModuleOptions> => {
  const environment = configService.get(ConfigKeys.NODE_ENV);
  const isDevelopment = environment === "development" || environment === "test";
  const ssl = await getSSLConfig(configService, isDevelopment);

  return {
    type: "postgres",
    url: getConnectionUrl(configService),
    entities: [
      Workflow,
      WorkflowActivity,
      WorkflowEngine,
      WorkflowEvent,
      WorkflowEventLog,
      WorkflowInstance,
      WorkflowInstanceActivity,
    ],
    ssl,
    synchronize: isDevelopment ? true : false,
    logging: false,
    extra: {
      max: 20,
      connectionTimeoutMillis: 60000,
      idleTimeoutMillis: 60000,
    },
  };
};
