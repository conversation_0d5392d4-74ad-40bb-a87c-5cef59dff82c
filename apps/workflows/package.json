{"name": "@thena-backend/workflows", "description": "", "version": "1.0.0", "dependencies": {"@fastify/static": "7.0.4", "@grpc/grpc-js": "^1.12.2", "@grpc/reflection": "^1.0.4", "@nestjs/common": "^10.4.4", "@nestjs/core": "^10.4.4", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.7", "@nestjs/platform-fastify": "^10.4.5", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^11.0.0", "@repo/nestjs-commons": "workspace:*", "@repo/shared-proto": "workspace:*", "@repo/thena-platform-entities": "workspace:*", "@repo/workflow-engine": "workspace:*", "@repo/nestjs-newrelic": "workspace:*", "@temporalio/activity": "^1.11.2", "@temporalio/worker": "^1.11.2", "@temporalio/workflow": "^1.11.2", "@types/handlebars": "^4.1.0", "ajv": "^8.17.1", "@types/js-yaml": "^4.0.9", "axios": "^1.7.8", "cls-rtracer": "^2.6.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "fastify": "^5.0.0", "handlebars": "^4.7.8", "ioredis": "^5.4.1", "joi": "^17.13.3", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "newrelic": "^12.18.2", "pino": "^9.5.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "tldts": "^6.1.55", "uuid": "^10.0.0"}, "devDependencies": {"@nestjs/axios": "^3.0.3", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.1.3", "@nestjs/testing": "^10.4.4", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/cors": "^2.8.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.9", "@types/node": "^22.9.0", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "istanbul-badges-readme": "^1.9.0", "jest": "^29.5.0", "pg": "^8.13.0", "pino-pretty": "^11.3.0", "prettier": "^3.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.20", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "maxWorkers": 1, "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "globalSetup": "../global-setup.ts", "globalTeardown": "../global-teardown.ts", "coverageDirectory": "../coverage", "testEnvironment": "node"}, "main": "index.js", "scripts": {"build": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "clean:modules": "rimraf node_modules", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "start": "nest start", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch | pino-pretty --singleLine --colorize", "start:dev:test": "export NODE_ENV=test && nest start --watch | pino-pretty --singleLine --colorize", "start:prod": "node dist/main", "test": "jest --runInBand --verbose --detectOpenHandles --forceExit", "test:cov": "jest --runInBand --coverage --verbose", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:file": "jest --runInBand --verbose", "test:watch": "jest --watch", "typeorm": "TS_NODE_PROJECT=./tsconfig-migrator.json typeorm-ts-node-commonjs", "worker": "nest start --config nest-cli-worker.json", "worker:debug": "nest start --debug --watch --config nest-cli-worker.json", "worker:dev": "nest start --watch --config nest-cli-worker.json | pino-pretty --singleLine --colorize", "worker:prod": "node dist/workflow.worker", "make-badges": "jest --coverage --coverageReporters=\"json-summary\" && istanbul-badges-readme"}}