import {
    BadRequestException,
    ForbiddenException,
    Injectable,
    UnauthorizedException,
} from '@nestjs/common';
import {
    Api<PERSON>ey,
    ApiKeyRepository,
    ApiKeyType,
    AppInstallationRepository,
    InstallationStatus,
    TransactionService,
    UserRepository,
} from '@repo/thena-platform-entities';
import * as crypto from 'crypto';
import { RequestMetadataInterface } from '../../interfaces/request-metadata.interface';
import { VaultApiKeyStorageService } from '../../secure-storage/service/vault-api-key-storage.service';
@Injectable()
export class ApiKeyService {
  private readonly KEY_PREFIX = {
    [ApiKeyType.LIVE]: 'pk_live_',
    [ApiKeyType.TEST]: 'pk_test_',
  };

  constructor(
    private readonly apiKeyRepository: ApiKeyRepository,
    private readonly userRepository: UserRepository,
    private readonly vaultApiKeyStorageService: VaultApiKeyStorageService,
    private readonly transactionService: TransactionService,
    private readonly appInstallationRepository: AppInstallationRepository,
  ) {}

  private generateApiKey(type: ApiKeyType = ApiKeyType.LIVE): {
    keyId: string;
    keySecret: string;
  } {
    const prefix = this.KEY_PREFIX[type];
    // Using URL-safe characters for both keyId and secret
    const keyId = `${prefix}${crypto.randomBytes(12).toString('base64url')}`;
    const keySecret = crypto.randomBytes(32).toString('base64url');
    return { keyId, keySecret };
  }

  private hashApiKey(keySecret: string): string {
    /**
     *  Note:
     *  Using a constant-time comparison safe hash
     *  ❌ Vulnerable to timing attacks for example if (hash1 === hash2) { ... }
     *  This is a constant-time comparison safe hash
     *  ✅ Secure against timing attacks example
     *  if (crypto.timingSafeEqual(hash1, hash2)) { ... }
     */
    return crypto
      .createHmac('sha256', process.env.API_KEY_SECRET || 'your-secret-key')
      .update(keySecret)
      .digest('hex');
  }

  /**
   * Check if an API key belongs to a privileged app
   * @param userUid - The UID of the user (bot) associated with the API key
   * @returns Promise<boolean> - True if the user's app is privileged
   */
  private async isPrivilegedApp(userUid: string): Promise<boolean> {
    try {
      const appInstallation = await this.appInstallationRepository
        .createQueryBuilder('appInstallation')
        .leftJoinAndSelect('appInstallation.app', 'app')
        .where('appInstallation.botUserId = :botUserId', { botUserId: userUid })
        .andWhere('appInstallation.status = :status', { status: InstallationStatus.ACTIVE })
        .andWhere('app.isThenaPrivileged = :isPrivileged', { isPrivileged: true })
        .getOne();

      return !!appInstallation;
    } catch (_error) {
      // Return false for safety if there's an error
      return false;
    }
  }

  /**
   * Deactivates an API key automatically due to system events
   * Examples: key expiration, suspicious activity, or system-initiated deactivation
   * This is an internal method not exposed to users
   *
   * @param keyId - The ID of the API key to deactivate
   * @returns Promise<void>
   */
  private async deactivateApiKey(keyId: string): Promise<void> {
    await this.apiKeyRepository.update(
      { id: keyId },
      {
        isActive: false,
        metadata: {
          revokedReason: 'SYSTEM_DEACTIVATED',
          revokedAt: new Date(),
        },
      },
    );
  }

  async createApiKey(params: {
    name: string;
    userId: string;
    organizationId: string;
    type?: ApiKeyType;
    expiresAt?: Date;
    metadata?: Partial<ApiKey['metadata']>;
    createdBy?: string;
    requestMetadata: RequestMetadataInterface;
  }): Promise<{ apiKey: ApiKey; secretKey: string }> {
    // Validate the expiration date
    if (params.expiresAt && params.expiresAt < new Date()) {
      throw new BadRequestException('Expiration date must be in the future');
    }

    // Check if user belongs to the organization
    // We will validate the user from the authId as this is the user id from supabase.
    const result = await this.userRepository.findByCondition({
      withDeleted: true,
      where: {
        authId: params.userId,
        organization: {
          uid: params.organizationId,
        },
      },
      select: {
        id: true,
        uid: true,
        authId: true,
        email: true,
        organization: {
          id: true,
          uid: true,
          name: true,
        },
      },
      relations: {
        organization: true,
      },
    });

    if (!result || result.organization.uid !== params.organizationId) {
      throw new ForbiddenException(
        'User does not have access to this organization',
      );
    }

    const { keyId, keySecret } = this.generateApiKey(params.type);
    const keyHash = this.hashApiKey(keySecret);

    const apiKey = this.apiKeyRepository.create({
      name: params.name,
      keyId,
      keyHash,
      type: params.type || ApiKeyType.TEST,
      userId: result.id, // Now we will use our public schema user ids.
      organizationId: result.organization.id,
      expiresAt: params.expiresAt ?? null,
      metadata: {
        ...params.metadata,
        createdFromIp: params.requestMetadata.ipAddress,
        lastUsedFromIp: params.requestMetadata.ipAddress,
        userAgent: params.requestMetadata.userAgent,
      },
      createdBy: result.id,
      isActive: true,
    });

    try {
      const savedApiKey = await this.transactionService.runInTransaction(
        async (txnContext) => {
          await this.vaultApiKeyStorageService.storeApiKeySecret(
            keyId,
            keySecret,
          );
          return await this.apiKeyRepository.saveWithTxn(txnContext, apiKey);
        },
      );
      return {
        apiKey: savedApiKey,
        secretKey: `${keyId}.${keySecret}`,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      throw new Error('Failed to create API key: ' + errorMessage);
    }
  }

  async validateApiKey(
    fullKey: string,
    requestMetadata: RequestMetadataInterface,
  ): Promise<any> {
    // change any to proper type
    try {
      const [keyId, keySecret] = fullKey.split('.');

      if (!keyId || !keySecret) {
        return { isValid: false, error: 'Invalid API key format' };
      }

      const keyHash = this.hashApiKey(keySecret);
      const apiKey = await this.apiKeyRepository.findByCondition({
        where: {
          keyId,
          keyHash,
          isActive: true,
        },
      });

      if (!apiKey) {
        return { isValid: false, error: 'Invalid API key' };
      }

      const user = await this.userRepository.findByCondition({
        where: {
          id: apiKey.userId,
        },
        select: {
          id: true,
          uid: true,
          authId: true,
          email: true,
          userType: true,
          timezone: true,
          organization: {
            id: true,
            uid: true,
            tier: true,
          },
        },
        relations: {
          organization: true,
        },
      });

      if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
        await this.deactivateApiKey(apiKey.id);
        return { isValid: false, error: 'API key has expired' };
      }

      // Update last used timestamp
      await this.apiKeyRepository.update(apiKey.id, {
        metadata: {
          lastUsedAt: new Date(),
          lastUsedFromIp: requestMetadata.ipAddress,
          userAgent: requestMetadata.userAgent,
        },
      });

      // Check if this API key belongs to a privileged app
      const isPrivileged = await this.isPrivilegedApp(user.uid);

      const response = {
        sub: user.id,
        authId: user.authId,
        uid: user.uid,
        email: user.email,
        orgId: user.organization.id,
        orgUid: user.organization.uid,
        orgTier: user.organization.tier,
        userType: user.userType,
        timezone: user.timezone,
        scopes: [],
        isPrivilegedApp: isPrivileged, // Add privileged app status
      };
      return { isValid: true, ...response };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      return {
        isValid: false,
        error: 'Error validating API key',
        trace: errorMessage,
      };
    }
  }

  /**
   * Lists all active API keys for a user within an organization
   * Users can have multiple API keys for different purposes:
   * - Different environments (dev, staging, prod)
   * - Different applications or services
   * - Different access levels or scopes
   * - Key Rotation and migration periods
   *
   * @param userId - The ID of the user
   * @param organizationId - The organization context
   * @param options - Optional filtering parameters
   * @returns Promise<ApiKey[]> List of active API keys
   */

  async listApiKeys(
    userId: string,
    organizationId: string,
    options: {
      includeInactive?: boolean;
      type?: ApiKeyType;
      scope?: string[];
    } = {},
  ): Promise<ApiKey[]> {
    const where: any = {
      userId,
      organizationId,
    };

    if (!options.includeInactive) {
      where.isActive = true;
    }

    if (options.type) {
      where.type = options.type;
    }

    if (options.scope && options.scope.length > 0) {
      where.scopes = options.scope;
    }

    return await this.apiKeyRepository.findAll({
      where,
      order: {
        createdAt: 'DESC',
      },
    });
  }

  /**
   * Revokes an API key based on user/admin action
   * This is an explicit user-initiated action and requires proper authorization
   * Maintains audit trail of who revoked the key and when
   *
   * @param keyId - The ID of the API key to revoke
   * @param userId - The ID of the user performing the revocation
   * @param organizationId - The organization context
   * @throws UnauthorizedException if user doesn't have permission or key doesn't exist
   * @returns Promise<void>
   */
  async revokeApiKey(
    keyId: string,
    userId: string,
    organizationId: string,
  ): Promise<void> {
    const result = await this.apiKeyRepository.update(
      {
        id: keyId,
        userId,
        organizationId,
      },
      {
        isActive: false,
        metadata: {
          revokedBy: userId,
          revokedAt: new Date(),
          revokedReason: 'USER_REVOKED',
        },
      },
    );

    if (result.affected === 0) {
      throw new UnauthorizedException('API key not found or unauthorized');
    }
  }

  /**
   * Fetch the secret from the vault
   * @param keyId - The ID of the API key
   * @returns Promise<string> The secret key
   * TODO: add security checks to ensure the user has permission to fetch the secret
   */
  async fetchSecretFromVault(keyId: string): Promise<{ api_key: string }> {
    return await this.vaultApiKeyStorageService.getApiKeySecret(keyId);
  }
}
