import {
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ILogger } from '@repo/nestjs-commons/logger';
import { CurrentUser } from '@repo/nestjs-commons/utils';
import {
  App,
  AppRepository,
  CachedOrganizationRepository,
  CachedUserRepository,
  TransactionService,
  User,
  UserRepository,
  UserType,
} from '@repo/thena-platform-entities';
import { AuthUser, createClient, SupabaseClient } from '@supabase/supabase-js';
import { randomBytes } from 'crypto';
import { DataSource, FindOneOptions, FindOptionsWhere } from 'typeorm';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import {
  LoginDto,
  RegisterBotDto,
  RegisterCustomerDto,
  RegisterDto,
} from '../dto';

@Injectable()
export class AuthenticationService {
  public supabaseClient: SupabaseClient;

  constructor(
    @Inject('CustomLogger') private readonly logger: ILogger,
    private readonly configService: ConfigService,
    private readonly cachedUserRepository: CachedUserRepository,
    private readonly userRepository: UserRepository,
    private readonly cachedOrganizationRepository: CachedOrganizationRepository,
    private readonly transactionService: TransactionService,
    private readonly appRepository: AppRepository,
    private readonly datasource: DataSource,
  ) {
    this.supabaseClient = createClient(
      this.configService.get(ConfigKeys.SUPABASE_API_URL),
      this.configService.get(ConfigKeys.SUPABASE_API_KEY),
      {
        auth: {
          flowType: 'pkce',
        },
      },
    );
  }

  async getOrganizationBySlug(slug: string) {
    // Lookup organization by slug
    const org = await this.cachedOrganizationRepository.findByCondition({
      where: { slug },
    });

    return org;
  }

  /**
   * Registers a new user in the system.
   * @param registerDto The DTO containing the user's information.
   * @returns The newly created user.
   */
  async register(
    registerDto: RegisterDto,
  ): Promise<{ authUser: AuthUser; dbUser: User }>;
  async register(
    registerDto: RegisterCustomerDto,
  ): Promise<{ authUser: AuthUser; dbUser: User }>;
  async register(registerDto: RegisterBotDto) {
    const {
      name,
      email,
      appId,
      password,
      organizationId,
      externalId = '',
      isCustomer = false,
      isOrgAdmin = false,
      isBot = false,
      purpose = '',
      avatarUrl = '',
    } = registerDto;

    // Lookup organization
    const organization =
      await this.cachedOrganizationRepository.findByCondition({
        where: {
          uid: organizationId,
        },
      });

    // Check if organization is active [or exists]
    if (!organization || !organization.isActive) {
      throw new NotFoundException('Organization not found!');
    }

    // Check if user already exists
    const existingUser = await this.userRepository.findByCondition({
      where: { email, organization: { id: organization.id } },
    });

    // If user already exists, throw an error
    if (existingUser) {
      return { dbUser: existingUser };
    }

    // Create a new user
    const { authUser, dbUser } = await this.transactionService.runInTransaction(
      async (txnContext) => {
        this.logger.log(
          `Creating user: ${email} name:${name} appId:${appId} orgId:${organizationId}`,
        );

        let authUser: any;

        // Check if user already exists
        const { data, error } = await this.supabaseClient.auth.admin.createUser(
          {
            email,
            password,
            email_confirm: true,
          },
        );

        // Error encountered while creating user from Supabase
        // if the user already exists, we need to fetch the user from the database
        // and use that user's id and create a persona
        if (error) {
          this.logger.error(
            `Error encountered while creating user: ${error.message}`,
            error?.stack,
          );

          if (error.code === 'email_exists') {
            this.logger.debug(`User with email ${email} already exists!`);
            const qr = this.datasource.createQueryRunner();
            const u = await qr.query(
              `SELECT * FROM auth.users WHERE "email" = $1`,
              [email.toLowerCase()],
            );
            qr.release();

            authUser = u[0];
          } else {
            throw error;
          }
        } else {
          authUser = data.user;
        }

        let app: App = null;
        if (appId) {
          // Lookup app
          app = await this.appRepository.findByCondition({
            where: { id: appId },
          });

          // If app is not found, throw an error
          if (!app) throw new NotFoundException('App not found!');
        }

        // Create a new user in Platform users table
        const userType = this.getUserType(isCustomer, isOrgAdmin, isBot);
        const newDbUser = this.userRepository.create({
          name,
          email: email.toLowerCase(),
          userType,
          metadata: {
            appId,
            externalId,
            isAgent: app?.isAgentApp ?? false,
            purpose,
          },
          avatarUrl,
          authId: authUser.id,
          organizationId: organization.id,
          uid: this.generateUserIdentifier(userType),
        });

        // Save user to Platform users table
        const savedUser = await this.userRepository.saveWithTxn(
          txnContext,
          newDbUser,
        );

        // Update user metadata in Supabase
        await this.supabaseClient.auth.admin.updateUserById(authUser.id, {
          user_metadata: {
            appId,
            id: savedUser.id,
            uid: savedUser.uid,
            orgId: organization.id,
          },
        });

        // Return the new user
        return { authUser: authUser, dbUser: savedUser };
      },
    );

    // Return the newly created user
    return { authUser, dbUser };
  }

  private getUserType(
    isCustomer: boolean,
    isOrgAdmin: boolean,
    isBot: boolean,
  ): UserType {
    if (isOrgAdmin) return UserType.ORG_ADMIN;
    if (isCustomer) return UserType.CUSTOMER_USER;
    if (isBot) return UserType.BOT_USER;
    return UserType.ORG_ADMIN;
  }

  /**
   * Logs in a user with the provided email and password.
   * @param loginDto The DTO containing the user's information.
   * @param queryString The query string is passed if the login request is from a Login page
   * @returns The user and the session token.
   */
  async login(loginDto: LoginDto, queryString?: string) {
    const { email, password, organizationId } = loginDto;

    let user: User | null = null;
    if (organizationId) {
      // Lookup organization
      const org = await this.cachedOrganizationRepository.findByCondition({
        where: { uid: organizationId },
      });

      // If organization is not found, throw an error
      if (!org) throw new NotFoundException('Organization not found!');

      // Lookup user in the public schema
      user = await this.cachedUserRepository.findByCondition({
        where: { email, organizationId: org.id },
        relations: ['organization'],
      });

      // If user is not found, throw an error
      if (!user) throw new NotFoundException('Invalid email or password!');
    }

    // Sign in with password
    const { data, error } = await this.supabaseClient.auth.signInWithPassword({
      email,
      password,
    });

    // If there is an error, throw it
    if (error) {
      if (queryString) {
        throw new Error(error.message);
      }
      throw new UnauthorizedException(error.message);
    }

    // Generate access token for inter-service communication
    const accessToken = data.session.access_token;
    const refreshToken = data.session.refresh_token;

    // Add scopes to the user
    const returnedUser = {
      ...user,
      scopes: [
        'app.create',
        'app.publish',
        'app.manage',
        'app.install',
        'app.view',
      ],
    };

    // Construct the final payload
    const payload = { user: returnedUser, accessToken, refreshToken };

    // Return the final payload
    return payload;
  }

  /**
   * Refreshes the session and returns the new access token.
   * @param refreshToken The refresh token.
   * @returns The new access token.
   */
  async refreshSession(refreshToken: string) {
    // If there is no refresh token, throw an error
    if (!refreshToken) {
      throw new UnauthorizedException('Invalid refresh token!');
    }

    // Refresh the session
    const { data, error } = await this.supabaseClient.auth.refreshSession({
      refresh_token: refreshToken,
    });

    // If there is an error, throw it
    if (error) throw new UnauthorizedException(error.message);

    // Return the new access token
    return {
      accessToken: data.session.access_token,
      refreshToken: data.session.refresh_token,
    };
  }

  /**
   * Gets the user details.
   * @param user The user.
   * @param orgId The organization ID.
   * @returns The user details.
   */
  async getUserDetails(user: { id: string }, orgId?: string) {
    const whereClause: FindOptionsWhere<User> = { authId: user.id };
    const query: FindOneOptions<User> = { where: whereClause };

    // If organization ID is provided, lookup the organization
    if (orgId) {
      // Lookup organization
      const org = await this.cachedOrganizationRepository.findByCondition({
        where: { uid: orgId },
      });

      // If organization is not found, throw an error
      if (!org) throw new NotFoundException('Organization not found!');

      // Set the organization ID
      whereClause.organizationId = org.id;

      // Include the organization in the query
      query.relations = ['organization'];
    }

    // Lookup user in the Platform users table
    const userPersona = await this.userRepository.findByCondition(query);

    // Return the user details
    return userPersona;
  }

  /**
   * Gets the organizations for a user.
   * @param user The current user.
   * @returns The organizations.
   */
  async getOrganizations(user: { id: string }) {
    const orgs = await this.userRepository.findAll({
      where: { authId: user.id },
      relations: { organization: true },
    });

    return orgs.map((u) => ({ ...u.organization, userType: u.userType }));
  }

  /**
   * Validates the access token and returns the user.
   * @param validateAndGetUserDto The DTO containing the access token.
   * @returns The user.
   */
  async validateAndGetUser(user: CurrentUser) {
    // Lookup user in the Platform users table
    const dbUser = await this.cachedUserRepository.findByCondition({
      where: { authId: user.authId, organization: { uid: user.orgUid } },
      relations: ['organization'],
      select: {
        id: true,
        authId: true,
        uid: true,
        email: true,
        organization: { uid: true, id: true, tier: true },
        userType: true,
        timezone: true,
      },
    });

    // If user is not found, throw an error
    if (!dbUser) throw new NotFoundException('User not found!');

    // Return the user data
    return { dbUser };
  }

  /**
   * Generates a random identifier for a user.
   * @param userType The type of user.
   * @returns A random identifier for the user.
   */
  private generateUserIdentifier(userType: UserType): string {
    switch (userType) {
      case UserType.ORG_ADMIN:
      case UserType.USER: {
        return this.generateRandomId('U');
      }

      case UserType.CUSTOMER_USER: {
        return this.generateRandomId('C');
      }

      case UserType.APP_USER: {
        return this.generateRandomId('A');
      }

      case UserType.BOT_USER: {
        return this.generateRandomId('O');
      }

      default: {
        throw new Error('Invalid user type');
      }
    }
  }

  async getUserForOrg(userId: string, orgId: string) {
    const user = await this.cachedUserRepository.findByCondition({
      where: { uid: userId, organization: { uid: orgId } },
      relations: ['organization'],
      select: {
        id: true,
        authId: true,
        uid: true,
        email: true,
        organization: { id: true, uid: true, tier: true },
        userType: true,
        timezone: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Return standardized user format matching the authentication responses
    return {
      sub: user.id,
      authId: user.authId,
      uid: user.uid,
      email: user.email,
      orgId: user.organization.id,
      orgUid: user.organization.uid,
      orgTier: user.organization.tier,
      userType: user.userType,
      timezone: user.timezone,
      scopes: [], // Consistent with other auth responses
    };
  }

  /**
   * Generates a random string of the specified length.
   * @param prefix The prefix to prepend to the generated string.
   * @param length The length of the generated string.
   * @returns A random string of the specified length.
   */
  private generateRandomId(prefix: string, length: number = 10): string {
    const ALPHABET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

    const randomBytesLength = Math.ceil(((length - 1) * 6) / 8);
    const randomBytesBuffer = randomBytes(randomBytesLength);

    let id = prefix;
    for (let i = 0; i < length - 1; i++) {
      const randomIndex =
        randomBytesBuffer.readUInt8(Math.floor((i * 6) / 8)) % ALPHABET.length;
      id += ALPHABET[randomIndex];
    }

    return id;
  }

  /**
   * Logs in a user using Google OAuth.
   * @param loginDto The DTO containing the user's information (email, organizationId).
   * @param queryString The query string is passed if the login request is from a Login page
   * @returns The user and the session token (if available from Supabase).
   */
  async loginWithGoogle(oauthRedirect: string) {
    const { data, error } = await this.supabaseClient.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: oauthRedirect
          ? `${this.configService.get(ConfigKeys.MCP_AUTHENTICATION_SERVICE_HOST)}/v1/authentication/google/callback?redirect=${encodeURIComponent(oauthRedirect)}`
          : this.configService.get(ConfigKeys.MCP_AUTHENTICATION_SERVICE_HOST),
      },
    });

    if (error) {
      throw new Error(error.message);
    }

    if (data.url) {
      return data.url; // use the redirect API for your server framework
    }
  }

  /**
   * Exchanges an OAuth code for a Supabase session.
   * @param code The authorization code from the OAuth provider.
   * @returns The session data and any error from Supabase.
   */
  async exchangeCodeForSession(code: string) {
    return await this.supabaseClient.auth.exchangeCodeForSession(code);
  }
}
