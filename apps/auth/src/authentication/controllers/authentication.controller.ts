import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { ILogger } from '@repo/nestjs-commons/logger';
import { CurrentUser } from '@repo/nestjs-commons/utils';
import { GENERIC_ERROR_MESSAGES } from '@repo/thena-shared-libs';
import { FastifyReply, FastifyRequest } from 'fastify';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { AuthenticationRedirectDto, LoginDto, OrganizationResponseDto, RegisterDto } from '../dto';
import { AuthenticationService } from '../services/authentication.service';
import { SupabaseAuthGuard } from '../supabase-auth.guard';

@ApiTags('Authentication')
@Controller('v1/authentication')
export class AuthenticationController {
  constructor(
    @Inject('CustomLogger') private readonly logger: ILogger,
    private readonly authenticationService: AuthenticationService,
    private readonly configService: ConfigService,
  ) {}

  @Post('/register')
  @ApiBody({ type: RegisterDto })
  async register(@Body() registerDto: RegisterDto) {
    const { dbUser } = await this.authenticationService.register(registerDto);
    return { id: dbUser.uid };
  }

  @Get('/me')
  @UseGuards(SupabaseAuthGuard)
  async me(@Req() req: FastifyRequest) {
    const orgId = req.headers['x-org-id'] as string;
    const user = await this.authenticationService.getUserDetails(
      req.user,
      orgId,
    );

    return {
      ...req.user,
      display_name: user.name,
      user_image: user.avatarUrl,
      organization_name: user.organization?.name,
      team_image: user.organization?.logoUrl,
      user_persona: {
        uid: user.uid,
        organization_id: user.organization?.uid,
        name: user.name,
        email: user.email,
        user_type: user.userType,
        avatar_url: user.avatarUrl,
        timezone: user.timezone,
        created_at: user.createdAt,
      },
    };
  }

  @Get('/organization/:slug')
  @UseGuards(SupabaseAuthGuard)
  async getOrganizationBySlug(@Param('slug') slug: string) {
    try {
      const org = await this.authenticationService.getOrganizationBySlug(slug);
      return OrganizationResponseDto.fromEntity(org);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error instanceof Error) {
        this.logger.error(
          `Error getting organization by slug: ${slug}, ${error?.message}`,
          error.stack,
        );
      } else {
        console.error(`Error getting organization by slug: ${slug}`, error);
      }

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Get('/organizations')
  @UseGuards(SupabaseAuthGuard)
  async getOrganizations(@CurrentUser() user: { id: string }) {
    const orgs = await this.authenticationService.getOrganizations(user);
    return orgs.map(OrganizationResponseDto.fromEntity);
  }

  @Post('/refresh')
  async refresh(@Body() body: { refreshToken: string }) {
    // If there is no refresh token, throw an error
    if (!body.refreshToken) {
      throw new BadRequestException('Refresh token is required!');
    }

    // Refresh the session
    const { accessToken, refreshToken: newRefreshToken } =
      await this.authenticationService.refreshSession(body.refreshToken);

    // Return the new access token and refresh token
    return { accessToken, refreshToken: newRefreshToken };
  }

  @Post('/login')
  @ApiBody({ type: LoginDto })
  async login(
    @Body() loginDto: LoginDto,
    @Query() query: AuthenticationRedirectDto,
    @Res() reply: FastifyReply) {
    try {
      const queryString = new URLSearchParams(query as any).toString();
      const { accessToken, refreshToken, user } =
        await this.authenticationService.login(loginDto, queryString);

      if (query.redirect) {
        reply.setCookie('access_token', accessToken, { httpOnly: true, path: '/' });
        return reply.redirect(`${query.redirect}`, 302);
      }

      return {
        accessToken,
        refreshToken,
        user: user
          ? {
              id: user.uid,
              email: user.email,
              name: user.name,
              userType: user.userType,
            }
          : null,
      };
    } catch (_error) {
      // Prepare query string for the login page
      const queryString = new URLSearchParams(query as any).toString();
      // Show a generic error message
      return reply.view('login.ejs', {
        error: 'Login failed. Please check your credentials and try again.',
        queryString,
      });
    }
  }

  @Get('/login')
  showLoginPage(@Res() reply: FastifyReply, @Query() query: AuthenticationRedirectDto) {
    const queryString = new URLSearchParams(query as any).toString();
    return reply.view('login.ejs', { queryString });
  }

  @Get('/google/callback')
  /**
   * Google OAuth callback endpoint. Exchanges the code for a session, sets the access token in a cookie, and redirects.
   * @param req FastifyRequest
   * @param reply FastifyReply
   * @param code The code from Google OAuth
   * @param state The original redirect URL or query string
   */
  async googleCallback(
    @Req() req: FastifyRequest,
    @Res() reply: FastifyReply,
    @Query('code') code: string,
    @Query('redirect') redirect?: string,
  ) {
    try {
      if (!code) {
             this.logger.error('Missing authorization code in Google callback');
             return reply.view('login.ejs', {
               error: 'Authentication failed. Missing authorization code.',
               queryString: '',
             });
          }
        // Exchange the code for a session
        const { data, error } = await this.authenticationService.exchangeCodeForSession(code);
        if (error) {
          this.logger.error(`Error exchanging code for session: ${error.message}`, error.stack);
          return reply.view('login.ejs', {
            error: 'Google login failed. Please try again.',
            queryString: '',
          });
        }
        // Set the access token in a cookie
        if (data.session?.access_token) {
          reply.setCookie('access_token', data.session.access_token, { httpOnly: true, path: '/' });
        }
        // Validate redirect URL to prevent open redirect vulnerabilities
        const validRedirect = redirect && 
                (redirect.startsWith('/') || 
                redirect.startsWith(`${this.configService.get(ConfigKeys.MCP_AUTHENTICATION_SERVICE_HOST)}`) || 
                redirect.startsWith(`${this.configService.get(ConfigKeys.AUTHENTICATION_SERVICE_HOST)}`))
        
        return reply.redirect(validRedirect ? redirect : '/', 302);
      
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error('Error in Google OAuth callback', error.stack);
      } else {
        this.logger.error('Error in Google OAuth callback', String(error));
      }
      return reply.view('login.ejs', {
        error: 'Google login failed. Please try again.',
        queryString: redirect ? `redirect=${encodeURIComponent(redirect)}` : '',
      });
    }
  }

  @Get('/login-with-google')
  async loginWithGoogle(@Req() req: FastifyRequest, @Res() reply: FastifyReply, @Query('redirect') redirect?: string) {
    try {
      // Build the query string to preserve redirect state
      const oauthRedirect = redirect;
      // You can pass email/organizationId if you want to restrict, or just pass empty for now
      const url = await this.authenticationService.loginWithGoogle(oauthRedirect);
      return reply.redirect(url, 302);
    } catch (error) {
      this.logger.error('Error during Google OAuth login', error instanceof Error ? error.stack : String(error));
      return reply.code(500).view('login.ejs', {
        error: 'Failed to start Google login. Please try again later.',
        queryString: '',
      });
    }
  }

}
