import { z } from 'zod';

export const createOrganizationAdminValidator = z.object({
  email: z.string().email('Invalid email address'),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters long' }),
  organizationUid: z.string(),
});

export const createUserValidator = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters long' }),
  organizationUid: z.string(),
  isCustomer: z.boolean().optional().default(false),
  externalId: z.string().optional(),
});

export const createBotUserValidator = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters long' }),
  organizationUid: z.string(),
  appId: z.string().optional(),
  purpose: z.string().optional(),
  avatarUrl: z.string().url('Invalid avatar url!').optional(),
});
