import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { getCertificateFromValut } from '@repo/nestjs-commons/utils';
import {
  Account,
  AccountActivity,
  AccountAttributeValue,
  AccountNote,
  AccountRelationship,
  AccountRelationshipType,
  AccountTask,
  ApiKey,
  App,
  AuditLog,
  BusinessHoursConfig,
  Comment,
  CustomerContact,
  CustomField,
  CustomFieldValues,
  Draft,
  Emojis,
  Form,
  Mentions,
  OAuthAuthorizationCode,
  OAuthClient,
  OAuthToken,
  Organization,
  OrganizationDomains,
  OrganizationInvitations,
  Reactions,
  Storage,
  Tag,
  Team,
  TeamCapacity,
  TeamConfiguration,
  TeamMember,
  TeamRoutingRules,
  Ticket,
  TicketPriority,
  TicketRelationships,
  TicketSentiment,
  TicketStatus,
  TicketTimeLog,
  TicketType,
  TimeOff,
  User,
  UserSkills,
  Views,
  ViewsType,
} from '@repo/thena-platform-entities';
import { ConfigKeys, ConfigService } from '../config.service';

const entities = [
  ApiKey,
  Account,
  AccountActivity,
  AccountAttributeValue,
  CustomerContact,
  AccountNote,
  AccountRelationship,
  AccountRelationshipType,
  AccountTask,
  AuditLog,
  BusinessHoursConfig,
  Comment,
  CustomField,
  CustomFieldValues,
  Draft,
  Emojis,
  Mentions,
  Organization,
  OrganizationDomains,
  OrganizationInvitations,
  Form,
  Reactions,
  App,
  Storage,
  Tag,
  Team,
  TeamCapacity,
  TeamConfiguration,
  TeamMember,
  TeamRoutingRules,
  Ticket,
  TicketPriority,
  TicketSentiment,
  TicketRelationships,
  TicketStatus,
  TicketTimeLog,
  TicketType,
  TimeOff,
  User,
  UserSkills,
  Views,
  ViewsType,
  OAuthClient,
  OAuthAuthorizationCode,
  OAuthToken,
];

const getConnectionUrl = (configService: ConfigService) => {
  const dbConfig = {
    host: configService.get(ConfigKeys.THENA_AUTH_DB_HOST),
    port: configService.get(ConfigKeys.THENA_AUTH_DB_PORT),
    database: configService.get(ConfigKeys.THENA_AUTH_DB_NAME),
    username: configService.get(ConfigKeys.THENA_AUTH_DB_USER),
    password: configService.get(ConfigKeys.THENA_AUTH_DB_PASSWORD),
  };
  return `postgresql://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
};

async function getSSLConfig(
  configService: ConfigService,
  isDevelopment: boolean,
) {
  if (isDevelopment) {
    return false;
  }

  return await getCertificateFromValut({
    url: configService.get(ConfigKeys.VAULT_URL),
    token: configService.get(ConfigKeys.VAULT_TOKEN),
    certPath: configService.get(ConfigKeys.CERT_PATH),
  });
}

/**
 * Get the Auth DB configuration
 * @param configService The configuration service
 * @returns The Auth DB configuration
 */
export const getAuthDBConfig = async (
  configService: ConfigService,
): Promise<TypeOrmModuleOptions> => {
  const environment = configService.get(ConfigKeys.NODE_ENV);
  const isDevelopment = environment === 'development';
  const ssl = await getSSLConfig(configService, isDevelopment);

  // Development environments configuration
  return {
    type: 'postgres',
    url: getConnectionUrl(configService),
    entities,
    ssl,
    synchronize: isDevelopment ? true : false,
    logging: false,
    extra: {
      max: 20,
      connectionTimeoutMillis: 60000,
      idleTimeoutMillis: 60000,
    },
  };
};
