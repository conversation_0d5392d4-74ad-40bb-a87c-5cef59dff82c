import { Injectable, OnModuleInit } from "@nestjs/common";
import { BaseGrpcClient } from "@repo/nestjs-commons/grpc";
import { ILogger } from "@repo/nestjs-commons/logger";
import { apps } from "@repo/shared-proto";

@Injectable()
export class AppsGrpcClient extends BaseGrpcClient implements OnModuleInit {
  constructor(logger: ILogger) {
    super(logger, "apps");
  }

  onModuleInit() {
    this.initializeClient("apps", "Apps");
  }

  protected getServiceUrl(): string {
    // TODO: Get from config service
    return process.env.APPS_GRPC_URL || "0.0.0.0:50053";
  }

  /**
   * Check if a bot user belongs to a Thena-owned app
   * @param botUserUid The UID of the bot user
   * @returns Promise<apps.IsThenaOwnedAppResponse>
   */
  async isThenaOwnedApp(
    botUserUid: string,
  ): Promise<apps.IsThenaOwnedAppResponse> {
    try {
      return await this.makeGrpcRequest<
        apps.IsThenaOwnedAppRequest,
        apps.IsThenaOwnedAppResponse
      >("IsThenaOwnedApp", { botUserUid }, {});
    } catch (error) {
      // Return false for safety if gRPC call fails
      return {
        isThenaOwned: false,
        appUid: "",
        appName: "",
      };
    }
  }
}
