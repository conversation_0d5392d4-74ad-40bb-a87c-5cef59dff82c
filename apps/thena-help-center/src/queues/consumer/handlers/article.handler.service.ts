import { Injectable } from "@nestjs/common";
import { ObjectId } from "mongodb";
import { NodeHtmlMarkdown } from "node-html-markdown";
import OpenAI from "openai";
import {
  ArticleState,
  ArticleVisibility,
} from "src/api/article/article.constants";
import { ArticleRepository } from "src/api/article/article.repository";
import { CollectionRepository } from "src/api/collection/collection.repository";
import { Collection } from "src/api/collection/collection.schema";
import { HelpCenterRepository } from "src/api/help-center/help-center.repository";
import { HelpCenter } from "src/api/help-center/help-center.schema";
import { SettingRepository } from "src/api/settings/setting.repository";
import { TagRepository } from "src/api/tag/tag.repository";
import { Tag } from "src/api/tag/tag.schema";
import { AssistantType } from "src/openai/assistant/assistant.constant";
import { FileService } from "src/openai/file/file.service";
import { VectorService } from "src/openai/vector/vector.service";
import { LoggerService } from "src/shared/logger/logger.service";
import { TypesenseService } from "src/typesense/typesense.service";
import { stripHtml } from "string-strip-html";
import Typesense from "typesense";

@Injectable()
export class ArticleHandlerService {
  constructor(
    private readonly articleRepository: ArticleRepository,
    private readonly typesenseService: TypesenseService,
    private readonly tagRepository: TagRepository,
    private readonly collectionRepository: CollectionRepository,
    private readonly loggerService: LoggerService,
    private readonly helpCenterRepository: HelpCenterRepository,
    private readonly vectorService: VectorService,
    private readonly settingRepository: SettingRepository,
    private readonly fileService: FileService,
  ) {}

  async handleArticleUpsert(payload: { articleId: string }) {
    const logContext = `${ArticleHandlerService.name} - ${this.handleArticleUpsert.name} - articleId- ${payload.articleId}`;
    try {
      // Convert string to MongoDB ObjectId
      const articleId = new ObjectId(payload.articleId);

      // Find the article
      const article = await this.articleRepository.findOne({
        _id: articleId,
        isDeleted: false,
        state: ArticleState.PUBLISHED,
      });

      if (!article) {
        this.loggerService.error("Article not found", logContext);
        return;
      }

      // fetch article tags and its link to helpcenters, settings, helpCenterData
      const [tags, collections, setting, helpCenterData] = await Promise.all([
        this.tagRepository.find(
          {
            _id: { $in: article.tags },
          },
          { name: 1 },
        ),
        this.collectionRepository.find(
          { articles: articleId },
          { helpCenterId: 1 },
        ),
        this.settingRepository.findOne(
          {
            orgId: article.orgId,
          },
          { aiData: 1 },
        ),
        this.helpCenterRepository.find(
          {
            orgId: article.orgId,
          },
          { aiData: 1 },
        ),
      ]);

      const helpCenterIds = collections.map((collection: Collection) =>
        collection.helpCenterId.toString(),
      );

      this.loggerService.log(
        `This article belongs to these helpcenter ids ${JSON.stringify(helpCenterIds)}`,
        logContext,
      );

      const tagNames = tags.map((tag: Tag) => tag.name);

      if (helpCenterIds.length) {
        // convert html to plain text for indexing in typesense
        const articleText = stripHtml(article.publishedVersion.html, {
          stripTogetherWithTheirContents: [
            "script",
            "style",
            "xml",
            "iframe",
            "img",
          ],
        }).result;

        // Upsert document in Typesense
        const fileInTypesense = await this.typesenseService.indexDocument({
          id: article._id.toString(),
          uid: article.uid,
          organization_uid: article.orgId.toString(),
          title: article.title,
          description: article.description || "",
          body: articleText,
          tags: tagNames,
          help_center_ids: helpCenterIds,
          visibility: article.visibility,
        });
        this.loggerService.log(
          `Successfully processed typesense article upsert- ID- ${fileInTypesense.id}`,
          logContext,
        );
      } else {
        // Delete document from Typesense if article is not the part of any of the helpcenters
        const deletedFileFromTypesense = await Promise.allSettled([
          this.typesenseService.deleteDocument(article._id.toString()),
        ]);

        // If Typesense file is already deleted (404) silently consume the error
        if (
          deletedFileFromTypesense[0].status === "rejected" &&
          deletedFileFromTypesense[0].reason.httpStatus !== 404
        ) {
          throw deletedFileFromTypesense[0].reason;
        }
        this.loggerService.log(
          `Article deleted from Typesense successfully`,
          logContext,
        );
      }

      // Delete assistant file if already present
      if (article.openaiFileId) {
        const deletedFileFromOpenai = await Promise.allSettled([
          this.fileService.delete(article.openaiFileId),
        ]);
        // If Openai file is already deleted (404) silently consume the error
        if (
          deletedFileFromOpenai[0].status === "rejected" &&
          deletedFileFromOpenai[0].reason.status !== 404
        ) {
          throw deletedFileFromOpenai[0].reason;
        }
        this.loggerService.log(
          `Article file deleted from Openai successfully, file id- ${article.openaiFileId}`,
          logContext,
        );
        await this.articleRepository.findOneAndUpdate(
          { _id: article._id },
          { openaiFileId: "" },
        );
      }

      if (!helpCenterIds.length) {
        this.loggerService.log(
          `Article is not the part of any of the helpcenters skipping openai uploading`,
          logContext,
        );
        return;
      }

      const articleGeneratorAssistant = setting.aiData.assistants.find(
        (assistant: any) =>
          assistant.assistantType === AssistantType.ARTICLE_GENERATOR,
      );

      const markdownContent = NodeHtmlMarkdown.translate(
        article.publishedVersion.html,
      );

      const mdArticle = [
        "---",
        `type: article`,
        `id: ${article.uid}`,
        `title: ${article.title}`,
        `description: ${article.description || ""}`,
        `tags: [${tagNames.length ? tagNames.join(", ") : ""}]`,
        "---",
        "",
        markdownContent,
      ].join("\n");

      const fileBuffer = Buffer.from(mdArticle);
      const { id: openaiFileId } = await this.fileService.create(
        fileBuffer,
        `article_${article.uid}.md`,
      );

      this.loggerService.log(
        `New file created in openai file id- ${openaiFileId}`,
        logContext,
      );

      await this.articleRepository.findOneAndUpdate(
        { _id: article._id },
        { openaiFileId },
      );

      this.loggerService.log(
        `Article doc got updated with new openai file id- ${openaiFileId}`,
        logContext,
      );

      // Step 2: Reattach files based on the basis of visibility and help_center_ids
      const attachPromises: Promise<any>[] = [];

      // Always attach to article-generator
      attachPromises.push(
        this.vectorService.attachFile(
          articleGeneratorAssistant.vectorId,
          openaiFileId,
        ),
      );

      // Filter helpcenter data based on articles connected to helpcenters
      const filteredHelpcenters = helpCenterData.filter(
        (helpcenter: HelpCenter) =>
          helpCenterIds.includes(helpcenter._id.toString()),
      );

      // Attach to filtered helpcenters based on visibility
      filteredHelpcenters.forEach((helpcenter: HelpCenter) => {
        const assistants = helpcenter.aiData.assistants;

        switch (article.visibility) {
          case ArticleVisibility.PUBLIC:
            // Attach to all vectors
            assistants.forEach((assistant: any) => {
              attachPromises.push(
                this.vectorService.attachFile(assistant.vectorId, openaiFileId),
              );
            });
            break;

          case ArticleVisibility.CUSTOMER:
            // Attach only to customer and private vectors
            assistants.forEach((assistant: any) => {
              if (
                [
                  ArticleVisibility.CUSTOMER,
                  ArticleVisibility.PRIVATE,
                ].includes(assistant.assistantType)
              ) {
                attachPromises.push(
                  this.vectorService.attachFile(
                    assistant.vectorId,
                    openaiFileId,
                  ),
                );
              }
            });
            break;

          case ArticleVisibility.PRIVATE:
            // Attach only to private vectors
            const privateAssistant = assistants.find(
              (assistant: any) =>
                assistant.assistantType === ArticleVisibility.PRIVATE,
            );
            if (privateAssistant) {
              attachPromises.push(
                this.vectorService.attachFile(
                  privateAssistant.vectorId,
                  openaiFileId,
                ),
              );
            }
            break;
        }
      });

      // Wait for all attachments to complete
      const attachedFilesToVectors = await Promise.allSettled(attachPromises);

      // Filter out rejected promises and extract relevant error info
      const failures = attachedFilesToVectors
        .filter(
          (result): result is PromiseRejectedResult =>
            result.status === "rejected",
        )
        .map((result) => {
          const reason = result.reason;
          return {
            status: reason.status,
            request_id: reason.request_id,
            error: reason.error,
          };
        });

      if (failures.length > 0) {
        this.loggerService.error(
          `Attachment to vector failures- ${JSON.stringify(failures)}`,
          logContext,
        );
        throw new Error(`${failures.length} attachments to vector failed`);
      } else {
        this.loggerService.log(
          `Article got attached to all respective vectors`,
          logContext,
        );
      }
    } catch (error) {
      if (error instanceof Typesense.Errors.TypesenseError) {
        this.loggerService.error(
          `Typesense Error :: Failed to process article upsert :: Message- ${error.message} :: Error name- ${error.name} :: Error status- ${error.httpStatus}`,
          logContext,
          JSON.stringify(error.stack),
        );
      } else if (error instanceof OpenAI.APIError) {
        this.loggerService.error(
          `Open AI Error :: Failed to process article upsert :: Status- ${error.status} :: Error Object- ${JSON.stringify(error.error)}`,
          logContext,
          JSON.stringify(error.stack),
        );
      } else {
        this.loggerService.error(
          `Failed to process article upsert :: Message- ${error.message} :: Error name- ${error.name}`,
          logContext,
          JSON.stringify(error.stack),
        );
      }
      throw error;
    }
  }

  async handleArticleDelete(payload: { articleId: string }) {
    const logContext = `${ArticleHandlerService.name} - ${this.handleArticleDelete.name} - articleId- ${payload.articleId}`;
    try {
      // Convert string to MongoDB ObjectId
      const articleId = new ObjectId(payload.articleId);

      // Find the article
      const article = await this.articleRepository.findOne({
        _id: articleId,
      });

      if (!article) {
        this.loggerService.error("Article not found", logContext);
        return;
      }

      // Delete document from Typesense
      const deletedFileFromTypesense = await Promise.allSettled([
        this.typesenseService.deleteDocument(article._id.toString()),
      ]);

      // If Typesense file is already deleted (404) silently consume the error
      if (
        deletedFileFromTypesense[0].status === "rejected" &&
        deletedFileFromTypesense[0].reason.httpStatus !== 404
      ) {
        throw deletedFileFromTypesense[0].reason;
      }
      this.loggerService.log(
        `Article deleted from Typesense successfully`,
        logContext,
      );

      // Delete assistant file
      if (article.openaiFileId) {
        const deletedFileFromOpenai = await Promise.allSettled([
          this.fileService.delete(article.openaiFileId),
        ]);
        // If Openai file is already deleted (404) silently consume the error
        if (
          deletedFileFromOpenai[0].status === "rejected" &&
          deletedFileFromOpenai[0].reason.status !== 404
        ) {
          throw deletedFileFromOpenai[0].reason;
        }
        this.loggerService.log(
          `Article file deleted from Openai successfully, file id- ${article.openaiFileId}`,
          logContext,
        );

        await this.articleRepository.findOneAndUpdate(
          { _id: article._id },
          { openaiFileId: "" },
        );
      }
    } catch (error) {
      if (error instanceof Typesense.Errors.TypesenseError) {
        this.loggerService.error(
          `Typesense Error :: Failed to process article delete :: Message- ${error.message} :: Error name- ${error.name} :: Error status- ${error.httpStatus}`,
          logContext,
          JSON.stringify(error.stack),
        );
      } else if (error instanceof OpenAI.APIError) {
        this.loggerService.error(
          `Open AI Error :: Failed to process article delete :: Status- ${error.status} :: Error Object- ${JSON.stringify(error.error)}`,
          logContext,
          JSON.stringify(error.stack),
        );
      } else {
        this.loggerService.error(
          `Failed to process article delete :: Message- ${error.message} :: Error name- ${error.name}`,
          logContext,
          JSON.stringify(error.stack),
        );
      }
      throw error;
    }
  }
}
