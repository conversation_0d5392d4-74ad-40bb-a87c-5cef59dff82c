import { Injectable } from "@nestjs/common";
import OpenAI from "openai";
import { ArticleRepository } from "src/api/article/article.repository";
import { QnaRepository } from "src/api/qna/qna.repository";
import { SettingRepository } from "src/api/settings/setting.repository";
import {
  AssistantType,
  ThreadPrompts,
} from "src/openai/assistant/assistant.constant";
import { FileService } from "src/openai/file/file.service";
import { OpenAiClientService } from "src/openai/openai-client/openai-client.service";
import { VectorService } from "src/openai/vector/vector.service";
import { GeneralQueueProducerService } from "src/queues/producer/producer.service";
import { QnaEvents } from "src/queues/queue.constant";
import { LoggerService } from "src/shared/logger/logger.service";
import { InstallationRepository } from "src/thena-core/repositories/installation.repository";
import { NotificationMessageRepository } from "src/thena-core/repositories/notificationmessage.repository";

@Injectable()
export class QnaHandlerService {
  constructor(
    private readonly loggerService: LoggerService,
    private readonly settingRepository: SettingRepository,
    private readonly fileService: FileService,
    private readonly qnaRepository: QnaRepository,
    private readonly generalQueueProducerService: GeneralQueueProducerService,
    private readonly installationRepository: InstallationRepository,
    private readonly notificationMessageRepository: NotificationMessageRepository,
    private readonly openAiClientService: OpenAiClientService,
    private readonly vectorService: VectorService,
    private readonly articleRepository: ArticleRepository,
  ) {}

  async handleUpdateQna(payload: { orgId: string }) {
    const logContext = `${QnaHandlerService.name} - ${this.handleUpdateQna.name} - orgId- ${payload.orgId}`;
    try {
      const orgId = payload.orgId;
      const setting = await this.settingRepository.findOne(
        {
          orgId,
        },
        { aiData: 1 },
      );

      // First delete existing qna files
      const qnaFilePromises = [];
      if (setting.aiData.qnaFileId) {
        qnaFilePromises.push(this.fileService.delete(setting.aiData.qnaFileId));
      }
      if (setting.aiData.verifiedQnaFileId) {
        qnaFilePromises.push(
          this.fileService.delete(setting.aiData.verifiedQnaFileId),
        );
      }

      const fileDeletionResult = await Promise.allSettled(qnaFilePromises);

      // Throw error only in case of error other than file not found (404)
      if (
        fileDeletionResult[0] &&
        fileDeletionResult[0].status === "rejected" &&
        fileDeletionResult[0].reason.status !== 404
      ) {
        throw fileDeletionResult[0].reason;
      }
      if (
        fileDeletionResult[1] &&
        fileDeletionResult[1].status === "rejected" &&
        fileDeletionResult[1].reason.status !== 404
      ) {
        throw fileDeletionResult[1].reason;
      }

      this.loggerService.log(
        `qnaFile and verifiedQnaFile successfully deleted`,
        logContext,
      );

      const qnas = await this.qnaRepository.fetchQnaWithTagNames(orgId);
      const verifiedQnas = [];
      const allQnas = [];

      qnas.forEach((qna) => {
        if (qna.isVerified) {
          delete qna.isVerified;
          verifiedQnas.push(qna);
        }
        delete qna.isVerified;
        allQnas.push(qna);
      });

      if (allQnas.length) {
        const qnaAssistant = setting.aiData.assistants.find(
          (assistant: any) =>
            assistant.assistantType === AssistantType.QNA_GENERATOR,
        );

        const articleAssistant = setting.aiData.assistants.find(
          (assistant: any) =>
            assistant.assistantType === AssistantType.ARTICLE_GENERATOR,
        );

        const vectorAttachPromises = [];

        const qnaFileBuffer = Buffer.from(JSON.stringify(allQnas));
        const { id: qnaFileId } = await this.fileService.create(
          qnaFileBuffer,
          `qnaFile.json`,
        );

        vectorAttachPromises.push(
          this.vectorService.attachFile(qnaAssistant.vectorId, qnaFileId),
        );

        let verifiedQnaFileId = "";

        if (verifiedQnas.length) {
          const verifiedQnaFileBuffer = Buffer.from(
            JSON.stringify(verifiedQnas),
          );

          const fileData = await this.fileService.create(
            verifiedQnaFileBuffer,
            `verifiedQnaFile.json`,
          );
          verifiedQnaFileId = fileData.id;
          vectorAttachPromises.push(
            this.vectorService.attachFile(
              articleAssistant.vectorId,
              verifiedQnaFileId,
            ),
          );
        }

        // Update newly added file ids in setting
        await this.settingRepository.findOneAndUpdate(
          {
            orgId,
          },
          {
            "aiData.qnaFileId": qnaFileId,
            "aiData.verifiedQnaFileId": verifiedQnaFileId,
          },
        );

        // attach files to respective vectors
        await Promise.all(vectorAttachPromises);

        this.loggerService.log(
          `New file uploaded :: qnaFileId- ${qnaFileId} :: verifiedQnaFileId- ${verifiedQnaFileId}`,
          logContext,
        );
      }

      // fire next event for qna generation
      await this.generalQueueProducerService.sendMessage(
        QnaEvents.GENERATE_QNA,
        {
          orgId,
        },
      );

      this.loggerService.log(`Qna updated successfully in openai`, logContext);
    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        this.loggerService.error(
          `Open AI Error :: Failed to update qnas :: Status- ${error.status} :: Error Object- ${JSON.stringify(error.error)}`,
          logContext,
          JSON.stringify(error.stack),
        );
      } else {
        this.loggerService.error(
          `Failed to update qnas :: Message- ${error.message} :: Error name- ${error.name}`,
          logContext,
          JSON.stringify(error.stack),
        );
      }
      throw error;
    }
  }

  async handleGenerateQna(payload: { orgId: string }) {
    const logContext = `${QnaHandlerService.name} - ${this.handleGenerateQna.name} - orgId- ${payload.orgId}`;
    try {
      const orgId = payload.orgId;

      const setting = await this.settingRepository.findOne(
        {
          orgId,
        },
        { aiData: 1 },
      );

      const qnaAssistant = setting.aiData.assistants.find(
        (assistant: any) =>
          assistant.assistantType === AssistantType.QNA_GENERATOR,
      );

      const installation = await this.installationRepository.findOne(
        {
          organization_id: orgId,
        },
        { _id: 1 },
      );

      if (!installation) {
        this.loggerService.log(`Installation not found`, logContext);
        return;
      }

      // fetch all request closed in last 24 hours
      let requests = await this.notificationMessageRepository.find(
        {
          installation_id: installation._id,
          status: "CLOSED",
          "statusChangeActivity.action": { $ne: "AI_DEFLECTION_CLOSE" },
          closed_on: {
            $gte: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
          },
        },
        {
          activity: 1,
        },
      );

      requests = requests
        .map((request) => ({
          ...request,
          activity: request.activity
            .filter((activity) => activity?.user_type != null)
            .sort((a, b) => a?._id.toString() - b?._id.toString()),
        }))
        .filter((request) => request.activity.length > 1);

      if (!requests.length) {
        this.loggerService.log(
          `No request found skipping qna generation`,
          logContext,
        );
        return;
      }

      let content = `Slack Conversations: \n\n--------------------------\n\n`;

      let counter = 1;
      for (const request of requests) {
        let conversation = ``;
        for (const activity of request?.activity) {
          if (activity.user_type) {
            conversation += `name: ${
              activity?.user_info?.display_name ||
              activity?.user_info?.real_name ||
              activity?.user_info?.name ||
              "User"
            }\n`;
            conversation += `text: ${activity?.text}\n\n`;
          }
        }
        if (conversation) {
          conversation =
            `Conversation No- ${counter}\n\n` +
            conversation +
            `--------------------------\n\n`;
          content += conversation;
          counter += 1;
        }
      }

      this.loggerService.log(
        `${requests.length} requests are closed in last 24 hours which hold more than one message`,
        logContext,
      );

      const client = this.openAiClientService.getClient();

      const thread = await client.beta.threads.create();

      await client.beta.threads.messages.create(thread.id, {
        role: "user",
        content,
      });

      await client.beta.threads.runs.createAndPoll(thread.id, {
        assistant_id: qnaAssistant.assistantId,
      });

      const messages = await client.beta.threads.messages.list(thread.id);
      if (messages.data[0].content[0].type === "text") {
        const rawData = messages.data[0].content[0].text.value.replace(
          /\【.*?】/g,
          "",
        );

        // Check if string is wrapped in code blocks
        const codeBlockMatch = rawData.match(/^```json\s*([\s\S]*?)\s*```$/);

        // If matched, parse the content between code blocks
        const jsonString = codeBlockMatch ? codeBlockMatch[1] : rawData;
        const data = JSON.parse(jsonString);

        if (data.length) {
          const qnaRecords = data.map((qna) => ({
            insertOne: {
              document: {
                orgId,
                question: qna.question,
                answer: qna.answer,
              },
            },
          }));

          await this.qnaRepository.bulkInsert(qnaRecords);
          this.loggerService.log(
            `${data.length} qnas generated successfully`,
            logContext,
          );
        } else {
          this.loggerService.log(
            `No meaningfull conversation found`,
            logContext,
          );
        }
      }
      // fire event for finding article gaps
      await this.generalQueueProducerService.sendMessage(
        QnaEvents.ARTICLE_GAPS,
        {
          orgId,
        },
      );
    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        this.loggerService.error(
          `Open AI Error :: Failed to generate qnas :: Status- ${error.status} :: Error Object- ${JSON.stringify(error.error)}`,
          logContext,
          JSON.stringify(error.stack),
        );
      } else {
        this.loggerService.error(
          `Failed to generate qnas :: Message- ${error.message} :: Error name- ${error.name}`,
          logContext,
          JSON.stringify(error.stack),
        );
      }
      throw error;
    }
  }

  async handleArticleGaps(payload: { orgId: string }) {
    const logContext = `${QnaHandlerService.name} - ${this.handleArticleGaps.name} - orgId- ${payload.orgId}`;
    try {
      const orgId = payload.orgId;
      const setting = await this.settingRepository.findOne(
        {
          orgId,
        },
        { aiData: 1 },
      );

      const articleAssistant = setting.aiData.assistants.find(
        (assistant: any) =>
          assistant.assistantType === AssistantType.ARTICLE_GENERATOR,
      );

      const client = this.openAiClientService.getClient();

      const thread = await client.beta.threads.create();

      await client.beta.threads.messages.create(thread.id, {
        role: "user",
        content: ThreadPrompts.ARTICLE_GAP_IDENTIFIER,
      });

      await client.beta.threads.runs.createAndPoll(thread.id, {
        assistant_id: articleAssistant.assistantId,
      });

      const messages = await client.beta.threads.messages.list(thread.id);
      if (messages.data[0].content[0].type === "text") {
        const rawData = messages.data[0].content[0].text.value.replace(
          /\【.*?】/g,
          "",
        );

        // Check if string is wrapped in code blocks
        const codeBlockMatch = rawData.match(/^```json\s*([\s\S]*?)\s*```$/);

        // If matched, parse the content between code blocks
        const jsonString = codeBlockMatch ? codeBlockMatch[1] : rawData;
        const data = JSON.parse(jsonString);

        if (data.length) {
          this.loggerService.log(
            `${data.length} gaps found in articles for ids- ${JSON.stringify(data)}`,
            logContext,
          );

          await this.articleRepository.updateMany(
            { orgId, uid: { $in: data } },
            { isUpdateRequired: true },
          );
        } else {
          this.loggerService.log(`No gaps found in articles`, logContext);
        }
      }
    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        this.loggerService.error(
          `Open AI Error :: Failed to find article gaps :: Status- ${error.status} :: Error Object- ${JSON.stringify(error.error)}`,
          logContext,
          JSON.stringify(error.stack),
        );
      } else {
        this.loggerService.error(
          `Failed to find article gaps :: Message- ${error.message} :: Error name- ${error.name}`,
          logContext,
          JSON.stringify(error.stack),
        );
      }
      throw error;
    }
  }
}
