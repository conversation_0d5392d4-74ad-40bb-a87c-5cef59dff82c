import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ArticleRepository } from "src/api/article/article.repository";
import { Article, ArticleSchema } from "src/api/article/article.schema";
import { CollectionRepository } from "src/api/collection/collection.repository";
import {
  Collection,
  CollectionSchema,
} from "src/api/collection/collection.schema";
import { TagRepository } from "src/api/tag/tag.repository";
import { Tag, TagSchema } from "src/api/tag/tag.schema";
import { DatabaseName } from "src/shared/constants/database-name.constant";
import { TypesenseReindexService } from "./typesense-reindex.service";
import { TypesenseService } from "./typesense.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        { name: Article.name, schema: ArticleSchema },
        { name: Collection.name, schema: CollectionSchema },
        { name: Tag.name, schema: TagSchema },
      ],
      DatabaseName.THENA_HELP_CENTER,
    ),
  ],
  providers: [
    TypesenseService,
    TypesenseReindexService,
    ArticleRepository,
    CollectionRepository,
    TagRepository,
  ],
  exports: [
    TypesenseService,
    TypesenseReindexService,
    ArticleRepository,
    CollectionRepository,
    TagRepository,
  ],
})
export class TypesenseModule {}
