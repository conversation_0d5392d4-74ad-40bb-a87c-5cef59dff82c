import { Injectable } from "@nestjs/common";
import { Types } from "mongoose";
import { ArticleState } from "src/api/article/article.constants";
import { ArticleRepository } from "src/api/article/article.repository";
import { CollectionRepository } from "src/api/collection/collection.repository";
import { TagRepository } from "src/api/tag/tag.repository";
import { LoggerService } from "src/shared/logger/logger.service";
import { stripHtml } from "string-strip-html";
import { TypesenseService } from "./typesense.service";

@Injectable()
export class TypesenseReindexService {
  constructor(
    private readonly articleRepository: ArticleRepository,
    private readonly collectionRepository: CollectionRepository,
    private readonly typesenseService: TypesenseService,
    private readonly tagRepository: TagRepository,
    private readonly loggerService: LoggerService,
  ) {}

  /**
   * Reindex all published articles for a specific organization
   * @param orgId The organization ID
   */
  async reindexArticlesForOrg(
    orgId: string,
  ): Promise<{ success: number; failed: number }> {
    const logContext = `${TypesenseReindexService.name} - ${this.reindexArticlesForOrg.name}`;

    try {
      this.loggerService.log(
        `Starting reindexing articles for organization: ${orgId}`,
        logContext,
      );

      // Get all published articles for this organization
      const articles = await this.articleRepository.find({
        orgId,
        state: ArticleState.PUBLISHED,
        isDeleted: false,
      });

      this.loggerService.log(
        `Found ${articles.length} published articles to reindex`,
        logContext,
      );

      // Early return if no articles to process
      if (articles.length === 0) {
        return { success: 0, failed: 0 };
      }

      // Extract all article IDs and tag IDs for batch fetching
      const articleIds = articles.map((article) => article._id);
      const allTagIds = new Set<string>();

      // Collect all tag IDs from all articles
      articles.forEach((article) => {
        if (article.tags && article.tags.length > 0) {
          article.tags.forEach((tagId) => allTagIds.add(tagId.toString()));
        }
      });

      // Batch fetch all collections for all articles at once
      this.loggerService.log(
        `Batch fetching collections for ${articleIds.length} articles`,
        logContext,
      );
      const allCollections = await this.collectionRepository.find(
        { articles: { $in: articleIds } },
        { helpCenterId: 1, articles: 1, _id: 0 },
      );

      // Create a map of article ID to help center IDs
      const articleToHelpCenterMap = new Map<string, string[]>();
      allCollections.forEach((collection) => {
        const helpCenterId = collection.helpCenterId.toString();
        collection.articles.forEach((articleId) => {
          const articleIdStr = articleId.toString();
          if (!articleToHelpCenterMap.has(articleIdStr)) {
            articleToHelpCenterMap.set(articleIdStr, []);
          }
          articleToHelpCenterMap.get(articleIdStr).push(helpCenterId);
        });
      });

      // Batch fetch all tags for all articles at once
      this.loggerService.log(
        `Batch fetching ${allTagIds.size} tags`,
        logContext,
      );

      const allTags =
        allTagIds.size === 0
          ? []
          : await this.tagRepository.find(
              {
                _id: {
                  $in: Array.from(allTagIds).map(
                    (id) => new Types.ObjectId(id),
                  ),
                },
              },
              { name: 1 },
            );

      // Create a map of tag ID to tag name
      const tagIdToNameMap = new Map<string, string>();
      allTags.forEach((tag) => {
        tagIdToNameMap.set(tag._id.toString(), tag.name);
      });

      let successCount = 0;
      let failedCount = 0;

      // Process each article using the pre-fetched data
      for (const article of articles) {
        try {
          const articleIdStr = article._id.toString();

          // Get help center IDs for this article from the map
          const helpCenterIds = articleToHelpCenterMap.get(articleIdStr) || [];

          // Skip if article doesn't belong to any help center
          if (helpCenterIds.length === 0) {
            this.loggerService.log(
              `Article ${article._id} (${article.title}) doesn't belong to any help center, skipping indexing`,
              logContext,
            );
            continue;
          }

          // Get tag names for this article using the tag ID to name map
          const tagNames = article.tags
            ? article.tags
                .map((tagId) => tagIdToNameMap.get(tagId.toString()))
                .filter((name): name is string => typeof name === "string")
            : [];

          // Convert HTML to plain text for indexing
          const html = article.publishedVersion?.html ?? "";
          const articleText = stripHtml(html, {
            stripTogetherWithTheirContents: [
              "script",
              "style",
              "xml",
              "iframe",
              "img",
            ],
          }).result;

          // Upsert document in Typesense
          await this.typesenseService.indexDocument({
            id: articleIdStr,
            uid: article.uid,
            organization_uid: article.orgId.toString(),
            title: article.title,
            description: article.description || "",
            body: articleText,
            tags: tagNames,
            help_center_ids: helpCenterIds,
            visibility: article.visibility,
          });

          successCount++;

          const processed = successCount + failedCount;
          if (processed % 10 === 0) {
            this.loggerService.log(
              `Progress: ${processed} articles processed`,
              logContext,
            );
          }
        } catch (error) {
          failedCount++;
          this.loggerService.error(
            `Failed to reindex article ${article._id} (${article.title}): ${error.message}`,
            logContext,
            error.stack,
          );
        }
      }

      this.loggerService.log(
        `Reindexing completed for organization ${orgId}. Success: ${successCount}, Failed: ${failedCount}`,
        logContext,
      );

      return { success: successCount, failed: failedCount };
    } catch (error) {
      this.loggerService.error(
        `Error during reindexing for organization ${orgId}: ${error.message}`,
        logContext,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Reindex all published articles across all organizations
   */
  async reindexAllArticles(): Promise<{
    [orgId: string]: { success: number; failed: number };
  }> {
    const logContext = `${TypesenseReindexService.name} - ${this.reindexAllArticles.name}`;

    try {
      this.loggerService.log(
        "Starting reindexing for all organizations",
        logContext,
      );

      // Get distinct organization IDs that have published articles
      const organizations =
        await this.articleRepository.getDistinctOrgIdsWithPublishedArticles();

      this.loggerService.log(
        `Found ${organizations.length} organizations with published articles`,
        logContext,
      );

      const results: { [orgId: string]: { success: number; failed: number } } =
        {};

      // Reindex articles for each organization
      for (const orgId of organizations) {
        try {
          results[orgId] = await this.reindexArticlesForOrg(orgId);
        } catch (error) {
          this.loggerService.error(
            `Failed to reindex articles for organization ${orgId}: ${error.message}`,
            logContext,
            error.stack,
          );
          results[orgId] = { success: 0, failed: 0 };
        }
      }

      this.loggerService.log(
        "Reindexing completed for all organizations",
        logContext,
      );

      return results;
    } catch (error) {
      this.loggerService.error(
        `Error during reindexing for all organizations: ${error.message}`,
        logContext,
        error.stack,
      );
      throw error;
    }
  }
}
