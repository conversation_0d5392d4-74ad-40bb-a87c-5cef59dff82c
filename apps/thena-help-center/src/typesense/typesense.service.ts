import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ObjectId } from "mongodb";
import { ArticleVisibility } from "src/api/article/article.constants";
import { LoggerService } from "src/shared/logger/logger.service";
import * as Typesense from "typesense";
import { IIndexDocument, IUpdateDocument } from "./typesense.interface";

@Injectable()
export class TypesenseService {
  private typesenseClient: Typesense.Client;
  private readonly COLLECTION_NAME = "help_center";

  constructor(
    private readonly configService: ConfigService,
    private readonly loggerService: LoggerService,
  ) {
    this.typesenseClient = new Typesense.Client({
      nodes: [
        {
          host: this.configService.get<string>("typesense.host"),
          port: this.configService.get<number>("typesense.port"),
          protocol: this.configService.get<string>("typesense.protocol"),
        },
      ],
      apiKey: this.configService.get<string>("typesense.apiKey"),
      connectionTimeoutSeconds: 30,
    });
  }

  /**
   * Doc-{@link https://typesense.org/docs/27.1/api/api-keys.html#create-an-api-key Typesense Search API key}
   *
   * Doc-{@link https://typesense.org/docs/27.1/api/api-keys.html#generate-scoped-search-key Typesense Scoped API key}
   */
  async generateKeys(orgId: string) {
    // generate parent search only key
    const parentPrivateKey = await this.typesenseClient.keys().create({
      description: `Search-only key for Help Center ${orgId}`,
      actions: ["documents:search"],
      collections: [this.COLLECTION_NAME],
    });

    const publicKey = this.typesenseClient
      .keys()
      .generateScopedSearchKey(parentPrivateKey.value, {
        filter_by: `visibility:${ArticleVisibility.PUBLIC}`,
      });

    // generate customer search only key
    const customerKey = this.typesenseClient
      .keys()
      .generateScopedSearchKey(parentPrivateKey.value, {
        filter_by: `organization_uid:${orgId} && (visibility:${ArticleVisibility.PUBLIC} || visibility:${ArticleVisibility.CUSTOMER})`,
      });

    // generate private search key scoped to organization
    const privateKey = this.typesenseClient
      .keys()
      .generateScopedSearchKey(parentPrivateKey.value, {
        filter_by: `organization_uid:${orgId}`,
      });

    return {
      privateKey,
      customerKey,
      publicKey,
    };
  }

  async indexDocument(document: IIndexDocument): Promise<any> {
    return this.typesenseClient
      .collections(this.COLLECTION_NAME)
      .documents()
      .upsert(document);
  }

  async updateDocument(
    orgId: string,
    articleId: ObjectId,
    document: IUpdateDocument,
  ) {
    return this.typesenseClient
      .collections(this.COLLECTION_NAME)
      .documents(articleId.toString())
      .update(document);
  }

  async deleteDocument(documentId: string) {
    return this.typesenseClient
      .collections(this.COLLECTION_NAME)
      .documents(documentId)
      .delete();
  }

  async getCollection() {
    try {
      return await this.typesenseClient
        .collections(this.COLLECTION_NAME)
        .retrieve();
    } catch (err) {
      this.loggerService.log(
        `Typesense collection not found or could not be retrieved. Error: ${err?.message || err}`,
      );
      return null;
    }
  }
}
