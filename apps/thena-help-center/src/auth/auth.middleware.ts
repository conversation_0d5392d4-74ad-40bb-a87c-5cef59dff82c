import {
  HttpException,
  HttpStatus,
  Injectable,
  NestMiddleware,
} from "@nestjs/common";
import cookieParser from "cookie-parser";
import { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import { ClsService } from "nestjs-cls";
import { LoggerService } from "src/shared/logger/logger.service";

@Injectable()
export class SessionMiddleware implements NestMiddleware {
  constructor(
    private readonly loggerService: LoggerService,
    private readonly clsService: ClsService,
  ) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Parse cookies
    cookieParser()(req, res, async () => {
      const accessToken = req.cookies["base-access"];
      const orgId = req.headers["x-org-id"];
      const userId = req.headers["x-user-id"];

      if (!accessToken) {
        this.loggerService.error(
          `No access token found`,
          SessionMiddleware.name,
        );
        return next(
          new HttpException("No access token found", HttpStatus.UNAUTHORIZED),
        );
      }

      try {
        const decoded = jwt.decode(accessToken);

        const sessionId = decoded.session_id;
        if (!sessionId) {
          this.loggerService.error(
            "No session ID found in token",
            SessionMiddleware.name,
          );
          return next(
            new HttpException(
              "No session ID found in token",
              HttpStatus.UNAUTHORIZED,
            ),
          );
        }

        req["user"] = decoded;
        req["sessionId"] = sessionId;
        req["userId"] = userId;
        req["orgId"] = orgId;

        if (!req["userId"] || !req["orgId"]) {
          this.loggerService.error(
            "User ID or Organization ID not found in request",
            SessionMiddleware.name,
          );
          return next(
            new HttpException(
              "User ID or Organization ID not found in request",
              HttpStatus.UNAUTHORIZED,
            ),
          );
        }

        // Set the user and organization ID in the CLS context
        this.clsService.set("ORG_ID", `${req["orgId"]}`);
        this.clsService.set("USER_ID", `${req["userId"]}`);

        next();
      } catch (error) {
        this.loggerService.error(
          `Failed to verify token :: Message- ${error.message} :: Error name- ${error.name}`,
          SessionMiddleware.name,
          JSON.stringify(error.stack),
        );
        return next(
          new HttpException(
            "Invalid or expired token",
            HttpStatus.UNAUTHORIZED,
          ),
        );
      }
    });
  }
}
