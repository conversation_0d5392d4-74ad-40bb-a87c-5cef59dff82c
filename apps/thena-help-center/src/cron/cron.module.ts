import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { SettingRepository } from "src/api/settings/setting.repository";
import { Setting, SettingSchema } from "src/api/settings/setting.schema";
import { GeneralQueueProducerModule } from "src/queues/producer/producer.module";
import { DatabaseName } from "src/shared/constants/database-name.constant";
import { TypesenseModule } from "src/typesense/typesense.module";
import { CronService } from "./cron.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [{ name: Setting.name, schema: SettingSchema }],
      DatabaseName.THENA_HELP_CENTER,
    ),
    TypesenseModule,
    GeneralQueueProducerModule,
  ],
  providers: [CronService, SettingRepository],
})
export class CronModule {}
