import { Injectable, OnModuleD<PERSON>roy, OnModuleInit } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Agenda } from "agenda";
import { SettingRepository } from "src/api/settings/setting.repository";
import { Setting } from "src/api/settings/setting.schema";
import { GeneralQueueProducerService } from "src/queues/producer/producer.service";
import { QnaEvents } from "src/queues/queue.constant";
import { LoggerService } from "src/shared/logger/logger.service";
import { TypesenseReindexService } from "src/typesense/typesense-reindex.service";
import { JOB_NAMES } from "./cron.constant";

@Injectable()
export class CronService implements OnModuleInit, OnModuleDestroy {
  private agenda: Agenda;
  // Default to 2 hours lock lifetime for safety
  private readonly DEFAULT_LOCK_LIFETIME_MS = 2 * 60 * 60 * 1000;

  constructor(
    private readonly configService: ConfigService,
    private readonly loggerService: LoggerService,
    private readonly settingRepository: SettingRepository,
    private readonly generalQueueProducerService: GeneralQueueProducerService,
    private readonly typesenseReindexService: TypesenseReindexService,
  ) {}

  async onModuleInit() {
    this.agenda = new Agenda({
      db: {
        address: this.configService.get<string>("database.mongo.uri"),
        collection: "agenda",
      },
      processEvery: "1 minute",
      maxConcurrency: 20,
    });

    // Clear any stuck locks on startup
    await this.clearStalledJobs();

    await this.agenda.start();
    await this.defineJobs();
  }

  // Clear any stalled jobs on startup
  private async clearStalledJobs() {
    try {
      // Access the MongoDB collection directly
      if (!("_mdb" in this.agenda)) {
        this.loggerService.warn(
          "Agenda internal handle not present – stalled-job sweep skipped",
        );
        return;
      }
      const db = (this.agenda as any)._mdb.db;
      const collection = db.collection("agenda");

      // Find and unlock any locked jobs
      const result = await collection.updateMany(
        { lockedAt: {
          $lte: new Date(Date.now() - this.DEFAULT_LOCK_LIFETIME_MS),
        } },
        { $set: { lockedAt: null } },
      );

      this.loggerService.log(
        `Cleared ${result.modifiedCount} stalled jobs on startup`,
      );
    } catch (error) {
      this.loggerService.error(
        `Failed to clear stalled jobs: ${error.message}`,
        `${CronService.name} - clearStalledJobs`,
        error.stack,
      );
    }
  }

  async onModuleDestroy() {
    await this.agenda.stop();
  }

  private async defineJobs() {
    this.agenda.define(JOB_NAMES.QNA_JOB, async (job, done) => {
      this.loggerService.log(
        `QNA job started for updating qna file in vectors, creating new qnas from closed request in last 24 hours and finding gaps between articles and qnas`,
      );

      try {
        const promises = [];

        const settings = await this.settingRepository.find(
          { isQnaAutoGenerationActive: true },
          { orgId: 1 },
        );
        settings.forEach((setting: Setting) => {
          this.loggerService.log(`Job queued for orgId- ${setting.orgId}`);
          promises.push(
            this.generalQueueProducerService.sendMessage(QnaEvents.UPDATE_QNA, {
              orgId: setting.orgId,
            }),
          );
        });

        await Promise.all(promises);

        this.loggerService.log(`QNA job finished`);
        done();
      } catch (error) {
        this.loggerService.error(
          `Error in QNA job: ${error.message}`,
          `${CronService.name} - ${JOB_NAMES.QNA_JOB}`,
          error.stack,
        );
        done();
      }
    });

    this.agenda.define(
      JOB_NAMES.REINDEX_ARTICLES_JOB,
      { lockLifetime: this.DEFAULT_LOCK_LIFETIME_MS } as any,
      async (job, done) => {
        const startTime = Date.now();
        this.loggerService.log(
          `Reindex articles job started at ${new Date().toISOString()} with lock lifetime of ${this.DEFAULT_LOCK_LIFETIME_MS / 60000} minutes`,
        );

        try {
          const results =
            await this.typesenseReindexService.reindexAllArticles();

          let totalSuccess = 0;
          let totalFailed = 0;

          Object.entries(results).forEach(([orgId, result]) => {
            totalSuccess += result.success;
            totalFailed += result.failed;
            this.loggerService.log(
              `Organization ${orgId}: ${result.success} articles indexed successfully, ${result.failed} failed`,
            );
          });

          const duration = (Date.now() - startTime) / 1000;
          this.loggerService.log(
            `Reindex articles job finished in ${duration}s. Total: ${totalSuccess} successful, ${totalFailed} failed`,
          );
          done();
        } catch (error) {
          this.loggerService.error(
            `Error in reindex articles job: ${error.message}`,
            `${CronService.name} - ${JOB_NAMES.REINDEX_ARTICLES_JOB}`,
            error.stack,
          );
          done();
        }
      },
    );

    await this.agenda.every("30 1 * * *", JOB_NAMES.QNA_JOB);
    await this.agenda.every("0 19 * * *", JOB_NAMES.REINDEX_ARTICLES_JOB);
  }
}
