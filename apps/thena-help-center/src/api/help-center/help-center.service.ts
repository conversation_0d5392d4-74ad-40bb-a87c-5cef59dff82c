import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { Types } from "mongoose";
import OpenAI from "openai";
import { CloudfrontService } from "src/cloudfront/cloudfront.service";
import {
  AssistantPrompts,
  AssistantType,
} from "src/openai/assistant/assistant.constant";
import { AssistantService } from "src/openai/assistant/assistant.service";
import { VectorType } from "src/openai/vector/vector.constant";
import { VectorService } from "src/openai/vector/vector.service";
import { UtilityHelper } from "src/shared/helpers/utility.helpers";
import { LoggerService } from "src/shared/logger/logger.service";
import { TypesenseService } from "src/typesense/typesense.service";
import { VercelService } from "src/vercel/vercel.service";
import { OrganizationGrpcClient } from "../../organization/grpc/organization.grpc-client";
import { CollectionRepository } from "../collection/collection.repository";
import { SettingRepository } from "../settings/setting.repository";
import { DomainStatusDto } from "./dtos/help-center.dto";
import { ErrorMessages } from "./help-center.constant";
import { HelpCenterRepository } from "./help-center.repository";
import { HelpCenter } from "./help-center.schema";
import {
  ICreateHelpCenter,
  ISaveCustomDomain,
  IUpdateHelpCenter,
  IUploadImage,
} from "./interfaces/help-center.interface";

@Injectable()
export class HelpCenterService {
  constructor(
    private readonly helpCenterRepository: HelpCenterRepository,
    private readonly typesenseService: TypesenseService,
    private readonly loggerService: LoggerService,
    private readonly assistantService: AssistantService,
    private readonly vectorService: VectorService,
    private readonly settingRepository: SettingRepository,
    private readonly cloudfrontService: CloudfrontService,
    private readonly vercelService: VercelService,
    private readonly collectionRepository: CollectionRepository,
    private readonly organizationGrpcClient: OrganizationGrpcClient,
  ) {}

  async create(
    createHelpCenterDto: ICreateHelpCenter,
    userId: string,
    orgId: string,
  ): Promise<HelpCenter> {
    const logContext = `${HelpCenterService.name} - ${this.create.name}`;
    try {
      // Check if this is the first help center for this org
      const isDefaultHelpCenter = (await this.helpCenterRepository.findOne(
        { orgId },
        { _id: 1 },
      ))
        ? false
        : true;

      // Extract properties from the DTO
      const { name, logo } = createHelpCenterDto;

      const domains = await this.getOrganizationDomains(orgId);

      // If you need to clean a specific domain, select one first
      const cleanedDomain = domains?.[0] // or whichever domain you want to use
        ? domains[0].replace(/[^a-zA-Z0-9\s-]/g, "").replace(/\s+/g, "-")
        : "";

      let defaultDomain = "";

      if (cleanedDomain) {
        const isDomainTaken = await this.isDomainTaken(
          cleanedDomain,
          domains?.[0],
        );

        defaultDomain = isDomainTaken
          ? await this.generateUniqueDefaultDomain(cleanedDomain)
          : cleanedDomain;
      } else {
        defaultDomain = await this.generateUniqueDefaultDomain(cleanedDomain);
      }

      // Create a new instance of the HelpCenter document
      const helpCenterData = {
        orgId,
        name,
        defaultDomain,
        logo,
        createdBy: userId,
        orgDomain: domains?.[0],
      };

      // Save the new HelpCenter document to the database using the repository
      const helpcenter = await this.helpCenterRepository.create(helpCenterData);

      if (isDefaultHelpCenter) {
        // generate scoped api keys for typesense
        const keys = await this.typesenseService.generateKeys(orgId.toString());

        // if this is first helpcenter of the org create setting entry for the first time
        await this.settingRepository.findOneAndUpsert(
          {
            orgId,
          },
          { defaultHelpCenterId: helpcenter._id, typesenseKeys: keys, orgId },
        );
      }

      // create and attach assistants/vectors
      await this.setupAssistantsAndVectors(
        helpcenter._id,
        orgId,
        isDefaultHelpCenter,
      );

      this.loggerService.log(`Help center successfully created- ${helpcenter}`);

      return helpcenter;
    } catch (error) {
      this.loggerService.error(
        `Failed to create help center :: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        ErrorMessages.HELP_CENTER_CREATION_FAILED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrganizationDomains(orgUid: string): Promise<string[]> {
    try {
      const response = await this.organizationGrpcClient.getDomains(orgUid, {
        orgId: orgUid,
        userId: orgUid,
      });
      return response.domains;
    } catch (error) {
      this.loggerService.error("Error getting organization domains", error);
      throw new HttpException(
        "Failed to get organization domains",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAll(userId: string, orgId: string): Promise<HelpCenter[]> {
    const logContext = `${HelpCenterService.name} - ${this.getAll.name}`;
    try {
      const getAllHelpCenters = await this.helpCenterRepository.find({
        orgId,
      });

      this.loggerService.log(`ALl help centers fetched successfully`);

      return getAllHelpCenters;
    } catch (error) {
      this.loggerService.error(
        `Failed to fetch all help center :: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        ErrorMessages.FAILED_TO_FETCH_ALL_HELP_CENTERS,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async get(
    helpCenterId: Types.ObjectId,
    userId: string,
    orgId: string,
  ): Promise<HelpCenter | []> {
    const logContext = `${HelpCenterService.name} - ${this.get.name}`;
    try {
      this.loggerService.log(`Fetching help center for id- ${helpCenterId}`);
      const getHelpCenter = await this.helpCenterRepository.findOne({
        _id: helpCenterId,
        orgId,
      });

      if (!getHelpCenter) {
        this.loggerService.log(`No result found for id- ${helpCenterId}`);
        return [];
      }

      return getHelpCenter;
    } catch (error) {
      this.loggerService.error(
        `Failed to fetch help center :: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        ErrorMessages.FAILED_TO_FETCH_ALL_HELP_CENTERS,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async update(
    helpCenterId: Types.ObjectId,
    updateHelpCenterDto: IUpdateHelpCenter,
    userId: string,
    orgId: string,
  ): Promise<HelpCenter> {
    const logContext = `${HelpCenterService.name} - ${this.update.name}`;
    try {
      const getHelpCenter = await this.helpCenterRepository.findOne({
        _id: helpCenterId,
        orgId,
      });

      if (!getHelpCenter) {
        throw new HttpException(
          ErrorMessages.HELP_CENTER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      delete updateHelpCenterDto["helpCenterId"];
      const updatedHelpCenter =
        await this.helpCenterRepository.findOneAndUpdate(
          { _id: helpCenterId },
          updateHelpCenterDto,
        );

      return updatedHelpCenter;
    } catch (error) {
      this.loggerService.error(
        `Failed to update help center :: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        ErrorMessages.UPDATE_HELP_CENTER_FAILED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async delete(helpCenterId: Types.ObjectId, orgId: string) {
    const logContext = `${HelpCenterService.name} - ${this.get.name}`;
    try {
      const getHelpCenter = await this.helpCenterRepository.findOne({
        _id: helpCenterId,
        orgId,
      });

      if (!getHelpCenter) {
        throw new HttpException(
          ErrorMessages.HELP_CENTER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }
      // 1. delete the help-center
      await this.helpCenterRepository.delete({
        orgId,
        _id: helpCenterId,
      });

      // 2. Delete the collections associated to the help-center
      await this.collectionRepository.deleteMany({ helpCenterId, orgId });

      return;
    } catch (error) {
      this.loggerService.error(
        `Failed to delete the helpcenter :: helpcenterId: ${helpCenterId} :: OrgId: ${orgId} :: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );
      throw new HttpException(
        ErrorMessages.HELP_CENTER_DELETE_FAILED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async setupAssistantsAndVectors(
    helpCenterId: Types.ObjectId,
    orgId: string,
    isDefaultHelpCenter: boolean,
  ): Promise<void> {
    const logContext = `${HelpCenterService.name} - ${this.setupAssistantsAndVectors.name}`;
    try {
      // create internal assistants/vector if the helpcenter is default one
      if (isDefaultHelpCenter) {
        this.loggerService.log(`Creating internal assistants and vectors`);

        const qnaGeneratorAssistantPromise = this.assistantService.create(
          `${orgId}-${AssistantType.QNA_GENERATOR}`,
          AssistantPrompts.QNA_GENERATOR_PROMPT,
        );

        const articleGeneratorAssistantPromise = this.assistantService.create(
          `${orgId}-${AssistantType.ARTICLE_GENERATOR}`,
          AssistantPrompts.ARTICLE_GENERATOR_PROMPT,
        );

        const internalAssistantsAndVector = await Promise.all([
          qnaGeneratorAssistantPromise,
          articleGeneratorAssistantPromise,
          this.vectorService.create(`${orgId}-${VectorType.QNA}`),
          this.vectorService.create(`${orgId}-${VectorType.ARTICLE}`),
        ]);

        this.loggerService.log(`Internal assistants and vectors got created`);

        // attach internal vector to qna and article generator assistant
        await Promise.all([
          this.assistantService.attachVector(
            internalAssistantsAndVector[0].id,
            [internalAssistantsAndVector[2].id],
          ),
          this.assistantService.attachVector(
            internalAssistantsAndVector[1].id,
            [internalAssistantsAndVector[3].id],
          ),
        ]);

        this.loggerService.log(
          `Internal vector got attached to internal assistants`,
        );

        // store internal assistants in thena-core database
        await this.settingRepository.findOneAndUpdate(
          { orgId: orgId },
          {
            "aiData.assistants": [
              {
                name: internalAssistantsAndVector[0].name,
                assistantId: internalAssistantsAndVector[0].id,
                assistantType: AssistantType.QNA_GENERATOR,
                vectorId: internalAssistantsAndVector[2].id,
              },
              {
                name: internalAssistantsAndVector[1].name,
                assistantId: internalAssistantsAndVector[1].id,
                assistantType: AssistantType.ARTICLE_GENERATOR,
                vectorId: internalAssistantsAndVector[3].id,
              },
            ],
          },
        );
        this.loggerService.log(
          `Internal assistants and vector stored into organization collection`,
        );
      }

      // create external assistants promises
      const externalAsistantsPromises = [
        AssistantType.PUBLIC,
        AssistantType.CUSTOMER,
        AssistantType.PRIVATE,
      ].map((name: string) =>
        this.assistantService.create(
          `${helpCenterId}-${name}`,
          AssistantPrompts.QUERY_PROMPT,
        ),
      );

      // create external vectors promises
      const externalVectorsPromises = [
        VectorType.PUBLIC,
        VectorType.CUSTOMER,
        VectorType.PRIVATE,
      ].map((name: string) =>
        this.vectorService.create(`${helpCenterId}-${name}`),
      );

      const externalAssistantsAndVectors = await Promise.all([
        ...externalAsistantsPromises,
        ...externalVectorsPromises,
      ]);

      this.loggerService.log(`External assistants and vectors got created`);

      await Promise.all([
        // attach public level vector to public level assistant
        this.assistantService.attachVector(externalAssistantsAndVectors[0].id, [
          externalAssistantsAndVectors[3].id,
        ]),
        // attach customer level vector to customer level assistant
        this.assistantService.attachVector(externalAssistantsAndVectors[1].id, [
          externalAssistantsAndVectors[4].id,
        ]),
        // attach private level vector to private level assistant
        this.assistantService.attachVector(externalAssistantsAndVectors[2].id, [
          externalAssistantsAndVectors[5].id,
        ]),
      ]);

      this.loggerService.log(
        `External vector got attached to internal assistants`,
      );

      // prepare assistant data to store in DB
      const assistantData = [
        {
          name: externalAssistantsAndVectors[0].name,
          assistantId: externalAssistantsAndVectors[0].id,
          assistantType: AssistantType.PUBLIC,
          vectorId: externalAssistantsAndVectors[3].id,
        },
        {
          name: externalAssistantsAndVectors[1].name,
          assistantId: externalAssistantsAndVectors[1].id,
          assistantType: AssistantType.CUSTOMER,
          vectorId: externalAssistantsAndVectors[4].id,
        },
        {
          name: externalAssistantsAndVectors[2].name,
          assistantId: externalAssistantsAndVectors[2].id,
          assistantType: AssistantType.PRIVATE,
          vectorId: externalAssistantsAndVectors[5].id,
        },
      ];

      // create assistants in database
      await this.helpCenterRepository.findOneAndUpdate(
        { _id: helpCenterId },
        {
          "aiData.assistants": assistantData,
        },
      );

      this.loggerService.log(
        `External assistants and vector stored into organization collection`,
      );
    } catch (error) {
      if (error instanceof OpenAI.APIError) {
        this.loggerService.error(
          `Failed to create and attach assistant and vectors :: Message- ${error.message} 
          :: Error status- ${error.status} :: Error type- ${error.type} :: Error code- ${error.code}`,
          logContext,
          JSON.stringify(error.stack),
        );
      } else {
        this.loggerService.error(
          `Failed to create and attach assistant and vectors :: Message- ${error.message} :: Error name- ${error.name}`,
          logContext,
          JSON.stringify(error.stack),
        );
      }
      throw new HttpException(
        ErrorMessages.HELP_CENTER_CREATION_FAILED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async generateUniqueDefaultDomain(teamHandle: string): Promise<string> {
    let domain: string;
    let existingHelpCenter: HelpCenter;

    do {
      const uid = UtilityHelper.generateUniqueId();
      domain = teamHandle ? `${teamHandle}-${uid}` : uid.toString();
      existingHelpCenter = await this.helpCenterRepository.findOne(
        { defaultDomain: domain },
        { _id: 1 },
      );
    } while (existingHelpCenter);

    return domain;
  }

  async uploadImage(
    file: Express.Multer.File,
    uploadImageDto: IUploadImage,
    userId: string,
    orgId: string,
  ) {
    const logContext = `${HelpCenterService.name} - ${this.uploadImage.name}`;
    try {
      const { type } = uploadImageDto;
      const uploadData = await this.cloudfrontService.uploadPublicImage(
        orgId.toString(),
        file,
        type,
      );

      return uploadData;
    } catch (error) {
      this.loggerService.error(
        `Failed to upload image :: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        ErrorMessages.IMAGE_UPLOAD_FAILED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async handleDomainChange(
    oldDomain: string | null,
    newDomain: string | null,
  ): Promise<void> {
    // No domain change
    if (oldDomain === newDomain) return;

    // First time domain setup
    if (!oldDomain && newDomain) {
      this.loggerService.log(
        "Setting up custom domain for the first time:",
        newDomain,
      );
      await this.vercelService.createNewdomain({ domain: newDomain });
      return;
    }

    // Domain update
    if (oldDomain && newDomain) {
      this.loggerService.log(
        `Updating custom domain from ${oldDomain} to: ${newDomain}`,
      );
      await this.vercelService.removeDomain({ domain: oldDomain });
      await this.vercelService.createNewdomain({ domain: newDomain });
      return;
    }

    // Domain removal
    if (oldDomain && !newDomain) {
      this.loggerService.log("Removing custom domain:", oldDomain);
      await this.vercelService.removeDomain({ domain: oldDomain });
    }
  }

  async saveCustomDomain(
    helpCenterId: Types.ObjectId,
    saveCustomDomainDto: ISaveCustomDomain,
    userId: string,
    orgId: string,
  ) {
    const logContext = `${HelpCenterService.name} - ${this.saveCustomDomain.name}`;
    try {
      // Extract properties from the DTO
      const { customDomain: newDomain } = saveCustomDomainDto;

      const getHelpCenter = await this.helpCenterRepository.findOne({
        _id: helpCenterId,
        orgId,
      });

      if (!getHelpCenter) {
        throw new HttpException(
          ErrorMessages.HELP_CENTER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      const domains = await this.getOrganizationDomains(orgId);
      const orgDomain = domains?.[0];

      const existingDomain = await this.isDomainTaken(newDomain, orgDomain);

      if (existingDomain) {
        throw new HttpException(
          ErrorMessages.DEFAULT_DOMAIN_ALREADY_ACQUIRED,
          HttpStatus.CONFLICT,
        );
      }

      const oldDomain = getHelpCenter.customDomain;
      await this.handleDomainChange(oldDomain, newDomain);

      const updatedHelpCenter =
        await this.helpCenterRepository.findOneAndUpdate(
          { _id: helpCenterId },
          saveCustomDomainDto,
        );

      return updatedHelpCenter;
    } catch (error) {
      this.loggerService.error(
        `Failed to update custom domain in helpcenter ${helpCenterId}:: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        ErrorMessages.UPDATE_HELP_CENTER_FAILED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async checkDomainStatus(
    helpCenterId: Types.ObjectId,
    userId: string,
    orgId: string,
    { domain }: DomainStatusDto,
  ) {
    const logContext = `${HelpCenterService.name} - ${this.checkDomainStatus.name}`;
    try {
      const getHelpCenter = await this.helpCenterRepository.findOne({
        _id: helpCenterId,
        orgId,
      });

      if (!getHelpCenter) {
        throw new HttpException(
          ErrorMessages.HELP_CENTER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      const { data } = await this.vercelService.checkDomainStatus(domain);

      return {
        ...data,
      };
    } catch (error) {
      this.loggerService.error(
        `Failed to get status for domain ${domain} :: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        ErrorMessages.DOMAIN_STATUS_CHECK_FAILED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getDomainInfo(
    helpCenterId: Types.ObjectId,
    userId: string,
    orgId: string,
    { domain }: DomainStatusDto,
  ) {
    const logContext = `${HelpCenterService.name} - ${this.checkDomainStatus.name}`;
    try {
      const getHelpCenter = await this.helpCenterRepository.findOne({
        _id: helpCenterId,
        orgId,
      });

      if (!getHelpCenter) {
        throw new HttpException(
          ErrorMessages.HELP_CENTER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      const { data } = await this.vercelService.getDomainInfo(domain);

      return {
        ...data,
      };
    } catch (error) {
      this.loggerService.error(
        `Failed to get domain info ${domain} :: Message- ${error.message} :: Error name- ${error.name}`,
        logContext,
        JSON.stringify(error.stack),
      );

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        ErrorMessages.DOMAIN_INFO_CHECK_FAILED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async isDomainTaken(domain: string, orgDomain: string): Promise<boolean> {
    const existingHelpCenter =
      await this.helpCenterRepository.findByDefaultDomainAndOrgDomain(
        domain,
        orgDomain,
      );

    return !!existingHelpCenter;
  }
}
