import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { DatabaseName } from "src/shared/constants/database-name.constant";
import { BaseRepository } from "src/shared/repository/base.repository";
import { ArticleState } from "./article.constants";
import { Article } from "./article.schema";
@Injectable()
export class ArticleRepository extends BaseRepository<Article> {
  constructor(
    @InjectModel(Article.name, DatabaseName.THENA_HELP_CENTER)
    private readonly articleModel: Model<Article>,
  ) {
    super(articleModel);
  }

  /**
   * Get distinct organization IDs that have published articles
   */
  async getDistinctOrgIdsWithPublishedArticles(): Promise<string[]> {
    const ids = await this.articleModel.distinct("orgId", {
      state: ArticleState.PUBLISHED,
      isDeleted: false,
    });

    return ids.map((id: string | Types.ObjectId) => id.toString());
  }

  async getArticleAlongWithParentCollections(
    uid: number,
    helpCenterId: Types.ObjectId,
    orgId: string,
    state: ArticleState.PUBLISHED,
  ) {
    const pipeline = [
      {
        $match: { uid, state, orgId },
      },
      {
        $lookup: {
          from: "collections",
          localField: "_id",
          foreignField: "articles",
          as: "collections",
          pipeline: [
            {
              $match: {
                helpCenterId,
                orgId,
              },
            },
            {
              $project: {
                _id: 1,
              },
            },
          ],
        },
      },
      {
        $graphLookup: {
          from: "collections",
          startWith: "$collections._id",
          connectFromField: "parentId",
          connectToField: "_id",
          as: "parentCollections",
          depthField: "depth",
        },
      },
      {
        $project: {
          _id: 1,
          orgId: 1,
          uid: 1,
          title: 1,
          description: 1,
          slug: 1,
          parentCollections: 1,
          authors: 1,
          content: "$publishedVersion.html",
          publishedAt: "$publishedVersion.publishedAt",
          createdAt: 1,
          updatedAt: 1,
          meta: 1,
          tags: 1,
        },
      },
    ];
    const results = await this.articleModel.aggregate(pipeline);
    return results[0];
  }

  async getAllArticlesOfOrg(orgId: string) {
    const pipeline = [
      {
        $match: {
          orgId,
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: "collections",
          localField: "_id",
          foreignField: "articles",
          as: "collections",
        },
      },
      {
        $addFields: {
          collections: {
            $map: {
              input: "$collections",
              as: "collection",
              in: "$$collection._id",
            },
          },
          helpCenters: {
            $map: {
              input: "$collections",
              as: "collection",
              in: "$$collection.helpCenterId",
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          title: 1,
          uid: 1,
          description: 1,
          slug: 1,
          tags: 1,
          collections: 1,
          helpCenters: 1,
          authors: 1,
          createdAt: 1,
          createdBy: 1,
          updatedAt: 1,
          state: 1,
          visibility: 1,
          lastEditedBy: 1,
          lastEditedAt: 1,
          meta: 1,
        },
      },
    ];
    const results = await this.articleModel.aggregate(pipeline);
    return results;
  }

  async getArticle(orgId: string, articleId: Types.ObjectId) {
    const pipeline = [
      {
        $match: {
          orgId,
          _id: articleId,
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: "collections",
          localField: "_id",
          foreignField: "articles",
          as: "collections",
        },
      },
      {
        $addFields: {
          collections: {
            $map: {
              input: "$collections",
              as: "collection",
              in: "$$collection._id",
            },
          },
          helpCenters: {
            $map: {
              input: "$collections",
              as: "collection",
              in: "$$collection.helpCenterId",
            },
          },
        },
      },
      {
        $project: {
          collections: 0,
        },
      },
    ];
    const results = await this.articleModel.aggregate(pipeline);
    return results[0];
  }
}
