---
title: 'All entries'
description: 'Complete history of updates and changes to the Thena platform.'
---

## 💬 June 3rd, 2025

<img src="/images/docs/Auto-responder.webp" alt="Auto-responder interface" />

### Major update: Introducing Auto-responder

The auto-responder is the most advanced multi-channel response automation built for modern support teams. It gives you precise control over when, how, and where customers receive automatic replies—whether during holidays, after hours, or when assigned agents are unavailable.

[Learn more →](/guides/ticketing/auto-responder)

### Key features:

- **Multi-channel support**: Automatically replies on the original channel—Slack, MS Teams, or email—maintaining a consistent experience.
- **Flexible triggers**: Responds to new tickets or incoming messages on existing tickets.
- **Intelligent conditions**: Rules can be triggered during holidays, outside business hours, or when agents are unavailable.
- **Advanced filtering**: Target responses based on ticket properties, tags, or customer information.
- **Branded messaging**: Messages are sent by a bot named after your organization with its logo.
- **Rich text formatting**: Create professional responses with bold, italics, and lists for better readability.

<Tip>
Auto-responders help set proper expectations with customers while reducing follow-up inquiries and improving overall satisfaction.
</Tip>

*The new Thena is currently in beta, with access limited to select customers. If you're an existing customer interested in exploring the new platform, please request beta access. General availability release is coming soon.*

---

## 🤖 May 22nd, 2025

<img src="/images/docs/MCP-Final.webp" alt="Thena MCP Server" />

### Major update: Introducing Thena MCP server

Your AI models and agents can now access your Thena data in a simple and secure way through our official MCP server. This integration follows the authenticated remote [MCP spec](https://modelcontextprotocol.io/specification/2025-03-26), enabling seamless connection between AI assistants and your Thena platform data.

- **Native AI assistant integration**: Connect directly as a new integration in Claude and other AI assistants.
- **Code editor support**: Works with Cursor, Windsurf, Zed, and other clients using the [mcp-remote](https://github.com/geelen/mcp-remote) module.
- **Secure OAuth authentication**: Organization-level authentication ensures your data remains secure.
- **Comprehensive tool suite**: Access and manage tickets, teams, accounts, and customer contacts.

[Learn more →](https://docs.thena.ai/api-reference/thena-mcp-server)

### Integration highlights:

- **Tickets management**: Create, update, and search tickets directly from your AI assistant.
- **Team operations**: Manage team settings and assignments without switching contexts.
- **Account management**: Retrieve account information and activities for better customer context.
- **Contact management**: Access customer contact information to personalize interactions.

*The new Thena is currently in beta, with access limited to select customers. If you're an existing customer interested in exploring the new platform, please request beta access. General availability release is coming soon.*

<Tip>
The MCP server integration enables AI assistants to become true members of your support team, with access to the same data and tools as human agents.
</Tip>

---

## 🔗 May 10th, 2025

### Major update: Internal threads for team collaboration

Internal threads in Thena give your team a focused space to collaborate on a customer ticket without disrupting the external conversation. You can keep it fully internal or optionally link it to a Slack channel for real-time collaboration.

- **Private team discussions**: Create internal conversations that are only visible to your team—customers never see them.
- **Slack integration**: Connect threads to Slack channels for real-time collaboration with bi-directional sync.
- **Rich media support**: Share files, images, and formatted text directly in threads.
- **Multiple threads per ticket**: Create separate threads for different aspects of a ticket.

Internal threads help teams collaborate efficiently across departments, share knowledge, coordinate escalations, and document important decisions—all without cluttering customer-facing communications.

[Learn more →](https://docs.thena.ai/guides/ticketing/internal-threads)

### Update: Emoji actions for faster workflows

Emoji actions allow your team to perform common ticket operations with a simple emoji reaction, saving time and streamlining your workflow.

- **One-click operations**: Add specific emoji reactions to trigger actions automatically.
- **Default actions**: Use built-in emoji shortcuts for changing ticket status with a simple reaction.
- **Customizable workflows**: Create your own emoji actions with custom triggers and operations.

[Learn more →](https://docs.thena.ai/guides/ticketing/emoji-actions)

*The new Thena is currently in beta, with access limited to select customers. If you're an existing customer interested in exploring the new platform, please request beta access. General availability release is coming soon.*

<Tip>
Both internal threads and emoji actions integrate directly with Slack, allowing your team to collaborate efficiently regardless of which platform they prefer to use.
</Tip>

---

## ⚡ May 1st, 2025

<img src="/images/docs/Apis-new.webp" alt="Thena API Experience" />

### Major update: Advanced APIs release

We're excited to announce the release of our comprehensive API suite, organized into three main sections to serve distinct purposes in the Thena ecosystem:

- **Platform APIs**: Core infrastructure services including authentication, SLA management, workflow orchestration, ticketing, accounts, teams, tags, forms, comments, and more.

- **App Platform APIs**: Create, manage, and distribute custom applications within the Thena ecosystem. Includes app creation, installation, uninstallation, and webhook handling.

- **Workflows APIs**: Automate business processes and orchestrate workflows across the Thena platform. Includes workflow creation, execution, event handling, and activity/task management.

All APIs require authentication using an **x-api-key** header. API keys can be generated from **Dashboard → Organization Settings → Security and Access**.

[Explore the API reference →](https://docs.thena.ai/api-reference/introduction)

### Other updates:

- **Enhanced developer documentation**: Added comprehensive guides, tutorials, and code samples to help developers get started with our APIs.
- **API Explorer tool**: Launched an interactive API testing tool that allows developers to experiment with API calls directly from the documentation.
- **Client libraries**: Released official client libraries for JavaScript, Python, and Ruby to simplify API integration.
- **Webhook improvements**: Enhanced webhook delivery with retry logic and detailed delivery logs.

*The new Thena is currently in beta, with access limited to select customers. If you're an existing customer interested in exploring the new platform, please request beta access. General availability release is coming soon.*

<Tip>
API keys should be kept secure and never exposed in client-side code. Use server-side proxies for client applications that need to access Thena APIs.
</Tip>

---

## 🏢 April 24th, 2025

<img src="/images/docs/Accounts.webp" alt="Thena Accounts Experience" />

### Major update: A new Accounts experience

Thena now brings all your customer accounts into a single, real-time, intelligent view — no manual entry, no switching tools.

- **Unified accounts table**: View, sort, filter, and edit customer accounts with a powerful spreadsheet-like interface.
- **Automated account creation**: Accounts are automatically built from Slack conversations, emails, CRM, and API integrations.
- **Account detail view**: See linked tickets, contacts, notes, tasks, and activities — all in one sidebar for a complete 360° view.
- **Centralized customer intelligence**: Move faster across success, support, solutions, and leadership teams with shared context and insights.

[Learn more →](https://docs.thena.ai/guides/accounts/accounts-view)

### Other updates:

- **Account tasks**: Track and manage customer-related tasks linked to specific accounts — keeping teams aligned on next steps. [Learn more →](https://docs.thena.ai/guides/accounts/tasks)
- **Account notes**: Capture and organize customer notes tied to accounts — accessible to everyone working with that customer. [Learn more →](https://docs.thena.ai/guides/accounts/notes)
- **Account activity**: View a real-time activity feed for every customer — conversations, ticket updates, task completions, and more. [Learn more →](https://docs.thena.ai/guides/accounts/activity)
- **Custom account fields**: Add your own fields to track customer-specific metadata — from industry to renewal dates to custom tags. [Learn more →](https://docs.thena.ai/guides/accounts/account-fields)

*The new Thena is currently in beta, with access limited to select customers. If you're an existing customer interested in exploring the new platform, please request beta access. General availability release is coming soon.*

---

## 🔔 April 17th, 2025

<img src="/images/docs/Notifications.webp" alt="Thena Notifications" />

### Major update: Smarter, multi-channel notifications

Thena now delivers a modern, flexible notification system across Slack, Email, Inbox, and In-App Toasts — fully customizable to how you work.

- Choose where you want to receive updates: Slack, Email, Inbox, or lightweight in-app Toasts.
- Configure exactly which events trigger notifications — customized per channel.
- Stay on top of ticket updates, SLA breaches, CSAT responses, and internal thread activity.
- Batch notifications intelligently to stay informed without noise.

[Learn more →](https://docs.thena.ai/guides/preferences/notifications)

### Other updates:

- **Multiple internal threads**: Start and manage private conversations inside a single ticket in sync with Slack. [Learn more →](https://docs.thena.ai/guides/ticketing/internal-threads)
- **Customizable themes**: Personalize Thena with light or dark modes to suit your working style. [Learn more →](https://docs.thena.ai/guides/preferences/themes)

*The new Thena is currently in beta, with access limited to select customers. If you're an existing customer interested in exploring the new platform, please request beta access. General availability release is coming soon.*

---

## 🚀 April 10th, 2025

<img src="/images/docs/Thena launch (1).webp" alt="Thena Platform Launch" />

Thena is built for how modern teams work today — helping them support, serve, and manage high-value customers through a flexible, AI-native platform.

It brings conversations, ticketing, customer data, and automation together across Slack, email, and web — designed to work at speed and scale without adding complexity.

With Thena, teams can:

- Run multi-channel, multi-team, and multi-group operations out of the box.
- Manage accounts, tickets, tasks, and track CSAT in one place.
- Build custom AI agents and set up proactive workflows tailored to each team's needs.
- Create knowledge bases and help centers without relying on extra tools.
- Collaborate across teams without losing structure or visibility.

Each team can set up its own flows, automations, and AI — while staying connected within the larger system.
Thena gives teams the flexibility to move independently, and the structure to operate together — without the usual friction.

<Callout emoji="✨">
This isn't a support tool refresh. It's the modular, AI-native infrastructure modern companies will build on for the next decade.
</Callout>

### What's new

- **Powerful APIs** to support complex B2B workflows.
- **New core entities**: Organization, Account, Contact, Ticket, Team, and Group.
- **Embedded ticketing concepts**: SLAs, routing, working hours, and ticket fields.
- **Flexible team structure**: Create teams and groups to match your organization.
- **Unified customer view**: Accounts, contacts, tickets, and activity in one place.
- **Multi-channel support**: Slack, email, web chat, and more.
- **AI-native platform**: Build custom AI agents for your specific needs.
- **Knowledge base**: Create and manage help centers and documentation.
- **Workflow automation**: Set up proactive workflows and automations.
- **Reporting and analytics**: Track performance and customer satisfaction.

*The new Thena is currently in beta, with access limited to select customers. If you're an existing customer interested in exploring the new platform, please request beta access. General availability release is coming soon.*
