---
title: '💬 June 3rd, 2025'
description: 'Introducing Auto-responder: Advanced multi-channel response automation.'
---

<img src="/images/docs/Auto-responder.webp" alt="Auto-responder interface" />

## Major update: Introducing Auto-responder

The auto-responder is the most advanced multi-channel response automation built for modern support teams. It gives you precise control over when, how, and where customers receive automatic replies—whether during holidays, after hours, or when assigned agents are unavailable.

[Learn more →](/guides/ticketing/auto-responder)

## Key features:

- **Multi-channel support**: Automatically replies on the original channel—Slack, MS Teams, or email—maintaining a consistent experience.
- **Flexible triggers**: Responds to new tickets or incoming messages on existing tickets.
- **Intelligent conditions**: Rules can be triggered during holidays, outside business hours, or when agents are unavailable.
- **Advanced filtering**: Target responses based on ticket properties, tags, or customer information.
- **Branded messaging**: Messages are sent by a bot named after your organization with its logo.
- **Rich text formatting**: Create professional responses with bold, italics, and lists for better readability.

<Tip>
Auto-responders help set proper expectations with customers while reducing follow-up inquiries and improving overall satisfaction.
</Tip>

## Example use cases:

- **Holiday notifications**: Inform customers when your team is away for holidays.
- **After-hours communication**: Let customers know when they can expect a response during business hours.
- **Plan-based response times**: Set expectations based on customer account plan levels.
- **Ticket acknowledgments**: Confirm receipt of new support requests automatically.

*The new Thena is currently in beta, with access limited to select customers. If you're an existing customer interested in exploring the new platform, please request beta access. General availability release is coming soon.*
