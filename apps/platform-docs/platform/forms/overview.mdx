---
title: "Forms in Thena Platform"
description: "Comprehensive guide to creating, customizing, and managing forms in the Thena Platform."
---

# Forms in Thena Platform

Forms in Thena allow you to collect and manage data efficiently. This guide provides a comprehensive overview of creating, customizing, and utilizing forms, including advanced features like custom fields, conditional logic, and validation.

## Creating Forms
- Navigate to the Forms section in the dashboard.
- Click 'Create Form' and define the fields and layout.
- Determine the goal of your form and the information you need to collect.
- Choose from a variety of field types such as text, dropdown, and checkbox.
- Drag and drop fields to arrange them in the desired order.
- Set form properties such as title, description, and submission settings.
- Preview the form to ensure it meets your requirements, then save it for use.

## Custom Fields
Custom fields let you collect specialized data and tailor forms to your business needs.
- Add custom fields to forms for specialized data collection.
- Use conditional logic to display fields based on user input.
- Edit or delete custom fields as needed to keep your forms up to date.
- Monitor the usage of custom fields across different forms.

## Conditional Logic
Conditional logic enables dynamic and responsive forms by showing or hiding fields based on user input.
- Apply conditions to fields using operators like equals, not equals, greater than, etc.
- Combine multiple conditions using AND/OR logic and use nested conditions for complex scenarios.
- Preview the form to test conditions and ensure they work as expected.
- Use the Forms API to programmatically set conditions.

### Example Conditions
- Show 'Phone Number' only if 'Preferred Contact Method' is 'Phone'.
- Require 'Cover Letter' if 'Position' is 'Manager'.
- Display 'Interests' only if 'Age' is greater than 18.

## Validation
Form validation ensures data integrity and improves user experience.
- Set required fields to ensure essential information is collected.
- Use input masks to guide users in entering data correctly.
- Implement custom validation logic using JavaScript or validate data against external APIs before submission.
- Provide clear and concise error messages and use inline validation for immediate feedback.

## Examples
### Contact Form
- Fields: Name, Email, Message
- Condition: Show 'Phone Number' if 'Preferred Contact Method' is 'Phone'.
- API Example:
```json
{
  "name": "Contact Form",
  "fields": [
    { "type": "text", "label": "Name", "required": true },
    { "type": "email", "label": "Email", "required": true },
    { "type": "textarea", "label": "Message" },
    { "type": "text", "label": "Phone Number", "condition": "Preferred Contact Method == 'Phone'" }
  ]
}
```

### Feedback Form
- Fields: Rating, Comments
- Condition: Show 'Additional Feedback' if Rating is less than 3.
- API Example:
```json
{
  "name": "Feedback Form",
  "fields": [
    { "type": "number", "label": "Rating", "min": 1, "max": 5, "required": true },
    { "type": "textarea", "label": "Comments" },
    { "type": "textarea", "label": "Additional Feedback", "condition": "Rating < 3" }
  ]
}
```

### Registration Form
- Fields: Username, Password, Confirm Password
- Condition: Validate that Password and Confirm Password match.
- API Example:
```json
{
  "name": "Registration Form",
  "fields": [
    { "type": "text", "label": "Username", "required": true },
    { "type": "password", "label": "Password", "required": true },
    { "type": "password", "label": "Confirm Password", "validation": "Password == Confirm Password" }
  ]
}
```

### Survey Form
- Fields: Age, Gender, Interests
- Condition: Show 'Interests' only if Age is greater than 18.
- API Example:
```json
{
  "name": "Survey Form",
  "fields": [
    { "type": "number", "label": "Age", "required": true },
    { "type": "select", "label": "Gender", "options": ["Male", "Female", "Other"] },
    { "type": "checkbox", "label": "Interests", "options": ["Sports", "Music", "Travel"], "condition": "Age > 18" }
  ]
}
```

### Job Application Form
- Fields: Name, Email, Resume, Cover Letter
- Condition: Require 'Cover Letter' if 'Position' is 'Manager'.
- API Example:
```json
{
  "name": "Job Application Form",
  "fields": [
    { "type": "text", "label": "Name", "required": true },
    { "type": "email", "label": "Email", "required": true },
    { "type": "file", "label": "Resume", "required": true },
    { "type": "file", "label": "Cover Letter", "condition": "Position == 'Manager'" }
  ]
}
```

## Utilizing Forms
- Embed forms on your website or share them via a link.
- Collect responses and analyze data using the dashboard.
