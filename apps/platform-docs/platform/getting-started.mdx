---
title: 'Introduction'
description: 'Introduction to Thena platform'
icon: 'rocket'
---

## Introduction

Thena Platform is an AI operating system built to run customer workflows end-to-end across multiple channels and business applications. Designed for the age of AGI, Then<PERSON> enables AI agents to take ownership of business operations, with humans acting as co-pilots. The platform brings together permissioning, entitlements, data management, and cross-channel workflows, helping organizations automate and optimize customer-facing processes for greater efficiency and adaptability.

## Quick Start Guide

To get started with <PERSON><PERSON>, please follow our comprehensive onboarding guide:

[Getting started with <PERSON><PERSON>](/guides/getting-started)

This guide will walk you through creating your first project, exploring the App Studio, configuring and building your application, and deploying it live. For detailed, step-by-step instructions, visit the link above.

## Common Use Cases

<CardGroup cols={2}>
  <Card 
    title="Customer Support" 
    icon="headset"
    href="/platform/use-cases/customer-support"
  >
    Streamline support operations with intelligent ticket routing, automated responses, and unified communication channels.
  </Card>
  <Card 
    title="Team Collaboration" 
    icon="users"
    href="/platform/use-cases/team-collaboration"
  >
    Enable seamless collaboration across teams with shared workspaces, real-time updates, and integrated communication tools.
  </Card>
  <Card 
    title="Workflow Automation" 
    icon="gears"
    href="/platform/use-cases/workflow-automation"
  >
    Automate repetitive tasks and complex workflows with customizable rules, triggers, and AI-powered decision making.
  </Card>
  <Card 
    title="Knowledge Management" 
    icon="book"
    href="/platform/use-cases/knowledge-management"
  >
    Create, organize, and share knowledge base content to improve self-service and maintain consistent support quality.
  </Card>
  <Card 
    title="Account Management" 
    icon="building-user"
    href="/platform/use-cases/account-management"
  >
    Manage customer accounts, track relationships, monitor account health, and maintain comprehensive activity history.
  </Card>
  <Card 
    title="Customer Management" 
    icon="address-book"
    href="/platform/use-cases/customer-management"
  >
    Track customer interactions, preferences, and communication history for personalized engagement and support.
  </Card>
</CardGroup>
