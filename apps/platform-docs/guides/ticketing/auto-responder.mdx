---
title: 'Auto-responder'
description: 'The most advanced multi-channel response automation for modern support teams.'
image: '/images/docs/Auto-responder.webp'
---

<Frame>
  <img src="/images/docs/Auto-responder.webp" alt="Auto-responder interface showing rule creation" />
</Frame>

The auto-responder is the most advanced multi-channel response automation built for modern support teams. It gives you precise control over when, how, and where customers receive automatic replies—whether during holidays, after hours, or when assigned agents are unavailable.

Auto-responses are sent directly on the same channel the ticket originated from—Slack, MS Teams, or email. They are sent by a bot that adopts your organization's name and icon by default, so make sure those are configured before you turn it on.

## What makes it powerful

<CardGroup cols={2}>
  <Card title="Flexible triggers" icon="bell">
    Supports both ticket created and message received as trigger types, giving you precise control over when responses are sent.
  </Card>
  
  <Card title="Channel-aware" icon="comments">
    Automatically replies based on the original channel—Slack, MS Teams, or email—maintaining a consistent experience for customers.
  </Card>
  
  <Card title="Branded responses" icon="building">
    Messages are sent by a bot named after your organization with its logo, ensuring professional and consistent communication.
  </Card>
  
  <Card title="Intelligent conditions" icon="calendar-check">
    Rules can be triggered during holidays, outside business hours, or when an agent or group is unavailable.
  </Card>
  
  <Card title="Advanced filtering" icon="filter">
    Target responses based on priority, tags, assignee, or ticket title to tailor messages for specific scenarios.
  </Card>
  
  <Card title="Scheduled automation" icon="clock">
    Configure holiday-specific auto-responders in advance, so they activate automatically when needed.
  </Card>
  
  <Card title="Centralized management" icon="list-check">
    All rules are visible in one place and can be easily managed, providing a clear overview of your automation.
  </Card>
  
  <Card title="Rich formatting" icon="text-size">
    Auto-response messages support rich text formatting like bold, italics, and lists for more effective communication.
  </Card>
</CardGroup>

## Step-by-step: how to create an auto-responder rule

<Steps>
  <Step title="Go to auto-responder settings">
    Navigate to Settings → Customer support → Auto-responder.
  </Step>
  <Step title="Click 'Create your first rule'">
    If no rules exist, you'll see a blank state. Click the button to begin.
  </Step>
  <Step title="Set the rule overview">
    Give the rule a clear name, like "Memorial Day autoresponder".
  </Step>
  <Step title="Choose when the auto-responder activates">
    Pick one:
    <ul className="mt-4 ml-6 space-y-2">
      <li>When a ticket is created: responds when a new ticket is submitted.</li>
      <li>When a message is received: responds when a message is sent on an assigned ticket.</li>
    </ul>
  </Step>
  <Step title="Add conditions (optional)">
    Apply conditions to control when the response is sent:
    <ul className="mt-4 ml-6 space-y-2">
      <li>Outside business hours.</li>
      <li>During holidays (you can select specific holidays).</li>
      <li>When assigned member is unavailable or on leave.</li>
      <li>When assigned group is unavailable or on holiday.</li>
    </ul>
  </Step>
  <Step title="Use advanced filters (optional)">
    Add targeting filters such as:
    <ul className="mt-4 ml-6 space-y-2">
      <li>Ticket status, priority, or type.</li>
      <li>Tags, assignee, or escalation status.</li>
      <li>Customer email, account, or AI-generated summaries.</li>
    </ul>
  </Step>
  <Step title="Write your auto-response message">
    Use rich text to format your message.
    
    <Card className="mt-4">
      <p><strong>Example:</strong></p>
      <p>Thanks for your message!</p>
      <p>Our team is currently out for Memorial Day and will return on Tuesday. We'll get back to you shortly.</p>
    </Card>
  </Step>
  <Step title="Click 'Create rule' to save and activate it">
    Your rule is now live and will respond based on the conditions you set.
  </Step>
</Steps>

## Example use cases

<CardGroup cols={2}>
  <Card title="Ticket acknowledgment" icon="envelope-open">
    <p><strong>Trigger:</strong> When a ticket is created</p>
    <p><strong>Message:</strong> "Thanks for reaching out. We've received your request."</p>
  </Card>
  
  <Card title="Holiday responder" icon="calendar">
    <p><strong>Trigger:</strong> Ticket created</p>
    <p><strong>Condition:</strong> During holidays → July 4th</p>
    <p><strong>Message:</strong> "We're offline today for Independence Day. We'll respond as soon as we're back."</p>
  </Card>
  
  <Card title="After-hours fallback" icon="moon">
    <p><strong>Trigger:</strong> Message received</p>
    <p><strong>Condition:</strong> Outside business hours</p>
    <p><strong>Message:</strong> "Hey! Our team is currently offline. We'll get back to you during our working hours."</p>
  </Card>
  
  <Card title="Plan-based response time" icon="gem">
    <p><strong>Trigger:</strong> Ticket created</p>
    <p><strong>Filter:</strong> Account plan field is "Basic"</p>
    <p><strong>Message:</strong> "Thank you for contacting us. As a Basic plan user, your request will be addressed within 48 hours. To receive faster support, consider upgrading to our Premium plan."</p>
  </Card>
</CardGroup>
