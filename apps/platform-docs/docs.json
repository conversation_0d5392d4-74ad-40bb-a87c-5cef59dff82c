{"$schema": "https://mintlify.com/docs.json", "theme": "palm", "name": "Thena AI", "colors": {"primary": "#6A00FF", "light": "#6A00FF", "dark": "#6A00FF"}, "favicon": "/logo/favicon.svg", "navigation": {"tabs": [{"tab": "Guides", "groups": [{"group": "Guides", "icon": "book-open", "pages": [{"group": "Onboarding", "pages": ["guides/onboarding/introduction", "guides/onboarding/getting-started"]}, {"group": "Ticketing", "pages": ["guides/ticketing/teams", "guides/ticketing/groups", "guides/ticketing/routing", "guides/ticketing/working-hours", "guides/ticketing/slas", "guides/ticketing/ticket-fields", "guides/ticketing/forms", "guides/ticketing/tags", "guides/ticketing/statuses", "guides/ticketing/window", "guides/ticketing/internal-threads", "guides/ticketing/emoji-actions", "guides/ticketing/auto-responder"]}, {"group": "Sources", "pages": ["guides/sources/slack", "guides/sources/email", "guides/sources/web-chat", "guides/sources/ms-teams", "guides/sources/discord"]}, {"group": "Apps", "pages": ["platform/apps/jira"]}, {"group": "AI agents", "pages": ["guides/ai-agents/recruitment", "guides/ai-agents/flows", "guides/ai-agents/knowledge", "guides/ai-agents/executions"]}, {"group": "Accounts", "pages": ["guides/accounts/accounts-view", "guides/accounts/tasks", "guides/accounts/notes", "guides/accounts/activity", "guides/accounts/account-fields", "guides/accounts/contact-fields"]}, {"group": "Knowledge base", "pages": ["guides/knowledge-base/help-centers", "guides/knowledge-base/articles", "guides/knowledge-base/live-collaboration", "guides/knowledge-base/tags"]}, {"group": "Organization", "pages": ["guides/organization/setup", "guides/organization/members"]}, {"group": "Preferences", "pages": ["guides/preferences/profile", "guides/preferences/slack-authorization", "guides/preferences/themes", "guides/preferences/notifications", "guides/preferences/reset-password", "guides/preferences/api-key"]}]}]}, {"tab": "API reference", "groups": [{"group": "API reference", "icon": "book", "pages": ["api-reference/introduction", "api-reference/thena-mcp-server"]}, {"group": "Platform", "pages": [{"group": "Search", "pages": ["api-reference/search/introduction", "api-reference/search/tickets-search", "api-reference/search/accounts-search", "api-reference/search/comments-search", "api-reference/platform/search/entity-search", "api-reference/platform/search/federated-search"]}, {"group": "Users", "pages": ["api-reference/platform/users/fetch-all-users", "api-reference/platform/users/fetch-current-users-details", "api-reference/platform/users/update-your-user-details", "api-reference/platform/users/update-your-working-hours", "api-reference/platform/users/update-your-availability", "api-reference/platform/users/get-all-your-time-off", "api-reference/platform/users/create-your-time-off", "api-reference/platform/users/update-your-time-off", "api-reference/platform/users/delete-your-time-off"]}, {"group": "Teams", "pages": ["api-reference/platform/teams/get-all-teams-that-user-is-the-part-of!", "api-reference/platform/teams/create-a-team", "api-reference/platform/teams/get-all-public-teams", "api-reference/platform/teams/get-a-team-by-id", "api-reference/platform/teams/delete-a-team", "api-reference/platform/teams/update-a-team", "api-reference/platform/teams/get-sub-teams-for-a-team", "api-reference/platform/teams/get-all-team-members", "api-reference/platform/teams/add-a-team-member", "api-reference/platform/teams/remove-a-team-member", "api-reference/platform/teams/get-team-configurations", "api-reference/platform/teams/update-team-configurations", "api-reference/platform/teams/get-team-routing", "api-reference/platform/teams/create-a-team-routing-rule", "api-reference/platform/teams/delete-a-team-routing-rule", "api-reference/platform/teams/update-team-routing"]}, {"group": "Tickets", "pages": ["api-reference/platform/tickets/get-all-tickets", "api-reference/platform/tickets/create-a-ticket", "api-reference/platform/tickets/create-tickets-in-bulk", "api-reference/platform/tickets/get-a-ticket", "api-reference/platform/tickets/update-a-ticket", "api-reference/platform/tickets/update-tickets-in-bulk", "api-reference/platform/tickets/delete-a-ticket", "api-reference/platform/tickets/delete-tickets-in-bulk", "api-reference/platform/tickets/assign-a-ticket-to-an-agent", "api-reference/platform/tickets/reassign-a-ticket-to-a-team", "api-reference/platform/tickets/escalate-a-ticket", "api-reference/platform/tickets/get-ticket-related", "api-reference/platform/tickets/mark-a-ticket-as-sub-ticket", "api-reference/platform/tickets/mark-a-ticket-as-duplicate", "api-reference/platform/tickets/link-tickets", "api-reference/platform/tickets/comment-on-a-ticket", "api-reference/platform/tickets/get-comments-for-a-ticket", "api-reference/platform/tickets/get-comments-for-a-ticket-by-user-type", "api-reference/platform/tickets/attach-a-file-to-a-ticket", "api-reference/platform/tickets/get-v1tickets-attachments", "api-reference/platform/tickets/log-time-for-a-ticket", "api-reference/platform/tickets/get-time-logs-for-a-ticket", "api-reference/platform/tickets/create-a-new-ticket-status", "api-reference/platform/tickets/get-all-ticket-statuses", "api-reference/platform/tickets/get-a-ticket-status-by-its-id", "api-reference/platform/tickets/update-a-ticket-status", "api-reference/platform/tickets/delete-a-custom-ticket-status", "api-reference/platform/tickets/create-a-new-custom-ticket-type", "api-reference/platform/tickets/get-all-ticket-types", "api-reference/platform/tickets/get-v1ticketsticket-types", "api-reference/platform/tickets/get-a-ticket-type-by-id", "api-reference/platform/tickets/update-a-custom-ticket-type", "api-reference/platform/tickets/delete-a-custom-ticket-type", "api-reference/platform/tickets/create-a-new-custom-ticket-priority", "api-reference/platform/tickets/get-all-ticket-priorities", "api-reference/platform/tickets/get-a-ticket-priority-by-id", "api-reference/platform/tickets/update-a-custom-ticket-priority", "api-reference/platform/tickets/delete-a-custom-ticket-priority", "api-reference/platform/tickets/create-a-new-ticket-sentiment", "api-reference/platform/tickets/get-all-ticket-sentiments", "api-reference/platform/tickets/get-a-ticket-sentiment-by-id", "api-reference/platform/tickets/update-a-ticket-sentiment", "api-reference/platform/tickets/delete-a-ticket-sentiment", "api-reference/platform/tickets/unarchive-a-ticket", "api-reference/platform/tickets/archive-a-ticket", "api-reference/platform/tickets/archive-tickets-in-bulk"]}, {"group": "Tags", "pages": ["api-reference/platform/tags/get-all-tags", "api-reference/platform/tags/create-a-new-tag", "api-reference/platform/tags/get-a-specific-tag", "api-reference/platform/tags/update-a-tag", "api-reference/platform/tags/delete-a-tag", "api-reference/platform/tags/get-all-tags-for-a-particular-team", "api-reference/platform/tags/create-a-new-tag-for-a-particular-team", "api-reference/platform/tags/update-a-tag-for-a-particular-team", "api-reference/platform/tags/remove-a-tag-from-a-particular-team"]}, {"group": "Ticket tags", "pages": ["api-reference/platform/ticket-tags/get-all-tags-for-a-ticket", "api-reference/platform/ticket-tags/tags-successfully-added-to-ticket", "api-reference/platform/ticket-tags/remove-a-tag-from-a-ticket"]}, {"group": "Accounts", "pages": ["api-reference/platform/accounts/get-all-accounts", "api-reference/platform/accounts/create-an-account", "api-reference/platform/accounts/get-accounts-by-ids", "api-reference/platform/accounts/get-accounts-by-primary-domains", "api-reference/platform/accounts/create-accounts-in-bulk", "api-reference/platform/accounts/get-account-details", "api-reference/platform/accounts/update-an-account", "api-reference/platform/accounts/delete-an-account", "api-reference/platform/accounts/get-all-account-relationships", "api-reference/platform/accounts/create-an-account-relationship", "api-reference/platform/accounts/update-an-account-relationship", "api-reference/platform/accounts/delete-an-account-relationship", "api-reference/platform/accounts/get-all-account-relationship-types", "api-reference/platform/accounts/create-an-account-relationship-type", "api-reference/platform/accounts/update-an-account-relationship-type", "api-reference/platform/accounts/delete-an-account-relationship-type", "api-reference/platform/accounts/get-all-customer-contacts", "api-reference/platform/accounts/create-a-customer-contact", "api-reference/platform/accounts/filter-customer-contacts-by-ids", "api-reference/platform/accounts/search-customer-contacts", "api-reference/platform/accounts/bulk-create-customer-contacts", "api-reference/platform/accounts/update-a-customer-contact", "api-reference/platform/accounts/delete-a-customer-contact", "api-reference/platform/accounts/get-custom-fields-for-an-account", "api-reference/platform/accounts/gets-all-account-attribute-values", "api-reference/platform/accounts/creates-an-account-attribute-value", "api-reference/platform/accounts/updates-an-account-attribute-value", "api-reference/platform/accounts/deletes-an-account-attribute-value", "api-reference/platform/accounts/fetches-all-account-activities", "api-reference/platform/accounts/creates-an-account-activity", "api-reference/platform/accounts/updates-an-account-activity", "api-reference/platform/accounts/deletes-an-account-activity", "api-reference/platform/accounts/removes-an-attachment-from-an-account-activity", "api-reference/platform/accounts/fetches-all-account-notes-by-account-id-or-by-note-id", "api-reference/platform/accounts/creates-an-account-note", "api-reference/platform/accounts/updates-an-account-note", "api-reference/platform/accounts/deletes-an-account-note", "api-reference/platform/accounts/removes-an-attachment-from-an-account-note", "api-reference/platform/accounts/fetches-all-account-tasks-by-account-id-or-by-task-id", "api-reference/platform/accounts/creates-an-account-task", "api-reference/platform/accounts/updates-an-account-task", "api-reference/platform/accounts/deletes-an-account-task", "api-reference/platform/accounts/removes-an-attachment-from-an-account-task", "api-reference/platform/accounts/ingest-users"]}, {"group": "Forms", "pages": ["api-reference/platform/forms/get-all-forms", "api-reference/platform/forms/create-a-form", "api-reference/platform/forms/update-form", "api-reference/platform/forms/get-forms-by-ids", "api-reference/platform/forms/search-forms-using-name", "api-reference/platform/forms/delete-forms", "api-reference/platform/forms/order-forms"]}, {"group": "Comments", "pages": ["api-reference/platform/comments/get-comments-on-an-entity", "api-reference/platform/comments/get-comments-for-an-entity-by-user-type", "api-reference/platform/comments/get-comment-threads", "api-reference/platform/comments/get-a-comment", "api-reference/platform/comments/comment-on-an-entity", "api-reference/platform/comments/update-a-comment", "api-reference/platform/comments/delete-a-comment"]}, {"group": "Emoji actions", "pages": ["api-reference/platform/emoji-actions/get-all-emoji-actions", "api-reference/platform/emoji-actions/get-all-emoji-actions-for-a-team", "api-reference/platform/emoji-actions/map-an-emoji-action", "api-reference/platform/emoji-actions/unmap-an-emoji-action", "api-reference/platform/emoji-actions/update-an-emoji-action", "api-reference/platform/emoji-actions/update-an-emoji-action-by-id", "api-reference/platform/emoji-actions/delete-an-emoji-action-by-id"]}, {"group": "Custom object fields", "pages": ["api-reference/platform/custom-object-fields/get-fields-for-a-custom-object", "api-reference/platform/custom-object-fields/add-a-field-to-a-custom-object"]}, {"group": "Custom objects", "pages": ["api-reference/platform/custom-objects/get-all-custom-objects", "api-reference/platform/custom-objects/get-custom-objects-by-ids", "api-reference/platform/custom-objects/search-custom-object-using-name", "api-reference/platform/custom-objects/create-a-custom-object", "api-reference/platform/custom-objects/update-custom-objects"]}, {"group": "Object records", "pages": ["api-reference/platform/object-records/get-v1custom-object-records", "api-reference/platform/object-records/get-records-by-ids", "api-reference/platform/object-records/post-v1custom-object-records", "api-reference/platform/object-records/patch-v1custom-object-records", "api-reference/platform/object-records/delete-v1custom-object-records"]}, {"group": "Views", "pages": ["api-reference/platform/views/get-all-views", "api-reference/platform/views/create-a-new-view!", "api-reference/platform/views/fetch-one-view!", "api-reference/platform/views/update-a-view!", "api-reference/platform/views/delete-a-view!", "api-reference/platform/views/duplicate-a-view!"]}, {"group": "Views types", "pages": ["api-reference/platform/views-types/get-all-views"]}, {"group": "Draft tickets", "pages": ["api-reference/platform/draft-tickets/get-all-draft-tickets", "api-reference/platform/draft-tickets/create-a-new-ticket-draft", "api-reference/platform/draft-tickets/get-draft-ticket-by-uid", "api-reference/platform/draft-tickets/update-draft-ticket", "api-reference/platform/draft-tickets/delete-draft-ticket", "api-reference/platform/draft-tickets/publish-draft-ticket"]}, {"group": "Storage", "pages": ["api-reference/platform/storage/get-a-file-by-its-identifier", "api-reference/platform/storage/upload-a-single-file", "api-reference/platform/storage/get-private-url-for-a-file", "api-reference/platform/storage/delete-a-file-by-its-identifier"]}, {"group": "Reactions", "pages": ["api-reference/platform/reactions/add-a-reaction-to-a-comment", "api-reference/platform/reactions/remove-a-reaction-from-a-comment", "api-reference/platform/reactions/get-v1reactionsemojis"]}]}, {"group": "Workflows", "pages": [{"group": "Workflows", "pages": ["api-reference/workflows/workflows/available-filter-operators-and-logical-operators-to-use-in-workflow-filters", "api-reference/workflows/workflows/create-a-new-workflow", "api-reference/workflows/workflows/delete-a-workflow", "api-reference/workflows/workflows/get-activity-registry", "api-reference/workflows/workflows/get-all-the-executions-of-a-workflow", "api-reference/workflows/workflows/get-all-the-tasks-of-a-workflow-execution", "api-reference/workflows/workflows/get-all-the-workflows-defined-by-the-organization", "api-reference/workflows/workflows/get-all-the-workflows-defined-by-the-organization-1", "api-reference/workflows/workflows/get-event-registry", "api-reference/workflows/workflows/toggle-a-workflow", "api-reference/workflows/workflows/update-a-workflow"]}]}, {"group": "Apps platform", "pages": [{"group": "App creation", "pages": ["api-reference/apps-platform/app-creation/create-a-new-app", "api-reference/apps-platform/app-creation/delete-an-app", "api-reference/apps-platform/app-creation/fetch-app-manifest", "api-reference/apps-platform/app-creation/get-appsfetch-apps", "api-reference/apps-platform/app-creation/update-an-existing-app"]}, {"group": "App installation", "pages": ["api-reference/apps-platform/app-installation/install-an-app", "api-reference/apps-platform/app-installation/get-installed-apps-by-organization", "api-reference/apps-platform/app-installation/update-the-configuration-for-installations", "api-reference/apps-platform/app-installation/add-app-to-teams", "api-reference/apps-platform/app-installation/remove-app-from-teams"]}, {"group": "App uninstallation", "pages": ["api-reference/apps-platform/app-uninstallation/post-appsuninstall"]}, {"group": "App reinstallation", "pages": ["api-reference/apps-platform/app-reinstallation/reinstall-an-app"]}, {"group": "Incoming webhook", "pages": ["api-reference/apps-platform/incoming-webhook/handle-published-events-webhook"]}]}]}, {"tab": "Changelog", "groups": [{"group": "Changelog", "icon": "list", "pages": ["changelog/all"]}, {"group": "Release history", "icon": "clock-rotate-left", "pages": ["changelog/june-3-2025", "changelog/may-22-2025", "changelog/may-10-2025", "changelog/may-1-2025", "changelog/april-24-2025", "changelog/april-17-2025", "changelog/april-10-2025"]}]}]}, "logo": {"light": "/images/docs/Thena-logo.svg", "dark": "/logo/Group 38 (2).svg"}, "api": {"openapi": [], "playground": {"display": "interactive", "proxy": false}}, "navbar": {"links": [{"label": "Status", "href": "https://status-platform.thena.ai/"}], "primary": {"type": "button", "label": "Get started", "href": "https://thena.ai/signup"}}, "footer": {"socials": {"twitter": "https://twitter.com/thenaai", "github": "https://github.com/thenaai", "linkedin": "https://www.linkedin.com/company/thenaai"}}}