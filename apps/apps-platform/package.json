{"name": "@thena-backend/apps-platform", "description": "The Thena Apps Platform enables enterprises, developers, and AI agents to build, publish, and manage applications that extend Thena's core functionality", "version": "0.0.1", "author": "@thena", "dependencies": {"@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^10.2.1", "@fastify/static": "7.0.4", "@grpc/grpc-js": "^1.12.2", "@grpc/proto-loader": "^0.7.13", "@grpc/reflection": "^1.0.4", "@nestjs/axios": "^3.0.3", "@nestjs/common": "^10.4.4", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.4", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.7", "@nestjs/passport": "^10.0.3", "@nestjs/platform-fastify": "^10.4.5", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "@repo/eslint-config": "workspace:*", "@repo/nestjs-commons": "workspace:*", "@repo/shared-proto": "workspace:*", "@repo/thena-eventbridge": "workspace:*", "@repo/thena-platform-entities": "workspace:*", "@repo/thena-shared-libs": "workspace:*", "@repo/workflow-engine": "workspace:*", "@repo/nestjs-newrelic": "workspace:*", "@types/js-yaml": "^4.0.9", "ajv-formats": "^3.0.1", "axios": "^1.7.8", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cls-rtracer": "^2.6.3", "dotenv": "^16.4.5", "fastify": "^5.0.0", "handlebars": "^4.7.8", "istanbul-badges-readme": "^1.9.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "newrelic": "^12.18.2", "passport-jwt": "^4.0.1", "pino": "^9.5.0", "pino-pretty": "^11.3.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "svix": "^1.42.0", "typeorm": "^0.3.20", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.1.3", "@nestjs/testing": "^10.4.4", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/jest": "^29.5.2", "@types/node": "^22.9.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "ajv": "^8.17.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "node-vault": "^0.10.2", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "superjson": "^2.2.2", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "private": true, "scripts": {"build": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "clean:modules": "rimraf node_modules", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "start": "nest start", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch | pino-pretty --singleLine --colorize", "start:prod": "node dist/main", "test": "jest", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:watch": "jest --watch", "test:app-manifest": "jest src/app-manifest/test", "test:app-manifest:cov": "jest --coverage \"src/app-manifest/.*\\.spec\\.ts$\"", "test:webhook": "jest src/webhook/test", "test:webhook:cov": "jest --coverage \"src/webhook/.*\\.spec\\.ts$\"", "test:all:cov": "jest --coverage", "make-badges": "jest --coverage --coverageReporters=\"json-summary\"  && istanbul-badges-readme"}}