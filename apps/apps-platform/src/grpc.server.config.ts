import { MicroserviceOptions, Transport } from "@nestjs/microservices";
import { apps, health } from "@repo/shared-proto";
import { join } from "path";

export const grpcServerConfig: MicroserviceOptions = {
  transport: Transport.GRPC,
  options: {
    url: "0.0.0.0:50053",
    loader: { keepCase: false },
    package: [
      apps.GRPC_APPS_V1_PACKAGE_NAME,
      health.GRPC_HEALTH_V1_PACKAGE_NAME,
    ],
    protoPath: [
      join(__dirname, "../../../packages/shared-proto/dist/proto/apps/apps.proto"),
      join(__dirname, "../../../packages/shared-proto/dist/proto/health/health.proto"),
    ],
  },
};
