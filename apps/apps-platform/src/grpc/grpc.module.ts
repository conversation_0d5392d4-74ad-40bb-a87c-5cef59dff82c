import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  App,
  AppInstallation,
  AppInstallationRepository,
  AppRepository,
} from "@repo/thena-platform-entities";
import { CommonModule } from "../common/common.module";
import { AddToTeamsClientGrpc } from "./client/add-to-teams.client.grpc";
import { BotInstallationGrpcClient } from "./client/bot-installation.client.grpc";
import { FetchApiKeyGrpcClient } from "./client/fetch-api-key.client.grpc";
import { WorkflowGrpcClient } from "./client/workflow-register-events.client.grpc";
import { AppsGrpcController } from "./controllers/apps.grpc.controller";
import { TeamManagementService } from "./services/add-to-teams.transaction.service";
import { AppsGrpcService } from "./services/apps.grpc.service";

@Module({
  imports: [
    CommonModule,
    ConfigModule.forRoot({
      envFilePath: process.env.NODE_ENV === "test" ? ".env.test" : ".env",
    }),
    TypeOrmModule.forFeature([
      App,
      AppInstallation,
      AppRepository,
      AppInstallationRepository,
    ]),
  ],
  controllers: [AppsGrpcController],
  providers: [
    BotInstallationGrpcClient,
    WorkflowGrpcClient,
    ConfigService,
    AddToTeamsClientGrpc,
    TeamManagementService,
    AppsGrpcService,
    AppRepository,
    AppInstallationRepository,
    FetchApiKeyGrpcClient,
  ],
  exports: [
    BotInstallationGrpcClient,
    WorkflowGrpcClient,
    AddToTeamsClientGrpc,
    TeamManagementService,
    AppsGrpcService,
    FetchApiKeyGrpcClient,
  ],
})
export class GrpcModule {}
