import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { CommonModule } from "../common/common.module";
import { AddToTeamsClientGrpc } from "./client/add-to-teams.client.grpc";
import { BotInstallationGrpcClient } from "./client/bot-installation.client.grpc";
import { WorkflowGrpcClient } from "./client/workflow-register-events.client.grpc";
import { TeamManagementService } from "./services/add-to-teams.transaction.service";

@Module({
  imports: [
    CommonModule,
    ConfigModule.forRoot({
      envFilePath: process.env.NODE_ENV === "test" ? ".env.test" : ".env",
    }),
    TypeOrmModule.forFeature([
      App,
      AppInstallation,
      AppRepository,
      AppInstallationRepository,
    ]),
  ],
  controllers: [AppsGrpcController],
  providers: [
    BotInstallationGrpcClient,
    WorkflowGrpcClient,
    ConfigService,
    AddToTeamsClientGrpc,
    TeamManagementService,
    AppsGrpcService,
    AppRepository,
    AppInstallationRepository,
  ],
  exports: [
    BotInstallationGrpcClient,
    WorkflowGrpcClient,
    AddToTeamsClientGrpc,
    TeamManagementService,
    AppsGrpcService,
  ],
})
export class GrpcModule {}
