import { Controller, Inject } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { ILogger } from "@repo/nestjs-commons/logger";
import { apps } from "@repo/shared-proto";
import { AppsGrpcService } from "../services/apps.grpc.service";

@Controller()
export class AppsGrpcController {
  constructor(
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly appsGrpcService: AppsGrpcService,
  ) {}

  @GrpcMethod("Apps", "IsThenaOwnedApp")
  async isThenaOwnedApp(
    data: apps.IsThenaOwnedAppRequest,
  ): Promise<apps.IsThenaOwnedAppResponse> {
    try {
      this.logger.log("Checking if bot belongs to Thena-owned app", {
        botUserUid: data.botUserUid,
      });

      const result = await this.appsGrpcService.isThenaOwnedApp(data.botUserUid);

      this.logger.log("Thena app check completed", {
        botUserUid: data.botUserUid,
        isThenaOwned: result.isThenaOwned,
        appUid: result.appUid,
      });

      return {
        isThenaOwned: result.isThenaOwned,
        appUid: result.appUid || "",
        appName: result.appName || "",
      };
    } catch (error) {
      this.logger.error("Error checking if bot belongs to Thena-owned app", {
        error: error.message,
        botUserUid: data.botUserUid,
      });

      // Return false for safety
      return {
        isThenaOwned: false,
        appUid: "",
        appName: "",
      };
    }
  }
}
