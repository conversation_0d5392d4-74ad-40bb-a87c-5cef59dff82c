import { Injectable, Logger } from "@nestjs/common";
import {
  AppInstallationRepository,
  InstallationStatus,
} from "@repo/thena-platform-entities";

export interface ThenaAppCheckResult {
  isThenaOwned: boolean;
  appUid?: string;
  appName?: string;
}

@Injectable()
export class AppsGrpcService {
  private readonly logger = new Logger(AppsGrpcService.name);

  constructor(
    private readonly appInstallationRepository: AppInstallationRepository,
  ) {}

  /**
   * Checks if a bot user belongs to a Thena-owned app
   * @param botUserUid The UID of the bot user
   * @returns Promise<ThenaAppCheckResult>
   */
  async isThenaOwnedApp(botUserUid: string): Promise<ThenaAppCheckResult> {
    try {
      this.logger.log("Checking if bot belongs to Thena-owned app", {
        botUserUid,
      });

      // Find the app installation for this bot user using repository method
      const appInstallation =
        await this.appInstallationRepository.findByCondition({
          where: {
            botUserId: botUserUid,
            status: InstallationStatus.ACTIVE,
          },
          relations: ["app"],
        });

      if (!appInstallation) {
        this.logger.warn("No active app installation found for bot user", {
          botUserUid,
        });
        return { isThenaOwned: false };
      }

      // Check if the app is Thena-owned
      const isThenaOwned = appInstallation.app.isThenaPrivileged;

      this.logger.log("App ownership check completed", {
        botUserUid,
        appUid: appInstallation.app.uid,
        appName: appInstallation.app.name,
        isThenaOwned,
      });

      return {
        isThenaOwned,
        appUid: appInstallation.app.uid,
        appName: appInstallation.app.name,
      };
    } catch (error) {
      this.logger.error("Error checking if bot belongs to Thena-owned app", {
        error: error.message,
        botUserUid,
      });

      // Return false for safety
      return { isThenaOwned: false };
    }
  }
}
