import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from "@nestjs/common";
import {
  SQSConsumerService,
  SQSMessage,
} from "@repo/nestjs-commons/aws-utils/sqs";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  AppEventsAuditRepository,
  AppRepository,
  AppVisibility,
  AppWebhookDeliveryLog,
  AppWebhookDeliveryRepository,
  TransactionService,
  WebhookDeliveryStatus,
} from "@repo/thena-platform-entities";
import { In, MoreThan, Not } from "typeorm";
import { ConfigService } from "../../config/app.config.service";
import { SQSMessageAttributes } from "../Interfaces/incoming-webhook.interface";
import { ALLOWED_FILTER_TYPES } from "../Interfaces/webhook.svix.interface";
import { SvixWebhookService } from "./svix.webhook.service";

/**
 *  Service responsible for handling webhook delivery to installed apps
 * - Consumes messages from SQS
 * - Delivers webhook events to installed apps via Svix
 * - Tracks delivery success/failure in audit logs
 * - Provides selective retry for failed deliveries
 */

@Injectable()
export class WebhookService implements OnModuleInit, OnModuleDestroy {
  constructor(
    // SQS Consumer
    @Inject("SQS_CONSUMER")
    private readonly platformSqsConsumer: SQSConsumerService,

    // Logger
    @Inject("CustomLogger") private readonly logger: ILogger,

    // Repositories
    private readonly appEventsAuditRepository: AppEventsAuditRepository,
    private readonly appRepository: AppRepository,
    private readonly appWebhookDeliveryRepository: AppWebhookDeliveryRepository,
    // Services
    private readonly svixWebhookService: SvixWebhookService,
    private readonly configService: ConfigService,
    private readonly transactionService: TransactionService,
  ) {}

  onModuleInit() {
    this.startConsumers();
  }

  onModuleDestroy() {
    this.stopConsumers();
  }

  private startConsumers(): void {
    // Start the platform SQS consumer
    this.platformSqsConsumer.startConsumer(async (message) => {
      /**
       * Check if the event payload is a valid json object
       */
      if (typeof message === "string") {
        this.logger.error(
          "[WebhookService] Invalid event payload. Expected a JSON object. Check the SNS settings [ENABLE_RAW_MESSAGE_DELIVERY SHOULD BE TRUE].",
        );
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST, // SNS will Move this meesage to Dead Letter Queue. [400 status code]
            error: "Invalid event payload. Expected a JSON object.",
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.handleMessage(message);
    });

    this.logger.log(
      "[WebhookService] platform SQS consumer started successfully",
    );
  }

  private stopConsumers(): void {
    // Stop the platform SQS consumer upon module shutdown
    this.platformSqsConsumer.stopConsumer();
    this.logger.log(
      "[WebhookService] platform SQS consumer stopped successfully",
    );
  }

  /**
   * Creates an audit log entry for a failed webhook delivery
   *
   * @param eventId The event ID
   * @param appId The app ID
   * @param appName The app name
   * @param errorMessage The error message
   * @param isAppNotFound Whether the app was not found in SVIX
   * @returns Promise<AppWebhookDeliveryLog> The created delivery log
   */
  private async createDeliveryLog(
    eventId: string,
    appId: string,
    appName: string,
    status: WebhookDeliveryStatus,
    startTime: number,
    options?: {
      errorMessage?: string;
      isAppNotFound?: boolean;
      svixMessageId?: string;
    },
  ): Promise<AppWebhookDeliveryLog> {
    const endTime = Date.now();
    const processingTimeMs = endTime - startTime;
    const { errorMessage, isAppNotFound, svixMessageId } = options || {};

    // Log the failure with structured data
    if (status === WebhookDeliveryStatus.SUCCESS) {
      this.logger.log(
        `[WebhookService] Successfully sent message ${eventId} to app ${appId} | Name: ${appName}`,
      );
    } else if (status === WebhookDeliveryStatus.FAILED) {
      if (isAppNotFound) {
        this.logger.warn(
          `[WebhookService] App not found for event ${eventId} to app ${appId} | Name: ${appName}`,
        );
      } else {
        this.logger.warn(
          `[WebhookService] Failed to send message for event ${eventId} to app ${appId} | Name: ${appName} | Error: ${errorMessage}`,
        );
      }
    }

    // Create delivery log
    const deliveryLog = await this.appWebhookDeliveryRepository.save({
      eventId,
      appId,
      status,
      attemptCount: 1,
      errorMessage:
        status === WebhookDeliveryStatus.FAILED
          ? `${appId}: ${errorMessage}`
          : null,
      processingTimeMs,
      svixMessageId: svixMessageId || null,
    });

    return deliveryLog;
  }

  private async handleMessage(event: SQSMessage): Promise<void> {
    try {
      const { messageAttributes } = event;
      const { message } = event;
      const {
        event_id,
        event_name,
        context_organization_id,
        context_user_id,
        context_user_type,
      } = messageAttributes as SQSMessageAttributes;

      /**
       * Store the message in the audit log
       * This will be used to track the message delivery status
       * TODO: Make this find and update. Handle Idempotency
       */
      await this.appEventsAuditRepository.save({
        eventId: event_id,
        eventName: event_name,
        organizationId: context_organization_id,
        executingUserId: context_user_id,
        executingUserType: context_user_type,
        payload: message as Record<string, any>,
        status: WebhookDeliveryStatus.PENDING,
        targetAppIds: [],
        successfulAppIds: [],
        failedAppIds: [],
      });
      await this.processSQSMessage(event);
    } catch (error) {
      this.logger.error(`[WebhookService] Error processing webhook:`, error);
      throw error;
    }
  }

  /**
   * Processes a webhook message from SQS
   * Implements selective retry for failed app IDs and handles idempotency
   *
   * @param priority Priority level of the message (high, medium, low)
   * @param data The SQS message data
   * @returns Promise<void>
   */
  private async processSQSMessage(event: SQSMessage): Promise<void> {
    const startTime = Date.now();
    const successfulAppIds: string[] = [];
    const failedAppIds: string[] = [];
    const inactiveAppIds: string[] = [];
    const { message, messageAttributes } = event;
    const { event_id, event_name } = messageAttributes as SQSMessageAttributes;

    this.logger.log(
      `[WebhookService] Processing SQS message at ${new Date(
        startTime,
      ).toLocaleString()} | Event ID: ${event_id} | Event Name: ${event_name}`,
    );

    /**
     * Function to send the SQS message to the SVIX Consumer apps.
     * 1. Check the event name and find the apps that have subscribed to the event. [TODO: PHASE 2][Currently all apps are subscribed to all events]
     * 2. Find all the active apps in the apps directory.
     * 3. Parellelly send the message to the SVIX consumer apps.
     * 4. Have 3 attemps for each app. But if the error message is that the app is not found, then don't attempt to send the message again. [TODO: PHASE 2]
     * 5. Update the audit log with the apps that have successfully received the message.
     * 6. For apps that were not found mark the apps as INACTIVE immediately.
     * 7. ACK the message from SQS.
     */

    const apps = await this.appRepository.findAll({
      where: {
        isActive: true,
        visibility: Not(AppVisibility.UNLISTED),
        installationCount: MoreThan(0),
      },
      select: ["uid", "name"],
    });

    if (apps.length === 0) {
      this.logger.log(
        "[WebhookService] No active apps found to process webhook",
      );
      return;
    }

    this.logger.log(
      `[WebhookService] Found ${apps.length} apps to process webhook`,
    );

    // Process apps in parallel
    const deliveryPromises = await Promise.all(
      apps.map(async (app) => {
        try {
          const response = await this.svixWebhookService.sendMessage(app.uid, {
            eventType: ALLOWED_FILTER_TYPES.EVENTS,
            payload: {
              message,
              xWebhookEvent: event_name,
            },
            channels: [this.configService.get("NODE_ENV")],
          });

          // Add the entry to the audit log for successful delivery in the app_webhook_delivery_logs table
          await this.createDeliveryLog(
            event_id,
            app.uid,
            app.name,
            WebhookDeliveryStatus.SUCCESS,
            startTime,
            {
              svixMessageId: response?.id,
            },
          );
          this.logger.log(
            `[WebhookService] Successfully sent to app: ${app.uid} | Name: ${app.name}`,
          );
          // Add the entry to the audit log
          return { appId: app.uid, success: true, markAsInactive: false };
        } catch (error) {
          if (error.code === 404) {
            // Add the entry to the audit log for inactive app in the app_webhook_delivery_logs table
            await this.createDeliveryLog(
              event_id,
              app.uid,
              app.name,
              WebhookDeliveryStatus.FAILED,
              startTime,
              {
                errorMessage: error.message,
                isAppNotFound: true,
              },
            );
            this.logger.log(
              `[WebhookService] App not found: ${app.uid} | Name: ${app.name}`,
            );
            return {
              appId: app.uid,
              success: false,
              reason: error.message,
              markAsInactive: true,
            };
          }
          // Add the entry to the audit log for failed delivery in the app_webhook_delivery_logs table
          await this.createDeliveryLog(
            event_id,
            app.uid,
            app.name,
            WebhookDeliveryStatus.FAILED,
            startTime,
            {
              errorMessage: error.message,
              isAppNotFound: false,
            },
          );
          this.logger.error(
            `[WebhookService] Failed to send to app ${app.uid} | Name: ${app.name} | Reason: ${error.message}`,
          );
          return {
            appId: app.uid,
            success: false,
            reason: error.message,
            markAsInactive: false,
          };
        }
      }),
    );

    // Process the delivery promises
    deliveryPromises.forEach((result) => {
      if (!result.success) {
        failedAppIds.push(result.appId);
        if (result.markAsInactive) inactiveAppIds.push(result.appId);
        return;
      }

      successfulAppIds.push(result.appId);
    });

    /**
     * Update the audit log with the results
     * Mark the apps as inactive if they are not found
     */

    await this.transactionService.runInTransaction(async (txnContext) => {
      await this.appEventsAuditRepository.updateWithTxn(
        txnContext,
        { eventId: event_id },
        {
          targetAppIds: [...successfulAppIds, ...failedAppIds],
          successfulAppIds,
          failedAppIds,
          status: WebhookDeliveryStatus.SUCCESS,
          processingTimeMs: Date.now() - startTime,
        },
      );

      // Mark the apps as inactive if they are not found
      if (inactiveAppIds.length > 0) {
        this.logger.log(
          `[WebhookService] Marking ${inactiveAppIds.length} apps as inactive`,
        );
        await this.appRepository.updateWithTxn(
          txnContext,
          { uid: In(inactiveAppIds) },
          {
            isActive: false,
            deletedAt: new Date(),
            updatedAt: new Date(),
          },
        );
      }
    });

    this.logger.log(
      `[WebhookService] Webhook delivery complete | Event ID: ${event_id} | Event Name: ${event_name} | Successful: ${
        successfulAppIds.length
      } | Failed: ${failedAppIds.length} | Processing Time: ${
        Date.now() - startTime
      }ms`,
    );
  }
}
