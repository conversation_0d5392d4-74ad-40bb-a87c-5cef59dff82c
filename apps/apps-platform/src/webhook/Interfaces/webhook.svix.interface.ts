import { EndpointIn, EndpointOut, EndpointUpdate } from "svix";

/**
 * Interface for creating a Svix application
 * Required non-empty name for the application
 * Optional rate limit for the application
 * Optional unique identifier for the application
 * Optional metadata object for the application
 */
export const ALLOWED_FILTER_TYPES = {
  INSTALLATION: "app.installation",
  EVENTS: "app.event",
} as const;

export const ALLOWED_EVENT_TYPES = {
  INSTALLATION: "app:installation",
  EVENTS: "app:event",
  REINSTALLATION: "app:reinstall",
  UNINSTALLATION: "app:uninstall",
  TEAM_ADDED: "app:team:added",
  TEAM_REMOVED: "app:team:removed",
  CONFIGURATION_UPDATE: "app:configuration:update",
} as const;

export type AllowedFilterTypes =
  (typeof ALLOWED_FILTER_TYPES)[keyof typeof ALLOWED_FILTER_TYPES];

export interface EndpointConfig extends Omit<EndpointIn, "filterTypes"> {
  filterTypes: AllowedFilterTypes[];
}

export interface ISvixApplicationRequest {
  name: string;
  rateLimit?: number | null;
  uid?: string | null;
  metadata?: {
    eventsWebhookConfig?: EndpointIn;
    installationsWebhookConfig?: EndpointIn;
    [key: string]: any;
  };
}
export interface ISvixApplicationUpdate {
  events: EndpointUpdate & { id: string };
  installations: EndpointUpdate & { id: string };
}

export interface ISvixApplicationResponse {
  id: string;
  uid?: string | null;
  name: string;
  rateLimit?: number | null;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface ISvixConsumerAppResponse {
  app: ISvixApplicationResponse;
  endpoints: {
    events?: EndpointOut;
    installations?: EndpointOut;
  };
}

export interface SagaState {
  data: {
    applicationId?: string;
    installationsWebhookResponse?: EndpointOut;
    eventsWebhookResponse?: EndpointOut;
  };
}

export interface SvixSecretResponse {
  appId: string;
  secrets: Record<string, { key: string; endpointName: string }>; // Map endpointId -> secret
}
