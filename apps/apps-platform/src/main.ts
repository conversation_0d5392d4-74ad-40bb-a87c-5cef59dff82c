import { ValidationPipe } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import {
  FastifyAdapter,
  NestFastifyApplication,
} from "@nestjs/platform-fastify";
import { shouldExcludeFromSentry } from "@repo/nestjs-commons/errors";
import {
  SENTRY_SERVICE_TOKEN,
  SentryExceptionFilter,
} from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { NewRelicInterceptor, NewRelicService } from "@repo/nestjs-newrelic";
import * as Sentry from "@sentry/node";
import * as clsTracer from "cls-rtracer";
import dotenv from "dotenv";
import "newrelic";
import pino from "pino";
import { v4 as uuidv4 } from "uuid";
import { AppModule } from "./app.module";
import { ConfigService } from "./config/app.config.service";
import { ValidationExceptionFilter } from "./fliters/validation-exception.filter";
import { setupSwagger } from "./utils/swagger";
dotenv.config({ path: ".env" });
/**
 * Bootstrap the NestJS application.
 * Initializes the Fastify adapter, sets up middleware, and starts the server.
 */
async function bootstrap() {
  const fastifyAdapter = new FastifyAdapter({
    logger: {
      level: "info",
      base: { app: process.env.APP_TAG, service: process.env.SERVICE_TAG },
      timestamp: pino.stdTimeFunctions.isoTime,
    },
    trustProxy: ["loopback"],
    genReqId: () => uuidv4(),
  });

  // Initialize Sentry
  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV,
    tracesSampleRate: 1.0,
    attachStacktrace: true,
    autoSessionTracking: true,
    beforeSend(event, hint) {
      const exception = hint.originalException;
      if (exception instanceof Error && shouldExcludeFromSentry(exception)) {
        return null; // Don't send to Sentry
      }
      return event;
    },
  });

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    fastifyAdapter,
    { bufferLogs: true },
  );

  // Connect the gRPC microservice for gRPC routes
  const { grpcServerConfig } = await import("./grpc.server.config");
  app.connectMicroservice<MicroserviceOptions>(grpcServerConfig);

  const configService = app.get(ConfigService);
  const logger = app.get<ILogger>("CustomLogger");
  app.useLogger(logger);

  // Add validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      stopAtFirstError: true,
    }),
  );

  // Add request tracking middleware
  app.use(
    clsTracer.fastifyMiddleware({
      echoHeader: true,
      requestIdFactory: (req) => ({
        reqId: req.id,
        context: {},
      }),
    }),
  );

  // Register Sentry Exception Filter
  const sentryService = app.get(SENTRY_SERVICE_TOKEN);
  app.useGlobalFilters(
    new SentryExceptionFilter(sentryService),
    new ValidationExceptionFilter(),
  );

  // Enable CORS
  app.enableCors({
    origin: "*", // Configure as needed
    methods: ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE"],
    credentials: true,
  });

  // Swagger setup
  if (configService.isDevelopment || configService.isStaging) {
    setupSwagger(app, configService);
  }

  // Get NewRelicService from the app
  const newRelicService = app.get(NewRelicService);

  // Only add the interceptor if New Relic is enabled
  if (newRelicService.isEnabled() && process.env.NODE_ENV !== "development") {
    const interceptor = new NewRelicInterceptor(newRelicService);
    app.useGlobalInterceptors(interceptor as any);
    newRelicService.setServiceName(
      `${process.env.APP_TAG} | ${process.env.SERVICE_TAG} | ${process.env.NODE_ENV}`,
    );
  }

  const port = configService.get("PORT");
  const host = "0.0.0.0";

  try {
    // Start all microservices (including gRPC)
    await app.startAllMicroservices();
    await app.listen(port, host);
    logger.log(
      `🚀 Server launched in ${configService.nodeEnv} mode on ${host}:${port}`,
    );
    if (configService.isDevelopment) {
      logger.log(
        `📚 Swagger documentation available at http://${host}:${port}/api`,
      );
      logger.log(`❤️  Health check available at http://${host}:${port}/health`);
    }
  } catch (error) {
    logger.error(
      "Failed to start application",
      error instanceof Error ? error.stack : String(error),
    );
    process.exit(1);
  }
}

bootstrap().catch((err) => {
  console.error("Failed to bootstrap application:", err);
  process.exit(1);
});
