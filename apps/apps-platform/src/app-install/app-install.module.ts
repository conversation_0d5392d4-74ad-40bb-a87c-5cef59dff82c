import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  App,
  AppInstallation,
  AppInstallationRepository,
  AppRepository,
  CachedAppInstallationsRepository,
  CachedAppRepository,
} from "@repo/thena-platform-entities";
import { CommonModule } from "../common/common.module";
import { WebhookValidationService } from "../common/webhook-validation.service";
import { GrpcModule } from "../grpc/grpc.module";
import { TeamManagementService } from "../grpc/services/add-to-teams.transaction.service";
import { SvixWebhookService } from "../webhook/Service/svix.webhook.service";
import { WebhookModule } from "../webhook/webhook.module";
import { AppInstallActionController } from "./controller/app.install.action.controller";
import { AppInstallTeamsController } from "./controller/app.install.teams.controller";
import { AppInstallUpdateConfigController } from "./controller/app.install.update.config.controller";
import { AppReinstallActionController } from "./controller/app.reinstallation.action.controller";
import { AppUninstallActionController } from "./controller/app.uninstall.action.controller";
import { AppInstallService } from "./services/app.install.action.service";
import { AppInstallTeamsService } from "./services/app.install.teams.service";
import { AppInstallUpdateConfigService } from "./services/app.install.update.config.service";
import { AppReinstallService } from "./services/app.reinstall.action.service";
import { AppUninstallService } from "./services/app.uninstall.action.service";
@Module({
  imports: [
    CommonModule,
    GrpcModule,
    ConfigModule,
    WebhookModule,
    TypeOrmModule.forFeature([
      AppRepository,
      App,
      AppInstallationRepository,
      AppInstallation,
    ]),
  ],
  controllers: [
    AppInstallActionController,
    AppUninstallActionController,
    AppReinstallActionController,
    AppInstallUpdateConfigController,
    AppInstallTeamsController,
  ],
  providers: [
    // Repositories
    AppRepository,
    AppInstallationRepository,

    // Cached Repositories
    CachedAppRepository,
    CachedAppInstallationsRepository,

    // Services
    AppInstallService,
    AppUninstallService,
    AppReinstallService,
    WebhookValidationService,
    SvixWebhookService,
    AppInstallUpdateConfigService,
    AppInstallTeamsService,
    TeamManagementService,
  ],
  exports: [AppInstallService],
})
export class AppInstallModule {}
