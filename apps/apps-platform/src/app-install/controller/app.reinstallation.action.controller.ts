import { Body, Controller, Post, UseGuards } from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { RequireScopes } from "src/auth/decorators/scopes.decorator";
import { ScopeGuard } from "src/auth/gaurds/scopes.gaurd";
import { CurrentUser } from "../../auth/decorators/user.decorator";
import { InstallAppDto } from "../dtos/install-app.dto";
import {
  ActivityValidationException,
  BotInstallationException,
  EventValidationException,
  InstallationSaveException,
} from "../exceptions/app-install.exceptions";
import { AppReinstallService } from "../services/app.reinstall.action.service";

@ApiTags("App Reinstallation")
@ApiBearerAuth("x-api-key")
@Controller("apps/reinstall")
export class AppReinstallActionController {
  constructor(private readonly appReinstallService: AppReinstallService) {}

  @Post()
  @UseGuards(ScopeGuard)
  @RequireScopes("app.install")
  @ApiResponse({
    status: 400,
    description: "Bad Request - Invalid input parameters",
  })
  @ApiResponse({
    status: 422,
    description: "Bot Installation Failed",
    type: BotInstallationException,
  })
  @ApiResponse({
    status: 422,
    description: "Activity Registration Failed",
    type: ActivityValidationException,
  })
  @ApiResponse({
    status: 422,
    description: "Event Registration Failed",
    type: EventValidationException,
  })
  @ApiResponse({
    status: 422,
    description: "Reinstallation Save Failed",
    type: InstallationSaveException,
  })
  @ApiResponse({ status: 500, description: "Internal Server Error" })
  @ApiOperation({
    summary: "Reinstall an app",
    description: "Reinstall an app for the given teams.",
  })
  async installApp(
    @Body() appData: InstallAppDto,
    @CurrentUser() user: CurrentUser,
  ) {
    return await this.appReinstallService.reinstallApp(appData, user);
  }
}
