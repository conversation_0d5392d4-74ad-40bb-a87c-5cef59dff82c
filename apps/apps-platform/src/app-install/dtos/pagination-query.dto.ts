import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsN<PERSON>ber, IsOptional } from "class-validator";

export class PaginationQueryDto {
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    description: "Page number (starts from 1)",
    example: 1,
    required: false,
    default: 1,
  })
  page?: number = 1;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  @ApiProperty({
    description: "Number of items per page",
    example: 10,
    required: false,
    default: 10,
  })
  limit?: number = 10;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => Boolean(value))
  @ApiProperty({
    description: "Include privileged apps",
    example: false,
    required: false,
  })
  includePrivileged?: boolean = false;
}
