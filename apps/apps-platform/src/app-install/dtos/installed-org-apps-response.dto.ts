import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";

export class InstalledAppDto {
  @Expose()
  @ApiProperty({
    description: "Unique identifier of the installed app",
    example: "app_123xyz",
  })
  appId: string;

  @Expose()
  @ApiProperty({
    description: "Name of the installed app",
    example: "Thena Hub",
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: "Team ID where the app is installed",
    example: "TGGA0557MV",
  })
  teamId: string;

  @Expose()
  @ApiProperty({
    description: "Description of the installed app",
    example: "Integration with Thena Hub for ticket management",
  })
  description: string;

  @Expose()
  @ApiProperty({
    description: "App manifest",
    example: "{}",
  })
  appManifest: Record<string, any>;

  @Expose()
  @ApiProperty({
    description: "App manifest",
    example: "{}",
  })
  appConfiguration: Record<string, any>;

  @Expose()
  @ApiProperty({
    description: "Installation date",
    example: "2024-03-15T10:00:00Z",
  })
  installedBySub: string;

  @Expose()
  @ApiProperty({
    description: "Installation date",
    example: "2024-03-15T10:00:00Z",
  })
  installedByEmail: string;

  @Expose()
  @ApiProperty({
    description: "Last update date",
    example: "2024-03-15T10:00:00Z",
  })
  updatedAt: Date;

  @Expose()
  @ApiProperty({
    description: "Bot user ID",
    example: "B1234567890",
  })
  botUserId: string;
}

export class InstalledAppsResponseDto {
  @Expose()
  @ApiProperty({
    description: "Organization ID",
    example: "ESS2VQQKUZ",
  })
  organizationId: string;

  @Expose()
  @ApiProperty({
    description: "Total number of installed apps",
    example: 5,
  })
  totalCount: number;

  @Expose()
  @ApiProperty({
    description: "Current page number",
    example: 1,
  })
  currentPage: number;

  @Expose()
  @ApiProperty({
    description: "Total number of installed apps",
    example: 5,
  })
  totalPages: number;

  @Expose()
  @Type(() => InstalledAppDto)
  @ApiProperty({
    description: "List of installed apps",
    type: [InstalledAppDto],
  })
  apps: InstalledAppDto[];
}
