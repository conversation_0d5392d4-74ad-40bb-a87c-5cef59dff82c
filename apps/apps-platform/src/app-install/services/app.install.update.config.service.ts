import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ILogger } from "@repo/nestjs-commons/logger";
import { CurrentUser } from "@repo/nestjs-commons/utils";
import { workflows } from "@repo/shared-proto/dist/generated/src";
import {
  AppInstallationRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { AppManifest } from "../../app-manifest/interfaces/app-manifest.interface";
import { WorkflowGrpcClient } from "../../grpc/client/workflow-register-events.client.grpc";
import {
  ALLOWED_EVENT_TYPES,
  ALLOWED_FILTER_TYPES,
} from "../../webhook/Interfaces/webhook.svix.interface";
import { SvixWebhookService } from "../../webhook/Service/svix.webhook.service";
import {
  UpdateInstallationConfigDto,
  UpdateInstallationConfigResponseDto,
} from "../dtos/update-installation-config.dto";
import { AppInstallationConfiguration } from "../interfaces/app-install.interface";
import { validateAppConfiguration } from "../utils/validate-app-configuration.utils";
import {
  safeJsonStringify,
  sanitizeString,
  validateHttpMethod,
} from "../utils/validation.utils";

@Injectable()
export class AppInstallUpdateConfigService {
  constructor(
    private readonly appInstallationRepository: AppInstallationRepository,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly transactionService: TransactionService,
    private readonly workflowGrpcClient: WorkflowGrpcClient,
    private readonly svixWebhookService: SvixWebhookService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Process an activity definition with the new configuration values
   */
  private processActivityDefinition(
    activity: any,
    configuration: Record<string, any>,
    installationId: string,
  ): any {
    const processHeaders = (headers: Record<string, string>) => {
      const processed: Record<string, string> = {};

      Object.entries(headers).forEach(([key, value]) => {
        if (value.startsWith("${configuration.requiredSettings.")) {
          const match = value.match(
            /\${configuration\.requiredSettings\.([^}]+)}/,
          );
          if (match) {
            const [, settingKey] = match;
            const settingObject = configuration?.required_settings?.find(
              (setting) => settingKey in setting,
            );
            processed[key] = settingObject?.[settingKey] || value;
          } else {
            processed[key] = value;
          }
        } else if (value.startsWith("${configuration.optionalSettings.")) {
          const match = value.match(
            /\${configuration\.optionalSettings\.([^}]+)}/,
          );
          if (match) {
            const [, settingKey] = match;
            const settingObject = configuration?.optional_settings?.find(
              (setting) => settingKey in setting,
            );
            processed[key] = settingObject?.[settingKey] || value;
          } else {
            processed[key] = value;
          }
        } else {
          processed[key] = value;
        }
      });

      return processed;
    };

    return {
      activityName: sanitizeString(activity.name),
      description: sanitizeString(activity.description),
      identifier: `${sanitizeString(activity.name)}-${installationId}`,
      requestSchema: safeJsonStringify({
        type: "object",
        properties: activity.request_schema,
        required: Object.keys(activity.request_schema || {}),
      }),
      responseSchema: safeJsonStringify({
        type: "object",
        properties: activity.response_schema,
        required: Object.keys(activity.response_schema || {}),
      }),
      connectionDetails: {
        transport: "HTTP",
        httpConfig: {
          url: sanitizeString(activity.http_config.endpoint_url),
          method: validateHttpMethod(activity.http_config.httpVerb),
          headers: processHeaders(activity.http_config.headers),
        },
      },
      isCompensable: false,
      teamIds: [],
      metadata: safeJsonStringify({
        actual_data: {
          name: activity.name,
          description: activity.description,
          http_config: activity.http_config,
        },
      }),
    };
  }

  /**
   * Update activities with new configuration and register via gRPC
   */
  private async registerWorkflowActivitiesWithUpdatedConfig(
    manifest: AppManifest,
    organizationId: string,
    installationId: string,
    configuration: AppInstallationConfiguration,
  ): Promise<void> {
    if (!manifest.activities || !Array.isArray(manifest.activities)) {
      this.logger.warn(
        "No activities found in manifest or invalid activities format",
      );
      return;
    }

    try {
      // Process each activity with the new configuration
      const processedActivities = manifest.activities.map((activity) =>
        this.processActivityDefinition(activity, configuration, installationId),
      );

      // Register activities via gRPC
      await this.workflowGrpcClient.registerActivities(
        {
          source: workflows.Source.REGISTERED_APP,
          organizationId: organizationId,
          activities: processedActivities,
        },
        {
          bypass_auth: "true", // TODO: Remove this post the change for bot validation in auth guard
        },
      );

      this.logger.log(
        `Successfully registered ${processedActivities.length} activities`,
      );
    } catch (error) {
      this.logger.error(`Failed to update activities: ${error}`);
      throw new BadRequestException("Failed to update activities");
    }
  }

  /**
   * Updates the configuration of an app installation
   * @param installationId ID of the installation to update
   * @param configData New configuration data
   */
  async updateInstallationConfig(
    config: UpdateInstallationConfigDto,
    user: CurrentUser,
  ): Promise<UpdateInstallationConfigResponseDto> {
    this.logger.log(
      `Updating installation config for installationId: ${config.installationId}`,
    );

    const installation = await this.appInstallationRepository.findByCondition({
      where: {
        uid: config.installationId,
        organizationId: user.orgUid,
      },
      relations: ["app"],
    });

    if (!installation) {
      throw new NotFoundException(
        `Installation with id ${config.installationId} not found`,
      );
    }

    // Get the app manifest to validate against
    const appManifest = installation.app.manifest;
    if (!appManifest || !appManifest.configuration) {
      throw new BadRequestException(
        "App manifest or configuration schema not found",
      );
    }

    const isvalidConfiguration = validateAppConfiguration(
      appManifest.configuration,
      config.configuration,
    );

    if (!isvalidConfiguration) {
      throw new BadRequestException("Invalid configuration");
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      const updateInstallation =
        await this.appInstallationRepository.updateWithTxn(
          txnContext,
          { uid: config.installationId },
          {
            configuration: config.configuration as Record<string, any>,
            updatedAt: new Date(),
            installedBySub: user.uid,
            installedByEmail: user.email,
          },
        );

      // update the activities in the workflows
      await this.registerWorkflowActivitiesWithUpdatedConfig(
        appManifest as AppManifest,
        installation.organizationId,
        installation.app.uid,
        config.configuration,
      );

      // Send the change event to the app
      await this.svixWebhookService.sendMessage(installation.app.uid, {
        eventType: ALLOWED_FILTER_TYPES.INSTALLATION,
        payload: {
          installation_id: installation.uid,
          team_ids: installation.allowedTeamIds,
          organisation_id: user.orgUid,
          application_id: installation.app.uid,
          application_name: installation.app.name,
          application_metadata: installation.app?.manifest?.metadata || {},
          bot_id: installation.botUserId,
          bot_token: "**REDACTED**",
          configuration: config.configuration,
          updated_by: user.uid,
          updated_at: new Date().toISOString(),
          event_type: ALLOWED_EVENT_TYPES.CONFIGURATION_UPDATE,
        },
        channels: [this.configService.get("NODE_ENV")],
      });
      this.logger.log("Installation event published successfully");

      return updateInstallation;
    });

    return {
      installationId: config.installationId,
      configuration: config.configuration,
    };
  }
}
