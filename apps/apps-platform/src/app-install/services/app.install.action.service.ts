import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from "@nestjs/common";
import { CurrentUser } from "@repo/nestjs-commons/utils";
import {
  AppInstallationRepository,
  AppRepository,
  CachedAppRepository,
  InstallationStatus,
  TransactionService,
} from "@repo/thena-platform-entities";
import {
  ALLOWED_EVENT_TYPES,
  ALLOWED_FILTER_TYPES,
} from "../../webhook/Interfaces/webhook.svix.interface";
import { AppInstallResponseDto } from "../dtos/install-app-response.dto";
import { InstallAppDto } from "../dtos/install-app.dto";
import {
  InstallationState,
  ManifestConfig,
  ManifestEvents,
} from "../interfaces/app-install.interface";

/**
 * GRPC Clients
 */
import { workflows } from "@repo/shared-proto/dist/generated/src";
import { UserType } from "@repo/shared-proto/dist/generated/src/proto/users/users";
import { In } from "typeorm";
import { BotInstallationGrpcClient } from "../../grpc/client/bot-installation.client.grpc";
import { WorkflowGrpcClient } from "../../grpc/client/workflow-register-events.client.grpc";

/**
 * Custom exceptions
 */
import {
  ActivityValidationException,
  AppInstallBaseException,
  BotInstallationException,
  EventValidationException,
  InstallationSaveException,
} from "../exceptions/app-install.exceptions";

/**
 * Validation utils
 */
import { SvixWebhookService } from "../../webhook/Service/svix.webhook.service";
import {
  isValidEventSchema,
  isValidHttpConfig,
  safeJsonStringify,
  sanitizeHeaders,
  sanitizeString,
  validateHttpMethod,
} from "../utils/validation.utils";

import { ConfigService } from "@nestjs/config";
import { ILogger } from "@repo/nestjs-commons/logger";
import { plainToClass } from "class-transformer";
import { ValidationFailedException } from "../../exceptions/validation-failed-exception";
import { InstalledAppsResponseDto } from "../dtos/installed-org-apps-response.dto";
import { PaginationQueryDto } from "../dtos/pagination-query.dto";
import { processLiquidTags } from "../utils/liquid-tag-validator.utils";
import { validateAppConfiguration } from "../utils/validate-app-configuration.utils";
/**
 * Service responsible for managing app installation process.
 * Implements a saga pattern for distributed transactions with compensation.
 *
 * Installation Steps:
 * 1. Validate app and check for existing installations
 * 2. Create bot installation user
 * 3. Register activities
 * 4. Register events
 * 5. Save installation records
 *
 * If any step fails, the compensation mechanism will rollback completed steps.
 */
@Injectable()
export class AppInstallService {
  constructor(
    private readonly appRepository: AppRepository,
    private readonly cachedAppRepository: CachedAppRepository,
    private readonly appInstallationRepository: AppInstallationRepository,
    private readonly transactionService: TransactionService,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly botInstallationGrpcClient: BotInstallationGrpcClient,
    private readonly workflowGrpcClient: WorkflowGrpcClient,
    private readonly svixWebhookService: SvixWebhookService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Installs an app for specified teams with full compensation handling.
   *
   * @param appData - Installation request data containing appId and teamIds
   * @param user - Current user performing the installation
   * @returns Promise<InstallAppResponseDto> - Installation result
   * @throws BadRequestException if validation fails
   * @throws InternalServerErrorException if installation steps fail
   */
  async installApp(
    appData: InstallAppDto,
    user: CurrentUser,
  ): Promise<AppInstallResponseDto> {
    const installationState: InstallationState = {
      botCreated: false,
      activitiesRegistered: false,
      eventsRegistered: false,
      installationRecorded: false,
      botResponse: null as any,
      app: null as any,
      activityResponse: null as any,
      eventResponse: null as any,
    };
    try {
      // Step 1: Validate app and check existing installations
      await this.validateInstallation(appData, user, installationState);

      // Step 2: Create bot installation user
      await this.createBotInstallation(appData, user, installationState);

      // Step 3: Register activities
      if (installationState.app.manifest.activities?.length > 0) {
        await this.registerActivities(user.orgUid, installationState, appData);
      } else {
        this.logger.log("No activities found in manifest");
        installationState.activitiesRegistered = true;
      }

      // Step 4: Register events
      if (
        installationState.app.manifest.events?.publish.length > 0 ||
        installationState.app.manifest.events?.subscribe.length > 0
      ) {
        await this.registerEvents(user.orgUid, installationState);
      } else {
        this.logger.log("No publish events found in manifest");
        installationState.eventsRegistered = true;
      }

      // Step 5: Save installation records
      await this.saveInstallationRecords(appData, user, installationState);
      return this.createSuccessResponse(installationState, appData, user);
    } catch (error) {
      // Special handling for ValidationFailedException
      if (error instanceof ValidationFailedException) {
        throw error; // Pass through without modification
      }
      // First attempt compensation
      await this.compensate(installationState, appData.teamIds);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Validates the installation request and checks for existing installations.
   * @param appData Installation request data
   * @param state Current installation state
   * @throws BadRequestException if validation fails
   */
  private async validateInstallation(
    appData: InstallAppDto,
    user: CurrentUser,
    state: any,
  ): Promise<void> {
    const app = await this.appRepository.findByCondition({
      where: { uid: appData.appId },
    });

    if (!app) {
      throw new BadRequestException(
        `App for the appId ${appData.appId} not found.`,
      );
    }
    state.app = app;

    // Validate configuration against manifest
    const manifestConfig: ManifestConfig = app.manifest?.configuration;
    if (!manifestConfig) {
      throw new BadRequestException("App manifest configuration is missing");
    }

    const installationConfig = appData?.appConfiguration;

    if (manifestConfig.required_settings.length > 0) {
      if (!installationConfig?.required_settings) {
        // Get the keys of required settings from the manifest
        const requiredKeys = manifestConfig.required_settings.map(
          (setting) => setting.key,
        );

        // Format error messages in exact class-validator format
        const validationErrors = [
          "appConfiguration.required_settings should not be empty",
          `appConfiguration.required_settings must include all required settings: [${requiredKeys.join(
            ", ",
          )}]`,
        ];

        // For each missing setting, add a specific error message
        for (const key of requiredKeys) {
          validationErrors.push(
            `appConfiguration.required_settings should contain setting with key "${key}"`,
          );
        }

        throw new ValidationFailedException(validationErrors);
      }
    }

    /**
     * Validate the configuration against the manifest
     */

    const isvalidConfiguration = validateAppConfiguration(
      manifestConfig,
      installationConfig,
    );

    if (!isvalidConfiguration) {
      throw new BadRequestException("Invalid app configuration");
    }

    const existingInstallations =
      await this.appInstallationRepository.findByCondition({
        where: {
          appId: app.id,
          organizationId: user.orgUid,
          status: InstallationStatus.ACTIVE,
        },
      });

    if (existingInstallations != null) {
      throw new InstallationSaveException(
        "App is already installed for one or more of the specified teams.",
      );
    }
  }

  /**
   * Creates bot installation user.
   */
  private async createBotInstallation(
    appData: InstallAppDto,
    user: CurrentUser,
    state: InstallationState,
  ): Promise<void> {
    try {
      const avatarUrl = state.app?.manifest?.app?.icons?.small;
      state.botResponse =
        await this.botInstallationGrpcClient.createBotInstallationUser(
          {
            name: state.app.name,
            appId: state.app.id,
            organizationId: state.app.organizationId,
            teamIds: appData.teamIds,
            userType: UserType.BOT_USER,
            avatarUrl,
          },
          {
            user_id: user.uid,
            org_id: user.orgUid,
          },
        );

      if (
        !state.botResponse.appSecretKey ||
        !state.botResponse.appKey ||
        !state.botResponse.uid
      ) {
        throw new BotInstallationException("Invalid bot installation response");
      }

      state.botCreated = true;
    } catch (error) {
      this.logger.error("Bot installation failed", error);
      const errorMessage = error.message || error.toString();
      const formattedError = errorMessage.includes("UNKNOWN")
        ? errorMessage.split("UNKNOWN:")[1]?.trim() || errorMessage
        : errorMessage;
      throw new BotInstallationException(
        `Unable to create bot user: ${formattedError}`,
      );
    }
  }

  /**
   * Registers activities for the workflow.
   */
  private async registerActivities(
    organizationId: string,
    state: InstallationState,
    appData: InstallAppDto,
  ): Promise<void> {
    try {
      const activityData = this.getActivityDefinitions(state, appData);
      state.activityResponse = await this.workflowGrpcClient.registerActivities(
        {
          source: workflows.Source.REGISTERED_APP,
          activities: activityData,
          organizationId,
        },
        {
          bypass_auth: "true", // TODO: Remove this post the change for bot validation in auth guard
        },
      );
      state.activitiesRegistered = true;
    } catch (error) {
      const errorMessage = error.message || error.toString();
      const formattedError = errorMessage.includes("UNKNOWN")
        ? errorMessage.split("UNKNOWN:")[1]?.trim() || errorMessage
        : errorMessage;
      throw new ActivityValidationException(
        `Unable to register activities: ${formattedError}`,
      );
    }
  }

  /**
   * Registers events for the workflow.
   */
  private async registerEvents(
    organizationId: string,
    state: InstallationState,
  ): Promise<void> {
    const eventArray = this.getEventDefinitions(state);
    try {
      state.eventResponse = await this.workflowGrpcClient.registerEvents(
        {
          source: workflows.Source.REGISTERED_APP,
          events: eventArray,
          organizationId,
        },
        {
          bypass_auth: "true", // TODO: Remove this post the change for bot validation in auth guard
        },
      );
      state.eventsRegistered = true;
    } catch (error) {
      const errorMessage = error.message || error.toString();
      const formattedError = errorMessage.includes("UNKNOWN")
        ? errorMessage.split("UNKNOWN:")[1]?.trim() || errorMessage
        : errorMessage;
      throw new EventValidationException(
        `Unable to register events: ${formattedError}`,
      );
    }
  }

  /**
   * Saves installation records in a transaction.
   */

  private async saveInstallationRecords(
    appData: InstallAppDto,
    user: CurrentUser,
    state: InstallationState,
  ): Promise<void> {
    try {
      await this.transactionService.runInTransaction(async (txnContext) => {
        if (!state.app?.id) {
          throw new AppInstallBaseException(
            "App state is invalid",
            HttpStatus.BAD_REQUEST,
          );
        }

        const installation = this.appInstallationRepository.create({
          appId: state.app.id,
          allowedTeamIds: appData.teamIds,
          installedBySub: user.uid,
          installedByEmail: user.email,
          status: InstallationStatus.ACTIVE,
          configuration: appData.appConfiguration,
          organizationId: user.orgUid,
          botUserId: state.botResponse.uid,
        });

        await this.appRepository.updateWithTxn(
          txnContext,
          { id: state.app.id },
          { installationCount: state.app.installationCount + 1 },
        );

        await this.appInstallationRepository.saveWithTxn(
          txnContext,
          installation,
        );

        state.installationRecorded = true;
      });
    } catch (error) {
      this.logger.error("Installation records save failed", error);
      state.installationRecorded = false;
      throw new InstallationSaveException(
        `Failed to save installation records: ${error.message}`,
      );
    }
  }

  /**
   * Compensates (rollbacks) completed steps in case of failure.
   */
  private async compensate(state: any, teamIds: Array<string>): Promise<void> {
    this.logger.log("Starting compensation process");

    if (state.installationRecorded) {
      try {
        await this.transactionService.runInTransaction(async (txnContext) => {
          // Rollback installation records
          await this.appInstallationRepository.updateWithTxn(
            txnContext,
            {
              appId: state.app.id,
              allowedTeamIds: In(teamIds),
              status: InstallationStatus.ACTIVE,
            },
            {
              status: InstallationStatus.FAILED,
              updatedAt: new Date(),
            },
          );
        });
        this.logger.log(
          `Successfully rolled back installation records for team ${teamIds}`,
        );
      } catch (error) {
        this.logger.error("Failed to rollback installation records", error);
      }
    }

    if (state.eventsRegistered) {
      try {
        await this.workflowGrpcClient.compensateRegisterEvents(
          state.eventResponse.transactionId,
          {
            bypass_auth: "true", // TODO: Remove this post the change for bot validation in auth guard
          },
        );
        this.logger.log("Successfully rolled back events");
      } catch (error) {
        this.logger.error("Failed to deregister events", error);
      }
    }

    if (state.activitiesRegistered) {
      try {
        await this.workflowGrpcClient.compensateRegisterActivities(
          state.activityResponse.transactionId,
          {
            bypass_auth: "true", // TODO: Remove this post the change for bot validation in auth guard
          },
        );
        this.logger.log("Successfully rolled back activities");
      } catch (error) {
        this.logger.error("Failed to deregister activities", error);
      }
    }

    if (state.botCreated && state.botResponse) {
      try {
        await this.botInstallationGrpcClient.rollbackBotInstallationUser(
          {
            id: state.botResponse.id,
            transactionId: state.botResponse.uid,
            reason: "Installation failed",
            teamIds,
            organizationId: state.app.organizationId,
          },
          {
            user_id: state.botResponse.id,
            org_id: state.app.organizationId,
          },
        );
        this.logger.log("Successfully rolled back bot installation");
      } catch (error) {
        this.logger.error("Failed to rollback bot installation", error);
      }
    }
  }
  /**
   * Creates success response object.
   */
  private async createSuccessResponse(
    installationData: InstallationState,
    appData: InstallAppDto,
    user: CurrentUser,
  ): Promise<AppInstallResponseDto> {
    await this.publishInstallationEvent(installationData, appData, user);
    return plainToClass(
      AppInstallResponseDto,
      {
        app_name: installationData.app.name,
        app_identifier: installationData.app.uid,
        bot_id: installationData.botResponse.uid,
        bot_token: installationData.botResponse.appSecretKey,
        installed_by_user_id: user.uid,
        installed_by_email: user.email,
        installed_by_org_id: user.orgUid,
        app_installed_for_teams: appData.teamIds,
        metadata: installationData.app?.manifest?.metadata || {},
      },
      { excludeExtraneousValues: true },
    );
  }

  /**
   * Returns activity definitions for workflow registration.
   */
  private getActivityDefinitions(
    state: InstallationState,
    appData: InstallAppDto,
  ): workflows.Activity[] {
    try {
      // If no manifest or activities, return empty array
      if (!state?.app?.manifest?.activities) {
        this.logger.warn("No activities found in manifest");
        return [];
      }

      const activities = state.app.manifest.activities;

      if (!Array.isArray(activities)) {
        this.logger.error("Activities in manifest is not an array");
        throw new ActivityValidationException(
          "Invalid activities format in manifest",
        );
      }

      // Extract installation data for liquid tag processing
      const installationData = {
        bot_token: state.botResponse?.appKey,
        bot_id: state.botResponse?.uid,
        app_name: state.app?.name,
        application_id: state.app?.uid,
        organization_id: state.app?.organizationId,
        team_ids: appData?.teamIds,
      };

      // Extract configuration data from settings
      const configurationData = {
        requiredSettings: {},
        optionalSettings: {},
      };

      // Process required settings from appData configuration
      if (
        appData.appConfiguration?.required_settings &&
        Array.isArray(appData.appConfiguration.required_settings)
      ) {
        appData.appConfiguration.required_settings.forEach((settingObj) => {
          // Each object has a single key which is the setting name
          const key = Object.keys(settingObj)[0];
          if (key) {
            configurationData.requiredSettings[key] = settingObj[key];
          }
        });
      }

      // Process optional settings from appData configuration
      if (
        appData.appConfiguration?.optional_settings &&
        Array.isArray(appData.appConfiguration.optional_settings)
      ) {
        appData.appConfiguration.optional_settings.forEach((settingObj) => {
          const key = Object.keys(settingObj)[0];
          if (key) {
            configurationData.optionalSettings[key] = settingObj[key];
          }
        });
      }

      return activities
        .filter((activity) => {
          try {
            // Validate required fields
            const requiredFields = [
              "name",
              "description",
              "request_schema",
              "response_schema",
              "http_config",
            ];
            const missingFields = requiredFields.filter(
              (field) => !Object.prototype.hasOwnProperty.call(activity, field),
            );

            if (missingFields.length > 0) {
              this.logger.warn(
                `Activity missing required fields: ${missingFields.join(", ")}`,
              );
              return false;
            }

            // Validate http_config
            if (!isValidHttpConfig(activity.http_config)) {
              this.logger.warn(
                `Invalid HTTP config for activity: ${activity.name}`,
              );
              return false;
            }

            return true;
          } catch (error) {
            this.logger.warn(`Error validating activity: ${error.message}`);
            return false;
          }
        })
        .map((activity) => {
          // Create a deep copy to avoid modifying the original
          const activityCopy = JSON.parse(JSON.stringify(activity));
          // Process the activity to resolve liquid tags with our data
          const processedActivity = processLiquidTags(
            activityCopy,
            installationData,
            configurationData,
          );

          return {
            activityName: sanitizeString(processedActivity.name),
            description: sanitizeString(processedActivity.description),
            identifier: `${sanitizeString(processedActivity.name)}-${
              installationData.application_id
            }`,
            requestSchema: safeJsonStringify(processedActivity.request_schema),
            responseSchema: safeJsonStringify({
              type: processedActivity.response_schema.type,
              required: processedActivity.response_schema.required,
              properties: processedActivity.response_schema.properties,
            }),
            connectionDetails: {
              transport: "HTTP",
              httpConfig: {
                url: sanitizeString(processedActivity.http_config.endpoint_url),
                method: validateHttpMethod(
                  processedActivity.http_config.httpVerb,
                ),
                headers: sanitizeHeaders(processedActivity.http_config.headers),
              },
            },
            isCompensable: false,
            accessibleToTeam: installationData?.team_ids?.length > 0,
            teamIds: installationData?.team_ids || [],
            metadata: safeJsonStringify({
              actual_data: {
                name: processedActivity.name,
                description: processedActivity.description,
                http_config: processedActivity.http_config,
              },
            }),
          };
        });
    } catch (error) {
      this.logger.error("Error processing activity definitions", error);
      throw new InternalServerErrorException(
        `Failed to process activity definitions: ${error.message}`,
      );
    }
  }

  /**
   * Returns event definitions for workflow registration.
   */
  private getEventDefinitions(state: InstallationState): workflows.Event[] {
    try {
      // Check if manifest and events exist
      if (!state?.app?.manifest?.events) {
        this.logger.warn("No events found in manifest");
        return [];
      }

      const events = state.app.manifest.events as ManifestEvents;

      // We only care about publish events
      if (!events.publish) {
        this.logger.warn("No publish events found in manifest");
        return [];
      }

      if (!Array.isArray(events.publish)) {
        this.logger.error("Published events in manifest is not an array");
        throw new EventValidationException(
          "Invalid published events format in manifest",
        );
      }

      return events.publish
        .filter((event) => {
          try {
            // Validate required fields
            const requiredFields = ["event", "reason", "schema"];
            const missingFields = requiredFields.filter(
              (field) => !event[field],
            );

            if (missingFields.length > 0) {
              this.logger.warn(
                `Event missing required fields: ${missingFields.join(", ")}`,
              );
              return false;
            }

            // Validate schema structure
            if (!isValidEventSchema(event.schema)) {
              this.logger.warn(
                `Invalid schema structure for event: ${event.event}`,
              );
              return false;
            }

            return true;
          } catch (error) {
            this.logger.warn(`Error validating event: ${error.message}`);
            return false;
          }
        })
        .map((event) => ({
          eventName: sanitizeString(event.event),
          eventType: sanitizeString(event.event),
          description: sanitizeString(event.reason),
          schema: safeJsonStringify({
            type: "object",
            requried: ["message", "messageAttributes"],
            properties: {
              message: event.schema,
              messageAttributes: {
                type: "object",
                requried: [
                  "context_organization_id",
                  "context_user_id",
                  "context_user_type",
                ],
                properties: {
                  context_organization_id: {
                    type: "string",
                  },
                  context_user_id: {
                    type: "string",
                  },
                  context_user_type: {
                    type: "string",
                  },
                },
              },
            },
          }),
          accessibleToTeam: false,
          teamIds: null,
          metadata: safeJsonStringify({
            original_data: {
              event: event.event,
              reason: event.reason,
            },
          }),
        }));
    } catch (error) {
      this.logger.error("Error processing event definitions", error);
      throw new EventValidationException(
        `Failed to process event definitions: ${error.message}`,
      );
    }
  }

  /**
   * Publish installation event to SNS
   */
  private async publishInstallationEvent(
    installationData: InstallationState,
    appData: InstallAppDto,
    user: CurrentUser,
  ) {
    try {
      let formattedConfig;
      const app = await this.cachedAppRepository.findByCondition({
        where: { uid: installationData.app.uid },
      });

      const installationId =
        await this.appInstallationRepository.findByCondition({
          where: {
            appId: app.id,
            organizationId: user.orgUid,
            status: InstallationStatus.ACTIVE,
          },
          select: {
            uid: true,
          },
        });

      if (!installationId) {
        this.logger.error("Installation not found");
        throw new NotFoundException("Installation not found");
      }

      if (appData?.appConfiguration?.required_settings) {
        formattedConfig = {
          required_settings: appData.appConfiguration.required_settings.reduce(
            (acc, setting) => {
              const key = Object.keys(setting)[0];
              acc[key] = setting[key];
              return acc;
            },
            {},
          ),
          optional_settings: appData.appConfiguration.optional_settings?.reduce(
            (acc, setting) => {
              const key = Object.keys(setting)[0];
              acc[key] = setting[key];
              return acc;
            },
            {},
          ),
        };
      }

      await this.svixWebhookService.sendMessage(installationData.app.uid, {
        eventType: ALLOWED_FILTER_TYPES.INSTALLATION,
        payload: {
          installation_id: installationId.uid,
          team_ids: appData.teamIds,
          organization_id: user.orgUid,
          application_id: app.uid,
          application_name: app.name,
          application_metadata: installationData.app?.manifest?.metadata || {},
          bot_id: installationData.botResponse.uid,
          bot_token: installationData.botResponse.appSecretKey,
          configuration: formattedConfig,
          created_by: user.uid,
          created_at: new Date().toISOString(),
          event_type: ALLOWED_EVENT_TYPES.INSTALLATION,
        },
        channels: [this.configService.get("NODE_ENV")],
      });
      this.logger.log("Installation event published successfully");
    } catch (error) {
      this.logger.error(
        "Error encountered while publishing installation event to webhook",
        error,
      );
      throw error;
    }
  }

  /**
   * Get installed apps by organization
   */
  async getOrganizationInstalledApps(
    user: CurrentUser,
    { page = 1, limit = 10, includePrivileged = false }: PaginationQueryDto,
  ) {
    const { orgUid } = user;
    console.log("includePrivileged", includePrivileged);
    const installedApps =
      await this.appInstallationRepository.fetchPaginatedResults(
        {
          page: page - 1,
          limit,
        },
        {
          where: {
            organizationId: orgUid,
            status: InstallationStatus.ACTIVE,
            app: {
              isThenaPrivileged: includePrivileged,
            },
          },
          relations: ["app"],
          order: {
            createdAt: "DESC",
          },
        },
      );

    if (!installedApps || installedApps.total === 0) {
      return {
        organizationId: orgUid,
        totalCount: 0,
        currentPage: page,
        totalPages: 0,
        apps: [],
      };
    }

    return plainToClass(
      InstalledAppsResponseDto,
      {
        organizationId: orgUid,
        totalCount: installedApps.total,
        currentPage: page,
        totalPages: Math.ceil(installedApps.total / limit),
        apps: installedApps.results.map((installation) => ({
          appId: installation.app.uid,
          botUserId: installation.botUserId,
          name: installation.app.name,
          teamId: installation.allowedTeamIds,
          description: installation.app.description,
          status: installation.status,
          appManifest: installation.app.manifest,
          installedBySub: installation.installedBySub,
          installedByEmail: installation.installedByEmail,
          appConfiguration: installation.configuration,
          installedAt: installation.createdAt,
          updatedAt: installation.updatedAt,
        })),
      },
      { excludeExtraneousValues: true },
    );
  }
}
