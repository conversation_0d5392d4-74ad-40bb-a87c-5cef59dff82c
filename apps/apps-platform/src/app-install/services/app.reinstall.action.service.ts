import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ILogger } from "@repo/nestjs-commons/logger";
import { CurrentUser } from "@repo/nestjs-commons/utils";
import { workflows } from "@repo/shared-proto/dist/generated/src";
import { ContextUserType, SNSPublisherService } from "@repo/thena-eventbridge";
import {
  AppInstallationRepository,
  AppRepository,
  InstallationStatus,
  TransactionService,
} from "@repo/thena-platform-entities";
import { ValidationFailedException } from "../../exceptions/validation-failed-exception";
import { FetchApiKeyGrpcClient } from "../../grpc/client/fetch-api-key.client.grpc";
import { WorkflowGrpcClient } from "../../grpc/client/workflow-register-events.client.grpc";
import {
  ALLOWED_EVENT_TYPES,
  ALLOWED_FILTER_TYPES,
} from "../../webhook/Interfaces/webhook.svix.interface";
import { SvixWebhookService } from "../../webhook/Service/svix.webhook.service";
import { ReinstallAppDto } from "../dtos/reinstall-app.dto";
import { processLiquidTags } from "../utils/liquid-tag-validator.utils";
import { validateAppConfiguration } from "../utils/validate-app-configuration.utils";
import {
  isValidHttpConfig,
  safeJsonStringify,
  sanitizeString,
  validateHttpMethod,
} from "../utils/validation.utils";

// First, let's create an interface for the return type
interface ReinstallResult {
  message: string;
  details: {
    activitiesUpdated: number;
    eventsUpdated: number;
    appName: string;
    installation: {
      id: string;
      teamIds: string[];
      organizationId: string;
      applicationId: string;
      applicationSlug: string;
      botId: string;
      configuration: {
        required_settings: Record<string, any>;
        optional_settings: Record<string, any>;
      };
      updatedBy: string;
      updatedAt: string;
    };
  };
}

@Injectable()
export class AppReinstallService {
  constructor(
    private readonly appRepository: AppRepository,
    private readonly appInstallationRepository: AppInstallationRepository,
    private readonly svixWebhookService: SvixWebhookService,
    private readonly transactionService: TransactionService,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly configService: ConfigService,
    private readonly workflowGrpcClient: WorkflowGrpcClient,
    private readonly fetchApiKeyGrpcClient: FetchApiKeyGrpcClient,
    @Inject("WEBHOOK_SNS_PUBLISHER")
    private readonly snsService: SNSPublisherService,
  ) {}

  async reinstallApp(
    appData: ReinstallAppDto,
    user: CurrentUser,
  ): Promise<ReinstallResult> {
    /**
     * Find the app installation and the app details from the installation id
     * Validate the app configuration
     * Register the app events
     * Register the app activities
     * update the webhook endpoints if any changes made to it.
     */

    const appInstallation =
      await this.appInstallationRepository.findByCondition({
        where: {
          uid: appData.installationId,
          status: InstallationStatus.ACTIVE,
          reinstallRequired: true,
        },
      });

    if (!appInstallation) {
      throw new NotFoundException("App installation not found");
    }

    const app = await this.appRepository.findByCondition({
      where: {
        id: appInstallation.appId,
      },
    });

    if (!app) {
      throw new NotFoundException("App not found");
    }

    /**
     * 1. Get the user api key
     * 2. Validate the app configuration
     * 3. Register the app activities
     * 4. Register the app events
     * 5. Send Reinstall Webhook event on svix
     */

    const botToken = await this.fetchBotToken(appInstallation.botUserId, user);

    // Validate configuration changes against manifest requirements
    const manifestConfig = app.manifest.configuration;
    const existingConfig = appInstallation.configuration;
    const newConfig = appData.appConfiguration || existingConfig;

    // Use the helper method
    const validatedConfiguration = this.validateAppConfiguration(
      newConfig,
      manifestConfig,
    );

    // Track what we need to roll back in case of failure
    const rollbackState = {
      activitiesRegistered: false,
      eventsRegistered: false,
      activitiesTransactionId: null,
      eventsTransactionId: null,
    };

    let activitiesCount = 0;
    let eventsCount = 0;

    try {
      await this.transactionService.runInTransaction(async (txnContext) => {
        // 1. Update installation record
        await this.appInstallationRepository.updateWithTxn(
          txnContext,
          { id: appInstallation.id },
          {
            configuration: validatedConfiguration,
            reinstallRequired: false,
            updatedAt: new Date(),
            installedBySub: user.uid,
            installedByEmail: user.email,
          },
        );

        // 2. Re-register activities with updated configuration
        if (app.manifest.activities?.length > 0) {
          try {
            // Fetch the bot token key from the auth grpc service.

            // Extract installation data for liquid tag processing
            const installationData = {
              bot_token: botToken,
              bot_id: appInstallation.botUserId,
              app_name: app.name,
              application_id: app.uid,
              organization_id: user.orgUid,
              team_ids: appInstallation.allowedTeamIds,
            };

            // Extract configuration data from settings
            const configurationData = {
              requiredSettings: {},
              optionalSettings: {},
            };

            // Process required settings from configuration
            if (
              validatedConfiguration.required_settings &&
              Array.isArray(validatedConfiguration.required_settings)
            ) {
              validatedConfiguration.required_settings.forEach((settingObj) => {
                const key = Object.keys(settingObj)[0];
                if (key) {
                  configurationData.requiredSettings[key] = settingObj[key];
                }
              });
            }

            // Process optional settings from configuration
            if (
              validatedConfiguration.optional_settings &&
              Array.isArray(validatedConfiguration.optional_settings)
            ) {
              validatedConfiguration.optional_settings.forEach((settingObj) => {
                const key = Object.keys(settingObj)[0];
                if (key) {
                  configurationData.optionalSettings[key] = settingObj[key];
                }
              });
            }

            // Process and validate each activity
            const processedActivities = app.manifest.activities
              .filter((activity) => {
                try {
                  // Validate required fields
                  const requiredFields = [
                    "name",
                    "description",
                    "request_schema",
                    "response_schema",
                    "http_config",
                  ];
                  const missingFields = requiredFields.filter(
                    (field) =>
                      !Object.prototype.hasOwnProperty.call(activity, field),
                  );

                  if (missingFields.length > 0) {
                    this.logger.warn(
                      `Activity missing required fields: ${missingFields.join(
                        ", ",
                      )}`,
                    );
                    return false;
                  }

                  // Validate http_config
                  if (!isValidHttpConfig(activity.http_config)) {
                    this.logger.warn(
                      `Invalid HTTP config for activity: ${activity.name}`,
                    );
                    return false;
                  }

                  return true;
                } catch (error) {
                  this.logger.warn(
                    `Error validating activity: ${error.message}`,
                  );
                  return false;
                }
              })
              .map((activity) => {
                // Create a deep copy to avoid modifying the original
                const activityCopy = JSON.parse(JSON.stringify(activity));

                // Process the activity to resolve liquid tags with our data
                const processedActivity = processLiquidTags(
                  activityCopy,
                  installationData,
                  configurationData,
                );

                return {
                  activityName: sanitizeString(processedActivity.name),
                  description: sanitizeString(processedActivity.description),
                  identifier: `${sanitizeString(processedActivity.name)}-${
                    app.uid
                  }`,
                  requestSchema: safeJsonStringify(
                    processedActivity.request_schema,
                  ),
                  responseSchema: safeJsonStringify({
                    type: processedActivity.response_schema.type,
                    required: processedActivity.response_schema.required,
                    properties: processedActivity.response_schema.properties,
                  }),
                  connectionDetails: {
                    transport: "HTTP",
                    httpConfig: {
                      url: sanitizeString(
                        processedActivity.http_config.endpoint_url,
                      ),
                      method: validateHttpMethod(
                        processedActivity.http_config.httpVerb,
                      ),
                      headers: processedActivity.http_config.headers,
                    },
                  },
                  isCompensable: false,
                  accessibleToTeam: appInstallation.allowedTeamIds?.length > 0,
                  teamIds: appInstallation.allowedTeamIds || [],
                  metadata: safeJsonStringify({
                    actual_data: {
                      name: processedActivity.name,
                      description: processedActivity.description,
                      http_config: processedActivity.http_config,
                    },
                  }),
                };
              });

            // Register activities via gRPC
            if (processedActivities.length > 0) {
              const response = await this.workflowGrpcClient.registerActivities(
                {
                  source: workflows.Source.REGISTERED_APP,
                  organizationId: user.orgUid,
                  activities: processedActivities,
                },
                {
                  org_id: user.orgUid,
                  user_id: user.uid,
                },
              );

              rollbackState.activitiesRegistered = true;
              rollbackState.activitiesTransactionId = response.transactionId;
              activitiesCount = processedActivities.length;

              this.logger.log(
                `Re-registered ${processedActivities.length} activities for app ${app.uid}`,
              );
            } else {
              this.logger.warn(
                `No valid activities found to re-register for app ${app.uid}`,
              );
            }
          } catch (error) {
            this.logger.error(
              `Failed to re-register activities for app ${app.uid}: ${error.message}`,
              error.stack,
            );
            throw error; // Let the transaction handler catch this
          }
        }

        // 3. Re-register events with updated configuration
        if (
          app.manifest.events?.publish?.length > 0 ||
          app.manifest.events?.subscribe?.length > 0
        ) {
          try {
            // Process publish events
            const eventDefinitions = [];

            if (
              app.manifest.events.publish &&
              Array.isArray(app.manifest.events.publish)
            ) {
              for (const event of app.manifest.events.publish) {
                // Basic validation
                if (!event.event || !event.reason || !event.schema) {
                  this.logger.warn(
                    `Skipping event with missing required fields: ${JSON.stringify(
                      event,
                    )}`,
                  );
                  continue;
                }

                eventDefinitions.push({
                  eventName: sanitizeString(event.event),
                  eventType: sanitizeString(event.event),
                  description: sanitizeString(event.reason),
                  schema: safeJsonStringify({
                    type: "object",
                    required: ["message", "messageAttributes"],
                    properties: {
                      message: event.schema,
                      messageAttributes: {
                        type: "object",
                        required: [
                          "context_organization_id",
                          "context_user_id",
                          "context_user_type",
                        ],
                        properties: {
                          context_organization_id: {
                            type: "string",
                          },
                          context_user_id: {
                            type: "string",
                          },
                          context_user_type: {
                            type: "string",
                          },
                        },
                      },
                    },
                  }),
                  accessibleToTeam: appInstallation.allowedTeamIds?.length > 0,
                  teamIds: appInstallation.allowedTeamIds || [],
                  metadata: safeJsonStringify({
                    original_data: {
                      event: event.event,
                      reason: event.reason,
                    },
                  }),
                });
              }
            }

            // Register events if we found any
            if (eventDefinitions.length > 0) {
              const response = await this.workflowGrpcClient.registerEvents(
                {
                  source: workflows.Source.REGISTERED_APP,
                  events: eventDefinitions,
                  organizationId: user.orgUid,
                },
                {
                  org_id: user.orgUid,
                  user_id: user.uid,
                },
              );

              rollbackState.eventsRegistered = true;
              rollbackState.eventsTransactionId = response.transactionId;
              eventsCount = eventDefinitions.length;

              this.logger.log(
                `Re-registered ${eventDefinitions.length} events for app ${app.uid}`,
              );
            } else {
              this.logger.log(
                `No valid events found to register for app ${app.uid}`,
              );
            }
          } catch (error) {
            this.logger.error(`Failed to re-register events: ${error.message}`);
            throw error; // Let the transaction handler catch this
          }
        }
      });

      // After successful transaction, publish events
      await this.publishReinstallationEvent(
        app,
        appInstallation,
        user,
        activitiesCount,
        eventsCount,
        validatedConfiguration,
        botToken,
      );

      const formattedConfig = {
        required_settings: validatedConfiguration.required_settings?.reduce(
          (acc, setting) => {
            const key = Object.keys(setting)[0];
            acc[key] = setting[key];
            return acc;
          },
          {},
        ),
        optional_settings: validatedConfiguration.optional_settings?.reduce(
          (acc, setting) => {
            const key = Object.keys(setting)[0];
            acc[key] = setting[key];
            return acc;
          },
          {},
        ),
      };

      return {
        message: `Successfully reinstalled ${app.name}`,
        details: {
          activitiesUpdated: activitiesCount,
          eventsUpdated: eventsCount,
          appName: app.name,
          installation: {
            id: appInstallation.uid,
            teamIds: appInstallation.allowedTeamIds,
            organizationId: user.orgUid,
            applicationId: app.uid,
            applicationSlug: app.slug,
            botId: appInstallation.botUserId,
            configuration: formattedConfig,
            updatedBy: user.uid,
            updatedAt: new Date().toISOString(),
          },
        },
      };
    } catch (error) {
      // Handle rollback
      this.logger.error(
        `Error during reinstallation, performing rollback: ${error.message}`,
      );

      // Rollback activities if they were registered
      if (
        rollbackState.activitiesRegistered &&
        rollbackState.activitiesTransactionId
      ) {
        try {
          await this.workflowGrpcClient.compensateRegisterActivities(
            rollbackState.activitiesTransactionId,
            {
              org_id: user.orgUid,
              user_id: user.uid,
            },
          );
          this.logger.log("Successfully rolled back activities");
        } catch (rollbackError) {
          this.logger.error(
            `Failed to rollback activities: ${rollbackError.message}`,
            rollbackError.stack,
          );
          // Continue with other rollbacks even if this one fails
        }
      }

      // Rollback events if they were registered
      if (rollbackState.eventsRegistered && rollbackState.eventsTransactionId) {
        try {
          await this.workflowGrpcClient.compensateRegisterEvents(
            rollbackState.eventsTransactionId,
            {
              org_id: user.orgUid,
              user_id: user.uid,
            },
          );
          this.logger.log("Successfully rolled back events");
        } catch (rollbackError) {
          this.logger.error(
            `Failed to rollback events: ${rollbackError.message}`,
            rollbackError.stack,
          );
          // Continue with other rollbacks even if this one fails
        }
      }

      // Re-throw the original error after rollback
      throw error;
    }
  }

  private validateAppConfiguration(
    appConfiguration: any,
    manifestConfig: any,
  ): any {
    try {
      validateAppConfiguration(manifestConfig, appConfiguration);
      return appConfiguration;
    } catch (error) {
      if (error instanceof ValidationFailedException) {
        throw error; // Pass validation errors through
      }
      throw new BadRequestException("Invalid configuration for reinstallation");
    }
  }

  private async fetchBotToken(
    bot_user_id: string,
    user: CurrentUser,
  ): Promise<string> {
    try {
      const response = await this.fetchApiKeyGrpcClient.fetchApiKey(
        { botUserId: bot_user_id },
        {
          org_id: user.orgUid,
          user_id: user.uid,
        },
      );
      if (!response?.botToken) {
        throw new BadRequestException("Failed to fetch bot token");
      }
      return response.botToken;
    } catch (error) {
      throw new BadRequestException(
        `Failed to fetch bot token: ${error.message}`,
      );
    }
  }

  private async publishReinstallationEvent(
    app: any,
    appInstallation: any,
    user: CurrentUser,
    activitiesCount: number,
    eventsCount: number,
    configuration: any,
    botToken: string,
  ) {
    try {
      const formattedConfig = {
        required_settings: configuration.required_settings?.reduce(
          (acc, setting) => {
            const key = Object.keys(setting)[0];
            acc[key] = setting[key];
            return acc;
          },
          {},
        ),
        optional_settings: configuration.optional_settings?.reduce(
          (acc, setting) => {
            const key = Object.keys(setting)[0];
            acc[key] = setting[key];
            return acc;
          },
          {},
        ),
      };

      const payloadData = {
        application_id: app.uid,
        application_metadata: app.manifest?.metadata || {},
        application_name: app.name,
        application_slug: app.slug,
        bot_id: appInstallation.botUserId,
        bot_token: botToken,
        configuration: formattedConfig,
        event_type: ALLOWED_EVENT_TYPES.REINSTALLATION,
        activities_updated: activitiesCount,
        events_updated: eventsCount,
        installation_id: appInstallation.uid,
        organization_id: user.orgUid,
        team_ids: appInstallation.allowedTeamIds || [],
        updated_at: new Date().toISOString(),
        updated_by: user.uid,
      };

      // Send to Svix
      await this.svixWebhookService.sendMessage(app.uid, {
        eventType: ALLOWED_FILTER_TYPES.INSTALLATION,
        payload: payloadData,
        channels: [this.configService.get("NODE_ENV")],
      });
      this.logger.log("Reinstallation event published successfully");

      // Publish to SNS topic
      await this.snsService.publishSNSMessage({
        topicArn: this.configService.get("AWS_SNS_WEBHOOK_TOPIC_ARN"),
        subject: ALLOWED_EVENT_TYPES.REINSTALLATION,
        message: JSON.stringify(payloadData),
        messageAttributes: {
          event_name: ALLOWED_EVENT_TYPES.REINSTALLATION,
          event_id: appInstallation.uid,
          event_timestamp: Math.floor(Date.now() / 1000).toString(),
          context_user_id: user.uid,
          context_organization_id: user.orgUid,
          context_user_type: ContextUserType.BOT_USER,
        },
        messageGroupId: user.orgUid,
      });
      this.logger.log(
        `Reinstallation event published to SNS topic for app: ${app.uid}`,
      );
    } catch (error) {
      this.logger.error(
        "Error encountered while publishing reinstallation event",
        error,
      );
      throw error;
    }
  }
}
