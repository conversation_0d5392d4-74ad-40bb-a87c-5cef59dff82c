import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  AppInstallationRepository,
  AppRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { CurrentUser } from "../../auth/decorators/user.decorator";
import { SvixWebhookService } from "../../webhook/Service/svix.webhook.service";
import { InstallAppDto } from "../dtos/install-app.dto";

@Injectable()
export class AppReinstallService {
  constructor(
    private readonly appRepository: AppRepository,
    private readonly appInstallationRepository: AppInstallationRepository,
    private readonly svixWebhookService: SvixWebhookService,
    private readonly transactionService: TransactionService,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly configService: ConfigService,
  ) {}

  private validateConfigurationChanges(
    existingConfig: any,
    manifestConfig: any,
  ) {
    const configDiff = {
      missingRequired: [] as string[],
      newConfig: {
        required_settings: [...existingConfig.required_settings],
        optional_settings: [...existingConfig.optional_settings],
      },
    };

    // Check each required setting in manifest
    for (const setting of manifestConfig.required_settings) {
      const key = setting.key;
      const existingSetting = existingConfig.required_settings.find(
        (s: any) => key in s,
      );

      if (!existingSetting) {
        configDiff.missingRequired.push(key);
      }
    }

    return configDiff;
  }

  async reinstallApp(appData: InstallAppDto, _user: CurrentUser) {
    //TODO: @amit - Implement reinstallation logic.
    //TODO: check if scopes are updated so ask for updated bot token
    //TODO: Update the app events, activities, etc.
    // Updating the installation records.
    // fetching and firing the installation webhook

    // Check if the app is installed for the team and the status is active and reinstallation is required
    const appInfo = await this.appRepository.findByCondition({
      where: {
        uid: appData.appId,
      },
    });

    if (!appInfo) {
      throw new NotFoundException("App not found");
    }

    // for (const teamId of appData.teamIds) {
    //   const existingInstallation =
    //     await this.appInstallationRepository.findByCondition({
    //       where: {
    //         appId: appInfo.id,
    //         allowedTeamIds: [teamId],
    //         installedBySub: user.userSub,
    //         reinstallRequired: true,
    //       },
    //     });

    //   if (!existingInstallation) {
    //     throw new BadRequestException(
    //       "No active installation requiring reinstallation found",
    //     );
    //   }

    //   // Validate configuration changes against manifest requirements
    //   const configDiff = this.validateConfigurationChanges(
    //     existingInstallation.configuration,
    //     appInfo.manifest.configuration,
    //   );

    //   // If there are missing required settings, check if they're provided in appData
    //   if (configDiff.missingRequired.length > 0) {
    //     if (!appData.appConfiguration?.required_settings) {
    //       throw new BadRequestException({
    //         message: "Missing required configuration settings",
    //         required: configDiff.missingRequired,
    //       });
    //     }

    //     // Validate and add new settings
    //     for (const requiredKey of configDiff.missingRequired) {
    //       const settingFound = appData.appConfiguration.required_settings.find(
    //         (setting) => Object.keys(setting)[0] === requiredKey,
    //       );

    //       if (!settingFound) {
    //         throw new BadRequestException({
    //           message: `Missing required setting: ${requiredKey}`,
    //           required: configDiff.missingRequired,
    //         });
    //       }

    //       const value = settingFound[requiredKey];
    //       const manifestSetting =
    //         appInfo.manifest.configuration.required_settings.find(
    //           (s) => s.key === requiredKey,
    //         );

    //       // Validate type
    //       if (
    //         !value ||
    //         (typeof value !== manifestSetting.type &&
    //           manifestSetting.type !== "secret")
    //       ) {
    //         throw new BadRequestException(
    //           `Invalid type for "${requiredKey}". Expected ${
    //             manifestSetting.type
    //           }, got ${typeof value}`,
    //         );
    //       }

    //       // Add new setting to existing configuration
    //       existingInstallation.configuration.required_settings.push({
    //         [requiredKey]: value,
    //       });
    //     }

    //     // Update installation and send webhook in transaction
    //     await this.transactionService.runInTransaction(async (txnContext) => {
    //       // Update installation in database
    //       await this.appInstallationRepository.updateWithTxn(
    //         txnContext,
    //         { id: existingInstallation.id },
    //         {
    //           configuration: existingInstallation.configuration,
    //           reinstallRequired: false,
    //         },
    //       );

    //       // Format configuration for webhook
    //       const formattedConfig = {
    //         required_settings:
    //           existingInstallation.configuration.required_settings.reduce(
    //             (acc, setting) => {
    //               const key = Object.keys(setting)[0];
    //               acc[key] = setting[key];
    //               return acc;
    //             },
    //             {},
    //           ),
    //         optional_settings:
    //           existingInstallation.configuration.optional_settings?.reduce(
    //             (acc, setting) => {
    //               const key = Object.keys(setting)[0];
    //               acc[key] = setting[key];
    //               return acc;
    //             },
    //             {},
    //           ) || {},
    //       };

    //       // Send webhook notification
    //       await this.svixWebhookService.sendMessage(appInfo.uid, {
    //         eventType: ALLOWED_FILTER_TYPES.INSTALLATION,
    //         payload: {
    //           installation_id: existingInstallation.uid,
    //           teamId: existingInstallation.teamId,
    //           organizationId: user.organizationId,
    //           applicationId: appInfo.uid,
    //           botToken: user.userSub, //TODO: - replace with bot token.
    //           configuration: formattedConfig,
    //           created_by: user.userSub,
    //           created_at: new Date().toISOString(),
    //           xWebhookEvent: ALLOWED_FILTER_TYPES.INSTALLATION,
    //         },
    //         channels: [this.configService.get("NODE_ENV")],
    //       });

    //       this.logger.log(
    //         `Installation updated and webhook sent for app ${appInfo.uid}`,
    //       );
    //     });
    //   }
    // }

    return {
      message: "App reinstalled successfully",
    };
  }
}
