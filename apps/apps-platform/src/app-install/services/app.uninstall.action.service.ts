import {
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { CurrentUser } from "@repo/nestjs-commons/utils";
import {
  AppInstallationRepository,
  AppRepository,
  InstallationStatus,
  TransactionService,
} from "@repo/thena-platform-entities";
import { BotInstallationGrpcClient } from "../../grpc/client/bot-installation.client.grpc";
import { WorkflowGrpcClient } from "../../grpc/client/workflow-register-events.client.grpc";
import {
  ALLOWED_EVENT_TYPES,
  ALLOWED_FILTER_TYPES,
} from "../../webhook/Interfaces/webhook.svix.interface";
import { SvixWebhookService } from "../../webhook/Service/svix.webhook.service";
import { AppUninstallResponseDto } from "../dtos/uninstall-app-response.dto";
import { UninstallAppDto } from "../dtos/uninstall-app.dto";
/**
 * Service for uninstalling an app
 * Validate the uninstall request data
 * Check if the app is actually installed
 * Verify user permissions for uninstallation
 * Ensure all required teams/workspaces are specified
 * Delete all app data from the database
 * Send uninstallation events to the app
 * Cleanup any residual data or records
 *
 *
 * Remove bot tokens and credentials
 *
 * Deregister all event subscriptions
 * Remove webhook configurations
 * Clean up event handler registrations
 * Remove any persistent event listeners
 *
 * Log the uninstallation details
 * Record timestamp and user who performed uninstall
 * Update app status in database
 *
 * Notify relevant systems about uninstallation
 * Trigger any necessary webhooks
 */
@Injectable()
export class AppUninstallService {
  constructor(
    private readonly appRepository: AppRepository,
    private readonly appInstallationRepository: AppInstallationRepository,
    private readonly transactionService: TransactionService,
    private readonly workflowGrpcClient: WorkflowGrpcClient,
    private readonly svixWebhookService: SvixWebhookService,
    private readonly botInstallationGrpcClient: BotInstallationGrpcClient,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly configService: ConfigService,
    @Inject("Sentry") private readonly sentry: SentryService,
  ) {}

  async uninstallApp(
    appData: UninstallAppDto,
    user: CurrentUser,
  ): Promise<AppUninstallResponseDto> {
    const { appId } = appData;
    const { orgUid, uid, email } = user;

    const app = await this.appRepository.findByCondition({
      where: { uid: appId },
    });

    if (!app) {
      throw new NotFoundException(`App with identifier: ${appId} not found`);
    } else if (app.isActive !== true) {
      throw new NotFoundException(
        `App with identifier: ${appId} is not active`,
      );
    }

    const appInstallation =
      await this.appInstallationRepository.findByCondition({
        where: {
          appId: app.id,
          organizationId: orgUid,
          status: InstallationStatus.ACTIVE,
        },
      });

    if (!appInstallation) {
      throw new NotFoundException(
        `App installation with identifier: ${appId} not found`,
      );
    }

    // State to track our saga execution
    const processState = {
      dbUpdated: false,
      activitiesDeregistered: false,
      eventsDeregistered: false,
      botUninstalled: false,
      activitiesResponse: null as any,
      eventsResponse: null as any,
      botResponse: null as any,
    };
    const uninstalledAt = new Date();

    try {
      // Step 1: Update the database record
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.appInstallationRepository.updateWithTxn(
          txnContext,
          { id: appInstallation.id },
          {
            status: InstallationStatus.UNINSTALLED,
            uninstalledAt,
            uninstalledBySub: uid,
            uninstalledByEmail: email,
          },
        );

        processState.dbUpdated = true;
        this.logger.log(
          `PROCESS STATE:STEP 1: Database record updated for app ${appId}`,
        );
      });

      // Step 2: Deregister activities
      try {
        const uniqueIdentifiers = [];
        app.manifest.activities.forEach((activity) => {
          uniqueIdentifiers.push(`${activity.name}-${app.uid}`);
        });

        if (uniqueIdentifiers?.length > 0) {
          const unregisterPromises = uniqueIdentifiers.map((identifier) =>
            this.workflowGrpcClient.unregisterActivities(
              {
                identifier,
                organizationId: orgUid,
              },
              {
                org_id: orgUid,
                user_id: uid,
              },
            ),
          );
          const results = await Promise.all(unregisterPromises);
          processState.activitiesResponse = results;
        }
        processState.activitiesDeregistered = true;
        this.logger.log(
          `PROCESS STATE:STEP 2: Activities deregistered for app: ${appId}`,
        );
      } catch (error) {
        this.logger.error(
          `PROCESS STATE:STEP 2: Failed to deregister activities: ${error}`,
        );
        throw error;
      }

      // Step 3: Deregister events
      try {
        const uniqueIdentifiers = [];
        app.manifest?.events?.publish?.forEach((event) => {
          uniqueIdentifiers.push(event.event);
        });
        if (uniqueIdentifiers?.length > 0) {
          const unregisterPromises = uniqueIdentifiers.map((identifier) =>
            this.workflowGrpcClient.unregisterEvents(
              {
                eventType: identifier,
                organizationId: orgUid,
              },
              {
                org_id: orgUid,
                user_id: uid,
              },
            ),
          );
          const results = await Promise.all(unregisterPromises);
          processState.eventsResponse = results;
        }
        processState.eventsDeregistered = true;
        this.logger.log(
          `PROCESS STATE:STEP 3: Events deregistered for app: ${appId}`,
        );
      } catch (error) {
        this.logger.error(
          `PROCESS STATE:STEP 3: Failed to deregister events: ${error}`,
        );
        throw error;
      }

      // Step 4: Uninstall bot user
      try {
        await this.botInstallationGrpcClient.uninstallBotUser(
          {
            appId: app.id,
            organizationId: orgUid,
          },
          {
            user_id: appInstallation.botUserId,
            org_id: orgUid,
          },
        );

        processState.botUninstalled = true;
        this.logger.log(
          `PROCESS STATE:STEP 4: Bot user uninstalled for app: ${appId}`,
        );
        if (
          processState.activitiesDeregistered &&
          processState.eventsDeregistered &&
          processState.botUninstalled
        ) {
          await this.svixWebhookService.sendMessage(app.uid, {
            eventType: ALLOWED_FILTER_TYPES.INSTALLATION,
            payload: {
              installation_id: appInstallation.uid,
              team_ids: appInstallation?.allowedTeamIds,
              organization_id: user.orgUid,
              application_id: app.uid,
              application_name: app.name,
              application_metadata: app.manifest?.metadata || {},
              bot_id: appInstallation.botUserId,
              bot_token: "**REDACTED**",
              configuration: {},
              created_by: user.uid,
              created_at: new Date().toISOString(),
              event_type: ALLOWED_EVENT_TYPES.UNINSTALLATION,
            },
            channels: [this.configService.get("NODE_ENV")],
          });
        }
      } catch (error) {
        this.logger.error(
          `PROCESS STATE:STEP 4: Failed to uninstall bot user: ${error}`,
        );
        throw error;
      }

      return {
        httpStatus: HttpStatus.OK,
        status: InstallationStatus.UNINSTALLED,
        uninstalledAt,
        uninstalledBySub: uid,
        uninstalledByEmail: email,
        message: "App uninstalled successfully",
      };
    } catch (error) {
      await this.rollbackUninstallation(processState, orgUid, uid, appId);

      // Capture the original error
      this.captureSentryException(error, "app_uninstall_failure", appId);

      throw new InternalServerErrorException(error);
    }
  }

  /**
   * Rolls back any completed steps during app uninstallation.
   *
   * @param {any} processState - State tracking which steps were completed
   * @param {string} orgUid - Organization ID
   * @param {string} uid - User ID
   * @param {string} appId - Application ID
   * @returns {Promise<void>}
   */
  private async rollbackUninstallation(
    processState: any,
    orgUid: string,
    uid: string,
    appId: string,
  ): Promise<void> {
    try {
      if (
        processState.activitiesDeregistered &&
        processState.activitiesResponse
      ) {
        await this.workflowGrpcClient.compensateUnregisterActivities(
          processState?.activitiesResponse?.transactionId || "",
          {
            org_id: orgUid,
            user_id: uid,
          },
        );
        this.logger.log("Successfully rolled back activities deletion");
      }

      if (processState.eventsDeregistered && processState.eventsResponse) {
        await this.workflowGrpcClient.compensateUnregisterEvents(
          processState?.eventsResponse?.transactionId || "",
          {
            org_id: orgUid,
            user_id: uid,
          },
        );
        this.logger.log("Successfully rolled back events deletion");
      }

      // Add database rollback if needed
      if (processState.dbUpdated) {
        // Implement DB rollback if needed
      }
    } catch (error) {
      this.logger.error(`Failed to roll back uninstallation steps: ${error}`);
      this.captureSentryException(
        error,
        "app_uninstall_rollback_failure",
        appId,
      );
    }
  }

  /**
   * Captures an exception in Sentry with standard tagging for consistent alerts.
   *
   * @param {Error} error - The error to capture
   * @param {string} alertType - Alert type for Zenduty integration
   * @param {string} appId - Application ID
   */
  private captureSentryException(
    error: Error,
    alertType: string,
    appId: string,
  ): void {
    this.sentry.captureException(error, {
      tag: "APP_UNINSTALL",
      fn: "uninstallApp",
      severity: "critical",
      alert: alertType,
      appId,
    });
  }
}
