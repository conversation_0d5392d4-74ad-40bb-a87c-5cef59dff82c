import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { User } from "@repo/nestjs-commons/guards";
import { ILogger } from "@repo/nestjs-commons/logger";
import { CurrentUser } from "@repo/nestjs-commons/utils";
import {
  AppInstallationRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { WorkflowGrpcClient } from "../../grpc/client/workflow-register-events.client.grpc";
import { TeamManagementService } from "../../grpc/services/add-to-teams.transaction.service";
import {
  ALLOWED_EVENT_TYPES,
  ALLOWED_FILTER_TYPES,
} from "../../webhook/Interfaces/webhook.svix.interface";
import { SvixWebhookService } from "../../webhook/Service/svix.webhook.service";
import {
  AddTeamsToInstallationDto,
  AddTeamsToInstallationResponseDto,
  RemoveTeamsFromInstallationDto,
  RemoveTeamsFromInstallationResponseDto,
} from "../dtos/install-add-teams.dto";

@Injectable()
export class AppInstallTeamsService {
  constructor(
    private readonly appInstallationRepository: AppInstallationRepository,
    private readonly transactionService: TransactionService,
    private readonly teamManagementService: TeamManagementService,
    private readonly workflowGrpcClient: WorkflowGrpcClient,
    private readonly svixWebhookService: SvixWebhookService,
    private readonly configService: ConfigService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  async addAppToTeams(
    addTeamsToInstallationDto: AddTeamsToInstallationDto,
    user: CurrentUser,
  ): Promise<AddTeamsToInstallationResponseDto> {
    const appInstallation =
      await this.appInstallationRepository.findByCondition({
        where: {
          uid: addTeamsToInstallationDto.installationId,
          organizationId: user.orgUid,
        },
        relations: ["app"],
      });
    if (!appInstallation) {
      throw new NotFoundException("App installation not found");
    }

    if (appInstallation.app?.isAgentApp) {
      throw new BadRequestException("Agent apps cannot be added to teams");
    }

    try {
      await this.transactionService.runInTransaction(async (txnContext) => {
        // add teams to bot installation user
        await this.teamManagementService.addTeamsToBotInstallationUser(
          addTeamsToInstallationDto.teamIds,
          appInstallation.botUserId,
          user as unknown as User,
        );

        // Check if the app has activities
        const appHasActivities =
          appInstallation.app?.manifest?.activities?.length > 0;

        if (appHasActivities) {
          const activityIdentifiers = new Set<string>();
          appInstallation.app.manifest.activities.forEach((activity) => {
            activityIdentifiers.add(
              `${activity.name}-${appInstallation.app.uid}`,
            );
          });
          await this.workflowGrpcClient.updateAccessibleTeamsForActivities(
            {
              activityIdentifiers: Array.from(activityIdentifiers),
              teamIds: addTeamsToInstallationDto.teamIds,
            },
            {
              user_id: user.uid,
              org_id: user.orgUid,
            },
          );
        }

        // update app installation
        await this.appInstallationRepository.updateWithTxn(
          txnContext,
          { id: appInstallation.id },
          { allowedTeamIds: addTeamsToInstallationDto.teamIds },
        );
      });

      // Send notification to the apps
      await this.svixWebhookService.sendMessage(appInstallation.app.uid, {
        eventType: ALLOWED_FILTER_TYPES.INSTALLATION,
        payload: {
          installation_id: appInstallation.uid,
          team_ids: appInstallation.allowedTeamIds,
          organisation_id: user.orgUid,
          application_id: appInstallation.app.uid,
          application_name: appInstallation.app.name,
          application_metadata: appInstallation.app?.manifest?.metadata || {},
          bot_id: appInstallation.botUserId,
          bot_token: "**REDACTED**",
          configuration: appInstallation.configuration,
          updated_by: user.uid,
          updated_at: new Date().toISOString(),
          event_type: ALLOWED_EVENT_TYPES.TEAM_ADDED,
        },
        channels: [this.configService.get("NODE_ENV")],
      });
      this.logger.log("Installation event published successfully");

      return {
        message: "App added to teams successfully",
        teamIdsAdded: addTeamsToInstallationDto.teamIds,
      };
    } catch (error) {
      // Compensation for team members.
      await this.teamManagementService.removeTeamsFromBotInstallationUser(
        addTeamsToInstallationDto.teamIds,
        appInstallation.botUserId,
        user as unknown as User,
      );
      throw new InternalServerErrorException(
        `Failed to add app to teams: ${error?.details || error?.message}`,
      );
    }
  }

  async removeAppFromTeams(
    removeTeamsFromInstallationDto: RemoveTeamsFromInstallationDto,
    user: CurrentUser,
  ): Promise<RemoveTeamsFromInstallationResponseDto> {
    const appInstallation =
      await this.appInstallationRepository.findByCondition({
        where: {
          uid: removeTeamsFromInstallationDto.installationId,
          organizationId: user.orgUid,
        },
        relations: ["app"],
      });
    if (!appInstallation) {
      throw new NotFoundException("App installation not found");
    }

    try {
      await this.transactionService.runInTransaction(async (txnContext) => {
        // remove teams from bot installation user
        await this.teamManagementService.removeTeamsFromBotInstallationUser(
          removeTeamsFromInstallationDto.teamIds,
          appInstallation.botUserId,
          user as unknown as User,
        );

        // Check if the app has activities
        const appHasActivities =
          appInstallation.app?.manifest?.activities?.length > 0;

        // Get the array of teamIds that are NOT in the removeTeamsFromInstallationDto.teamIds
        const remainingTeamIds = appInstallation.allowedTeamIds.filter(
          (teamId) => !removeTeamsFromInstallationDto.teamIds.includes(teamId),
        );

        if (appHasActivities) {
          const activityIdentifiers = new Set<string>();
          appInstallation.app.manifest.activities.forEach((activity) => {
            activityIdentifiers.add(
              `${activity.name}-${appInstallation.app.uid}`,
            );
          });
          await this.workflowGrpcClient.updateAccessibleTeamsForActivities(
            {
              activityIdentifiers: Array.from(activityIdentifiers),
              teamIds: remainingTeamIds,
            },
            {
              user_id: user.uid,
              org_id: user.orgUid,
            },
          );
        }

        // update app installation
        await this.appInstallationRepository.updateWithTxn(
          txnContext,
          { id: appInstallation.id },
          { allowedTeamIds: remainingTeamIds },
        );
      });

      // Send notification to the apps
      await this.svixWebhookService.sendMessage(appInstallation.app.uid, {
        eventType: ALLOWED_FILTER_TYPES.INSTALLATION,
        payload: {
          installation_id: appInstallation.uid,
          team_ids: appInstallation.allowedTeamIds,
          organisation_id: user.orgUid,
          application_id: appInstallation.app.uid,
          application_name: appInstallation.app.name,
          application_metadata: appInstallation.app?.manifest?.metadata || {},
          bot_id: appInstallation.botUserId,
          bot_token: "**REDACTED**",
          configuration: appInstallation.configuration,
          updated_by: user.uid,
          updated_at: new Date().toISOString(),
          event_type: ALLOWED_EVENT_TYPES.TEAM_REMOVED,
        },
        channels: [this.configService.get("NODE_ENV")],
      });
      this.logger.log("Installation event published successfully");

      return {
        message: "App removed from teams successfully",
        teamIdsRemoved: removeTeamsFromInstallationDto.teamIds,
      };
    } catch (error) {
      // Compensation for team members.
      await this.teamManagementService.addTeamsToBotInstallationUser(
        removeTeamsFromInstallationDto.teamIds,
        appInstallation.botUserId,
        user as unknown as User,
      );
      throw new InternalServerErrorException(
        `Failed to remove app from teams: ${error?.details || error?.message}`,
      );
    }
  }
}
