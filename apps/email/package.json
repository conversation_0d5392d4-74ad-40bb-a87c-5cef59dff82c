{"name": "@thena-backend/email", "description": "", "version": "0.0.1", "author": "", "dependencies": {"@nestjs/microservices": "^10.4.7", "@aws-sdk/client-s3": "^3.677.0", "@grpc/reflection": "^1.0.4", "@nestjs/common": "^10.4.4", "@nestjs/core": "^10.4.4", "@nestjs/mapped-types": "^2.0.6", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^11.0.0", "@repo/nestjs-commons": "workspace:*", "@repo/nestjs-newrelic": "workspace:*", "@repo/shared-proto": "workspace:*", "@repo/thena-platform-entities": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/handlebars": "^4.1.0", "ajv": "^8.17.1", "async-retry": "^1.3.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cls-rtracer": "^2.6.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "handlebars": "^4.7.8", "ioredis": "^5.4.1", "joi": "^17.13.3", "newrelic": "^12.18.2", "pino": "^9.5.0", "postmark": "^4.0.5", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "tldts": "^6.1.55", "uuid": "^10.0.0"}, "devDependencies": {"@bull-board/api": "^5.9.1", "@bull-board/fastify": "^5.9.1", "@nestjs/bullmq": "^11.0.1", "@nestjs/cli": "^10.0.0", "@nestjs/platform-fastify": "^10.4.5", "@nestjs/schematics": "^10.1.3", "@nestjs/testing": "^10.4.4", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/cors": "^2.8.17", "@types/jest": "^29.5.2", "@types/node": "^22.9.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "bullmq": "^5.37.0", "cheerio": "^1.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "fastify": "^5.0.0", "fastify-plugin": "^5.0.1", "istanbul-badges-readme": "^1.0.0", "jest": "^29.5.0", "pg": "^8.13.0", "pino-pretty": "^11.3.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.20", "typescript": "^5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "license": "UNLICENSED", "private": true, "scripts": {"build": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "clean:modules": "rimraf node_modules", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "start": "nest start", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch | pino-pretty --singleLine --colorize", "start:prod": "node dist/main", "test": "jest", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:watch": "jest --watch", "typeorm": "TS_NODE_PROJECT=./tsconfig-migrator.json typeorm-ts-node-commonjs", "make-badges": "jest --coverage --coverageReporters=\"json-summary\" && istanbul-badges-readme"}}