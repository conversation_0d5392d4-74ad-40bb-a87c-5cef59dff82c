# syntax=docker/dockerfile:1.4


##################
##### Stage #1
##################
# Prune dependencies for Alpha
FROM --platform=linux/amd64 node:22.13-bullseye-slim AS builder
RUN apt-get update && apt-get install -y --no-install-recommends \
  libc6 \
  && rm -rf /var/lib/apt/lists/*


# Enable PNPM
RUN npm install -g corepack@latest
RUN corepack use pnpm@10.1.0
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

WORKDIR /app
RUN pnpm add -g turbo
COPY . .

# Include all required workspace packages in the prune
RUN pnpm turbo prune @thena-backend/email --docker

##################
##### Stage #2
##################
# 1. Copy pruned app from stage #1
# 2. Install dependencies
# 3. Build

FROM --platform=linux/amd64 node:22.13-bullseye-slim AS installer
RUN apt-get update && apt-get install -y --no-install-recommends \
  libc6 \
  python3-minimal \
  protobuf-compiler \
  && rm -rf /var/lib/apt/lists/*

ARG TURBO_TEAM
ENV TURBO_TEAM=$TURBO_TEAM

ARG TURBO_TOKEN
ENV TURBO_TOKEN=$TURBO_TOKEN

# Enable PNPM
RUN npm install -g corepack@latest
RUN corepack use pnpm@10.1.0
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

WORKDIR /app

# These files won't change much
COPY .gitignore .gitignore
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=builder /app/turbo.json ./turbo.json
COPY scripts ./scripts

RUN pnpm i


COPY --from=builder /app/out/full/ .
RUN pnpm turbo run build --filter=@thena-backend/email... \
  && pnpm prune --prod \
  && rm -rf /root/.cache /root/.npm /root/.pnpm-store


##################
##### Stage #3
##################
# Copy build from Stage #2
# Run the application
FROM --platform=linux/amd64 node:22.13-bullseye-slim AS runner
RUN apt-get update && apt-get install -y --no-install-recommends \
  libc6 \
  && rm -rf /var/lib/apt/lists/*

# Enable PNPM
RUN npm install -g corepack@latest
RUN corepack use pnpm@10.1.0
ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

WORKDIR /app

# Only copy what's needed to run the app
COPY --from=installer /app/package.json ./package.json
COPY --from=installer /app/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=installer /app/turbo.json ./turbo.json
COPY --from=installer /app/node_modules ./node_modules
COPY --from=installer /app/apps ./apps
COPY --from=installer /app/packages ./packages

EXPOSE 8003
# Skip migrations in the container start and only run the application
CMD ["node", "apps/email/dist/main"]
