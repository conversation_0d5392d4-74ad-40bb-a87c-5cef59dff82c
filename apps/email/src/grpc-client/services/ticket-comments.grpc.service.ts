import { Inject, Injectable, OnModuleInit } from "@nestjs/common";
import { BaseGrpcClient } from "@repo/nestjs-commons/grpc";
import { ILogger } from "@repo/nestjs-commons/logger";
import { communication } from "@repo/shared-proto";
import { ConfigKeys, ConfigService } from "../../config/config.service";

@Injectable()
export class TicketCommentsGrpcClient
  extends BaseGrpcClient
  implements OnModuleInit
{
  constructor(
    @Inject("CustomLogger") logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    super(logger, "Tickets");
  }

  onModuleInit() {
    this.initializeClient(
      "communication",
      communication.COMMUNICATION_SERVICE_NAME,
    );
  }

  protected getServiceUrl(): string {
    return this.configService.get(ConfigKeys.PLATFORM_GRPC_URL);
  }

  async createComment(
    commentData: communication.CreateCommentRequest,
    metadata: any,
  ): Promise<communication.CommentResponse> {
    return await this.makeGrpcRequest<
      communication.CreateCommentRequest,
      communication.CommentResponse
    >("CreateCommentOnAnEntity", commentData, metadata);
  }

  async updateComment(
    commentData: communication.UpdateCommentRequest,
    metadata: any,
  ): Promise<communication.CommentResponse> {
    return await this.makeGrpcRequest<
      communication.UpdateCommentRequest,
      communication.CommentResponse
    >("UpdateComment", commentData, metadata);
  }

  async getComment(
    commentData: communication.GetCommentRequest,
    metadata: any,
  ): Promise<communication.CommentResponse> {
    return await this.makeGrpcRequest<
      communication.GetCommentRequest,
      communication.CommentResponse
    >("GetComment", commentData, metadata);
  }
}
