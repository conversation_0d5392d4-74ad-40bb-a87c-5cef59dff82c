import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from "@nestjs/common";
import { User } from "@repo/nestjs-commons/guards";
import {
  EmailConfigRepository,
  EmailDNSRepository,
  SendersPreferredChoice,
} from "@repo/thena-platform-entities";
import { generateReadableEmail } from "../common/utils/default-email-generator";
import { PostmarkAdminService } from "../email-provider/services/postmark-admin.services";

import { Logger } from "@nestjs/common";
import { IsNull, Not } from "typeorm";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { TeamGrpcClient } from "../grpc-client/services/team.grpc.service";
import {
  EmailConfigDto,
  EnableEmailDto,
  UpdateEmailConfigDto,
  VerifyEmailForwardingDto,
  VerifyEmailVerificationCodeDto,
} from "./dto/email-config.dto";
@Injectable()
export class EmailConfigService {
  private readonly logger = new Logger(EmailConfigService.name);

  constructor(
    private readonly emailConfigRepository: EmailConfigRepository,
    private readonly postmarkAdminService: PostmarkAdminService,
    private readonly emailDNSRepository: EmailDNSRepository,
    private readonly configService: ConfigService,
    private readonly teamGrpcClient: TeamGrpcClient,
  ) {}

  async enableEmail(enableEmailDto: EnableEmailDto, currentUser: User) {
    const { teamId } = enableEmailDto;
    const { orgUid } = currentUser;

    try {
      // check if teamId is valid
      const team = await this.teamGrpcClient.getTeamById(teamId, {
        user_id: currentUser.uid,
        org_id: orgUid,
      });

      if (!team) {
        throw new NotFoundException(`Team with ID ${teamId} not found`);
      }

      const emailConfig = await this.emailConfigRepository.findByCondition({
        where: { teamId, organizationId: orgUid },
      });

      if (!emailConfig) {
        const forwardingEmailAddress = await generateReadableEmail(
          teamId,
          orgUid,
          this.configService.get(ConfigKeys.THENA_EMAIL_DOMAIN),
        );

        const createEmailConfig = await this.saveEmailConfig(
          currentUser,
          teamId,
          null, // domain
          null, // customEmail
          forwardingEmailAddress,
          SendersPreferredChoice.ORIGNAL_NAME,
          "", // sendersPreferredChoiceValue
          false, // isEmailForwardingVerified
        );

        return {
          message: "Email enabled successfully",
          emailConfig: createEmailConfig,
        };
      }

      return {
        message: "Email already enabled",
        emailConfig: emailConfig,
      };
    } catch (error) {
      this.logger.error(
        `Failed to enable email for team ${teamId}: ${error.message}`,
        error.stack,
      );

      throw new InternalServerErrorException(
        "Failed to enable email configuration",
      );
    }
  }

  async getDefaultEmail(currentUser: User, teamId: string) {
    // Make sure the order is teamId, orgUid, domain
    return await generateReadableEmail(
      teamId,
      currentUser.orgUid,
      this.configService.get(ConfigKeys.THENA_EMAIL_DOMAIN),
    );
  }

  private async saveEmailConfig(
    currentUser: User,
    teamId: string,
    domain: string,
    customEmail: string,
    forwardingEmail: string,
    sendersPreferredChoice: SendersPreferredChoice,
    sendersPreferredChoiceValue: string,
    isEmailForwardingVerified?: boolean,
  ) {
    try {
      const emailConfig = await this.emailConfigRepository.save({
        userId: currentUser.uid,
        teamId,
        organizationId: currentUser.orgUid,
        customEmail: customEmail,
        domain,
        forwardingEmailAddress: forwardingEmail,
        isEmailForwardingVerified: isEmailForwardingVerified ?? false,
        sendersPreferredChoice,
        sendersPreferredChoiceValue,
      });
      return emailConfig;
    } catch (error) {
      throw new InternalServerErrorException(
        `Internal Query Failed: ${error.message}`,
      );
    }
  }

  async addCustomEmail(
    emailConfigDto: EmailConfigDto,
    currentUser: User,
  ): Promise<any> {
    const {
      email,
      teamId,
      sendersPreferredChoice,
      sendersPreferredChoiceValue,
    } = emailConfigDto;
    const domain = email ? email.split("@")[1] : null;

    const emailConfig = await this.emailConfigRepository.findByCondition({
      where: {
        customEmail: email,
        teamId,
      },
    });

    if (emailConfig) {
      throw new BadRequestException("Email already exists");
    }

    // check if the domain is verified
    const isDomainVerified = await this.emailDNSRepository.findByCondition({
      where: { domain, organizationUid: currentUser.orgUid },
    });

    if (!isDomainVerified) {
      throw new BadRequestException("Domain not found");
    }

    if (!isDomainVerified.isDnsVerified) {
      throw new BadRequestException("Domain not verified");
    }

    try {
      const forwardingEmail = await generateReadableEmail(
        teamId,
        currentUser.orgUid,
        this.configService.get(ConfigKeys.THENA_EMAIL_DOMAIN),
      );

      const createEmailConfig = await this.saveEmailConfig(
        currentUser,
        teamId,
        domain,
        email,
        forwardingEmail,
        sendersPreferredChoice,
        sendersPreferredChoiceValue,
      );

      return createEmailConfig;
    } catch (error) {
      throw new InternalServerErrorException(
        `Internal Query Failed: ${error.message}`,
      );
    }
  }

  async updateCustomEmail(
    emailConfigDto: UpdateEmailConfigDto,
    currentUser: User,
    configId: string,
  ) {
    const {
      email,
      teamId,
      sendersPreferredChoice,
      sendersPreferredChoiceValue,
    } = emailConfigDto;
    const domain = email ? email.split("@")[1] : null;

    const idFromConfigUid = await this.emailConfigRepository.findByCondition({
      where: { uid: configId, teamId },
      select: ["id"],
    });

    if (!idFromConfigUid) {
      throw new BadRequestException("Email config not found");
    }

    // check if the domain is verified
    const isDomainVerified = await this.emailDNSRepository.findByCondition({
      where: { domain, organizationUid: currentUser.orgUid },
    });

    if (!isDomainVerified) {
      throw new BadRequestException("Domain not found");
    }

    if (!isDomainVerified.isDnsVerified) {
      throw new BadRequestException("Domain not verified");
    }

    if (email) {
      // check if the email is already in use
      const isEmailInUse = await this.emailConfigRepository.findByCondition({
        where: { customEmail: email, teamId },
      });

      if (isEmailInUse) {
        throw new BadRequestException("Email already in use");
      }
    }

    let updateEmailConfig = {};
    if (email) {
      updateEmailConfig = {
        customEmail: email,
        isEmailForwardingVerified: false,
        lastVerifiedAt: IsNull(),
        forwardingVerificationCode: null,
      };
    }

    // update the email config
    await this.emailConfigRepository.update(idFromConfigUid.id, {
      ...updateEmailConfig,
      sendersPreferredChoice,
      sendersPreferredChoiceValue,
    });

    return {
      message: "Email config updated successfully",
      emailConfigDto: emailConfigDto,
    };
  }

  async getCustomEmailConfig(teamId: string, currentUser: User) {
    const emailConfig = await this.emailConfigRepository.findAll({
      where: {
        teamId,
        organizationId: currentUser.orgUid,
        customEmail: Not(IsNull()),
      },
    });
    if (emailConfig.length === 0) {
      throw new NotFoundException("No custom email config found");
    }
    return emailConfig;
  }

  async verifyCustomEmailForwarding(
    verifyEmailForwardingDto: VerifyEmailForwardingDto,
    _currentUser: User,
  ) {
    const { email, teamId } = verifyEmailForwardingDto;
    const emailConfig = await this.emailConfigRepository.findByCondition({
      where: { customEmail: email, teamId },
    });

    if (!emailConfig) {
      throw new BadRequestException("Email not found");
    }

    if (emailConfig.isEmailForwardingVerified) {
      return {
        message: "Email already verified",
        emailConfig,
      };
    }

    let verificationCode = emailConfig.forwardingVerificationCode;
    if (!verificationCode) {
      verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    }

    try {
      await this.emailConfigRepository.update(emailConfig.id, {
        forwardingVerificationCode: verificationCode,
        updatedAt: new Date(),
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `Internal Query Failed: ${error.message}`,
      );
    }

    const isEmailForwardingVerified =
      await this.postmarkAdminService.verifyEmailForwarding(
        "<EMAIL>",
        emailConfig.customEmail,
        verificationCode,
      );

    if (!isEmailForwardingVerified) {
      throw new BadRequestException("Email forwarding verification failed");
    }

    return isEmailForwardingVerified;
  }

  async verifyEmailVerificationCode(
    verifyEmailVerificationCodeDto: VerifyEmailVerificationCodeDto,
    _currentUser: User,
  ) {
    const { email, teamId, verificationCode } = verifyEmailVerificationCodeDto;
    const emailConfig = await this.emailConfigRepository.findByCondition({
      where: { customEmail: email, teamId },
    });

    if (!emailConfig) {
      throw new BadRequestException("Email not found");
    }

    if (emailConfig.forwardingVerificationCode !== verificationCode) {
      throw new BadRequestException("Invalid verification code");
    }

    const updatedEmailConfig = await this.emailConfigRepository.update(
      emailConfig.id,
      {
        isEmailForwardingVerified: true,
        lastVerifiedAt: new Date(),
      },
    );

    return updatedEmailConfig;
  }

  async deleteEmailConfig(teamId: string, configId: string, currentUser: User) {
    const emailConfig = await this.emailConfigRepository.findByCondition({
      where: {
        uid: configId,
        teamId,
        userId: currentUser.uid,
        deletedAt: null,
      },
    });

    if (!emailConfig) {
      throw new BadRequestException("Email config not found");
    }

    await this.emailConfigRepository.update(emailConfig.id, {
      deletedAt: new Date(),
    });

    return {
      message: "Email config deleted successfully",
    };
  }

  async getEmailSettings(currentUser: User, teamId: string) {
    const emailConfig = await this.emailConfigRepository.findByCondition({
      where: { teamId, organizationId: currentUser.orgUid },
    });

    if (!emailConfig) {
      throw new NotFoundException("Email config not found");
    }

    const returnEmailConfig = {
      isEmailEnabled: true, // if config is found then email is enabled regardless of the custom email or default email
      ...emailConfig,
    };

    return returnEmailConfig;
  }
}
