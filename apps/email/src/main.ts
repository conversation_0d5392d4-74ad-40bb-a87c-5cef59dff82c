import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { MicroserviceOptions } from "@nestjs/microservices";
import type { NestFastifyApplication } from "@nestjs/platform-fastify";
import { FastifyAdapter } from "@nestjs/platform-fastify";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { shouldExcludeFromSentry } from "@repo/nestjs-commons/errors";
import {
  DatabaseExceptionFilter,
  HttpExceptionFilter,
  SENTRY_SERVICE_TOKEN,
  SentryExceptionFilter,
} from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { NewRelicInterceptor, NewRelicService } from "@repo/nestjs-newrelic";
import * as Sentry from "@sentry/node";
import * as rTracer from "cls-rtracer";
import cors from "cors";
import dotenv from "dotenv";
import "newrelic";
import pino from "pino";
import { v4 as uuidv4 } from "uuid";
import { AppModule } from "./app.module";
import bullBoardPlugin from "./bull-queue/bull-board.plugin";
import { BullBoardService } from "./bull-queue/services/bull-board.service";
import { grpcServerConfig } from "./grpc.server.config";

dotenv.config({ path: ".env" });

dotenv.config({ path: ".env" });

async function bootstrap() {
  let app: NestFastifyApplication | null = null;
  let logger: ILogger | null = null;

  try {
    const fastifyAdapter = new FastifyAdapter({
      logger: {
        level: "info",
        base: { app: process.env.APP_TAG, service: process.env.SERVICE_TAG },
        timestamp: pino.stdTimeFunctions.isoTime,
      },
      trustProxy: ["loopback"],
      genReqId: () => uuidv4(),
    });

    // Initialize Sentry
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: 1.0,
      attachStacktrace: true,
      autoSessionTracking: true,
      beforeSend(event, hint) {
        const exception = hint.originalException;
        if (exception instanceof Error && shouldExcludeFromSentry(exception)) {
          return null; // Don't send to Sentry
        }
        return event;
      },
    });

    app = await NestFactory.create<NestFastifyApplication>(
      AppModule,
      fastifyAdapter,
      { bufferLogs: true },
    );

    logger = app.get<ILogger>("CustomLogger");
    app.useLogger(logger);

    // Install global exception handler
    process.on("uncaughtException", (error) => {
      if (logger) {
        logger.error(`Uncaught Exception: ${error.message}`, error.stack);
      } else {
        console.error(`Uncaught Exception: ${error.message}`, error.stack);
      }
      // Only send to Sentry if it's not an excluded error
      if (!(error instanceof Error) || !shouldExcludeFromSentry(error)) {
        Sentry.captureException(error);
      }
    });

    // Handle unhandled promise rejections
    process.on("unhandledRejection", (reason: any, promise) => {
      if (logger) {
        logger.error(
          `Unhandled Rejection at: ${promise}, reason: ${
            reason?.message || reason
          }`,
          reason?.stack || new Error().stack,
        );
      } else {
        console.error(
          `Unhandled Rejection at: ${promise}, reason: ${
            reason?.message || reason
          }`,
          reason?.stack || new Error().stack,
        );
      }
      // Only send to Sentry if it's not an excluded error
      if (!(reason instanceof Error) || !shouldExcludeFromSentry(reason)) {
        Sentry.captureException(reason);
      }
    });

    app.connectMicroservice<MicroserviceOptions>(grpcServerConfig);

    const bullBoardService = app.get(BullBoardService);
    await fastifyAdapter.register(bullBoardPlugin as any, {
      queues: [
        bullBoardService.emailQueue,
        bullBoardService.forwardingVerificationQueue,
      ],
      basePath: "/admin/queues",
    });

    // Add validation pipe to the app
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          return new UnprocessableEntityException(
            errors.map((error) => ({
              property: error.property,
              constraints: error.constraints,
            })),
          );
        },
      }),
    );

    const sentryService = app.get(SENTRY_SERVICE_TOKEN);
    app.useGlobalFilters(
      new HttpExceptionFilter(),
      new DatabaseExceptionFilter(),
      new SentryExceptionFilter(sentryService),
    );

    app.use(
      rTracer.fastifyMiddleware({
        echoHeader: true,
        requestIdFactory: (req) => ({
          reqId: req.id,
          context: {},
        }),
      }),
    );

    // Enable CORS with default options
    app.use(cors());

    // Start listening to server termination signals
    app.enableShutdownHooks();

    // Alternatively, customize CORS options
    app.enableCors({
      origin: "*", // your frontend URLs
      methods: ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE"],
      allowedHeaders: "*",
      credentials: true,
    });

    // Add Swagger documentation to the app
    const swaggerConfig = new DocumentBuilder()
      .setTitle("Thena Platform")
      .setDescription("The Thena Platform API description")
      .setVersion("1.0")
      .addTag("Thena Platform APIs")
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup("api", app, document);

    // Get NewRelicService from the app
    const newRelicService = app.get(NewRelicService);

    // Only add the interceptor if New Relic is enabled
    if (newRelicService.isEnabled() && process.env.NODE_ENV !== "development") {
      const interceptor = new NewRelicInterceptor(newRelicService);
      app.useGlobalInterceptors(interceptor as any);
      newRelicService.setServiceName(
        `${process.env.APP_TAG} | ${process.env.SERVICE_TAG} | ${process.env.NODE_ENV}`,
      );
    }

    // Setup process exit handlers
    setupGracefulShutdown(app, logger);
    await app.startAllMicroservices();
    await app.listen({ port: 8003, host: "0.0.0.0" }, (err, address) => {
      if (err) {
        logger.error(`Failed to start server: ${err.message}`, err.stack);
        // Don't exit on startup errors, try to continue
      } else {
        logger.log(`Server listening at ${address}`);
      }
    });
  } catch (error) {
    if (error instanceof Error) {
      if (logger) {
        logger.error(
          `Error during application bootstrap: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(
          "Failed to start the application:",
          error.message,
          error.stack,
        );
      }
      // Only send to Sentry if it's not an excluded error
      if (!shouldExcludeFromSentry(error)) {
        Sentry.captureException(error);
      }
    }

    // Don't exit process here either, try to recover if possible
    if (app) {
      try {
        await app.close();
      } catch (closeError) {
        // Just log, don't throw
        if (logger) {
          logger.error(
            `Error while closing app: ${closeError.message}`,
            closeError.stack,
          );
        } else {
          console.error(
            `Error while closing app: ${closeError.message}`,
            closeError.stack,
          );
        }
      }
    }
  }
}

function setupGracefulShutdown(app: NestFastifyApplication, logger: ILogger) {
  const signals = ["SIGTERM", "SIGINT"];
  signals.forEach((signal) => {
    process.on(signal, async () => {
      logger.log(
        `🚨🚨🚨 Mayday mayday! Received ${signal}. Starting graceful shutdown...`,
      );

      try {
        await app.close();
        logger.log("Application gracefully closed.");
        process.exit(0);
      } catch (error) {
        if (error instanceof Error) {
          logger.error(
            `Error during graceful shutdown: ${error.message}`,
            error.stack,
          );
        }
        // Still need to exit here since it's an intentional shutdown signal
        process.exit(1);
      }
    });
  });
}

// Start the application
bootstrap().catch((error) => {
  console.error("Fatal error in bootstrap process:", error);
  if (!shouldExcludeFromSentry(error)) {
    Sentry.captureException(error);
  }
});
