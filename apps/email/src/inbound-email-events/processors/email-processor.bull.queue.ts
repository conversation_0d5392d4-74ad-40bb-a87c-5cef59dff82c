import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import { UploadFileResponse } from "@repo/shared-proto/dist/generated/src/proto/storage/storage";
import {
  CommentEntityTypes,
  CommentType,
  CommentVisibility,
  EmailConfigRepository,
  EmailRepository,
  EmailTicketActivity,
  TransactionService,
} from "@repo/thena-platform-entities";
import { Job } from "bullmq";
import { plainToClass } from "class-transformer";
import { validateOrReject } from "class-validator";
import { S3Service } from "src/storage/s3.service";
import { IsNull, Not } from "typeorm";
import { QUEUE_CONFIGS } from "../../bull-queue/constants/bull-queue.interface";
import { EmailConfigData } from "../../bull-queue/interfaces/bull-queue-config.interface";
import { StorageGrpcClient } from "../../grpc-client/services/storage.grpc.service";
import { TicketCommentsGrpcClient } from "../../grpc-client/services/ticket-comments.grpc.service";
import { TicketsGrpcClient } from "../../grpc-client/services/tickets.grpc.service";
import { InboundEmailDto } from "../dtos/inbound-email.dto";
import { InboundEmailMessage } from "../interface/inbound-email.interface";
@Injectable()
@Processor(QUEUE_CONFIGS.EMAIL_PROCESSING.name)
export class EmailQueueProcessor extends WorkerHost {
  constructor(
    private readonly ticketsGrpcClient: TicketsGrpcClient,
    private readonly ticketCommentsGrpcClient: TicketCommentsGrpcClient,
    private readonly storageGrpcClient: StorageGrpcClient,
    private readonly emailConfigRepository: EmailConfigRepository,
    private readonly emailTicketActivityRepository: EmailRepository,
    private readonly transactionService: TransactionService,
    private readonly s3Service: S3Service,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {
    super();
  }

  /**
   * Process inline attachments in HTML body and replace CID references with public URLs
   * @param htmlBody The HTML body content
   * @param attachments Array of uploaded attachments with their public URLs
   * @returns Processed HTML body with replaced image sources
   */
  private processInlineAttachments(
    htmlBody: string,
    attachments: Array<{
      id: string;
      fileName: string;
      publicUrl: string;
      contentId?: string;
    }>,
  ): string {
    if (!htmlBody || !attachments.length) {
      return htmlBody;
    }

    let processedHtml = htmlBody;

    attachments.forEach((attachment) => {
      // Try to match by contentId if available
      if (attachment.contentId) {
        const cleanContentId = attachment.contentId.replace(/[<>]/g, "");
        const cidReference = `cid:${cleanContentId}`;

        // Replace all occurrences of the CID reference with the public URL
        processedHtml = processedHtml.replace(
          new RegExp(cidReference, "g"),
          attachment.publicUrl,
        );
      }

      // Also match by fileName in case the cid: reference uses the fileName directly
      if (attachment.fileName) {
        const fileNameReference = `cid:${attachment.fileName}`;

        // Replace all occurrences of the fileName reference with the public URL
        processedHtml = processedHtml.replace(
          new RegExp(fileNameReference, "g"),
          attachment.publicUrl,
        );
      }
    });

    return processedHtml;
  }

  /**
   * Upload a file to storage
   * @param file The file to upload
   * @returns The file ID
   */
  private async uploadFileToStorage(
    file: Buffer,
    fileName: string,
    contentType: string,
    userId: string,
    orgId: string,
  ): Promise<UploadFileResponse> {
    const response = await this.storageGrpcClient.uploadFile(
      {
        fileData: file,
        fileName,
        contentType,
      },
      {
        user_id: userId,
        org_id: orgId,
      },
    );
    return response;
  }

  /**
   * Check if the email is a platform email
   * @param emailData The email data
   * @returns The message ID if it's a platform email, false otherwise
   */
  private getIsPlatformEmail(emailData: InboundEmailDto): string | boolean {
    const messageId = emailData?.Headers?.find(
      (header) => header.Name === "Message-Id",
    )?.Value;

    if (!messageId) {
      return false;
    }
    const match = messageId.match(/<([^@]+)@/);
    return match ? match[1] : false;
  }

  /**
   * Check if a message has already been processed
   * Uses both Redis and database for reliability
   * @param messageId The message ID to check
   * @returns true if the message should be processed, false if it's already been processed
   */
  private async checkMessageIdempotency(messageId: string): Promise<boolean> {
    const existingEmail =
      await this.emailTicketActivityRepository.findByCondition({
        where: { messageId },
        select: ["id", "ticketId", "status"],
      });

    // If the message exists and has a ticket ID, it's already been processed
    if (existingEmail && existingEmail.ticketId) {
      return false;
    }

    return true;
  }

  /**
   * Extract data from forwarding email
   * @param emailData The email data
   * @returns Email configuration data or null
   */
  private async extractDataFromForwardingEmail(
    emailData: InboundEmailDto,
  ): Promise<EmailConfigData | null> {
    /**
     *  Scenario 1: Normal forwarded email
     *  1. Check the originalRecipient from emailParticipants. This is the forwarding email address.
     *  2. Check the Delivered-To and X-Forwarded-For headers to get the custom email address.
     *
     *  Scenario 2: Reply from forwarding email
     *  1. Check the X-Forwarded-For header to get the original email address.
     *  2. Check the Return-Path header to get the custom email address.
     */
    let emailConfig;
    const { OriginalRecipient, Headers } = emailData;
    const forwardingEmail =
      OriginalRecipient ||
      Headers.find((header) => header.Name === "X-Forwarded-To")?.Value;
    let forwardedForEmail = Headers.find(
      (header) => header.Name === "X-Forwarded-For",
    )?.Value?.split(" ")[0];

    if (!forwardedForEmail) {
      this.logger.log(
        `No X-Forwarded-For header found, this means the forwardedForEmail has sent the email.`,
      );
      const returnPathHeader = Headers.find(
        (header) => header.Name === "Return-Path",
      )?.Value;
      forwardedForEmail = returnPathHeader?.split("<")[1]?.split(">")[0];

      const isGoogleGroupEmail = Headers.find(
        (header) => header.Name === "X-Google-Group-Id",
      )?.Value;

      if (isGoogleGroupEmail) {
        this.logger.log(
          `X-Google-Group-Id header found for group id ${isGoogleGroupEmail}, this means the email is a google group email.`,
        );

        forwardedForEmail = forwardedForEmail.includes("+")
          ? forwardedForEmail.split("+")[0] +
            "@" +
            forwardedForEmail.split("@")[1]
          : forwardedForEmail;
      } else {
        this.logger.log(
          `No X-Google-Group-Id header found, this means the email is not a google group email.`,
        );
        this.logger.log(
          `Forwarded for email is ${forwardedForEmail} and this is the orignal Default email address. No forwarding from custom email is setup.`,
        );

        emailConfig = await this.emailConfigRepository.findByCondition({
          where: {
            forwardingEmailAddress: forwardingEmail.toLowerCase(),
            deletedAt: null,
            isEmailForwardingVerified: false,
          },
          select: [
            "id",
            "organizationId",
            "teamId",
            "userId",
            "sendersPreferredChoice",
            "sendersPreferredChoiceValue",
          ],
        });

        if (emailConfig) {
          this.logger.log(
            `Found email configuration for the default email address ${forwardingEmail}`,
          );
          // Update the headers to include the custom auto generated email address
          emailData.Headers.push({
            Name: "X-platform-auto-generated-email",
            Value: forwardingEmail,
          });
          emailData.CcFull.push({
            Email: forwardingEmail,
            Name: "",
            MailboxHash: "",
          });
          emailData.BccFull.push({
            Email: forwardingEmail,
            Name: "",
            MailboxHash: "",
          });
          return emailConfig;
        } else {
          this.logger.log(
            `No email configuration found for the default email address ${forwardedForEmail}`,
          );
          return null;
        }
      }
    }

    if (forwardingEmail && forwardedForEmail) {
      this.logger.log(
        `Found the forwarding email address ${forwardingEmail} and the custom email address ${forwardedForEmail}`,
      );

      emailConfig = await this.emailConfigRepository.findByCondition({
        where: {
          forwardingEmailAddress: forwardingEmail.toLowerCase(),
          customEmail: forwardedForEmail.toLowerCase(),
          deletedAt: null,
          isEmailForwardingVerified: true,
        },
        select: [
          "id",
          "organizationId",
          "teamId",
          "userId",
          "sendersPreferredChoice",
          "sendersPreferredChoiceValue",
        ],
      });
    } else {
      this.logger.log(
        `No forwarding email address found, this means the sender has sent the email.`,
      );
      return null;
    }

    if (!emailConfig) {
      this.logger.warn(
        `Could not find email configuration for this email ${emailData.MessageID}`,
      );
      return null;
    }

    return {
      id: emailConfig.id,
      sendersPreferredChoice: emailConfig.sendersPreferredChoice,
      organizationId: emailConfig.organizationId,
      teamId: emailConfig.teamId,
      userId: emailConfig.userId,
      sendersPreferredChoiceValue: emailConfig.sendersPreferredChoiceValue,
    };
  }

  /**
   * Create a comment reply for an inbound email
   * @param data The email data
   * @param replyActivity The reply activity
   * @param configData The email configuration data
   * @param existingLink The existing link
   * @returns The created comment
   */
  private async createInboundCommentReply(
    data: InboundEmailDto,
    replyActivity: EmailTicketActivity,
    configData: EmailConfigData,
    existingLink: EmailTicketActivity,
    parsedHtmlBody: string,
  ) {
    const existingComment = await this.ticketCommentsGrpcClient.getComment(
      {
        commentId: existingLink.commentId,
      },
      {
        user_id: configData.userId,
        org_id: configData.organizationId,
      },
    );

    if (existingComment) {
      let attachmentIds: string[] = [];
      if (
        data.Attachments.length > 0 &&
        data.Attachments.some((attachment) => attachment.Content)
      ) {
        attachmentIds = await Promise.all(
          data.Attachments.map(async (attachment) => {
            const file = await this.uploadFileToStorage(
              Buffer.from(attachment.Content, "base64"),
              attachment.Name,
              attachment.ContentType,
              configData.userId,
              configData.organizationId,
            );
            return file.data.id;
          }),
        );
      }

      const comment = await this.ticketCommentsGrpcClient.createComment(
        {
          content: parsedHtmlBody,
          contentHtml: parsedHtmlBody,
          entityType: CommentEntityTypes.TICKET,
          entityId: replyActivity.ticketId,
          parentCommentId: existingComment.id,
          commentVisibility: CommentVisibility.PUBLIC,
          commentType: CommentType.COMMENT,
          attachmentIds,
          impersonatedUserEmail: data.FromFull.Email,
          impersonatedUserName: data.FromFull.Name,
          metadata: JSON.stringify({
            email: {
              from: data.FromFull,
              to: data.ToFull,
              cc: data.CcFull,
              bcc: data.BccFull,
              headers: data.Headers,
              isIgnoreSelf: true,
              subject: data.Subject,
            },
          }),
        },
        {
          user_id: configData.userId,
          org_id: configData.organizationId,
        },
      );

      return {
        commentId: comment?.id,
      };
    } else {
      this.logger.log(
        `No existing comment found for ${existingLink.commentId}`,
      );
      return null;
    }
  }

  /**
   * Create a new ticket with an initial comment in a single call
   * @param data The email data
   * @param configData The email configuration data
   * @returns The created ticket and comment
   */
  private async createTicketAndComment(
    data: InboundEmailDto,
    attachmentIds: string[],
    configData: EmailConfigData,
    parsedHtmlBody: string,
  ) {
    try {
      // This is a new thread - create a new ticket with initial comment in one call
      const ticket = await this.ticketsGrpcClient.createTicket(
        {
          title: data.Subject,
          requestorEmail: data?.FromFull?.Email,
          teamId: configData.teamId,
          attachmentUrls: [],
          source: "email",
          customFieldValues: [],
          // New comment fields
          commentContent: parsedHtmlBody,
          commentContentHtml: parsedHtmlBody,
          commentAttachmentIds: attachmentIds,
          commentImpersonatedUserEmail: data.FromFull.Email,
          commentImpersonatedUserName: data.FromFull.Name,
          commentMetadata: JSON.stringify({
            email: {
              from: data.FromFull,
              to: data.ToFull,
              cc: data.CcFull,
              bcc: data.BccFull,
              headers: data.Headers,
              isIgnoreSelf: true,
              subject: data.Subject,
            },
          }),
        },
        {
          user_id: configData.userId,
          org_id: configData.organizationId,
        },
      );

      if (ticket?.id) {
        this.logger.log(
          `Successfully created ticket with initial comment for email ${data.MessageID}`,
        );
        return {
          ticketId: ticket.id,
          commentId: ticket.comment.id, // Assuming the response includes the created comment ID
        };
      }
    } catch (error) {
      this.logger.error(
        `Failed to create ticket with initial comment for email ${data.MessageID}`,
      );
      throw error;
    }
  }

  /**
   * Process an email job
   * 1. Check if MessageID is present.
   * 2. Fetch the payload from s3
   * 3. Check if the email is idempotent.
   * 4. If it is, create a ticket and comment.
   * 5. Update the email status to processed and update the ticket and comment id.
   * 6. Log the success message.
   * @param job The job to process
   */
  // TODO: Need compensation grpc methods to handle any failures.

  async process(job: Job<InboundEmailMessage>): Promise<void> {
    let attachmentsData: Array<{
      id: string;
      publicUrl: string;
      fileName: string;
      contentId?: string;
    }> = []; // Contains the attachment ids and public urls
    try {
      const { MessageID, s3_attachment_path } = job.data;
      if (!MessageID) {
        this.logger.warn("Skipping email processing: Missing Message-ID");
        return;
      }

      const fetchEmailFromS3 = await this.s3Service.fetchPayload(
        s3_attachment_path || `emails/${MessageID}.json`,
      );

      // Create a proper instance of InboundEmailDto and transform the payload
      const emailData = plainToClass(InboundEmailDto, fetchEmailFromS3);
      // Validate the data against our DTO schema
      await validateOrReject(emailData, {
        whitelist: true,
        forbidNonWhitelisted: true,
        validationError: { target: false },
        skipMissingProperties: false, // Ensure all required properties are present
      });

      const {
        Headers,
        Subject,
        HtmlBody,
        TextBody,
        FromFull,
        ToFull,
        CcFull,
        BccFull,
        Attachments,
      } = emailData;

      const isIdempotent = await this.checkMessageIdempotency(MessageID);
      if (!isIdempotent) {
        this.logger.log(
          `Email is not idempotent, Duplicate email, Skipping processing ${MessageID}`,
        );
        return;
      }

      // Check if it's a platform email
      const isPlatfromEmail = this.getIsPlatformEmail(emailData);
      if (isPlatfromEmail) {
        this.logger.log(
          `Email is a platform email, updating the email status to processed ${isPlatfromEmail}`,
        );
        const inReplyToHeader = Headers.find(
          (header) => header.Name === "In-Reply-To",
        )?.Value;

        const headerMessageId = Headers.find(
          (header) => header.Name === "Message-Id",
        )?.Value;

        await this.transactionService.runInTransaction(async (txnContext) => {
          await this.emailTicketActivityRepository.updateWithTxn(
            txnContext,
            {
              messageId: isPlatfromEmail as string,
              metadata: {
                handleFromInbound: true,
              },
            },
            {
              status: "processed",
              inReplyTo: inReplyToHeader,
              emailHeaderMessageId: headerMessageId,
              emailHeaders: Headers,
              conversationDate: emailData.Date,
              processedAt: new Date(),
            },
          );

          const activityData =
            await this.emailTicketActivityRepository.findByCondition({
              where: {
                messageId: isPlatfromEmail as string,
              },
            });

          if (!activityData) {
            throw new Error("Activity data not found");
          }

          const { commentId } = activityData;
          this.logger.log(
            `Updating the metadata of comment with id ${isPlatfromEmail} on the platfrom`,
          );

          const extractData = await this.extractDataFromForwardingEmail(
            emailData,
          );
          if (!extractData) {
            throw new Error("Forwarding email config not found");
          }

          if (
            Attachments.length > 0 &&
            Attachments.some((attachment) => attachment.Content)
          ) {
            attachmentsData = await Promise.all(
              Attachments.map(async (attachment) => {
                const file = await this.uploadFileToStorage(
                  Buffer.from(attachment.Content, "base64"),
                  `${attachment.ContentID}`, // using content id as the name to ensure uniqueness
                  attachment.ContentType,
                  extractData.userId,
                  extractData.organizationId,
                );
                return {
                  id: file.data.id,
                  fileName: attachment.ContentID,
                  contentId: attachment.ContentID,
                  publicUrl: file.data.metadata.publicUrl,
                };
              }),
            );
          }

          await this.ticketCommentsGrpcClient.updateComment(
            {
              commentId,
              attachments: attachmentsData.map((attachment) => attachment.id),
              metadata: JSON.stringify({
                email: {
                  from: FromFull,
                  to: ToFull,
                  cc: CcFull,
                  bcc: BccFull,
                  headers: Headers,
                  isIgnoreSelf: true,
                  subject: Subject,
                },
              }),
            },
            {
              user_id: extractData.userId,
              org_id: extractData.organizationId,
            },
          );

          this.logger.log(
            `Successfully processed platform email ${isPlatfromEmail}`,
          );
          return;
        });
        return; // Exit after processing platform email
      }

      // Extract data from forwarding email
      const extractData = await this.extractDataFromForwardingEmail(emailData);
      if (!extractData) {
        this.logger.error(`No forwarding email config found for ${MessageID}`);
        return;
      }

      const { organizationId, teamId } = extractData;
      const inReplyToHeader = Headers.find(
        (header) => header.Name === "In-Reply-To",
      )?.Value;
      const headerMessageId = Headers.find(
        (header) => header.Name === "Message-ID",
      )?.Value;

      // Write here
      const referenceHeaderArray = Headers.find(
        (headers) => headers.Name === "References",
      )?.Value?.split(" ");

      // Simple logic: If References exist, it's a reply. If not, it's new thread.
      const hasReferences =
        referenceHeaderArray && referenceHeaderArray.length > 0;

      if (!hasReferences) {
        /**
         * No References header = New thread, create a new ticket and comment
         */
        if (
          Attachments.length > 0 &&
          Attachments.some((attachment) => attachment.Content)
        ) {
          attachmentsData = await Promise.all(
            Attachments.map(async (attachment) => {
              const file = await this.uploadFileToStorage(
                Buffer.from(attachment.Content, "base64"),
                `${attachment.ContentID}`,
                attachment.ContentType,
                extractData.userId,
                extractData.organizationId,
              );
              return {
                id: file?.data?.id,
                fileName: attachment.ContentID,
                contentId: attachment.ContentID,
                publicUrl: file?.data?.metadata?.publicUrl,
              };
            }),
          );
        }

        const parsedHtmlBody = this.processInlineAttachments(
          HtmlBody,
          attachmentsData,
        );

        await this.transactionService.runInTransaction(async (txnContext) => {
          const emailTicketActivity =
            await this.emailTicketActivityRepository.saveWithTxn(txnContext, {
              messageId: MessageID,
              emailHeaderMessageId: headerMessageId,
              organizationId: organizationId,
              teamId: teamId,
              subject: Subject,
              htmlBody: parsedHtmlBody,
              textBody: TextBody,
              from: FromFull,
              to: ToFull,
              cc: CcFull,
              bcc: BccFull,
              attachments: attachmentsData.map((attachment) => attachment.id),
              conversationDate: emailData.Date,
              emailHeaders: Headers,
            });

          const { ticketId, commentId } = await this.createTicketAndComment(
            emailData,
            attachmentsData.map((attachment) => attachment.id),
            extractData,
            parsedHtmlBody,
          );

          if (ticketId && commentId) {
            await this.emailTicketActivityRepository.updateWithTxn(
              txnContext,
              { id: emailTicketActivity.id },
              {
                ticketId: ticketId,
                commentId: commentId,
                status: "processed",
                processedAt: new Date(),
              },
            );
          }
          this.logger.log(
            `Successfully processed new thread ${MessageID} with ticket ${ticketId} and comment ${commentId}`,
          );
          return;
        });
      } else {
        /**
         * References header exists = Reply to existing thread
         */
        let existingLink = null;

        // Try to find any message from the References array
        for (const ref of referenceHeaderArray) {
          existingLink =
            await this.emailTicketActivityRepository.findByCondition({
              where: {
                emailHeaderMessageId: ref,
                ticketId: Not(IsNull()),
              },
            });

          if (existingLink) {
            this.logger.log(
              `Found existing thread via reference ${ref} for ${MessageID}`,
            );
            break;
          }
        }

        if (existingLink) {
          // Create reply comment on existing ticket
          if (
            Attachments.length > 0 &&
            Attachments.some((attachment) => attachment.Content)
          ) {
            attachmentsData = await Promise.all(
              Attachments.map(async (attachment) => {
                const file = await this.uploadFileToStorage(
                  Buffer.from(attachment.Content, "base64"),
                  `${attachment.ContentID}`,
                  attachment.ContentType,
                  extractData.userId,
                  extractData.organizationId,
                );
                return {
                  id: file.data.id,
                  fileName: attachment.ContentID,
                  contentId: attachment.ContentID,
                  publicUrl: file.data.metadata.publicUrl,
                };
              }),
            );
          }

          const parsedHtmlBody = this.processInlineAttachments(
            HtmlBody,
            attachmentsData,
          );

          await this.transactionService.runInTransaction(async (txnContext) => {
            const replyActivity =
              await this.emailTicketActivityRepository.saveWithTxn(txnContext, {
                messageId: MessageID,
                emailHeaderMessageId: headerMessageId,
                inReplyTo: inReplyToHeader,
                organizationId: organizationId,
                teamId: teamId,
                subject: Subject,
                htmlBody: parsedHtmlBody,
                textBody: TextBody,
                from: FromFull,
                to: ToFull,
                cc: CcFull,
                bcc: BccFull,
                attachments: attachmentsData.map((attachment) => attachment.id),
                conversationDate: emailData.Date,
                emailHeaders: Headers,
                ticketId: existingLink.ticketId,
              });

            const comment = await this.createInboundCommentReply(
              emailData,
              replyActivity,
              extractData,
              existingLink,
              parsedHtmlBody,
            );

            if (comment?.commentId) {
              await this.emailTicketActivityRepository.updateWithTxn(
                txnContext,
                { id: replyActivity.id },
                {
                  status: "processed",
                  commentId: comment.commentId,
                  processedAt: new Date(),
                },
              );
            }

            this.logger.log(
              `Successfully processed reply ${MessageID} with ticket ${existingLink.ticketId} and comment ${comment.commentId}`,
            );
            return;
          });
        } else {
          // No existing thread found, but has References = orphaned reply
          this.logger.log(
            `Orphaned reply detected for ${MessageID}, creating new ticket`,
          );

          // Create new ticket for orphaned reply (same logic as new thread)
          if (
            Attachments.length > 0 &&
            Attachments.some((attachment) => attachment.Content)
          ) {
            attachmentsData = await Promise.all(
              Attachments.map(async (attachment) => {
                const file = await this.uploadFileToStorage(
                  Buffer.from(attachment.Content, "base64"),
                  `${attachment.ContentID}`,
                  attachment.ContentType,
                  extractData.userId,
                  extractData.organizationId,
                );
                return {
                  id: file?.data?.id,
                  fileName: attachment.ContentID,
                  contentId: attachment.ContentID,
                  publicUrl: file?.data?.metadata?.publicUrl,
                };
              }),
            );
          }

          const parsedHtmlBody = this.processInlineAttachments(
            HtmlBody,
            attachmentsData,
          );

          await this.transactionService.runInTransaction(async (txnContext) => {
            const emailTicketActivity =
              await this.emailTicketActivityRepository.saveWithTxn(txnContext, {
                messageId: MessageID,
                emailHeaderMessageId: headerMessageId,
                inReplyTo: inReplyToHeader, // Keep reply context
                organizationId: organizationId,
                teamId: teamId,
                subject: Subject,
                htmlBody: parsedHtmlBody,
                textBody: TextBody,
                from: FromFull,
                to: ToFull,
                cc: CcFull,
                bcc: BccFull,
                attachments: attachmentsData.map((attachment) => attachment.id),
                conversationDate: emailData.Date,
                emailHeaders: Headers,
              });

            const { ticketId, commentId } = await this.createTicketAndComment(
              emailData,
              attachmentsData.map((attachment) => attachment.id),
              extractData,
              parsedHtmlBody,
            );

            if (ticketId && commentId) {
              await this.emailTicketActivityRepository.updateWithTxn(
                txnContext,
                { id: emailTicketActivity.id },
                {
                  ticketId: ticketId,
                  commentId: commentId,
                  status: "processed",
                  processedAt: new Date(),
                },
              );
            }
            this.logger.log(
              `Successfully created new ticket for orphaned reply ${MessageID} with ticket ${ticketId}`,
            );
            return;
          });
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to process email job ${job.data.MessageID}: ${error.message}`,
      );
      throw error;
    }
  }
}
