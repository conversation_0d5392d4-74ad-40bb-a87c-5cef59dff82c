import { Controller, Inject } from "@nestjs/common";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { ILogger } from "@repo/nestjs-commons/logger";
import { email } from "@repo/shared-proto";

@Controller()
@email.EmailProviderControllerMethods()
export class EmailGrpcController {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
  ) {
    this.logger.log("EmailGrpcController initialized");
  }

  sendEmail(request: email.SendEmailRequest): email.SendEmailResponse {
    try {
      this.logger.log(
        `SendEmail method called with data: ${JSON.stringify(request)}`,
      );
      return { status: 1 }; // SUCCESS
    } catch (error) {
      handleRpcError(error);
    }
  }
}
