import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AdminClient, ServerClient } from "postmark";
import { CommonModule } from "../common/common.module";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { EmailGrpcController } from "./controller/send-email.grpc.controller";
import { PostmarkAdminService } from "./services/postmark-admin.services";

@Module({
  imports: [ConfigModule, CommonModule],
  controllers: [EmailGrpcController],
  providers: [
    PostmarkAdminService,
    {
      provide: ServerClient,
      useFactory: (configService: ConfigService) =>
        new ServerClient(configService.get(ConfigKeys.POSTMARK_SERVER_ID)),
      inject: [ConfigService],
    },
    {
      provide: AdminClient,
      useFactory: (configService: ConfigService) =>
        new AdminClient(configService.get(ConfigKeys.POSTMARK_API_TOKEN)),
      inject: [ConfigService],
    },
  ],
  exports: [PostmarkAdminService],
})
export class EmailProviderModule {}
