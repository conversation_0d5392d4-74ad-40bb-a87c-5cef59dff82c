import { ReflectionService } from "@grpc/reflection";
import { MicroserviceOptions, Transport } from "@nestjs/microservices";
import { email } from "@repo/shared-proto";
import { resolve } from "path";

export const grpcServerConfig: MicroserviceOptions = {
  transport: Transport.GRPC,
  options: {
    url: "0.0.0.0:50055",
    loader: { keepCase: false },
    package: [email.GRPC_EMAIL_V1_PACKAGE_NAME],
    maxReceiveMessageLength: 1024 * 1024 * 50,
    maxSendMessageLength: 1024 * 1024 * 50,
    protoPath: [
      // Email
      resolve(
        require.resolve("@repo/shared-proto/dist/proto/email/email.proto"),
      ),
    ],
    onLoadPackageDefinition: (pkg, server) => {
      new ReflectionService(pkg).addToServer(server);
    },
  },
};
