/**
 * @deprecated re wite this again fully
 */
import {
  Inject,
  Injectable,
  OnModuleD<PERSON>roy,
  OnModuleInit,
} from "@nestjs/common";
import {
  SQSConsumerService,
  SQSMessage,
} from "@repo/nestjs-commons/aws-utils/new/sqs";
import { ExpectedSQSRetryError } from "@repo/nestjs-commons/errors/custom.errors";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  EmailConfig,
  EmailConfigRepository,
  EmailRepository,
  SendersPreferredChoice,
  TransactionService,
} from "@repo/thena-platform-entities";
import { Redis } from "ioredis";
import { PostmarkAdminService } from "../../email-provider/services/postmark-admin.services";
@Injectable()
export class PlatformEventsService implements OnModuleInit, OnModuleDestroy {
  private readonly EVENT_STATUS_PREFIX = "platform:event:status:";
  private readonly IN_PROGRESS_TTL = 60 * 60; // 1 hour
  private readonly COMPLETED_TTL = 60 * 60 * 24 * 30; // 30 days

  constructor(
    @Inject("PlatformEventsConsumer")
    private readonly platformEventsConsumer: SQSConsumerService,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly emailRepository: EmailRepository,
    private readonly emailConfigRepository: EmailConfigRepository,
    private readonly postmarkAdminService: PostmarkAdminService,
    private readonly transactionService: TransactionService,
    @Inject("REDIS_CLIENT") private readonly redis: Redis,
  ) {}

  onModuleInit() {
    this.handleMessages();
  }

  onModuleDestroy() {
    this.platformEventsConsumer.stopConsumer();
  }

  private extractInactiveEmails(message: string) {
    // Extract email addresses from the error message
    const match = message.match(/inactive addresses: ([^"]+)/);
    if (match && match[1]) {
      return match[1].split(",").map((email) => email.trim());
    }
    return [];
  }

  /**
   * Check if an event has already been processed and mark it as in progress
   * @param eventId The event ID to check
   * @returns true if the event should be processed, false if it's already been processed
   */
  private async checkAndSetEventStatus(eventId: string): Promise<boolean> {
    try {
      const statusKey = `${this.EVENT_STATUS_PREFIX}${eventId}`;

      // Check if the event has already been processed
      const status = await this.redis.get(statusKey);
      if (status === "completed") {
        this.logger.log(`Event ${eventId} already fully processed, skipping`);
        return false;
      }

      if (status === "in_progress") {
        this.logger.log(`Event ${eventId} is in progress, sending back to SQS`);
        return Promise.reject(
          new ExpectedSQSRetryError(
            `Event ${eventId} is already being processed`,
          ),
        );
      }

      // Try to set the status to in_progress
      const setResult = await this.redis.set(
        statusKey,
        "in_progress",
        "EX",
        this.IN_PROGRESS_TTL,
        "NX",
      );

      // If we couldn't set the key, another instance just started processing it
      if (setResult !== "OK") {
        this.logger.log(
          `Event ${eventId} already in progress because of another instance, sending back to SQS`,
        );
        return Promise.reject(
          new ExpectedSQSRetryError(
            `Event ${eventId} is already being processed by another instance`,
          ),
        );
      }

      return true;
    } catch (error) {
      if (error.message.includes("already being processed")) {
        return Promise.reject(
          new ExpectedSQSRetryError(
            `Event ${eventId} is already being processed by another instance`,
          ),
        );
      }
      // If Redis fails, log but continue processing (fail open for Redis issues)
      this.logger.error("Error checking event status in Redis", error);
      return true;
    }
  }

  /**
   * Mark an event as completed in Redis
   * @param eventId The event ID to mark as completed
   */
  private async markEventCompleted(eventId: string): Promise<void> {
    try {
      const statusKey = `${this.EVENT_STATUS_PREFIX}${eventId}`;
      await this.redis.set(statusKey, "completed", "EX", this.COMPLETED_TTL);
    } catch (error) {
      // If Redis fails, log but don't fail the processing
      this.logger.error("Error marking event as completed in Redis", error);
    }
  }

  private async createEmailPayload(data: any, emailConfig: EmailConfig) {
    let from = "";
    // Extract email addresses as arrays
    const toEmails =
      data?.payload?.comment?.metadata?.email?.to?.map(
        (recipient) => recipient.Email,
      ) || [];
    const ccEmails =
      data?.payload?.comment?.metadata?.email?.cc?.map(
        (recipient) => recipient.Email,
      ) || [];
    const bccEmails =
      data?.payload?.comment?.metadata?.email?.bcc?.map(
        (recipient) => recipient.Email,
      ) || [];

    // Check if customEmail exists in any recipient list
    const customEmail = emailConfig?.customEmail?.toLowerCase();
    const isCustomEmailInRecipients = [
      ...toEmails,
      ...ccEmails,
      ...bccEmails,
    ].some((email) => email?.toLowerCase() === customEmail);

    // Add custom email to CC if needed
    if (!isCustomEmailInRecipients) {
      ccEmails.push(emailConfig.customEmail);
    }

    // Handle removal of forwarding email from bcc to avoid bounce and loops
    const cleanBcc = bccEmails.filter(
      (email) =>
        email.toLowerCase() !==
        emailConfig.forwardingEmailAddress.toLowerCase(),
    );

    // Convert to comma-separated strings for the email payload
    const parsedTo = toEmails.join(",");
    const parsedCC = ccEmails.join(",");
    const parsedBcc = cleanBcc.join(",");

    const senderOriginalName = data?.payload?.comment?.author?.name;
    const senderPreference = emailConfig.sendersPreferredChoice;
    const sendersPreferredValue = emailConfig.sendersPreferredChoiceValue;
    switch (senderPreference) {
      case SendersPreferredChoice.ORIGNAL_NAME:
        from = `${senderOriginalName} ${emailConfig.customEmail}`;
        break;
      case SendersPreferredChoice.USE_COMMON_NAME:
        from = `${sendersPreferredValue} ${emailConfig.customEmail}`;
        break;
      case SendersPreferredChoice.BOTH:
        from = `${senderOriginalName} | ${sendersPreferredValue} ${emailConfig.customEmail}`;
        break;
      default:
        from = emailConfig.customEmail;
    }

    const attachmentsData = [];
    const attachments = data?.payload?.comment?.attachments || [];
    if (attachments.length > 0) {
      for (const attachment of attachments) {
        const response = await fetch(attachment.url);
        const arrayBuffer = await response.arrayBuffer();
        const base64String = Buffer.from(arrayBuffer).toString("base64");
        attachmentsData.push({
          name: attachment.id,
          contentId: attachment.id,
          content: base64String,
          contentType: attachment.contentType,
        });
      }
    }

    const isDefaultNonForwardedEmail =
      data?.payload?.comment?.metadata?.email?.headers?.find(
        (header) => header.Name === "X-platform-auto-generated-email",
      )?.Value;

    if (isDefaultNonForwardedEmail) {
      from = `${senderOriginalName} <${isDefaultNonForwardedEmail}>`;
    }

    return {
      from: from,
      to: parsedTo,
      cc: parsedCC,
      bcc: parsedBcc,
      subject: data?.payload?.comment?.metadata?.email?.subject,
      textBody: data?.payload?.comment?.content,
      htmlBody: data?.payload?.comment?.contentHtml,
      metadata: {
        ticketId: data?.payload?.ticket?.id,
        commentId: data?.payload?.comment?.id,
        organizationId: data?.orgId,
        teamId: data?.payload?.comment?.teamId,
        handleFromInbound: true,
      },
      headers: [
        {
          Name: "In-Reply-To",
          Value: data?.payload?.comment?.metadata?.email?.headers?.find(
            (header) => header.Name === "Message-ID",
          )?.Value,
        },
        {
          Name: "References",
          Value: data?.payload?.comment?.metadata?.email?.headers?.find(
            (header) => header.Name === "References",
          )?.Value,
        },
      ],
      attachments: attachmentsData,
    };
  }

  private handleMessages(): void {
    this.platformEventsConsumer.startConsumer(async (message: SQSMessage) => {
      try {
        const { messageAttributes, message: payload } = message;
        const eventName = messageAttributes?.event_name;
        const eventId = messageAttributes?.event_id;

        this.logger.log(
          `Processing platform event: ${eventName} (ID: ${eventId})`,
        );

        // Skip processing if the event has already been processed
        if (eventId && !(await this.checkAndSetEventStatus(eventId))) {
          return; // Acknowledge to SQS
        }

        switch (eventName) {
          case "ticket:comment:added":
            this.logger.log(
              `Event sent to comment handler for the event ${eventName}`,
            );
            await this.handleTicketCommentAddedEvent(payload);
            // Mark event as completed after successful processing
            if (eventId) {
              await this.markEventCompleted(eventId);
            }
            break;
          default:
            this.logger.log(`We don't handle this event type: ${eventName}`);
        }
      } catch (error) {
        this.logger.error("Error processing platform event", error);
        throw error; // Retries based on SQS configuration
      }
    });
  }

  private async handleTicketCommentAddedEvent(
    data: any, // TODO: ADD TYPES
  ): Promise<void> {
    try {
      /**
       * Check for isIgnoreSelf in the metadata
       * if true then dont handle the event because this is the same ticket:comment:added event we created.
       * if false then handle the event
       */
      if (data.payload.comment.metadata?.email?.isIgnoreSelf) {
        this.logger.log(
          `Ignoring event because isIgnoreSelf is true for the ${data.eventType} with event id ${data?.eventId} for ticket ${data?.payload?.ticket?.id} and comment ${data?.payload?.comment?.id}`,
        );
        return;
      }

      /**
       * The event sends the metadata header of the email they are replying to.
       * the header["Message-ID"] is the message id of the email they are replying to.
       * the header["In-Reply-To"] is the message id of the email they are replying to.
       *
       * Steps
       * 1. get the email config for the organization + team id + custom email address.
       * 2. create the email payload. While creating the payload check the settings form the email config for the sendersPreference.
       * 3. send the email.
       * 4. Send all the necessary data in the metadata field of postmark email.
       * 5. Because the forwarder email is present in the metadata. When postmark sends us back we will update the email_ticket_activity table and comment metadata with the new data we got.
       */
      const organizationId = data?.orgId;
      const teamId = data?.payload?.comment?.teamId;
      const headers = data?.payload?.comment?.metadata?.email?.headers || [];

      // Extract forwarding email from headers
      let forwardingEmail =
        headers.find((header) => header.Name === "Delivered-To")?.Value ||
        headers
          .find((header) => header.Name === "X-Forwarded-For")
          ?.Value?.split(" ")[0] ||
        headers.find((header) => header.Name === "Return-Path")?.Value;

      const isGoogleGroupEmail = headers.find(
        (header) => header.Name === "X-Google-Group-Id",
      )?.Value;

      if (isGoogleGroupEmail) {
        this.logger.log(
          `X-Google-Group-Id header found for group id ${isGoogleGroupEmail}, this means this is a reply to a google group email.`,
        );
        const cleanForwardingEmail = forwardingEmail.replace(/[<>]/g, "");
        forwardingEmail = cleanForwardingEmail.includes("+")
          ? cleanForwardingEmail.split("+")[0] +
            "@" +
            cleanForwardingEmail.split("@")[1]
          : cleanForwardingEmail;
      }

      const isDefaultNonForwardedEmail = headers.find(
        (header) => header.Name === "X-platform-auto-generated-email",
      )?.Value;

      let query = {};
      if (isDefaultNonForwardedEmail) {
        this.logger.log(
          `X-platform-auto-generated-email header found for email ${isDefaultNonForwardedEmail}, this means this is a reply to a default non forwarded email.`,
        );
        query = {
          organizationId: organizationId,
          teamId: teamId,
          forwardingEmailAddress: isDefaultNonForwardedEmail,
        };
      } else {
        query = {
          organizationId: organizationId,
          teamId: teamId,
          customEmail: forwardingEmail,
        };
      }

      if (!forwardingEmail) {
        this.logger.warn("No forwarding email found in headers");
        return;
      }

      const emailConfig = await this.emailConfigRepository.findByCondition({
        where: query,
      });

      if (!emailConfig) {
        this.logger.warn(
          "No email config found for the given organization, team, and custom email",
        );
        return;
      }

      const createEmailPayloadData = await this.createEmailPayload(
        data,
        emailConfig,
      );
      const emailResponse = await this.postmarkAdminService.sendEmail(
        createEmailPayloadData,
      );

      // Check for partial delivery
      if (
        emailResponse.ErrorCode === 0 &&
        emailResponse.Message.includes("inactive addresses")
      ) {
        // Extract the inactive email addresses
        const inactiveEmails = this.extractInactiveEmails(
          emailResponse.Message,
        );
        this.logger.log(`Inactive emails: ${JSON.stringify(inactiveEmails)}`);
      }

      if (emailResponse.ErrorCode === 0) {
        await this.emailRepository.save({
          organizationId: organizationId,
          teamId: teamId,
          messageId: emailResponse.MessageID,
          ticketId: data?.payload?.ticket?.id,
          commentId: data?.payload?.comment?.id,
          subject: createEmailPayloadData.subject,
          htmlBody: createEmailPayloadData.htmlBody,
          textBody: createEmailPayloadData.textBody,
          from: data?.payload?.comment?.metadata?.email?.from,
          to: data?.payload?.comment?.metadata?.email?.to,
          cc: data?.payload?.comment?.metadata?.email?.cc,
          bcc: data?.payload?.comment?.metadata?.email?.bcc,
          direction: "outbound",
          metadata: {
            handleFromInbound: true, // This handles the inbound email when it comes back to us from postmark
          },
        });
        this.logger.log(
          `Successfully sent reply email for ticket ${data?.payload?.ticket?.id}`,
        );
      } else {
        this.logger.error(
          `Failed to send reply email for ticket ${data?.payload?.ticket?.id}`,
        );
        // Remove the cache
        await this.redis.del(`${this.EVENT_STATUS_PREFIX}${data?.eventId}`);
        throw new Error("Failed to send reply email");
      }
    } catch (error) {
      this.logger.error("Error sending email", error);
      throw error;
    }
  }
  catch(error) {
    this.logger.error("Error processing ticket event", error);
    throw error; // Retries based on SQS configuration
  }
}
