import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SQSConsumerService } from "@repo/nestjs-commons/aws-utils/new/sqs";
import { SQSConfig, SQSModule } from "@repo/nestjs-commons/aws-utils/sqs";
import {
  EmailConfig,
  EmailConfigRepository,
  EmailRepository,
  EmailTicketActivity,
  TransactionService,
} from "@repo/thena-platform-entities";
import Redis from "ioredis";
import { AdminClient, ServerClient } from "postmark";
import { CommonModule } from "../common/common.module";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { PostmarkAdminService } from "../email-provider/services/postmark-admin.services";
import { PlatformEventsService } from "./services/platform-events.service";

@Module({
  imports: [
    ConfigModule,
    SQSModule,
    CommonModule,
    TypeOrmModule.forFeature([EmailTicketActivity, EmailConfig]),
  ],
  providers: [
    {
      provide: "PlatformEventsSQSConfig",
      useFactory: (configService: ConfigService) => ({
        queueUrl: configService.get(
          ConfigKeys.AWS_SQS_PLATFORM_EVENT_QUEUE_URL,
        ),
        region: configService.get(ConfigKeys.AWS_REGION),
        credentials: {
          accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
          secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
        },
      }),
      inject: [ConfigService],
    },
    {
      provide: "PlatformEventsConsumer",
      useFactory: (config: SQSConfig) => new SQSConsumerService(config),
      inject: ["PlatformEventsSQSConfig"],
    },
    {
      provide: "REDIS_CLIENT",
      useFactory: (configService: ConfigService) => {
        return new Redis({
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: parseInt(configService.get(ConfigKeys.REDIS_PORT), 10),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
        });
      },
      inject: [ConfigService],
    },
    PlatformEventsService,
    EmailRepository,
    EmailConfigRepository,
    PostmarkAdminService,
    TransactionService,
    {
      provide: ServerClient,
      useFactory: (configService: ConfigService) =>
        new ServerClient(configService.get(ConfigKeys.POSTMARK_SERVER_ID)),
      inject: [ConfigService],
    },
    {
      provide: AdminClient,
      useFactory: (configService: ConfigService) =>
        new AdminClient(configService.get(ConfigKeys.POSTMARK_API_TOKEN)),
      inject: [ConfigService],
    },
  ],
  exports: [PlatformEventsService],
})
export class PlatformEventsModule {}
