import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import retry from "async-retry";
import { ConfigKeys, ConfigService } from "../config/config.service";

@Injectable()
export class S3Service {
  private s3Client: S3Client;
  private bucketName: string;

  constructor(
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    this.s3Client = new S3Client({
      region: this.configService.get(ConfigKeys.AWS_REGION),
      requestHandler: {
        requestTimeout: 5 * 60 * 1000, // 5 minutes timeout
        connectionTimeout: 30 * 1000, // 30 seconds connection timeout
      },
    });
    this.bucketName = this.configService.get(ConfigKeys.EMAIL_PAYLOAD_BUCKET);
  }

  async uploadPayload(key: string, payload: any): Promise<string> {
    try {
      // Use async-retry with configuration similar to our manual approach
      return await retry(
        async (_, attemptNumber) => {
          try {
            const data = JSON.stringify(payload);
            const command = new PutObjectCommand({
              Bucket: this.bucketName,
              Key: key,
              Body: data,
              ContentType: "application/json",
              ServerSideEncryption: "AES256", // Enable encryption at rest
              Metadata: {
                "Message-ID": payload.MessageID || "",
                timestamp: new Date().toISOString(),
              },
            });

            await this.s3Client.send(command);
            this.logger.log(
              `Successfully uploaded payload to S3 with key ${key}`,
            );
            return key;
          } catch (error) {
            this.logger.error(
              `S3 upload attempt ${attemptNumber} failed: ${error}`,
            );

            // Rethrow the error to trigger retry
            throw error;
          }
        },
        {
          retries: 5,
          minTimeout: 2000, // Increased from 1000ms to 2000ms
          maxTimeout: 10000, // Increased from 2000ms to 10000ms
          factor: 2,
          randomize: true, // Add jitter to prevent thundering herd issues
          onRetry: (error, attemptNumber) => {
            this.logger.warn(
              `Retrying S3 upload (attempt ${attemptNumber}) after error: ${error.message}`,
            );
          },
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to upload payload to S3 after multiple attempts: ${error}`,
      );
      throw error;
    }
  }
}
