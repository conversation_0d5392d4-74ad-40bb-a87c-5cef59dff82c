import { TicketEvents } from "@repo/thena-shared-interfaces";

export interface SLABreachPayload {
  sla: {
    policyId: string;
    policyName: string;
    metric: string;
    duration?: number;
  };
  ticket?: {
    id: string;
    teamId: string;
  };
}

export interface TicketSlaSNSEvent<T extends TicketEvents> {
  eventId: string;
  eventType: T;
  timestamp: string;
  orgId: string;
  teamId: string;
  payload: SLABreachPayload;
}
