import { Process, Processor } from "@nestjs/bull";
import { Inject } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import { ContextUserType, SNSPublisherService } from "@repo/thena-eventbridge";
import {
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Filter,
  PolicyMetric,
  SlaEntityType,
  SLAJobStatus,
  SLAMetricType,
  SLAPolicy,
  SLAPolicyRepository,
  SLAScheduledRepository,
} from "@repo/thena-platform-entities";
import { TicketEvents } from "@repo/thena-shared-interfaces";
import { Job } from "bull";
import { In } from "typeorm";
import { v4 as uuidv4 } from "uuid";
import { ActivitiesGrpcClient } from "../../common/grpc/activities.grpc-client";
import { ConfigKeys, ConfigService } from "../../config/config.service";
import {
  SLABreachPayload,
  TicketSlaSNSEvent,
} from "../../interfaces/sns-event.interface";
import { ITicket } from "../../interfaces/ticket-sla-interface";
import { SLAAuditLogsService } from "../services/sla-audit-logs.service";
import { SLABreachUtils } from "../utils/sla-breach.utils";
import { TicketsUtils } from "../utils/tickets.utils";

interface SLAJobData {
  entityId: string;
  eventName: string;
  entityType: string;
  policyId: string;
  metricObject: PolicyMetric;
  specifPolicyMetric: Filter;
  ticketData: ITicket;
  user: string;
  scheduledAt: Date;
  organizationId: string;
  jobDuration: number;
  resumedAt?: Date;
}

@Processor("sla-jobs")
export class SLAProcessor {
  // private readonly logger = new Logger(SLAProcessor.name);

  constructor(
    private readonly slaPolicyRepository: SLAPolicyRepository,
    private readonly slaScheduledRepository: SLAScheduledRepository,
    private readonly slaAuditLogsService: SLAAuditLogsService,
    private readonly configService: ConfigService,
    @Inject("SLA_SNS_PUBLISHER")
    private readonly slaSnsPublisher: SNSPublisherService,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly ticketsUtils: TicketsUtils,
    private readonly slaBreachUtils: SLABreachUtils,

    // GRPC Clients
    @Inject(ActivitiesGrpcClient)
    private readonly activitiesGrpcClient: ActivitiesGrpcClient,
  ) {}

  @Process("process-sla")
  async handleSLA(job: Job<SLAJobData>) {
    try {
      const {
        entityId,
        eventName,
        entityType,
        policyId,
        metricObject,
        specifPolicyMetric,
        user,
        scheduledAt: scheduledAtString,
        organizationId,
        jobDuration,
        resumedAt,
      } = {
        ...job.data,
      };
      const referenceTimestamp = resumedAt
        ? new Date(resumedAt)
        : new Date(scheduledAtString);
      this.logger.log(
        `Processing SLA for ${eventName} ${entityId} ${entityType}`,
      );

      switch (entityType) {
        case SlaEntityType.TICKET:
        case SlaEntityType.COMMENT: {
          let ticketData: ITicket;
          try {
            ticketData = await this.ticketsUtils.fetchTicketData(
              entityId,
              user,
              organizationId,
            );
          } catch (error) {
            // Log the failure in SLA audit logs
            await this.slaAuditLogsService.updateOrCreate({
              entityType: entityType,
              scheduledJobId: job.id.toString(),
              policyId: policyId,
              entityId: entityId,
              organizationId: organizationId,
              teamId: null, // We don't have ticket data
              eventType: entityType,
              message: `Failed to fetch ${entityType} data: ${
                error instanceof Error ? error.message : String(error)
              }`,
              metric: metricObject.metric,
              specificMetric: specifPolicyMetric,
              filter: null, // We don't have policy data here
              userId: user,
            });
            throw error; // Re-throw to be caught by outer try-catch
          }
          if (!ticketData) {
            this.logger.warn(
              `No ticket data found for ${entityId} ${entityType}`,
            );
            return { success: false, entityId, eventName };
          }

          this.logger.log(`Processing ${entityType} SLA`);
          const [{ isBreached, breachedAt }, { isAchieved, achievedAt }] =
            await Promise.all([
              this.slaBreachUtils.checkBreach(
                metricObject,
                ticketData,
                user,
                organizationId,
                referenceTimestamp,
                entityType as SlaEntityType,
                jobDuration,
              ),
              this.slaBreachUtils.checkSlaAchievements(
                metricObject,
                ticketData,
                user,
                organizationId,
                referenceTimestamp,
                entityType as SlaEntityType,
              ),
            ]);
          if (isBreached) {
            await this.handleSLABreach(
              entityId,
              policyId,
              job.id.toString(),
              ticketData,
              breachedAt,
            );
          }
          if (isAchieved) {
            await this.handleSLAAchieved(
              entityId,
              policyId,
              job.id.toString(),
              ticketData,
              achievedAt,
            );
          }
          break;
        }
      }

      return { success: true, entityId, eventName };
    } catch (error) {
      this.logger.error(
        `Error processing SLA job: ${
          error instanceof Error ? error.message : JSON.stringify(error)
        }`,
      );
      throw error;
    }
  }

  // TODO this is for now only for ticket we need to have a check here for other entities
  // Based on entity type we will get the entity id (ticket id, account id, etc)
  private async handleSLABreach(
    entityId: string,
    policyId: string,
    scheduledJobId: string,
    ticketData?: ITicket,
    breachedAt?: Date,
  ) {
    try {
      this.logger.log(`SLA Breach detected for ${entityId}`);

      // Find the scheduled SLA job
      const scheduledSLA = await this.slaScheduledRepository.findByCondition({
        where: {
          scheduledJobId: scheduledJobId,
          status: In([SLAJobStatus.RUNNING, SLAJobStatus.SCHEDULED]),
          jobId: ticketData.id,
        },
        relations: ["policy"],
      });

      if (scheduledSLA) {
        // Update the job status to BREACHED
        await this.slaScheduledRepository.update(scheduledSLA.id, {
          status: SLAJobStatus.BREACHED,
          breachedAt: breachedAt ?? new Date(),
        });

        await this.slaAuditLogsService.updateOrCreate({
          entityType: SlaEntityType.TICKET,
          scheduledJobId: scheduledJobId,
          status: SLAJobStatus.BREACHED,
          policyId: policyId,
          entityId: entityId,
          organizationId: scheduledSLA.organizationId,
          teamId: scheduledSLA.policy.teamId,
          eventType: "bull_queue_processor",
          message: `SLA Breach detected for ${entityId}`,
          metric: scheduledSLA.metric as SLAMetricType,
          specificMetric: scheduledSLA.specificPolicyMetric,
          filter: scheduledSLA.policy.filter,
          userId: scheduledSLA.policy.createdBy,
        });
        await this.publishToSNS({
          entityId,
          entityType: SlaEntityType.TICKET,
          event: SLAJobStatus.BREACHED,
          metric: scheduledSLA.metric as SLAMetricType,
          subject: "SLA Breach Detected",
          organizationId: scheduledSLA.organizationId,
          teamId: scheduledSLA.policy.teamId,
          user: scheduledSLA.policy.createdBy,
          policy: scheduledSLA.policy,
        });
        this.logger.log(`Updated SLA job ${scheduledJobId} status to BREACHED`);
      } else {
        this.logger.warn(
          `No active SLA job found for scheduledJobId: ${scheduledJobId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error handling SLA breach: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      throw error;
    }
  }

  private async handleSLAAchieved(
    entityId: string,
    policyId: string,
    scheduledJobId: string,
    ticketData?: ITicket,
    achievedAt?: Date,
  ) {
    try {
      this.logger.log(`SLA achievement detected for ${entityId}`);

      // Find the scheduled SLA job
      const scheduledSLA = await this.slaScheduledRepository.findByCondition({
        where: {
          scheduledJobId: scheduledJobId,
          status: In([
            SLAJobStatus.RUNNING,
            SLAJobStatus.SCHEDULED,
            SLAJobStatus.BREACHED,
          ]),
          jobId: ticketData.id,
        },
        relations: ["policy"],
      });

      if (scheduledSLA) {
        if (scheduledSLA.status === SLAJobStatus.BREACHED) {
          await this.slaScheduledRepository.update(scheduledSLA.id, {
            achievedAt: achievedAt ?? new Date(),
          });
          return;
        }

        await this.slaScheduledRepository.update(scheduledSLA.id, {
          status: SLAJobStatus.ACHIEVED,
          achievedAt: achievedAt ?? new Date(),
        });

        await this.slaAuditLogsService.updateOrCreate({
          entityType: SlaEntityType.TICKET,
          scheduledJobId: scheduledJobId,
          status: SLAJobStatus.ACHIEVED,
          policyId: policyId,
          entityId: entityId,
          organizationId: scheduledSLA.organizationId,
          teamId: scheduledSLA.policy.teamId,
          eventType: "ticket",
          message: `SLA achieved for ${scheduledSLA.metric} duration and created new SLA for ${scheduledSLA.policy.name} which leads to SLA breach`,
          metric: scheduledSLA.metric as SLAMetricType,
          specificMetric: scheduledSLA.specificPolicyMetric,
          filter: scheduledSLA.policy.filter,
          userId: scheduledSLA.policy.createdBy,
        });
        await this.publishToSNS({
          entityId,
          entityType: SlaEntityType.TICKET,
          event: SLAJobStatus.ACHIEVED,
          metric: scheduledSLA.metric as SLAMetricType,
          subject: "SLA Achieved",
          organizationId: scheduledSLA.organizationId,
          teamId: scheduledSLA.policy.teamId,
          user: scheduledSLA.policy.createdBy,
          policy: scheduledSLA.policy,
        });
        this.logger.log(`Updated SLA job ${scheduledJobId} status to ACHIEVED`);
      } else {
        this.logger.warn(
          `No active SLA job found for scheduledJobId: ${scheduledJobId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error handling SLA achievement: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      throw error;
    }
  }

  private async publishToSNS(params: {
    entityId: string;
    entityType: SlaEntityType;
    event: SLAJobStatus;
    subject?: string;
    metric?: SLAMetricType;
    organizationId: string;
    teamId: string;
    user: string;
    policy?: SLAPolicy;
  }) {
    try {
      const userId: string = params.user;

      const slaPayload: SLABreachPayload = {
        sla: {
          policyId: params.policy?.uid || "unknown",
          policyName: params.policy?.name || "unknown",
          metric: params.metric || "unknown",
        },
        ticket: {
          id: params.entityId,
          teamId: params.teamId,
        },
      };

      if (params.event === SLAJobStatus.BREACHED) {
        const snsEvent: TicketSlaSNSEvent<TicketEvents> = {
          eventId: uuidv4(),
          eventType: TicketEvents.SLA_BREACHED,
          timestamp: new Date().toISOString(),
          orgId: params.organizationId,
          teamId: params.teamId,
          payload: slaPayload,
        };

        await this.slaSnsPublisher.publishSNSMessage({
          subject: params.subject ?? "Tickets SLA Event",
          message: JSON.stringify(snsEvent),
          topicArn: this.configService.get(
            ConfigKeys.AWS_SNS_TICKETS_TOPIC_ARN,
          ),
          messageGroupId: params.entityId,
          messageAttributes: {
            event_name: TicketEvents.SLA_BREACHED,
            event_id: snsEvent.eventId,
            event_timestamp: Math.floor(Date.now() / 1000).toString(),
            context_user_id: userId,
            context_user_type: ContextUserType.USER,
            context_organization_id: params.organizationId,
          },
        });

        this.logger.log(`SNS message published: ${JSON.stringify(snsEvent)}`);
      }

      // Record audit log
      const auditMessage = this.getAuditMessage(
        params.event,
        params.policy?.name,
        params.metric,
      );
      await this.activitiesGrpcClient.recordAuditLog(
        params.organizationId,
        params.teamId,
        userId,
        AuditLogEntityType.TICKET,
        params.entityId,
        AuditLogOp.UPDATED,
        AuditLogVisibility.ORGANIZATION,
        auditMessage,
        auditMessage,
      );

      this.logger.log(`Audit log recorded: ${JSON.stringify(auditMessage)}`);
    } catch (error) {
      this.logger.error(
        `Error encountered while publishing SLA event ${params.event} to SNS: ${error}`,
      );
    }
  }

  private getAuditMessage(
    status: SLAJobStatus,
    policyName?: string,
    metric?: string,
    duration?: number,
  ): string {
    const metricName = (metric || "")
      .replace(/_/g, " ")
      .replace(/^\w/, (c) => c.toUpperCase());

    const baseMsg = `SLA ${status.toLowerCase()} for policy ${
      policyName || "unknown"
    } with metric ${metricName || "unknown"}`;
    switch (status) {
      case SLAJobStatus.SCHEDULED:
        return `${baseMsg} for ${duration || 0} minutes`;
      case SLAJobStatus.BREACHED:
        return `${baseMsg}`;
      case SLAJobStatus.CANCELLED:
        return `${baseMsg}`;
      case SLAJobStatus.ACHIEVED:
        return `${baseMsg}`;
      case SLAJobStatus.PAUSED:
        return `${baseMsg}`;
      default:
        return baseMsg;
    }
  }
}
