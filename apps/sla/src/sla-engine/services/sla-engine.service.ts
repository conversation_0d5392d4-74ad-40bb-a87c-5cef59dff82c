import { InjectQueue } from "@nestjs/bull";
import { Inject, Injectable, OnModuleInit } from "@nestjs/common";
import {
  SQSConsumerService,
  SQSMessage,
} from "@repo/nestjs-commons/aws-utils/sqs";
import { RedisCacheProvider } from "@repo/nestjs-commons/cache";
import { ILogger } from "@repo/nestjs-commons/logger";
import { ContextUserType, SNSPublisherService } from "@repo/thena-eventbridge";
import {
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  EntityType,
  Filter,
  PolicyMetric,
  ScheduledJobPayload,
  ScheduledSLA,
  SlaEntityType,
  SLAJobStatus,
  SLAMetricType,
  SLAPolicy,
  SLAScheduledRepository,
} from "@repo/thena-platform-entities";
import { TicketEvents } from "@repo/thena-shared-interfaces";
import { Queue } from "bull";
import { In, <PERSON>Null, Not } from "typeorm";
import { v4 as uuidv4 } from "uuid";
import { ActivitiesGrpcClient } from "../../common/grpc/activities.grpc-client";
import { AnnotatorGrpcClient } from "../../common/grpc/annotator.grpc-client";
import { UsersGrpcClient } from "../../common/grpc/users.grpc-client";
import { ConfigKeys, ConfigService } from "../../config/config.service";
import {
  ISlaCommentPayload,
  ISlaPayload,
  ISlaTicketPayload,
} from "../../interfaces/sla-payload.interface";
import {
  SLABreachPayload,
  TicketSlaSNSEvent,
} from "../../interfaces/sns-event.interface";
import { ITicket } from "../../interfaces/ticket-sla-interface";
import { SLABreachUtils } from "../utils/sla-breach.utils";
import { SlaEngineUtils } from "../utils/sla-engine-utils";
import { TeamUtils } from "../utils/team.utils";
import { TicketsUtils } from "../utils/tickets.utils";
import { WorkingHours } from "../utils/working-hours.utils";
import { SLAAuditLogsService } from "./sla-audit-logs.service";
import { SLAPolicyService } from "./sla-policy.service";

const SKIPPABLE_TICKET_EVENTS = [
  TicketEvents.TAG_UPDATED,
  TicketEvents.STATUS_CHANGED,
  TicketEvents.PRIORITY_CHANGED,
  TicketEvents.CUSTOM_FIELD_UPDATED,
  TicketEvents.SLA_BREACHED,
  TicketEvents.SLA_BREACH_WARNING,
];

@Injectable()
export class SLAEngineService implements OnModuleInit {
  constructor(
    private readonly configService: ConfigService,

    // Logger
    @Inject("CustomLogger") private readonly logger: ILogger,

    // SNS, SQS, BullMQ
    @Inject("SLA_SNS_PUBLISHER")
    private readonly slaSnsPublisher: SNSPublisherService,
    @Inject("SLAEngineSQSConsumerService")
    private sqsConsumerService: SQSConsumerService,
    @InjectQueue("sla-jobs") private slaQueue: Queue,

    // Redis
    private readonly redisService: RedisCacheProvider,

    // Repositories
    private readonly slaScheduledRepository: SLAScheduledRepository,

    // Services
    private readonly slaPolicyService: SLAPolicyService,
    private readonly slaAuditLogsService: SLAAuditLogsService,
    private readonly teamUtils: TeamUtils,
    private readonly ticketsUtils: TicketsUtils,
    private readonly slaBreachUtils: SLABreachUtils,
    private readonly slaEngineUtils: SlaEngineUtils,

    // GRPC Clients
    @Inject(ActivitiesGrpcClient)
    private readonly activitiesGrpcClient: ActivitiesGrpcClient,
    @Inject(AnnotatorGrpcClient)
    private readonly annotatorClient: AnnotatorGrpcClient,
    @Inject(UsersGrpcClient)
    private readonly usersGrpcClient: UsersGrpcClient,
  ) {}

  onModuleInit() {
    this.startConsumingMessages();
  }
  onModuleDestroy() {
    this.sqsConsumerService.stopConsumer();
  }

  private startConsumingMessages() {
    const messageHandler = async (message: SQSMessage) => {
      try {
        const data = message.message;
        this.logger.log(JSON.stringify(data));
        const { eventType, eventId, timestamp } =
          data as unknown as ISlaPayload;
        this.logger.log(
          `eventType: ${eventType}, eventId: ${eventId}, timestamp: ${timestamp}`,
        );

        await this.processMessage(data as unknown as ISlaPayload);
      } catch (error) {
        console.error("Error processing message:", error);
        throw error;
      }
    };
    this.sqsConsumerService.startConsumer(messageHandler);
  }

  private async isDuplicateMessage(payload: ISlaPayload): Promise<boolean> {
    const { eventType, eventId, timestamp } = payload;
    const messageKey = `sla:msg:${eventType}:${eventId}:${timestamp}`;

    const exists = await this.redisService.get(messageKey);
    if (exists) {
      this.logger.log("Duplicate message detected, skipping processing");
      return true;
    }
    return false;
  }

  private async markMessageAsProcessed(payload: ISlaPayload) {
    const { eventType, eventId, timestamp } = payload;
    const messageKey = `sla:msg:${eventType}:${eventId}:${timestamp}`;
    await this.redisService.set(messageKey, "1", 300);
  }

  public async processMessage(payload: ISlaPayload) {
    // Check for duplicate message
    const isDuplicate = await this.isDuplicateMessage(payload);
    if (isDuplicate) {
      this.logger.log("Duplicate message detected, skipping processing");
      return;
    }

    const { eventType, eventId } = payload;
    try {
      this.logger.log(`Processing event: ${eventType}`);

      // Handle comment events
      if (eventType.startsWith("ticket:comment:")) {
        await this.processCommentEvent(payload, eventId);
      } else if (
        eventType.startsWith("ticket:") &&
        !eventType.startsWith("ticket:sla:")
      ) {
        await this.processTicketEvent(payload, eventId);
      }
      // Handle related entity events (account, user, etc.)
      else {
        await this.processRelatedEntityEvent(eventType, payload, eventId);
      }

      await this.markMessageAsProcessed(payload);
    } catch (error) {
      this.logger.error(
        `Error processing eventId: ${eventId}, eventType: ${eventType}, teamId: ${
          payload.teamId
        }, orgId: ${payload.orgId}. Error: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      throw error;
    }
  }

  private async processTicketEvent(payload: ISlaPayload, eventId: string) {
    const { eventType } = payload;

    if (SKIPPABLE_TICKET_EVENTS.includes(eventType as TicketEvents)) {
      this.logger.log(`Skipping ticket event ${eventType}`);
      return;
    }

    const ticketPayload = payload.payload.ticket as ISlaTicketPayload;
    const ticketData = await this.ticketsUtils.fetchTicketData(
      ticketPayload.id,
      null,
      payload.orgId,
    );

    if (!ticketData) {
      this.logger.error(`Ticket data not found for ticket ${ticketPayload.id}`);
      return;
    }

    await this.processTicketSLA(ticketData, payload, eventId);
  }

  private async processCommentEvent(payload: ISlaPayload, eventId: string) {
    const commentPayload = payload.payload as ISlaCommentPayload;

    if (!commentPayload.ticket.id) {
      this.logger.error(
        `Ticket ID not found in comment payload for event ${eventId}. payload: ${JSON.stringify(
          payload,
        )}`,
      );
      return;
    }

    const ticketData = await this.ticketsUtils.fetchTicketData(
      commentPayload.ticket.id,
      null,
      payload.orgId,
    );

    if (!ticketData) {
      this.logger.error(
        `Ticket data not found for comment on ticket ${commentPayload.ticket.id}`,
      );
      return;
    }

    await this.handleCommentSLA(payload, ticketData, eventId);
  }

  private extractEntityTypeAndId(eventType: string, payload: ISlaPayload) {
    if (eventType.startsWith("account:")) {
      return {
        entityType: EntityType.ACCOUNT,
        entityId: payload.payload.account.id,
      };
    }
    if (eventType.startsWith("user:")) {
      return { entityType: EntityType.USER, entityId: payload.payload.user.id };
    }
    return { entityType: null, entityId: null };
  }

  private async processRelatedEntityEvent(
    eventType: string,
    payload: ISlaPayload,
    eventId: string,
  ) {
    try {
      const { entityType, entityId } = this.extractEntityTypeAndId(
        eventType,
        payload,
      );

      if (!entityId) {
        this.logger.error(`Entity ID not found in payload for ${eventType}`);
        return;
      }

      this.logger.log(`Processing ${entityType} update for ID: ${entityId}`);

      // Get all policies that depend on this entity type
      const relevantPolicies =
        await this.slaPolicyService.findPoliciesByEntityType(
          entityType,
          payload.orgId,
        );

      if (relevantPolicies.length === 0) {
        this.logger.log(`No policies found that depend on ${entityType}`);
        return;
      }

      // Group policies by team for more efficient processing
      const policiesByTeam = this.groupPoliciesByTeam(relevantPolicies);
      this.logger.log(
        `Found policies across ${Object.keys(policiesByTeam).length} teams`,
      );

      // Process each team's tickets
      for (const [teamId, teamPolicies] of Object.entries(policiesByTeam)) {
        await this.processTeamTicketsForEntityUpdate(
          teamId,
          entityType,
          entityId,
          payload,
          eventId,
          teamPolicies,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error processing related entity event: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  private groupPoliciesByTeam(
    policies: SLAPolicy[],
  ): Record<string, SLAPolicy[]> {
    const groupedPolicies: Record<string, SLAPolicy[]> = {};
    for (const policy of policies) {
      if (!groupedPolicies[policy.teamId]) {
        groupedPolicies[policy.teamId] = [];
      }
      groupedPolicies[policy.teamId].push(policy);
    }
    return groupedPolicies;
  }

  private async processTeamTicketsForEntityUpdate(
    teamId: string,
    entityType: string,
    entityId: string,
    payload: ISlaPayload,
    eventId: string,
    teamPolicies: SLAPolicy[] = [],
  ) {
    try {
      if (!teamPolicies.length) {
        this.logger.warn(
          `No policies found for team ${teamId} with entity type ${entityType}`,
        );
        return;
      }

      // Fetch tickets affected by this entity update
      const tickets = await this.fetchTicketsByEntityRelation(
        teamId,
        entityType,
        entityId,
        payload.orgId,
        teamPolicies,
      );

      if (!tickets.length) {
        this.logger.log(
          `No tickets found for team ${teamId} with ${entityType} relation for ID ${entityId}`,
        );
        return;
      }

      this.logger.log(
        `Found ${tickets.length} tickets affected by ${entityType} update in team ${teamId}`,
      );

      // Process tickets with a concurrency limit to avoid overwhelming the system
      const CONCURRENCY_LIMIT = 50;
      const chunks = this.chunkArray(tickets, CONCURRENCY_LIMIT);

      for (const chunk of chunks) {
        await Promise.all(
          chunk.map(async (ticket) => {
            try {
              await this.processTicketSLA(ticket, payload, eventId);
            } catch (error) {
              this.logger.error(
                `Error processing ticket ${ticket.id} for entity update: ${
                  error instanceof Error ? error.message : String(error)
                }`,
              );
            }
          }),
        );
      }
    } catch (error) {
      this.logger.error(
        `Error processing team ${teamId} tickets for ${entityType} update: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  // Helper method to chunk an array for controlled concurrency
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private async fetchTicketsByEntityRelation(
    teamId: string,
    entityType: string,
    entityId: string,
    organizationId: string,
    policies: SLAPolicy[] = [],
  ): Promise<ITicket[]> {
    try {
      if (!policies.length) {
        this.logger.warn(`No policies provided for team ${teamId}`);
        return [];
      }

      // Group policies by their entity relation paths
      const relationGroups: Set<string> = new Set();

      // Collect and group relation paths for the target entity type
      policies.forEach((policy) => {
        const relationships = policy.metadata?.relationships || [];

        // Find relationships that match our target entity type
        relationships
          .filter((rel) => rel.entityType === entityType)
          .forEach((rel) => {
            const relationPath = rel.relation;

            relationGroups.add(relationPath);
          });
      });

      // If no relevant relation paths found
      const relationPaths = Array.from(relationGroups);
      if (relationPaths.length === 0) {
        this.logger.warn(
          `No ${entityType} relation paths found in policies for team ${teamId}`,
        );
        return [];
      }

      this.logger.log(
        `Found ${
          relationPaths.length
        } distinct relation paths for ${entityType}: ${relationPaths.join(
          ", ",
        )}`,
      );

      // Execute queries for each relation group and collect results
      const allTickets: Map<string, ITicket> = new Map();

      for (const relationPath of relationPaths) {
        try {
          // Build the specific filter condition for this relation path
          const filterCondition: Record<string, any> = { "team.id": teamId };

          // Determine the ID field path
          // For "assignee" we need "assignee.id"
          // For "account.accountOwner" we need "account.accountOwner.id"
          const idFieldPath = `${relationPath}.id`;

          filterCondition[idFieldPath] = entityId;

          this.logger.log(
            `Querying tickets for relation path "${relationPath}" with filter: ${JSON.stringify(
              filterCondition,
            )}` + ` including relations: ${relationPath}, team`,
          );

          const orgAdmin = await this.usersGrpcClient.fetchOrganizationAdmin(
            organizationId,
          );

          // Execute the query with the specific filter and relations
          const response = await this.annotatorClient.filterEntityData(
            {
              entityType: "Ticket",
              data: JSON.stringify(filterCondition),
              relations: [relationPath, "team"],
            },
            {
              user_id: orgAdmin.userId,
              org_id: organizationId,
            },
          );

          if (response && response.data) {
            // Convert response to array if needed
            const tickets = JSON.parse(response.data) as ITicket[];

            // Add results to our collection, avoiding duplicates
            tickets.forEach((ticket) => {
              if (ticket && ticket.id) {
                allTickets.set(ticket.id, ticket);
              }
            });

            this.logger.log(
              `Found ${tickets.length} tickets for relation path "${relationPath}"`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Error querying tickets for relation path "${relationPath}": ${
              error instanceof Error ? error.message : String(error)
            }`,
          );
        }
      }

      const results = Array.from(allTickets.values());
      this.logger.log(
        `Found total of ${results.length} unique tickets affected by ${entityType} update`,
      );

      return results;
    } catch (error) {
      this.logger.error(
        `Error fetching tickets for team ${teamId} with ${entityType} relation: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return [];
    }
  }

  private async processTicketSLA(
    ticketData: ITicket,
    payload: ISlaPayload,
    eventId: string,
  ) {
    this.logger.log(`Processing SLA for ticket ${ticketData.id}`);

    // Handle ticket SLA achievement
    await this.handleTicketSLAAchievement(payload, ticketData, eventId);

    // Create or update SLA ticket - use "agent" as the author type
    await this.createSLATicket(payload, ticketData, eventId, "agent");

    // Handle pause/resume logic
    await this.pauseSLATicket(payload, ticketData);
    await this.resumeSLATicket(payload, ticketData);

    this.logger.log(`Completed SLA processing for ticket ${ticketData.id}`);
  }

  private async handleTicketSLAAchievement(
    payload: ISlaPayload,
    ticketData: ITicket,
    updateEventId: string,
  ) {
    this.logger.log("Getting active jobs for ticket");
    const jobsToAchieve = await this.getJobsToAchieve(ticketData.id);
    if (jobsToAchieve && jobsToAchieve.length > 0) {
      this.logger.log("Active jobs found");
      for (const job of jobsToAchieve) {
        try {
          this.logger.log("Achieving SLA");
          await this.achieveSLA(job, ticketData, payload, updateEventId);
        } catch (error) {
          this.logger.error(
            error instanceof Error ? error.message : String(error),
          );
          continue;
        }
      }
    }
  }

  private async handleCommentSLA(
    payload: ISlaPayload,
    ticketData: ITicket,
    updateEventId: string,
  ) {
    this.logger.log("Handling comment SLA");
    if (!ticketData) {
      this.logger.error("Ticket data not found, cant handle comment sla");
      throw new Error("Ticket data not found, cant handle comment sla");
    }

    const comment = await this.ticketsUtils.fetchCommentData(
      (payload.payload as ISlaCommentPayload).comment.id,
      payload.actor.id,
      payload.orgId,
    );

    if (!comment) {
      // Happens for comment deleted event
      this.logger.error("Comment not found, cant handle comment sla");
      return;
    }

    if (!comment.customerContactId && !comment.customerContactEmail) {
      this.logger.log("Handling agent comment");
      await this.handleAgentComment(ticketData, payload, updateEventId);
    } else {
      this.logger.log("Handling customer comment");

      await this.handleCustomerComment(payload, ticketData, updateEventId);
    }

    await this.pauseSLATicket(payload, ticketData);
    this.logger.log("Ticket sla ticket paused");
    await this.resumeSLATicket(payload, ticketData);
    this.logger.log("Ticket sla ticket resumed");
  }

  private getJobsToAchieve(ticketId: string) {
    return this.slaScheduledRepository.findWithRelations({
      where: {
        jobId: ticketId,
        status: In([
          SLAJobStatus.RUNNING,
          SLAJobStatus.SCHEDULED,
          SLAJobStatus.RESUMED,
          SLAJobStatus.BREACHED,
        ]),
        achievedAt: IsNull(),
      },
      relations: ["policy"],
    });
  }

  private getActiveJobsForTicket(ticketId: string) {
    return this.slaScheduledRepository.findWithRelations({
      where: {
        jobId: ticketId,
        status: In([SLAJobStatus.RUNNING, SLAJobStatus.SCHEDULED]),
      },
      relations: ["policy"],
    });
  }

  private getPausedJobsForTicket(ticketId: string) {
    return this.slaScheduledRepository.findWithRelations({
      where: {
        jobId: ticketId,
        status: In([SLAJobStatus.PAUSED]),
      },
      relations: ["policy"],
    });
  }

  private async handleAgentComment(
    ticketData: ITicket,
    payload: ISlaPayload,
    updateEventId: string,
  ) {
    const jobsToAchieve = await this.getJobsToAchieve(ticketData.id);
    for (const job of jobsToAchieve) {
      try {
        await this.achieveSLA(job, ticketData, payload, updateEventId);
      } catch (error) {
        this.logger.error(
          error instanceof Error ? error.message : String(error),
        );
        continue;
      }
    }
    await this.createSLATicket(payload, ticketData, updateEventId, "agent");
  }

  private async handleCustomerComment(
    payload: ISlaPayload,
    ticketData: ITicket,
    updateEventId: string,
  ) {
    const activeJobs = await this.getActiveJobsForTicket(ticketData.id);

    if (activeJobs && activeJobs.length > 0) {
      const activeResponseJobExists = activeJobs.some(
        (job) =>
          job.metric === SLAMetricType.NEXT_TIME_RESPONSE ||
          job.metric === SLAMetricType.FIRST_TIME_RESPONSE,
      );
      if (activeResponseJobExists) {
        this.logger.log("Active SLA jobs exist, continuing with existing jobs");
        return;
      }
    }
    const payloadForPolicies = {
      ...payload,
      entity_id: ticketData.id,
      event_name: payload.eventType,
    };

    await this.createSLATicket(
      payloadForPolicies,
      ticketData,
      updateEventId,
      "customer",
    );
  }

  private async achieveSLA(
    job: ScheduledSLA,
    ticketData: ITicket,
    payload: ISlaPayload,
    updateEventId: string,
  ) {
    this.logger.log(`Achieving SLA for ticket: ${ticketData.ticketId}`);
    if (!job) return;

    try {
      this.logger.log("Finding metric object");
      const metricObject = this.findMetricObject(job);
      if (!metricObject) {
        this.logger.log("Metric object not found");
        return;
      }
      this.logger.log("Metric object found");

      const { isAchieved, achievedAt } =
        await this.checkAndUpdateSLAAchievement(job, metricObject, ticketData);
      if (isAchieved) {
        await this.handleSLAAchievement(
          job,
          payload,
          updateEventId,
          achievedAt,
        );
      }
    } catch (error) {
      console.error("Error achieving SLA:", error);
      throw error;
    }
  }

  private findMetricObject(job: ScheduledSLA) {
    return job.policy.policyMetrics.find((m) => m.metric === job.metric);
  }

  private checkAndUpdateSLAAchievement(
    job: ScheduledSLA,
    metricObject: PolicyMetric,
    ticketData: ITicket,
  ) {
    this.logger.log("Checking and updating SLA achievement");
    return this.slaBreachUtils.checkSlaAchievements(
      metricObject,
      ticketData,
      job.policy.createdBy,
      job.policy.organizationId,
      job.resumedAt || job.scheduledAt,
      SlaEntityType.TICKET,
    );
  }

  private async handleSLAAchievement(
    job: ScheduledSLA,
    payload: ISlaPayload,
    updateEventId: string,
    achievedAt: Date,
  ) {
    this.logger.log("SLA ACHIEVED");
    const bullQueueJob = await this.slaQueue.getJob(job.scheduledJobId);
    if (bullQueueJob) {
      await bullQueueJob.remove();
    }

    if (job.metric === SLAMetricType.TOTAL_RESOLUTION_TIME) {
      await this.handleTotalResolutionTimeAchievement(
        job,
        payload,
        updateEventId,
      );
    }

    if (job.status === SLAJobStatus.BREACHED) {
      await this.slaScheduledRepository.update(job.id, {
        achievedAt: achievedAt ?? new Date(),
      });
      return;
    }

    await this.slaScheduledRepository.update(job.id, {
      status: SLAJobStatus.ACHIEVED,
      achievedAt: achievedAt ?? new Date(),
    });

    await this.slaAuditLogsService.updateOrCreate({
      scheduledJobId: bullQueueJob?.id?.toString() || "NA",
      status: SLAJobStatus.ACHIEVED,
      policyId: job.policy.uid,
      entityType: SlaEntityType.TICKET,
      entityId: job.jobId,
      organizationId: job.policy.organizationId,
      teamId: job.policy.teamId,
      eventType: payload.eventType,
      message: `SLA achieved for ${job.metric} duration and created new SLA for ${job.policy.name} which leads to SLA breach`,
      specificMetric: job.specificPolicyMetric,
      metric: job.metric as SLAMetricType,
      updateEventId,
      userId: job.policy.createdBy,
    });

    await this.publishToSNS({
      entityId: job.jobId,
      entityType: SlaEntityType.TICKET,
      event: SLAJobStatus.ACHIEVED,
      metric: job.metric as SLAMetricType,
      subject: "SLA Achieved",
      organizationId: job.policy.organizationId,
      teamId: job.policy.teamId,
      user: job.policy.createdBy,
      policy: job.policy,
    });
  }

  private async handleTotalResolutionTimeAchievement(
    job: ScheduledSLA,
    payload: ISlaPayload,
    updateEventId: string,
  ) {
    const relatedJobs = await this.slaScheduledRepository.findWithRelations({
      where: {
        jobId: job.jobId,
        metric: Not(SLAMetricType.TOTAL_RESOLUTION_TIME),
        status: In([
          SLAJobStatus.SCHEDULED,
          SLAJobStatus.RUNNING,
          SLAJobStatus.PAUSED,
        ]),
      },
      relations: ["policy"],
    });

    for (const relatedJob of relatedJobs) {
      const bullQueueJob = await this.slaQueue.getJob(
        relatedJob.scheduledJobId,
      );

      await this.slaScheduledRepository.update(relatedJob.id, {
        status: SLAJobStatus.CANCELLED,
        cancelledAt: new Date(),
      });

      await this.slaAuditLogsService.updateOrCreate({
        scheduledJobId: bullQueueJob?.id?.toString() || "NA",
        status: SLAJobStatus.CANCELLED,
        policyId: relatedJob.policy.uid,
        entityType: SlaEntityType.TICKET,
        entityId: relatedJob.jobId,
        organizationId: relatedJob.policy.organizationId,
        teamId: relatedJob.policy.teamId,
        eventType: payload.eventType,
        message: `SLA cancelled for ${relatedJob.metric} as total resolution time SLA is achieved`,
        specificMetric: relatedJob.specificPolicyMetric,
        metric: relatedJob.metric as SLAMetricType,
        updateEventId,
        userId: relatedJob.policy.createdBy,
      });

      await this.publishToSNS({
        entityId: relatedJob.jobId,
        entityType: SlaEntityType.TICKET,
        event: SLAJobStatus.CANCELLED,
        metric: relatedJob.metric as SLAMetricType,
        subject: "SLA Cancelled",
        organizationId: relatedJob.policy.organizationId,
        teamId: relatedJob.policy.teamId,
        user: relatedJob.policy.createdBy,
        policy: relatedJob.policy,
      });
    }
  }

  private async pauseSLATicket(payload: ISlaPayload, ticketData: ITicket) {
    const activeJobs = await this.getActiveJobsForTicket(ticketData.id);
    for (const job of activeJobs) {
      const ticketWithRelations =
        await this.ticketsUtils.fetchTicketWithRelations(
          ticketData.id,
          job.policy.createdBy,
          payload.orgId,
          job.policy,
        );

      await this.pauseSLAJob(job, ticketWithRelations, payload);
    }
  }

  private async resumeSLATicket(payload: ISlaPayload, ticketData: ITicket) {
    const pausedJob = await this.getPausedJobsForTicket(ticketData.id);
    for (const job of pausedJob) {
      const ticketWithRelations =
        await this.ticketsUtils.fetchTicketWithRelations(
          ticketData.id,
          job.policy.createdBy,
          payload.orgId,
          job.policy,
        );
      await this.resumeSLAJob(job, payload, ticketWithRelations);
    }
  }

  private async resumeSLAJob(
    job: ScheduledSLA,
    payload: ISlaPayload,
    ticketData: ITicket,
  ) {
    const policy = job.policy;

    const shouldRemainPaused = this.checkIfTicketMatchesFilter(
      ticketData,
      policy,
      "pause",
    );

    if (!shouldRemainPaused) {
      // const durationLeft = job.durationToBreach;
      const teamConfiguration = await this.teamUtils.getTeamConfiguration(
        job.policy.teamId,
        policy.createdBy,
        payload.orgId,
      );
      const workingHours = new WorkingHours(teamConfiguration);

      const pausedDurationInWorkingMinutes =
        workingHours.calculateWorkingMinutes(job.pauseAt, new Date());

      const jobDuration = workingHours.getJobDurationInMinutes(
        new Date(),
        job.durationToBreachWorkingMinutes,
      );

      const dataForBullQueue: ScheduledJobPayload = {
        entityId: ticketData.id,
        eventName: payload.eventType,
        entityType: SlaEntityType.TICKET,
        policyId: policy.uid,
        metricObject: job.metricObject,
        specifPolicyMetric: job.specificPolicyMetric,
        userId: policy.createdBy,
        scheduledAt: job.scheduledAt,
        organizationId: policy.organizationId,
        jobDuration,
        resumedAt: new Date(),
      };
      this.logger.log(`dataForBullQueue: ${JSON.stringify(dataForBullQueue)}`);
      const newBullQueueJob = await this.slaQueue.add(
        "process-sla",
        dataForBullQueue,
        {
          delay: jobDuration * 60 * 1000,
          attempts: 3,
          removeOnComplete: true,
        },
      );
      await this.slaScheduledRepository.update(
        {
          id: job.id,
          jobId: job.jobId,
        },
        {
          status: SLAJobStatus.SCHEDULED,
          resumedAt: new Date(),
          nextAttemptAt: new Date(Date.now() + jobDuration * 60 * 1000),
          scheduledJobId: newBullQueueJob.id.toString(),
          scheduledJobPayload: dataForBullQueue,
          pausedDurationInWorkingMinutes:
            (job.pausedDurationInWorkingMinutes || 0) +
            pausedDurationInWorkingMinutes,
        },
      );
      await this.slaAuditLogsService.updateOrCreate({
        scheduledJobId: newBullQueueJob.id.toString(),
        status: SLAJobStatus.RESUMED,
        policyId: job.policy.uid,
        entityType: SlaEntityType.TICKET,
        entityId: job.jobId,
        organizationId: job.policy.organizationId,
        teamId: job.policy.teamId,
        eventType: payload.eventType,
        message: `SLA resumed for ticket ${job.jobId}`,
        specificMetric: job.specificPolicyMetric,
        metric: job.metric as SLAMetricType,
        filter: job.policy.filter,
        userId: job.policy.createdBy,
      });

      await this.publishToSNS({
        entityId: job.jobId,
        entityType: SlaEntityType.TICKET,
        event: SLAJobStatus.SCHEDULED,
        metric: job.metric as SLAMetricType,
        subject: "SLA Scheduled",
        organizationId: job.policy.organizationId,
        teamId: job.policy.teamId,
        user: job.policy.createdBy,
        policy: job.policy,
        duration: jobDuration,
      });
    }
  }

  private async pauseSLAJob(
    job: ScheduledSLA,
    ticketData: ITicket,
    payload: ISlaPayload,
  ) {
    const policy = job.policy;

    const needToPause = this.checkIfTicketMatchesFilter(
      ticketData,
      policy,
      "pause",
    );
    if (
      needToPause &&
      Object.keys(policy.pauseConditions).length > 0 &&
      (policy.pauseConditions.all.length > 0 ||
        policy.pauseConditions.any.length > 0)
    ) {
      const durationLeft = await this.slaEngineUtils.getDurationLeft(
        job,
        ticketData,
        policy.createdBy,
        payload.orgId,
      );
      const bullQueueJob = await this.slaQueue.getJob(job.scheduledJobId);
      if (bullQueueJob) {
        this.logger.log("Removing from bull queue");
        await bullQueueJob.remove();
      }
      await this.slaScheduledRepository.update(job.id, {
        status: SLAJobStatus.PAUSED,
        pauseAt: new Date(),
        durationToBreachWorkingMinutes: durationLeft,
      });
      await this.slaAuditLogsService.updateOrCreate({
        scheduledJobId: bullQueueJob.id.toString(),
        status: SLAJobStatus.PAUSED,
        policyId: job.policy.uid,
        entityType: SlaEntityType.TICKET,
        entityId: job.jobId,
        organizationId: job.policy.organizationId,
        teamId: job.policy.teamId,
        eventType: payload.eventType,
        message: `SLA paused for ticket ${job.jobId}`,
        specificMetric: job.specificPolicyMetric,
        metric: job.metric as SLAMetricType,
        filter: job.policy.filter,
        userId: job.policy.createdBy,
      });

      await this.publishToSNS({
        entityId: job.jobId,
        entityType: SlaEntityType.TICKET,
        event: SLAJobStatus.PAUSED,
        metric: job.metric as SLAMetricType,
        subject: "SLA Paused",
        organizationId: job.policy.organizationId,
        teamId: job.policy.teamId,
        user: job.policy.createdBy,
        policy: job.policy,
      });
    }
  }

  private async createSLATicket(
    payload: ISlaPayload,
    ticketData: ITicket,
    updateEventId: string,
    authorUserType: "agent" | "customer",
  ) {
    if (!ticketData) {
      throw new Error("Ticket data not found, cant create sla ticket");
    }
    if (ticketData.status?.name.toLowerCase() === "closed") {
      this.logger.log("Ticket is closed, skipping sla creation");
      return;
    }
    this.logger.log("Calculating possible sla policies");
    const { policiesToTrigger } = await this.calculatePossibleSlAPolicies(
      payload,
      SlaEntityType.TICKET,
      ticketData,
    );
    this.logger.log(
      `Policies to trigger: ${JSON.stringify(policiesToTrigger)}`,
    );
    const metricMap = await this.slaEngineUtils.buildMetricMap(
      ticketData,
      policiesToTrigger,
      payload.eventType,
      authorUserType,
    );
    this.logger.log(`Metric map for creation: ${JSON.stringify(metricMap)}`);

    // Cancel jobs which are existing but not present in the metric map
    const existingJobs = await this.getActiveJobsForTicket(ticketData.id);

    const metricMapToRun = await this.slaEngineUtils.buildMetricMap(
      ticketData,
      policiesToTrigger,
      payload.eventType,
      authorUserType,
      false,
    );
    this.logger.log(
      `Metric map for cancellation: ${JSON.stringify(metricMap)}`,
    );

    const ongoingMetricJobs = existingJobs.map((job) => job.metric);

    const metricJobsToCancel = ongoingMetricJobs.filter(
      (metric) => !metricMapToRun.has(metric as SLAMetricType),
    );

    for (const metric of metricJobsToCancel) {
      const job = existingJobs.find((job) => job.metric === metric);
      if (job) {
        await this.cancelExistingJob(job);

        job.status = SLAJobStatus.CANCELLED;
        job.cancelledAt = new Date();
        await this.slaScheduledRepository.update(job.id, job);

        await this.logJobCancellation(
          job,
          {
            policy: job.policy,
            specifPolicyMetric: job.specificPolicyMetric,
          },
          ticketData,
          payload,
          job.metricObject,
          updateEventId,
          `Cancelled SLA for ${metric} duration due to a ticket update`,
        );

        await this.publishToSNS({
          entityId: job.jobId,
          entityType: SlaEntityType.TICKET,
          event: SLAJobStatus.CANCELLED,
          subject: "SLA Cancelled",
          metric: job.metric as SLAMetricType,
          organizationId: job.organizationId,
          teamId: job.policy.teamId,
          user: job.policy.createdBy,
          policy: job.policy,
        });
      }
    }

    for (const key of metricMap.keys()) {
      this.logger.log(`Processing metric: ${key}`);
      await this.processMetricJob(
        key,
        metricMap.get(key),
        ticketData,
        payload,
        updateEventId,
      );
    }
    await this.logExpectedButUnConfiguredSLAs(metricMap, payload, ticketData);
  }

  private async logExpectedButUnConfiguredSLAs(
    metricMap: Map<
      string,
      { duration: number; specifPolicyMetric: Filter; policy: SLAPolicy }
    >,
    payload: ISlaPayload,
    ticketData: ITicket,
  ) {
    const metricChecks = {
      [SLAMetricType.FIRST_TIME_RESPONSE]: {
        shouldCheck: true,
        additionalFilters: {},
      },
      [SLAMetricType.TOTAL_RESOLUTION_TIME]: {
        shouldCheck: true,
        additionalFilters: {},
      },
      [SLAMetricType.NEXT_TIME_RESPONSE]: {
        shouldCheck:
          payload.eventType === TicketEvents.COMMENT_ADDED &&
          (payload.payload as ISlaCommentPayload).comment.parentCommentId !==
            null,
        additionalFilters: { status: SLAJobStatus.SCHEDULED },
      },
      [SLAMetricType.UPDATE_TIME]: {
        shouldCheck:
          payload.eventType === TicketEvents.COMMENT_ADDED &&
          (payload.payload as ISlaCommentPayload).comment.parentCommentId ===
            null,
        additionalFilters: { status: SLAJobStatus.SCHEDULED },
      },
    };

    for (const [metricType, config] of Object.entries(metricChecks)) {
      if (!config.shouldCheck) continue;

      const metric = metricMap.get(metricType);
      const existingJobs = await this.checkExistingJobs(
        metricType,
        ticketData.id,
        config.additionalFilters,
      );

      if (existingJobs) continue;

      if (!metric) {
        await this.logUnscheduledMetric(metricType, ticketData, payload);
      }
    }
  }

  private async checkExistingJobs(
    metric: string,
    ticketId: string,
    additionalFilters: Record<string, any> = {},
  ): Promise<boolean> {
    const whereCondition = {
      metric,
      jobId: ticketId,
      ...additionalFilters,
    };

    const scheduledJobs = await this.slaScheduledRepository.findByCondition({
      where: whereCondition,
    });

    return !!scheduledJobs;
  }

  private async logUnscheduledMetric(
    metricType: string,
    ticketData: ITicket,
    payload: ISlaPayload,
  ): Promise<void> {
    await this.slaAuditLogsService.updateOrCreate({
      scheduledJobId: "NA",
      status: SLAJobStatus.NOT_SCHEDULED,
      policyId: "NA",
      entityType: SlaEntityType.TICKET,
      entityId: ticketData.id,
      organizationId: payload.orgId,
      teamId: ticketData.team.id,
      eventType: payload.eventType,
      message: `No policy created for ${metricType}`,
      specificMetric: null,
      metric: metricType as SLAMetricType,
      filter: null,
      userId: payload.actor.id,
    });
  }

  private async processMetricJob(
    metric: SLAMetricType,
    details: {
      duration: number;
      specifPolicyMetric: Filter;
      policy: SLAPolicy;
    },
    ticketData: ITicket,
    payload: ISlaPayload,
    updateEventId: string,
  ) {
    this.logger.log(`Checking for existing job for metric: ${metric}`);
    const existingJob: ScheduledSLA = await this.findExistingJob(
      ticketData.id,
      metric,
    );

    if (existingJob) {
      this.logger.log("Existing job found");
      await this.handleExistingJob(
        existingJob,
        details,
        ticketData,
        payload,
        updateEventId,
      );
    } else {
      this.logger.log("No existing job found");
      // Check for completed jobs (breached or achieved) for specific metrics
      if (
        metric === SLAMetricType.FIRST_TIME_RESPONSE ||
        metric === SLAMetricType.TOTAL_RESOLUTION_TIME
      ) {
        this.logger.log("Checking for completed jobs");
        const completedJob =
          await this.slaScheduledRepository.findWithRelations({
            where: {
              jobId: ticketData.id,
              metric: metric,
              status: In([SLAJobStatus.BREACHED, SLAJobStatus.ACHIEVED]),
            },
            relations: ["policy"],
            order: {
              createdAt: "DESC",
            },
            take: 1,
          });

        if (completedJob.length > 0) {
          this.logger.log("Completed job found");
          const lastJob = completedJob[0];
          this.logger.log(
            `${metric} already ${lastJob.status.toLowerCase()}, skipping new job creation`,
          );
          this.slaAuditLogsService.updateOrCreate({
            scheduledJobId: "NA",
            status: lastJob.status,
            policyId: lastJob.policy.uid,
            entityType: SlaEntityType.TICKET,
            entityId: ticketData.id,
            organizationId: lastJob.policy.organizationId,
            teamId: lastJob.policy.teamId,
            eventType: payload.eventType,
            message: `SLA already ${lastJob.status.toLowerCase()}, skipping new job creation`,
            specificMetric: lastJob.specificPolicyMetric,
            metric: metric as SLAMetricType,
            filter: lastJob.policy.filter,
            userId: lastJob.policy.createdBy,
          });

          return;
        }
      }

      // If no completed job exists, create a new one
      await this.createNewJob(
        metric,
        details,
        ticketData,
        payload,
        updateEventId,
      );
    }
  }

  private async calculatePossibleSlAPolicies(
    payload: ISlaPayload,
    entityType: SlaEntityType,
    entityData: ITicket,
  ): Promise<{ policiesToTrigger: SLAPolicy[] }> {
    this.logger.log(
      `Processing for ${payload.eventType} ${entityData.id} ${entityType}`,
    );
    const policies = await this.slaPolicyService.findAllByCondition(
      entityData.team.id,
      entityType,
      payload.orgId,
    );
    const policiesToTrigger = [];

    // TODO: Can optimize fetchTicketWithRelations
    for (const policy of policies) {
      try {
        const ticketWithRelations =
          await this.ticketsUtils.fetchTicketWithRelations(
            entityData.id,
            payload.actor.id,
            payload.orgId,
            policy,
          );

        const matchedFilters = this.checkIfTicketMatchesFilter(
          ticketWithRelations,
          policy,
          "filter",
        );
        if (matchedFilters) {
          policiesToTrigger.push(policy);
        }
      } catch (error) {
        this.logger.error(
          error instanceof Error ? error.message : String(error),
        );
        continue;
      }
    }

    return { policiesToTrigger };
  }

  private checkIfTicketMatchesFilter(
    ticketData: any, // Using any since we'll have fully populated ticket with relations
    policy: SLAPolicy,
    type: "filter" | "pause",
  ): boolean {
    // Determine which conditions to check based on type
    const conditions =
      type === "filter" ? policy.filter : policy.pauseConditions;

    if (!conditions) {
      return false;
    }

    // Check 'all' conditions (AND)
    let matchedAllConditions = true;
    if (conditions.all && conditions.all.length > 0) {
      for (const filter of conditions.all) {
        if (!ticketData) {
          this.logger.warn(`No ticket data available for filter matching`);
          return false;
        }

        const matched = this.slaEngineUtils.matchesCondition(
          ticketData,
          filter,
        );
        if (!matched) {
          matchedAllConditions = false;
          break;
        }
      }
    }

    // Check 'any' conditions (OR)
    let matchedAnyConditions = false;
    if (conditions.any && conditions.any.length > 0) {
      for (const filter of conditions.any) {
        if (!ticketData) {
          this.logger.warn(`No ticket data available for filter matching`);
          return false;
        }

        const matched = this.slaEngineUtils.matchesCondition(
          ticketData,
          filter,
        );
        if (matched) {
          matchedAnyConditions = true;
          break;
        }
      }
    } else {
      // If no 'any' conditions exist, consider it as matched
      matchedAnyConditions = true;
    }

    // Return true only if both conditions are satisfied
    return matchedAllConditions && matchedAnyConditions;
  }

  private async findExistingJob(ticketId: string, metric: string) {
    const whereCondition: any = {
      jobId: ticketId,
      metric: metric,
    };

    // Only add status filter for metrics other than first_time_response
    whereCondition.status = In([
      SLAJobStatus.RUNNING,
      SLAJobStatus.SCHEDULED,
      SLAJobStatus.PAUSED,
    ]);

    const existingJobs = await this.slaScheduledRepository.findWithRelations({
      where: whereCondition,
      relations: ["policy"],
      order: {
        createdAt: "DESC",
      },
      take: 1,
    });
    return existingJobs.length > 0 ? existingJobs[0] : null;
  }

  private async handleExistingJob(
    existingJob: ScheduledSLA,
    details: {
      duration: number;
      specifPolicyMetric: Filter;
      policy: SLAPolicy;
    },
    ticketData: ITicket,
    payload: ISlaPayload,
    updateEventId: string,
  ) {
    const isPolicyDifferent = existingJob.policy.id !== details.policy.id;
    const isSpecificMetricDifferent = this.slaEngineUtils.isFilterDifferent(
      existingJob.specificPolicyMetric,
      details.specifPolicyMetric,
    );

    if (isPolicyDifferent || isSpecificMetricDifferent) {
      await this.cancelExistingJob(existingJob);

      const metricObject = this.slaEngineUtils.getUpdatedMetricObject(
        existingJob,
        details,
        isPolicyDifferent,
      );

      await this.logJobCancellation(
        existingJob,
        details,
        ticketData,
        payload,
        metricObject,
        updateEventId,
      );

      const { isBreached, breachedAt } = await this.checkSLABreach(
        metricObject,
        details,
        ticketData,
        existingJob,
      );

      if (isBreached) {
        await this.handleBreachedSLA(
          existingJob,
          details,
          ticketData,
          payload,
          metricObject,
          breachedAt,
        );
      } else {
        await this.handleUnBreachedSLA(
          existingJob,
          details,
          ticketData,
          payload,
          metricObject,
        );
      }
    }
  }

  private async cancelExistingJob(existingJob: ScheduledSLA) {
    const bullQueueJob = await this.slaQueue.getJob(existingJob.scheduledJobId);
    if (bullQueueJob) {
      await bullQueueJob.remove();
    }
  }

  private async logJobCancellation(
    existingJob: ScheduledSLA,
    details: { policy: SLAPolicy; specifPolicyMetric: Filter },
    ticketData: ITicket,
    payload: ISlaPayload,
    metricObject: any,
    updateEventId: string,
    message?: string,
  ) {
    await this.slaAuditLogsService.updateOrCreate({
      scheduledJobId: existingJob.scheduledJobId,
      status: SLAJobStatus.CANCELLED,
      policyId: existingJob.policy.uid,
      entityType: SlaEntityType.TICKET,
      entityId: ticketData.id,
      organizationId: existingJob.organizationId,
      teamId: existingJob.policy.teamId,
      eventType: payload.eventType,
      message:
        message ||
        `Cancelled SLA for ${existingJob.metric} duration and created new SLA for ${details.policy.name}`,
      specificMetric: details.specifPolicyMetric,
      metric: metricObject.metric as SLAMetricType,
      filter: existingJob.policy.filter,
      updateEventId,
      userId: existingJob.policy.createdBy,
    });
  }

  private checkSLABreach(
    metricObject: PolicyMetric,
    details: {
      specifPolicyMetric: Filter;
      policy: SLAPolicy;
      duration: number;
    },
    ticketData: ITicket,
    existingJob: ScheduledSLA,
  ) {
    return this.slaBreachUtils.checkBreach(
      metricObject,
      ticketData,
      details.policy.createdBy,
      details.policy.organizationId,
      existingJob.scheduledAt,
      SlaEntityType.TICKET,
      details.duration,
    );
  }

  private async handleBreachedSLA(
    existingJob: ScheduledSLA,
    details: { policy: SLAPolicy; specifPolicyMetric: Filter },
    ticketData: ITicket,
    payload: ISlaPayload,
    metricObject: PolicyMetric,
    breachedAt: Date,
  ) {
    this.logger.log("SLA BREACHED");
    await this.updateBreachedJob(existingJob, breachedAt);
    await this.logBreachedSLA(
      existingJob,
      details,
      ticketData,
      payload,
      metricObject,
    );
    await this.publishToSNS({
      entityId: existingJob.jobId,
      entityType: SlaEntityType.TICKET,
      event: SLAJobStatus.BREACHED,
      subject: "SLA Breached",
      metric: metricObject.metric,
      organizationId: existingJob.organizationId,
      teamId: details.policy.teamId,
      user: details.policy.createdBy,
      policy: details.policy,
    });
  }

  private async updateBreachedJob(existingJob: ScheduledSLA, breachedAt: Date) {
    await this.slaScheduledRepository.update(
      {
        id: existingJob.id,
        jobId: existingJob.jobId,
      },
      {
        status: SLAJobStatus.BREACHED,
        breachedAt: breachedAt ?? new Date(),
      },
    );
  }

  private async handleUnBreachedSLA(
    existingJob: ScheduledSLA,
    details: {
      duration: number;
      specifPolicyMetric: Filter;
      policy: SLAPolicy;
    },
    ticketData: ITicket,
    payload: ISlaPayload,
    metricObject: any,
  ) {
    this.logger.log("SLA NOT BREACHED");
    await this.updateCancelledJob(existingJob);

    const teamConfiguration = await this.teamUtils.getTeamConfiguration(
      details.policy.teamId,
      details.policy.createdBy,
      payload.orgId,
    );

    const cancelledJobs = await this.slaScheduledRepository.findAll({
      where: {
        jobId: ticketData.id,
        metric: existingJob.metric,
        status: SLAJobStatus.CANCELLED,
      },
    });

    let pausedDurationInWorkingMinutes = 0;
    for (const job of cancelledJobs) {
      pausedDurationInWorkingMinutes += job.pausedDurationInWorkingMinutes
        ? Number(job.pausedDurationInWorkingMinutes)
        : 0;
    }

    const eventTime = await this.slaEngineUtils.getEventTime(
      payload,
      existingJob.metric,
      SlaEntityType.TICKET,
      ticketData,
    );
    const workingHours = new WorkingHours(teamConfiguration);
    const breachDuration = workingHours.getJobDurationInMinutes(
      eventTime,
      Number(details.duration) + Number(pausedDurationInWorkingMinutes),
    );
    const breachTime = new Date(eventTime.getTime() + breachDuration * 60000);

    const jobDuration = (breachTime.getTime() - Date.now()) / 60000;

    this.logger.log(`breachDuration: ${breachDuration}`);
    const dataForBullQueue = {
      entityId: ticketData.id,
      eventName: payload.eventType,
      entityType: SlaEntityType.TICKET,
      policyId: details.policy.uid,
      metricObject: metricObject,
      specifPolicyMetric: details.specifPolicyMetric,
      userId: details.policy.createdBy,
      scheduledAt: eventTime,
      organizationId: details.policy.organizationId,
      jobDuration: breachDuration,
    };
    this.logger.log(`dataForBullQueue: ${JSON.stringify(dataForBullQueue)}`);
    const bullQueueJob = await this.slaQueue.add(
      "process-sla",
      dataForBullQueue,
      {
        delay: jobDuration > 0 ? jobDuration * 60 * 1000 : 0,
        attempts: 3,
        removeOnComplete: true,
      },
    );

    await this.slaScheduledRepository.save({
      policy: details.policy,
      metricObject: metricObject,
      scheduledAt: eventTime,
      nextAttemptAt: breachTime,
      status: SLAJobStatus.SCHEDULED,
      jobId: ticketData.id,
      metric: existingJob.metric,
      scheduledJobId: bullQueueJob.id.toString(),
      organizationId: details.policy.organizationId,
      specificPolicyMetric: details.specifPolicyMetric,
      durationToBreachWorkingMinutes:
        Number(details.duration) + pausedDurationInWorkingMinutes,
      scheduledJobPayload: dataForBullQueue,
    });

    await this.logRescheduledSLA(
      bullQueueJob,
      details,
      ticketData,
      payload,
      existingJob,
    );

    await this.publishToSNS({
      entityId: existingJob.jobId,
      entityType: SlaEntityType.TICKET,
      event: SLAJobStatus.SCHEDULED,
      subject: "SLA Rescheduled",
      metric: metricObject.metric,
      organizationId: existingJob.organizationId,
      teamId: details.policy.teamId,
      user: details.policy.createdBy,
      policy: details.policy,
      duration: jobDuration > 0 ? Math.round(jobDuration) : 0,
    });
  }

  private async updateCancelledJob(existingJob: ScheduledSLA) {
    await this.slaScheduledRepository.update(
      {
        id: existingJob.id,
        jobId: existingJob.jobId,
      },
      {
        status: SLAJobStatus.CANCELLED,
        cancelledAt: new Date(),
      },
    );
  }

  private async logRescheduledSLA(
    newBullQueueJob: any,
    details: {
      duration: number;
      specifPolicyMetric: Filter;
      policy: SLAPolicy;
    },
    ticketData: ITicket,
    payload: ISlaPayload,
    existingJob: ScheduledSLA,
  ) {
    await this.slaAuditLogsService.updateOrCreate({
      scheduledJobId: newBullQueueJob.id.toString(),
      status: SLAJobStatus.SCHEDULED,
      policyId: details.policy.uid,
      entityType: SlaEntityType.TICKET,
      entityId: ticketData.id,
      organizationId: details.policy.organizationId,
      teamId: payload.teamId,
      eventType: payload.eventType,
      message: `SLA re-scheduled for ${existingJob.metric} duration ${details.duration} minutes ticket	uid ${ticketData.id}`,
      specificMetric: details.specifPolicyMetric,
      metric: existingJob.metric as SLAMetricType,
      filter: details.policy.filter,
      userId: details.policy.createdBy,
    });
  }

  private async logBreachedSLA(
    existingJob: ScheduledSLA,
    details: { policy: SLAPolicy; specifPolicyMetric: Filter },
    ticketData: ITicket,
    payload: ISlaPayload,
    metricObject: PolicyMetric,
  ) {
    await this.slaAuditLogsService.updateOrCreate({
      scheduledJobId: existingJob.scheduledJobId,
      status: SLAJobStatus.BREACHED,
      policyId: existingJob.policy.uid,
      entityType: SlaEntityType.TICKET,
      entityId: ticketData.id,
      organizationId: existingJob.organizationId,
      teamId: existingJob.policy.teamId,
      eventType: payload.eventType,
      message: `Cancelled SLA for ${existingJob.metric} duration and created new SLA for ${details.policy.name}`,
      specificMetric: details.specifPolicyMetric,
      metric: metricObject.metric as SLAMetricType,
      filter: existingJob.policy.filter,
      userId: existingJob.policy.createdBy,
    });
  }

  private async createNewJob(
    metric: SLAMetricType,
    details: {
      duration: number;
      specifPolicyMetric: Filter;
      policy: SLAPolicy;
    },
    ticketData: ITicket,
    payload: ISlaPayload,
    updateEventId: string,
  ) {
    const metricObject = details.policy.policyMetrics.find(
      (m) => m.metric === metric,
    );
    const teamConfiguration = await this.teamUtils.getTeamConfiguration(
      details.policy.teamId,
      details.policy.createdBy,
      payload.orgId,
    );

    const cancelledJobs = await this.slaScheduledRepository.findAll({
      where: {
        jobId: ticketData.id,
        metric: metric,
        status: SLAJobStatus.CANCELLED,
      },
    });

    let pausedDurationInWorkingMinutes = 0;
    for (const job of cancelledJobs) {
      pausedDurationInWorkingMinutes += job.pausedDurationInWorkingMinutes || 0;
    }

    const eventTime = await this.slaEngineUtils.getEventTime(
      payload,
      metric,
      SlaEntityType.TICKET,
      ticketData,
    );
    const workingHours = new WorkingHours(teamConfiguration);
    const breachDuration = workingHours.getJobDurationInMinutes(
      eventTime,
      Number(details.duration) + pausedDurationInWorkingMinutes,
    );
    const breachTime = new Date(eventTime.getTime() + breachDuration * 60000);

    const jobDuration = (breachTime.getTime() - Date.now()) / 60000;

    this.logger.log(`breachDuration: ${breachDuration}`);
    const dataForBullQueue = {
      entityId: ticketData.id,
      eventName: payload.eventType,
      entityType: SlaEntityType.TICKET,
      policyId: details.policy.uid,
      metricObject: metricObject,
      specifPolicyMetric: details.specifPolicyMetric,
      userId: details.policy.createdBy,
      scheduledAt: eventTime,
      organizationId: details.policy.organizationId,
      jobDuration: breachDuration,
    };
    this.logger.log(`dataForBullQueue: ${JSON.stringify(dataForBullQueue)}`);
    const bullQueueJob = await this.slaQueue.add(
      "process-sla",
      dataForBullQueue,
      {
        delay: jobDuration > 0 ? jobDuration * 60 * 1000 : 0,
        attempts: 3,
        removeOnComplete: true,
      },
    );

    try {
      const scheduledSLA = await this.slaScheduledRepository.save({
        policy: details.policy,
        metricObject: metricObject,
        scheduledAt: eventTime,
        nextAttemptAt: breachTime,
        status: SLAJobStatus.SCHEDULED,
        jobId: ticketData.id,
        metric: metric,
        scheduledJobId: bullQueueJob.id.toString(),
        organizationId: details.policy.organizationId,
        specificPolicyMetric: details.specifPolicyMetric,
        durationToBreachWorkingMinutes:
          Number(details.duration) + pausedDurationInWorkingMinutes,
        scheduledJobPayload: dataForBullQueue,
      });
      const jobAlreadyExist = await this.slaAuditLogsService.getJob(
        ticketData.id,
        SlaEntityType.TICKET,
        metric,
      );

      const updates = {
        scheduledJobId: bullQueueJob.id.toString(),
        status: SLAJobStatus.SCHEDULED,
        policyId: details.policy.uid,
        entityType: SlaEntityType.TICKET,
        entityId: ticketData.id,
        organizationId: details.policy.organizationId,
        teamId: details.policy.teamId,
        eventType: payload.eventType,
        message: `SLA scheduled for ${metric} duration ${details.duration} minutes ticket	uid ${ticketData.id}`,
        specificMetric: details.specifPolicyMetric,
        metric,
        filter: details.policy.filter,
        updateEventId,
        userId: details.policy.createdBy,
      };
      if (jobAlreadyExist) {
        updates.message = `SLA scheduled for ${metric} duration ${details.duration} minutes ticket	uid ${ticketData.id}`;
        await this.slaAuditLogsService.updateOrCreate(updates);
        await this.publishToSNS({
          entityId: scheduledSLA.jobId,
          entityType: SlaEntityType.TICKET,
          event: SLAJobStatus.SCHEDULED,
          subject: "SLA Rescheduled",
          metric: metricObject.metric,
          organizationId: details.policy.organizationId,
          teamId: details.policy.teamId,
          user: details.policy.createdBy,
          policy: details.policy,
          duration: jobDuration > 0 ? Math.round(jobDuration) : 0,
        });
      } else {
        this.logger.log(
          `SLA already exists for ${metric} duration ${details.duration} minutes ticket	uid ${ticketData.id}`,
        );
        await this.slaAuditLogsService.updateOrCreate(updates);
        await this.publishToSNS({
          entityId: scheduledSLA.jobId,
          entityType: SlaEntityType.TICKET,
          event: SLAJobStatus.SCHEDULED,
          subject: "SLA Scheduled",
          metric: metricObject.metric,
          organizationId: details.policy.organizationId,
          teamId: details.policy.teamId,
          user: details.policy.createdBy,
          policy: details.policy,
          duration: jobDuration > 0 ? Math.round(jobDuration) : 0,
        });
      }
      this.logger.log(`scheduledSLA: ${JSON.stringify(scheduledSLA)}`);
    } catch (err) {
      this.logger.error(`Error creating scheduled SLA: ${err}`);
      const updates = {
        scheduledJobId: bullQueueJob.id.toString(),
        status: SLAJobStatus.SCHEDULED,
        policyId: details.policy.uid,
        entityType: SlaEntityType.TICKET,
        entityId: ticketData.id,
        organizationId: details.policy.organizationId,
        teamId: details.policy.teamId,
        eventType: payload.eventType,
        message: `Failed to create scheduled SLA for ${metric} duration ${details.duration} minutes ticket	uid ${ticketData.id}`,
        specificMetric: details.specifPolicyMetric,
        metric,
        filter: details.policy.filter,
        updateEventId,
      };
      await this.slaAuditLogsService.updateOrCreate(updates);
      await this.publishToSNS({
        entityId: ticketData.id || "unknown",
        entityType: SlaEntityType.TICKET,
        event: SLAJobStatus.NOT_SCHEDULED,
        subject: "SLA Failed to Create",
        metric: metricObject.metric,
        organizationId: details.policy.organizationId,
        teamId: details.policy.teamId,
        user: details.policy.createdBy,
        policy: details.policy,
      });
      throw err;
    }
  }

  private async publishToSNS(params: {
    entityId: string;
    entityType: SlaEntityType;
    event: SLAJobStatus;
    subject?: string;
    metric?: SLAMetricType;
    user: string;
    organizationId: string;
    teamId: string;
    policy?: SLAPolicy;
    duration?: number;
  }) {
    try {
      const userId: string = params.user;

      if (params.event === SLAJobStatus.BREACHED) {
        const slaPayload: SLABreachPayload = {
          sla: {
            policyId: params.policy?.uid || "unknown",
            policyName: params.policy?.name || "unknown",
            metric: params.metric || "unknown",
            duration: params.duration,
          },
          ticket: {
            id: params.entityId,
            teamId: params.teamId,
          },
        };

        const snsEvent: TicketSlaSNSEvent<TicketEvents> = {
          eventId: uuidv4(),
          eventType: TicketEvents.SLA_BREACHED,
          timestamp: new Date().toISOString(),
          orgId: params.organizationId,
          teamId: params.teamId,
          payload: slaPayload,
        };

        await this.slaSnsPublisher.publishSNSMessage({
          subject: params.subject ?? "SLA Event",
          message: JSON.stringify(snsEvent),
          topicArn: this.configService.get(
            ConfigKeys.AWS_SNS_TICKETS_TOPIC_ARN,
          ),
          messageGroupId: params.entityId,
          messageAttributes: {
            event_name: TicketEvents.SLA_BREACHED,
            event_id: snsEvent.eventId,
            event_timestamp: Math.floor(Date.now() / 1000).toString(),
            context_user_id: userId,
            context_user_type: ContextUserType.USER,
            context_organization_id: params.organizationId,
          },
        });

        this.logger.log(`SNS message published: ${JSON.stringify(snsEvent)}`);
      }

      // Record audit log
      const auditMessage = this.getAuditMessage(
        params.event,
        params.policy?.name,
        params.metric,
        params.duration,
      );
      await this.activitiesGrpcClient.recordAuditLog(
        params.organizationId,
        params.teamId,
        userId,
        AuditLogEntityType.TICKET,
        params.entityId,
        AuditLogOp.UPDATED,
        AuditLogVisibility.ORGANIZATION,
        auditMessage,
        auditMessage,
      );

      this.logger.log(`Audit log recorded: ${JSON.stringify(auditMessage)}`);
    } catch (error) {
      this.logger.error(
        `Error encountered while publishing SLA event ${params.event} to SNS: ${error}`,
      );
    }
  }

  private getAuditMessage(
    status: SLAJobStatus,
    policyName?: string,
    metric?: string,
    duration?: number,
  ): string {
    const metricName = (metric || "")
      .replace(/_/g, " ")
      .replace(/^\w/, (c) => c.toUpperCase());

    const baseMsg = `SLA ${status.toLowerCase()} for policy ${
      policyName || "unknown"
    } with metric ${metricName || "unknown"}`;
    switch (status) {
      case SLAJobStatus.SCHEDULED:
        return `${baseMsg} for ${duration || 0} minutes`;
      case SLAJobStatus.BREACHED:
        return `${baseMsg}`;
      case SLAJobStatus.CANCELLED:
        return `${baseMsg}`;
      case SLAJobStatus.ACHIEVED:
        return `${baseMsg}`;
      case SLAJobStatus.PAUSED:
        return `${baseMsg}`;
      default:
        return baseMsg;
    }
  }
}
