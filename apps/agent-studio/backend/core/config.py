import os
from functools import lru_cache
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application settings
    APP_NAME: str = "ThenaAgentStudio"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    API_PREFIX: str = "/api/v1"
    USE_HTTP2: bool = False
    PORT: int = 8008
    INSTANCE_ID: str = "default"  # Default instance ID for websocket manager

    # JWT settings
    JWT_SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Auth service settings
    AUTH_SERVICE_URL: str

    # Supabase settings
    SUPABASE_URL: str
    SUPABASE_SERVICE_KEY: str
    SUPABASE_DB_HOST: str
    SUPABASE_DB_USER: str
    SUPABASE_DB_PASSWORD: str
    DATABASE_URL: str = ""

    # Platform Supabase settings
    # These settings must be provided (non-empty) in production environments
    PLATFORM_SUPABASE_URL: str = os.getenv("PLATFORM_SUPABASE_URL", "")
    PLATFORM_SUPABASE_KEY: str = os.getenv("PLATFORM_SUPABASE_KEY", "")

    # Test Supabase settings
    TEST_SUPABASE_URL: str
    TEST_SUPABASE_KEY: str

    # OpenAI settings
    OPENAI_API_KEY: str
    OPENAI_MODEL: str = "gpt-4.1-mini"

    # PORTKEY settings
    PORTKEY_API_KEY: str
    PORTKEY_VIRTUAL_KEY: str
    PORTKEY_CONFIG: str
    PORTKEY_PROMPT_TEMPLATE_ID: str
    PORTKEY_TRACE_ID: str
    PORTKEY_VIRTUAL_KEY: str

    # GROQ settings
    GROQ_API_KEY: str

    # Firecrawl settings
    FIRECRAWL_API_KEY: str

    # Workflows settings
    WORKFLOWS_URL: str = os.getenv("WORKFLOWS_URL")
    PLATFORM_APP_URL: str = os.getenv("PLATFORM_APP_URL")

    # Thena Platform settings
    THENA_PLATFORM_URL: str = os.getenv(
        "THENA_PLATFORM_URL", "https://platform.thena.tools"
    )
    THENA_EMAIL_URL: str = os.getenv("THENA_EMAIL_URL", "https://email.thena.ai")

    # EXA settings
    EXA_API_KEY: str = os.getenv("EXA_API_KEY")
    EXA_API_URL: str = "https://api.exa.ai"

    # BRAINTRUST settings
    BRAINTRUST_API_KEY: str
    BRAINTRUST_ORG: str

    # # TipTap settings
    # TIPTAP_AI_API_SECRET: str
    # TIPTAP_PRO_TOKEN: str

    # Twilio settings
    TWILIO_AUTH_TOKEN: str

    # Redis settings
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "password")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: str = os.getenv("REDIS_DB", "default")
    REDIS_USE_SSL: bool = os.getenv("REDIS_USE_SSL", "false").lower() == "true"
    REDIS_CONNECTION_POOL_SIZE: int = int(os.getenv("REDIS_CONNECTION_POOL_SIZE", "50"))
    REDIS_PRE_INITIALIZED_CONNECTIONS: int = int(
        os.getenv("REDIS_PRE_INITIALIZED_CONNECTIONS", "10")
    )
    REDIS_MESSAGE_TTL_SECONDS: int = int(
        os.getenv("REDIS_MESSAGE_TTL_SECONDS", "86400")
    )

    # Cookie settings (defaults are production-ready)
    COOKIE_SAMESITE: str = os.getenv("COOKIE_SAMESITE", "none")
    COOKIE_SECURE: bool = os.getenv("COOKIE_SECURE", "true").lower() == "true"

    # LangGraph settings
    LANGGRAPH_MAX_ITERATIONS: int = int(
        os.getenv("LANGGRAPH_MAX_ITERATIONS", "20")
    )  # Default: 20 iterations

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")

    # Sentry settings
    SENTRY_DSN: Optional[str] = os.getenv("SENTRY_DSN")
    SENTRY_TRACES_SAMPLE_RATE: float = float(
        os.getenv("SENTRY_TRACES_SAMPLE_RATE", "1.0")
    )
    SENTRY_PROFILES_SAMPLE_RATE: float = float(
        os.getenv("SENTRY_PROFILES_SAMPLE_RATE", "1.0")
    )
    URL_FETCH_TIMEOUT: float = float(os.getenv("URL_FETCH_TIMEOUT", "300.0"))

    # Test constants
    TEST_AGENT_ID: str = os.getenv("TEST_AGENT_ID", "test-agent-id")
    TEST_USER_ID: str = os.getenv("TEST_USER_ID", "test-user-id")
    TEST_THREAD_ID: str = os.getenv("TEST_THREAD_ID", "test-thread-id")
    TEST_USER: str = os.getenv("TEST_USER", "<EMAIL>")
    TEST_PASSWORD: str = os.getenv("TEST_PASSWORD", "test-password")
    TEST_X_ORG_ID: str = os.getenv("TEST_X_ORG_ID", "test-org-id")
    TEST_WEBSOCKET_TICKET_ID: str = os.getenv(
        "TEST_WEBSOCKET_TICKET_ID", "test-websocket-ticket-id"
    )
    TEST_WEBSOCKET_AGENT_ID: str = os.getenv(
        "TEST_WEBSOCKET_AGENT_ID", "test-websocket-agent-id"
    )
    TEST_WEBSOCKET_THREAD_ID: str = os.getenv(
        "TEST_WEBSOCKET_THREAD_ID", "test-websocket-thread-id"
    )
    TEST_WEBSOCKET_TEAM_ID: str = os.getenv(
        "TEST_WEBSOCKET_TEAM_ID", "test-websocket-team-id"
    )

    # Additional test constants for webhook tests
    TEST_VALID_TEMPLATE_ID: str = os.getenv(
        "TEST_VALID_TEMPLATE_ID", "test-template-id"
    )
    TEST_EXISTING_APP_ID: str = os.getenv("TEST_EXISTING_APP_ID", "test-app-id")
    TEST_NEW_TEMPLATE_ID: str = os.getenv(
        "TEST_NEW_TEMPLATE_ID", "test-new-template-id"
    )

    # Test constants for flow API tests
    TEST_FLOW_AGENT_ID: str = os.getenv("TEST_FLOW_AGENT_ID", "test-flow-agent-id")

    # Test constants for flow API tests
    TEST_APP_ID: str = os.getenv("TEST_APP_ID", "test-app-id")
    TEST_BOT_ID: str = os.getenv("TEST_BOT_ID", "test-bot-id")
    TEST_BOT_TOKEN: str = os.getenv("TEST_BOT_TOKEN", "test-bot-token")
    TEST_CREATED_BY: str = os.getenv("TEST_CREATED_BY", "test-created-by")
    TEST_TEAM_ID: str = os.getenv("TEST_TEAM_ID", "test-team-id")
    TEST_TEMPLATE_ID: str = os.getenv("TEST_TEMPLATE_ID", "test-template-id")
    TEST_WEBHOOK_ORG_ID: str = os.getenv("TEST_WEBHOOK_ORG_ID", "test-webhook-org-id")

    @property
    def REDIS_URL(self) -> str:
        """Build Redis URL from components with proper handling of password and SSL."""
        protocol = "rediss" if self.REDIS_USE_SSL else "redis"
        auth_part = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"{protocol}://{auth_part}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"

    # CORS settings
    CORS_ORIGINS: list[str] = [
        "http://localhost",
        "http://localhost:3000",
        "http://localhost:8000",
        "https://app.thenacrew.com",
    ]

    # API Documentation
    API_TITLE: str = "ThenaAgentStudio API"
    API_DESCRIPTION: str = """
    ThenaAgentStudio - AI Agent Management System
    """


@lru_cache
def get_config() -> Settings:
    """Get the configuration instance with environment-specific settings.
    Automatically selects the appropriate .env file based on TESTING environment variable.

    Returns:
        Settings: Configuration instance with all settings
    """
    current_dir = Path(__file__).resolve().parent

    # Look for .env file in the backend directory
    if os.getenv("TESTING") == "1":
        env_path = current_dir.parent.parent / "tests" / ".env.test"
    else:
        env_path = current_dir.parent / ".env"

    # Load the appropriate .env file
    load_dotenv(env_path)

    return Settings()
