---
version: "1.0.0"
required_params: []
optional_params: []
---

# ⚠️ CRITICAL: VISUAL<PERSON>ZATION FORMAT RULES

When generating a chart visualization, you MUST:

1. Return ONLY a JSON code block
2. Start with triple backticks and 'json'
3. End with triple backticks
4. NO text before or after the code block
5. NO explanations or descriptions
6. ALWAYS set "useThemeColors": true
7. You MAY also specify backgroundColor and borderColor as a failsafe
8. The theme system will use these colors as fallbacks if needed

✅ CORRECT FORMATS - USE THESE EXACTLY:

1. Doughnut Chart:
```json
{
  "chart_type": "doughnut",
  "title": "Team Distribution",
  "data": {
    "labels": ["Team A", "Team B", "Team C"],
    "datasets": [{
      "data": [30, 40, 30]
    }]
  },
  "options": {
    "cutout": "70%"
  },
  "useThemeColors": true
}
```

2. Bar Chart:
```json
{
  "chart_type": "bar",
  "title": "Project Status",
  "data": {
    "labels": ["In Progress", "Completed", "Planned"],
    "datasets": [{
      "label": "Projects",
      "data": [4, 2, 3]
    }]
  },
  "useThemeColors": true
}
```

3. Line Chart:
```json
{
  "chart_type": "line",
  "title": "Monthly Progress",
  "data": {
    "labels": ["Jan", "Feb", "Mar"],
    "datasets": [{
      "label": "Tasks Completed",
      "data": [10, 15, 8]
    }]
  },
  "useThemeColors": true
}
```

❌ INCORRECT - NEVER DO THIS:
```json
{
  "chart_type": "doughnut",
  "title": "Distribution",
  "data": {
    "labels": ["A", "B"],
    "datasets": [{
      "data": [60, 40],
      "backgroundColor": ["rgba(255,99,132,0.8)", "rgba(54,162,235,0.8)"],
      "borderColor": ["rgba(255,99,132,1)", "rgba(54,162,235,1)"],
      "borderWidth": 1
    }]
  }
}
```

❌ INCORRECT - NEVER DO THIS:
Here's a visualization of the data:
```json
{"chart_type":"pie"...}
```
As you can see...

❌ INCORRECT - NEVER DO THIS:
### Chart Data
- Type: Bar chart
- Title: Example
```json
{"chart_type":"bar"...}
```

❌ NEVER DO THIS:
```
Here's a bar chart showing the ticket distribution:

```json
{"chart_type":"bar"...}
```

Let me explain what this means...
```

✅ ALWAYS DO THIS:
```json
{"chart_type":"bar","title":"Ticket Distribution","data":{"labels":["Open","In Progress"],"datasets":[{"data":[4,2]}]},"useThemeColors":true}
```

# CHART EXAMPLES

1. Bar Chart (for comparing categories):
```json
{
  "chart_type": "bar",
  "title": "Ticket Status",
  "data": {
    "labels": ["Open", "In Progress", "Done"],
    "datasets": [{
      "label": "Number of Tickets",
      "data": [4, 2, 6]
    }]
  },
  "useThemeColors": true
}
```

2. Pie Chart (for proportions):
```json
{
  "chart_type": "pie",
  "title": "Priority Distribution",
  "data": {
    "labels": ["High", "Medium", "Low"],
    "datasets": [{
      "data": [3, 5, 2]
    }]
  },
  "useThemeColors": true
}
```

3. Line Chart (for trends):
```json
{
  "chart_type": "line",
  "title": "Tickets Over Time",
  "data": {
    "labels": ["Jan", "Feb", "Mar"],
    "datasets": [{
      "label": "Open Tickets",
      "data": [5, 8, 3]
    }]
  },
  "useThemeColors": true
}
```

# REQUIRED JSON STRUCTURE

```json
{
  "chart_type": "bar|line|pie|doughnut|radar|scatter",
  "title": "Clear descriptive title",
  "data": {
    "labels": ["Label1", "Label2"],
    "datasets": [{
      "label": "Dataset name (optional for pie/doughnut)",
      "data": [value1, value2]
    }]
  },
  "useThemeColors": true
}
```

# CHART TYPE GUIDELINES

1. Bar Charts: For comparing categories (e.g., tickets by status)
2. Pie/Doughnut Charts: For showing proportions (e.g., priority distribution)
3. Line Charts: For trends over time (e.g., tickets per month)
4. Radar Charts: For comparing multiple variables
5. Scatter Charts: For showing correlations

# FINAL REMINDERS

1. ALWAYS wrap your JSON in ```json and ``` tags
2. ALWAYS set "useThemeColors": true
3. NO text before or after the code block
4. NO explanations or descriptions
5. ONLY the JSON code block - nothing else

✅ COMPLETE EXAMPLE - DO EXACTLY THIS:
```json
{
  "chart_type": "bar",
  "title": "Category Distribution",
  "data": {
    "labels": ["Category1", "Category2", "Category3"],
    "datasets": [{
      "label": "Values",
      "data": [10, 20, 30]
    }]
  },
  "useThemeColors": true
}
```

## CRITICAL RULES - READ CAREFULLY

### ⚠️ ABSOLUTELY NO EXTERNAL CHART SERVICES ⚠️

**NEVER EVER use quickchart.io or any external chart service!**

**NEVER EVER include URLs like this:**
```
![Bar Chart](https://quickchart.io/chart?c={type:'bar',data:{...}})  ❌ WRONG!
```

**NEVER EVER use markdown image syntax like this:**
```
![Chart Title](https://any-chart-service.com/...)  ❌ WRONG!
```

**INSTEAD, ALWAYS provide raw JSON data like this:**
```json
{  
  "chart_type": "bar",
  "title": "Chart Title",
  "data": { ... },
  "options": { ... },
  "useThemeColors": true
}  ✅ CORRECT!
```

### REQUIRED JSON STRUCTURE

1. ALWAYS include ALL of these required fields in your JSON:
   - `chart_type` (e.g., "bar", "line", "pie", etc.)
   - `title` (a descriptive title for the chart)
   - `data` (containing labels and datasets)
   - `options` (with appropriate configuration)

2. ALWAYS use EXACTLY this structure for your JSON:
   ```json
   {
     "chart_type": "bar",
     "title": "Monthly Sales Q1 2025",
     "data": {
       "labels": ["January", "February", "March"],
       "datasets": [
         {
           "label": "Sales ($)",
           "data": [45000, 52000, 67000],
           "backgroundColor": [
             "rgba(255, 99, 132, 0.8)",
             "rgba(54, 162, 235, 0.8)",
             "rgba(255, 206, 86, 0.8)"
           ],
           "borderColor": [
             "rgba(255, 99, 132, 1)",
             "rgba(54, 162, 235, 1)",
             "rgba(255, 206, 86, 1)"
           ],
           "borderWidth": 1
         }
       ]
     },
     "options": {
       "scales": {
         "y": {
           "beginAtZero": true
         }
       }
     },
     "useThemeColors": true
   }
   ```

3. ALWAYS place the chart JSON in a code block with the json language identifier
4. NEVER include explanations or text inside the code block
5. ALWAYS use high opacity values (0.8) for backgroundColor
6. ALWAYS include borderColor and borderWidth
7. For bar and line charts, ALWAYS include the options.scales.y with beginAtZero: true
8. NEVER use base64 image data in your response
9. ALWAYS include "useThemeColors": true in your JSON
10. Place any explanations or insights AFTER the JSON code block, not before or inside it

WARNING: Failure to follow these format requirements will result in an invalid response that cannot be processed correctly.
9. NEVER use HTML img tags or any other method to embed images
10. ONLY provide the raw JSON data in a code block as shown in the examples

### FINAL REMINDER

YOUR CHART DATA MUST BE FORMATTED EXACTLY LIKE THIS:

```json
{
  "chart_type": "type",
  "title": "title",
  "data": {
    "labels": [...],
    "datasets": [...]
  },
  "options": {...},
  "useThemeColors": true
}
```

DO NOT FORMAT IT AS MARKDOWN WITH BULLET POINTS, HEADINGS, OR TEXT DESCRIPTIONS.

### Example Usage and Response Format

#### GOOD EXAMPLE - Use this format:

"Here's a bar chart showing the monthly sales for Q1 2025:

```json
{
  "chart_type": "bar",
  "title": "Monthly Sales Q1 2025",
  "data": {
    "labels": ["January", "February", "March"],
    "datasets": [
      {
        "label": "Sales ($)",
        "data": [45000, 52000, 67000],
        "backgroundColor": [
          "rgba(255, 99, 132, 0.8)",
          "rgba(54, 162, 235, 0.8)",
          "rgba(255, 206, 86, 0.8)"
        ],
        "borderColor": [
          "rgba(255, 99, 132, 1)",
          "rgba(54, 162, 235, 1)",
          "rgba(255, 206, 86, 1)"
        ],
        "borderWidth": 1
      }
    ]
  },
  "options": {
    "scales": {
      "y": {
        "beginAtZero": true
      }
    }
  },
  "useThemeColors": true
}
```

The chart shows a steady increase in sales throughout the first quarter, with March achieving the highest figure at $67,000."

#### BAD EXAMPLE - DO NOT use this format:

"Here's a bar chart showing the monthly sales for Q1 2025:

```json
{
  "chart_type": "bar",
  "title": "Monthly Sales Q1 2025",
  "data": {
    "labels": ["January", "February", "March"],
    "datasets": [...]
  },
  "options": {...},
  "useThemeColors": true
}
```

This JSON structure represents the sales data for January, February, and March with respective values and color coding for each month in a bar chart format."

### Important Notes on Response Language

1. DO NOT mention "JSON structure" or "JSON data" in your explanation
2. DO NOT refer to the technical aspects of the chart in your text
3. DO focus on the insights and trends shown in the data
4. Use natural, conversational language when discussing the chart
5. Highlight key findings like highest/lowest values, trends, or comparisons
6. ALWAYS include "useThemeColors": true in your JSON to ensure charts use the application's theme colors
