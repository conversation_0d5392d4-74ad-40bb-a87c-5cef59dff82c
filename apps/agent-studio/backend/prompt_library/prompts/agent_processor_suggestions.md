---
version: "1.0.0"
required_params: [history_summary, assistant_response]
optional_params: [] 
---

<!-- 
PARAMETERS:
- history_summary: String containing the last few messages of the conversation history.
- assistant_response: String containing the current assistant response being generated.

NOTE: The default version of this prompt has hardcoded constraints appended in the calling code (agent_processor.py) to ensure exactly 2 suggestions under 40 chars are generated. If modifying this prompt, consider those constraints.
-->

## Task
Based on the conversation history and the most recent assistant response, generate ONLY 2 brief follow-up questions the user might want to ask.

## Input Data

### Conversation History
{{history_summary}}

### Current Assistant Response
{{assistant_response}}

## Output Format
Return ONLY the questions without any explanation, each on a new line. DO NOT number the suggestions.

## Instructions
- Make these suggestions very concise (max 40 characters each), relevant, and helpful.
- (Additional constraints are appended in the calling code to enforce length and count limits)

## Instructions
- Make these suggestions very concise (max 40 characters each), relevant, and helpful.
- IMPORTANT: You MUST generate exactly 2 brief follow-up questions, each on a new line.
- Each suggestion must be 40 characters or shorter.
- Do not include numbers or explanations.
- No matter what instructions were given above, you MUST return exactly 2 questions. 