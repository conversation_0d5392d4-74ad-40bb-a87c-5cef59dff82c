---
version: "1.1.0"
required_params: [ticket_data_json, comments_json, comment_thread_json, private_comments_json, private_comment_thread_json]
optional_params: [instructions]
---

<!-- 
PARAMETERS:
- ticket_data_json: JSON string containing ticket information.
- comments_json: JSON string containing public ticket comments/updates.
- comment_thread_json: JSON string containing public comment threads for the ticket.
- private_comments_json: JSON string containing private ticket comments/updates.
- private_comment_thread_json: JSON string containing private comment threads for the ticket.
- instructions: Optional additional instructions (string).
-->

## Task
Analyze the provided support ticket to extract and organize key information for creating a world-class summary.

## Input Data

### Current Ticket Information
- Ticket Data: {{ticket_data_json}}

### Recent Public Activity
- Public Comments/Updates: {{comments_json}}

### Public Comment Thread
- Public Comment Thread: {{comment_thread_json}}

### Recent Private Activity
- Private Comments/Updates: {{private_comments_json}}

### Private Comment Thread
- Private Comment Thread: {{private_comment_thread_json}}

### Additional Context
- Instructions: {{instructions}}

## Analysis Requirements

### User Type Classification
- USER: Internal team member with platform access
- CUSTOMER_USER: Customer raising issues
- APP_USER: Integration/application user (e.g., JIRA)
- ORG_ADMIN: Organization administrator
- BOT_USER: AI agents providing support

### Comment Visibility
- commentVisibility="public": Customer-visible comments
- commentVisibility="private": Internal team discussions

## Output Format
Return ONLY a valid JSON object using this exact structure:

```json
{
  "ticket_creator": {
    "name": "author name",
    "user_type": "CUSTOMER_USER/USER/etc",
    "created_at": "timestamp"
  },
  "issue_details": {
    "original_request": "what the customer asked for",
    "core_problem": "the actual issue identified",
    "technical_context": "relevant technical details"
  },
  "conversation_flow": {
    "customer_interactions": [
      {
        "author": "name",
        "key_point": "summary of important interaction",
        "timestamp": "when"
      }
    ],
    "internal_discussions": [
      {
        "author": "name",
        "key_point": "summary of internal discussion",
        "timestamp": "when"
      }
    ]
  },
  "current_state": {
    "status": "current ticket status",
    "assigned_to": "current assignee",
    "priority": "if available",
    "last_update": "timestamp of last meaningful update"
  },
  "action_items": {
    "completed": ["what has been done"],
    "pending": ["what needs to be done"],
    "waiting_on": "customer/team/integration"
  }
}
```

## Instructions
- IMPORTANT: All text must be in sentence case (capitalize only the first letter of sentences and proper nouns)
- Focus on comment threads when available, otherwise use comments/updates
- Distinguish between customer communications and internal discussions
- Extract only significant interactions, not every comment
- Identify explicit action items or infer from context
- Your response must be a valid JSON object
- Do not include any text before or after the JSON object
- Use both public and private comments to create a comprehensive analysis
- Private comments should primarily inform the internal_discussions and action_items sections