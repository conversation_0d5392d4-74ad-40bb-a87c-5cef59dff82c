---
version: 1.0
required_parameters:
  - content
optional_parameters:
  - file_type_context
---

## Main

<!--
Parameters:
  content: The text content to extract topics from (truncated if necessary).
  file_type_context: Context based on file type (e.g., 'website' or 'document').
-->

Extract the main topics from the following {{file_type_context}} content.
Return ONLY a JSON array of topic strings. Each topic should be 1-3 words.
Extract 3-7 topics that best represent the content.

For example: ["Machine Learning", "Neural Networks", "Data Science"]

Content:
{{content}}

Topics (JSON array only): 