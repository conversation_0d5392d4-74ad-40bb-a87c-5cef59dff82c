---
version: "1.1.0"
required_params: [analysis_json, ticket_data_json, comments_json, comment_thread_json, private_comments_json, private_comment_thread_json]
optional_params: [instructions]
---

<!-- 
PARAMETERS:
- analysis_json: JSON string containing the analysis from the previous step.
- ticket_data_json: JSON string of the original ticket data.
- comments_json: JSON string of public comments/updates for the ticket.
- comment_thread_json: JSON string of public comment threads for the ticket.
- private_comments_json: JSON string of private comments/updates for the ticket.
- private_comment_thread_json: JSON string of private comment threads for the ticket.
- instructions: Optional additional user instructions (string).
-->

## Task
Generate a world-class support ticket summary with a concise TL;DR and comprehensive detailed summary.

## Input Data

### Analysis

{{analysis_json}}

### Ticket Data

{{ticket_data_json}}

### Public Comments/Updates

{{comments_json}}

### Public Comment Thread

{{comment_thread_json}}

### Private Comments/Updates

{{private_comments_json}}

### Private Comment Thread

{{private_comment_thread_json}}

### Additional Instructions (Optional)

{{instructions}}

## Output Format
Return ONLY a valid JSON object using this structure like this:

```json
{
  "tldr": "<h3>TLDR</h3><p>[Customer name] from [Company] reported [core issue]. [Team member] investigated and [key action taken]. Currently [status] with [next action] pending on [who]. [Any critical timeline or impact info].</p>",
  
  "detailed_summary": "<h3>Ticket summary</h3>\n\n<h4>What happened</h4>\n<p>On [date], [Customer name] from [Company] reported [brief description of the issue or request].</p>\n\n<h4>Customer impact</h4>\n<p>[How this affected the customer - omit if minimal or no impact]</p>\n\n<h4>Team actions</h4>\n<ul>\n  <li>[Key action 1 by team member X]</li>\n  <li>[Key action 2 by team member Y]</li>\n</ul>\n\n<h4>Resolution progress</h4>\n<p>[Current status - omit if just opened with no progress]</p>\n\n<h4>Next steps</h4>\n<div>\n  <input type=\"checkbox\"> [Next action needed] - <em>Waiting on: [responsible party]</em><br>\n  <input type=\"checkbox\" checked> [Completed action] - <em>Completed by: [team member]</em>\n</div>\n\n<h4>Internal notes</h4>\n<p>[Only include if there are relevant internal discussions]</p>",
  
  "key_metadata": {
    "ticket_age": "time since creation",
    "last_customer_response": "timestamp",
    "last_team_response": "timestamp",
    "response_needed_from": "customer/team/none"
  }
}
```

## Formatting Guidelines
- **CRITICAL: Use sentence case throughout (only capitalize first letter of sentences and proper nouns)**
- Use HTML tags for proper formatting: `<h3>`, `<h4>`, `<p>`, `<ul>`, `<li>`, `<em>`, `<strong>`
- Use `<strong>` tags for emphasis on key information
- Use `<ul>` and `<li>` tags for lists of actions and findings
- Use `<input type='checkbox'>` for pending items, `<input type='checkbox' checked>` for completed items (without list tags)
- Keep TL;DR under 60 words, focusing on who reported the issue, what the issue is, what has been done, and what needs to happen next
- In detailed summary, maintain clear sections with header tags in sentence case
- Only include significant updates, not routine acknowledgments
- Highlight any SLA concerns or priority issues naturally within the narrative
- Separate customer-visible information from internal notes clearly
- Use private comments to inform internal notes and technical details

## Instructions
- Generate human-readable, well-structured summaries in sentence case
- IMPORTANT: Format your output using HTML tags, not markdown
- Completely omit sections in the detailed summary that aren't relevant to the ticket
- DO NOT include all sections with placeholder text - only include sections that have meaningful content
- For simple tickets (like requests for information), use a simplified structure with only 2-3 sections
- Example: A basic information request might only need "What happened" and "Next steps" sections
- Focus on clarity and actionability - quality over quantity
- Avoid repeating information across sections
- Ensure the TL;DR captures the essence without fluff
- Make the detailed summary scannable with clear HTML heading tags
- Your response must be a valid JSON object with HTML-formatted content
- Do not include any text before or after the JSON object
- Date should be in DD, MMM format (example: 1st June, 3rd May)
- Ensure all HTML tags are properly closed and nested correctly
- When referring to customers with company email domains (e.g., <EMAIL>), use the format "First name from Company" (e.g., "John from Apple") the first time they are mentioned in both TLDR and summary