---
version: "1.0.0"
required_params: [entity_text, content]
optional_params: []
---

<!-- 
PARAMETERS:
- entity_text: String listing the entities (name and type) to focus on, each on a new line.
- content: The input text from which to extract facts.
-->

## Task
Extract factual information about the mentioned entities from the provided text.
Even if the text is informal, identify clear factual statements about the entities.

## Input Data

### Entities
{{entity_text}}

### Text
{{content}}

## Output Format
For each fact you find, return it in this exact JSON format within a JSON array:

```json
[
  {
    "subject": "entity_name",
    "predicate": "relationship_or_attribute",
    "object": "value",
    "confidence": 0.9
  }
]
```

## Instructions
- Only return facts that are explicitly stated or can be directly inferred from the text.
- Return an empty array `[]` if no clear facts are found.
- Return ONLY the JSON array, no other text. 