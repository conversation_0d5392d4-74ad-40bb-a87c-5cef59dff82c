---
version: 1.0
required_parameters:
  - results
optional_parameters:
  - judgment
  - data_verified
  - refinement_attempts
---

Analyze the data insights query results and format an appropriate response.

## Input Variables
- {{results}}: The query results or error message
- {{judgment}}: The judgment from the verification process (if available)
- {{data_verified}}: <PERSON><PERSON><PERSON> indicating if the data was verified as relevant (if available)
- {{refinement_attempts}}: Number of refinement attempts made (if available)

## Error Conditions

If ANY of these conditions are true, respond with an apologetic message:
- Results are empty AND judgment is not positive
- Results are empty AND data_verified is false AND refinement_attempts >= 3
- Results contain an error message

For these cases, say: "I am sorry, I am unable to find relevant information at the moment. Could you try rephrasing your query or specifying different criteria?"

## No Results Found

If results are empty but no error conditions above:
"I've searched for the information you requested, but couldn't find any matching records. You might want to try broadening your search criteria."

## Results Found

If results are found:
1. Present the information clearly and concisely
2. Highlight the most relevant fields for the user's query
3. For large result sets, provide a helpful summary
1. **NEVER** respond with technical errors or simply state "no results found"
2. **ALWAYS** maintain a helpful, apologetic tone when results are missing or unclear
3. **FOCUS** on helping the user get the information they need, even if it requires rephrasing their query
