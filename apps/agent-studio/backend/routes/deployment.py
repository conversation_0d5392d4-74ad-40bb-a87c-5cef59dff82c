import traceback
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, HTTPException, Path, Query

from backend.core.auth import get_current_organization, security
from backend.core.logging_config import get_logger
from backend.models.deployment import CreateDeploymentKeyPayload
from backend.models.non_plat import Deployment, DeploymentCreateResponse
from backend.services.agent_service import AgentService, get_agent_service
from backend.services.deployment_service import (
    DeploymentService,
    get_deployment_service,
)

logger = get_logger("deployment_route")

router = APIRouter(
    prefix="/deployment",
    tags=["Deployment"],
    responses={
        (401): {"description": "Unauthorized - Invalid or missing JWT token"},
        (403): {"description": "Forbidden - Insufficient permissions"},
        (404): {"description": "Not found"},
        (500): {"description": "Internal server error"},
    },
    dependencies=[Depends(security)],
)


# Define local dependency function for DeploymentService
async def get_deployment_service_dependency(
    organization_id: UUID = Depends(get_current_organization),
) -> DeploymentService:
    """Initialize and return the deployment service for the current organization."""
    try:
        return await get_deployment_service(organization_id)
    except Exception as e:
        logger.error(f"Failed to initialize deployment service: {e}", props={})
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e))


# Define AgentService dependency function
async def get_agent_service_dependency(
    organization_id: UUID = Depends(get_current_organization),
) -> AgentService:
    """Initialize and return the agent service for the current organization."""
    try:
        return await get_agent_service(organization_id)
    except Exception as e:
        logger.error(f"Failed to initialize agent service: {e}", props={})
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agents/{agent_id}/keys", response_model=DeploymentCreateResponse)
async def create_deployment_key(
    payload: CreateDeploymentKeyPayload = Body(...),
    agent_id: UUID = Path(..., description="Agent ID"),
    organization_id: UUID = Depends(get_current_organization),
    deployment_service: DeploymentService = Depends(get_deployment_service_dependency),
    agent_service: AgentService = Depends(get_agent_service_dependency),
):
    """Create a new deployment API key for an agent, returning the HMAC secret key only on creation."""
    try:
        agent = await agent_service.get_agent(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")

        # Check if agent belongs to the correct organization
        agent_org_id = agent["organization_id"]
        if isinstance(agent_org_id, str):
            try:
                agent_org_id = UUID(agent_org_id)
            except ValueError:
                raise HTTPException(
                    status_code=400, detail="Invalid agent organization ID format"
                )
        if agent_org_id != organization_id:
            raise HTTPException(
                status_code=403, detail="Agent not found in this organization"
            )

        # Call the updated service method which returns a tuple
        deployment_model, hmac_secret_key = await deployment_service.create_deployment(
            agent_id=agent_id,
            team_id=payload.team_id,
            allowed_origins=payload.allowed_origins,
            deployment_type=payload.deployment_type,
        )

        # Construct the response using the specific response model
        response_data = DeploymentCreateResponse(
            **deployment_model.model_dump(),  # Use model_dump() for Pydantic v2
            hmac_secret_key=hmac_secret_key,
        )

        return response_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating deployment key: {str(e)}", props={})
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/keys", response_model=List[Deployment])
async def list_deployment_keys(
    agent_id: Optional[UUID] = Query(None, description="Filter by agent ID"),
    deployment_service: DeploymentService = Depends(get_deployment_service_dependency),
):
    """List all deployment keys for an organization."""
    try:
        # Use the injected service
        return await deployment_service.list_deployments(agent_id)
    except Exception as e:
        logger.error(f"Error listing deployment keys: {str(e)}", props={})
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/keys/{deployment_id}", status_code=204)
async def delete_deployment_key(
    deployment_id: UUID = Path(..., description="Deployment ID"),
    organization_id: UUID = Depends(get_current_organization),
    deployment_service: DeploymentService = Depends(get_deployment_service_dependency),
):
    """Delete a deployment key."""
    try:
        # Verify the deployment belongs to this organization
        deployment = await deployment_service.get_deployment(deployment_id)
        if not deployment:
            raise HTTPException(status_code=404, detail="Deployment key not found")

        # Use the correct attribute name 'org_id' from the model
        if deployment.org_id != organization_id:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this deployment key"
            )

        # Use the injected service
        await deployment_service.delete_deployment(deployment_id)
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting deployment key: {str(e)}", props={})
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/keys/{deployment_id}", response_model=Deployment)
async def get_deployment_key(
    deployment_id: UUID = Path(..., description="Deployment ID"),
    organization_id: UUID = Depends(get_current_organization),
    deployment_service: DeploymentService = Depends(get_deployment_service_dependency),
):
    """Get a specific deployment key."""
    try:
        # Use the injected service
        deployment = await deployment_service.get_deployment(deployment_id)
        # Ensure the deployment belongs to the correct organization before returning
        if not deployment:
            raise HTTPException(status_code=404, detail="Deployment key not found")

        # Use the correct attribute name 'org_id' from the model
        if deployment.org_id != organization_id:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this deployment key"
            )
        return deployment
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deployment key: {str(e)}", props={})
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e))
