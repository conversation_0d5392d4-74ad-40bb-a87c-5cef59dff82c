import json
import traceback
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, WebSocket
from sse_starlette.sse import EventSourceResponse

from backend.core.logging_config import get_logger

from ..core.auth import (
    get_current_organization,
    get_current_user,
    get_platform_token,
    security,
)
from ..models.chat import Chat<PERSON>essage, ChatMessageCreate, ChatThread, ChatThreadCreate
from ..services.call_service import CallService
from ..services.chat_service import ChatService
from ..services.chat_service import get_chat_service as get_service
from ..utils.logger import get_logger

logger = get_logger("chat_route")
router = APIRouter(
    prefix="/chat",
    tags=["Chat"],
    responses={
        (401): {"description": "Unauthorized - Invalid or missing JWT token"},
        (403): {"description": "Forbidden - Insufficient permissions"},
        (404): {"description": "Not found"},
        (500): {"description": "Internal server error"},
    },
    dependencies=[Depends(get_platform_token)],
)
call_router = APIRouter(
    prefix="/chat",
    tags=["Call"],
    responses={
        (404): {"description": "Not found"},
        (500): {"description": "Internal server error"},
    },
)


async def get_chat_service(
    agent_id: Optional[UUID] = None,
    organization_id: UUID = Depends(get_current_organization),
) -> ChatService:
    """Initialize and return the chat service.

    Uses the singleton pattern to reuse service instances for the same organization and agent.
    """
    try:
        return await get_service(organization_id=organization_id, agent_id=agent_id)
    except Exception as e:
        logger.error(f"Failed to initialize chat service: {e}", props={})
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/{agent_id}/threads", response_model=List[ChatThread], include_in_schema=False
)
async def list_chat_threads(
    agent_id: UUID,
    limit: int = Query(10, ge=1, le=50, description="Number of threads to return"),
    offset: int = Query(0, ge=0, description="Number of threads to skip"),
    chat_service: ChatService = Depends(get_chat_service),
    organization_id: UUID = Depends(get_current_organization),
    _auth_token: str = Depends(get_platform_token),
) -> List[ChatThread]:
    """List all chat threads for a specific agent."""
    try:
        return await chat_service.list_threads(agent_id, limit, offset)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing chat threads for agent {agent_id}: {e}", props={})
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.post("/{agent_id}/threads", response_model=ChatThread)
async def create_chat_thread(
    agent_id: UUID,
    thread_data: ChatThreadCreate,
    chat_service: ChatService = Depends(get_chat_service),
    organization_id: UUID = Depends(get_current_organization),
    _auth_token: str = Depends(get_platform_token),
    user_id: UUID = Depends(get_current_user),
) -> ChatThread:
    """Create a new chat thread."""
    try:
        return await chat_service.create_thread(thread_data, user_id, organization_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating chat thread for agent {agent_id}: {e}", props={})
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/{agent_id}/threads/{thread_id}", response_model=ChatThread)
async def get_chat_thread(
    agent_id: UUID,
    thread_id: UUID,
    message_limit: int = Query(
        50, ge=1, le=100, description="Number of messages to return"
    ),
    chat_service: ChatService = Depends(get_chat_service),
    organization_id: UUID = Depends(get_current_organization),
    user_id: UUID = Depends(get_current_user),
    _auth_token: str = Depends(get_platform_token),
) -> ChatThread:
    """Get a specific chat thread with its messages."""
    try:
        thread = await chat_service.get_thread(
            thread_id=thread_id,
            user_id=user_id,
            organization_id=organization_id,
            # message_limit=message_limit,
        )
        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")
        return thread
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error getting chat thread {thread_id} for agent {agent_id}: {e}", props={}
        )
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.post("/{agent_id}/threads/{thread_id}/messages", response_model=ChatMessage)
async def add_chat_message(
    agent_id: UUID,
    thread_id: UUID,
    message: ChatMessageCreate,
    chat_service: ChatService = Depends(get_chat_service),
    organization_id: UUID = Depends(get_current_organization),
    user_id: UUID = Depends(get_current_user),
    _auth_token: str = Depends(get_platform_token),
) -> ChatMessage:
    """Add a new message to a chat thread."""
    try:
        # Add user_id to message metadata if not already present
        if not message.metadata:
            message.metadata = {}
        message.metadata["user_id"] = str(user_id)

        return await chat_service.add_message(
            agent_id, thread_id, message, organization_id=organization_id
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error adding message to thread {thread_id} for agent {agent_id}: {e}",
            props={},
        )
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get(
    "/{agent_id}/threads/{thread_id}/messages", response_model=List[ChatMessage]
)
async def list_chat_messages(
    agent_id: UUID = Path(..., description="ID of the agent"),
    thread_id: UUID = Path(..., description="ID of the chat thread"),
    limit: int = Query(50, ge=1, le=100, description="Number of messages to return"),
    offset: int = Query(0, ge=0, description="Number of messages to skip"),
    chat_service: ChatService = Depends(get_chat_service),
    organization_id: UUID = Depends(get_current_organization),
    _auth_token: str = Depends(get_platform_token),
) -> List[ChatMessage]:
    """Get all messages for a specific chat thread."""
    try:
        return await chat_service.get_messages(
            thread_id, limit, offset, organization_id=organization_id
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error listing messages for thread {thread_id} for agent {agent_id}: {e}",
            props={},
        )
        logger.error(traceback.format_exc(), props={})
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/{agent_id}/threads/{thread_id}/stream")
async def stream_chat_messages(
    request: Request,
    agent_id: UUID,
    thread_id: UUID,
    chat_service: ChatService = Depends(get_chat_service),
    organization_id: UUID = Depends(get_current_organization),
    _auth_token: str = Depends(get_platform_token),
) -> EventSourceResponse:
    """Stream new messages for a chat thread using SSE."""

    async def event_generator():
        try:
            async for message in chat_service.stream_messages(agent_id, thread_id):
                if await request.is_disconnected():
                    logger.debug("Client disconnected", props={})
                    return
                yield {"event": "message", "data": message.json()}
        except Exception as e:
            logger.exception(f"Error streaming messages: {str(e)}", props={})
            # Yield error once and stop the generator
            try:
                yield {"event": "error", "data": str(e)}
            except Exception as yield_error:
                logger.exception(f"Error yielding error message: {yield_error}")
            finally:
                return

    return EventSourceResponse(event_generator())


@router.post("/{agent_id}/threads/{thread_id}/messages/stream")
async def add_chat_message_stream(
    agent_id: UUID,
    thread_id: UUID,
    message: ChatMessageCreate,
    chat_service: ChatService = Depends(get_chat_service),
    organization_id: UUID = Depends(get_current_organization),
    user_id: UUID = Depends(get_current_user),
    auth_token: str = Depends(get_platform_token),
) -> EventSourceResponse:
    """Add a new message to a chat thread and stream the AI response."""

    async def event_generator():
        try:
            # Create a context with user_id and auth_token
            context = {"user_id": str(user_id), "auth_token": auth_token}
            
            async for chunk in chat_service.process_message(
                thread_id=thread_id,
                message_data=message,
                organization_id=organization_id,
                user_id=user_id,
                context=context,
            ):
                try:
                    yield {"event": "message", "data": json.dumps(chunk)}
                except Exception as inner_e:  # Catch error during yield
                    logger.exception(
                        f"Error during message stream processing: {inner_e}", props={}
                    )
                    # Yield error once and stop the generator
                    try:
                        yield {"event": "error", "data": str(inner_e)}
                    except Exception as yield_error:
                        logger.exception(f"Error yielding error message: {yield_error}")
                    finally:
                        return  # Exit loop on error
        except Exception as e:
            logger.exception(f"Error streaming messages: {str(e)}", props={})
            # Yield error once and stop the generator
            try:
                yield {"event": "error", "data": str(e)}
            except Exception as yield_error:
                logger.exception(f"Error yielding error message: {yield_error}")
            finally:
                return

    return EventSourceResponse(event_generator())


@router.delete("/{agent_id}/threads/{thread_id}", status_code=204)
async def delete_chat_thread(
    agent_id: UUID,
    thread_id: UUID,
    chat_service: ChatService = Depends(get_chat_service),
    organization_id: UUID = Depends(get_current_organization),
    user_id: UUID = Depends(get_current_user),
    _auth_token: str = Depends(get_platform_token),
):
    """Delete a chat thread."""
    try:
        success = await chat_service.delete_thread(thread_id, organization_id, user_id)
        if not success:
            raise HTTPException(
                status_code=404, detail="Thread not found or could not be deleted"
            )
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(
            f"Error deleting thread {thread_id} for agent {agent_id}: {e}", props={}
        )
        raise HTTPException(status_code=500, detail=str(e)) from e


@call_router.api_route("/{agent_id}/call/incoming", methods=["GET", "POST"])
async def handle_incoming_call(request: Request, agent_id: UUID):
    """Handle incoming call and return TwiML response."""
    try:
        logger.info(f"Handling incoming call for agent {agent_id}", props={})
        call_service = CallService(agent_id=agent_id)
        return await call_service.handle_incoming_call(request)
    except Exception as e:
        logger.exception(f"Error handling incoming call: {e}", props={})
        raise HTTPException(
            status_code=500, detail="Error handling incoming call"
        ) from e


@call_router.websocket("/{agent_id}/call/media-stream")
async def handle_call_media_stream(websocket: WebSocket, agent_id: UUID):
    """Handle WebSocket connection for real-time audio streaming."""
    try:
        logger.info(f"Accepting WebSocket connection for agent {agent_id}", props={})
        await websocket.accept()
        call_service = CallService(agent_id=agent_id)
        await call_service.handle_media_stream(websocket)
    except Exception as e:
        logger.exception(f"Error in WebSocket connection: {e}", props={})
        try:
            await websocket.close()
        except:
            pass


@router.get("/history")
async def get_chat_history(
    limit: int = Query(10, ge=1, le=50, description="Number of threads to return"),
    cursor: Optional[str] = Query(None, description="Cursor for pagination"),
    user_id: str = Depends(get_current_user),
    organization_id: UUID = Depends(get_current_organization),
    chat_service: ChatService = Depends(get_chat_service),
    _auth_token: str = Depends(get_platform_token),
):
    """Get all chat histories for the authenticated user."""
    try:
        history_result = await chat_service.get_user_chat_history(
            user_id=user_id, organization_id=organization_id, limit=limit, cursor=cursor
        )
        return history_result
    except Exception as e:
        logger.exception(f"Error getting chat history: {e}", props={})
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/{agent_id}/recent-thread", response_model=Optional[ChatThread])
async def get_recent_thread(
    agent_id: UUID,
    organization_id: UUID = Depends(get_current_organization),
    user_id: UUID = Depends(get_current_user),
    _auth_token: str = Depends(get_platform_token),
) -> Optional[ChatThread]:
    """Get the most recent chat thread for an agent, if it exists."""
    try:
        # Avoid creating ChatService directly if get_chat_service dependency works
        chat_service = await get_chat_service(
            agent_id=agent_id, organization_id=organization_id
        )
        return await chat_service.get_recent_thread(agent_id, organization_id, user_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(
            f"Error getting recent thread for agent {agent_id}: {e}", props={}
        )
        raise HTTPException(status_code=500, detail=str(e)) from e
