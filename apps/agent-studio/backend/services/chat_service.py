import asyncio
import json
import re
import time
import traceback
import urllib.parse
import uuid
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional
from uuid import UUID, uuid4

from fastapi import HTTPException

from backend.core.redis_manager import RedisConnectionManager as redis_manager

from ..core.config import get_config
from ..core.database import get_supabase
from ..core.logging_config import get_logger
from ..models.chat import Chat<PERSON>essage, ChatMessageCreate, ChatThread, ChatThreadCreate
from ..prompt_library import format_prompt, load_prompt
from ..repositories.chat_cache_repository import ChatCacheRepository
from ..services.agent_service import AgentService
from ..services.memory_service import MemoryService

# Use the structured logger from logging_config
logger = get_logger(__name__)

_chat_service_instances = {}
_init_lock = asyncio.Lock()

settings = get_config()


class ChatService:
    def __init__(self, organization_id=None, agent_id=None):
        """Initialize chat service for an organization and agent."""
        try:
            self.organization_id = organization_id
            self.agent_id = agent_id
            self.db = None
            self.memory_service = MemoryService()
            self.streaming_service = None
            self._initialized = False
            self.cache_repository = ChatCacheRepository(
                organization_id=organization_id, agent_id=agent_id
            )
            logger.info(
                f"Chat service initialized for org: {organization_id}, agent: {agent_id}",
                props={
                    "organization_id": (
                        str(organization_id) if organization_id else None
                    ),
                    "agent_id": str(agent_id) if agent_id else None,
                },
            )
        except Exception as e:
            logger.error(
                f"Failed to initialize chat service: {e}", props={"error": str(e)}
            )
            logger.error(traceback.format_exc(), props={})
            raise

    async def initialize(self):
        """Complete async initialization steps."""
        if not self._initialized:
            try:
                self.db = await get_supabase()
                if self.agent_id:
                    from .langgraph_chat_service import get_langgraph_chat_service

                    self.streaming_service = await get_langgraph_chat_service(
                        organization_id=self.organization_id, agent_id=self.agent_id
                    )
                self._initialized = True
                logger.info(
                    f"Chat service async initialization complete for org: {self.organization_id}",
                    props={
                        "organization_id": (
                            str(self.organization_id) if self.organization_id else None
                        )
                    },
                )
            except Exception as e:
                logger.error(
                    f"Failed to complete async chat service initialization: {e}",
                    props={"error": str(e)},
                )
                logger.error(traceback.format_exc(), props={})
                raise

    async def _get_db(self):
        """Get the initialized database client. Initialize if needed."""
        if self.db is None:
            if not hasattr(self, "_initialized") or not self._initialized:
                await self.initialize()
            if self.db is None:
                self.db = await get_supabase()
                logger.debug("Database client initialized on demand", props={})
        return self.db

    def _add_user_filter_to_query(
        self, query: Any, user_id: UUID, *, is_anon: bool = False
    ) -> Any:
        """Adds the correct user ID filter (user_id or anon_user_id) to a query."""
        if is_anon:
            return query.eq("anon_user_id", str(user_id))
        else:
            return query.eq("user_id", str(user_id))

    async def store_message_in_redis_supabase(
        self,
        thread_id: UUID,
        message_data: ChatMessageCreate,
        organization_id: UUID,
        metadata: Dict = None,
    ) -> str:
        """
        Store a message in both Supabase database and Redis.
        Returns the message ID from Supabase.
        """
        try:
            # 1. Store message in Supabase
            db = await self._get_db()
            message_dict = {
                "thread_id": str(thread_id),
                "content": message_data.content,
                "role": message_data.role,
                "metadata": metadata or {},
                "organization_id": str(organization_id),
            }
            response = await db.table("chat_messages").insert(message_dict).execute()
            if not response.data:
                logger.error("Failed to create chat message in Supabase", props={})
                raise Exception("Failed to create chat message in Supabase")

            message_id = response.data[0]["id"]
            logger.info(
                f"Message stored in Supabase with ID: {message_id}",
                props={
                    "thread_id": str(thread_id),
                    "message_id": message_id,
                },
            )

            # 2. Store message in Redis if streaming service is available
            if self.streaming_service:
                try:
                    await self.initialize()
                    redis_message = {
                        "id": message_id,
                        "role": message_data.role,
                        "content": message_data.content,
                        "timestamp": datetime.utcnow().isoformat(),
                    }

                    # Store in Redis and check if we need to sync
                    await self.streaming_service._store_message_in_redis(
                        str(thread_id), redis_message
                    )
                    redis_messages = await self.streaming_service.message_storage.get_messages_from_redis(
                        str(thread_id)
                    )

                    # If there's only one message in Redis, sync all messages from Supabase
                    if len(redis_messages) == 1:
                        logger.info(
                            f"Only one message in Redis for thread {thread_id}, syncing all messages",
                            props={"thread_id": str(thread_id)},
                        )
                        await self._sync_messages_from_supabase_to_redis(thread_id)
                except Exception as e:
                    logger.error(
                        f"Redis storage failed: {str(e)}",
                        props={"thread_id": str(thread_id), "message_id": message_id},
                    )

            # 3. Invalidate cache if needed
            if hasattr(self, "cache_repository") and self.cache_repository:
                try:
                    cache_key = f"thread:{str(thread_id)}:messages"
                    if organization_id:
                        cache_key += f":org:{str(organization_id)}"

                    await self.cache_repository.delete(cache_key)

                    if hasattr(self, "user_id") and self.user_id:
                        await self.cache_repository.invalidate_user_threads(
                            self.user_id
                        )

                except Exception as e:
                    logger.error(
                        f"Failed to invalidate thread messages cache: {e}",
                        props={"thread_id": str(thread_id), "error": str(e)},
                    )

            return message_id
        except Exception as e:
            logger.error(
                f"Error in store_message_in_redis_supabase: {e}",
                props={"thread_id": str(thread_id)},
            )
            raise

    async def _sync_messages_from_supabase_to_redis(self, thread_id: UUID) -> None:
        """
        Sync all messages for a thread from Supabase to Redis.

        Args:
            thread_id: The UUID of the thread
        """
        if not self.streaming_service:
            logger.warning(
                f"Cannot sync messages to Redis: streaming service not initialized for thread {thread_id}",
                props={"thread_id": str(thread_id)},
            )
            return

        try:
            # Get all messages from Supabase
            db = await self._get_db()
            result = (
                await db.table("chat_messages")
                .select("*")
                .eq("thread_id", str(thread_id))
                .order("created_at")
                .execute()
            )

            if not result.data:
                logger.info(
                    f"No messages found in Supabase for thread {thread_id}",
                    props={"thread_id": str(thread_id)},
                )
                return

            logger.info(
                f"Syncing {len(result.data)} messages from Supabase to Redis for thread {thread_id}",
                props={"thread_id": str(thread_id)},
            )

            # Store each message in Redis
            for row in result.data:
                try:
                    redis_message = {
                        "id": row.get("id"),
                        "role": row.get("role"),
                        "content": row.get("content", ""),
                        "timestamp": row.get(
                            "created_at", datetime.utcnow().isoformat()
                        ),
                    }

                    # Add metadata if available
                    if row.get("metadata"):
                        if isinstance(row["metadata"], str):
                            try:
                                redis_message["metadata"] = json.loads(row["metadata"])
                            except json.JSONDecodeError:
                                redis_message["metadata"] = {}
                        else:
                            redis_message["metadata"] = row["metadata"]

                    # Store in Redis
                    await self.streaming_service._store_message_in_redis(
                        str(thread_id), redis_message
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to sync message {row.get('id')} to Redis: {e}",
                        props={
                            "thread_id": str(thread_id),
                            "message_id": row.get("id"),
                        },
                    )

            logger.info(
                f"Successfully synced {len(result.data)} messages from Supabase to Redis for thread {thread_id}",
                props={"thread_id": str(thread_id)},
            )
        except Exception as e:
            logger.error(
                f"Error syncing messages from Supabase to Redis: {e}",
                props={"thread_id": str(thread_id)},
            )
            logger.error(traceback.format_exc(), props={})

    async def process_message(
        self,
        thread_id: UUID,
        message_data: ChatMessageCreate,
        organization_id: UUID,
        user_id: UUID,
        context: Dict = None,
        deployment_type: Optional[str] = None,
    ) -> AsyncGenerator[Dict, None]:
        """Process a message and stream the response."""
        start_time = time.time()
        try:
            logger.info(
                f"Starting to process message in thread {thread_id}",
                props={"thread_id": str(thread_id)},
            )
            if self.db is None:
                await self.initialize()

            metadata = message_data.metadata or {}

            try:
                # Create a copy of message_data without ticket_data in metadata
                message_to_save = message_data.model_dump()
                if message_to_save.get("metadata") and message_to_save["metadata"].get(
                    "ticketData"
                ):
                    # Remove ticketData completely
                    del message_to_save["metadata"]["ticketData"]

                message_id = await self.store_message_in_redis_supabase(
                    thread_id=thread_id,
                    message_data=ChatMessageCreate(
                        **message_to_save
                    ),  # Convert back to ChatMessageCreate
                    organization_id=self.organization_id,
                    metadata=message_to_save.get("metadata", {}),
                )
            except Exception as e:
                logger.error(f"Failed to store message: {e}", props={})
                yield {"type": "error", "content": "Failed to create chat message"}

            ai_message_id = str(uuid4())
            tool_response = ""
            try:
                async for event in self.streaming_service.process_message(
                    agent_id=self.agent_id,
                    thread_id=thread_id,
                    message=message_data,
                    context=context or {},
                    message_id=message_id,
                    ai_message_id=ai_message_id,
                    deployment_type=deployment_type,
                ):
                    event_type = event.get("event")
                    logger.info(
                        f"Event received: {json.dumps(event, indent=2)}", props={}
                    )

                    # Pass through all events in the correct sequence
                    if event_type in [
                        "thread.message.created",
                        "thread.run.created",
                        "thread.run.step.created",
                        "thread.run.step.delta",
                        "thread.message.delta",
                        "thread.run.step.completed",
                    ]:
                        yield event

                        # Process delta content for tool calls only, without yielding the event again
                        delta_content = (
                            event.get("data", {}).get("delta", {}).get("content", [])
                        )
                        for content in delta_content:
                            if content.get("type") == "tool":
                                text = content.get("text", {}).get("value", "")
                                if text:
                                    tool_response = tool_response + text
                                    logger.info(
                                        f"Added tool content from message delta: {len(text)} chars",
                                        props={},
                                    )

                    elif event_type == "token_timing":
                        yield event

                    elif event_type == "thread.message.completed":
                        try:
                            content = (
                                event["data"]
                                .get("content", [{}])[0]
                                .get("text", {})
                                .get("value", "")
                            )

                            yield event

                        except Exception as e:
                            logger.error(
                                f"Error saving final response: {e}",
                                props={"thread_id": str(thread_id)},
                            )
                            yield {
                                "event": "thread.message.error",
                                "data": {"id": message_id, "error": str(e)},
                            }
                    elif event_type == "final_message":
                        try:
                            message_data = event.get("content", {})
                            if not message_data:
                                logger.error(
                                    "Empty message data in final_message event",
                                    props={},
                                )
                                continue

                            # Extract necessary fields
                            content = message_data.get("content", "")
                            role = message_data.get("role", "")

                            # Extract metadata
                            metadata = message_data.get("metadata", {})
                            if isinstance(metadata, str):
                                try:
                                    metadata = json.loads(metadata)
                                except json.JSONDecodeError:
                                    logger.error(
                                        "Invalid JSON in metadata",
                                        props={"thread_id": thread_id},
                                    )
                                    metadata = {}

                            # Create ChatMessageCreate object
                            chat_message = ChatMessageCreate(content=content, role=role)

                            # Store the message
                            ai_message_id = await self.store_message_in_redis_supabase(
                                thread_id=thread_id,
                                message_data=chat_message,
                                organization_id=self.organization_id,
                                metadata=metadata,
                            )

                        except Exception as e:
                            logger.error(
                                f"Error processing final_message: {e}",
                                props={"thread_id": str(thread_id)},
                                exc_info=True,
                            )
                    logger.info("Finished processing message", props={})
                    # Check if we need to update the thread title
                    # This is done after the first user message and AI response
                    await self._update_thread_title(thread_id, organization_id)
            except Exception as e:
                logger.error(
                    f"Error processing message: {e}",
                    props={"thread_id": str(thread_id), "error": str(e)},
                )
                logger.error(traceback.format_exc(), props={})
                raise
        finally:
            duration = time.time() - start_time
            logger.info(
                f"process_message completed in {duration:.2f}s for thread {thread_id}",
                props={},
            )

    async def add_message(
        self,
        agent_id: UUID,
        thread_id: UUID,
        message_data: ChatMessageCreate,
        organization_id: UUID,
    ) -> ChatMessage:
        """Add a message to a chat thread."""
        start_time = time.time()
        try:
            # Get user_id from message metadata
            user_id = None
            if message_data.metadata and "user_id" in message_data.metadata:
                user_id = message_data.metadata["user_id"]

            if not user_id:
                logger.error(
                    "User ID not found in message metadata",
                    props={"thread_id": str(thread_id)},
                )
                raise HTTPException(
                    status_code=400, detail="User ID not found in message metadata"
                )

            # Now we can get the thread with the user_id
            thread = await self.get_thread(
                thread_id, user_id=user_id, organization_id=self.organization_id
            )

            if not thread:
                raise HTTPException(status_code=404, detail="Thread not found")
            if thread.agent_id != agent_id:
                raise HTTPException(
                    status_code=404, detail="Chat thread not found for this agent"
                )

            metadata = message_data.metadata or {}
            metadata.update(
                {
                    "thread_id": str(thread_id),
                    "org_id": str(self.organization_id),
                    "agent_id": str(self.agent_id),
                }
            )

            if message_data.role == "user":
                try:
                    agent_service = AgentService(self.organization_id)
                    await agent_service.initialize()
                    agent_details = await agent_service.get_agent(agent_id)
                    if not agent_details:
                        raise HTTPException(status_code=404, detail="Agent not found")

                    result = await self.memory_service.process_message(
                        content=message_data.content,
                        org_id=organization_id,
                        thread_id=thread_id,
                        metadata=metadata,
                        use_custom_ner=(
                            agent_details.get("configuration", {}).get(
                                "use_custom_ner", False
                            )
                            if isinstance(agent_details, dict)
                            else getattr(
                                getattr(agent_details, "configuration", {}),
                                "use_custom_ner",
                                False,
                            )
                        ),
                    )

                    if result:
                        metadata.update(result.get("metadata", {}))
                        metadata["entity_ids"] = [
                            str(entity["id"]) for entity in result.get("entities", [])
                        ]
                        metadata["fact_ids"] = [
                            str(fact["fact_id"]) for fact in result.get("facts", [])
                        ]
                        logger.info(
                            f"Memory service processed message. Found {len(result.get('entities', []))} entities and {len(result.get('facts', []))} facts.",
                            props={"thread_id": str(thread_id)},
                        )
                except Exception as e:
                    logger.error(
                        f"Memory service error: {e}",
                        props={"thread_id": str(thread_id), "error": str(e)},
                    )
                    logger.error(traceback.format_exc(), props={})

            message_dict = {
                "thread_id": str(thread_id),
                "content": message_data.content,
                "role": message_data.role,
                "metadata": metadata,
                "organization_id": str(organization_id),
            }

            try:
                db = await self._get_db()
                if message_data.role == "user":
                    response = (
                        await db.table("chat_messages").insert(message_dict).execute()
                    )
                    if not response.data:
                        raise HTTPException(
                            status_code=500, detail="Failed to create chat message"
                        )

                    message = ChatMessage(**response.data[0])

                    # Invalidate relevant caches
                    # 1. Thread messages cache
                    thread_messages_key = f"thread:{thread_id}:messages"
                    if organization_id:
                        thread_messages_key += f":org:{organization_id}"
                    await self.cache_repository.delete(thread_messages_key)

                    # 2. Thread cache
                    thread_key = f"thread:{thread_id}:user:{thread.user_id}:org:{organization_id}"
                    await self.cache_repository.delete(thread_key)

                    # 3. User's threads cache
                    user_threads_key = f"user:{thread.user_id}:chat_history"
                    if organization_id:
                        user_threads_key += f":org:{organization_id}"
                    await self.cache_repository.delete(user_threads_key)

                    # 4. Agent-user threads cache
                    agent_user_threads_key = f"agent:{agent_id}:user:{thread.user_id}"
                    if organization_id:
                        agent_user_threads_key += f":org:{organization_id}"
                    await self.cache_repository.delete(agent_user_threads_key)

                    # 5. Recent thread cache
                    recent_thread_key = f"recent:agent:{agent_id}:user:{thread.user_id}:org:{organization_id}"
                    await self.cache_repository.delete(recent_thread_key)

                    logger.debug(
                        f"Invalidated caches for thread {thread_id} after adding message",
                        props={"thread_id": str(thread_id)},
                    )

                    return message
                return None
            except Exception as e:
                logger.error(
                    f"Failed to store message: {e}",
                    props={"thread_id": str(thread_id), "error": str(e)},
                )
                raise HTTPException(status_code=500, detail="Failed to store message")
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(
                f"Error adding message: {e}",
                props={"thread_id": str(thread_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            duration = time.time() - start_time
            logger.info(
                f"add_message completed in {duration:.2f}s for thread {thread_id}",
                props={"duration": duration, "thread_id": str(thread_id)},
            )

    async def _update_thread_timestamp(self, thread_id: UUID) -> None:
        """Update a thread's updated_at timestamp."""
        try:
            db = await self._get_db()
            await db.table("chat_threads").update(
                {"updated_at": datetime.utcnow().isoformat()}
            ).eq("id", str(thread_id)).execute()
        except Exception as e:
            logger.error(
                f"Error updating thread timestamp: {e}",
                props={"thread_id": str(thread_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})

    async def _update_thread_title(
        self, thread_id: UUID, organization_id: UUID
    ) -> None:
        """Update a thread's title based on the first user message and AI response.

        This function checks if the thread already has a generated title (using the title_generated metadata flag).
        If not, it retrieves messages from Redis (faster) or Supabase, generates a title using an LLM,
        and updates the thread title in Supabase.

        Args:
            thread_id: The UUID of the thread
            organization_id: The UUID of the organization
        """
        try:
            start_time = time.time()

            # Get messages from Redis first, falling back to Supabase if needed
            messages = await self.streaming_service.message_storage.get_messages(
                str(thread_id)
            )

            # Count user messages to determine if this is the first exchange
            user_messages = [msg for msg in messages if msg.role == "user"]

            # Only proceed if this is the first user message (count = 1)
            if len(user_messages) != 1:
                logger.info(
                    f"Not generating title for thread {thread_id} - found {len(user_messages)} user messages",
                    props={
                        "thread_id": str(thread_id),
                        "user_message_count": len(user_messages),
                    },
                )
                return

            # Get the thread data from Supabase (needed for updating title)
            db = await self._get_db()
            thread_response = (
                await db.table("chat_threads")
                .select("*")
                .eq("id", str(thread_id))
                .execute()
            )

            if not thread_response.data:
                logger.error(
                    f"Thread not found: {thread_id}",
                    props={"thread_id": str(thread_id)},
                )
                return

            thread = thread_response.data[0]
            metadata = thread.get("metadata", {})

            # If the title has already been generated, skip
            if metadata and metadata.get("title_generated", False):
                logger.info(
                    f"Title already generated for thread {thread_id}, skipping",
                    props={"thread_id": str(thread_id)},
                )
                return

            # 3. Generate a title using ChatOpenAI
            try:
                # Import the necessary modules
                from langchain_core.messages import (
                    AIMessage,
                    HumanMessage,
                    SystemMessage,
                )
                from langchain_openai import ChatOpenAI

                # Get API key from environment
                openai_api_key = settings.OPENAI_API_KEY
                if not openai_api_key:
                    logger.error(
                        "OPENAI_API_KEY not found in environment variables", props={}
                    )
                    return

                # Initialize ChatOpenAI
                llm = ChatOpenAI(
                    model=settings.OPENAI_MODEL,
                    temperature=0.7,
                    api_key=openai_api_key,
                )

                # Manually format the conversation history
                history_lines = []
                for msg in messages:  # Use original messages list for role info
                    role = msg.role if hasattr(msg, "role") else "unknown"
                    content = msg.content if hasattr(msg, "content") else ""
                    # Simple formatting, adjust role names if needed (e.g., HumanMessage -> User)
                    role_name = role.replace("Message", "")
                    history_lines.append(f"{role_name}: {content}")
                conversation_history_str = "\n".join(history_lines)

                # Load the prompt data first
                prompt_data = load_prompt("chat_service_title_generation")
                if not prompt_data:
                    logger.error("Failed to load title generation prompt", props={})
                    return  # or raise an error

                # Format the prompt using the loaded data and manually formatted history
                system_prompt_content = format_prompt(
                    prompt_data,
                    conversation_history=conversation_history_str,
                )

                # Create the final messages list for the LLM call
                llm_messages = [
                    SystemMessage(content=system_prompt_content),
                    HumanMessage(
                        content="Based on the conversation above, provide just the title without any additional text or explanation."
                    ),
                ]

                # Call the LLM to generate a title
                response = await llm.ainvoke(llm_messages)

                # Extract and clean up the title
                title = response.content.strip().strip('"').strip()

                # Limit title length if needed
                if len(title) > 100:
                    title = title[:97] + "..."

                logger.info(
                    f"Generated title for thread {thread_id}: {title}",
                    props={"thread_id": str(thread_id), "title": title},
                )

                # 4. Update the thread title and set the title_generated flag
                metadata["title_generated"] = True

                await db.table("chat_threads").update(
                    {
                        "title": title,
                        "metadata": metadata,
                        "updated_at": datetime.utcnow().isoformat(),
                    }
                ).eq("id", str(thread_id)).execute()
 
                # 5. Invalidate relevant caches
                # Thread cache
                thread_key = (
                    f"thread:{thread_id}:user:{thread['user_id']}:org:{organization_id}"
                )
                await self.cache_repository.delete(thread_key)

                # User's threads cache
                user_threads_key = f"user:{thread['user_id']}:chat_history"
                if organization_id:
                    user_threads_key += f":org:{organization_id}"
                await self.cache_repository.delete(user_threads_key)

                # Agent-user threads cache
                agent_user_threads_key = (
                    f"agent:{thread['agent_id']}:user:{thread['user_id']}"
                )
                if organization_id:
                    agent_user_threads_key += f":org:{organization_id}"
                await self.cache_repository.delete(agent_user_threads_key)

                # Recent thread cache
                recent_thread_key = f"recent:agent:{thread['agent_id']}:user:{thread['user_id']}:org:{organization_id}"
                await self.cache_repository.delete(recent_thread_key)

                logger.info(
                    f"Updated title for thread {thread_id} and invalidated caches",
                    props={"thread_id": str(thread_id)},
                )

            except Exception as e:
                logger.error(
                    f"Error generating title with LLM: {e}",
                    props={"thread_id": str(thread_id), "error": str(e)},
                )
                logger.error(traceback.format_exc(), props={})

        except Exception as e:
            logger.error(
                f"Error updating thread title: {e}",
                props={"thread_id": str(thread_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})
        finally:
            duration = time.time() - start_time
            logger.info(
                f"_update_thread_title completed in {duration:.2f}s for thread {thread_id}",
                props={"duration": duration, "thread_id": str(thread_id)},
            )

    async def create_thread(
        self,
        thread_data: ChatThreadCreate,
        user_id: UUID,
        organization_id: UUID,
        is_anon: bool = False,
    ) -> ChatThread:
        """Create a chat thread."""
        start_time = time.time()
        try:
            db = await self._get_db()

            # Prepare thread data
            thread_insert = {
                "agent_id": str(thread_data.agent_id),
                "organization_id": str(organization_id),
                "title": thread_data.title,
                "metadata": thread_data.metadata or {},
            }

            # Set either user_id or anon_user_id based on is_anon flag
            if is_anon:
                thread_insert["anon_user_id"] = str(user_id)
                # Ensure user_id is NULL to avoid foreign key constraint issues
                thread_insert["user_id"] = None

                # Add a flag in metadata to indicate this is a non-platform thread
                if "non_platform" not in thread_insert["metadata"]:
                    thread_insert["metadata"]["non_platform"] = True
            else:
                thread_insert["user_id"] = str(user_id)
                thread_insert["anon_user_id"] = None

            response = await db.table("chat_threads").insert(thread_insert).execute()
            if not response.data:
                raise HTTPException(
                    status_code=500, detail="Failed to create chat thread"
                )

            # Invalidate the chat history cache for this user
            # This ensures that when the user views their chat history next time,
            # they'll see the newly created thread
            try:
                await self.cache_repository.invalidate_user_chat_history(user_id)
                logger.info(
                    f"Invalidated chat history cache for user {user_id} after thread creation",
                    props={"user_id": str(user_id)},
                )
            except Exception as e:
                # Log the error but don't fail the thread creation
                logger.error(
                    f"Failed to invalidate chat history cache: {e}",
                    props={"user_id": str(user_id), "error": str(e)},
                )

            # --- START: Invalidate anon user history cache ---
            if is_anon:
                try:
                    # Ensure self.agent_id is available or get from thread_data
                    agent_id_to_invalidate = (
                        thread_data.agent_id
                    )  # Get agent_id from input data
                    if agent_id_to_invalidate:
                        logger.info(
                            f"Invalidating anon user history cache for user {user_id}, agent {agent_id_to_invalidate}",
                            props={
                                "user_id": str(user_id),
                                "agent_id": str(agent_id_to_invalidate),
                                "organization_id": str(organization_id),
                            },
                        )
                        await self.cache_repository.invalidate_anon_user_chat_history(
                            user_id=user_id,
                            agent_id=agent_id_to_invalidate,
                            organization_id=organization_id,
                        )
                    else:
                        logger.warning(
                            "Cannot invalidate anon history cache: agent_id missing in thread_data",
                            props={},
                        )
                except Exception as e:
                    logger.error(
                        f"Failed to invalidate anon user history cache: {e}",
                        props={
                            "user_id": str(user_id),
                            "organization_id": str(organization_id),
                            "error": str(e),
                        },
                    )
            # --- END: Invalidate anon user history cache ---

            # Get the created thread details
            thread = ChatThread(**response.data[0])
            # 2. Fetch the initial greeting message from Supabase
            # Get agent configuration from the database
            agent_config_response = (
                await db.table("agents")
                .select("configuration")
                .eq("id", str(thread_data.agent_id))
                .execute()
            )

            initial_message = "Hi, I'm Raj! How can I assist you today?\n\n ```suggestions\nWhat is this product about?\nTell me about pricing\n``` "  # Default fallback

            # Extract the initial message from the configuration if available
            if agent_config_response.data and len(agent_config_response.data) > 0:
                config = agent_config_response.data[0].get("configuration", {})
                if isinstance(config, str):
                    try:
                        config = json.loads(config)
                    except:
                        config = {}

                if config and "initial_ai_msg" in config:
                    initial_message = config["initial_ai_msg"]
                    config["initial_ai_msg"] = initial_message

            # 3. Create and store the initial AI message
            if initial_message:
                ai_message = ChatMessageCreate(
                    content=initial_message,
                    role="assistant",
                )

                await db.table("agents").update({"configuration": config}).eq(
                    "id", str(thread_data.agent_id)
                ).execute()

                await self.store_message_in_redis_supabase(
                    thread_id=thread.id,
                    message_data=ai_message,
                    organization_id=organization_id,
                    metadata={"is_greeting": True},
                )

                logger.info(
                    f"Added initial AI greeting to thread {thread.id}",
                    props={"thread_id": str(thread.id)},
                )

            return thread
        except Exception as e:
            logger.error(f"Error creating thread: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            duration = time.time() - start_time
            logger.info(f"create_thread completed in {duration:.2f}s", props={})

    async def get_thread(
        self,
        thread_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        is_anon: bool = False,
    ) -> ChatThread:
        """Get a chat thread.

        Args:
            thread_id: The UUID of the thread
            user_id: The UUID of the user or anonymous user
            organization_id: The UUID of the organization
            is_anon: Whether the user_id is for an anonymous user
        """
        start_time = time.time()
        try:
            # Generate a cache key for this thread
            cache_key = f"thread:{thread_id}:"
            if is_anon:
                cache_key += f"anon_user:{user_id}:"
            else:
                cache_key += f"user:{user_id}:"
            cache_key += f"org:{organization_id}"

            # Try to get the thread from cache first
            cached_thread = await self.cache_repository.get(cache_key)

            if cached_thread is not None:
                logger.info(
                    f"Cache HIT: Retrieved thread {thread_id} from cache",
                    props={
                        "thread_id": str(thread_id),
                        "user_id": str(user_id),
                        "source": "cache",
                    },
                )
                return ChatThread(**cached_thread)

            # If not in cache, query the database
            logger.debug(
                f"Cache miss for thread {thread_id}, querying database",
                props={"thread_id": str(thread_id), "user_id": str(user_id)},
            )

            db = await self._get_db()
            query = (
                db.table("chat_threads")
                .select("*")
                .eq("id", str(thread_id))
                .eq("organization_id", str(organization_id))
            )

            # Use helper function
            query = self._add_user_filter_to_query(query, user_id, is_anon=is_anon)

            response = await query.execute()

            if not response.data:
                raise HTTPException(status_code=404, detail="Chat thread not found")

            thread = ChatThread(**response.data[0])

            # Cache the result for future use (with a TTL of 10 minutes)
            await self.cache_repository.set(cache_key, response.data[0], ttl=600)

            logger.info(
                f"Database HIT: Retrieved thread {thread_id} from database",
                props={
                    "thread_id": str(thread_id),
                    "user_id": str(user_id),
                    "source": "database",
                },
            )

            return thread
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(
                f"Error getting thread: {e}",
                props={
                    "thread_id": str(thread_id),
                    "user_id": str(user_id),
                    "error": str(e),
                },
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            duration = time.time() - start_time
            logger.info(
                f"get_thread completed in {duration:.2f}s for thread {thread_id}",
                props={
                    "duration": duration,
                    "thread_id": str(thread_id),
                    "user_id": str(user_id),
                },
            )

    async def list_threads(
        self,
        agent_id: UUID,
        user_id: UUID,
        limit: int = 10,
        offset: int = 0,
        organization_id: UUID = None,
    ) -> List[ChatThread]:
        """List all chat threads for a user and agent."""
        start_time = time.time()
        try:
            # Generate a cache key for this specific combination
            cache_key = f"agent:{agent_id}:user:{user_id}"
            if organization_id:
                cache_key += f":org:{organization_id}"

            # Try to get threads from cache first
            cached_threads = await self.cache_repository.get(cache_key)

            if cached_threads is not None:
                logger.info(
                    f"Cache HIT: Retrieved threads for agent {agent_id} user {user_id} from cache",
                    props={
                        "agent_id": str(agent_id),
                        "user_id": str(user_id),
                        "source": "cache",
                    },
                )

                # Apply pagination in memory
                paginated_threads = cached_threads[offset : offset + limit]
                return [ChatThread(**thread) for thread in paginated_threads]

            # If not in cache, check if we need to get all threads or just the paginated ones
            logger.debug(
                f"Cache miss for threads list, querying database",
                props={"agent_id": str(agent_id), "user_id": str(user_id)},
            )

            db = await self._get_db()

            # If we're getting the first page or a small offset, it's more efficient to get all threads at once
            if (
                offset == 0 or offset < 50
            ):  # Smaller threshold since this is filtered by agent and user
                # Build the query
                query = (
                    db.table("chat_threads")
                    .select("*")
                    .eq("agent_id", str(agent_id))
                    .eq("user_id", str(user_id))
                )
                if organization_id:
                    query = query.eq("organization_id", str(organization_id))

                # Get all threads without pagination
                all_threads_response = await query.order(
                    "created_at", desc=True
                ).execute()
                all_threads = all_threads_response.data

                if all_threads:
                    # Cache all threads for future use (with a TTL of 10 minutes)
                    await self.cache_repository.set(cache_key, all_threads, ttl=600)

                    # Return the requested page
                    start_idx = offset
                    end_idx = min(offset + limit, len(all_threads))
                    return [
                        ChatThread(**thread)
                        for thread in all_threads[start_idx:end_idx]
                    ]
                return []
            else:
                # For larger offsets, just get the requested page
                query = (
                    db.table("chat_threads")
                    .select("*")
                    .eq("agent_id", str(agent_id))
                    .eq("user_id", str(user_id))
                )
                if organization_id:
                    query = query.eq("organization_id", str(organization_id))

                response = (
                    await query.order("created_at", desc=True)
                    .limit(limit)
                    .offset(offset)
                    .execute()
                )
                logger.info(
                    f"Database HIT: Retrieved threads for agent {agent_id} user {user_id} from database",
                    props={
                        "agent_id": str(agent_id),
                        "user_id": str(user_id),
                        "source": "database",
                    },
                )
                return [ChatThread(**thread) for thread in response.data]
        except Exception as e:
            logger.error(
                f"Error listing threads: {e}",
                props={
                    "agent_id": str(agent_id),
                    "user_id": str(user_id),
                    "error": str(e),
                },
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            duration = time.time() - start_time
            logger.info(
                f"list_threads completed in {duration:.2f}s for agent {agent_id}",
                props={"duration": duration, "agent_id": str(agent_id)},
            )

    async def get_messages(
        self,
        thread_id: UUID,
        limit: int = 50,
        offset: int = 0,
        organization_id: Optional[UUID] = None,
    ) -> List[ChatMessage]:
        """Get messages for a thread."""
        start_time = time.time()
        try:
            # Generate a cache key for this thread's messages
            cache_key = f"thread:{thread_id}:messages"
            if organization_id:
                cache_key += f":org:{organization_id}"

            # Try to get messages from cache first
            cached_messages = await self.cache_repository.get(cache_key)

            if cached_messages is not None:
                logger.info(
                    f"Cache HIT: Retrieved messages for thread {thread_id} from cache",
                    props={"thread_id": str(thread_id), "source": "cache"},
                )

                # Apply pagination in memory
                paginated_messages = cached_messages[offset : offset + limit]
                return [
                    self._process_message_tool_calls(ChatMessage(**msg))
                    for msg in paginated_messages
                ]

            # If not in cache, check if we need to get all messages or just the paginated ones
            logger.debug(
                f"Cache miss for thread messages, querying database",
                props={"thread_id": str(thread_id)},
            )

            db = await self._get_db()

            # First get system messages
            system_query = (
                db.table("chat_messages")
                .select("*")
                .eq("thread_id", str(thread_id))
                .eq("role", "system")
            )
            if organization_id:
                system_query = system_query.eq("organization_id", str(organization_id))
            system_response = await system_query.execute()
            system_message = None
            if system_response.data:
                message_data = system_response.data[0]
                system_message = self._process_message_tool_calls(
                    ChatMessage(**message_data)
                )

            # If we're getting the first page or a small offset, it's more efficient to get all messages at once
            if (
                offset == 0 or offset < 100
            ):  # Threads can have many messages, so use a higher threshold
                # Build the query
                query = (
                    db.table("chat_messages")
                    .select("*")
                    .eq("thread_id", str(thread_id))
                    .neq("role", "system")
                    .neq("role", "tool")  # Excludes tool messages
                )
                if organization_id:
                    query = query.eq("organization_id", str(organization_id))

                # Get all messages without pagination
                all_messages_response = await query.order(
                    "created_at", desc=True
                ).execute()
                all_messages = all_messages_response.data

                all_messages = [
                    msg
                    for msg in all_messages
                    if not (
                        msg["role"] == "assistant"
                        and (msg.get("content") == "" or msg.get("content") is None)
                    )
                ]

                if all_messages:
                    # Cache all messages for future use (with a TTL of 5 minutes)
                    await self.cache_repository.set(cache_key, all_messages, ttl=300)

                    # Return the requested page
                    start_idx = offset
                    end_idx = min(offset + limit, len(all_messages))
                    messages = [
                        self._process_message_tool_calls(ChatMessage(**msg))
                        for msg in all_messages[start_idx:end_idx]
                    ]
                    messages.reverse()
                    if system_message:
                        messages.insert(0, system_message)
                    logger.info(
                        f"Database HIT: Retrieved messages for thread {thread_id} from database",
                        props={"thread_id": str(thread_id), "source": "database"},
                    )
                    return messages

                # If no regular messages but we have a system message
                if system_message:
                    return [system_message]
                return []
            else:
                # For larger offsets, just get the requested page
                query = (
                    db.table("chat_messages")
                    .select("*")
                    .eq("thread_id", str(thread_id))
                    .neq("role", "system")
                    .neq("role", "tool")
                )
                if organization_id:
                    query = query.eq("organization_id", str(organization_id))

                response = (
                    await query.order("created_at", desc=True)
                    .limit(limit)
                    .offset(offset)
                    .execute()
                )
                messages = [
                    self._process_message_tool_calls(ChatMessage(**msg))
                    for msg in response.data
                    if not (
                        msg["role"] == "assistant"
                        and (msg.get("content") == "" or msg.get("content") is None)
                    )
                ]
                messages.reverse()
                if offset == 0 and system_message:
                    messages.insert(0, system_message)
                logger.info(
                    f"Database HIT: Retrieved messages for thread {thread_id} from database",
                    props={"thread_id": str(thread_id), "source": "database"},
                )
                return messages
        except Exception as e:
            logger.error(
                f"Error getting messages: {e}",
                props={"thread_id": str(thread_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            duration = time.time() - start_time
            logger.info(
                f"get_messages completed in {duration:.2f}s for thread {thread_id}",
                props={"duration": duration, "thread_id": str(thread_id)},
            )

    async def get_formatted_chat_history_for_thread(
        self, thread_id: UUID, organization_id: UUID
    ) -> Dict[str, Any]:
        """
        Retrieves all 'user' and 'assistant' messages for a given thread,
        formats them into an HTML string, and provides a JSON string of raw messages.
        Removes suggestion blocks from content before processing.
        """
        start_time = time.time()
        logger.info(
            "Fetching formatted chat history and raw messages for thread %s and organization %s",
            str(thread_id),
            str(organization_id),
            props={
                "thread_id": str(thread_id),
                "organization_id": str(organization_id),
            },
        )
        try:
            db = await self._get_db()
            response = (
                await db.table("chat_messages")
                .select(
                    "role, content, created_at"
                )  # Fetched created_at for potential future use in JSON
                .eq("thread_id", str(thread_id))
                .eq("organization_id", str(organization_id))
                .in_("role", ["user", "assistant"])
                .order("created_at", desc=False)
                .execute()
            )

            if not response.data:
                logger.info(
                    "No user or assistant messages found for thread %s",
                    str(thread_id),
                    props={"thread_id": str(thread_id)},
                )
                return {  # Return dict for no history
                    "html_string": "<p>No chat history found for this thread.</p>",
                    "raw_messages_json": json.dumps([]),
                }

            html_parts = []
            raw_messages_for_json = []
            suggestion_pattern = r"```suggestions[\s\S]*?```"
            import html

            for message_row in response.data:
                role = message_row.get("role", "unknown").capitalize()
                original_content = message_row.get("content", "").strip()
                created_at = message_row.get("created_at")  # Get created_at

                # Remove suggestions blocks for both HTML and JSON versions
                content_cleaned = re.sub(
                    suggestion_pattern, "", original_content
                ).strip()

                # Prepare for HTML output
                escaped_content_for_html = html.escape(content_cleaned).replace(
                    "\n", "<br>"
                )
                html_parts.append(
                    f"<p><strong>{role}:</strong><br>{escaped_content_for_html}</p>"
                )

                # Prepare for JSON output (using cleaned content)
                raw_messages_for_json.append(
                    {
                        "role": role.lower(),  # Store role in lowercase
                        "content": content_cleaned,  # Use cleaned content
                        "timestamp": created_at,  # Include timestamp if available
                    }
                )

            history_html_string = "\n".join(html_parts)
            try:
                # Wrap the list in a dictionary with the "messages" key
                history_data_for_json = {"messages": raw_messages_for_json}
                history_raw_json_string = json.dumps(
                    history_data_for_json, ensure_ascii=False
                )
            except TypeError as te:
                logger.error(
                    "TypeError during JSON serialization of raw messages for thread %s: %s",
                    str(thread_id),
                    str(te),
                )
                history_raw_json_string = json.dumps(
                    [{"error": "Failed to serialize messages"}]
                )

            duration = time.time() - start_time
            logger.info(
                "Successfully fetched/formatted chat history & raw messages in %.2f sec for thread %s",
                duration,
                str(thread_id),
                props={
                    "thread_id": str(thread_id),
                    "organization_id": str(organization_id),
                    "duration": duration,
                    "message_count": len(response.data),
                },
            )
            return {  # Return dict
                "html_string": history_html_string,
                "raw_messages_json": history_raw_json_string,
            }

        except Exception as e:
            logger.exception(
                "Error fetching formatted chat history and raw messages for thread %s: %s",
                str(thread_id),
                e,
                props={
                    "thread_id": str(thread_id),
                    "organization_id": str(organization_id),
                },
            )
            return {  # Return dict on error too
                "html_string": f"<p>Error retrieving chat history: {html.escape(str(e))}</p>",
                "raw_messages_json": json.dumps(
                    [{"error": f"Failed to retrieve messages: {str(e)}"}]
                ),
            }

    def _process_message_tool_calls(self, message: ChatMessage) -> ChatMessage:
        """Process tool calls in a message."""
        if message.tool_calls:
            tool_calls = []
            for tc in message.tool_calls:
                tool_calls.append(
                    {
                        "id": tc.get("id"),
                        "output": tc.get("output"),
                        "tool_name": tc.get("tool_name", "unknown"),
                        "arguments": tc.get("arguments", {}),
                        "start_time": datetime.now(),
                        "result": (
                            {"output": tc.get("output")} if tc.get("output") else None
                        ),
                        "error": None,
                    }
                )
            message.tool_calls = tool_calls
        return message

    async def get_thread_with_messages(
        self,
        thread_id: UUID,
        user_id: UUID,
        message_limit: int = 50,
        organization_id: UUID = None,
    ) -> ChatThread:
        """Get a chat thread with its messages."""
        thread = await self.get_thread(thread_id, user_id, organization_id)
        messages = await self.get_messages(
            thread_id, limit=message_limit, organization_id=organization_id
        )
        thread.messages = messages
        return thread

    async def get_user_chat_history(
        self,
        user_id: str,
        organization_id: UUID,
        limit: int = 10,
        cursor: Optional[str] = None,
        is_anon: bool = False,
    ) -> Dict:
        """
        Get chat history for a user across all agents
        Returns threads with agent details and pagination info
        """
        start_time = time.time()
        logger.info(
            f"Getting chat history for user {user_id}", props={"user_id": user_id}
        )
        try:
            # Generate a cache key for this user's chat history
            cache_key = "user_chat_history:"
            if is_anon:
                cache_key += f"anon_user:{user_id}:"
            else:
                cache_key += f"user:{user_id}:"
            cache_key += (
                f"org:{organization_id}:cursor:{cursor or 'none'}:limit:{limit}"
            )

            # Try to get chat history from cache first
            cached_history = await self.cache_repository.get(cache_key)

            if cached_history is not None:
                logger.info(
                    f"Cache HIT: Retrieved chat history for user {user_id} from cache",
                    props={"user_id": user_id, "source": "cache"},
                )
                # Convert cached data to Pydantic models
                result = {
                    "conversations": [
                        ChatThread(**thread)
                        for thread in cached_history.get("conversations", [])
                    ],
                    "has_more": cached_history.get("has_more", False),
                    "next_cursor": cached_history.get("next_cursor"),
                }
                return result

            # If not in cache, query the database
            logger.debug(
                f"Cache miss for user chat history, querying database",
                props={"user_id": user_id},
            )

            db = await self._get_db()
            query = (
                db.from_("chat_threads")
                .select("*")
                .eq("organization_id", str(organization_id))
            )

            # Use helper function
            # Convert user_id str to UUID for the query helper, if needed
            try:
                if isinstance(user_id, UUID):
                    user_uuid = user_id  # Use it directly if already a UUID
                else:
                    user_uuid = UUID(user_id)  # Otherwise, convert from string

                query = self._add_user_filter_to_query(
                    query, user_uuid, is_anon=is_anon
                )
            except ValueError:
                logger.error(
                    f"Invalid UUID format for user_id in get_user_chat_history: {user_id}",
                    props={},
                )
                raise HTTPException(status_code=400, detail="Invalid user ID format")

            if cursor:
                try:
                    # Handle cursor formatting for timestamp comparison
                    decoded_cursor = urllib.parse.unquote(cursor)
                    formatted_cursor = self._format_timestamp_for_query(decoded_cursor)
                    logger.info(
                        f"Formatted cursor timestamp: {formatted_cursor}",
                        props={"cursor": formatted_cursor},
                    )
                    query = query.lt("updated_at", formatted_cursor)
                except Exception as e:
                    logger.error(
                        f"Error formatting cursor timestamp: {e}",
                        props={"cursor": cursor, "error": str(e)},
                    )
                    logger.error(traceback.format_exc(), props={})

            thread_response = (
                await query.order("updated_at", desc=True).limit(limit + 1).execute()
            )

            if not thread_response.data:
                logger.info("No chat history found", props={"user_id": user_id})
                result = {"conversations": [], "has_more": False, "next_cursor": None}
                # Cache the empty result for a short time
                await self.cache_repository.set(cache_key, result, ttl=60)
                return result

            # Get agent details for all threads
            agent_ids = list(set(thread["agent_id"] for thread in thread_response.data))
            agent_response = (
                await db.from_("agents")
                .select(
                    "id, name, description, template_id, organization_id, configuration, metadata, avatar_url, status, created_at, updated_at"
                )
                .in_("id", agent_ids)
                .execute()
            )

            agent_map = (
                {agent["id"]: agent for agent in agent_response.data}
                if agent_response.data
                else {}
            )

            # Handle pagination
            has_more = len(thread_response.data) > limit
            threads_to_process = thread_response.data[:limit]
            next_cursor = None

            if has_more and threads_to_process:
                # Generate next cursor from the timestamp of the last thread
                next_cursor = self._generate_next_cursor(
                    threads_to_process[-1]["updated_at"]
                )

            # Process threads and convert to ChatThread models
            chat_history = []
            for thread in threads_to_process:
                agent_id = thread["agent_id"]
                agent_data = agent_map.get(agent_id, {})

                # Prepare thread data with agent information in metadata
                thread_data = {
                    "id": thread["id"],
                    "agent_id": agent_id,
                    "title": thread["title"] or "Untitled Chat",
                    "created_at": thread["created_at"],
                    "updated_at": thread["updated_at"],
                    "metadata": thread.get("metadata", {}),
                }

                # Add appropriate user ID field based on is_anon flag
                if is_anon:
                    thread_data["anon_user_id"] = thread["anon_user_id"]
                    thread_data["user_id"] = None
                else:
                    thread_data["user_id"] = thread["user_id"]
                    thread_data["anon_user_id"] = None

                # Add agent information to metadata
                thread_data["metadata"]["agent"] = {
                    "id": agent_id,
                    "name": agent_data.get("name", "Unknown Agent"),
                    "avatar_url": agent_data.get("avatar_url"),
                    "description": agent_data.get("description", ""),
                    "metadata": agent_data.get("metadata", {}),
                }

                # Create ChatThread model
                chat_thread = ChatThread(**thread_data)
                chat_history.append(chat_thread)

            logger.info(
                f"Retrieved {len(chat_history)} conversations for user {user_id}",
                props={"user_id": user_id, "count": len(chat_history)},
            )

            # Prepare result with ChatThread models
            result = {
                "conversations": chat_history,
                "has_more": has_more,
                "next_cursor": next_cursor,
            }

            # Cache the result for future use (with a TTL of 5 minutes)
            # We need to convert Pydantic models to dict for caching
            cache_data = {
                "conversations": [thread.model_dump() for thread in chat_history],
                "has_more": has_more,
                "next_cursor": next_cursor,
            }
            await self.cache_repository.set(cache_key, cache_data, ttl=300)

            logger.info(
                f"Database HIT: Retrieved chat history for user {user_id} from database",
                props={"user_id": user_id, "source": "database"},
            )

            return result
        except Exception as e:
            logger.error(
                f"Error getting user chat history: {e}",
                props={"user_id": str(user_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            duration = time.time() - start_time
            logger.info(
                f"get_user_chat_history completed in {duration:.2f}s for user {user_id}",
                props={"duration": duration, "user_id": str(user_id)},
            )

    def _format_timestamp_for_query(self, timestamp: str) -> str:
        """Format timestamp for database query"""
        try:
            # Handle different timestamp formats
            if " " in timestamp:
                dt_part, tz_part = timestamp.rsplit(" ", 1)
                if tz_part and not (tz_part.startswith("+") or tz_part.startswith("-")):
                    tz_part = f"+{tz_part}"
                return f"{dt_part.replace(' ', 'T')}{tz_part}"
            elif (
                "T" not in timestamp
                and "+" not in timestamp
                and "-" not in timestamp[10:]
            ):
                # Add timezone if missing
                return f"{timestamp}+00:00"
            return timestamp
        except Exception as e:
            logger.error(f"Error formatting timestamp: {e}", props={"error": str(e)})
            return timestamp

    def _generate_next_cursor(self, timestamp: str) -> str:
        """Generate next cursor from timestamp"""
        try:
            # Ensure timestamp is in the correct format for cursor
            if "T" in timestamp:
                if "+" in timestamp:
                    dt_part, tz_part = timestamp.split("+")
                    return f"{dt_part}+{tz_part}"
                elif "-" in timestamp:
                    match = re.search("(-\\d{2}:\\d{2})$", timestamp)
                    if match:
                        tz_part = match.group(1)
                        dt_part = timestamp[: match.start()]
                        return f"{dt_part}{tz_part}"
                    else:
                        return f"{timestamp}+00:00"
                else:
                    return f"{timestamp}+00:00"
            else:
                # Convert to ISO format if not already
                from datetime import datetime

                dt_obj = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S.%f")
                next_cursor = dt_obj.isoformat().replace(" ", "T")
                if "+" not in next_cursor and "-" not in next_cursor[10:]:
                    next_cursor += "+00:00"
                return next_cursor
        except Exception as e:
            logger.error(f"Error generating next cursor: {e}", props={"error": str(e)})
            return timestamp

    async def get_recent_thread(
        self,
        agent_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        is_anon: bool = False,
    ) -> Optional[ChatThread]:
        """Get the most recent chat thread for a user and agent."""
        start_time = time.time()
        try:
            # Generate a cache key for this specific combination
            cache_key = f"recent:agent:{agent_id}:"
            if is_anon:
                cache_key += f"anon_user:{user_id}:"
            else:
                cache_key += f"user:{user_id}:"
            cache_key += f"org:{organization_id}"

            # Try to get the recent thread from cache first
            cached_thread = await self.cache_repository.get(cache_key)

            if cached_thread is not None:
                logger.info(
                    f"Cache HIT: Retrieved recent thread for agent {agent_id} user {user_id} from cache",
                    props={
                        "agent_id": str(agent_id),
                        "user_id": str(user_id),
                        "source": "cache",
                    },
                )
                return ChatThread(**cached_thread) if cached_thread else None

            # If not in cache, query the database
            logger.debug(
                f"Cache miss for recent thread, querying database",
                props={"agent_id": str(agent_id), "user_id": str(user_id)},
            )

            db = await self._get_db()
            query = (
                db.table("chat_threads")
                .select("*")
                .eq("agent_id", str(agent_id))
                .eq("organization_id", str(organization_id))
            )

            # Use helper function
            query = self._add_user_filter_to_query(query, user_id, is_anon=is_anon)

            response = await query.order("created_at", desc=True).limit(1).execute()

            # Process the result
            if response.data:
                thread = ChatThread(**response.data[0])
                # Cache the result for future use (with a TTL of 5 minutes)
                await self.cache_repository.set(cache_key, response.data[0], ttl=300)
                logger.info(
                    f"Database HIT: Retrieved recent thread for agent {agent_id} user {user_id} from database",
                    props={
                        "agent_id": str(agent_id),
                        "user_id": str(user_id),
                        "source": "database",
                    },
                )
                return thread
            else:
                # Cache the empty result to avoid repeated database queries
                await self.cache_repository.set(
                    cache_key, None, ttl=60
                )  # Shorter TTL for empty results
                return None
        except Exception as e:
            logger.error(
                f"Error getting recent thread: {e}",
                props={
                    "agent_id": str(agent_id),
                    "user_id": str(user_id),
                    "error": str(e),
                },
            )
            logger.error(traceback.format_exc(), props={})
            return None
        finally:
            duration = time.time() - start_time
            logger.info(
                f"get_recent_thread completed in {duration:.2f}s for agent {agent_id} user {user_id}",
                props={
                    "duration": duration,
                    "agent_id": str(agent_id),
                    "user_id": str(user_id),
                },
            )

    async def get_threads_by_user(
        self, user_id: UUID, organization_id: UUID, limit: int = 50, offset: int = 0
    ) -> List[ChatThread]:
        """Get chat threads for a user."""
        try:
            # Try to get threads from cache first
            cached_threads = await self.cache_repository.get_user_threads(user_id)

            if cached_threads is not None:
                logger.info(
                    f"Cache HIT: Retrieved threads for user {user_id} from cache",
                    props={
                        "user_id": str(user_id),
                        "organization_id": str(organization_id),
                        "source": "cache",
                    },
                )

                # Apply pagination in memory
                paginated_threads = cached_threads[offset : offset + limit]
                return [ChatThread(**thread) for thread in paginated_threads]

            # If not in cache, check if we need to get all threads or just the paginated ones
            logger.debug(
                f"Cache miss for user threads, querying database",
                props={
                    "user_id": str(user_id),
                    "organization_id": str(organization_id),
                },
            )

            db = await self._get_db()

            # If we're getting the first page or a small offset, it's more efficient to get all threads at once
            # and cache them for future use
            if (
                offset == 0 or offset < 100
            ):  # Arbitrary threshold, adjust based on typical thread counts
                # Get all threads for the user without pagination
                all_threads_response = (
                    await db.table("chat_threads")
                    .select("*")
                    .eq("user_id", str(user_id))
                    .eq("organization_id", str(organization_id))
                    .order("updated_at", desc=True)
                    .execute()
                )

                all_threads = all_threads_response.data

                if all_threads:
                    # Cache all threads for future use
                    await self.cache_repository.set_user_threads(user_id, all_threads)

                    # Return the requested page
                    start_idx = offset
                    end_idx = min(offset + limit, len(all_threads))
                    return [
                        ChatThread(**thread)
                        for thread in all_threads[start_idx:end_idx]
                    ]
                return []
            else:
                # For larger offsets, just get the requested page
                response = (
                    await db.table("chat_threads")
                    .select("*")
                    .eq("user_id", str(user_id))
                    .eq("organization_id", str(organization_id))
                    .order("updated_at", desc=True)
                    .limit(limit)
                    .offset(offset)
                    .execute()
                )

                logger.info(
                    f"Database HIT: Retrieved threads for user {user_id} from database",
                    props={
                        "user_id": str(user_id),
                        "organization_id": str(organization_id),
                        "source": "database",
                    },
                )
                return [ChatThread(**thread) for thread in response.data]
        except Exception as e:
            logger.error(
                f"Error getting threads for user {user_id}: {e}",
                props={"user_id": str(user_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})
            return []

    async def get_thread_messages(
        self, thread_id: UUID, organization_id: UUID, limit: int = 50, offset: int = 0
    ) -> List[ChatMessage]:
        """Get messages for a chat thread."""
        try:
            # Calculate the page number for cache
            page = (offset // limit) + 1
            page_size = limit

            # Try to get messages from cache first
            cached_result = await self.cache_repository.get_paginated_thread_messages(
                thread_id, page=page, page_size=page_size
            )

            if cached_result:
                logger.info(
                    f"Cache HIT: Retrieved messages for thread {thread_id} from cache",
                    props={
                        "thread_id": str(thread_id),
                        "organization_id": str(organization_id),
                        "source": "cache",
                    },
                )
                return [ChatMessage(**msg) for msg in cached_result["messages"]]

            # If not in cache, check if we need to get all messages or just the paginated ones
            logger.debug(
                f"Cache miss for thread messages, querying database",
                props={
                    "thread_id": str(thread_id),
                    "organization_id": str(organization_id),
                },
            )

            db = await self._get_db()

            # If we're getting the first page or a small offset, it's more efficient to get all messages at once
            # and cache them for future use
            if (
                offset == 0 or offset < 100
            ):  # Arbitrary threshold, adjust based on typical message counts
                # Get all messages for the thread
                all_messages_response = (
                    await db.table("chat_messages")
                    .select("*")
                    .eq("thread_id", str(thread_id))
                    .eq("organization_id", str(organization_id))
                    .order("created_at", desc=True)
                    .execute()
                )

                all_messages = all_messages_response.data

                if all_messages:
                    # Cache all messages for future use
                    await self.cache_repository.set_thread_messages(
                        thread_id, all_messages
                    )

                    # Return the requested page
                    start_idx = offset
                    end_idx = min(offset + limit, len(all_messages))
                    return [
                        ChatMessage(**message)
                        for message in all_messages[start_idx:end_idx]
                    ]
                return []
            else:
                # For larger offsets, just get the requested page
                response = (
                    await db.table("chat_messages")
                    .select("*")
                    .eq("thread_id", str(thread_id))
                    .eq("organization_id", str(organization_id))
                    .order("created_at", desc=True)
                    .limit(limit)
                    .offset(offset)
                    .execute()
                )

                logger.info(
                    f"Database HIT: Retrieved messages for thread {thread_id} from database",
                    props={
                        "thread_id": str(thread_id),
                        "organization_id": str(organization_id),
                        "source": "database",
                    },
                )
                return [ChatMessage(**message) for message in response.data]
        except Exception as e:
            logger.error(
                f"Error getting messages for thread {thread_id}: {e}",
                props={"thread_id": str(thread_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})
            return []

    async def create_message(
        self,
        thread_id: UUID,
        message_data: ChatMessageCreate,
        organization_id: UUID,
        user_id: UUID = None,
    ) -> Optional[ChatMessage]:
        """Create a new chat message."""
        try:
            # If user_id is not provided, try to get it from message metadata
            if (
                user_id is None
                and message_data.metadata
                and "user_id" in message_data.metadata
            ):
                user_id = message_data.metadata["user_id"]

            # If we still don't have a user_id, we need to query the thread from the database
            if user_id is None:
                db = await self._get_db()
                thread_response = (
                    await db.table("chat_threads")
                    .select("user_id")
                    .eq("id", str(thread_id))
                    .execute()
                )

                if not thread_response.data or not thread_response.data[0]:
                    logger.error(
                        f"Thread not found: {thread_id}",
                        props={"thread_id": str(thread_id)},
                    )
                    return None

                user_id = thread_response.data[0]["user_id"]

            # Insert the message
            message_id = str(uuid.uuid4())
            message_dict = {
                "id": message_id,
                "thread_id": str(thread_id),
                "organization_id": str(organization_id),
                "role": message_data.role,
                "content": message_data.content,
                "metadata": message_data.metadata or {},
            }

            db = await self._get_db()
            response = await db.table("chat_messages").insert(message_dict).execute()

            if not response.data:
                logger.error(
                    f"Failed to create message for thread {thread_id}",
                    props={"thread_id": str(thread_id)},
                )
                return None

            # Create message object
            message = ChatMessage(**response.data[0])

            # Update thread timestamp
            await self._update_thread_timestamp(thread_id)

            # Add message to cache
            await self.cache_repository.add_thread_message(thread_id, response.data[0])

            # Update thread in cache (updated_at changed)
            thread = await self.get_thread(
                thread_id, user_id=user_id, organization_id=organization_id
            )
            if thread:
                await self.cache_repository.set_thread(thread_id, thread.model_dump())

                # Invalidate user threads cache (order changed due to updated_at)
                if thread.user_id:
                    await self.cache_repository.invalidate_user_threads(thread.user_id)

            logger.info(
                f"Database HIT: Created message for thread {thread_id} in database",
                props={
                    "thread_id": str(thread_id),
                    "organization_id": str(organization_id),
                    "source": "database",
                },
            )
            return message
        except Exception as e:
            logger.error(
                f"Error creating message for thread {thread_id}: {e}",
                props={"thread_id": str(thread_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})
            return None

    async def delete_thread(
        self, thread_id: UUID, organization_id: UUID, user_id: UUID = None
    ) -> bool:
        """Delete a chat thread."""
        try:
            # If user_id is not provided, we need to query the thread from the database
            if user_id is None:
                db = await self._get_db()
                thread_response = (
                    await db.table("chat_threads")
                    .select("user_id")
                    .eq("id", str(thread_id))
                    .execute()
                )

                if not thread_response.data or not thread_response.data[0]:
                    logger.error(
                        f"Thread not found: {thread_id}",
                        props={"thread_id": str(thread_id)},
                    )
                    return False

                user_id = thread_response.data[0]["user_id"]

            # Get thread to access user_id before deletion
            thread = await self.get_thread(
                thread_id, user_id=user_id, organization_id=organization_id
            )
            user_id = thread.user_id if thread else None

            db = await self._get_db()

            # Delete thread messages first
            await db.table("chat_messages").delete().eq("thread_id", str(thread_id)).eq(
                "organization_id", str(organization_id)
            ).execute()

            # Then delete the thread
            response = (
                await db.table("chat_threads")
                .delete()
                .eq("id", str(thread_id))
                .eq("organization_id", str(organization_id))
                .execute()
            )

            success = bool(response.data)

            if success:
                # Delete thread from cache
                await self.cache_repository.delete_thread(thread_id)

                # Invalidate user threads cache
                if user_id:
                    await self.cache_repository.invalidate_user_threads(user_id)

            logger.info(
                f"Database HIT: Deleted thread {thread_id} from database",
                props={
                    "thread_id": str(thread_id),
                    "organization_id": str(organization_id),
                    "source": "database",
                },
            )
            return success
        except Exception as e:
            logger.error(
                f"Error deleting thread {thread_id}: {e}",
                props={"thread_id": str(thread_id), "error": str(e)},
            )
            logger.error(traceback.format_exc(), props={})
            return False

    async def clear_all_chats(self, organization_id: UUID, user_id: UUID, agent_id: UUID) -> dict[str, int]:
        """Delete all chat threads and messages for a specific user and agent within an organization.

        Args:
            organization_id: The organization ID to clear chats for
            user_id: The user ID to clear chats for
            agent_id: Agent ID to filter chats by (required)

        Returns:
            dict with counts of deleted threads and messages
        """
        try:
            # Check if required parameters are available
            if not organization_id or not user_id or not agent_id:
                logger.warning(
                    "Cannot clear chats: Missing required parameters",
                    props={
                        "organization_id": str(organization_id) if organization_id else None,
                        "user_id": str(user_id) if user_id else None,
                        "agent_id": str(agent_id) if agent_id else None,
                    },
                )
                return {
                    "deleted_threads": 0,
                    "deleted_messages": 0,
                    "deleted_cache_keys": 0,
                    "error": "Missing required parameters",
                }
                
            await self.initialize()
            db = await self._get_db()

            # Check if there are any threads to delete
            thread_count_query = (
                db.table("chat_threads")
                .eq("organization_id", str(organization_id))
                .eq("user_id", str(user_id))
                .eq("agent_id", str(agent_id))
                .count()
            )
                
            thread_count_response = await thread_count_query.execute()
            thread_count = thread_count_response.count if hasattr(thread_count_response, "count") else 0
            
            if thread_count == 0:
                logger.info(
                    f"No chat threads found for user {user_id} with agent {agent_id} in organization {organization_id}",
                    props={
                        "organization_id": str(organization_id),
                        "user_id": str(user_id),
                        "agent_id": str(agent_id),
                    },
                )
                return {
                    "deleted_threads": 0,
                    "deleted_messages": 0,
                    "deleted_cache_keys": 0,
                }

            # First, get a count of messages that will be deleted
            # Get thread IDs for the user and agent in the organization
            threads_query = (
                db.table("chat_threads")
                .select("id")
                .eq("organization_id", str(organization_id))
                .eq("user_id", str(user_id))
                .eq("agent_id", str(agent_id))
            )
            threads_result = await threads_query.execute()
            thread_ids = [thread["id"] for thread in threads_result.data] if threads_result.data else []
            
            # Count messages associated with these threads
            deleted_messages_count = 0
            if thread_ids:
                # Use IN operator to count messages across all threads
                messages_count_query = (
                    db.table("chat_messages")
                    .in_("thread_id", thread_ids)
                    .count()
                )
                messages_count_result = await messages_count_query.execute()
                deleted_messages_count = messages_count_result.count if hasattr(messages_count_result, "count") else 0
            
            # Delete all threads for the user and agent in the organization
            # This will automatically delete associated messages due to cascade effect
            threads_response = (
                await db.table("chat_threads")
                .delete()
                .eq("organization_id", str(organization_id))
                .eq("user_id", str(user_id))
                .eq("agent_id", str(agent_id))
                .execute()
            )
            deleted_threads_count = len(threads_response.data) if threads_response.data else 0

            # Clear Redis cache for the user and agent combination
            cache_keys_deleted = 0
            if self.cache_repository:
                try:
                    # Invalidate user threads cache
                    await self.cache_repository.invalidate_user_threads(user_id)
                    
                    # Invalidate user chat history
                    await self.cache_repository.invalidate_user_chat_history(user_id)
                    
                    # Use Redis connection to delete keys matching specific patterns
                    async with redis_manager.connection("chat_service.clear_all_chats") as redis:
                        # Define all patterns to match relevant cache keys
                        patterns = [
                            # Message patterns
                            f"messages:{organization_id}:{agent_id}:*",
                            
                            # Checkpoint patterns
                            f"checkpoint:{organization_id}:{agent_id}:*",
                            
                            # Thread patterns
                            f"thread:{organization_id}:{agent_id}:*",
                            f"threads:{organization_id}:{user_id}:*",
                            
                            # Recent thread patterns
                            f"recent_thread:{organization_id}:{agent_id}:{user_id}",
                            
                            # Chat history patterns
                            f"chat_history:{organization_id}:{user_id}:*",
                            
                            # Agent-specific patterns
                            f"agent:{organization_id}:{agent_id}:*"
                        ]
                        
                        # Collect all keys matching any pattern
                        all_keys = []
                        for pattern in patterns:
                            pattern_keys = [key async for key in redis.scan_iter(match=pattern)]
                            all_keys.extend(pattern_keys)
                            
                        # Delete all matched keys if any found
                        if all_keys:
                            deleted = await redis.delete(*all_keys)
                            cache_keys_deleted = deleted
                            
                except redis.exceptions.RedisError as e:
                    # Handle Redis-specific errors
                    logger.exception(
                        f"Redis error while clearing cache",
                        props={
                            "organization_id": str(organization_id),
                            "user_id": str(user_id),
                            "agent_id": str(agent_id),
                            "error_type": type(e).__name__,
                        },
                    )
                    # Don't suppress Redis errors
                    raise
                    
                except Exception as e:
                    # Handle other unexpected errors
                    logger.exception(
                        "Error clearing Redis cache",
                        props={
                            "organization_id": str(organization_id),
                            "user_id": str(user_id),
                            "agent_id": str(agent_id),
                        },
                    )

            # Log the successful deletion
            log_message = f"Cleared all chats for user {user_id} with agent {agent_id} in organization {organization_id}"
            log_props = {
                "organization_id": str(organization_id),
                "user_id": str(user_id),
                "agent_id": str(agent_id),
                "deleted_threads": deleted_threads_count,
                "deleted_messages": deleted_messages_count,
                "deleted_cache_keys": cache_keys_deleted,
            }
            
            logger.info(log_message, props=log_props)

            return {
                "deleted_threads": deleted_threads_count,
                "deleted_messages": deleted_messages_count,
                "deleted_cache_keys": cache_keys_deleted,
            }

        except Exception as e:
            logger.error(
                f"Failed to clear all chats: {e}",
                props={
                    "organization_id": str(organization_id),
                    "error": str(e),
                },
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500,
                detail=f"Failed to clear all chats: {str(e)}",
            )


async def get_chat_service(
    organization_id: UUID, agent_id: Optional[UUID] = None
) -> ChatService:
    """
    Get or create a ChatService instance for the given organization and agent.

    Implements a singleton pattern to avoid creating multiple instances
    for the same organization_id and agent_id combination.

    Args:
        organization_id: The organization ID to get the service for
        agent_id: Optional agent ID to associate with the service

    Returns:
        An initialized ChatService instance
    """
    global _chat_service_instances, _init_lock

    key = f"{organization_id}:{agent_id if agent_id else 'None'}"

    # Fast path: return existing instance if available
    if key in _chat_service_instances:
        return _chat_service_instances[key]

    # Slow path: create new instance with lock to prevent race conditions
    async with _init_lock:
        # Check again in case another task created the instance while we were waiting
        if key in _chat_service_instances:
            return _chat_service_instances[key]

        # Create and initialize a new instance
        service = ChatService(organization_id=organization_id, agent_id=agent_id)
        await service.initialize()

        # Store in cache
        _chat_service_instances[key] = service

        return service


async def cleanup_chat_service():
    """Clean up all ChatService instances during application shutdown."""
    global _chat_service_instances

    logger.info(
        f"Cleaning up {len(_chat_service_instances)} ChatService instances", props={}
    )

    for key, service in _chat_service_instances.items():
        try:
            # Close any resources that need cleanup
            if service.streaming_service:
                await service.streaming_service.cleanup()
        except Exception as e:
            logger.error(
                f"Error during ChatService cleanup for {key}: {e}",
                props={"error": str(e)},
            )

    # Clear the instances dictionary
    _chat_service_instances.clear()
