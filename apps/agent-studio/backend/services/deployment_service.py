import asyncio
import secrets
import time
import traceback
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from fastapi import HTT<PERSON>Exception

from backend.core.database import get_supabase
from backend.core.logging_config import get_logger
from backend.models.non_plat import Deployment

logger = get_logger("deployment_service")

# Globals for singleton pattern
_deployment_service_instances = {}
_init_lock = asyncio.Lock()


class DeploymentService:
    """
    Service for handling deployment-related functionality.
    Provides methods for managing API keys, deployments, and other deployment-related tasks.
    """

    def __init__(self, organization_id: UUID = None):
        """Initialize the deployment service."""
        self.organization_id = organization_id
        self.db = None
        self._initialized = False
        logger.info(
            f"DeploymentService initialized for org: {organization_id}",
            props={
                "organization_id": str(organization_id) if organization_id else None
            },
        )

    async def initialize(self):
        """Complete async initialization steps."""
        if self._initialized:
            return

        start_time = time.time()
        try:
            self.db = await get_supabase()
            self._initialized = True

            duration = time.time() - start_time
            logger.info(
                f"DeploymentService initialization completed in {duration:.4f} seconds",
                props={
                    "organization_id": (
                        str(self.organization_id) if self.organization_id else None
                    )
                },
            )
        except Exception as e:
            logger.error(
                f"Failed to initialize DeploymentService: {e}", props={"error": str(e)}
            )
            logger.error(traceback.format_exc(), props={})
            raise

    async def _get_db(self):
        """Get the initialized database client. Initialize if needed."""
        if not self._initialized:
            await self.initialize()
        return self.db

    async def cleanup(self):
        """Release resources used by this service."""
        if not self._initialized:
            return

        try:
            logger.info(
                f"Cleaning up resources for DeploymentService with org_id: {self.organization_id}",
                props={
                    "organization_id": (
                        str(self.organization_id) if self.organization_id else None
                    )
                },
            )

            # Reset initialization flag
            self._initialized = False

            # Clear database connection
            self.db = None

            logger.info(
                f"DeploymentService resources released for org_id: {self.organization_id}",
                props={
                    "organization_id": (
                        str(self.organization_id) if self.organization_id else None
                    )
                },
            )
            return True
        except Exception as e:
            logger.error(
                f"Error cleaning up DeploymentService: {e}",
                props={
                    "error": str(e),
                    "organization_id": (
                        str(self.organization_id) if self.organization_id else None
                    ),
                },
            )
            return False

    def generate_api_key(self) -> str:
        """Generate a secure API key for deployment."""
        return f"thena_anon_{secrets.token_urlsafe(32)}"

    async def create_deployment(
        self,
        agent_id: UUID,
        team_id: str,
        allowed_origins: Optional[List[str]] = None,
        deployment_type: Optional[str] = None,
    ) -> Tuple[Deployment, str]:
        """
        Create a new deployment for an agent, including an HMAC secret key and allowed origins.

        Args:
            agent_id: The agent ID to create a deployment for
            team_id: The team ID associated with this deployment
            allowed_origins: Optional list of allowed origin domains for CORS.
            deployment_type: Optional deployment type

        Returns:
            A tuple containing the created Deployment object and the generated HMAC secret key.

        Raises:
            HTTPException: If the deployment could not be created
        """
        start_time = time.time()
        try:
            # Generate a unique API key
            api_key = self.generate_api_key()

            # Generate a unique HMAC secret key
            hmac_secret_key = secrets.token_hex(32)

            # Store the deployment in the database
            db = await self._get_db()
            data = {
                "agent_id": str(agent_id),
                "agent_key": api_key,
                "org_id": str(self.organization_id),
                "team_id": str(team_id),
                "hmac_secret_key": hmac_secret_key,
                "allowed_origins": allowed_origins,
                "deployment_type": deployment_type,
            }

            response = await db.table("deployments").insert(data).execute()

            if not response.data:
                logger.error(
                    "Failed to create deployment",
                    props={
                        "agent_id": str(agent_id),
                        "organization_id": str(self.organization_id),
                        "team_id": str(team_id),
                        "allowed_origins": allowed_origins,
                        "deployment_type": deployment_type,
                    },
                )
                raise HTTPException(
                    status_code=500, detail="Failed to create deployment"
                )

            # Get the created deployment data
            deployment_data = response.data[0]

            # Exclude the secret key when creating the Deployment object for general use
            deployment_model_data = {
                k: v for k, v in deployment_data.items() if k != "hmac_secret_key"
            }
            deployment = Deployment(**deployment_model_data)

            duration = time.time() - start_time
            logger.info(
                f"Deployment created in {duration:.4f} seconds",
                props={
                    "agent_id": str(agent_id),
                    "organization_id": str(self.organization_id),
                    "deployment_id": str(deployment.id),
                    "team_id": str(team_id),
                    "hmac_enabled": True,
                    "allowed_origins": allowed_origins,
                    "deployment_type": deployment_type,
                },
            )

            # Return the model object AND the secret key separately
            return deployment, hmac_secret_key
        except HTTPException:
            raise
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Error creating deployment after {duration:.4f} seconds: {str(e)}",
                props={
                    "agent_id": str(agent_id),
                    "organization_id": str(self.organization_id),
                    "team_id": str(team_id),
                    "allowed_origins": allowed_origins,
                    "deployment_type": deployment_type,
                    "error": str(e),
                },
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))

    async def list_deployments(
        self, agent_id: Optional[UUID] = None
    ) -> list[Deployment]:
        """
        List all deployments for an organization, optionally filtered by agent ID.

        Args:
            agent_id: Optional agent ID to filter deployments by

        Returns:
            A list of Deployment objects, sorted by created_at in descending order (newest first)
        """
        start_time = time.time()
        try:
            db = await self._get_db()
            # Explicitly select columns, excluding hmac_secret_key
            columns_to_select = "id, agent_id, agent_key, org_id, team_id, created_at, updated_at, allowed_origins, deployment_type"
            query = (
                db.table("deployments")
                .select(columns_to_select)
                .eq("org_id", str(self.organization_id))
            )

            if agent_id:
                query = query.eq("agent_id", str(agent_id))

            # Order by created_at in descending order to get the most recent deployments first
            query = query.order("created_at", desc=True)

            response = await query.execute()

            if not response.data:
                return []

            deployments = [Deployment(**item) for item in response.data]

            duration = time.time() - start_time
            logger.info(
                f"Retrieved {len(deployments)} deployments in {duration:.4f} seconds",
                props={
                    "organization_id": str(self.organization_id),
                    "agent_id": str(agent_id) if agent_id else None,
                },
            )

            return deployments
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Error listing deployments after {duration:.4f} seconds: {str(e)}",
                props={
                    "organization_id": str(self.organization_id),
                    "agent_id": str(agent_id) if agent_id else None,
                    "error": str(e),
                },
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))

    async def get_deployment(self, deployment_id: UUID) -> Deployment:
        """
        Get a specific deployment.

        Args:
            deployment_id: The ID of the deployment to get

        Returns:
            The Deployment object

        Raises:
            HTTPException: If the deployment was not found
        """
        start_time = time.time()
        try:
            db = await self._get_db()
            # Explicitly select columns, excluding hmac_secret_key
            columns_to_select = "id, agent_id, agent_key, org_id, team_id, created_at, updated_at, allowed_origins, deployment_type"

            response = (
                await db.table("deployments")
                .select(columns_to_select)
                .eq("id", str(deployment_id))
                .eq("org_id", str(self.organization_id))
                .execute()
            )

            if not response.data:
                logger.error(
                    f"Deployment not found",
                    props={
                        "deployment_id": str(deployment_id),
                        "organization_id": str(self.organization_id),
                    },
                )
                raise HTTPException(status_code=404, detail="Deployment not found")

            deployment_data = response.data[0]

            # Exclude the secret key when creating the Deployment object for general use
            deployment_model_data = {
                k: v for k, v in deployment_data.items() if k != "hmac_secret_key"
            }
            deployment = Deployment(**deployment_model_data)

            duration = time.time() - start_time
            logger.info(
                f"Retrieved deployment in {duration:.4f} seconds",
                props={
                    "deployment_id": str(deployment_id),
                    "organization_id": str(self.organization_id),
                },
            )

            return deployment
        except HTTPException:
            raise
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Error getting deployment after {duration:.4f} seconds: {str(e)}",
                props={
                    "deployment_id": str(deployment_id),
                    "organization_id": str(self.organization_id),
                    "error": str(e),
                },
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))

    async def delete_deployment(self, deployment_id: UUID) -> None:
        """
        Delete a deployment.

        Args:
            deployment_id: The ID of the deployment to delete

        Raises:
            HTTPException: If the deployment was not found or could not be deleted
        """
        start_time = time.time()
        try:
            db = await self._get_db()

            # Check if the deployment exists and belongs to this organization
            check_response = (
                await db.table("deployments")
                .select("*")
                .eq("id", str(deployment_id))
                .eq("org_id", str(self.organization_id))
                .execute()
            )

            if not check_response.data:
                logger.error(
                    f"Deployment not found for deletion",
                    props={
                        "deployment_id": str(deployment_id),
                        "organization_id": str(self.organization_id),
                    },
                )
                raise HTTPException(status_code=404, detail="Deployment not found")

            # Delete the deployment
            await db.table("deployments").delete().eq(
                "id", str(deployment_id)
            ).execute()

            duration = time.time() - start_time
            logger.info(
                f"Deleted deployment in {duration:.4f} seconds",
                props={
                    "deployment_id": str(deployment_id),
                    "organization_id": str(self.organization_id),
                },
            )

            return None
        except HTTPException:
            raise
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Error deleting deployment after {duration:.4f} seconds: {str(e)}",
                props={
                    "deployment_id": str(deployment_id),
                    "organization_id": str(self.organization_id),
                    "error": str(e),
                },
            )
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(status_code=500, detail=str(e))

    async def get_deployment_by_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """
        Get deployment information based on the API key, including the HMAC secret key if present.

        Args:
            api_key: The API key to look up

        Returns:
            A dictionary containing deployment information (including hmac_secret_key) or None if not found.
        """
        try:
            db = await self._get_db()
            response = (
                await db.table("deployments")
                .select("*, hmac_secret_key")
                .eq("agent_key", api_key)
                .execute()
            )

            if not response.data or len(response.data) == 0:
                return None

            return response.data[0]
        except Exception as e:
            logger.error(f"Error getting deployment by key: {str(e)}", props={})
            return None


async def get_allowed_origins_for_agent(agent_id: UUID) -> Optional[List[str]]:
    """Fetch the allowed_origins list for a specific agent ID from the deployments table."""
    try:
        # Use get_supabase() directly as this is a standalone utility function
        db = await get_supabase()
        response = (
            await db.table("deployments")
            .select("allowed_origins")
            .eq("agent_id", str(agent_id))
            .order(
                "created_at", desc=True
            )  # Order by created_at in descending order to get the latest deployment
            .limit(1)  # Limit to only 1 result (the latest)
            .execute()
        )

        if (
            response.data
            and len(response.data) > 0
            and "allowed_origins" in response.data[0]
        ):
            return response.data[0]["allowed_origins"]
        else:
            # Return None if agent not found or column is null
            return None
    except Exception as e:
        logger.error(
            f"Error fetching allowed origins for agent {agent_id}: {e}",
            props={"agent_id": str(agent_id), "error": str(e)},
        )
        logger.error(traceback.format_exc(), props={"agent_id": str(agent_id)})
        return None  # Return None on error


async def get_deployment_service(organization_id: UUID) -> DeploymentService:
    """
    Get or create a DeploymentService instance for the given organization.
    Implements a singleton pattern to avoid creating multiple instances for the same organization.

    Args:
        organization_id: The organization ID to get the service for

    Returns:
        An initialized DeploymentService instance
    """
    global _deployment_service_instances, _init_lock

    key = str(organization_id)

    # Fast path: return existing instance if available
    if key in _deployment_service_instances:
        # Ensure it's properly initialized
        if not _deployment_service_instances[key]._initialized:
            await _deployment_service_instances[key].initialize()
        return _deployment_service_instances[key]

    # Slow path: create new instance with lock to prevent race conditions
    async with _init_lock:
        # Check again in case another task created the instance while we were waiting
        if key in _deployment_service_instances:
            # Ensure it's properly initialized
            if not _deployment_service_instances[key]._initialized:
                await _deployment_service_instances[key].initialize()
            return _deployment_service_instances[key]

        # Create and initialize a new instance
        logger.info(
            f"Creating new DeploymentService instance for org_id: {organization_id}",
            props={"organization_id": str(organization_id)},
        )
        service = DeploymentService(organization_id=organization_id)
        await service.initialize()

        # Store in cache
        _deployment_service_instances[key] = service

        return service


async def cleanup_deployment_service():
    """Clean up all DeploymentService instances during application shutdown."""
    global _deployment_service_instances

    logger.info(
        f"Cleaning up {len(_deployment_service_instances)} DeploymentService instances",
        props={},
    )

    # Call cleanup on each instance
    for key, service in list(_deployment_service_instances.items()):
        try:
            await service.cleanup()
            logger.info(
                f"Successfully cleaned up DeploymentService for key: {key}", props={}
            )
        except Exception as e:
            logger.error(
                f"Error during DeploymentService cleanup for key {key}: {e}",
                props={"error": str(e)},
            )

    # Clear the instances dictionary
    _deployment_service_instances.clear()
