"""Database operation utilities for fetching information from databases."""

import asyncio
import time
from typing import Any, Dict, List, Optional

from ..core.database import get_supabase
from ..core.platform_database import get_platform_supabase
from ..utils.logger import get_logger

logger = get_logger("database_operation_service")

# Global state for database operation services
_platform_db_service_instance = None
_agent_studio_db_service_instance = None
_platform_init_lock = asyncio.Lock()
_agent_studio_init_lock = asyncio.Lock()


class PlatformDatabaseOperationService:
    """Service for platform database operations."""
    
    def __init__(self):
        """Initialize the Platform Database Operation Service."""
        self._initialized = False
        logger.info("PlatformDatabaseOperationService initialized", props={})
    
    async def initialize(self, auth_token: Optional[str] = None):
        """Asynchronously initialize the service."""
        start_time = time.time()
        try:
            # No specific initialization needed yet, but could be added in the future
            self._initialized = True
            duration = time.time() - start_time
            logger.info(f"PlatformDatabaseOperationService initialized in {duration:.4f} seconds", props={})
        except Exception as e:
            duration = time.time() - start_time
            logger.exception(
                f"Failed to initialize PlatformDatabaseOperationService after {duration:.4f} seconds: {str(e)}",
                props={},
            )
            raise
    
    async def get_numeric_organization_id(self, org_uid: str, auth_token: Optional[str] = None) -> Optional[int]:
        """Get the numeric organization ID from the organization UID.
        
        Args:
            org_uid: The organization UID (string format, like 'EKKGJZZL98')
            auth_token: Optional bearer token for authentication
            
        Returns:
            The numeric organization ID (int) if found, None otherwise
            
        Raises:
            Exception: If there's an error fetching the organization ID
        """
        try:
            # Get platform database client
            platform_db = await get_platform_supabase(auth_token=auth_token)
            
            # Query the organization table
            logger.info(f"Fetching numeric ID for organization with uid: {org_uid}", props={})
            org_response = await platform_db.from_("organization") \
                .select("id") \
                .eq("uid", org_uid) \
                .is_("deleted_at", "null") \
                .execute()
            
            if not org_response.data or len(org_response.data) == 0:
                logger.info(f"No organization found with uid {org_uid}", props={})
                return None
            
            # Get the numeric organization ID
            numeric_org_id = org_response.data[0]["id"]
            logger.info(f"Found numeric organization ID: {numeric_org_id} for uid: {org_uid}", props={})
            return numeric_org_id
            
        except Exception as e:
            logger.exception(f"Error getting numeric organization ID: {str(e)}", props={})
            raise Exception(f"Failed to fetch numeric organization ID: {str(e)}") from e
    
    async def get_numeric_team_id(self, team_uid: str, auth_token: Optional[str] = None) -> Optional[int]:
        """Get the numeric team ID from the team UID.
        
        Args:
            team_uid: The team UID (string format)
            auth_token: Optional bearer token for authentication
            
        Returns:
            The numeric team ID (int) if found, None otherwise
            
        Raises:
            Exception: If there's an error fetching the team ID
        """
        try:
            # Get platform database client
            platform_db = await get_platform_supabase(auth_token=auth_token)
            
            # Query the team table
            logger.info(f"Fetching numeric ID for team with uid: {team_uid}", props={})
            team_response = await platform_db.from_("team") \
                .select("id") \
                .eq("uid", team_uid) \
                .is_("deleted_at", "null") \
                .execute()
            
            if not team_response.data or len(team_response.data) == 0:
                logger.info(f"No team found with uid {team_uid}", props={})
                return None
            
            # Get the numeric team ID
            numeric_team_id = team_response.data[0]["id"]
            logger.info(f"Found numeric team ID: {numeric_team_id} for uid: {team_uid}", props={})
            return numeric_team_id
            
        except Exception as e:
            logger.exception(f"Error getting numeric team ID: {str(e)}", props={})
            raise Exception(f"Failed to fetch numeric team ID: {str(e)}") from e
    
    async def get_numeric_user_id(self, user_uid: str, auth_token: Optional[str] = None) -> Optional[int]:
        """Get the numeric user ID from the user UID.
        
        Args:
            user_uid: The user UID (string format)
            auth_token: Optional bearer token for authentication
            
        Returns:
            The numeric user ID (int) if found, None otherwise
            
        Raises:
            Exception: If there's an error fetching the user ID
        """
        try:
            # Get platform database client
            platform_db = await get_platform_supabase(auth_token=auth_token)
            
            # Query the user table
            logger.info("Fetching numeric ID for user", props=log_props)
            user_response = await platform_db.from_("user") \
                .select("id") \
                .eq("uid", user_uid) \
                .is_("deleted_at", "null") \
                .execute()
            
            if not user_response.data or len(user_response.data) == 0:
                logger.info("No user found with provided uid", props=log_props)
                return None
            
            # Get the numeric user ID
            numeric_user_id = user_response.data[0]["id"]
            logger.info("Found numeric user ID", props={**log_props, "numeric_user_id": numeric_user_id})
            return numeric_user_id
            
        except Exception as e:
            logger.exception(f"Error getting numeric user ID: {str(e)}", props={})
            raise Exception(f"Failed to fetch numeric user ID: {str(e)}") from e
    
    async def get_user_teams(self, user_id: str, auth_token: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all teams for a given user with team details.
        
        Args:
            user_id: The Thena platform user ID (uid)
            auth_token: Optional bearer token from API request for authentication
            
        Returns:
            List of dictionaries containing team_id, team_name, and team_identifier for each team
            the user is a member of. Format: [{"id": "team_uid", "name": "Team Name", "identifier": "team_identifier"}]
            
        Raises:
            Exception: If there's an error fetching team data
        """
        try:
            # Get platform database client
            platform_db = await get_platform_supabase(auth_token=auth_token)
            
            # Step 1: Get numeric user ID from uid
            logger.info(f"Fetching numeric ID for user with uid: {user_id}", props={})
            user_response = await platform_db.from_("user") \
                .select("id") \
                .eq("uid", user_id) \
                .is_("deleted_at", "null") \
                .execute()
            
            if not user_response.data or len(user_response.data) == 0:
                logger.info(f"No user found with uid {user_id}", props={})
                return []
            
            # Get the numeric user ID
            numeric_user_id = user_response.data[0]["id"]
            logger.info(f"Found numeric user ID: {numeric_user_id} for uid: {user_id}", props={})
            
            # Step 2: Get team IDs from team_member table
            logger.info(f"Fetching team memberships for user ID: {numeric_user_id}", props={})
            team_member_response = await platform_db.from_("team_member") \
                .select("team_id") \
                .eq("user_id", numeric_user_id) \
                .is_("deleted_at", "null") \
                .eq("is_active", True) \
                .execute()
            
            if not team_member_response.data or len(team_member_response.data) == 0:
                logger.info(f"No team memberships found for user ID: {numeric_user_id}", props={})
                return []
            
            # Extract team IDs
            team_ids = [tm["team_id"] for tm in team_member_response.data]
            logger.info(f"Found {len(team_ids)} team memberships: {team_ids}", props={})
            
            # Step 3: Get team details from team table
            logger.info(f"Fetching team details for team IDs: {team_ids}", props={})
            
            # Use a single query with .in_() to get all teams at once
            team_response = await platform_db.from_("team") \
                .select("uid, name, identifier") \
                .in_("id", team_ids) \
                .is_("deleted_at", None) \
                .eq("is_active", True) \
                .execute()
            
            # Format the response to match the expected output format
            team_results = [
                {"id": t["uid"], "name": t["name"], "identifier": t["identifier"]}
                for t in (team_response.data or [])
            ]
            
            # Sort results by name
            team_results.sort(key=lambda x: x["name"])
            
            if team_results:
                logger.info(f"Found {len(team_results)} active teams for user {user_id}", props={})
                return team_results
            
            logger.info(f"No active teams found for user {user_id}", props={})
            return []
            
        except Exception:
            logger.exception("Error fetching teams for user %s", user_id, props={})
            raise


class AgentStudioDatabaseOperationService:
    """Service for agent studio database operations."""
    
    def __init__(self):
        """Initialize the Agent Studio Database Operation Service."""
        self._initialized = False
        logger.info("AgentStudioDatabaseOperationService initialized", props={})
    
    async def initialize(self):
        """Asynchronously initialize the service."""
        start_time = time.time()
        try:
            # No specific initialization needed yet, but could be added in the future
            self._initialized = True
            duration = time.time() - start_time
            logger.info(f"AgentStudioDatabaseOperationService initialized in {duration:.4f} seconds", props={})
        except Exception as e:
            duration = time.time() - start_time
            logger.exception(
                f"Failed to initialize AgentStudioDatabaseOperationService after {duration:.4f} seconds: {str(e)}",
                props={},
            )
            raise
    
    async def get_thena_platform_user_id(self, user_id: str) -> Optional[str]:
        """Get the Thena platform user ID from the user_organizations table.
        
        Args:
            user_id: The user ID to look up
            
        Returns:
            The Thena platform user ID if found, None otherwise
            
        Raises:
            Exception: If there's an error fetching the user ID
        """
        try:
            # Get database client
            db = await get_supabase()
            
            # Query the user_organizations table to get thena_platform_user_id
            response = await db.from_("user_organizations") \
                .select("thena_platform_user_id") \
                .eq("user_id", user_id) \
                .execute()
            
            # Extract the Thena platform user ID if found
            if response.data and len(response.data) > 0:
                thena_user_id = response.data[0].get("thena_platform_user_id")
                logger.info(f"Found Thena platform user ID: {thena_user_id}", props={})
                return thena_user_id
            
            logger.info(f"No Thena platform user ID found for user {user_id}", props={})
            return None
        except Exception as e:
            logger.exception(f"Error getting Thena platform user ID: {str(e)}", props={})
            raise Exception(f"Failed to fetch Thena platform user ID: {str(e)}") from e


async def get_platform_db_service() -> PlatformDatabaseOperationService:
    """Get or create a PlatformDatabaseOperationService instance.
    
    Implements a singleton pattern to avoid creating multiple instances.
    
    Returns:
        An initialized PlatformDatabaseOperationService instance
    """
    global _platform_db_service_instance
    
    if _platform_db_service_instance is None:
        async with _platform_init_lock:
            if _platform_db_service_instance is None:
                logger.info("Creating new PlatformDatabaseOperationService instance", props={})
                _platform_db_service_instance = PlatformDatabaseOperationService()
                await _platform_db_service_instance.initialize()
    
    return _platform_db_service_instance


async def get_agent_studio_db_service() -> AgentStudioDatabaseOperationService:
    """Get or create an AgentStudioDatabaseOperationService instance.
    
    Implements a singleton pattern to avoid creating multiple instances.
    
    Returns:
        An initialized AgentStudioDatabaseOperationService instance
    """
    global _agent_studio_db_service_instance
    
    if _agent_studio_db_service_instance is None:
        async with _agent_studio_init_lock:
            if _agent_studio_db_service_instance is None:
                logger.info("Creating new AgentStudioDatabaseOperationService instance", props={})
                _agent_studio_db_service_instance = AgentStudioDatabaseOperationService()
                await _agent_studio_db_service_instance.initialize()
    
    return _agent_studio_db_service_instance


async def cleanup_db_services():
    """Clean up all database operation service instances during application shutdown."""
    global _platform_db_service_instance, _agent_studio_db_service_instance
    
    logger.info("Cleaning up database operation service instances", props={})
    _platform_db_service_instance = None
    _agent_studio_db_service_instance = None
    logger.info("Database operation service instances cleaned up", props={})
