import asyncio
import json
import time
import traceback
import uuid
from datetime import datetime
from typing import Any, Optional

import httpx
from fastapi import HTT<PERSON>Exception

from backend.core.config import get_config
from backend.core.database import get_supabase
from backend.core.logging_config import get_logger
from backend.core.redis_manager import RedisConnectionManager
from backend.models.webhook_handler import (
    InstallationPayload,
    TicketCommentBroadcast,
    TicketCommentWebhook,
    TicketCreatedBroadcast,
    TicketCreatedWebhook,
)
from backend.repositories.agent_cache_repository import AgentCacheRepository
from backend.services.chat_websocket_service import get_chat_websocket_service

config = get_config()

# Configure logging
webhook_logger = get_logger("webhook_service")

PLATFORM_API_BASE = config.WORKFLOWS_URL
EXA_API_KEY = config.EXA_API_KEY
MAX_CONCURRENT_WORKFLOWS = 3  # Maximum number of concurrent workflow creations
WORKFLOW_CREATION_TIMEOUT = 30  # Timeout in seconds for workflow creation

_webhook_service_instance = None
_init_lock = asyncio.Lock()


class WebhookHandlerService:
    _initialized = False
    _instance = None
    _lock = asyncio.Lock()

    def __init__(self) -> None:
        """Initialize the webhook handler service"""
        if not WebhookHandlerService._initialized:
            self._workflow_semaphore = asyncio.Semaphore(MAX_CONCURRENT_WORKFLOWS)
            self._db = None
            self._event_mapping = {}
            self._event_mapping_by_name = {}
            self._event_mapping_initialized = False
            self.redis_manager = RedisConnectionManager()
            WebhookHandlerService._initialized = True

    @classmethod
    async def get_instance(cls):
        """Get or create the singleton instance"""
        if not cls._instance:
            async with cls._lock:
                if not cls._instance:
                    cls._instance = cls()
                    await cls._instance._initialize()
        return cls._instance

    async def _initialize(self) -> None:
        """Initialize the service with required data"""
        try:
            webhook_logger.info("Initializing WebhookHandlerService...", props={})
            self._db = await get_supabase()
            webhook_logger.info("Database connection initialized", props={})
            webhook_logger.info(
                "WebhookHandlerService initialized successfully",
                props={},
            )
        except Exception as e:
            webhook_logger.error(
                f"Failed to initialize WebhookHandlerService: {str(e)}",
                props={},
            )
            raise

    @property
    def event_mapping(self):
        """Get event mapping dictionary"""
        return self._event_mapping

    @property
    def event_mapping_by_name(self):
        """Get event mapping by name dictionary"""
        return self._event_mapping_by_name

    async def _build_event_mapping(self, api_key: str) -> None:
        """Build mapping of event UIDs to names"""
        try:
            webhook_logger.info("Building event mapping...", props={})
            async with httpx.AsyncClient() as client:
                url = f"{PLATFORM_API_BASE}/workflows/registry/events"
                headers = {
                    "accept": "application/json",
                    "Content-Type": "application/json",
                    "x-api-key": api_key,
                }

                webhook_logger.info(f"Fetching events from: {url}")

                response = await client.get(url, headers=headers, timeout=30.0)

                webhook_logger.info(f"Response status: {response.status_code}")

                if response.status_code != 200:
                    webhook_logger.error(
                        f"Failed to fetch events. Status: {response.status_code}",
                    )
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Failed to fetch events: {response.text}",
                    )

                try:
                    events = response.json()
                except ValueError:
                    webhook_logger.error("Failed to parse events response")
                    raise HTTPException(
                        status_code=500,
                        detail="Invalid response format from event registry",
                    )

                # Clear existing mappings
                self._event_mapping.clear()
                self._event_mapping_by_name.clear()
                if isinstance(events, str):
                    webhook_logger.error("Received string response instead of JSON")
                    raise HTTPException(
                        status_code=500,
                        detail="Invalid response format from event registry",
                    )

                # Handle both array and object responses
                if isinstance(events, dict) and "results" in events:
                    events_list = events["results"]
                elif isinstance(events, list):
                    events_list = events
                else:
                    webhook_logger.error("Unexpected response format")
                    raise HTTPException(
                        status_code=500,
                        detail="Invalid response format from event registry",
                    )

                webhook_logger.info(f"Processing {len(events_list)} events")

                for event in events_list:
                    uid = event.get("uid")
                    name = event.get("name") or event.get(
                        "eventName",
                    )  # Try both name formats
                    if uid and name:
                        self._event_mapping[uid] = event
                        self._event_mapping_by_name[name] = uid
                        webhook_logger.info(f"Added event mapping: {name} -> {uid}")
                    else:
                        webhook_logger.warning("Skipping invalid event")

                self._event_mapping_initialized = True
                webhook_logger.info(
                    f"Built event mapping with {len(self._event_mapping)} events",
                )

        except httpx.TimeoutException:
            webhook_logger.error("Timeout while fetching events", props={})
            raise HTTPException(status_code=504, detail="Timeout while fetching events")
        except Exception as e:
            webhook_logger.error(f"Error building event mapping: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to build event mapping: {str(e)}",
            )

    async def _get_db(self):
        """Get the initialized database client. Initialize if needed."""
        if self._db is None:
            await self._initialize()
        return self._db

    async def cleanup(self) -> None:
        """Clean up resources used by the service."""
        try:
            self._db = None
            webhook_logger.info("WebhookHandlerService cleanup completed", props={})
        except Exception as e:
            webhook_logger.error(
                f"Error during WebhookHandlerService cleanup: {e}",
                props={},
            )
            webhook_logger.error(traceback.format_exc(), props={})

    async def handle_installation(self, payload: dict[str, Any]) -> None:
        """Handle installation webhook"""
        try:
            # Log raw payload
            webhook_logger.info("Processing installation webhook")

            # Check if this is an uninstall event
            if payload.get("event_type") == "app:uninstall":
                webhook_logger.info(
                    "Received uninstall event, skipping installation handling",
                )
                return

            # Transform webhook payload to match our schema
            transformed_payload = {
                "application_id": payload.get("application_id", "default_id"),
                "application_name": payload.get("application_name", "Thena App"),
                "bot_id": payload.get("bot_id", payload.get("created_by")),
                "bot_token": payload["bot_token"],
                "created_by": payload.get("created_by", payload.get("installed_by")),
                "team_ids": payload.get("team_ids", payload.get("team_id", [])),
                "organization_id": payload.get("organization_id"),
                "application_metadata": {
                    "template_id": payload.get("application_metadata", {}).get(
                        "template_id",
                        "default_template",
                    ),
                    "is_private_app": payload.get("application_metadata", {}).get(
                        "is_private_app",
                        False,
                    ),
                },
                "created_at": payload.get("created_at", "unknown"),
                "installation_id": payload.get("installation_id", "unknown"),
                "event_type": payload.get("event_type", "app:installation"),
                "configuration": payload.get(
                    "configuration",
                    {"required_settings": {}, "optional_settings": {}},
                ),
            }

            webhook_logger.info("Installation payload transformed")

            # Validate transformed payload
            installation_data = InstallationPayload(**transformed_payload)
            db = await self._get_db()
            org_result = (
                await db.table("organizations")
                .select("id")
                .eq("x-org-id", installation_data.organization_id)
                .execute()
            )
            if not org_result.data or len(org_result.data) == 0:
                raise ValueError(
                    f"Organization not found for x-org-id: {installation_data.organization_id}",
                )
            org_uuid = org_result.data[0]["id"]

            # Get template by template_id
            template_id = installation_data.application_metadata.template_id
            template_result = (
                await db.table("agent_templates")
                .select("*")
                .eq("id", template_id)
                .execute()
            )
            if not template_result.data:
                raise ValueError(f"Template not found for template_id: {template_id}")
            template = template_result.data[0]

            # Create agent data with appropriate template_id
            agent_data = {
                "organization_id": org_uuid,
                "name": installation_data.application_name,
                "description": template.get("description", ""),
                "template_id": template_id,
                "configuration": template.get("configuration", {}),
                "bot_sub": installation_data.bot_id,  # Store botSub in dedicated column
                "bot_token": installation_data.bot_token,  # Store botToken in dedicated column
                "metadata": {
                    **template.get("metadata", {}),
                    "installed_by_sub": installation_data.created_by,
                    "installed_by_email": "<EMAIL>",
                    "app_installed_for_teams": (
                        list(installation_data.team_ids)
                        if installation_data.team_ids
                        else []
                    ),  # Ensure it's a list
                    "is_private_app": installation_data.application_metadata.is_private_app,
                    "agent_id_platform": installation_data.application_id,  # Store platform ID for lookup
                },
                "avatar_url": template.get("avatar_url"),
                "status": "active",
                "team_id": (
                    "{" + ",".join(installation_data.team_ids) + "}"
                    if installation_data.team_ids
                    else "{}"
                ),  # Format as PostgreSQL array
            }

            # Create agent
            agent_result = await db.table("agents").insert(agent_data).execute()

            if not agent_result.data or len(agent_result.data) == 0:
                raise ValueError("Failed to create agent")
            agent_id = agent_result.data[0]["id"]

            # Copy functions for the new agent
            await self._copy_functions_for_agent(db, org_uuid, agent_id)

            # Get flows associated with this template from agent_template_flows
            template_flows_result = (
                await db.table("agent_template_flows")
                .select("*")
                .eq(
                    "agent_template_id",
                    installation_data.application_metadata.template_id,
                )
                .execute()
            )

            if template_flows_result.data:
                webhook_logger.info(
                    f"Found {len(template_flows_result.data)} template flows to link with agent {agent_id}",
                    props={},
                )
                agent_flows_data = [
                    {
                        "organization_id": org_uuid,
                        "agent_id": agent_id,
                        "flow_id": template_flow["flow_id"],
                        "is_enabled": False,
                        "configuration": {},
                        "auth": {},
                        "metadata": {
                            "installed_from_template": True,
                            "template_id": installation_data.application_metadata.template_id,
                        },
                    }
                    for template_flow in template_flows_result.data
                ]
                try:
                    agent_flows_result = (
                        await db.table("agent_flows").insert(agent_flows_data).execute()
                    )
                    webhook_logger.info(
                        f"Successfully created {len(agent_flows_result.data)} agent_flows entries",
                        props={},
                    )
                except Exception as e:
                    webhook_logger.error(
                        f"Failed to create agent_flows entries: {str(e)}",
                        props={},
                    )
            else:
                webhook_logger.info(
                    f"No template flows found for template {installation_data.application_metadata.template_id}",
                )

            # Create or update installation record using upsert
            installation_record = {
                "id": str(uuid.uuid4()),
                "organization_id": org_uuid,
                "api_key": installation_data.bot_token,
                "app_name": installation_data.application_name,
                "category": "thena platform",
                "installed_at": datetime.now().isoformat(),
                "agent_id": agent_id,
                "team_id": (
                    "{" + ",".join(installation_data.team_ids) + "}"
                    if installation_data.team_ids
                    else "{}"
                ),  # Format as PostgreSQL array
                "deleted_at": None,
            }

            exa_auth_record = {
                "id": str(uuid.uuid4()),
                "organization_id": org_uuid,
                "api_key": EXA_API_KEY,
                "app_name": "Exa Search",
                "category": "exa search",
                "installed_at": datetime.now().isoformat(),
                "agent_id": agent_id,
                "team_id": (
                    "{" + ",".join(installation_data.team_ids) + "}"
                    if installation_data.team_ids
                    else "{}"
                ),  # Format as PostgreSQL array
                "deleted_at": None,
            }

            webhook_logger.info(
                "Creating installation record",
                extra={"app_id": installation_data.application_id, "org_id": org_uuid},
            )

            # Use upsert operation - this will update if record exists, insert if it doesn't
            await db.table("agent_auth").upsert(installation_record).execute()
            await db.table("agent_auth").upsert(exa_auth_record).execute()

            webhook_logger.info(
                "Installation record + exa record created/updated successfully",
            )
            webhook_logger.info(
                "Successfully processed installation",
                extra={"org_id": installation_data.organization_id},
            )

            # Invalidate agent cache to ensure fresh data
            cache = AgentCacheRepository(organization_id=org_uuid)
            await cache.invalidate_agents_list()

            return {
                "status": "success",
                "message": "Agent created successfully",
                "agent_id": agent_id,
                "organization_id": org_uuid,
            }
        except Exception as e:
            webhook_logger.error(
                f"Failed to process installation webhook: {str(e)}",
                props={},
            )
            raise HTTPException(
                status_code=500,
                detail=f"Failed to process installation webhook: {str(e)}",
            )

    async def _copy_functions_for_agent(self, db, org_uuid: str, agent_id: str) -> None:
        """Copy existing functions for a new agent"""
        try:
            # First get a template of functions from an existing agent
            template_functions = (
                await db.table("functions").select("*").limit(1).execute()
            )

            if not template_functions.data or len(template_functions.data) == 0:
                webhook_logger.warning("No template functions found to copy")
                return

            # Get all unique functions
            all_functions = (
                await db.table("functions")
                .select(
                    "name",
                    "description",
                    "parameters",
                    "required_parameters",
                    "response_type",
                    "category",
                    "metadata",
                )
                .execute()
            )

            if not all_functions.data:
                webhook_logger.warning("No functions found to copy")
                return

            # Create new function entries for this agent
            new_functions = []
            seen_names = set()

            for func in all_functions.data:
                if func["name"] not in seen_names:
                    seen_names.add(func["name"])
                    new_func = {
                        "organization_id": org_uuid,
                        "agent_id": agent_id,
                        "name": func["name"],
                        "description": func["description"],
                        "parameters": func["parameters"],
                        "required_parameters": func["required_parameters"],
                        "response_type": func["response_type"],
                        "category": func["category"],
                        "metadata": func["metadata"],
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat(),
                    }
                    new_functions.append(new_func)

            webhook_logger.info(
                f"Creating {len(new_functions)} functions for agent",
                extra={"org_id": org_uuid, "agent_id": agent_id},
            )

            # Batch insert all functions
            if new_functions:
                result = await db.table("functions").insert(new_functions).execute()
                webhook_logger.info(
                    f"Successfully created {len(result.data)} functions",
                )

        except Exception:
            webhook_logger.error("Failed to copy functions for agent")
            raise

    async def handle_event(self, payload_json: dict[str, Any]) -> dict[str, Any]:
        """Handle event webhook payload.

        Args:
            payload_json: The parsed JSON payload from the webhook

        Returns:
            Dict containing status and response information
        """
        try:
            webhook_logger.info(
                "Processing event webhook",
                props={"event_type": payload_json.get("message", {}).get("eventType")},
            )

            # Process different event types
            event_type = payload_json.get("message", {}).get("eventType")

            if event_type == "ticket:created":
                return await self.handle_ticket_created_event(payload_json)
            elif event_type == "ticket:comment:added":
                return await self.handle_ticket_comment_event(payload_json)
            else:
                webhook_logger.info(f"Not a processed event type: {event_type}")
                return {
                    "status": "success",
                    "message": "Event type not processed",
                    "payload": payload_json,
                }

        except Exception as e:
            webhook_logger.error(f"Error processing event webhook: {str(e)}")
            webhook_logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    async def handle_ticket_created_event(
        self,
        payload_json: dict[str, Any],
    ) -> dict[str, Any]:
        """Handle ticket creation event with robust idempotency check."""
        event_id = payload_json.get("message", {}).get("eventId")
        event_type = payload_json.get("message", {}).get("eventType")
        webhook_logger.info(
            f"Received webhook event: eventId={event_id}, eventType={event_type}",
        )
        event_id_for_error_logging = event_id if event_id else "N/A"
        processed_ttl: Optional[int] = None
        lock_acquired = False

        if not event_id:
            webhook_logger.warning(
                "Webhook event received without an eventId. Proceeding without idempotency check.",
            )
            # Fall through to original logic unprotected by idempotency
        else:
            processed_key = f"idempotency:webhook:event:{event_id}:processed"
            processing_key = f"idempotency:webhook:event:{event_id}:processing"
            processing_ttl = 300  # 5 minutes
            processed_ttl = 3600  # 1 hour

            try:
                async with self.redis_manager.connection(
                    owner="webhook_idempotency_check",
                ) as redis:
                    # 1. Check if already processed
                    if await redis.exists(processed_key):
                        webhook_logger.info(
                            f"Event {event_id} already processed. Ignoring duplicate.",
                        )
                        return {
                            "status": "success",
                            "message": f"Duplicate eventId {event_id} ignored (already processed)",
                        }

                    # 2. Try to acquire processing lock atomically
                    lock_acquired = await redis.set(
                        processing_key,
                        time.time(),
                        ex=processing_ttl,
                        nx=True,
                    )
                    if lock_acquired:
                        webhook_logger.info(
                            f"Acquired processing lock for eventId={event_id}.",
                        )
                        # Proceed to business logic block below
                    else:
                        webhook_logger.info(
                            f"Event {event_id} is already being processed. Ignoring duplicate.",
                        )
                        return {
                            "status": "success",
                            "message": f"Duplicate eventId {event_id} ignored (already processing)",
                        }

            except ConnectionError as redis_conn_error:
                webhook_logger.exception(
                    "Redis ConnectionError during idempotency check for eventId %s. Proceeding (fail-open).",
                    event_id,
                    props={"eventId": event_id},
                )
                lock_acquired = False  # Failed to acquire lock
            except Exception as redis_error:
                webhook_logger.exception(
                    "Generic Redis error during idempotency check for eventId %s. Proceeding (fail-open).",
                    event_id,
                    props={"eventId": event_id},
                )
                lock_acquired = False  # Failed to acquire lock

            if not lock_acquired and event_id:
                webhook_logger.warning(
                    f"Proceeding with event {event_id} without acquired lock due to Redis error.",
                )
                # Fall through to business logic below - lock cleanup won't run in finally

        # ----- Business Logic Execution -----
        try:
            # --- Start Original Business Logic ---
            # 3. FULL PAYLOAD PARSING (Original Pydantic parsing)
            webhook_data = TicketCreatedWebhook(**payload_json)

            # Extract relevant information from the structured model
            event_data = webhook_data.message
            actor = event_data.actor
            actor_type = actor.type
            actor_id = actor.id

            payload = event_data.payload
            ticket_data = payload.ticket
            ticket_id = ticket_data.id
            status = ticket_data.statusName
            title = ticket_data.title

            thread_id = None
            if hasattr(ticket_data, "metadata"):
                metadata = ticket_data.metadata
                if isinstance(metadata, dict):
                    thread_id = metadata.get("thread_id")
                elif hasattr(metadata, "thread_id"):
                    thread_id = metadata.thread_id

            webhook_logger.info(
                f"Processing ticket creation: {ticket_id}, actor: {actor_type}:{actor_id}, thread_id from metadata: {thread_id}",
                props={
                    "ticket_id": ticket_id,
                    "actor_type": actor_type,
                    "actor_id": actor_id,
                    "thread_id": thread_id,
                },
            )

            db = await self._get_db()
            org_uuid = None
            widget_bot_subs = set()
            orgId = event_data.orgId

            if orgId:
                try:
                    org_result = (
                        await db.table("organizations")
                        .select("id")
                        .eq("x-org-id", orgId)
                        .limit(1)
                        .execute()
                    )
                    if org_result.data:
                        org_uuid = org_result.data[0]["id"]
                    else:
                        webhook_logger.error(
                            f"Organization not found for x-org-id: {orgId} in ticket:created handler",
                            props={"orgId": orgId, "ticket_id": ticket_id},
                        )
                except Exception as db_err:
                    webhook_logger.error(
                        f"DB error fetching org UUID in ticket:created handler: {db_err}",
                        props={"orgId": orgId, "ticket_id": ticket_id},
                    )

            widget_agent_ids = set()
            if org_uuid:
                try:
                    deployment_result = (
                        await db.table("deployments")
                        .select("agent_id")
                        .eq("org_id", org_uuid)
                        .execute()
                    )
                    if deployment_result.data:
                        widget_agent_ids = {
                            dep["agent_id"]
                            for dep in deployment_result.data
                            if dep.get("agent_id")
                        }
                except Exception as db_err:
                    webhook_logger.error(
                        f"DB error fetching deployments in ticket:created handler: {db_err}",
                        props={"org_uuid": org_uuid, "ticket_id": ticket_id},
                    )

            if widget_agent_ids:
                try:
                    agent_result = (
                        await db.table("agents")
                        .select("bot_sub")
                        .in_("id", list(widget_agent_ids))
                        .eq("status", "active")
                        .is_("deleted_at", None)
                        .not_.is_("bot_sub", None)
                        .execute()
                    )
                    if agent_result.data:
                        widget_bot_subs = {
                            agent["bot_sub"]
                            for agent in agent_result.data
                            if agent.get("bot_sub")
                        }
                except Exception as db_err:
                    webhook_logger.error(
                        f"DB error fetching agent bot_subs in ticket:created handler: {db_err}",
                        props={
                            "widget_agent_ids": list(widget_agent_ids),
                            "ticket_id": ticket_id,
                        },
                    )

            is_widget_bot_creator = False
            if actor_id and widget_bot_subs and actor_id in widget_bot_subs:
                is_widget_bot_creator = True

            if is_widget_bot_creator:
                webhook_logger.info(
                    f"Widget bot ticket created: {ticket_id} by actor {actor_id}",
                    props={
                        "ticket_id": ticket_id,
                        "thread_id": thread_id,
                        "actor_id": actor_id,
                    },
                )
                websocket_service = await get_chat_websocket_service()
                if not thread_id:
                    webhook_logger.info(
                        "Thread ID not found in webhook metadata, looking up from mapping",
                        props={
                            "ticket_id": ticket_id,
                            "ticket_id_type": type(ticket_id).__name__,
                        },
                    )
                    ticket_id_str = str(ticket_id)
                    thread_id = await websocket_service.get_thread_id_for_ticket(
                        ticket_id_str,
                    )
                    if thread_id:
                        webhook_logger.info(
                            f"Found thread ID from Redis mapping: {thread_id}",
                            props={"ticket_id": ticket_id_str, "thread_id": thread_id},
                        )
                    else:
                        webhook_logger.warning(
                            f"No thread ID found in Redis mapping for ticket {ticket_id_str}",
                            props={"ticket_id": ticket_id_str},
                        )

                message = TicketCreatedBroadcast(
                    ticket_id=ticket_id,
                    status=status,
                    title=title,
                    timestamp=time.time(),
                    thread_id=thread_id,
                )
                message_payload = message.model_dump()
                webhook_logger.info(
                    f"Broadcasting ticket creation message with payload: {json.dumps(message_payload)}",
                    props={
                        "message_type": message_payload.get("type"),
                        "ticket_id": ticket_id,
                    },
                )
                if thread_id:
                    webhook_logger.info(
                        f"Broadcasting ticket creation event to thread {thread_id}",
                        props={"ticket_id": ticket_id, "thread_id": thread_id},
                    )
                    await websocket_service.broadcast_to_thread(
                        thread_id,
                        "message",
                        message_payload,
                    )
                else:
                    webhook_logger.warning(
                        f"No thread_id could be determined for newly created ticket {ticket_id}. The ticket_created event will not be broadcast via WebSocket at this time.",
                        props={"ticket_id": ticket_id},
                    )
                webhook_logger.info(
                    f"Successfully processed ticket creation broadcast logic for ticket {ticket_id}",
                    props={"ticket_id": ticket_id, "thread_id": thread_id},
                )
            else:
                webhook_logger.info(
                    f"Skipping broadcast for ticket created by non-widget actor: {ticket_id} by actor {actor_id}",
                    props={
                        "ticket_id": ticket_id,
                        "actor_id": actor_id,
                        "actor_type": actor_type,
                    },
                )
            # --- End Original Business Logic ---

            # If business logic succeeded: Mark as processed (only if lock was acquired)
            if event_id and lock_acquired:
                try:
                    async with self.redis_manager.connection(
                        owner="webhook_idempotency_commit",
                    ) as redis:
                        await redis.setex(processed_key, processed_ttl, time.time())
                        webhook_logger.info(
                            f"Successfully marked event {event_id} as processed.",
                        )
                except Exception as commit_err:
                    webhook_logger.exception(
                        "CRITICAL: Failed to set processed key for eventId %s after successful execution.",
                        event_id,
                        props={"eventId": event_id},
                    )
                    # Continue to return success as business logic finished

            # Return original success response
            return {
                "status": "success",
                "payload": payload_json,
            }  # Kept original payload return

        except Exception as e:
            # Handle errors from the business logic processing
            webhook_logger.exception(
                "Error processing ticket creation (eventId: %s)",
                event_id_for_error_logging,
                props={"eventId": event_id_for_error_logging, "error": str(e)},
            )
            return {
                "status": "error",
                "message": f"Error processing ticket creation (eventId: {event_id_for_error_logging}): {str(e)}",
            }
        finally:
            # Clean up the processing lock ONLY if we had an event_id and acquired the lock
            if event_id and lock_acquired:
                try:
                    async with self.redis_manager.connection(
                        owner="webhook_idempotency_cleanup",
                    ) as redis:
                        await redis.delete(processing_key)
                        webhook_logger.info(
                            f"Cleaned up processing lock for eventId={event_id}.",
                        )
                except Exception as cleanup_err:
                    webhook_logger.exception(
                        "Failed to cleanup processing lock for eventId %s.",
                        event_id,
                        props={"eventId": event_id},
                    )

    async def handle_ticket_comment_event(
        self,
        payload_json: dict[str, Any],
    ) -> dict[str, Any]:
        """Handle ticket comment event with robust idempotency check."""
        event_id = payload_json.get("message", {}).get("eventId")
        event_type = payload_json.get("message", {}).get("eventType")  # For logging
        webhook_logger.info(
            f"Received webhook event: eventId={event_id}, eventType={event_type}",
        )
        event_id_for_error_logging = event_id if event_id else "N/A"
        processed_ttl: Optional[int] = None
        lock_acquired = False

        if not event_id:
            webhook_logger.warning(
                "Webhook event received without an eventId. Proceeding without idempotency check.",
            )
            # Fall through to original logic unprotected by idempotency
        else:
            processed_key = f"idempotency:webhook:event:{event_id}:processed"
            processing_key = f"idempotency:webhook:event:{event_id}:processing"
            processing_ttl = 300  # 5 minutes
            processed_ttl = 3600  # 1 hour

            try:
                async with self.redis_manager.connection(
                    owner="webhook_idempotency_check",
                ) as redis:
                    # 1. Check if already processed
                    if await redis.exists(processed_key):
                        webhook_logger.info(
                            f"Event {event_id} already processed. Ignoring duplicate.",
                        )
                        return {
                            "status": "success",
                            "message": f"Duplicate eventId {event_id} ignored (already processed)",
                        }

                    # 2. Try to acquire processing lock atomically
                    lock_acquired = await redis.set(
                        processing_key,
                        time.time(),
                        ex=processing_ttl,
                        nx=True,
                    )
                    if lock_acquired:
                        webhook_logger.info(
                            f"Acquired processing lock for eventId={event_id}.",
                        )
                        # Proceed to business logic block below
                    else:
                        webhook_logger.info(
                            f"Event {event_id} is already being processed. Ignoring duplicate.",
                        )
                        return {
                            "status": "success",
                            "message": f"Duplicate eventId {event_id} ignored (already processing)",
                        }

            except ConnectionError as redis_conn_error:
                webhook_logger.exception(
                    "Redis ConnectionError during idempotency check for eventId %s. Proceeding (fail-open).",
                    event_id,
                    props={"eventId": event_id},
                )
                lock_acquired = False  # Failed to acquire lock
            except Exception as redis_error:
                webhook_logger.exception(
                    "Generic Redis error during idempotency check for eventId %s. Proceeding (fail-open).",
                    event_id,
                    props={"eventId": event_id},
                )
                lock_acquired = False  # Failed to acquire lock

            if not lock_acquired and event_id:
                webhook_logger.warning(
                    f"Proceeding with event {event_id} without acquired lock due to Redis error.",
                )
                # Fall through to business logic below - lock cleanup won't run in finally

        # ----- Business Logic Execution -----
        try:
            # --- Start Original Business Logic ---
            # 3. FULL PAYLOAD PARSING (Original Pydantic parsing)
            webhook_data = TicketCommentWebhook(**payload_json)

            # 4. ORIGINAL LOGIC (Untouched)
            # Extract relevant information from the structured model
            event_data = webhook_data.message
            payload = event_data.payload
            comment_data = payload.comment
            ticket_data = payload.ticket

            ticket_id = ticket_data.id
            webhook_logger.debug(f"Extracted ticket ID: {ticket_id}")

            author = comment_data.author
            comment_author_id = author.id
            author_email = author.email
            webhook_logger.debug(
                f"Extracted author ID: {comment_author_id}, email: {author_email}",
            )

            content = comment_data.content
            comment_visibility = comment_data.commentVisibility
            webhook_logger.debug(
                f"Extracted content preview: {content[:100] if content else 'No content'}, Visibility: {comment_visibility}",
            )

            orgId = event_data.orgId
            db = await self._get_db()
            org_uuid = None
            widget_bot_subs = set()
            is_widget_bot_comment = False

            if orgId:
                try:
                    org_result = (
                        await db.table("organizations")
                        .select("id")
                        .eq("x-org-id", orgId)
                        .limit(1)
                        .execute()
                    )
                    if org_result.data:
                        org_uuid = org_result.data[0]["id"]
                    else:
                        webhook_logger.error(
                            f"Organization not found for x-org-id: {orgId}",
                            props={"orgId": orgId},
                        )
                except Exception as db_err:
                    webhook_logger.error(
                        f"Database error fetching organization UUID for orgId {orgId}: {db_err}",
                        props={"orgId": orgId},
                    )
            else:
                webhook_logger.warning(
                    "orgId not found in webhook payload.",
                    props={
                        "ticket_id": ticket_id if "ticket_id" in locals() else "N/A",
                    },
                )

            widget_agent_ids = set()
            if org_uuid:
                try:
                    deployment_result = (
                        await db.table("deployments")
                        .select("agent_id")
                        .eq("org_id", org_uuid)
                        .execute()
                    )
                    if deployment_result.data:
                        widget_agent_ids = {
                            dep["agent_id"]
                            for dep in deployment_result.data
                            if dep.get("agent_id")
                        }
                    else:
                        webhook_logger.warning(
                            f"No deployments found for organization {org_uuid}",
                            props={"organization_id": org_uuid},
                        )
                except Exception as db_err:
                    webhook_logger.error(
                        f"Database error fetching deployments for organization {org_uuid}: {db_err}",
                        props={"organization_id": org_uuid},
                    )

                if widget_agent_ids:
                    try:
                        agent_result = (
                            await db.table("agents")
                            .select("bot_sub")
                            .in_("id", list(widget_agent_ids))
                            .eq("status", "active")
                            .is_("deleted_at", None)
                            .not_.is_("bot_sub", None)
                            .execute()
                        )
                        if agent_result.data:
                            widget_bot_subs = {
                                agent["bot_sub"]
                                for agent in agent_result.data
                                if agent.get("bot_sub")
                            }
                        else:
                            webhook_logger.warning(
                                f"No active, non-deleted agents with bot_sub found for the deployed agent IDs: {widget_agent_ids}",
                                props={
                                    "organization_id": org_uuid,
                                    "deployed_agent_ids": list(widget_agent_ids),
                                },
                            )
                    except Exception as db_err:
                        webhook_logger.error(
                            f"Database error fetching agent bot_subs for deployed agents: {db_err}",
                            props={
                                "organization_id": org_uuid,
                                "deployed_agent_ids": list(widget_agent_ids),
                            },
                        )

            webhook_logger.debug(
                f"Checking comment author: ID='{comment_author_id}' (Type: {type(comment_author_id)}), Found bot subs: {widget_bot_subs} (Type: {type(widget_bot_subs)})",
                props={
                    "comment_author_id": comment_author_id,
                    "widget_bot_subs": (
                        list(widget_bot_subs) if widget_bot_subs else []
                    ),
                    "ticket_id": ticket_id,
                },
            )
            if (
                comment_author_id
                and widget_bot_subs
                and comment_author_id in widget_bot_subs
            ):
                is_widget_bot_comment = True
                webhook_logger.debug(
                    "Comment author IS identified as a widget bot.",
                    props={"ticket_id": ticket_id},
                )
            else:
                webhook_logger.debug(
                    "Comment author IS NOT identified as a widget bot.",
                    props={"ticket_id": ticket_id},
                )

            comment_type_value = comment_data.commentType

            webhook_logger.debug(
                f"Evaluating broadcast: ticket_id='{ticket_id}', author='{comment_author_id}', is_widget_bot={is_widget_bot_comment}, visibility='{comment_visibility}', comment_type='{comment_type_value}'",
                props={
                    "ticket_id": ticket_id,
                    "comment_author_id": comment_author_id,
                    "is_widget_bot_comment": is_widget_bot_comment,
                    "comment_visibility": comment_visibility,
                    "comment_id": comment_data.id,
                    "comment_type": comment_type_value,
                },
            )
            if is_widget_bot_comment:
                webhook_logger.info(
                    f"Skipping broadcast for comment from widget bot (author_id: {comment_author_id}, comment_id: {comment_data.id}) on ticket {ticket_id}",
                    props={
                        "ticket_id": ticket_id,
                        "author_id": comment_author_id,
                        "comment_id": comment_data.id,
                    },
                )
            elif comment_visibility == "private":
                webhook_logger.info(
                    f"Skipping broadcast for private comment (author_id: {comment_author_id}, comment_id: {comment_data.id}) on ticket {ticket_id}",
                    props={
                        "ticket_id": ticket_id,
                        "author_id": comment_author_id,
                        "comment_id": comment_data.id,
                        "comment_visibility": comment_visibility,
                    },
                )
            elif comment_type_value == "note":
                webhook_logger.info(
                    f"Skipping broadcast for comment type 'note' (author_id: {comment_author_id}, comment_id: {comment_data.id}) on ticket {ticket_id}",
                    props={
                        "ticket_id": ticket_id,
                        "author_id": comment_author_id,
                        "comment_id": comment_data.id,
                        "comment_type": comment_type_value,
                    },
                )
            elif ticket_id:  # Implies not a widget bot comment AND comment is not private (or visibility is None/public) AND comment_type is not 'note'
                webhook_logger.info(
                    f"Processing public, non-widget-bot, non-note comment for broadcast. Ticket: {ticket_id}, Author: {comment_author_id}, Comment: {comment_data.id}",
                    props={
                        "ticket_id": ticket_id,
                        "author_id": comment_author_id,
                        "author_email": author_email,
                        "comment_id": comment_data.id,
                    },
                )
                websocket_service = await get_chat_websocket_service()
                ticket_id_str = str(ticket_id)
                thread_id = await websocket_service.get_thread_id_for_ticket(
                    ticket_id_str,
                )
                if thread_id:
                    webhook_logger.info(
                        f"Found thread ID {thread_id} for ticket {ticket_id}. Broadcasting platform comment.",
                        props={"ticket_id": ticket_id_str, "thread_id": thread_id},
                    )
                    message = TicketCommentBroadcast(
                        message=content,
                        ticket_id=ticket_id,
                        comment_id=comment_data.id,
                        timestamp=time.time(),
                        author_name=comment_data.author.name,
                        author_id=comment_data.author.id,
                    )
                    message_payload = message.model_dump()
                    webhook_logger.info(
                        f"Broadcasting platform comment message with payload: {json.dumps(message_payload)}",
                        props={
                            "message_type": message_payload.get("type"),
                            "ticket_id": ticket_id_str,
                        },
                    )
                    await websocket_service.broadcast_to_thread(
                        thread_id,
                        "message",
                        message_payload,
                    )
                    webhook_logger.info(
                        f"Successfully broadcasted platform comment to thread {thread_id}",
                        props={"ticket_id": ticket_id_str, "thread_id": thread_id},
                    )
                else:
                    webhook_logger.warning(
                        f"No thread ID found for ticket {ticket_id}. Cannot broadcast platform comment.",
                        props={"ticket_id": ticket_id_str},
                    )
            else:  # Covers cases like missing ticket_id after bot and privacy checks
                webhook_logger.warning(
                    f"Skipping broadcast. Ticket ID is missing or condition not met. Ticket: '{ticket_id}', Author: '{comment_author_id}', WidgetBot: {is_widget_bot_comment}, Visibility: '{comment_visibility}', CommentType: '{comment_type_value}'",
                    props={
                        "ticket_id": ticket_id,
                        "comment_author_id": comment_author_id,
                        "is_widget_bot_comment_flag": is_widget_bot_comment,
                        "comment_visibility": comment_visibility,
                        "comment_id": comment_data.id,
                        "comment_type": comment_type_value,
                        "orgId_found": bool(orgId),
                        "org_uuid_found": bool(org_uuid),  # org_uuid is defined earlier
                        "widget_bot_subs_found": bool(
                            widget_bot_subs,
                        ),  # widget_bot_subs is defined earlier
                    },
                )
            # --- End Original Business Logic ---

            # If business logic succeeded: Mark as processed (only if lock was acquired)
            if event_id and lock_acquired:
                try:
                    async with self.redis_manager.connection(
                        owner="webhook_idempotency_commit",
                    ) as redis:
                        await redis.setex(processed_key, processed_ttl, time.time())
                        webhook_logger.info(
                            f"Successfully marked event {event_id} as processed.",
                        )
                except Exception as commit_err:
                    webhook_logger.exception(
                        "CRITICAL: Failed to set processed key for eventId %s after successful execution.",
                        event_id,
                        props={"eventId": event_id},
                    )
                    # Continue to return success as business logic finished

            # Return original success response
            return {"status": "success", "payload": payload_json}

        except Exception as e:
            # Handle errors from the business logic processing
            webhook_logger.exception(
                "Error processing ticket comment (eventId: %s)",
                event_id_for_error_logging,
                props={"eventId": event_id_for_error_logging, "error": str(e)},
            )
            return {
                "status": "error",
                "message": f"Error processing ticket comment (eventId: {event_id_for_error_logging}): {str(e)}",
            }
        finally:
            # Clean up the processing lock ONLY if we had an event_id and acquired the lock
            if event_id and lock_acquired:
                try:
                    async with self.redis_manager.connection(
                        owner="webhook_idempotency_cleanup",
                    ) as redis:
                        await redis.delete(processing_key)
                        webhook_logger.info(
                            f"Cleaned up processing lock for eventId={event_id}.",
                        )
                except Exception as cleanup_err:
                    webhook_logger.exception(
                        "Failed to cleanup processing lock for eventId %s.",
                        event_id,
                        props={"eventId": event_id},
                    )


async def get_webhook_handler_service() -> WebhookHandlerService:
    """Get an initialized instance of WebhookHandlerService.

    Returns:
        Initialized WebhookHandlerService instance
    """
    return await WebhookHandlerService.get_instance()


async def cleanup_webhook_handler_service() -> None:
    """Clean up the WebhookHandlerService instance.
    This should be called during application shutdown.
    """
    instance = await get_webhook_handler_service()
    if instance:
        webhook_logger.info("Cleaning up WebhookHandlerService instance", props={})
        await instance.cleanup()
        WebhookHandlerService._instance = None
        WebhookHandlerService._initialized = False
        webhook_logger.info("WebhookHandlerService cleanup completed", props={})