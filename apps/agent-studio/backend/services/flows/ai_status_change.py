import asyncio
import json
import traceback
from typing import Any
from uuid import UUID

import httpx
from langchain_core.messages import HumanMessage
from langgraph.graph import END, StateGraph

from ...core.config import get_config
from ...core.database import get_supabase
from ...core.thena_ext_apis import ExternalAPIsClient
from ...models.flow_models import StatusState, TicketComment, TicketStatus
from ...prompt_library import format_prompt, load_prompt
from ...utils.audit_logger import (
    AuditLogEntityType,
    AuditLogOp,
    AuditLogVisibility,
    record_audit_log,
)
from ...utils.llm_utils import call_openai_api_directly
from ...utils.logger import get_logger

# Get settings for API URLs
settings = get_config()
logger = get_logger("ai_status_change_flow")
THENA_PLATFORM_URL = settings.THENA_PLATFORM_URL

# Create an instance of the ExternalAPIsClient
external_apis_client = ExternalAPIsClient()


def format_agent_config_to_system_message(
    agent_details: dict[str, Any],
    config: dict[str, Any],
) -> str:
    """
    Format agent configuration into a system message for LLM prompts.
    
    This is a simplified version of the chat_state._format_agent_config_to_system_message function
    adapted specifically for the AI status change flow.
    
    Args:
        agent_details: Details about the agent (name, description, etc.)
        config: Configuration parameters for the agent
        
    Returns:
        str: A formatted system message string
    """
    # Start with basic agent identity - use agent name from agent_details
    agent_name = agent_details.get("name", "Assistant")
    
    # Extract key configuration parameters from config
    role = config.get("role", "")
    goal = config.get("goal", "")
    backstory = config.get("backstory", "")
    tone = config.get("tone", "")
    personality_traits = config.get("personality_traits", [])
    core_capabilities = config.get("core_capabilities", [])
    knowledge_domains = config.get("knowledge_domains", [])
    
    # Build the system message
    system_parts = []
    
    # Basic identity - use agent name from agent_details and role from config
    identity = f"You are {agent_name}"
    if role:
        identity += f", a {role}"
    system_parts.append(identity)
    
    # Goal and backstory from config
    if goal:
        system_parts.append(f"Your goal is to: {goal}")
    if backstory:
        system_parts.append(f"Backstory: {backstory}")
    
    # Personality from config
    personality_section = []
    if personality_traits:
        traits = ", ".join(personality_traits)
        personality_section.append(f"Personality traits: {traits}")
    if tone:
        personality_section.append(f"Tone: {tone}")
    
    if personality_section:
        system_parts.append("Your personality is defined by:\n" + "\n".join(personality_section))
    
    # Capabilities from config
    capabilities_section = []
    if core_capabilities:
        capabilities = "\n- " + "\n- ".join(core_capabilities)
        capabilities_section.append(f"Core capabilities: {capabilities}")
    if knowledge_domains:
        domains = "\n- " + "\n- ".join(knowledge_domains)
        capabilities_section.append(f"Knowledge domains: {domains}")
    
    if capabilities_section:
        system_parts.append("\n".join(capabilities_section))
    
    # Combine all parts with double newlines
    return "\n\n".join(system_parts)


async def get_available_statuses(
    team_id: str, api_key: str, x_org_id: str
) -> list[TicketStatus]:
    """Get available ticket statuses for the team using the ExternalAPIsClient."""
    try:
        # Use the external_apis_client to get the statuses
        status_data = await external_apis_client.get_available_statuses(
            team_id=team_id,
            api_key=api_key,
            x_org_id=x_org_id,
            thena_platform_url=THENA_PLATFORM_URL,
        )
        
        # Convert the raw data to TicketStatus objects
        return [TicketStatus(**status) for status in status_data]
    except httpx.HTTPError as e:
        # Handle HTTP-related errors
        logger.exception(f"HTTP error getting available statuses: {str(e)}")
        raise
    except ValueError as e:
        # Handle value errors (often from API response parsing)
        logger.exception(f"Value error processing available statuses: {str(e)}")
        raise
    except Exception as e:
        # Handle any other unexpected errors
        logger.exception(f"Unexpected error getting available statuses: {str(e)}")
        raise


async def get_ticket_comments(
    ticket_id: str, api_key: str, x_org_id: str, limit: int = 20, page: int = 0, visibility: str = "public"
) -> list[TicketComment]:
    """Get comments for a ticket using the ExternalAPIsClient."""
    try:
        # Use the external_apis_client to get the comments
        comments_data = await external_apis_client.get_ticket_comments(
            ticket_id=ticket_id,
            api_key=api_key,
            x_org_id=x_org_id,
            thena_platform_url=THENA_PLATFORM_URL,
            limit=limit,
            page=page,
            visibility=visibility,
        )
        
        # Convert the raw data to TicketComment objects
        return [TicketComment(**comment) for comment in comments_data]
    except httpx.HTTPError as e:
        # Handle HTTP-related errors
        logger.exception(f"HTTP error getting ticket comments: {str(e)}")
        raise
    except ValueError as e:
        # Handle value errors (often from API response parsing)
        logger.exception(f"Value error processing ticket comments: {str(e)}")
        raise
    except Exception as e:
        # Handle any other unexpected errors
        logger.exception(f"Unexpected error getting ticket comments: {str(e)}")
        raise

async def get_comment_threads(
    comment_id: str, api_key: str, x_org_id: str
) -> list[dict[str, Any]]:
    """Get all comments in a thread for a specific comment using the ExternalAPIsClient."""
    try:
        # Use the external_apis_client to get the comment threads
        return await external_apis_client.get_comment_threads(
            comment_id=comment_id,
            api_key=api_key,
            x_org_id=x_org_id,
            thena_platform_url=THENA_PLATFORM_URL,
        )
    except httpx.HTTPError as e:
        # Handle HTTP-related errors
        logger.exception(f"HTTP error getting comment threads: {str(e)}")
        raise
    except ValueError as e:
        # Handle value errors (often from API response parsing)
        logger.exception(f"Value error processing comment threads: {str(e)}")
        raise
    except Exception as e:
        # Handle any other unexpected errors
        logger.exception(f"Unexpected error getting comment threads: {str(e)}")
        raise


async def get_ticket_info_node(state: StatusState) -> StatusState:
    """Fetch ticket information, comments, and available statuses."""
    try:
        db = await get_supabase()

        agent_result = (
            await db.table("agents")
            .select("name", "bot_sub","bot_token","avatar_url")
            .eq("id", state["agent_id"])
            .execute()
        )
        state["agent_data"] = agent_result.data[0]

        api_key = agent_result.data[0]["bot_token"]
        bot_sub = agent_result.data[0]["bot_sub"]
        agent_name = agent_result.data[0]["name"]

        org_result = (
            await db.table("organizations")
            .select("x-org-id")
            .eq("id", state["org_id"])
            .execute()
        )
        if not org_result.data or not org_result.data[0].get("x-org-id"):
            error_msg = "Organization not found"
            logger.error(error_msg)
            raise ValueError(error_msg)
        x_org_id = org_result.data[0]["x-org-id"]
        state["x_org_id"] = x_org_id

        # Get user instruction
        configuration = (
            await db.table("agent_flows")
            .select("configuration")
            .eq("agent_id", state["agent_id"])
            .eq("flow_id", state["flow_id"])
            .execute()
        )
        state["configuration"] = configuration.data[0]["configuration"]

        async with httpx.AsyncClient() as client:
            # Get ticket data
            url = f"{THENA_PLATFORM_URL}/v1/tickets/{state['ticket_id']}"
            headers = {"x-api-key": api_key, "x-org-id": x_org_id}

            try:
                logger.info(f"Fetching ticket {state['ticket_id']}, org_id: {x_org_id}")
                response = await client.get(url, headers=headers)
                response.raise_for_status()
                logger.info(f"Get ticket info response: {response.json()}")
                ticket_data = response.json()["data"]
                state["ticket_data"] = ticket_data


                # Fetch available statuses and comments in parallel using asyncio.gather
                logger.info(f"Fetching statuses and comments in parallel for ticket {state['ticket_id']}")
                try:
                    # Use asyncio.gather to make API calls in parallel
                    available_statuses, public_comments, private_comments = await asyncio.gather(
                        get_available_statuses(ticket_data["teamId"], api_key, x_org_id),
                        get_ticket_comments(state["ticket_id"], api_key, x_org_id, visibility="public"),
                        get_ticket_comments(state["ticket_id"], api_key, x_org_id, visibility="private"),
                    )
                    
                    # Store results in state
                    state["available_statuses"] = available_statuses
                    state["public_comments"] = public_comments
                    state["private_comments"] = private_comments
                    
                    # Log results
                    logger.info(f"Available statuses: {state['available_statuses']}")
                    logger.info(f"Public comments: {state['public_comments']}")
                    logger.info(f"Private comments: {state['private_comments']}")
                except httpx.HTTPStatusError as e:
                    # Handle HTTP status errors (4xx, 5xx responses)
                    error_msg = f"HTTP status error when fetching data: {e.response.status_code} - {str(e)}"
                    logger.exception(error_msg)
                    state["error"] = error_msg
                    return state
                except httpx.RequestError as e:
                    # Handle request errors (connection errors, timeouts, etc.)
                    error_msg = f"HTTP request error when fetching data: {str(e)}"
                    logger.exception(error_msg)
                    state["error"] = error_msg
                    return state
                except ValueError as e:
                    # Handle value errors (often from API response parsing)
                    error_msg = f"Value error when processing API response: {str(e)}"
                    logger.exception(error_msg)
                    state["error"] = error_msg
                    return state
                except Exception as e:
                    # Catch any other unexpected exceptions
                    error_msg = f"Unexpected error fetching data in parallel: {str(e)}"
                    logger.exception(error_msg)  # This logs the full traceback
                    state["error"] = error_msg
                    return state

                public_all_comments = []

                # Only fetch thread comments for the latest comment if it exists and has replies
                if state["public_comments"] and len(state["public_comments"]) > 0:
                    latest_comment = state["public_comments"][-1]
                    if latest_comment.metadata and latest_comment.metadata.get("replies"):
                        try:
                            logger.info(f"Fetching thread comments for latest comment {latest_comment.id}")
                            thread_comments = await get_comment_threads(
                                latest_comment.id, api_key, x_org_id
                            )
                            logger.info(f"Thread comments: {thread_comments}")
                            # Convert thread comments to TicketComment objects
                            thread_comments = [TicketComment(**c) for c in thread_comments]
                            public_all_comments.extend(thread_comments)
                        except Exception as e:
                            logger.exception(
                                f"Error fetching thread comments for latest comment {latest_comment.id}"
                            )
                    else:
                        public_all_comments.extend(state["public_comments"])
        

                private_all_comments = []

                # Only fetch thread comments for the latest comment if it exists and has replies
                if state["private_comments"] and len(state["private_comments"]) > 0:
                    latest_comment = state["private_comments"][-1]
                    if latest_comment.metadata and latest_comment.metadata.get("replies"):
                        try:
                            logger.info(f"Fetching thread comments for latest comment {latest_comment.id}")
                            thread_comments = await get_comment_threads(
                                latest_comment.id, api_key, x_org_id
                            )
                            logger.info(f"Thread comments: {thread_comments}")
                            # Convert thread comments to TicketComment objects
                            thread_comments = [TicketComment(**c) for c in thread_comments]
                            private_all_comments.extend(thread_comments)
                        except Exception as e:
                            logger.exception(
                                f"Error fetching thread comments for latest comment {latest_comment.id}"
                            )
                    else:
                        private_all_comments.extend(state["private_comments"])

                # Update state with all comments (parent + thread)
                state["public_comment_thread"] = public_all_comments
                state["private_comment_thread"] = private_all_comments
                
                # Get user mapping for comments in parallel
                logger.info(f"Fetching author mappings in parallel for ticket {state['ticket_id']}")
                try:
                    # Use asyncio.gather to make API calls in parallel
                    public_author_mapping, private_author_mapping = await asyncio.gather(
                        external_apis_client.get_user_mapping_per_comment(
                            comment_thread=public_all_comments, 
                            x_org_id=x_org_id, 
                            team_id=ticket_data["teamId"], 
                            api_key=api_key,
                            thena_platform_url=THENA_PLATFORM_URL
                        ),
                        external_apis_client.get_user_mapping_per_comment(
                            comment_thread=private_all_comments, 
                            x_org_id=x_org_id, 
                            team_id=ticket_data["teamId"], 
                            api_key=api_key,
                            thena_platform_url=THENA_PLATFORM_URL
                        ),
                    )
                    
                    # Store results in state
                    state["public_author_mapping"] = public_author_mapping
                    state["private_author_mapping"] = private_author_mapping
                    
                    # Log results
                    logger.info(f"Public author mapping: {public_author_mapping}")
                    logger.info(f"Private author mapping: {private_author_mapping}")
                except Exception as e:
                    error_msg = f"Error fetching author mappings in parallel: {str(e)}"
                    logger.error(error_msg)
                    state["error"] = error_msg
                    return state
                team_id = ticket_data["teamId"]
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.INFO,
                    description=f"Ticket info fetched successfully by {agent_name}",
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )   

                return state

            except httpx.HTTPStatusError as e:
                error_msg = f"HTTP status error when fetching ticket data: {e.response.status_code} - {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.ERROR,
                    description=f"Ticket info fetched failed by {agent_name}",
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )
                raise ValueError(error_msg)
            except httpx.RequestError as e:
                error_msg = f"HTTP request error when fetching ticket data: {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.ERROR,
                    description=f"Ticket info fetched failed by {agent_name}",
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )
                raise ValueError(error_msg)
            except httpx.HTTPError as e:
                error_msg = f"HTTP error occurred when fetching ticket data: {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.ERROR,
                    description=f"Ticket info fetched failed by {agent_name}",
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )
                raise ValueError(error_msg)
            except json.JSONDecodeError as e:
                error_msg = f"Invalid JSON response from ticket API: {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.ERROR,
                    description=f"Ticket info fetched failed by {agent_name}",
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )
                raise ValueError(error_msg)
            except KeyError as e:
                error_msg = f"Missing required field in ticket response: {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.ERROR,
                    description=f"Ticket info fetched failed by {agent_name}",
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )
                raise ValueError(error_msg)

    except ValueError as e:
        # Re-raise specific ValueError exceptions with their messages
        logger.error(f"Error in get_ticket_info: {str(e)}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Ticket info fetched failed by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )
        raise
    except Exception as e:
        # Catch any other unexpected exceptions
        error_msg = f"Unexpected error in get_ticket_info: {str(e)}"
        logger.error(error_msg)
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Ticket info fetched failed by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )
        raise ValueError(error_msg)


async def analyze_status_node(state: StatusState) -> StatusState:
    """Analyze ticket content and activity to recommend status changes."""
    try:
        logger.info(f"Analyze status node - Input state: {state}")

        # Get ticket info directly from state
        ticket_info = state["ticket_data"]
        judge_feedback = state["feedback"]
        available_statuses = state.get("available_statuses", [])
        public_comments_thread = state.get("public_comment_thread", [])
        public_latest_comment = public_comments_thread[-1] if public_comments_thread else None
        public_author_mapping = state.get("public_author_mapping", {})
        private_comments_thread = state.get("private_comment_thread", [])
        private_latest_comment = private_comments_thread[-1] if private_comments_thread else None
        private_author_mapping = state.get("private_author_mapping", {})
        agent_config = state.get("agent_config", {})

        # Ensure we have a valid ticket ID
        if not ticket_info.get("id"):
            return state

        configuration = state.get("configuration", "")
        instructions = configuration.get("instructions", "")
        # Format statuses as a clean JSON array without unnecessary indentation
        statuses_json = json.dumps(
            [
                {"id": s.id, "name": s.name, "description": s.description, "parentStatusId": s.parentStatusId}
                for s in available_statuses
            ]
        )
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]
        x_org_id = state["x_org_id"]

        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Ticket status analysis starting by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )


        # Load the prompt data
        prompt_data = load_prompt("ai_status_change_analysis")  # Use prompt name without .md
        
        # Format the agent configuration into a system message
        formatted_agent_config = format_agent_config_to_system_message(
            agent_details=state["agent_data"],
            config=agent_config or {}
        )
        
        # Format the prompt with the loaded data - ensure ALL parameters have non-empty string values
        analysis_prompt = format_prompt(
            prompt_data,  # Pass the loaded dictionary here
            agent_config=formatted_agent_config,
            ticket_data=json.dumps(ticket_info if ticket_info is not None else "No ticket data available"),
            public_comments=json.dumps([comment.dict() if hasattr(comment, 'dict') else comment for comment in public_comments_thread] if public_comments_thread is not None else "No public comments available"),
            public_author_mapping=json.dumps(public_author_mapping if public_author_mapping is not None else "No public author mapping available"),
            public_latest_comment=json.dumps(public_latest_comment.dict() if hasattr(public_latest_comment, 'dict') else public_latest_comment if public_latest_comment is not None else "No public latest comment available"),
            private_comments=json.dumps([comment.dict() if hasattr(comment, 'dict') else comment for comment in private_comments_thread] if private_comments_thread is not None else "No private comments available"),
            private_author_mapping=json.dumps(private_author_mapping if private_author_mapping is not None else "No private author mapping available"),
            private_latest_comment=json.dumps(private_latest_comment.dict() if hasattr(private_latest_comment, 'dict') else private_latest_comment if private_latest_comment is not None else "No private latest comment available"),
            statuses_json=json.dumps(statuses_json if statuses_json is not None else "[]"),
            instructions=json.dumps(instructions if instructions is not None else "No instructions available"),
            judge_feedback=json.dumps(judge_feedback
                if judge_feedback is not None
                else "No previous feedback available."
            ),
        )

        logger.info(f"Graph: Analysis prompt: {analysis_prompt}")

        response = await call_openai_api_directly(
            system_prompt="",
            user_message=analysis_prompt,
            temperature=0,
            max_tokens=1000,
            model="gpt-4.1"
        )
        try:
            # Extract JSON from the response content
            content = response
            logger.info(f"Analysis Status node: Response: {content}")
            # Find the JSON object between curly braces
            start = content.find("{")
            end = content.rfind("}") + 1
            if start != -1 and end != -1:
                json_str = content[start:end]
                try:
                    analysis = json.loads(json_str)
                except json.JSONDecodeError as e:
                    error_msg = f"Failed to parse AI response as JSON: {str(e)}"
                    logger.error(error_msg)
                    state["error"] = error_msg
                    return state
            else:
                error_msg = "No JSON object found in response"
                logger.error(error_msg)
                state["error"] = error_msg
                return state

            # Update state with analysis results
            state["reasoning"] = analysis.get("reasoning", "")
            state["analysis"] = analysis

            try:
                state["confidence"] = float(analysis.get("confidence", 0))
            except (ValueError, TypeError) as e:
                error_msg = f"Invalid confidence value in AI response: {str(e)}"
                logger.error(error_msg)
                state["confidence"] = 0.0

            # Find status name from ID
            status_id = analysis.get("statusId")
            if status_id:
                status = next(
                    (s for s in available_statuses if s.id == status_id), None
                )
                if status:
                    state["recommended_status"] = status.name
            else:
                # If no status ID is provided but confidence is high, keep current status
                if state["confidence"] >= 0.85:
                    current_status = next(
                        (s for s in available_statuses if s.name == ticket_info.get("status")), None
                    )
                    if current_status:
                        state["recommended_status"] = current_status.name
                        # Update the analysis with the current status ID
                        analysis["statusId"] = current_status.id

            await record_audit_log(
                entity_type=AuditLogEntityType.TICKET,
                entity_id=state["ticket_id"],
                operation=AuditLogOp.INFO,
                description=f"Ticket status analysis completed by {agent_name}",
                metadata={
                    "reasoning": state["reasoning"],
                    "confidence": state["confidence"],
                    "recommended_status": state["recommended_status"],
                },
                user_id=bot_sub,
                visibility=AuditLogVisibility.TEAM,
                team_id=team_id,
                x_org_id=x_org_id,
                api_key=api_key,
            )            

        except json.JSONDecodeError as e:
            error_msg = f"Failed to parse analysis: {str(e)}"
            logger.error(error_msg)
            await record_audit_log( 
                entity_type=AuditLogEntityType.TICKET,
                entity_id=state["ticket_id"],
                operation=AuditLogOp.ERROR,
                description=f"Ticket status analysis failed by {agent_name}",
                metadata={
                    "error": error_msg,
                },
                user_id=bot_sub,
                visibility=AuditLogVisibility.TEAM,
                team_id=team_id,
                x_org_id=x_org_id,
                api_key=api_key,
            )
            state["error"] = error_msg

    except Exception as e:
        error_msg = f"Analysis error: {str(e)}"
        logger.error(error_msg)
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Ticket status analysis failed by {agent_name}",
            metadata={
                "error": error_msg,
            },
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )
        state["error"] = error_msg

    return state


async def judge_analysis_node(state: StatusState) -> StatusState:
    """Judge the analysis using GPT-4-0 to validate the recommendation."""
    try:
        # Get relevant info from state
        ticket_info = state["ticket_data"]
        analysis = {
            "reasoning": state["reasoning"],
            "confidence": state["confidence"],
            "recommended_status": state["recommended_status"],
        }
        configuration = state.get("configuration")
        instructions = configuration.get("instructions")

        # Load the prompt data first
        prompt_data = load_prompt(
            "ai_status_change_judge"
        )  # Use prompt name without .md

        # Format the prompt with the loaded data
        base_prompt = format_prompt(
            prompt_data,  # Pass the loaded dictionary here
            ticket_info_json=json.dumps(
                ticket_info, indent=2
            ),  # Changed from ticket_info
            analysis_json=json.dumps(analysis, indent=2),  # Changed from analysis
            instructions=str(instructions if instructions else "No instructions provided"),
        )

        logger.info(f"Judge analysis node: Base prompt: {base_prompt}")

        response = await call_openai_api_directly(
            system_prompt="",
            user_message=base_prompt,
            temperature=0,
            max_tokens=1000,
            model="gpt-4.1"
        )
        try:
            # Extract JSON from potential markdown code block
            content = response
            logger.info(f"Judge analysis node: Response: {content}")
            if "```json" in content:
                # Find content between ```json and ```
                start = content.find("{", content.find("```json"))
                end = content.rfind("}") + 1
            else:
                # Find the first JSON object
                start = content.find("{")
                end = content.rfind("}") + 1

            if start != -1 and end != -1:
                json_str = content[start:end]
                try:
                    judgment = json.loads(json_str)
                except json.JSONDecodeError as e:
                    error_msg = f"Failed to parse judge's response as JSON: {str(e)}"
                    logger.error(error_msg)
                    state["error"] = error_msg
                    return state
            else:
                error_msg = "No JSON object found in judge's response"
                logger.error(error_msg)
                state["error"] = error_msg
                return state

            # Adjust confidence based on judge's evaluation
            try:
                state["confidence"] = max(state["confidence"], float(
                    judgment.get("judge_confidence", 0)
                ))
            except (ValueError, TypeError) as e:
                error_msg = f"Invalid confidence adjustment value: {str(e)}"
                logger.error(error_msg)
                # Continue without adjustment rather than failing

            approve = judgment.get("approved", False)
            # Increment attempt counter
            state["attempt_count"] += 1
            # Set ticket updates if confidence is high enough
            if approve == True:
                # Find status ID from the recommended status
                status = next(
                    (
                        s
                        for s in state["available_statuses"]
                        if s.name == state["recommended_status"]
                    ),
                    None,
                )
                if status:
                    state["ticket_updates"] = {"statusId": status.id}
            # Store analysis and reset if confidence not high enough and attempts remain
            if approve == False and state["attempt_count"] < 2:
                state[
                    "feedback"
                ] = f"""
                Attempt {state['attempt_count']}:
                - Recommended Status: {state['recommended_status']}
                - Confidence: {state['confidence']}
                - Reasoning: {state['reasoning']}

                Judge's Feedback: {judgment.get('feedback')}
                """
                # Reset analysis fields
                state["reasoning"] = ""
                state["confidence"] = 0.0
                state["recommended_status"] = ""
                state["ticket_updates"] = {}

        except (json.JSONDecodeError, ValueError) as e:
            error_msg = f"Failed to parse judge's analysis: {str(e)}"
            logger.error(error_msg)
            state["error"] = error_msg

        except Exception as e:
            error_msg = f"Error processing judge's response: {str(e)}"
            logger.error(error_msg)
            state["error"] = error_msg

    except Exception as e:
        error_msg = f"Judge analysis error: {str(e)}"
        logger.error(error_msg)
        state["error"] = error_msg

    return state


async def update_ticket_status_node(state: StatusState) -> StatusState:
    """Update the ticket status via API call."""
    try:
        ticket_id = state["ticket_id"]
        available_statuses = state["available_statuses"]
        ticket_info = state["ticket_data"]
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        x_org_id = state["x_org_id"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]

        # Find the status ID for the recommended status
        status_id = None
        for status in available_statuses:
            if isinstance(status, TicketStatus):
                # Access Pydantic model attributes directly
                if status.name == state["recommended_status"]:
                    status_id = status.id
                    break
            elif isinstance(status, dict):
                # Handle case where it's a dict
                if status["name"] == state["recommended_status"]:
                    status_id = status["id"]
                    break
            else:
                if state["confidence"] >= 0.85:
                    current_status = next(
                        (s for s in available_statuses if s.name == ticket_info.get("status")), None
                    )
                    if current_status:
                        status_id = current_status.id
       

        if not status_id:
            error_message = f"Could not find status ID for recommended status: {state['recommended_status']}"
            logger.error(error_message)
            state["error"] = error_message
            state["status_updated"] = False
            raise ValueError(
                error_message
            )  # Raise exception to be caught by outer try/except

        # Check if the current status is the same as the recommended status
        if (
            state.get("ticket_data")
            and state["ticket_data"].get("statusId") == status_id
        ):
            logger.info(
                "Ticket %s already has status %s – skipping update.",
                ticket_id,
                state["recommended_status"],
            )
            state["status_updated"] = False
            await record_audit_log(
                entity_type=AuditLogEntityType.TICKET,
                entity_id=state["ticket_id"],
                operation=AuditLogOp.INFO,
                description=f"Ticket status update skipped by {agent_name}",
                metadata={
                    "reasoning": state["reasoning"],
                    "confidence": state["confidence"],
                    "recommended_status": state["recommended_status"],
                },
                user_id=bot_sub,
                visibility=AuditLogVisibility.TEAM,
                team_id=team_id,
                x_org_id=x_org_id,
                api_key=api_key,
            )
            return state

        # Get API credentials from database 
        api_key = state["agent_data"]["bot_token"]
        bot_sub = state["agent_data"]["bot_sub"]
        agent_name = state["agent_data"]["name"]
        avatar_url = state["agent_data"]["avatar_url"]
        x_org_id = state["x_org_id"]
        team_id = state["ticket_data"]["teamId"]

        # Set up headers
        headers = {
            "Content-Type": "application/json",
            "x-api-key": api_key,
            "x-org-id": x_org_id,
        }

        async with httpx.AsyncClient() as client:
            try:
                # First update the status
                status_payload = {"statusId": status_id}

                status_response = await client.patch(
                    f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}",
                    headers=headers,
                    json=status_payload,
                )

                # Check if the status update was successful
                if status_response.status_code != 200:
                    error_msg = (
                        f"Failed to update ticket status: {status_response.text}"
                    )
                    logger.error(error_msg)
                    await record_audit_log(
                        entity_type=AuditLogEntityType.TICKET,
                        x_org_id=x_org_id,
                        entity_id=ticket_id,
                        operation=AuditLogOp.ERROR,
                        description=f"Ticket status update failed by {agent_name}",
                        metadata={
                            "error": error_msg,
                        },
                        user_id=bot_sub,
                        api_key=api_key,
                        visibility=AuditLogVisibility.TEAM,
                        team_id=team_id,
                    )
                    state["error"] = error_msg
                    return state
                else:
                    state["status_updated"] = True
                    await record_audit_log(
                        entity_type=AuditLogEntityType.TICKET,
                        x_org_id=x_org_id,
                        entity_id=ticket_id,
                        operation=AuditLogOp.INFO,
                        description=f"Ticket status updated by {agent_name}",
                        metadata={
                            "status": state["recommended_status"],
                            "reasoning": state["reasoning"],
                            "confidence": state["confidence"],
                        },
                        user_id=bot_sub,
                        api_key=api_key,
                        visibility=AuditLogVisibility.TEAM,
                        team_id=team_id,
                    )
                    
            except httpx.HTTPError as e:
                error_msg = f"HTTP error occurred: {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    x_org_id=x_org_id,
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=ticket_id,
                    operation=AuditLogOp.ERROR,
                    description=f"Ticket status update failed by {agent_name}",
                    metadata={
                        "error": error_msg,
                    },
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    api_key=api_key,
                )
                raise ValueError(error_msg)
            except Exception as e:
                error_msg = f"Error updating ticket status: {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    x_org_id=x_org_id,
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=ticket_id,
                    operation=AuditLogOp.ERROR,
                    description=f"Ticket status update failed by {agent_name}",
                    metadata={
                        "error": error_msg,
                    },
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    api_key=api_key,
                )
                raise ValueError(error_msg)

        return state

    except Exception as e:
        logger.error(f"Error in update_ticket_status: {str(e)}")
        await record_audit_log(
            x_org_id=x_org_id,
            entity_type=AuditLogEntityType.TICKET,
            entity_id=ticket_id,
            operation=AuditLogOp.ERROR,
            description=f"Ticket status update failed by {agent_name}",
            metadata={
                "error": str(e),
            },
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            api_key=api_key,
        )
        raise


async def should_continue(state: StatusState) -> bool:
    """Determine if we should continue the analysis loop."""
    try:
        # Return True (continue) only if:
        # 1. Haven't exceeded max attempts (2 attempts)
        # 2. Confidence is below threshold
        confidence = state.get("confidence")
        return state["attempt_count"] < 2 and (  # Max 2 attempts
            confidence is None or confidence < 0.85
        )  # Continue if no confidence or below threshold
    except Exception as e:
        # Log error but don't continue on error
        logger.error(f"Error in should_continue: {str(e)}")
        return False  # Default to not continuing on error


def create_workflow() -> StateGraph:
    """Create the status change workflow with validation loop."""
    workflow = StateGraph(StatusState)

    # Add nodes
    workflow.add_node("get_ticket_info", get_ticket_info_node)
    workflow.add_node("analyze_status", analyze_status_node)
    workflow.add_node("judge_analysis", judge_analysis_node)
    workflow.add_node("update_ticket_status", update_ticket_status_node)

    # Set entry point
    workflow.set_entry_point("get_ticket_info")

    # Add edges with conditions
    workflow.add_edge(
        "get_ticket_info", "analyze_status"
    )  # First get ticket info, then analyze
    workflow.add_edge("analyze_status", "judge_analysis")  # Then judge the analysis

    # After judge analysis, either retry or update
    workflow.add_conditional_edges(
        "judge_analysis",  # Judge decides whether to continue
        should_continue,
        {
            True: "analyze_status",  # Loop back for another attempt if needed
            False: "update_ticket_status",  # Proceed to update if done
        },
    )

    # Always end after update attempt
    workflow.add_edge("update_ticket_status", END)

    return workflow.compile()


async def execute_flow(**inputs: dict[str, Any]) -> dict[str, Any]:
    """Execute the ticket status change workflow."""
    try:
        # Debug logging for input structure
        logger.info(f"AI Status Change Flow - Input structure: {inputs}")
        logger.info(f"AI Status Change Flow - Input types: {type(inputs)}")

        # Extract ticket ID from potentially nested structure
        ticket_id = None
        if isinstance(inputs, dict):
            # Try different possible paths to find ticket_id
            if "input" in inputs and isinstance(inputs["input"], dict):
                input_data = inputs["input"]

                # Path 1: input.ticket.input.ticket.ticket_id (current structure)
                if (
                    "ticket" in input_data
                    and isinstance(input_data["ticket"], dict)
                    and "input" in input_data["ticket"]
                    and isinstance(input_data["ticket"]["input"], dict)
                    and "ticket" in input_data["ticket"]["input"]
                    and isinstance(input_data["ticket"]["input"]["ticket"], dict)
                ):
                    ticket_id = input_data["ticket"]["input"]["ticket"].get("ticket_id")
                    logger.info(f"Found ticket_id in path 1: {ticket_id}")

                # Path 2: input.ticket.ticket_id (simpler structure)
                elif "ticket" in input_data and isinstance(input_data["ticket"], dict):
                    ticket_id = input_data["ticket"].get("ticket_id")
                    logger.info(f"Found ticket_id in path 2: {ticket_id}")

        # Get org_id from the correct level
        org_id = inputs.get("org_id")
        if not org_id and isinstance(inputs, dict) and "input" in inputs:
            org_id = inputs["input"].get("org_id")

        # Get agent_config from the correct level
        agent_config = inputs.get("agent_config", {})
        if not agent_config and isinstance(inputs, dict) and "input" in inputs:
            agent_config = inputs["input"].get("agent_config", {})

        logger.info(f"Final org ID: {org_id}")
        logger.info(f"Extracted ticket_id: {ticket_id}")

        # Initialize state from inputs
        initial_state: StatusState = {
            "flow_id": inputs.get("flow_id"),
            "ticket_id": ticket_id,
            "agent_id": inputs.get("agent_id"),
            "org_id": org_id,
            "agent_config": agent_config,
            "ticket_data": None,
            "available_statuses": None,
            "comments": None,
            "user_instruction": None,
            "analysis": None,
            "recommended_status": None,
            "confidence": None,
            "reasoning": None,
            "attempt_count": 0,
            "feedback": None,
            "status_updated": False,
        }

        logger.info(f"AI Status Change Flow - Initial state: {initial_state}")

        if not initial_state["ticket_id"]:
            logger.error("AI Status Change Flow - Missing ticket ID")
            raise ValueError("Ticket ID is required")

        workflow = create_workflow()
        final_state = await workflow.ainvoke(initial_state)

        return {
            "status": True,
            "data": {
                "status_updated": final_state["status_updated"],
                "recommended_status": final_state.get("recommended_status"),
                "confidence": final_state.get("confidence"),
                "reasoning": final_state.get("reasoning"),
            },
        }

    except Exception as e:
        logger.error(f"Error in execute_flow: {str(e)}")
        logger.error(traceback.format_exc())
        return {"status": False, "message": str(e)}
