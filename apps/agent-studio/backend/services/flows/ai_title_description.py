import json
from typing import Any, Dict, List

import httpx
from langchain_core.messages import HumanMessage
from langgraph.graph import END, StateGraph

from ...core.config import get_config
from ...core.database import get_supabase
from ...models.flow_models import TicketComment, TitleState
from ...prompt_library import format_prompt, load_prompt
from ...utils.llm_utils import call_openai_api_directly
from ...utils.logger import get_logger
from ...utils.audit_logger import record_audit_log, AuditLogEntityType, AuditLogOp, AuditLogVisibility

# Get settings for API URLs
settings = get_config()
THENA_PLATFORM_URL = settings.THENA_PLATFORM_URL
logger = get_logger("ai_title_description_flow")


async def get_ticket_info(
    ticket_id: str, api_key: str, x_org_id: str
) -> Dict[str, Any]:
    """Get ticket information from the API."""
    url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}

    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)
        response.raise_for_status()
        return response.json()["data"]


async def get_ticket_comments(
    ticket_id: str, api_key: str, x_org_id: str, limit: int = 20, page: int = 0
) -> List[TicketComment]:
    """Get comments for a ticket."""
    url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}/comments"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}
    params = {
        "limit": limit,
        "page": page,
        "commentType": "comment",
        "visibility": "public",
    }

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()

            api_response = response.json()
            if not api_response.get("status"):
                error_msg = f"API error: {api_response.get('message', 'No error message provided')}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            return [
                TicketComment(**comment) for comment in api_response.get("data", [])
            ]

        except httpx.HTTPError as e:
            error_msg = f"HTTP error occurred: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except KeyError as e:
            error_msg = f"Missing required field in response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

async def get_comment_threads(
    comment_id: str, api_key: str, x_org_id: str
) -> List[Dict[str, Any]]:
    """Get all comments in a thread for a specific comment."""
    url = f"{THENA_PLATFORM_URL}/v1/comments/{comment_id}/threads"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            logger.info(f"Get comment threads response: {response.json()}")

            api_response = response.json()
            if not api_response.get("status"):
                error_msg = f"API error: {api_response.get('message', 'No error message provided')}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            return api_response.get("data", [])

        except httpx.HTTPError as e:
            error_msg = f"HTTP error occurred: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except KeyError as e:
            error_msg = f"Missing required field in response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)        


async def get_ticket_info_node(state: TitleState) -> TitleState:
    """Fetch ticket information and comments."""

    try:
        # Get API credentials from config
        db = await get_supabase()
        agent_result = (
            await db.table("agents")
            .select("name", "bot_sub","bot_token")
            .eq("id", state.agent_id)
            .execute()
        )
        state.agent_data = agent_result.data[0]

        api_key = agent_result.data[0]["bot_token"]
        bot_sub = agent_result.data[0]["bot_sub"]
        agent_name = agent_result.data[0]["name"]

        org_result = (
            await db.table("organizations")
            .select("x-org-id")
            .eq("id", state.org_id)
            .execute()
        )
        if not org_result.data or not org_result.data[0].get("x-org-id"):
            error_msg = f"Organization {state.org_id} not found or x-org-id missing"
            logger.error(error_msg)
            raise ValueError(error_msg)
        x_org_id = org_result.data[0]["x-org-id"]
        state.x_org_id = x_org_id

        configuration = (
            await db.table("agent_flows")
            .select("configuration")
            .eq("agent_id", state.agent_id)
            .eq("flow_id", state.flow_id)
            .execute()
        )
        if configuration.data and len(configuration.data) > 0:
            state.configuration = configuration.data[0]["configuration"]
        else:
            state.configuration = {
                "configuration": {
                    "instructions": "No instructions provided",
                }
            }    

        # Fetch ticket data and comments in parallel
        ticket_data = await get_ticket_info(state.ticket_id, api_key, x_org_id)
        comments = await get_ticket_comments(state.ticket_id, api_key, x_org_id)
        all_comments = []
            
        # Only fetch thread comments for the latest comment if it exists and has replies
        if comments and len(comments) > 0:
            latest_comment = comments[-1]
            if latest_comment.metadata and latest_comment.metadata.get('replies'):
                try:
                    thread_comments = await get_comment_threads(
                            latest_comment.id, api_key, x_org_id
                        )
                    # Convert thread comments to TicketComment objects
                    thread_comments = [TicketComment(**c) for c in thread_comments]
                    all_comments.extend(thread_comments)
                except Exception as e:
                    logger.exception(f"Error fetching thread comments for latest comment {latest_comment.id}")
        

        state.ticket_data = ticket_data
        state.comments = comments
        state.comment_thread = all_comments
        logger.info(f"Graph: Ticket data: {ticket_data}")
        logger.info(f"Graph: Comments: {comments}")
        logger.info(f"Graph: Comment thread: {all_comments}")
        team_id = state.ticket_data["team_id"]
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Fetched ticket info and comments by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )

        return state

    except Exception as e:
        error_msg = f"Error fetching ticket info: {str(e)}"
        logger.error(f"Graph: Error fetching ticket info, error: {error_msg}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Failed to fetch ticket info and comments by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        state.error = error_msg
        raise


async def analyze_ticket_node(state: TitleState) -> TitleState:
    """Analyze ticket content and activity."""

    try:
        # Prepare ticket data for analysis
        ticket_data = state["ticket_data"] or ""
        comments = state["comments"] or ""
        configuration = state["configuration"] or {}
        comment_thread = state["comment_thread"] or ""
        instructions = configuration.get("instructions", "")
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["team_id"]
        api_key = state["agent_data"]["bot_token"]

        # Load and format the analysis prompt
        analysis_prompt_data = load_prompt("ai_title_description_analysis")
        analysis_prompt = format_prompt(
            analysis_prompt_data,
            ticket_data_json=f"{ticket_data}" if ticket_data else "No ticket data",  
            comments_json=f"{comments}" if comments else "No comments",  
            comment_thread_json=f"{comment_thread}" if comment_thread else "No comment thread",
            instructions=f"{instructions}" if instructions else "No additional instructions"  
        )

        logger.info(f"Graph: Analysis prompt: {analysis_prompt}")

        response = await call_openai_api_directly(
            system_prompt="",
            user_message=analysis_prompt,
            temperature=0.1,
            max_tokens=1000,
            model="gpt-4.1"
        )
        analysis = json.loads(response.strip())

        logger.info(f"Graph: Analysis response: {analysis}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Analyzed ticket for Ai title generation by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )

        state.analysis = analysis
        return state

    except Exception as e:
        error_msg = f"Error analyzing ticket: {str(e)}"
        logger.error(f"Graph: Error analyzing ticket, error: {error_msg}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Failed to analyze ticket by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        state.error = error_msg
        raise


async def generate_title_node(state: TitleState) -> TitleState:
    """Generate final summaries based on analysis."""

    try:
        # Get analysis and ticket data
        analysis = state["analysis"]
        logger.info(f"Graph: Analysis: {analysis}")
        ticket_data = state["ticket_data"]
        logger.info(f"Graph: Ticket data: {ticket_data}")
        comments = state["comments"]
        logger.info(f"Graph: Comments: {comments}")
        configuration = state["configuration"] or {}
        instructions = configuration.get("instructions", "")
        logger.info(f"Graph: Instructions: {instructions}")
        comment_thread = state["comment_thread"] or ""
        logger.info(f"Graph: Comment thread: {comment_thread}")
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["team_id"]
        api_key = state["agent_data"]["bot_token"]

        # Load and format the title generation prompt
        title_prompt_data = load_prompt("ai_title_description_generation")
        title_prompt = format_prompt(
            title_prompt_data,
            analysis_json=f"{analysis}" if analysis else "No analysis",  
            ticket_data_json=f"{ticket_data}" if ticket_data else "No ticket data",  
            comments_json=f"{comments}" if comments else "No comments",  
            comment_thread_json=f"{comment_thread}" if comment_thread else "No comment thread",
            instructions=f"{instructions}" if instructions else "No additional instructions"  
        )

        logger.info(f"Graph: Title generation prompt: {title_prompt}")
        response = await call_openai_api_directly(
            system_prompt="",
            user_message=title_prompt,
            temperature=0.1,
            max_tokens=1000,
            model="gpt-4.1"
        )
        logger.info(f"Graph: Title generation response: {response}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Generated title for Ai title generation by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )

        # Extract JSON from markdown code block if present
        content = response
        if content.startswith("```json"):
            content = content[7:]  # Remove ```json prefix
        if content.endswith("```"):
            content = content[:-3]  # Remove ``` suffix

        ai_title = json.loads(content.strip())
        logger.info(f"Graph: Generated title: {ai_title}")

        # Update state with summaries
        state.ai_title = ai_title.get("ai_title", "")
        state.confidence = ai_title.get("confidence", 0.0)

        return state

    except Exception as e:
        error_msg = f"Error generating title: {str(e)}"
        logger.error(f"Graph: Error generating title, error: {error_msg}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Failed to generate title for Ai title generation by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        state.error = error_msg
        raise


async def update_ticket_node(state: TitleState) -> TitleState:
    """Update ticket with summaries."""

    try:
        # Update ticket with summaries

        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["team_id"]
        api_key = state["agent_data"]["bot_token"]
        x_org_id = state["x_org_id"]
        ticket_id = state["ticket_id"]
        ai_title = state["ai_title"]

        # Prepare update payload
        update_payload = {"aiGeneratedTitle": ai_title}

        url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}"
        headers = {"x-api-key": api_key, "x-org-id": x_org_id}

        async with httpx.AsyncClient() as client:
            response = await client.patch(url, headers=headers, json=update_payload)
            response.raise_for_status()

        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Updated ticket for Ai title generation by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )

        return state

    except Exception as e:
        error_msg = f"Error updating ticket: {str(e)}"
        logger.error(f"Graph: Error updating ticket, error: {error_msg}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Failed to update ticket for Ai title generation by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        state.error = error_msg
        raise


def create_workflow() -> StateGraph:
    """Create the title workflow."""

    workflow = StateGraph(TitleState)

    # Add nodes
    workflow.add_node("get_ticket_info", get_ticket_info_node)
    workflow.add_node("analyze_ticket", analyze_ticket_node)
    workflow.add_node("generate_title", generate_title_node)
    workflow.add_node("update_ticket", update_ticket_node)

    # Set up the flow
    workflow.set_entry_point("get_ticket_info")
    workflow.add_edge("get_ticket_info", "analyze_ticket")
    workflow.add_edge("analyze_ticket", "generate_title")
    workflow.add_edge("generate_title", "update_ticket")
    workflow.add_edge("update_ticket", END)

    return workflow.compile()


async def execute_flow(**inputs: Dict[str, Any]) -> Dict[str, Any]:
    """Execute the title workflow."""
    logger.info(
        "Graph: Starting workflow execution", {"input_keys": list(inputs.keys())}
    )

    try:
        # Log the raw input for debugging
        logger.info(
            "Graph: Raw input received",
            {"raw_input": json.dumps(inputs, indent=2, default=str)},
        )

        ticket_id = None
        comment_id = None
        parentCommentId = None

        logger.info("Ticket info extraction started")
        
        if isinstance(inputs, dict):
            # Try different possible paths to find ticket info
            if "input" in inputs and isinstance(inputs["input"], dict):
                input_data = inputs["input"]
                
                # Path 1: input.ticket.input.ticket (current structure)
                if ("ticket" in input_data and isinstance(input_data["ticket"], dict) and
                    "input" in input_data["ticket"] and isinstance(input_data["ticket"]["input"], dict) and
                    "ticket" in input_data["ticket"]["input"] and isinstance(input_data["ticket"]["input"]["ticket"], dict)):
                    ticket_data = input_data["ticket"]["input"]["ticket"]
                    ticket_id = ticket_data.get("ticket_id")
                
                # Path 2: input.ticket (simpler structure)
                elif "ticket" in input_data and isinstance(input_data["ticket"], dict):
                    ticket_data = input_data["ticket"]
                    ticket_id = ticket_data.get("ticket_id") or ticket_data.get("id")

        # Log extracted fields
        logger.info(f"Graph: Extracted fields: {ticket_id}")
        org_id = inputs.get("input", {}).get("org_id")
        agent_id = inputs.get("agent_id")
        flow_id = inputs.get("flow_id")
        agent_config = inputs.get("input", {}).get("agent_config", {})

        # Initialize state with inputs
        initial_state = TitleState(
            ticket_id=ticket_id,
            agent_id=agent_id,
            org_id=org_id,
            flow_id=flow_id,
            config=agent_config,
        )

        # Validate required fields
        missing_fields = []
        if not initial_state.ticket_id:
            missing_fields.append("ticket_id")
        if not initial_state.agent_id:
            missing_fields.append("agent_id")
        if not initial_state.org_id:
            missing_fields.append("org_id")
        if not initial_state.flow_id:
            missing_fields.append("flow_id")

        if missing_fields:
            error_msg = f"Missing required fields: {', '.join(missing_fields)}"
            logger.error(
                f"Graph: Validation failed, missing fields: {', '.join(missing_fields)}"
            )
            raise ValueError(error_msg)

        # Create and execute workflow
        workflow = create_workflow()
        final_state_dict = dict(await workflow.ainvoke(initial_state))

        # Return results
        return {
            "status": True,
            "data": {
                "title": final_state_dict.get("title", ""),
                "confidence": final_state_dict.get("confidence", 0.0),
            },
        }

    except Exception as e:
        error_msg = f"Error executing title workflow: {str(e)}"
        logger.error(f"Graph: Error executing title workflow, error: {error_msg}")
        return {
            "status": False,
            "message": error_msg,
            "data": {"title": "", "confidence": 0.0},
        }
