import json
import traceback
from typing import Any, Dict, List

import httpx
from langgraph.graph import END, StateGraph

from ...core.config import get_config
from ...core.database import get_supabase
from ...models.flow_models import SummaryState, TicketComment
from ...prompt_library import format_prompt, load_prompt
from ...utils.llm_utils import call_openai_api_directly
from ...utils.logger import get_logger
from ...utils.audit_logger import record_audit_log, AuditLogEntityType, AuditLogOp, AuditLogVisibility

logger = get_logger("ai_summary_service")
settings = get_config()
THENA_PLATFORM_URL = settings.THENA_PLATFORM_URL


async def get_ticket_info(
    ticket_id: str, api_key: str, x_org_id: str
) -> Dict[str, Any]:
    """Get ticket information from the API."""
    url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}

    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)
        response.raise_for_status()
        logger.info(f"Get ticket info response: {response.json()}")
        return response.json()["data"]


async def get_ticket_comments(
    ticket_id: str, api_key: str, x_org_id: str, limit: int = 20, page: int = 0
) -> List[TicketComment]:
    """Get comments for a ticket."""
    url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}/comments"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}
    params = {
        "limit": limit,
        "page": page,
        "commentType": "comment",
        "visibility": "public",
    }

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            logger.info(f"Get ticket comments response: {response.json()}")
            api_response = response.json()
            if not api_response.get("status"):
                error_msg = f"API error: {api_response.get('message', 'No error message provided')}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            return [
                TicketComment(**comment) for comment in api_response.get("data", [])
            ]

        except httpx.HTTPError as e:
            error_msg = f"HTTP error occurred: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except KeyError as e:
            error_msg = f"Missing required field in response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)



async def get_comment_threads(
    comment_id: str, api_key: str, x_org_id: str
) -> List[Dict[str, Any]]:
    """Get all comments in a thread for a specific comment."""
    url = f"{THENA_PLATFORM_URL}/v1/comments/{comment_id}/threads"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            logger.info(f"Get comment threads response: {response.json()}")

            api_response = response.json()
            if not api_response.get("status"):
                error_msg = f"API error: {api_response.get('message', 'No error message provided')}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            return api_response.get("data", [])

        except httpx.HTTPError as e:
            error_msg = f"HTTP error occurred: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except KeyError as e:
            error_msg = f"Missing required field in response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg) 


async def get_ticket_info_node(state: SummaryState) -> SummaryState:
    """Fetch ticket information and comments."""

    try:
        # Get API credentials from config
        db = await get_supabase()
        agent_id = state["agent_id"]
        agent_result = (
            await db.table("agents")
            .select("name", "bot_sub","bot_token","organization_id")
            .eq("id", agent_id)
            .execute()
        )
        state["agent_data"] = agent_result.data[0]

        api_key = agent_result.data[0]["bot_token"]
        bot_sub = agent_result.data[0]["bot_sub"]
        agent_name = agent_result.data[0]["name"]
        org_id = agent_result.data[0]["organization_id"]

        org_result = (
            await db.table("organizations")
            .select("x-org-id")
            .eq("id", org_id)
            .execute()
        )
        x_org_id = org_result.data[0]["x-org-id"]
        state["x_org_id"] = x_org_id

        if not api_key or not x_org_id:
            raise ValueError("Missing API credentials in config")

        configuration = (
            await db.table("agent_flows")
            .select("configuration")
            .eq("agent_id", agent_id)
            .eq("flow_id", state["flow_id"])
            .execute()
        )
        if configuration.data and len(configuration.data) > 0:
            state["configuration"] = configuration.data[0]["configuration"]
        else:
            state["configuration"] = {
                "configuration": {
                    "instructions": "No instructions provided",
                }
            }  

        # Fetch ticket data and comments in parallel
        ticket_data = await get_ticket_info(state["ticket_id"], api_key, x_org_id)
        comments = await get_ticket_comments(state["ticket_id"], api_key, x_org_id)
        all_comments = []
            
        # Only fetch thread comments for the latest comment if it exists and has replies
        if comments and len(comments) > 0:
            latest_comment = comments[-1]
            if latest_comment.metadata and latest_comment.metadata.get('replies'):
                try:
                    thread_comments = await get_comment_threads(
                            latest_comment.id, api_key, x_org_id
                        )
                    # Convert thread comments to TicketComment objects
                    thread_comments = [TicketComment(**c) for c in thread_comments]
                    all_comments.extend(thread_comments)
                except Exception as e:
                    logger.exception(f"Error fetching thread comments for latest comment {latest_comment.id}")

        state["ticket_data"] = ticket_data
        state["comments"] = comments
        state["comment_thread"] = all_comments
        team_id = ticket_data.get("teamId")
        logger.info(f"Ticket info fetched successfully : {ticket_data}")
        logger.info(f"Comments fetched successfully : {comments}")
        logger.info(f"Thread comments fetched successfully : {all_comments}")
        
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Ticket info fetched successfully by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        return state

    except Exception as e:
        error_msg = f"Error fetching ticket info: {str(e)}"
        logger.error(error_msg)
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Error fetching ticket info by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        raise ValueError(error_msg)


async def analyze_ticket_node(state: SummaryState) -> SummaryState:
    """Analyze ticket content and activity."""
    logger.info("Graph: Entering analyze_ticket node")

    try:
        # Prepare ticket data for analysis
        ticket_data = state["ticket_data"]
        comments = state["comments"]
        configuration = state["configuration"] or {}
        comment_thread = state["comment_thread"] or ""


        # Load and format the analysis prompt
        prompt_data = load_prompt("ai_summaries_analysis")
        instructions = configuration.get("instructions", "")
        logger.info(f"Graph: Instructions: {instructions}")

        analysis_prompt = format_prompt(
            prompt_data,
            ticket_data_json=f"{ticket_data}" if ticket_data else 'No ticket data',
           comments_json=f"{comments}" if comments else 'No comments', 
            comment_thread_json=f"{comment_thread}" if comment_thread else 'No comment thread',
            instructions=instructions if instructions else 'No instructions'
        )

        logger.info(f"Graph: Analysis prompt: {analysis_prompt}")

        response = await call_openai_api_directly(
            system_prompt="",
            user_message=analysis_prompt,
            temperature=0.1,
            max_tokens=1000,
            model="gpt-4.1"
        )

        # Parse and validate analysis response
        state["analysis"] = response
        logger.info(f"Graph: Analysis response: {response}")
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]
        
      

        # Extract JSON from potential markdown code block
        content = response
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Ticket analysis completed by : {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        try:
            # Try to parse the raw content first
            analysis = json.loads(content.strip())
        except json.JSONDecodeError as json_error:
            logger.warning(
                f"Initial JSON parsing failed, attempting to extract from markdown: {str(json_error)}"
            )

            # Check if response is in a code block
            if "```json" in content or "```" in content:
                # Extract content from code block
                if "```json" in content:
                    start_marker = "```json"
                    end_marker = "```"
                else:
                    start_marker = "```"
                    end_marker = "```"

                start_idx = content.find(start_marker) + len(start_marker)
                end_idx = content.find(end_marker, start_idx)

                if start_idx != -1 and end_idx != -1:
                    json_str = content[start_idx:end_idx].strip()
                    logger.info(
                        f"Extracted JSON from code block: {json_str}"
                    )
                    analysis = json.loads(json_str)
                else:
                    # Try to find JSON object directly
                    start_idx = content.find("{")
                    end_idx = content.rfind("}") + 1

                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_str = content[start_idx:end_idx]
                        logger.info(
                            f"Extracted JSON object directly: {json_str}"
                        )
                        analysis = json.loads(json_str)
                    else:
                        raise ValueError(
                            f"Could not extract valid JSON from response: {content}"
                        )
            else:
                raise ValueError(
                    f"Response does not contain valid JSON or code block: {content}"
                )

        state["analysis"] = analysis

        logger.info("Graph: Successfully analyzed ticket")
        return state

    except Exception as e:
        error_msg = f"Error analyzing ticket: {str(e)}"
        logger.error(f"Graph: Node execution failed: {error_msg}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Ticket analysis failed by : {agent_name} and error : {error_msg}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        state.error = error_msg
        raise


async def generate_summary_node(state: SummaryState) -> SummaryState:
    """Generate final summaries based on analysis."""
    logger.info("Graph: Entering generate_summary node")
    try:
        # Get analysis and ticket data
        analysis = state["analysis"]
        ticket_data = state["ticket_data"]
        configuration = state["configuration"] or {}
        instructions = configuration.get("instructions","")
        comments = state["comments"] or ""
        comment_thread = state["comment_thread"] or ""
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]

        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Starting ticket summary creation by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        # Load and format the summary generation prompt
        prompt_data = load_prompt("ai_summaries_generation")
        summary_prompt = format_prompt(
            prompt_data,
            analysis_json=f"{analysis}" if analysis else 'No analysis',
            ticket_data_json=f"{ticket_data}" if ticket_data else 'No ticket data',
            comments_json=f"{comments}" if comments else 'No comments',
            comment_thread_json=f"{comment_thread}" if comment_thread else 'No comment thread',
            instructions=f"{instructions}" if instructions else 'No additional instructions'
        )
        logger.info(f"Prompt for Summary generation is {summary_prompt}")

        response = await call_openai_api_directly(
            system_prompt="",
            user_message=summary_prompt,
            temperature=0.1,
            max_tokens=1000,
            model="gpt-4.1"
        )

        raw_response = response.strip()
        logger.info(f"Graph: Raw LLM response{raw_response}")


        # Extract JSON from code blocks if present
        if raw_response.startswith("```"):
            try:
                # Find the content between code blocks
                start_marker = "```json\n" if "```json" in raw_response else "```\n"
                end_marker = "\n```"

                start_idx = raw_response.find(start_marker) + len(start_marker)
                end_idx = raw_response.find(end_marker, start_idx)

                if start_idx != -1 and end_idx != -1:
                    json_str = raw_response[start_idx:end_idx].strip()
                    logger.info(f"Graph: Extracted JSON from code block {json_str}")
                else:
                    json_str = raw_response
            except Exception as e:
                logger.error(f"Graph: Error extracting JSON from code block:{e}")
                raise
        else:
            json_str = raw_response

        try:
            summaries = json.loads(json_str)
        except json.JSONDecodeError as je:
            logger.error(f"Graph: JSON parsing error: {je}")
            raise

        # Update state with summaries
        state["key_points"] = summaries.get("key_points", [])
        state["customer_summary"] = summaries.get("customer_summary", "")
        state["technical_summary"] = summaries.get("technical_summary", "")

        logger.info("Graph: Successfully generated summaries")
        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Ticket summary created by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )

        return state

    except Exception as e:
        error_msg = f"Error generating summaries: {str(e)}"
        logger.error(f"Graph: Node execution failed: {error_msg}")
        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Ticket summary creation failed by {agent_name} and error : {error_msg}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        raise


async def update_ticket_node(state: SummaryState) -> SummaryState:
    """Update ticket with summaries."""
    logger.info("Graph: Entering update_ticket node")

    try:
        # Update ticket with summaries

        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]
        x_org_id = state["x_org_id"]
        
        ticket_id = state["ticket_id"]
        customer_summary = state["customer_summary"]
        technical_summary = state["technical_summary"]

        # Prepare update payload
        update_payload = {
            "aiGeneratedSummary": f"{customer_summary} + {technical_summary}"
        }
        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Updating ticket Ai summary by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )   

        url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}"
        headers = {"x-api-key": api_key, "x-org-id": x_org_id}

        async with httpx.AsyncClient() as client:
            response = await client.patch(url, headers=headers, json=update_payload)
            response.raise_for_status()

        return state

    except Exception as e:
        error_msg = f"Error updating ticket: {str(e)}"
        logger.error(f"Graph: Node execution failed: {error_msg}")
        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Updating ticket Ai summary by {agent_name} failed with error : {error_msg}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )
        raise


def create_workflow() -> StateGraph:
    """Create the summary workflow."""
    logger.info("Graph: Creating workflow")

    workflow = StateGraph(SummaryState)

    # Add nodes
    workflow.add_node("get_ticket_info", get_ticket_info_node)
    workflow.add_node("analyze_ticket", analyze_ticket_node)
    workflow.add_node("generate_summary", generate_summary_node)
    workflow.add_node("update_ticket", update_ticket_node)

    # Set up the flow
    workflow.set_entry_point("get_ticket_info")
    workflow.add_edge("get_ticket_info", "analyze_ticket")
    workflow.add_edge("analyze_ticket", "generate_summary")
    workflow.add_edge("generate_summary", "update_ticket")
    workflow.add_edge("update_ticket", END)

    logger.info("Graph: Workflow compiled and ready")

    return workflow.compile()


async def execute_flow(**inputs: Dict[str, Any]) -> Dict[str, Any]:
    """Execute the summary workflow."""
    logger.info("Graph: Starting workflow execution")

    try:
        logger.info("Graph: Raw input received", {"raw_input": json.dumps(inputs, indent=2, default=str)})

        ticket_id = None
        comment_id = None
        parentCommentId = None

        logger.info("Ticket info extraction started")
        
        if isinstance(inputs, dict):
            # Try different possible paths to find ticket info
            if "input" in inputs and isinstance(inputs["input"], dict):
                input_data = inputs["input"]
                
                # Path 1: input.ticket.input.ticket (current structure)
                if ("ticket" in input_data and isinstance(input_data["ticket"], dict) and
                    "input" in input_data["ticket"] and isinstance(input_data["ticket"]["input"], dict) and
                    "ticket" in input_data["ticket"]["input"] and isinstance(input_data["ticket"]["input"]["ticket"], dict)):
                    ticket_data = input_data["ticket"]["input"]["ticket"]
                    ticket_id = ticket_data.get("ticket_id")
                
                # Path 2: input.ticket (simpler structure)
                elif "ticket" in input_data and isinstance(input_data["ticket"], dict):
                    ticket_data = input_data["ticket"]
                    ticket_id = ticket_data.get("ticket_id") or ticket_data.get("id")

        # Log extracted fields
        logger.info(f"Graph: Extracted fields: {ticket_id}")
        org_id = inputs.get("input", {}).get("org_id")
        agent_id = inputs.get("agent_id")
        flow_id = inputs.get("flow_id")
        agent_config = inputs.get("input", {}).get("agent_config", {})

        # Initialize state with inputs
        initial_state = SummaryState(
            ticket_id=ticket_id,
            agent_id=agent_id,
            org_id=org_id,
            flow_id=flow_id,
            config=agent_config,
        )

        # Log state initialization
        logger.info("Graph: State initialization")

        # Validate state with detailed logging
        if not initial_state["ticket_id"]:
            logger.error("Graph: Missing ticket_id in state")
            raise ValueError("ticket_id is required")

        if not initial_state["agent_id"]:
            logger.error("Graph: Missing agent_id in state")
            raise ValueError("agent_id is required")

        if not initial_state["org_id"]:
            logger.error("Graph: Missing org_id in state")
            raise ValueError("org_id is required")

        if not initial_state["flow_id"]:
            logger.error("Graph: Missing flow_id in state")
            raise ValueError("flow_id is required")

        # Create and execute workflow
        workflow = create_workflow()
        final_state_dict = dict(await workflow.ainvoke(initial_state))

        # Return results
        return {
            "status": True,
            "data": {
                "key_points": final_state_dict.get("key_points", []),
                "customer_summary": final_state_dict.get("customer_summary", ""),
                "technical_summary": final_state_dict.get("technical_summary", ""),
            },
        }

    except Exception as e:
        error_msg = f"Error executing summary workflow: {str(e)}"
        logger.error(error_msg)
        return {
            "status": False,
            "message": error_msg,
            "data": {"key_points": [], "customer_summary": "", "technical_summary": ""},
        }
