import json
from typing import Any

import httpx
from langgraph.graph import END, StateGraph

from ...core.config import get_config
from ...core.database import get_supabase
from ...models.flow_models import SummaryState, TicketComment
from ...prompt_library import format_prompt, load_prompt
from ...utils.audit_logger import (
    AuditLogEntityType,
    AuditLogOp,
    AuditLogVisibility,
    record_audit_log,
)
from ...utils.llm_utils import call_openai_api_directly
from ...utils.logger import get_logger

logger = get_logger("ai_summary_service")
settings = get_config()
THENA_PLATFORM_URL = settings.THENA_PLATFORM_URL

# Import the singleton getter for ExternalAPIsClient
from ...core.thena_ext_apis import get_external_apis_client


async def get_ticket_info(
    ticket_id: str, api_key: str, x_org_id: str
) -> dict[str, Any]:
    """Get ticket information from the API using the ExternalAPIsClient."""
    try:
        # Get the singleton instance of ExternalAPIsClient
        external_apis_client = await get_external_apis_client()
        
        # Use the external_apis_client to get the ticket info
        return await external_apis_client.get_ticket_info(
            ticket_id=ticket_id,
            api_key=api_key,
            x_org_id=x_org_id,
            thena_platform_url=THENA_PLATFORM_URL,
        )
    except Exception:
        # Re-raise any exceptions from the client
        logger.exception("Error getting ticket info")
        raise


async def get_ticket_comments(
    ticket_id: str, api_key: str, x_org_id: str, limit: int = 20, page: int = 0, visibility: str = "public"
) -> list[TicketComment]:
    """Get comments for a ticket using the ExternalAPIsClient."""
    try:
        # Get the singleton instance of ExternalAPIsClient
        external_apis_client = await get_external_apis_client()
        
        # Use the external_apis_client to get the comments
        comments_data = await external_apis_client.get_ticket_comments(
            ticket_id=ticket_id,
            api_key=api_key,
            x_org_id=x_org_id,
            thena_platform_url=THENA_PLATFORM_URL,
            limit=limit,
            page=page,
            visibility=visibility,
        )
        
        # Convert the raw data to TicketComment objects
        return [TicketComment(**comment) for comment in comments_data]
    except Exception:
        # Re-raise any exceptions from the client
        logger.exception("Error getting ticket comments")
        raise


async def get_comment_threads(
    comment_id: str, api_key: str, x_org_id: str
) -> list[dict[str, Any]]:
    """Get all comments in a thread for a specific comment using the ExternalAPIsClient."""
    try:
        # Get the singleton instance of ExternalAPIsClient
        external_apis_client = await get_external_apis_client()
        
        # Use the external_apis_client to get the comment threads
        return await external_apis_client.get_comment_threads(
            comment_id=comment_id,
            api_key=api_key,
            x_org_id=x_org_id,
            thena_platform_url=THENA_PLATFORM_URL,
        )
    except Exception:
        # Re-raise any exceptions from the client
        logger.exception("Error getting comment threads")
        raise


async def get_ticket_info_node(state: SummaryState) -> SummaryState:
    """Fetch ticket information and comments."""
    
    # Initialize variables that will be used in error handling
    agent_name = None
    bot_sub = None
    team_id = None
    api_key = None

    try:
        # Get API credentials from config
        db = await get_supabase()
        agent_id = state["agent_id"]
        agent_result = (
            await db.table("agents")
            .select("name", "bot_sub","bot_token","organization_id")
            .eq("id", agent_id)
            .execute()
        )
        state["agent_data"] = agent_result.data[0]

        api_key = agent_result.data[0]["bot_token"]
        bot_sub = agent_result.data[0]["bot_sub"]
        agent_name = agent_result.data[0]["name"]
        org_id = agent_result.data[0]["organization_id"]

        org_result = (
            await db.table("organizations")
            .select("x-org-id")
            .eq("id", org_id)
            .execute()
        )
        x_org_id = org_result.data[0]["x-org-id"]
        state["x_org_id"] = x_org_id

        if not api_key or not x_org_id:
            raise ValueError("Missing API credentials in config")

        configuration = (
            await db.table("agent_flows")
            .select("configuration")
            .eq("agent_id", agent_id)
            .eq("flow_id", state["flow_id"])
            .execute()
        )
        if configuration.data and len(configuration.data) > 0:
            state["configuration"] = configuration.data[0]["configuration"]
        else:
            state["configuration"] = {
                "configuration": {
                    "instructions": "No instructions provided",
                }
            }  

        # Fetch ticket data and comments
        ticket_data = await get_ticket_info(state["ticket_id"], api_key, x_org_id)
        comments = await get_ticket_comments(state["ticket_id"], api_key, x_org_id)
        
        # Also fetch private comments
        private_comments = await get_ticket_comments(state["ticket_id"], api_key, x_org_id, visibility="private")
        
        # Process public comments thread
        all_comments = []
        
        # Only fetch thread comments for the latest comment if it exists and has replies
        if comments and len(comments) > 0:
            latest_comment = comments[-1]
            if latest_comment.metadata and latest_comment.metadata.get('replies'):
                try:
                    thread_comments = await get_comment_threads(
                        latest_comment.id, api_key, x_org_id
                    )
                    # Convert thread comments to TicketComment objects
                    thread_comments = [TicketComment(**c) for c in thread_comments]
                    # Add the latest comment first to maintain proper conversation flow
                    all_comments.append(latest_comment)
                    # Then add all the thread comments
                    all_comments.extend(thread_comments)
                except Exception:
                    logger.exception(f"Error fetching thread comments for latest comment {latest_comment.id}")
        
        # Process private comments thread
        private_all_comments = []
        
        # Only fetch thread comments for the latest private comment if it exists and has replies
        if private_comments and len(private_comments) > 0:
            latest_comment = private_comments[-1]
            if latest_comment.metadata and latest_comment.metadata.get('replies'):
                try:
                    thread_comments = await get_comment_threads(
                        latest_comment.id, api_key, x_org_id
                    )
                    # Convert thread comments to TicketComment objects
                    thread_comments = [TicketComment(**c) for c in thread_comments]
                    # Add the latest comment first to maintain proper conversation flow
                    private_all_comments.append(latest_comment)
                    # Then add all the thread comments
                    private_all_comments.extend(thread_comments)
                except Exception:
                    logger.exception(f"Error fetching thread comments for latest private comment {latest_comment.id}")
            
            # Add the original comments to the thread
            private_all_comments.extend(private_comments)

        # Store everything in state
        state["ticket_data"] = ticket_data
        state["comments"] = comments
        state["comment_thread"] = all_comments
        state["private_comments"] = private_comments
        state["private_comment_thread"] = private_all_comments
        
        team_id = ticket_data.get("teamId")
        logger.info(f"Ticket info fetched successfully : {ticket_data}")
        logger.info(f"Comments fetched successfully : {comments}")
        logger.info(f"Thread comments fetched successfully : {all_comments}")
        logger.info(f"Private comments fetched successfully : {private_comments}")
        logger.info(f"Private thread comments fetched successfully : {private_all_comments}")
        
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Ticket info fetched successfully by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        return state

    except Exception as e:
        error_msg = f"Error fetching ticket info: {str(e)}"
        logger.error(error_msg)
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Error fetching ticket info by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        raise ValueError(error_msg)


async def analyze_ticket_node(state: SummaryState) -> SummaryState:
    """Analyze ticket content and activity."""
    logger.info("Graph: Entering analyze_ticket node")
    
    # Initialize variables that will be used in error handling
    agent_name = state["agent_data"]["name"] if state.get("agent_data") else "Unknown Agent"
    bot_sub = state["agent_data"]["bot_sub"] if state.get("agent_data") else None
    team_id = state["ticket_data"]["teamId"] if state.get("ticket_data") else None
    api_key = state["agent_data"]["bot_token"] if state.get("agent_data") else None

    try:
        # Prepare ticket data for analysis
        ticket_data = state["ticket_data"]
        comments = state["comments"]
        comment_thread = state["comment_thread"] or []
        private_comments = state.get("private_comments", [])
        private_comment_thread = state.get("private_comment_thread", [])
        configuration = state["configuration"] or {}


        # Load and format the analysis prompt
        prompt_data = load_prompt("ai_summaries_analysis")
        instructions = configuration.get("instructions", "")
        logger.info("Instructions for analysis: %s", instructions if instructions else "None provided")

        analysis_prompt = format_prompt(
            prompt_data,
            ticket_data_json=f"{ticket_data}" if ticket_data else 'No ticket data',
            comments_json=f"{comments}" if comments else 'No comments',
            comment_thread_json=f"{comment_thread}" if comment_thread else 'No comment thread',
            private_comments_json=f"{private_comments}" if private_comments else 'No private comments',
            private_comment_thread_json=f"{private_comment_thread}" if private_comment_thread else 'No private comment thread',
            instructions=instructions if instructions else 'No instructions'
        )

        logger.info("Analysis prompt prepared for ticket %s", state["ticket_id"])

        response = await call_openai_api_directly(
            system_prompt="",
            user_message=analysis_prompt,
            temperature=0.1,
            max_tokens=1000,
            model="gpt-4.1"
        )

        # Parse and validate analysis response
        state["analysis"] = response
        logger.info("Analysis completed for ticket %s", state["ticket_id"])
        
      

        # Extract JSON from potential markdown code block
        content = response
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Ticket analysis completed by : {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        try:
            # Try to parse the raw content first
            analysis = json.loads(content.strip())
        except json.JSONDecodeError as json_error:
            logger.warning(
                f"Initial JSON parsing failed, attempting to extract from markdown: {str(json_error)}"
            )

            # Check if response is in a code block
            if "```json" in content or "```" in content:
                # Extract content from code block
                if "```json" in content:
                    start_marker = "```json"
                    end_marker = "```"
                else:
                    start_marker = "```"
                    end_marker = "```"

                start_idx = content.find(start_marker) + len(start_marker)
                end_idx = content.find(end_marker, start_idx)

                if start_idx != -1 and end_idx != -1:
                    json_str = content[start_idx:end_idx].strip()
                    logger.info(
                        f"Extracted JSON from code block: {json_str}"
                    )
                    analysis = json.loads(json_str)
                else:
                    # Try to find JSON object directly
                    start_idx = content.find("{")
                    end_idx = content.rfind("}") + 1

                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_str = content[start_idx:end_idx]
                        logger.info(
                            f"Extracted JSON object directly: {json_str}"
                        )
                        analysis = json.loads(json_str)
                    else:
                        raise ValueError(
                            f"Could not extract valid JSON from response: {content}"
                        )
            else:
                raise ValueError(
                    f"Response does not contain valid JSON or code block: {content}"
                )

        state["analysis"] = analysis

        logger.info("Graph: Successfully analyzed ticket")
        return state

    except Exception as e:
        error_msg = f"Error analyzing ticket: {str(e)}"
        logger.error(f"Graph: Node execution failed: {error_msg}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Ticket analysis failed by : {agent_name} and error : {error_msg}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        state["error"] = error_msg
        raise


async def generate_summary_node(state: SummaryState) -> SummaryState:
    """Generate final summaries based on analysis."""
    logger.info("Graph: Entering generate_summary node")
    
    # Initialize variables that will be used in error handling
    agent_name = state["agent_data"]["name"] if state.get("agent_data") else "Unknown Agent"
    bot_sub = state["agent_data"]["bot_sub"] if state.get("agent_data") else None
    team_id = state["ticket_data"]["teamId"] if state.get("ticket_data") else None
    api_key = state["agent_data"]["bot_token"] if state.get("agent_data") else None
    
    try:
        # Get analysis and ticket data
        analysis = state["analysis"]
        ticket_data = state["ticket_data"]
        configuration = state["configuration"] or {}
        instructions = configuration.get("instructions","")
        
        # Get comments and threads
        comments = state["comments"] or []
        comment_thread = state["comment_thread"] or []
        private_comments = state.get("private_comments", [])
        private_comment_thread = state.get("private_comment_thread", [])
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]

        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Starting ticket summary creation by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        # Load and format the summary generation prompt
        prompt_data = load_prompt("ai_summaries_generation")
        summary_prompt = format_prompt(
            prompt_data,
            analysis_json=f"{analysis}" if analysis else 'No analysis',
            ticket_data_json=f"{ticket_data}" if ticket_data else 'No ticket data',
            comments_json=f"{comments}" if comments else 'No comments',
            comment_thread_json=f"{comment_thread}" if comment_thread else 'No comment thread',
            private_comments_json=f"{private_comments}" if private_comments else 'No private comments',
            private_comment_thread_json=f"{private_comment_thread}" if private_comment_thread else 'No private comment thread',
            instructions=f"{instructions}" if instructions else 'No additional instructions'
        )
        logger.info("Summary generation prompt prepared for ticket %s", state["ticket_id"])

        response = await call_openai_api_directly(
            system_prompt="",
            user_message=summary_prompt,
            temperature=0.1,
            max_tokens=1000,
            model="gpt-4.1"
        )

        logger.info("Summary generation completed for ticket %s", state["ticket_id"])

        # Parse the response to extract the JSON
        raw_response = response.strip()
        logger.info(f"Raw LLM response received")

        # Extract JSON from code blocks if present
        if raw_response.startswith("```"):
            try:
                # Find the content between code blocks
                start_marker = "```json\n" if "```json" in raw_response else "```\n"
                end_marker = "\n```"

                start_idx = raw_response.find(start_marker) + len(start_marker)
                end_idx = raw_response.find(end_marker, start_idx)

                if start_idx != -1 and end_idx != -1:
                    json_str = raw_response[start_idx:end_idx].strip()
                    logger.info("Extracted JSON from code block")
                else:
                    json_str = raw_response
            except Exception as e:
                logger.error(f"Error extracting JSON from code block: {e}")
                state["error"] = f"Error extracting JSON: {str(e)}"
                raise ValueError(f"Error extracting JSON: {str(e)}")
        else:
            json_str = raw_response

        try:
            summary = json.loads(json_str)
        except json.JSONDecodeError as je:
            error_msg = f"JSON parsing error: {je}"
            logger.error(error_msg)
            state["error"] = error_msg
            raise ValueError(error_msg)

        # Update state with summaries
        state["tldr"] = summary.get("tldr", "")
        state["detailed_summary"] = summary.get("detailed_summary", "")
        state["key_metadata"] = summary.get("key_metadata", {})

        logger.info("Successfully generated summaries for ticket %s", state["ticket_id"])
        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Ticket summary created by {agent_name}",
            metadata={
                "tldr": state["tldr"],
                "detailed_summary": state["detailed_summary"],
                "key_metadata": state["key_metadata"],
            },
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )

        return state

    except Exception as e:
        error_msg = f"Error generating summaries: {str(e)}"
        logger.error(f"Graph: Node execution failed: {error_msg}")
        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Ticket summary creation failed by {agent_name} and error : {error_msg}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        raise


async def update_ticket_node(state: SummaryState) -> SummaryState:
    """Update ticket with summaries."""
    logger.info("Graph: Entering update_ticket node")

    try:
        # Update ticket with summaries

        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]
        x_org_id = state["x_org_id"]
        
        ticket_id = state["ticket_id"]
        
        # Get summary fields
        tldr = state["tldr"]
        detailed_summary = state["detailed_summary"]
        
        # Combine the HTML content with proper spacing
        formatted_summary = f"{tldr}\n{detailed_summary}"
        
        logger.info(f"Processed HTML summary content")
        
        # Prepare update payload
        update_payload = {
            "aiGeneratedSummary": formatted_summary,
        }
        logger.info(f"Updating ticket {ticket_id} with formatted summary")
        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Updating ticket Ai summary by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )   

        url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}"
        headers = {"x-api-key": api_key, "x-org-id": x_org_id}

        async with httpx.AsyncClient() as client:
            response = await client.patch(url, headers=headers, json=update_payload)
            response.raise_for_status()

        return state

    except Exception as e:
        error_msg = f"Error updating ticket: {str(e)}"
        logger.error(f"Graph: Node execution failed: {error_msg}")
        await record_audit_log(   
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Updating ticket Ai summary by {agent_name} failed with error : {error_msg}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )
        raise


def create_workflow() -> StateGraph:
    """Create the summary workflow."""
    logger.info("Graph: Creating workflow")

    workflow = StateGraph(SummaryState)

    # Add nodes
    workflow.add_node("get_ticket_info", get_ticket_info_node)
    workflow.add_node("analyze_ticket", analyze_ticket_node)
    workflow.add_node("generate_summary", generate_summary_node)
    workflow.add_node("update_ticket", update_ticket_node)

    # Set up the flow
    workflow.set_entry_point("get_ticket_info")
    workflow.add_edge("get_ticket_info", "analyze_ticket")
    workflow.add_edge("analyze_ticket", "generate_summary")
    workflow.add_edge("generate_summary", "update_ticket")
    workflow.add_edge("update_ticket", END)

    logger.info("Graph: Workflow compiled and ready")

    return workflow.compile()


async def execute_flow(**inputs: dict[str, Any]) -> dict[str, Any]:
    """Execute the summary workflow."""
    logger.info("Graph: Starting workflow execution")

    try:
        logger.info("Graph: Raw input received", {"raw_input": json.dumps(inputs, indent=2, default=str)})

        ticket_id = None
        comment_id = None
        parentCommentId = None

        logger.info("Ticket info extraction started")
        
        if isinstance(inputs, dict):
            # Try different possible paths to find ticket info
            if "input" in inputs and isinstance(inputs["input"], dict):
                input_data = inputs["input"]
                
                # Path 1: input.ticket.input.ticket (current structure)
                if ("ticket" in input_data and isinstance(input_data["ticket"], dict) and
                    "input" in input_data["ticket"] and isinstance(input_data["ticket"]["input"], dict) and
                    "ticket" in input_data["ticket"]["input"] and isinstance(input_data["ticket"]["input"]["ticket"], dict)):
                    ticket_data = input_data["ticket"]["input"]["ticket"]
                    ticket_id = ticket_data.get("ticket_id")
                
                # Path 2: input.ticket (simpler structure)
                elif "ticket" in input_data and isinstance(input_data["ticket"], dict):
                    ticket_data = input_data["ticket"]
                    ticket_id = ticket_data.get("ticket_id") or ticket_data.get("id")

        # Log extracted fields
        logger.info(f"Graph: Extracted fields: {ticket_id}")
        org_id = inputs.get("input", {}).get("org_id")
        agent_id = inputs.get("agent_id")
        flow_id = inputs.get("flow_id")
        agent_config = inputs.get("input", {}).get("agent_config", {})

        # Initialize state with inputs
        initial_state = SummaryState(
            ticket_id=ticket_id,
            agent_id=agent_id,
            org_id=org_id,
            flow_id=flow_id,
            config=agent_config,
        )

        # Log state initialization
        logger.info("Graph: State initialization")

        # Validate state with detailed logging
        if not initial_state["ticket_id"]:
            logger.error("Graph: Missing ticket_id in state")
            raise ValueError("ticket_id is required")

        if not initial_state["agent_id"]:
            logger.error("Graph: Missing agent_id in state")
            raise ValueError("agent_id is required")

        if not initial_state["org_id"]:
            logger.error("Graph: Missing org_id in state")
            raise ValueError("org_id is required")

        if not initial_state["flow_id"]:
            logger.error("Graph: Missing flow_id in state")
            raise ValueError("flow_id is required")

        # Create and execute workflow
        workflow = create_workflow()
        final_state_dict = dict(await workflow.ainvoke(initial_state))

        # Return results
        return {
            "status": True,
            "data": {
                "tldr": final_state_dict.get("tldr", ""),
                "detailed_summary": final_state_dict.get("detailed_summary", ""),
                "key_metadata": final_state_dict.get("key_metadata", {}),
            },
        }

    except Exception as e:
        error_msg = f"Error executing summary workflow: {str(e)}"
        logger.error(error_msg)
        return {
            "status": False,
            "message": error_msg,
            "data": {"tldr": "", "detailed_summary": "", "key_metadata": {}},
        }
