import json
import traceback
from typing import Any, Dict, List, Optional
from uuid import UUID

import httpx
from langchain.tools.base import ToolException
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool, ToolException, tool
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langgraph.graph import END, StateGraph

from ...core.config import get_config
from ...core.database import get_supabase
from ...models.flow_models import ResponseState, SubTeamResponse, TicketComment
from ...prompt_library import format_prompt, load_prompt
from ...services.memory.fact_store import get_fact_store
from ...services.memory.ner import EntityRecognizer
from ...utils.llm_utils import call_openai_api_directly, get_llm_instance
from ...utils.logger import get_logger
from ...utils.audit_logger import record_audit_log, AuditLogEntityType, AuditLogOp, AuditLogVisibility

# Get settings for API URLs
settings = get_config()
THENA_PLATFORM_URL = settings.THENA_PLATFORM_URL
THENA_EMAIL_URL = settings.THENA_EMAIL_URL

logger = get_logger("ai_ticket_deflection_flow")
llm = get_llm_instance()


async def get_ticket_comments(
    ticket_id: str, api_key: str, x_org_id: str, limit: int = 20, page: int = 0
) -> List[TicketComment]:
    """Get comments for a ticket."""
    url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}/comments"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}
    params = {
        "limit": limit,
        "page": page,
        "commentType": "comment",
        "visibility": "public",
    }

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()

            api_response = response.json()
            if not api_response.get("status"):
                error_msg = f"API error: {api_response.get('message', 'No error message provided')}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            return [
                TicketComment(**comment) for comment in api_response.get("data", [])
            ]

        except httpx.HTTPError as e:
            error_msg = f"HTTP error occurred: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except KeyError as e:
            error_msg = f"Missing required field in response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)


async def get_comment_threads(
    comment_id: str, api_key: str, x_org_id: str
) -> List[Dict[str, Any]]:
    """Get all comments in a thread for a specific comment."""
    url = f"{THENA_PLATFORM_URL}/v1/comments/{comment_id}/threads"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.get(url, headers=headers)
            response.raise_for_status()

            api_response = response.json()
            if not api_response.get("status"):
                error_msg = f"API error: {api_response.get('message', 'No error message provided')}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            return api_response.get("data", [])

        except httpx.HTTPError as e:
            error_msg = f"HTTP error occurred: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        except KeyError as e:
            error_msg = f"Missing required field in response: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)


async def get_ticket_info_node(state: ResponseState) -> ResponseState:
    """Fetch ticket information, comments, and available statuses."""
    try:
        db = await get_supabase()

        agent_result = (
            await db.table("agents")
            .select("name", "bot_sub","bot_token", "avatar_url", "organization_id")
            .eq("id", state["agent_id"])
            .execute()
        )
        state["agent_data"] = agent_result.data[0]

        api_key = agent_result.data[0]["bot_token"]
        bot_sub = agent_result.data[0]["bot_sub"]
        agent_name = agent_result.data[0]["name"]
        avatar_url = agent_result.data[0]["avatar_url"]
        org_id = agent_result.data[0]["organization_id"]

        org_result = (
            await db.table("organizations")
            .select("x-org-id")
            .eq("id", org_id)
            .execute()
        )
        if not org_result.data or not org_result.data[0].get("x-org-id"):
            error_msg = f"Organization {org_id} not found or x-org-id missing"
            logger.error(error_msg)
            raise ValueError(error_msg)
        x_org_id = org_result.data[0]["x-org-id"]
        state["x_org_id"] = x_org_id

        # Get user instruction
        configuration = (
            await db.table("agent_flows")
            .select("configuration")
            .eq("agent_id", state["agent_id"])
            .eq("flow_id", state["flow_id"])
            .execute()
        )
        if configuration.data and len(configuration.data) > 0:
            state["configuration"] = configuration.data[0]["configuration"]
        else:
            state["configuration"] = {
                "configuration": {
                    "instructions": "No instructions provided",
                }
            } 
        async with httpx.AsyncClient(timeout=60.0) as client:
            # Get ticket data
            url = f"{THENA_PLATFORM_URL}/v1/tickets/{state['ticket_id']}"
            headers = {"x-api-key": api_key, "x-org-id": x_org_id}

            response = await client.get(url, headers=headers)
            response.raise_for_status()
            ticket_data = response.json()["data"]
            state["ticket_data"] = ticket_data
            team_id = state["ticket_data"]["teamId"]
            state["source"] = ticket_data["source"]

            # Get parent comments
            state["comments"] = await get_ticket_comments(
                state["ticket_id"], api_key, x_org_id
            )

            # Initialize all_comments with parent comments
            all_comments = []

            # Only fetch thread comments for the latest comment if it exists and has replies
            if state["comments"] and len(state["comments"]) > 0:
                latest_comment = state["comments"][-1]
                if latest_comment.metadata and latest_comment.metadata.get("replies"):
                    try:
                        thread_comments = await get_comment_threads(
                            latest_comment.id, api_key, x_org_id
                        )
                        # Convert thread comments to TicketComment objects
                        thread_comments = [TicketComment(**c) for c in thread_comments]
                        all_comments.extend(thread_comments)
                    except Exception as e:
                        logger.exception(
                            f"Error fetching thread comments for latest comment {latest_comment.id}"
                        )

            # Update state with all comments (parent + thread)
            state["comment_thread"] = all_comments
            await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Ticket info fetched successfully by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )

            return state

    except Exception as e:
        logger.error(f"Error in get_ticket_info: {str(e)}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Error fetching ticket info by {agent_name}",
            metadata={"error": str(e)},
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )
        raise


async def get_context_node(state: ResponseState) -> ResponseState:
    """Get context data including entities, facts and relevant file content."""
    try:
        # Initialize EntityRecognizer directly
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]
        x_org_id = state["x_org_id"]
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Context fetching started by {agent_name}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )
        ner = EntityRecognizer()

        # Combine ticket title and description for context
        ticket_content = (
            f"{state['ticket_data']['title']}\n{state['ticket_data']['description']}"
        )

        # 1. Get entities and facts
        entity_data, _ = await ner.process_message(
            text=ticket_content, org_id=str(state["org_id"])
        )

        # Extract entity names
        entities = set()
        for entity in entity_data:
            entity_name = entity["name"]
            entities.add(entity_name)

        # Get facts from fact store
        fact_store = await get_fact_store()
        entity_ids = [e["id"] for e in entity_data]
        existing_facts = await fact_store.get_facts_for_entities(
            str(state["org_id"]), entity_ids
        )

        # Process facts
        facts = {}
        for fact_item in existing_facts:
            fact = fact_item["fact"]
            # Find matching entity
            matching_entities = [
                entity for entity in entities if entity.lower() in fact.lower()
            ]
            if matching_entities:
                entity = max(matching_entities, key=len)
                if entity not in facts:
                    facts[entity] = []
                facts[entity].append(fact)

        # 2. Search context from files
        from ...services.context_service import get_context_service

        context_service = await get_context_service()

        # Search using ticket content
        results = await context_service.search_context(
            agent_id=UUID(state["agent_id"]),
            query=ticket_content,
            organization_id=UUID(state["org_id"]),
            top_k=5,
            similarity_threshold=0.7,
        )

        # Store all context data in state
        if not results:
            state["context_data"] = {
                "entities": [],
                "facts": {},
                "search_results": [],
                "message": "No relevant files or facts available",
            }
        else:
            state["context_data"] = {
                "entities": list(entities),
                "facts": facts,
                "search_results": [
                    {
                        "content": r["content"],
                        "file_name": r["file_name"],
                        "similarity": r["similarity"],
                    }
                    for r in results
                ],
            }

        logger.info("Context data gathered successfully")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.INFO,
            description=f"Context fetched successfully by {agent_name}",
            metadata={"context": results},
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )

        return state

    except Exception as e:
        error_msg = f"Error getting context data: {str(e)}"
        state["context_data"] = {
            "error": error_msg,
            "entities": [],
            "facts": {},
            "search_results": [],
        }
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Error fetching context by {agent_name}",
            metadata={"error": error_msg},
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )
        return state


async def small_llm_response_node(state: ResponseState) -> ResponseState:
    """Generate response based on ticket context and other details."""
    try:
        # Get tools instance and bind to LLM
        tools_instance = await get_ticket_tools(
            organization_id=UUID(state["org_id"]) if state["org_id"] else None,
            agent_id=UUID(state["agent_id"]) if state["agent_id"] else None,
        )
        llm_with_tools = llm.bind_tools(tools=tools_instance.get_tools())

        # Get ticket info directly from state
        ticket_info = state["ticket_data"]
        comments_thread = state.get("comment_thread", [])
        latest_comment = comments_thread[-1] if comments_thread else None
        comments = state.get("comments", [])
        context_data = state.get("context_data", {})
        agent_config = state.get("agent_config", {})

        # Ensure we have a valid ticket ID
        if not ticket_info.get("id"):
            return state

        configuration = state.get("configuration", {})
        instructions = configuration.get("instructions", "")

        context_json = json.dumps(context_data, indent=2)
        feedback = state.get("judge_llm_feedback", "")

        # Load and format the analysis prompt
        prompt_data = load_prompt("ai_ticket_deflection_analysis")

        # Add debug logger
        logger.info(f"Prompt data metadata: {prompt_data['metadata']}")
        logger.info(
            f"Required params: {prompt_data['metadata'].get('required_params', [])}"
        )

        response_prompt = format_prompt(
            prompt_data,
            agent_config=str(agent_config if agent_config else ""),
            ticket_id=str(ticket_info["id"]),
            ticket_title=str(ticket_info["title"]),
            ticket_description=str(ticket_info["description"]),
            ticket_status=str(ticket_info["status"]),
            ticket_priority=str(ticket_info["priority"]),
            ticket_assigned_agent=str(ticket_info.get("assignedAgent", "Unassigned")),
            ticket_created_at=str(ticket_info["createdAt"]),
            ticket_updated_at=str(ticket_info["updatedAt"]),
            ticket_ai_summary=str(ticket_info.get("aiGeneratedSummary", "")),
            latest_comment_text=str(latest_comment if latest_comment else ""),
            previous_comments_text=str(comments if comments else ""),
            context_json=str(context_json if context_json else ""),
            instructions=str(instructions if instructions else ""),
            judge_llm_feedback=str(feedback if feedback else ""),
        )

        # Log the formatted prompt
        logger.info(f"Prompt length: {len(response_prompt)}")
        logger.info(f"Prompt: {response_prompt}")
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]
        x_org_id = state["x_org_id"]

        # First get analysis from base LLM
        response = await llm.ainvoke([HumanMessage(content=response_prompt)])
        try:
            # Extract JSON from markdown code block
            content = response.content

            # Try finding JSON code block first
            JSON_BLOCK_START = "```json\n"
            CODE_BLOCK_START = "```\n"
            BLOCK_END = "\n```"

            # Look for JSON block marker
            block_pos = content.rfind(JSON_BLOCK_START)
            if block_pos != -1:
                json_start = block_pos + len(JSON_BLOCK_START)
            else:
                # Fallback: look for generic code block
                block_pos = content.rfind(CODE_BLOCK_START)
                json_start = (
                    block_pos + len(CODE_BLOCK_START) if block_pos != -1 else -1
                )

            if json_start != -1:  # Found a code block
                json_end = content.rfind(BLOCK_END)
                if json_end != -1:
                    json_str = content[json_start:json_end].strip()
                    try:
                        result = json.loads(json_str)
                        logger.info(f"Parsed JSON result: {result}")
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse JSON from code block: {e}")
                        raise ValueError("Invalid JSON response from LLM")
                else:
                    raise ValueError("Malformed code block in LLM response")
            else:
                # Fallback: try parsing the entire content as JSON
                try:
                    result = json.loads(content)
                    logger.info(f"Parsed full content as JSON: {result}")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse response as JSON: {e}")
                    raise ValueError("Invalid JSON response from LLM")

            # Check if user is frustrated and needs subteam assignment
            if (
                result.get("is_frustrated", False)
                and result.get("frustration_confidence", 0) > 0.7
            ):
                # Load and format the subteam prompt
                subteam_prompt_data = load_prompt("ai_ticket_deflection_subteam")

                # Add debug logger
                logger.info(
                    f"Subteam prompt metadata: {subteam_prompt_data['metadata']}"
                )
                logger.info(
                    f"Required params: {subteam_prompt_data['metadata'].get('required_params', [])}"
                )

                latest_msg = str(
                    comments[-1].content if comments else ticket_info["description"]
                )
                subteam_prompt = format_prompt(
                    subteam_prompt_data,
                    latest_message=latest_msg,
                    instructions=str(instructions if instructions else ""),
                    team_id=str(ticket_info["teamId"]),
                    ticket_id=str(ticket_info["id"]),
                    flow_id=str(state["flow_id"]),
                    context_json=str(context_json if context_json else ""),
                )

                # Log the formatted prompt
                logger.info(f"Subteam prompt length: {len(subteam_prompt)}")
                logger.info(
                    f"First 200 chars of subteam prompt: {subteam_prompt[:200]}"
                )

                tool_response = await llm_with_tools.ainvoke(
                    [HumanMessage(content=subteam_prompt)]
                )

                # Log the raw tool response
                logger.info(
                    "Raw tool response",
                    extra={
                        "raw_response": tool_response.content,
                        "response_type": str(type(tool_response)),
                        "has_content": hasattr(tool_response, "content"),
                    },
                )

                # Execute tool calls if present
                if tool_response.additional_kwargs.get("tool_calls"):
                    tool_call = tool_response.additional_kwargs["tool_calls"][0]
                    if tool_call["function"]["name"] == "assign_to_subteam":
                        args = json.loads(tool_call["function"]["arguments"])
                        # Get the actual tool function
                        tool_fn = tools_instance.get_tools()[
                            0
                        ]  # assign_to_subteam is the first tool
                        tool_result = await tool_fn.ainvoke(
                            input={
                                "ticket_id": args["ticket_id"],
                                "team_id": args["team_id"],
                                "user_id": args.get(
                                    "user_id", "UNASSIGN"
                                ),  # Default to UNASSIGN if not provided
                                "flow_id": args["flow_id"],
                            }
                        )

                        # If successfully assigned, update state with frustration info and response
                        if tool_result and tool_result.get("assigned_team_name"):
                            state[
                                "response"
                            ] = f"""I understand your concern, and I'm escalating this to our {tool_result['assigned_team_name']} team who will be better equipped to help you. A team member will review your case and get back to you shortly.

{tool_result.get('message', '')}"""
                            state["confidence"] = 0.95
                            state[
                                "reasoning"
                            ] = f"User showed frustration ({result['frustration_reasoning']}). Assigned to appropriate subteam."
                            await record_audit_log(
                                entity_type=AuditLogEntityType.TICKET,
                                entity_id=state["ticket_id"],
                                operation=AuditLogOp.INFO,
                                description=f"Ticket assigned to subteam by {agent_name}",
                                metadata={"assigned_team_name": tool_result['assigned_team_name'], "reason":result['frustration_reasoning']},
                                user_id=bot_sub,
                                visibility=AuditLogVisibility.TEAM,
                                team_id=team_id,
                                x_org_id=x_org_id,
                                api_key=api_key,
                            )

                        elif tool_result and tool_result.get("assigned_user_id"):
                            state[
                                "response"
                            ] = f"""I understand your concern, and I'm escalating this to User with id {tool_result['assigned_user_id']} who will be better equipped to help you. They will review your case and get back to you shortly.

{tool_result.get('message', '')}"""
                            state["confidence"] = 0.95
                            state[
                                "reasoning"
                            ] = f"User showed frustration ({result['frustration_reasoning']}). Assigned to appropriate user."
                            await record_audit_log(
                                entity_type=AuditLogEntityType.TICKET,
                                entity_id=state["ticket_id"],
                                operation=AuditLogOp.INFO,
                                description=f"Ticket assigned to user by {agent_name}",
                                metadata={"assigned_user_id": tool_result["assigned_user_id"], "reason": result['frustration_reasoning']},
                                user_id=bot_sub,
                                visibility=AuditLogVisibility.TEAM,
                                team_id=team_id,
                                x_org_id=x_org_id,
                                api_key=api_key,
                            )
                        else:
                            # If tool call failed, fall back to normal response
                            state["reasoning"] = result.get("reasoning", "")
                            state["confidence"] = float(result.get("confidence", 0))
                            state["response"] = result.get("response", "")
                            await record_audit_log(
                                entity_type=AuditLogEntityType.TICKET,
                                entity_id=state["ticket_id"],
                                operation=AuditLogOp.INFO,
                                description=f"Tool call failed, L1 response by {agent_name}",
                                user_id=bot_sub,
                                visibility=AuditLogVisibility.TEAM,
                                team_id=team_id,
                                x_org_id=x_org_id,
                                api_key=api_key,
                            )
                else:
                    # No tool calls, fall back to normal response
                    state["reasoning"] = result.get("reasoning", "")
                    state["confidence"] = float(result.get("confidence", 0))
                    state["response"] = result.get("response", "")
                    await record_audit_log(
                        entity_type=AuditLogEntityType.TICKET,
                        entity_id=state["ticket_id"],
                        operation=AuditLogOp.INFO,
                        description=f"No tool call was done, L1 response by {agent_name}",
                        metadata={"reason": state["reasoning"], "confidence": state["confidence"]},
                        user_id=bot_sub,
                        visibility=AuditLogVisibility.TEAM,
                        team_id=team_id,
                        x_org_id=x_org_id,
                        api_key=api_key,
                    )
            else:
                # Not frustrated, use normal response
                state["reasoning"] = result.get("reasoning", "")
                state["confidence"] = float(result.get("confidence", 0))
                state["response"] = result.get("response", "")
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.INFO,
                    description=f"L1 Response generated by {agent_name}",
                    metadata={"reason": state["reasoning"], "confidence": state["confidence"]},
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )

            # Always store frustration analysis
            state["is_frustrated"] = result.get("is_frustrated", False)
            state["frustration_confidence"] = float(
                result.get("frustration_confidence", 0)
            )
            state["frustration_reasoning"] = result.get("frustration_reasoning", "")

        except json.JSONDecodeError as e:
            state["error"] = f"Failed to parse analysis: {str(e)}"
            await record_audit_log(
                entity_type=AuditLogEntityType.TICKET,
                entity_id=state["ticket_id"],
                operation=AuditLogOp.ERROR,
                description=f"Failed to parse analysis: {str(e)}",
                user_id=bot_sub,
                visibility=AuditLogVisibility.TEAM,
                team_id=team_id,
                x_org_id=x_org_id,
                api_key=api_key,
            )

    except Exception as e:
        state["error"] = f"Analysis error: {str(e)}"
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Analysis error: {str(e)}",
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=x_org_id,
            api_key=api_key,
        )

    return state


async def judgeLLM_analysis_node(state: ResponseState) -> ResponseState:
    """Judge the analysis using GPT-4-0 to validate the recommendation."""
    try:
        # If user was frustrated and ticket was assigned to subteam, auto-approve
        if (
            state.get("is_frustrated", False)
            and state.get("frustration_confidence", 0) > 0.7
            and "escalating this to our" in state.get("response", "")
        ):
            state["confidence"] = 0.95  # High confidence for escalation
            state["approved"] = True  # Set approval status
            state[
                "judge_llm_feedback"
            ] = "Auto-approved: Ticket was deflected to appropriate subteam due to user frustration."
            return state

        # Get relevant info from state
        ticket_info = state["ticket_data"]
        small_llm_response = {
            "reasoning": state["reasoning"],
            "confidence": state["confidence"],
            "response": state["response"],
            "is_frustrated": state.get("is_frustrated", False),
            "frustration_confidence": state.get("frustration_confidence", 0),
            "frustration_reasoning": state.get("frustration_reasoning", ""),
        }

        instructions = state.get("instructions")

        # Load and format the judge prompt
        judge_prompt_data = load_prompt("ai_ticket_deflection_judge")

        # Add debug logger
        logger.info(f"Judge prompt metadata: {judge_prompt_data['metadata']}")
        logger.info(
            f"Required params: {judge_prompt_data['metadata'].get('required_params', [])}"
        )

        base_prompt = format_prompt(
            judge_prompt_data,
            ticket_analysis=str(json.dumps(small_llm_response)),
            instructions=str(instructions if instructions else ""),
        )

        # Log the formatted prompt
        logger.info(f"Judge prompt length: {len(base_prompt)}")
        logger.info(f"First 200 chars of judge prompt: {base_prompt[:200]}")

        response = await llm.ainvoke([HumanMessage(content=base_prompt)])
        try:
            # Extract JSON from potential markdown code block
            content = response.content
            if "```json" in content:
                # Find content between ```json and ```
                start = content.find("{", content.find("```json"))
                end = content.rfind("}") + 1
            else:
                # Find the first JSON object
                start = content.find("{")
                end = content.rfind("}") + 1

            if start != -1 and end != -1:
                json_str = content[start:end]
                judgment = json.loads(json_str)
            else:
                raise ValueError("No JSON object found in response")

            # Adjust confidence based on judge's evaluation
            state["confidence"] = float(judgment.get("confidence", 0))
            approve = judgment.get("approved", False)
            # Increment attempt counter
            state["attempt_count"] += 1
            # Set ticket updates if confidence is high enough
            if approve == True:
                state["response"] = state["response"]
            # Store analysis and reset if confidence not high enough and attempts re
            if approve == False and state["attempt_count"] < 2:
                state["judge_llm_feedback"] = judgment.get("feedback", "")

        except (json.JSONDecodeError, ValueError) as e:
            state["error"] = f"Failed to parse judge's analysis: {str(e)}"

    except Exception as e:
        state["error"] = f"Judge analysis error: {str(e)}"

    return state


async def ticket_response_node(state: ResponseState) -> ResponseState:
    """Update the ticket status via API call."""
    try:
        logger.info(f"Ticket response node started: {state}")
        ticket_id = state["ticket_id"]
        response = state["response"]
        reasoning = state["reasoning"]
        confidence = state["confidence"]
        parentCommentId = state.get("parentCommentId", "")
        agent_id = state.get("agent_id", "")
        org_id = state.get("org_id", "")
        source = state.get("source", "")
        configuration = state.get("configuration",{})
        instructions = configuration.get("instructions", "")
        # Get API credentials from database
        db = await get_supabase()
        agent_name = state["agent_data"]["name"]
        bot_sub = state["agent_data"]["bot_sub"]
        team_id = state["ticket_data"]["teamId"]
        api_key = state["agent_data"]["bot_token"]
        x_org_id = state["x_org_id"]
        avatar_url = state["agent_data"]["avatar_url"]


        logger.info(f"Response: {response}")

        # Set up headers
        headers = {
            "Content-Type": "application/json",
            "x-api-key": api_key,
            "x-org-id": x_org_id,
        }

        # Find parent comment based on source, author and commentType - iterate in reverse to get earliest comment
        if not parentCommentId and state.get("comments") and len(state["comments"]) > 0:
            if source.lower() == "slack":
                # For Slack, find the earliest slack message by iterating in reverse
                for comment in reversed(state["comments"]):
                    author = (
                        comment.author
                        if isinstance(comment, TicketComment)
                        else comment.get("author", "")
                    )
                    comment_id = (
                        comment.id
                        if isinstance(comment, TicketComment)
                        else comment.get("id")
                    )
                    comment_type = (
                        comment.commentType
                        if isinstance(comment, TicketComment)
                        else comment.get("commentType", "")
                    )

                    if "slack" in author.lower() and comment_type == "comment":
                        parentCommentId = comment_id
                        break
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.INFO,
                    description=f"Starting to add response to Slack and ticket by {agent_name}",
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )

            elif source.lower() == "email":
                # For email, take the first non-bot comment
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.INFO,
                    description=f"Starting to add response to email and ticket by {agent_name}",
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=x_org_id,
                    api_key=api_key,
                )
                for comment in state["comments"]:
                    comment_id = (
                        comment.id
                        if isinstance(comment, TicketComment)
                        else comment.get("id")
                    )
                    author_type = (
                        comment.authorUserType
                        if isinstance(comment, TicketComment)
                        else comment.get("authorUserType", "")
                    )

                    if isinstance(comment, TicketComment):
                        if comment.authorUserType != "BOT_USER":
                            parentCommentId = comment_id
                            break
                    else:
                        if comment.get("authorUserType", "") != "BOT_USER":
                            parentCommentId = comment_id
                            break

        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                # Convert response to properly formatted HTML using LLM
                prompt = load_prompt("flow_html_generation")
                formatted_prompt = format_prompt(
                    prompt,
                    text=str(response),
                    instructions=str(instructions if instructions else "No instructions available")
                )

                html_content = await call_openai_api_directly(
                    system_prompt=formatted_prompt,
                    user_message=response,
                    temperature=0,  # Keep it deterministic
                    max_tokens=1000,
                    model="gpt-4.1"  # Using GPT-4 for better formatting
                )
                
                comment_payload = {
                    "content": f"{response}",
                    "parentCommentId": parentCommentId,
                    "contentHtml": html_content,
                    "impersonatedUserAvatar": f"{avatar_url}",
                    "commentType": "comment",
                    "metadata": {"ignoreSelf": False, "isAgent": True},
                    "impersonatedUserName": f"{agent_name}",
                }

                # For Slack source, add contentJson and copy metadata from the latest comment
                if (
                    source.lower() == "slack"
                    and state.get("comments")
                    and len(state["comments"]) > 0
                ):
                    # Get the latest comment
                    latest_comment = state["comments"][-1]

                    # Access metadata based on whether it's a Pydantic model or dict
                    if isinstance(latest_comment, TicketComment):
                        # Access metadata directly as a property of the Pydantic model
                        comment_metadata = latest_comment.metadata
                    else:
                        # If it's already a dict, access it with get
                        comment_metadata = latest_comment.get("metadata", {})

                    # Add contentJson field with proper document structure format
                    content_json = {
                        "type": "doc",
                        "content": [
                            {
                                "type": "paragraph",
                                "attrs": {"textAlign": "left"},
                                "content": [{"type": "text", "text": f"{response}"}],
                            }
                        ],
                    }
                    comment_payload["contentJson"] = json.dumps(content_json)

                    # Copy metadata but ensure ignoreSelf is False
                    if (
                        isinstance(comment_metadata, dict)
                        and "external_sinks" in comment_metadata
                        and "slack" in comment_metadata["external_sinks"]
                    ):
                        slack_metadata = comment_metadata["external_sinks"][
                            "slack"
                        ].copy()

                        # Update timestamp to be slightly higher than the latest comment
                        if "ts" in slack_metadata:
                            try:
                                current_ts = float(slack_metadata["ts"])
                                slack_metadata["ts"] = str(current_ts + 1)

                                # Also update threadTs if it exists
                                if "threadTs" in slack_metadata:
                                    slack_metadata["threadTs"] = slack_metadata[
                                        "threadTs"
                                    ]
                            except (ValueError, TypeError):
                                # If conversion fails, keep the original timestamp
                                pass

                        # Set ignoreSelf to False
                        slack_metadata["ignoreSelf"] = False

                        # Create a deep copy of the metadata structure
                        new_metadata = {
                            "replies": comment_metadata.get("replies", []),
                            "isAgent": True,
                            "mentions": comment_metadata.get("mentions", []),
                            "external_sinks": {"slack": slack_metadata},
                            "userReactions": comment_metadata.get("userReactions", []),
                        }

                        comment_payload["metadata"] = new_metadata
                        logger.info(f"Comment payload: {comment_payload}")

                # For Email source, fetch thread comments and set up email headers
                elif source.lower() == "email" and parentCommentId:
                    # Add contentJson field with proper document structure format
                    content_json = {
                        "type": "doc",
                        "content": [
                            {
                                "type": "paragraph",
                                "attrs": {"textAlign": "left"},
                                "content": [{"type": "text", "text": f"{response}"}],
                            }
                        ],
                    }
                    comment_payload["contentJson"] = json.dumps(content_json)
                    comment_payload["parentCommentId"] = parentCommentId

                    try:
                        # Get team ID from ticket data
                        team_id = None
                        if state.get("ticket_data"):
                            team_id = state["ticket_data"].get("teamId")

                        # Initialize metadata structure
                        new_metadata = {
                            "email": {
                                "to": [],
                                "cc": [],
                                "bcc": [],
                                "from": {},
                                "headers": [],
                                "isIgnoreSelf": False,
                                "subject": "",
                            },
                            "isAgent": True,
                        }

                        # First, get email metadata from first comment (if available)
                        if state.get("comments") and len(state["comments"]) > 0:
                            first_comment = state["comments"][0]

                            # Access metadata based on whether it's a Pydantic model or dict
                            if isinstance(first_comment, TicketComment):
                                first_metadata = first_comment.metadata
                                if isinstance(first_metadata, dict):
                                    first_metadata = first_metadata
                            else:
                                # If it's already a dict, access it with get
                                first_metadata = first_comment.get("metadata", {})

                            # Get email data from first comment
                            if (
                                isinstance(first_metadata, dict)
                                and "email" in first_metadata
                            ):
                                email_data = first_metadata["email"]

                                # For replies, we swap the from and to fields
                                if "from" in email_data and isinstance(
                                    email_data["from"], dict
                                ):
                                    new_metadata["email"]["to"] = [email_data["from"]]

                                # Get CC and BCC from original email
                                new_metadata["email"]["cc"] = email_data.get("cc", [])
                                new_metadata["email"]["bcc"] = email_data.get("bcc", [])
                                new_metadata["email"]["headers"] = email_data.get(
                                    "headers", []
                                )
                                new_metadata["email"]["subject"] = email_data.get(
                                    "subject", ""
                                )
                                new_metadata["email"]["isIgnoreSelf"] = False
                                new_metadata["email"]["from"] = email_data.get(
                                    "from", {}
                                )

                        # If we have a parent comment ID, try to get the latest thread comment
                        # which will override the first comment's metadata
                        if parentCommentId:
                            thread_comments = await get_comment_threads(
                                parentCommentId, api_key, x_org_id
                            )
                            if thread_comments and len(thread_comments) > 0:
                                latest_thread = thread_comments[-1]
                                thread_metadata = latest_thread.get("metadata", {})

                                # Override headers with latest thread comment's headers
                                if "headers" in thread_metadata:
                                    new_metadata["email"]["headers"] = thread_metadata[
                                        "headers"
                                    ]

                                # Override email data with latest thread comment's email data
                                if "email" in thread_metadata:
                                    email_data = thread_metadata["email"]

                                    # For replies, we swap the from and to fields
                                    if "from" in email_data and isinstance(
                                        email_data["from"], dict
                                    ):
                                        new_metadata["email"]["to"] = [
                                            email_data["from"]
                                        ]

                                    # Get CC and BCC from latest thread
                                    new_metadata["email"]["cc"] = email_data.get(
                                        "cc", []
                                    )
                                    new_metadata["email"]["bcc"] = email_data.get(
                                        "bcc", []
                                    )
                                    new_metadata["email"]["subject"] = email_data.get(
                                        "subject", ""
                                    )
                                    new_metadata["email"]["isIgnoreSelf"] = False
                                    new_metadata["email"]["from"] = email_data.get(
                                        "from", {}
                                    )

                                    # Get forwarded email from headers
                                    forwarded_email = None
                                    for header in email_data["headers"]:
                                        if header.get("Name") == "X-Forwarded-For":
                                            forwarded_email = header.get(
                                                "Value", ""
                                            ).split(" ")[0]
                                            break
                                    new_metadata["email"][
                                        "forwardedEmail"
                                    ] = forwarded_email

                        # Try to get the forwarded email from headers
                        forwarded_email = None
                        for header in new_metadata["email"]["headers"]:
                            if header.get("Name") == "X-Forwarded-For":
                                forwarded_email = header.get("Value", "").split(" ")[0]
                                break

                        # Get email configuration for the team
                        from_address = {}
                        if team_id:
                            email_configs = await get_email_config(
                                team_id, api_key, x_org_id
                            )

                            # Find matching email config based on forwarded email
                            matching_config = None
                            if forwarded_email and email_configs:
                                matching_config = next(
                                    (
                                        config
                                        for config in email_configs
                                        if config.get("customEmail") == forwarded_email
                                    ),
                                    None,
                                )

                            # If we have a matching config, use it for the from address
                            if matching_config:
                                sender_name = agent_name
                                if (
                                    matching_config.get("sendersPreferredChoice")
                                    == "original_name"
                                ):
                                    sender_name = agent_name
                                elif (
                                    matching_config.get("sendersPreferredChoice")
                                    == "both"
                                ):
                                    sender_name = f"{agent_name} | {matching_config.get('sendersPreferredChoiceValue', '')}"
                                else:
                                    sender_name = matching_config.get(
                                        "sendersPreferredChoiceValue", agent_name
                                    )

                                from_address = {
                                    "Name": sender_name,
                                    "Email": matching_config.get("customEmail", ""),
                                    "MailboxHash": "",
                                }

                        # If we couldn't get a from address but have 'to' addresses, use the first one
                        if (
                            not from_address
                            and new_metadata["email"]["to"]
                            and len(new_metadata["email"]["to"]) > 0
                        ):
                            to_email = new_metadata["email"]["to"][0].get("Email", "")
                            if to_email:
                                from_address = {
                                    "Name": agent_name,
                                    "Email": to_email,
                                    "MailboxHash": "",
                                }

                        # Set the from address in the metadata
                        new_metadata["email"]["from"] = from_address

                        # Set the metadata in the comment payload
                        comment_payload["metadata"] = new_metadata
                    except Exception as e:
                        logger.warning(f"Error setting up email metadata: {str(e)}")
                        logger.warning(traceback.format_exc())
                        # Continue without thread metadata if there's an error

                logger.info(f"Comment payload: {comment_payload}")


                comment_response = await client.post(
                    f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}/comment",
                    headers=headers,
                    json=comment_payload,
                )
                logger.info(f"Comment added: {comment_response.status_code}")
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.INFO,
                    description=f"Comment added to ticket by {agent_name}",
                    metadata={"comment": comment_response.json()},
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=state["x_org_id"],
                    api_key=api_key,
                )
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.INFO,
                    description=f"Note added to ticket by {agent_name}",
                    metadata={"reasoning": reasoning, "confidence": confidence},
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=state["x_org_id"],
                    api_key=api_key,
                )
                # add func to fetch comments that are public for ticket id and see if it has replies or not, if reply is only 1, assign the ticket to agent
                bot_result, bot_sub = None, None
                logger.info(f"Ticket id for Assigning ticket to agent: {ticket_id}")
                logger.info(f"API key for Assigning ticket to agent: {api_key}")
                logger.info(f"X-org-id for Assigning ticket to agent: {x_org_id}")
                logger.info(f"Get ticket comments")
                comments = await get_ticket_comments(ticket_id=ticket_id, api_key=api_key, x_org_id=x_org_id)
                logger.info(f"Comments for Assigning ticket to agent: {comments}")
                logger.info(f"Comments length for Assigning ticket to agent: {len(comments)}")
                if comments and len(comments) > 0:
                    logger.info(f"First comment for Assigning ticket to agent: {comments[0]}")
                    first_comment = comments[0]
                    logger.info(f"First comment for Assigning ticket to agent: {first_comment}")
                    replies = (
                        first_comment.metadata.get("replies", [])
                        if first_comment.metadata
                        else []
                    )
                    logger.info(f"Replies for Assigning ticket to agent: {replies}")
                    logger.info(f"Replies length for Assigning ticket to agent: {len(replies)}")
                    if len(replies) == 1:
                        # Get bot_sub from agents table
                        bot_result = (
                            await db.table("agents")
                            .select("bot_sub")
                            .eq("id", agent_id)
                            .execute()
                        )
                        logger.info(f"Bot result for Assigning ticket to agent: {bot_result}")
                        if bot_result.data and bot_result.data[0].get("bot_sub"):
                            bot_sub = bot_result.data[0]["bot_sub"]
                            logger.info(f"Bot sub for Assigning ticket to agent: {bot_sub}")
                            # Update ticket with bot_sub as assigned user
                            async with httpx.AsyncClient(timeout=60.0) as client:
                                url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}"
                                update_data = {"assignedAgentId": bot_sub}
                                headers = {"x-api-key": api_key, "x-org-id": x_org_id}
                                bot_response = await client.patch(url, headers=headers, json=update_data)
                                bot_response.raise_for_status()
                                logger.info(f"Successfully assigned ticket to agent: {bot_response.status_code}")
                                await record_audit_log(
                                    entity_type=AuditLogEntityType.TICKET,
                                    entity_id=state["ticket_id"],
                                    operation=AuditLogOp.INFO,
                                    description=f"Successfully assigned ticket to agent: {agent_name}",
                                    metadata={"assigned_agent": bot_sub, "reason": "As this ticket has only comment"},
                                    user_id=bot_sub,
                                    visibility=AuditLogVisibility.TEAM,
                                    team_id=team_id,
                                    x_org_id=state["x_org_id"],
                                    api_key=api_key,
                                )

                # Check HTTP status and response content
                if comment_response.status_code not in [200, 201]:
                    logger.warning(f"Failed to add response comment: {comment_response.text}")
                    await record_audit_log(
                        entity_type=AuditLogEntityType.TICKET,
                        entity_id=state["ticket_id"],
                        operation=AuditLogOp.INFO,
                        description=f"Failed to add response comment: {agent_name}",
                        metadata={"comment": comment_response.json()},
                        user_id=bot_sub,
                        visibility=AuditLogVisibility.TEAM,
                        team_id=team_id,
                        x_org_id=state["x_org_id"],
                        api_key=api_key,
                    )
                    raise ValueError(f"Failed to add response comment: HTTP {comment_response.status_code}")

                if bot_response is not None and bot_response.status_code not in [200, 201]:
                    logger.warning(f"Failed to assign ticket to agent: {bot_response.text}")
                    await record_audit_log(
                        entity_type=AuditLogEntityType.TICKET,
                        entity_id=state["ticket_id"],
                        operation=AuditLogOp.INFO,
                        description=f"Failed to assign ticket to agent: {agent_name}",
                        metadata={"bot_response": bot_response.json()},
                        user_id=bot_sub,
                        visibility=AuditLogVisibility.TEAM,
                        team_id=team_id,
                        x_org_id=state["x_org_id"],
                        api_key=api_key,
                    )
                    raise ValueError(f"Failed to assign ticket to agent: HTTP {bot_response.status_code}")

                # Log successful responses
                logger.info(f"Successfully added comment: {comment_response.status_code}")
                logger.info(f"Successfully assigned ticket to agent: {bot_response.status_code}")

                state["response_added"] = True

            except httpx.HTTPError as e:
                error_msg = f"HTTP error occurred: {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.ERROR,
                    description=f"HTTP error occurred: {agent_name}",
                    metadata={"error": error_msg},
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=state["x_org_id"],
                    api_key=api_key,
                )
                raise ValueError(error_msg)
            except Exception as e:
                error_msg = f"Error updating ticket response: {str(e)}"
                logger.error(error_msg)
                await record_audit_log(
                    entity_type=AuditLogEntityType.TICKET,
                    entity_id=state["ticket_id"],
                    operation=AuditLogOp.ERROR,
                    description=f"Error updating ticket response: {agent_name}",
                    metadata={"error": error_msg},
                    user_id=bot_sub,
                    visibility=AuditLogVisibility.TEAM,
                    team_id=team_id,
                    x_org_id=state["x_org_id"],
                    api_key=api_key,
                )
                raise ValueError(error_msg)

        return state

    except Exception as e:
        logger.error(f"Error in update_ticket_response: {str(e)}")
        await record_audit_log(
            entity_type=AuditLogEntityType.TICKET,
            entity_id=state["ticket_id"],
            operation=AuditLogOp.ERROR,
            description=f"Error in update_ticket_response: {agent_name}",
            metadata={"error": str(e)},
            user_id=bot_sub,
            visibility=AuditLogVisibility.TEAM,
            team_id=team_id,
            x_org_id=state["x_org_id"],
            api_key=api_key,
        )
        raise


async def should_continue(state: ResponseState) -> bool:
    """Determine if we should continue the analysis loop."""
    # Return True (continue) only if:
    # 1. Haven't exceeded max attempts (2 attempts)
    # 2. Confidence is below threshold
    confidence = state.get("confidence")
    return state["attempt_count"] < 2 and (  # Max 2 attempts
        confidence is None or confidence < 0.85
    )  # Continue if no confidence or below threshold


class TicketTools:
    """Tools for ticket deflection and assignment."""

    def __init__(
        self, organization_id: Optional[UUID] = None, agent_id: Optional[UUID] = None
    ):
        """
        Initialize the tools with organization and agent IDs.

        Args:
            organization_id: The UUID of the organization
            agent_id: The UUID of the agent
        """
        self.organization_id = organization_id
        self.agent_id = agent_id

    async def get_subteams(self, team_id: str, headers: dict) -> List[SubTeamResponse]:
        """Get subteams for a team."""
        async with httpx.AsyncClient(timeout=60.0) as client:
            url = f"{THENA_PLATFORM_URL}/v1/teams/{team_id}/sub-teams"
            response = await client.get(url, headers=headers)
            logger.info(f"Get subteams response: {response.json()}")
            response.raise_for_status()
            return [SubTeamResponse(**team) for team in response.json().get("data", [])]

    async def update_ticket_subteam(
        self, ticket_id: str, subteam_id: str, headers: dict
    ) -> None:
        """Update ticket with new subteam."""
        async with httpx.AsyncClient(timeout=60.0) as client:
            url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}"
            update_data = {"subTeamId": subteam_id}
            logger.info(f"Update ticket subteam data: {update_data}")
            response = await client.patch(url, headers=headers, json=update_data)
            response.raise_for_status()
            logger.info(f"Update ticket subteam response: {response.json()}")

    async def update_ticket_assigned_user(
        self, ticket_id: str, user_id: str, headers: dict
    ) -> None:
        """Update ticket with new assigned user."""
        async with httpx.AsyncClient(timeout=60.0) as client:
            url = f"{THENA_PLATFORM_URL}/v1/tickets/{ticket_id}"
            update_data = {"assignedAgentId": user_id}
            logger.info(f"Update ticket assigned user data: {update_data}")
            response = await client.patch(url, headers=headers, json=update_data)
            response.raise_for_status()
            logger.info(f"Update ticket assigned user response: {response.json()}")

    def _assign_to_subteam_wrapper(self):
        @tool
        async def assign_to_subteam(
            ticket_id: str,
            team_id: str,
            user_id: str,
            flow_id: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> Dict[str, Any]:
            """Assign a ticket to a subteam based on user instruction and team context.

            Args:
                ticket_id: The ID of the ticket to assign
                team_id: The ID of the team to get subteams from
                flow_id: The ID of the flow to get user instruction from
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - assigned_team_id: ID of the assigned subteam (if successful)
                - assigned_team_name: Name of the assigned subteam (if successful)
                - message: A message explaining the result
            """
            try:
                # Get API key from agent_auth
                db = await get_supabase()
                agent_auth = (
                    await db.table("agent_auth")
                    .select("api_key")
                    .eq("agent_id", self.agent_id)
                    .neq("category", "exa search")
                    .execute()
                )
                if not agent_auth.data:
                    raise ToolException("No auth found for agent")
                api_key = agent_auth.data[0]["api_key"]

                # Get user instruction from agent_flows
                configuration = (
                    await db.table("agent_flows")
                    .select("configuration")
                    .eq("agent_id", self.agent_id)
                    .eq("flow_id", flow_id)
                    .execute()
                )
                configuration = configuration.data[0]["configuration"]
                if not configuration:
                    raise ToolException("No flow configuration found")
                handoff_assigne_id = configuration["handoffAssigneeId"]
                handoffAssigneeName = configuration["handoffAssigneeName"]
                handoffAssigneeType = configuration["handoffAssigneeType"]
                instructions = configuration["instructions"]
                handoff_assignee_dict = {
                    "id": handoff_assigne_id,
                    "name": handoffAssigneeName,
                    "type": handoffAssigneeType
                }

                org_result = (
                    await db.table("organizations")
                    .select("x-org-id")
                    .eq("id", self.organization_id)
                    .execute()
                )
                if not org_result.data:
                    raise ValueError(
                        f"No organization found with id {self.organization_id}"
                    )
                x_org_id = org_result.data[0]["x-org-id"]

                # Set up headers for API requests
                headers = {"x-org-id": str(x_org_id), "x-api-key": api_key}

                # Get subteams using separate client
                subteams = await self.get_subteams(team_id, headers)
                if not subteams:
                    return {
                        "success": False,
                        "message": "No subteams found for this team",
                    }

                # Format subteams for LLM context
                subteams_json = json.dumps([s.dict() for s in subteams])
                logger.info(
                    "Subteam assignment context",
                    extra={
                        "subteams_count": len(subteams),
                        "subteams": subteams_json,
                        "ticket_id": ticket_id,
                        "team_id": team_id,
                        "user_id": user_id,
                        "flow_id": flow_id,
                    },
                )

                # Load and format the selection prompt
                selection_prompt_data = load_prompt(
                    "ai_ticket_deflection_tool_subteam_selection"
                )

                # Add debug logger
                logger.info(
                    f"Selection prompt metadata: {selection_prompt_data['metadata']}"
                )
                logger.info(
                    f"Required params: {selection_prompt_data['metadata'].get('required_params', [])}"
                )

                selection_prompt = format_prompt(
                    selection_prompt_data,
                    subteam_context=str(subteams_json),
                    handoff_assignee_dict=str(handoff_assignee_dict if handoff_assignee_dict else ""),
                    instructions=str(instructions if instructions else "")
                )

                # Log the formatted prompt
                logger.info(f"Selection prompt length: {len(selection_prompt)}")
                logger.info(
                    f"First 200 chars of selection prompt: {selection_prompt[:200]}"
                )

                messages = [HumanMessage(content=selection_prompt)]

                # Use LLM to select appropriate subteam
                llm = ChatOpenAI(model="gpt-4.1", temperature=0)
                response = await llm.ainvoke(messages)
                logger.info(f"Response from LLM: {response}")

                # Extract JSON from markdown code block
                content = response.content
                json_start = content.rfind("```json\n") + 8  # Skip '```json\n'
                if json_start == 7:  # Not found (-1 + 8)
                    json_start = content.rfind("```\n") + 4  # Try without 'json'

                if json_start >= 4:  # Found a code block
                    json_end = content.rfind("\n```")
                    if json_end != -1:
                        json_str = content[json_start:json_end].strip()
                        try:
                            result = json.loads(json_str)
                            logger.info(f"Parsed JSON result: {result}")
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse JSON from code block: {e}")
                            raise ToolException("Invalid JSON response from LLM")
                    else:
                        raise ToolException("Malformed code block in LLM response")
                else:
                    # Fallback: try parsing the entire content as JSON
                    try:
                        result = json.loads(content)
                        logger.info(f"Parsed full content as JSON: {result}")
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse response as JSON: {e}")
                        raise ToolException("Invalid JSON response from LLM")

                if not result.get("team_id") and not result.get("user_id"):
                    return {
                        "success": False,
                        "message": f"No matching subteam or user found. Reasoning: {result.get('reasoning')}",
                    }

                if result.get("user_id"):
                    await self.update_ticket_assigned_user(
                        ticket_id, result["user_id"], headers
                    )

                if result.get("team_id"):
                    await self.update_ticket_subteam(
                        ticket_id, result["team_id"], headers
                    )

                # Find team name for the assigned team
                assigned_team = next(
                    (team for team in subteams if team.id == result["team_id"]), None
                )

                return {
                    "success": True,
                    "assigned_team_id": result["team_id"],
                    "assigned_user_id": result["user_id"],
                    "assigned_team_name": assigned_team.name if assigned_team else None,
                    "message": f"Ticket assigned to subteam. Reasoning: {result.get('reasoning')}",
                }

            except httpx.HTTPError as e:
                error_msg = f"HTTP error occurred: {str(e)}"
                logger.error(error_msg)
                raise ToolException(error_msg)
            except Exception as e:
                error_msg = f"Error assigning subteam: {str(e)}"
                logger.error(error_msg)
                raise ToolException(error_msg)

        return assign_to_subteam

    def get_tools(self) -> List[BaseTool]:
        """Get all available tools from this TicketTools instance."""
        tools = [self._assign_to_subteam_wrapper()]

        logger.info(
            f"Retrieved {len(tools)} tools from TicketTools instance",
            extra={
                "tool_names": [getattr(tool, "name", str(tool)) for tool in tools],
                "organization_id": (
                    str(self.organization_id) if self.organization_id else None
                ),
                "agent_id": str(self.agent_id) if self.agent_id else None,
            },
        )

        return tools


# Global instances dictionary
_ticket_tools_instances = {}


async def get_ticket_tools(
    organization_id: Optional[UUID] = None, agent_id: Optional[UUID] = None
) -> TicketTools:
    """Get or create a TicketTools instance."""
    instance_key = f"{organization_id or 'default'}:{agent_id or 'default'}"

    if instance_key not in _ticket_tools_instances:
        _ticket_tools_instances[instance_key] = TicketTools(
            organization_id=organization_id, agent_id=agent_id
        )

    return _ticket_tools_instances[instance_key]


async def get_email_config(
    team_id: str, api_key: str, x_org_id: str
) -> List[Dict[str, Any]]:
    """Get email configuration for a team."""
    url = f"{THENA_EMAIL_URL}/email-config/get-custom-email-config?teamId={team_id}"
    headers = {"x-api-key": api_key, "x-org-id": x_org_id}

    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            response = await client.get(url, headers=headers)
            response.raise_for_status()

            return response.json()

        except httpx.HTTPError as e:
            error_msg = f"HTTP error occurred when fetching email config: {str(e)}"
            logger.error(error_msg)
            return []
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON response from email config: {str(e)}"
            logger.error(error_msg)
            return []
        except Exception as e:
            error_msg = f"Error fetching email config: {str(e)}"
            logger.error(error_msg)
            return []


def create_workflow() -> StateGraph:
    """Create the deflection response workflow with validation loop."""
    workflow = StateGraph(ResponseState)

    # Add nodes
    workflow.add_node("get_ticket_info", get_ticket_info_node)
    workflow.add_node("get_context", get_context_node)
    workflow.add_node("small_llm_response", small_llm_response_node)
    workflow.add_node("judgeLLM_analysis", judgeLLM_analysis_node)
    workflow.add_node("ticket_response", ticket_response_node)

    # Set entry point
    workflow.set_entry_point("get_ticket_info")

    # Add edges with conditions
    workflow.add_edge("get_ticket_info", "get_context")
    workflow.add_edge("get_context", "small_llm_response")
    workflow.add_edge("small_llm_response", "judgeLLM_analysis")

    # After judge analysis, either retry or update
    workflow.add_conditional_edges(
        "judgeLLM_analysis",
        should_continue,
        {True: "small_llm_response", False: "ticket_response"},
    )

    # Always end after update attempt
    workflow.add_edge("ticket_response", END)

    return workflow.compile()


async def execute_flow(**inputs: Dict[str, Any]) -> Dict[str, Any]:
    """Execute the ticket status change workflow."""
    try:
        # Extract ticket info
        ticket_id = None
        comment_id = None
        parentCommentId = None

        logger.info("Ticket info extraction started")

        if isinstance(inputs, dict):
            # Try different possible paths to find ticket info
            if "input" in inputs and isinstance(inputs["input"], dict):
                input_data = inputs["input"]

                # Path 1: input.ticket.input.ticket (current structure)
                if (
                    "ticket" in input_data
                    and isinstance(input_data["ticket"], dict)
                    and "input" in input_data["ticket"]
                    and isinstance(input_data["ticket"]["input"], dict)
                    and "ticket" in input_data["ticket"]["input"]
                    and isinstance(input_data["ticket"]["input"]["ticket"], dict)
                ):
                    ticket_data = input_data["ticket"]["input"]["ticket"]
                    ticket_id = ticket_data.get("ticket_id")

                # Path 2: input.ticket (simpler structure)
                elif "ticket" in input_data and isinstance(input_data["ticket"], dict):
                    ticket_data = input_data["ticket"]
                    ticket_id = ticket_data.get("ticket_id") or ticket_data.get("id")

        logger.info(f"Inputs: {inputs}")

        # Initialize state from inputs
        initial_state: ResponseState = {
            "flow_id": inputs.get("flow_id"),
            "ticket_id": ticket_id,
            "agent_id": inputs.get("agent_id"),
            "org_id": inputs.get("input", {}).get("org_id"),
            "agent_config": inputs.get("input", {}).get("agent_config", {}),
            "parentCommentId": inputs.get("input", {})
            .get("ticket", {})
            .get("input", {})
            .get("parentCommentId", ""),
            "comment_id": inputs.get("input", {})
            .get("ticket", {})
            .get("input", {})
            .get("comment_id", ""),
            "ticket_data": None,
            "comments": None,
            "user_instruction": None,
            "response": None,
            "confidence": None,
            "reasoning": None,
            "attempt_count": 0,
            "source": None,
            "feedback": None,
            "response_added": False,
            "is_frustrated": None,
            "frustration_confidence": None,
            "frustration_reasoning": None,
        }

        # Log the initial state
        logger.info("Graph: Initial state created")

        if not initial_state["ticket_id"]:
            raise ValueError("Ticket ID is required")

        workflow = create_workflow()
        final_state = await workflow.ainvoke(initial_state)

        return {
            "response_added": True,
            "data": {
                "response": final_state["response"],
                "confidence": final_state.get("confidence"),
                "reasoning": final_state.get("reasoning"),
            },
        }

    except Exception as e:
        logger.error(f"Error in execute_flow: {str(e)}")
        return {"response_added": False, "message": str(e)}
