# NOTE: This file is being refactored and split into multiple modules under a new tools/ directory for maintainability.
import asyncio
from typing import Any, Optional
from uuid import UUID

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool, tool

from backend.core.config import get_config
from backend.core.logging_config import get_logger
from backend.models.rag_tools import (
    ContextSearchInput,
    ExaSearchInput,
)
from backend.repositories.chat_cache_repository import ChatCacheRepository
from backend.services.langgraph_chat.all_tools.flow_tools import get_ticket_summary_impl
from backend.services.langgraph_chat.all_tools.memory_tools import memory_process_impl
from backend.services.langgraph_chat.all_tools.search_tools import (
    exa_search_impl,
    search_context_impl,
)
from backend.services.langgraph_chat.all_tools.ticket_tools import (
    add_tags_to_ticket_impl,
    archive_ticket_impl,
    archive_tickets_bulk_impl,
    create_comment_impl,
    create_team_tag_impl,
    create_ticket_impl,
    create_ticket_with_history_impl,
    create_tickets_bulk_impl,
    data_insights_query_impl,
    get_team_tags_impl,
    update_ticket_impl,
    update_tickets_bulk_impl,
)
from backend.services.langgraph_chat.all_tools.visualization_tools import (
    graph_visualization_impl,
)
from backend.services.langgraph_chat.all_tools.view_tools import (
    create_view_impl,
    update_view_impl,
)
from backend.services.langgraph_chat.graph_models import TicketInput
from backend.services.memory.fact_deducer import FactDeducer
from backend.services.memory.ner import EntityRecognizer

logger = get_logger(__name__)
config = get_config()

# Get the platform URL from config
THENA_PLATFORM_URL = config.THENA_PLATFORM_URL


class ThenaTools:
    """
    A class containing all tools available for the Thena platform.

    This class uses the @tool decorator to make methods available as tools to the LLM.
    Each tool method is decorated and will be automatically discovered and registered.
    """

    def __init__(
        self, organization_id: Optional[UUID] = None, agent_id: Optional[UUID] = None
    ):
        """
        Initialize the tools with organization and agent IDs.

        Args:
            organization_id: The UUID of the organization
            agent_id: The UUID of the agent
        """
        self.organization_id = organization_id
        self.agent_id = agent_id
        self._cache = ChatCacheRepository(
            organization_id=organization_id, agent_id=agent_id
        )
        self.ner = EntityRecognizer()  # Initialize NER
        self.fact_deducer = FactDeducer()  # Initialize FactDeducer

    # Create wrapper for create_ticket tool
    def _create_ticket_wrapper(self) -> BaseTool:
        @tool
        async def create_ticket(
            requestorEmail: str,
            title: str,
            teamId: Optional[str] = None,
            description: Optional[str] = None,
            assignedAgentId: Optional[str] = None,
            accountId: Optional[str] = None,
            assignedAgentEmail: Optional[str] = None,
            dueDate: Optional[str] = None,
            submitterEmail: Optional[str] = None,
            statusId: Optional[str] = None,
            statusName: Optional[str] = None,
            priorityId: Optional[str] = None,
            priorityName: Optional[str] = None,
            sentimentId: Optional[str] = None,
            metadata: Optional[dict[str, Any]] = None,
            typeId: Optional[str] = None,
            isPrivate: Optional[bool] = None,
            source: Optional[str] = None,
            aiGeneratedTitle: Optional[str] = None,
            aiGeneratedSummary: Optional[str] = None,
            attachmentUrls: Optional[list[str]] = None,
            customFieldValues: Optional[list[dict[str, Any]]] = None,
            performRouting: Optional[bool] = None,
            formId: Optional[str] = None,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Creates a new ticket in the Thena platform.

            Required Args:
                requestorEmail: Email of the person requesting the ticket
                title: Title of the ticket
                teamId: ID of the team to assign the ticket to. If not provided, the team_id from config metadata will be used.
                
            Optional Args (do not prompt for these unless specifically needed):
                description: Detailed description of the ticket
                assignedAgentId: ID of the agent to assign the ticket to
                accountId: ID of the account associated with the ticket
                assignedAgentEmail: Email of the agent to assign the ticket to
                dueDate: Due date for the ticket in ISO format (e.g., "2023-11-07T05:31:56Z")
                submitterEmail: Email of the person submitting the ticket
                statusId: ID of the status to set for the ticket
                statusName: Name of the status to set for the ticket
                priorityId: ID of the priority to set for the ticket
                priorityName: Name of the priority to set for the ticket
                sentimentId: ID of the sentiment to set for the ticket
                metadata: Metadata for the ticket
                typeId: ID of the ticket type
                isPrivate: Boolean indicating whether the ticket is private
                source: Source of the ticket
                aiGeneratedTitle: AI-generated title for the ticket
                aiGeneratedSummary: AI-generated summary for the ticket
                attachmentUrls: List of attachment URLs for the ticket
                customFieldValues: List of custom field values for the ticket
                performRouting: Boolean indicating whether to perform routing
                formId: ID of the form used to create the ticket
                
            Configuration:
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - ticket: Dictionary with ticket details including:
                  * id: The unique identifier of the ticket (for API operations)
                  * ticketId: The numeric ID of the ticket
                  * display_id: The user-friendly display format of the ticket (e.g., "ENG - 123")
                  * status: The current status of the ticket (e.g., "Open")
                  * title: The title of the ticket
                  * requestorEmail: Email of the person who requested the ticket
                  * teamId: ID of the team the ticket is assigned to
                - message: A message confirming the ticket was created with its display ID
            """
            return await create_ticket_impl(
                self.organization_id,
                self.agent_id,
                requestorEmail,
                title,
                teamId,
                description,
                assignedAgentId=assignedAgentId,
                accountId=accountId,
                assignedAgentEmail=assignedAgentEmail,
                dueDate=dueDate,
                submitterEmail=submitterEmail,
                statusId=statusId,
                statusName=statusName,
                priorityId=priorityId,
                priorityName=priorityName,
                sentimentId=sentimentId,
                metadata=metadata,
                typeId=typeId,
                isPrivate=isPrivate,
                source=source,
                aiGeneratedTitle=aiGeneratedTitle,
                aiGeneratedSummary=aiGeneratedSummary,
                attachmentUrls=attachmentUrls,
                customFieldValues=customFieldValues,
                performRouting=performRouting,
                formId=formId,
                config=config,
            )

        return create_ticket

    # Create wrapper for create_comment tool
    # Create wrapper for create_tickets_bulk tool
    def _create_tickets_bulk_wrapper(self) -> BaseTool:
        @tool
        async def create_tickets_bulk(
            tickets: list[TicketInput],
            stop_on_error: bool = True,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Creates multiple tickets in bulk in the Thena platform.
            
            Required Args:
                tickets: List of ticket objects. Each ticket must contain:
                    - requestorEmail: Email of the person requesting the ticket
                    - title: Title of the ticket
                    - teamId: ID of the team to assign the ticket to
                    
            Optional Args:
                stop_on_error: Whether to stop processing if an error occurs (default: True)
                
            Configuration:
                config: RunnableConfig for additional configuration
                
            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - tickets: List of created ticket objects with their details
                - message: A message confirming the number of tickets created
            """
            return await create_tickets_bulk_impl(
                self.organization_id,
                self.agent_id,
                tickets,
                stop_on_error,
                config,
            )

        return create_tickets_bulk

    # Create wrapper for update_tickets_bulk tool
    def _update_tickets_bulk_wrapper(self) -> BaseTool:
        @tool
        async def update_tickets_bulk(
            ticket_ids: list[str],
            statusId: Optional[str] = None,
            typeId: Optional[str] = None,
            priorityId: Optional[str] = None,
            assignedAgentId: Optional[str] = None,
            isPrivate: Optional[bool] = None,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Updates multiple tickets in bulk in the Thena platform. Use this tool when you need to
            make changes to multiple tickets simultaneously.
            
            IMPORTANT: Always use this tool instead of multiple individual update_ticket calls when
            you need to update the same field(s) across multiple tickets. This is more efficient
            and reduces API calls.
            
            Required Args:
                ticket_ids: List of ticket IDs to update
                
            Optional Args (at least one must be provided):
                statusId: ID of the status to set for the tickets
                typeId: ID of the ticket type to set for the tickets
                priorityId: ID of the priority to set for the tickets
                assignedAgentId: ID of the agent to assign the tickets to
                isPrivate: Boolean indicating whether the tickets should be private
                
            Configuration:
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - result: Dictionary with updated and skipped tickets information
                - message: A message confirming how many tickets were updated
            
            Example usage:
                When asked to update multiple tickets with the same or variable priority, status, or other attributes,
                use this tool rather than making separate update_ticket calls for each ticket.
            """
            return await update_tickets_bulk_impl(
                self.organization_id,
                self.agent_id,
                ticket_ids,
                statusId=statusId,
                typeId=typeId,
                priorityId=priorityId,
                assignedAgentId=assignedAgentId,
                isPrivate=isPrivate,
                config=config,
            )

        return update_tickets_bulk
        
    # Create wrapper for add_tags_to_ticket tool
    def _add_tags_to_ticket_wrapper(self) -> BaseTool:
        @tool
        async def add_tags_to_ticket(
            ticket_id: str,
            tag_ids: list[str],
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Adds tags to an existing ticket in the Thena platform.
            
            Required Args:
                ticket_id: ID of the ticket to add tags to
                tag_ids: List of tag UUIDs to add to the ticket. These must be the UUIDs of existing tags,
                    not the tag names. Use the get_team_tags tool first to retrieve the UUIDs of available tags.
                
            Configuration:
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - ticket_id: ID of the ticket that was updated
                - tags: List of tags that were added to the ticket
                - count: Number of tags that were added to the ticket
                - message: A message confirming the tags were added
                
            Example usage:
                1. First retrieve available tags using get_team_tags
                2. Extract the tag UUIDs from the response
                3. Use those UUIDs with this tool to add tags to a ticket
            """
            return await add_tags_to_ticket_impl(
                self.organization_id,
                self.agent_id,
                ticket_id,
                tag_ids,
                config,
            )

        return add_tags_to_ticket
        
    # Create wrapper for create_team_tag tool
    def _create_team_tag_wrapper(self) -> BaseTool:
        @tool
        async def create_team_tag(
            team_id: str,
            name: str,
            color: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
            description: Optional[str] = None,
        ) -> dict[str, Any]:
            """
            Creates a new tag for a team in the Thena platform.
            
            Required Args:
                team_id: ID of the team to create a tag for
                name: Name of the tag
                color: Color of the tag in hexadecimal format (e.g., "#FF0000" for red)
                
            Optional Args:
                description: Description of the tag
                
            Configuration:
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - team_id: ID of the team that the tag was created for
                - tags: List of tags that were created
                - count: Number of tags that were created
                - message: A message confirming the tag was created
            """
            return await create_team_tag_impl(
                self.organization_id,
                self.agent_id,
                team_id,
                name,
                color,
                "ticket",  # Still pass the default tag_type to the implementation
                description,
                config,
            )

        return create_team_tag
        
    # Create wrapper for get_team_tags tool
    def _get_team_tags_wrapper(self) -> BaseTool:
        @tool
        async def get_team_tags(
            team_id: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Gets all tags for a team in the Thena platform.
            
            Required Args:
                team_id: ID of the team to get tags for
                
            Configuration:
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - team_id: ID of the team that the tags belong to
                - tags: List of tags for the team
                - count: Number of tags for the team
                - message: A message confirming the tags were retrieved
            """
            return await get_team_tags_impl(
                self.organization_id,
                self.agent_id,
                team_id,
                config,
            )
            
        return get_team_tags
        
    def _archive_ticket_wrapper(self) -> BaseTool:
        @tool
        async def archive_ticket(
            ticket_id: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Archives a ticket in the Thena platform. Archiving a ticket removes it from active views
            but preserves all its data and history.
            
            Required Args:
                ticket_id: ID of the ticket to archive
                
            Configuration:
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - ticket_id: ID of the archived ticket
                - message: A confirmation message
            """
            return await archive_ticket_impl(
                self.organization_id,
                self.agent_id,
                ticket_id,
                config,
            )

        return archive_ticket
        
    def _archive_tickets_bulk_wrapper(self) -> BaseTool:
        @tool
        async def archive_tickets_bulk(
            ticket_ids: list[str],
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Archives multiple tickets in bulk in the Thena platform. Use this tool when you need to archive
            multiple tickets simultaneously.
            
            IMPORTANT: Always use this tool instead of multiple individual archive_ticket calls when
            you need to archive multiple tickets. This is more efficient and reduces API calls.
            
            Required Args:
                ticket_ids: List of ticket IDs to archive
                
            Configuration:
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - count: Number of tickets archived
                - ticket_ids: List of archived ticket IDs
                - message: A confirmation message
                
            Example usage:
                When a user asks to "archive multiple tickets", use this tool
                with ticket_ids [] rather than making separate archive_ticket calls for each ticket.
            """
            return await archive_tickets_bulk_impl(
                self.organization_id,
                self.agent_id,
                ticket_ids,
                config,
            )

        return archive_tickets_bulk

    def _create_comment_wrapper(self) -> BaseTool:
        @tool
        async def create_comment(
            ticket_id: str,
            content: str,
            parent_comment_id: Optional[str] = None,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Creates a new comment on an existing ticket in the Thena platform.

            Args:
                ticket_id: ID of the ticket to add the comment to
                content: Content of the comment to add
                parent_comment_id: Optional ID of the parent comment if this is a reply
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - comment: Dictionary with comment details including:
                  * id: The unique identifier of the comment
                  * content: The content of the comment
                  * ticket_id: The ID of the ticket the comment was added to
                  * parent_comment_id: The ID of the parent comment (if this is a reply)
                - message: A message confirming the comment was created with its ID
            """
            return await create_comment_impl(
                self.organization_id,
                self.agent_id,
                ticket_id,
                content,
                parent_comment_id,
                config,
            )

        return create_comment

    # Create wrapper for update_ticket tool
    def _update_ticket_wrapper(self) -> BaseTool:
        @tool
        async def update_ticket(
            ticket_id: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
            title: Optional[str] = None,
            description: Optional[str] = None,
            teamId: Optional[str] = None,
            assignedAgentId: Optional[str] = None,
            accountId: Optional[str] = None,
            assignedAgentEmail: Optional[str] = None,
            dueDate: Optional[str] = None,
            submitterEmail: Optional[str] = None,
            statusId: Optional[str] = None,
            statusName: Optional[str] = None,
            priorityId: Optional[str] = None,
            priorityName: Optional[str] = None,
            sentimentId: Optional[str] = None,
            metadata: Optional[dict[str, Any]] = None,
            typeId: Optional[str] = None,
            isPrivate: Optional[bool] = None,
            source: Optional[str] = None,
            aiGeneratedTitle: Optional[str] = None,
            aiGeneratedSummary: Optional[str] = None,
            attachmentUrls: Optional[list[str]] = None,
            customFieldValues: Optional[list[dict[str, Any]]] = None,
            subTeamId: Optional[str] = None,
            formId: Optional[str] = None,
        ) -> dict[str, Any]:
            """
            Updates an existing ticket in the Thena platform.

            Required Args:
                ticket_id: ID of the ticket to update
                
            Optional Args (do not prompt for these unless specifically needed):
                title: New title for the ticket
                description: New description of the ticket
                teamId: New team ID for the ticket
                assignedAgentId: ID of the agent to assign the ticket to
                accountId: ID of the account associated with the ticket
                assignedAgentEmail: Email of the agent to assign the ticket to
                dueDate: Due date for the ticket in ISO format (e.g., "2023-11-07T05:31:56Z")
                submitterEmail: Email of the person submitting the ticket
                statusId: ID of the status to set for the ticket
                statusName: Name of the status to set for the ticket (e.g., "Open", "In Progress", "Completed")
                priorityId: ID of the priority to set for the ticket
                priorityName: Name of the priority to set for the ticket (e.g., "High", "Medium", "Low")
                sentimentId: ID of the sentiment to set for the ticket
                metadata: Metadata for the ticket
                typeId: ID of the ticket type
                isPrivate: Boolean indicating whether the ticket is private
                source: Source of the ticket
                aiGeneratedTitle: AI-generated title for the ticket
                aiGeneratedSummary: AI-generated summary for the ticket
                attachmentUrls: List of attachment URLs for the ticket
                customFieldValues: List of custom field values for the ticket
                subTeamId: ID of the sub-team to assign the ticket to
                formId: ID of the form used to create the ticket
                
            Configuration:
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - ticket: Dictionary with updated ticket details
                - message: A message confirming the ticket was updated
            """
            return await update_ticket_impl(
                self.organization_id,
                self.agent_id,
                ticket_id,
                title=title,
                description=description,
                teamId=teamId,
                assignedAgentId=assignedAgentId,
                accountId=accountId,
                assignedAgentEmail=assignedAgentEmail,
                dueDate=dueDate,
                submitterEmail=submitterEmail,
                statusId=statusId,
                statusName=statusName,
                priorityId=priorityId,
                priorityName=priorityName,
                sentimentId=sentimentId,
                metadata=metadata,
                typeId=typeId,
                isPrivate=isPrivate,
                source=source,
                aiGeneratedTitle=aiGeneratedTitle,
                aiGeneratedSummary=aiGeneratedSummary,
                attachmentUrls=attachmentUrls,
                customFieldValues=customFieldValues,
                subTeamId=subTeamId,
                formId=formId,
                config=config,
            )

        return update_ticket

    def _get_ticket_summary_wrapper(self) -> BaseTool:
        @tool
        async def get_ticket_summary(
            ticket_id: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
            agent_id: Optional[str] = None,
        ) -> dict[str, Any]:
            """
            Get an AI-generated summary of a ticket.

            Args:
                ticket_id: ID of the ticket to summarize
                agent_id: Optional agent ID to use for the summary
                config: RunnableConfig for additional configuration

            Returns:
                Summary information including key points and both customer and technical summaries
            """
            return await get_ticket_summary_impl(
                self.organization_id, self.agent_id, ticket_id, config
            )

        return get_ticket_summary

    def _search_context_wrapper(self) -> BaseTool:
        @tool
        async def agent_knowledge_search(
            query: str,
            *,  # Make config a keyword-only parameter
            top_k: Optional[int] = 5,
            config: RunnableConfig,
            similarity_threshold: float = 0.7,
            file_ids: Optional[list[str]] = None,
        ) -> dict[str, Any]:
            """
            Search for relevant knowledge across agent files using RAG (Retrieval-Augmented Generation).

            Args:
                query: Search query to find relevant information
                top_k: Maximum number of results to return (default: 5)
                similarity_threshold: Minimum similarity score from 0-1 (default: 0.7)
                file_ids: Optional list of specific file IDs to search within
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - results: List of context chunks with their metadata including:
                  * content: The text content of the chunk
                  * file_name: Name of the file the chunk is from
                  * similarity: Similarity score between query and chunk
                - message: A message summarizing the search results
            """
            # Create and validate input using Pydantic model
            input_model = ContextSearchInput(
                query=query,
                top_k=top_k,
                similarity_threshold=similarity_threshold,
                file_ids=file_ids,
            )

            return await search_context_impl(
                self.organization_id, self.agent_id, input_model, config
            )

        return agent_knowledge_search

    def _exa_search_wrapper(self) -> BaseTool:
        @tool
        async def exa_search(
            query: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
            num_results: Optional[int] = 5,
            use_autoprompt: Optional[bool] = True,
            include_domains: Optional[list[str]] = None,
            exclude_domains: Optional[list[str]] = None,
        ) -> dict[str, Any]:
            """
            Search the web for RECENT information that may be outside the LLM's knowledge cutoff.

            ONLY use this tool when:
            - The user explicitly asks for real-time or current information
            - The query is about events that occurred after the LLM's knowledge cutoff
            - The user asks to search for something specific online
            - You need to verify recent facts that you're uncertain about

            DO NOT use this tool for:
            - Basic factual questions
            - Historical information that is well-established
            - General knowledge that should be within the LLM's training data

            Args:
                query: The search query for finding recent or real-time information
                num_results: Maximum number of results to return (default: 5)
                use_autoprompt: Whether to use Exa's autoprompt feature to improve search
                                relevance (default: True)
                include_domains: Optional list of domains to include in the search (e.g.,
                                ["nytimes.com", "bbc.com"] for news sources)
                exclude_domains: Optional list of domains to exclude from the search
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - results: List of search results with their metadata including:
                  * title: The title of the web page
                  * url: The URL of the web page
                  * content: The extracted content from the web page
                  * published_date: Optional publication date of the content
                  * score: Relevance score of the result
                - message: A message summarizing the search results
            """
            # Create and validate input using Pydantic model
            input_model = ExaSearchInput(
                query=query,
                num_results=num_results,
                use_autoprompt=use_autoprompt,
                include_domains=include_domains,
                exclude_domains=exclude_domains,
            )

            return await exa_search_impl(
                self.organization_id, self.agent_id, input_model, config
            )

        return exa_search

    def _memory_process_wrapper(self) -> BaseTool:
        @tool
        async def process_memory(
            message: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Process a message to extract entities and facts, storing them in memory.

            Args:
                message: The message to process for entities and facts
                config: RunnableConfig for additional configuration

            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - entities: List of extracted entities
                - facts: Dictionary of facts associated with entities
                - message: A message summarizing the results
            """
            return await memory_process_impl(
                self.organization_id,
                self.agent_id,
                message,
                config,
                self._cache,
                self.ner,
                self.fact_deducer,
            )

        return process_memory

    # --- New Tool: Create Ticket with History (for Widget) ---
    def _create_ticket_with_history_wrapper(self) -> BaseTool:
        @tool
        async def create_ticket_with_history(
            requestorEmail: str,
            title: str,
            teamId: str,
            userName: str,
            description: Optional[str] = None,
            *,
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Creates a new ticket in Thena and adds the current chat history as the first comment.
            This tool is specifically for widget deployments and you need to follow the
            Chat Widget Instructions if they are present.

            Args:
                requestorEmail: Email of the person requesting the ticket.
                title: Title of the ticket.
                teamId: ID of the team to assign the ticket to.
                userName: REQUIRED Name of the user requesting the ticket creation. Ask the user for this.
                description: Optional detailed description of the ticket (used for ticket creation).
                config: RunnableConfig containing metadata, including thread_id.

            Returns:
                A dictionary indicating success or failure, with ticket and comment details.
            """
            return await create_ticket_with_history_impl(
                self.organization_id,
                self.agent_id,
                requestorEmail,
                title,
                teamId,
                userName,
                description,
                config,
            )

        return create_ticket_with_history
        
    def _data_insights_query_wrapper(self) -> BaseTool:
        @tool
        async def data_insights_query(
            query: str,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Query for tickets, accounts, and customer data using natural language and get structured results.
            
            This tool allows you to search for information using natural language queries and returns 
            structured data from the platform database. It's especially effective for ticket queries.
            
            Example queries:
            - "open tickets"
            - "tickets assigned to Support team"
            - "tickets created last week"
            - "tickets with status on hold"
            - "high priority overdue tickets"
            - "tickets for Acme Corp account"
            - "tickets with sentiment negative"
            - "escalated tickets in IT team"
            
            Ticket data you can query includes:
            - Status (open, closed, on hold, etc.)
            - Priority (high, medium, low)
            - Type (bug, feature request, etc.)
            - Team assignment
            - Agent assignment
            - Creation and due dates
            - Sentiment
            - Whether tickets are escalated
            
            The tool can also query information about:
            - Accounts: Company name, industry, classification, health score, and account owner
            - Customer contacts: Contact information and relationship to accounts
            
            Args:
                query: Natural language query about tickets, accounts, or customer data
                config: RunnableConfig for additional configuration
                
            Returns:
                A formatted response with instructions on how to present the query results to the user.
                If an error occurs, returns a dictionary with an "error" key containing the error message.
            """
            return await data_insights_query_impl(
                self.organization_id,
                self.agent_id,
                query,
                config,
            )
            
        return data_insights_query
        
    def _graph_visualization_wrapper(self) -> BaseTool:
        @tool
        async def graph_visualization(
            query: str,
            data: list[dict[str, Any]],
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Generate a visualization for the given data.
            
            This tool takes data (typically from data_insights_query) and generates a visualization
            like a chart or graph based on the data and query.
            
            REQUIRED DATA FORMAT:
            The data must be in one of these formats:
            1. List of dictionaries with consistent keys, e.g.:
               [{"name": "Open", "value": 10}, {"name": "Closed", "value": 20}]
            2. Simple dictionary that will be converted to format #1:
               {"Open": 10, "Closed": 20}
            3. List of objects with properties that can be used for visualization:
               [{"status": "Open", "count": 10}, {"status": "Closed", "count": 20}]
            
            For time series data, include a date/time field:
            [{"date": "2023-01-01", "value": 10}, {"date": "2023-01-02", "value": 15}]
            
            IMPORTANT: The data must be properly formatted JSON. Do not include markdown code blocks or any text formatting.
            
            Args:
                query: The original natural language query or a description of the visualization
                data: The data to visualize in one of the formats described above
                config: RunnableConfig for additional configuration
                
            Returns:
                A dictionary containing:
                - visualization_complete: Boolean indicating if the operation was successful
                - graph_data: JSON data for the visualization (compatible with Chart.js)
                - error: Any error that occurred
                - response: Instructions for rendering the visualization
            """
            return await graph_visualization_impl(
                self.organization_id,
                self.agent_id,
                query,
                data,
                config,
            )
            
        return graph_visualization

    def _create_view_wrapper(self) -> BaseTool:
        @tool
        async def create_view(
            name: str,
            ticket_ids: list[str],
            scope: str,
            team_id: Optional[str] = None,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Creates a saved view in the Thena platform for a collection of tickets.
            
            Required Args:
                name: Name for the saved view. ALWAYS ASK THE USER for this value - do not assume or generate a default.
                ticket_ids: List of ticket IDs to include in the view
                scope: Scope of the view, must be either "USER" or "TEAM". ALWAYS ASK THE USER for this value - do not assume a default.
                
            Optional Args:
                team_id: ID of the team to associate with the view. If not provided, the team_id from config metadata will be used.
                
            Configuration:
                config: RunnableConfig for additional configuration
                
            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - message: A confirmation message
                - view: Dictionary with view details including:
                  * id: The unique identifier of the view
                  * name: The name of the view
                  * ticket_count: The number of tickets in the view
            """
            # Pass the config directly to the implementation function
            return await create_view_impl(
                self.organization_id,
                name,
                ticket_ids,
                scope,
                team_id,
                config=config,
            )
        
        return create_view
        
    def _update_view_wrapper(self) -> BaseTool:
        @tool
        async def update_view(
            original_name: str,
            updated_name: Optional[str] = None,
            updated_ticket_ids: Optional[list[str]] = None,
            updated_scope: Optional[str] = None,
            team_id: Optional[str] = None,
            *,  # Make config a keyword-only parameter
            config: RunnableConfig,
        ) -> dict[str, Any]:
            """
            Updates an existing saved view in the Thena platform.
            
            Required Args:
                original_name: The current name of the view to update
                
            Optional Args:
                updated_name: New name for the saved view
                updated_ticket_ids: New list of ticket IDs to include in the view
                updated_scope: New scope for the view, must be either "USER" or "TEAM"
                team_id: ID of the team associated with the view. If not provided, the team_id from config metadata will be used.
                
            Configuration:
                config: RunnableConfig for additional configuration
                
            Returns:
                A dictionary containing:
                - success: Boolean indicating if the operation was successful
                - message: A confirmation message
                - view: Dictionary with view details if successful, or a list of available views if the original view wasn't found
            """
            # Pass the config directly to the implementation function
            return await update_view_impl(
                self.organization_id,
                original_name,
                updated_name,
                updated_ticket_ids,
                updated_scope,
                team_id,
                config=config,
            )
        
        return update_view

    # Get all available tools from this instance
    def get_tools(self, deployment_type: Optional[str] = None) -> list[BaseTool]:
        """
        Get all available tools from this ThenaTools instance, filtering by deployment type.

        Args:
            deployment_type: Optional type of the deployment (e.g., 'widget')

        Returns:
            list[BaseTool]: A list of available tools based on deployment type.
        """
        # Filter tools based on deployment type
        if deployment_type == "widget":
            # Define allowed tools directly for 'widget' type
            widget_tools = [
                self._search_context_wrapper(),  # agent_knowledge_search
                self._create_ticket_with_history_wrapper(),
                # Add other specific read-only tools here if needed
            ]
            widget_tool_count = len(widget_tools)
            org_id_str = str(self.organization_id) if self.organization_id else None
            agent_id_str = str(self.agent_id) if self.agent_id else None

            logger.info(
                "Returning %d tools for 'widget' deployment type.",
                widget_tool_count,
                props={
                    "organization_id": org_id_str,
                    "agent_id": agent_id_str,
                    "widget_tool_count": widget_tool_count,
                },
            )
            return widget_tools
        else:
            # If not a widget or type is None, define all tools
            all_tools = [
                self._create_ticket_wrapper(),
                # self._create_tickets_bulk_wrapper(),  # Disabled until enterprise tier access is confirmed
                self._create_comment_wrapper(),
                self._update_ticket_wrapper(),
                self._update_tickets_bulk_wrapper(), 
                self._add_tags_to_ticket_wrapper(),
                self._create_team_tag_wrapper(),
                self._get_team_tags_wrapper(),
                self._archive_ticket_wrapper(),
                self._archive_tickets_bulk_wrapper(),
                self._get_ticket_summary_wrapper(),
                self._search_context_wrapper(),
                self._exa_search_wrapper(),
                self._data_insights_query_wrapper(),
                self._graph_visualization_wrapper(),
                self._create_view_wrapper(),
                self._update_view_wrapper(),
                # self._memory_process_wrapper(),
                # self._create_ticket_with_history_wrapper()
            ]
            all_tools_count = len(all_tools)
            tool_names = [getattr(tool, "name", str(tool)) for tool in all_tools]
            org_id_str = str(self.organization_id) if self.organization_id else None
            agent_id_str = str(self.agent_id) if self.agent_id else None

            logger.debug(
                "Returning all %d tools for deployment type: %s",
                all_tools_count,
                deployment_type,
                props={
                    "tool_names": tool_names,
                    "organization_id": org_id_str,
                    "agent_id": agent_id_str,
                    "deployment_type": deployment_type,
                    "all_tools_count": all_tools_count,
                },
            )
            return all_tools


# Global instances dictionary and lock for thread-safe singleton pattern
_thena_tools_instances = {}
_init_lock = asyncio.Lock()


async def get_thena_tools(
    organization_id: Optional[UUID] = None, agent_id: Optional[UUID] = None
) -> ThenaTools:
    """
    Get or create a ThenaTools instance.

    Args:
        organization_id: The UUID of the organization
        agent_id: The UUID of the agent

    Returns:
        An initialized ThenaTools instance
    """
    instance_key = f"{organization_id or 'default'}:{agent_id or 'default'}"

    if instance_key not in _thena_tools_instances:
        async with _init_lock:
            if instance_key not in _thena_tools_instances:
                logger.info(
                    f"Creating new ThenaTools instance for {instance_key}", props={}
                )
                _thena_tools_instances[instance_key] = ThenaTools(
                    organization_id=organization_id, agent_id=agent_id
                )

    return _thena_tools_instances[instance_key]


# Function to get all available tools - maintains compatibility with existing code
async def get_available_tools(
    organization_id: Optional[UUID] = None,
    agent_id: Optional[UUID] = None,
    deployment_type: Optional[str] = None,
) -> list[BaseTool]:
    """
    Get a list of all available tools for the agent, filtered by deployment type.

    This function maintains compatibility with the existing codebase.

    Args:
        organization_id: Optional organization ID
        agent_id: Optional agent ID
        deployment_type: Optional deployment type

    Returns:
        list[BaseTool]: A list of available tools
    """
    try:
        # Get the singleton instance of ThenaTools
        tools_instance = await get_thena_tools(
            organization_id=organization_id, agent_id=agent_id
        )

        logger.debug(
            "Getting tools from ThenaTools instance",
            props={
                "organization_id": str(organization_id) if organization_id else None,
                "agent_id": str(agent_id) if agent_id else None,
                "deployment_type": deployment_type,
            },
        )

        # Get tool wrappers filtered by deployment type
        tools = tools_instance.get_tools(deployment_type=deployment_type)

        # Log the tools we found (now potentially filtered)
        logger.info(
            f"Retrieved {len(tools)} tools for deployment type '{deployment_type}'",
            props={
                "tool_names": [getattr(tool, "name", str(tool)) for tool in tools],
                "organization_id": str(organization_id) if organization_id else None,
                "agent_id": str(agent_id) if agent_id else None,
                "deployment_type": deployment_type,
            },
        )
        return tools
    except Exception as e:
        logger.error(f"Error getting available tools: {e}", props={"error": str(e)})
        return []
