"""
Agent processor for LangGraph chat service.

This module provides utilities for configuring and processing AI agent responses
in a LangGraph conversation.
"""

import json
import time
import traceback
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Tuple, Union
from uuid import UUID, uuid4

from langchain_core.messages import (
    AIMessage,
    AIMessageChunk,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)
from langchain_core.runnables import RunnableConfig
from langchain_groq import ChatGroq
from langchain_openai import ChatOpenAI
from langgraph.graph import END, START, StateGraph
from portkey_ai import PORTKEY_GATEWAY_URL, createHeaders

from backend.core.config import get_config
from backend.core.logging_config import get_logger
from backend.prompt_library import format_prompt, load_prompt
from backend.repositories.chat_cache_repository import ChatCacheRepository
from backend.services.langgraph_chat.tools import get_available_tools
from backend.services.memory.fact_deducer import FactDeducer
from backend.services.memory.fact_store import get_fact_store
from backend.services.memory.ner import EntityRecognizer

from .chat_state import AgentState
from .event_models import (
    ErrorEvent,
    MessageCompletedData,
    MessageCompletedEvent,
    MessageCreatedData,
    MessageCreatedEvent,
    MessageCreation,
    MessageDeltaContent,
    MessageDeltaData,
    MessageDeltaEvent,
    RunCreatedData,
    RunCreatedEvent,
    RunStepCreatedData,
    RunStepCreatedEvent,
    RunStepData,
    RunStepEvent,
    StepDetails,
    TextContent,
    TextItem,
    ToolCall,
    ToolCallFunction,
    ToolStepCompletedData,
    ToolStepCompletedEvent,
    ToolStepCreatedData,
    ToolStepCreatedEvent,
    ToolStepDeltaData,
    ToolStepDeltaDelta,
    ToolStepDeltaEvent,
    ToolStepDeltaStepDetails,
    ToolStepDeltaToolCall,
)
from .graph_models import TeamsData

settings = get_config()
logger = get_logger(__name__)


class AgentProcessor:
    """
    Handles AI agent configuration and response generation for LangGraph chat service.
    """

    # Tool display name mapping
    TOOL_DISPLAY_NAMES = {
        "agent_knowledge_search": "knowledge search.",
        "exa_search": "web search.",
        "create_ticket": "create ticket.",
        "create_tickets_bulk": "create multiple tickets.",
        "create_comment": "add comment.",
        "update_ticket": "update ticket.",
        "update_tickets_bulk": "update multiple tickets.",
        "add_tags_to_ticket": "add tags to ticket.",
        "create_team_tag": "create team tag.",
        "get_team_tags": "get team tags.",
        "archive_ticket": "archive ticket.",
        "archive_tickets_bulk": "archive multiple tickets.",
        "get_ticket_summary": "summarize ticket.",
        "process_memory": "process memory.",
        "data_insights_query": "data insights explorer.",
        "graph_visualization": "data visualization.",
        "create_view": "create view.",
        "update_view": "update view.",
    }

    def __init__(self, organization_id: UUID, agent_id: UUID):
        """
        Initialize the agent processor.

        Args:
            organization_id: The UUID of the organization
            agent_id: The UUID of the agent
        """
        self.organization_id = organization_id
        self.agent_id = agent_id
        self._llm = None
        self._llm_with_tools = None
        self._graph = None
        self._initialized = False
        self.state: AgentState = None
        self._cache = ChatCacheRepository(
            organization_id=organization_id, agent_id=agent_id
        )
        self.ner = EntityRecognizer()  # Initialize NER
        self.fact_deducer = FactDeducer()  # Initialize FactDeducer
        logger.info(
            f"AgentProcessor initialized for org: {organization_id}, agent: {agent_id}",
            props={},
        )

    async def initialize(self):
        """
        Initialize the agent processor.
        """
        start_time_initialize = time.time()
        try:
            await self._initialize_llm()
            self._initialize_graph()
            self._initialized = True
            duration = time.time() - start_time_initialize
            logger.info(
                f"AgentProcessor initialized in {duration:.4f} seconds",
                props={},
            )
        except Exception as e:
            duration = time.time() - start_time_initialize
            logger.error(
                f"Failed to initialize agent processor after {duration:.4f} seconds: {str(e)}",
                props={},
            )
            logger.error(traceback.format_exc(), props={})
            raise

    async def cleanup(self):
        """
        Clean up resources used by the agent processor.
        """
        try:
            logger.info(
                f"Cleaning up AgentProcessor resources for org {self.organization_id}",
                props={},
            )
            self._llm = None
            self._llm_with_tools = None
            self._graph = None
            self._initialized = False
            logger.info(
                f"AgentProcessor resources released for org {self.organization_id}",
                props={},
            )
        except Exception as e:
            logger.error(f"Error cleaning up AgentProcessor: {e}", props={})
            logger.error(traceback.format_exc(), props={})

    async def _initialize_llm(self):
        """
        Initialize the LLM for the agent.
        """
        try:
            logger.info("Initializing LLM", props={})
            self._llm = self._get_llm_instance()
            logger.info("LLM initialized", props={})
        except Exception as e:
            logger.error(f"Error initializing LLM: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise

    def _get_llm_instance(self):
        """
        Helper function to get the appropriate LLM instance based on configuration.

        Returns:
            The configured LLM instance (ChatOpenAI or ChatGroq)
        """
        # is_production = settings.ENVIRONMENT == 'production'
        is_production = True
        # Check if Portkey API key is available
        portkey_headers = None
        if settings.PORTKEY_API_KEY:
            logger.info("Portkey API key available, creating headers", props={})
            # Initialize Portkey client headers
            portkey_headers = createHeaders(
                api_key=settings.PORTKEY_API_KEY,
                config=settings.PORTKEY_CONFIG,
                parent_span_id="agent",
                provider="openai",  # This is the default provider for headers
            )

        # In production, use OpenAI
        if is_production:
            if portkey_headers:
                logger.info("Using ChatOpenAI with Portkey in production", props={})

                # Initialize ChatOpenAI with Portkey
                return ChatOpenAI(
                    api_key=settings.OPENAI_API_KEY,
                    base_url=PORTKEY_GATEWAY_URL,
                    default_headers=portkey_headers,
                    model=settings.OPENAI_MODEL,
                    temperature=0.5,  # Lowered from 0.7 for more consistent outputs
                    top_p=0.9,  # Added top_p for more deterministic responses
                    streaming=True,  # Enable streaming for delta events
                )
            else:
                logger.info("Using ChatOpenAI directly in production", props={})

                # Initialize ChatOpenAI directly
                return ChatOpenAI(
                    api_key=settings.OPENAI_API_KEY,
                    model_name=settings.OPENAI_MODEL,
                    temperature=0.5,  # Lowered from 0.7 for more consistent outputs
                    top_p=0.9,  # Added top_p for more deterministic responses
                    streaming=True,  # Enable streaming for delta events
                )
        else:
            # Non-production environment - use Groq with Llama-3-Groq-8B-Tool-Use
            logger.info(
                "Using ChatGroq with Llama-3-Groq-8B-Tool-Use in non-production environment",
                props={},
            )

            # Initialize ChatGroq, with Portkey headers if available
            groq_params = {
                "api_key": settings.GROQ_API_KEY,
                "model": "Llama-3-Groq-8B-Tool-Use",
                "temperature": 0.5,
                "top_p": 0.9,
                "streaming": True,
            }

            # Add Portkey headers if available
            if portkey_headers:
                logger.info("Adding Portkey headers to ChatGroq", props={})
                # Note: This assumes ChatGroq supports the same header structure as ChatOpenAI
                # If implementation differs, this may need adjustment
                groq_params["default_headers"] = portkey_headers
                groq_params["base_url"] = PORTKEY_GATEWAY_URL

            return ChatGroq(**groq_params)

    def _initialize_graph(self):
        """
        Initialize the LangGraph state graph.
        """
        try:
            logger.info("Starting graph construction", props={})

            # Define the state graph with proper state type
            builder = StateGraph(AgentState)

            # Add nodes
            builder.add_node("agent", self._agent_node)
            builder.add_node("suggestions", self._suggestions_node)
            builder.add_node(
                "tools_processor",
                self._tools_node,
            )
            builder.add_node("memory", self._process_memory_node)

            def should_continue(state: AgentState):
                messages = state["messages"]
                if not messages:
                    return END

                last_message = messages[-1]

                if (
                    isinstance(last_message, AIMessage)
                    and hasattr(last_message, "tool_calls")
                    and last_message.tool_calls
                ):
                    return "tools_processor"

                # Get agent config to check if suggestions are enabled - check the proper location
                show_suggestions = True  # Default is True

                # First check if state has 'config' attribute (pydantic model)
                if hasattr(state, "config") and state.config:
                    # Check if config has the attribute
                    if hasattr(state.config, "show_suggestions_after_each_message"):
                        show_suggestions = (
                            state.config.show_suggestions_after_each_message
                        )
                        logger.info(
                            "Found show_suggestions_after_each_message in state.config",
                            props={
                                "thread_id": state.get("thread_id", "unknown"),
                                "show_suggestions": show_suggestions,
                            },
                        )
                # Fallback to dictionary access if not found as attribute
                elif "agent_config" in state:
                    show_suggestions = state["agent_config"].get(
                        "show_suggestions_after_each_message", True
                    )
                    logger.info(
                        "Found show_suggestions_after_each_message in state[agent_config]",
                        props={
                            "thread_id": state.get("thread_id", "unknown"),
                            "show_suggestions": show_suggestions,
                        },
                    )
                else:
                    logger.info(
                        "No config found, using default show_suggestions=True",
                        props={"thread_id": state.get("thread_id", "unknown")},
                    )

                # If suggestions are disabled, skip the suggestions node
                if not show_suggestions:
                    logger.info(
                        "Suggestions disabled for this agent, skipping suggestions node",
                        props={"thread_id": state.get("thread_id", "unknown")},
                    )
                    return END
                return "suggestions"  # Route to suggestions node instead of END

            # Add edge from tools back to agent
            builder.add_edge(START, "memory")
            builder.add_edge("memory", "agent")
            builder.add_conditional_edges(
                "agent", should_continue, ["tools_processor", "suggestions", END]
            )
            builder.add_edge("tools_processor", "agent")
            builder.add_edge(
                "suggestions", END
            )  # Suggestions node is the final step before ending

            # Compile the graph
            self._graph = builder.compile()

            logger.info("LangGraph state graph initialized", props={})
        except Exception as e:
            logger.error(f"Error initializing LangGraph state graph: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise

    async def _process_memory_node(self, state: AgentState) -> AgentState:
        """Process memory from the latest message."""

        # Find memory flag from config
        use_memory = True
        if (
            hasattr(state, "config")
            and state.config
            and hasattr(state.config, "use_memory_context")
            and state.config.use_memory_context
        ):
            use_memory = state.config.use_memory_context
            logger.info(
                "Found use_memory_context in state.config",
                props={
                    "thread_id": state.get("thread_id", "unknown"),
                    "use_memory": use_memory,
                },
            )

        # Check if memory is enabled
        if not use_memory:
            logger.info(
                "Memory context disabled for this agent, skipping memory processing",
                props={"thread_id": state.get("thread_id", "unknown")},
            )
            return state

        # Continue if memory is enabled
        try:
            latest_message = state.messages[-1] if state.messages else None
            if not latest_message or not hasattr(latest_message, "content"):
                return state

            cached_entities = await self._cache.get_thread(
                f"entities:{state.get('thread_id', '')}"
            )
            cached_facts = await self._cache.get_thread(
                f"facts:{state.get('thread_id', '')}"
            )

            # Initialize entities and facts from cache if available
            if cached_entities:
                state.entities = set(cached_entities)
            if cached_facts:
                state.facts = cached_facts

            # Get entities and store them using NER
            entity_data, entity_objects = await self.ner.process_message(
                text=latest_message.content, org_id=str(self.organization_id)
            )

            # Initialize state attributes if needed
            if not hasattr(state, "entities"):
                state.entities = set()
            if not hasattr(state, "facts"):
                state.facts = {}

            # Add new entities to state
            new_entities = set()
            for entity in entity_data:
                entity_name = entity["name"]
                new_entities.add(entity_name)
                state.entities.add(entity_name)

            # Get existing facts from fact store
            fact_store = await get_fact_store()
            entity_ids = [e["id"] for e in entity_data]
            existing_facts = await fact_store.get_facts_for_entities(
                str(self.organization_id), entity_ids
            )

            # Process facts and update state
            new_facts = {}
            for fact_item in existing_facts:
                fact = fact_item["fact"]
                # Find matching entity by checking if any known entity appears in the fact
                matching_entities = [
                    entity
                    for entity in state.entities
                    if entity.lower() in fact.lower()
                ]
                # If multiple entities match, use the longest one (most specific)
                if matching_entities:
                    entity = max(matching_entities, key=len)
                    if entity not in state.facts:
                        state.facts[entity] = []
                    if fact not in state.facts[entity]:
                        state.facts[entity].append(fact)
                        if entity not in new_facts:
                            new_facts[entity] = []
                        new_facts[entity].append(fact)

            # Deduce new facts synchronously
            if entity_objects:
                deduced_facts = await self.fact_deducer.deduce_facts(
                    content=latest_message.content,
                    entities=entity_objects,
                    org_id=str(self.organization_id),
                )

                # Process deduced facts
                for fact_item in deduced_facts:
                    fact = fact_item["fact"]
                    # Find matching entity by checking if any known entity appears in the fact
                    matching_entities = [
                        entity
                        for entity in state.entities
                        if entity.lower() in fact.lower()
                    ]
                    # If multiple entities match, use the longest one (most specific)
                    if matching_entities:
                        entity = max(matching_entities, key=len)
                        if entity not in state.facts:
                            state.facts[entity] = []
                        if fact not in state.facts[entity]:
                            state.facts[entity].append(fact)
                            if entity not in new_facts:
                                new_facts[entity] = []
                            new_facts[entity].append(fact)

            # Cache entities and facts after both existing and deduced facts are processed
            if new_entities or new_facts:
                await self._cache_entities_and_facts(state, new_entities, new_facts)

            return state
        except Exception as e:
            logger.error(f"Error in memory processing: {str(e)}")
            return state

    async def _agent_node(
        self, state: AgentState, config: RunnableConfig, writer=None
    ) -> Dict[str, Any]:
        """
        Process the agent node in the graph.

        Args:
            state: The current state
            config: The RunnableConfig for the node
            writer: Optional writer function for streaming

        Returns:
            Dict[str, Any]: The updated state with next node
        """
        try:
            # Get the messages
            original_messages = state.get("messages", [])

            # Create a copy of messages for the LLM call that may include ticket data
            messages_for_llm = list(original_messages)

            # Load flags for real time context + context prompt + context size
            use_real_time_context = True  # Default to True for backward compatibility
            # Load the prompt template from the prompt library
            prompt_data = load_prompt("agent_processor_realtime_context")
            context_prompt = prompt_data
            
            # Check if the prompts are dictionaries or strings
            if isinstance(context_prompt, dict) and "content" in context_prompt:
                context_prompt_content = context_prompt["content"]
            else:
                context_prompt_content = str(context_prompt)

            # TODO: We have removed the use of the graph visualization prompt since it is now being sent
            # as instructions with the tool response. Hence this has been removed from system prompt.
            context_prompt = context_prompt_content
            max_context_size = 10000  # Default size limit

            # Load flags for memory context
            use_memory_context = True  # Default to True for backward compatibility
            # Initialize memory context
            memory_context = ""

            # Attempt to update all flags from config
            try:
                agent_config = None
                if hasattr(state, "config") and state.config:
                    agent_config = state.config

                if agent_config:
                    # Check if real-time context is enabled in the config
                    if hasattr(agent_config, "use_real_time_context"):
                        use_real_time_context = agent_config.use_real_time_context
                    elif (
                        isinstance(agent_config, dict)
                        and "use_real_time_context" in agent_config
                    ):
                        use_real_time_context = agent_config["use_real_time_context"]

                    # Get custom context prompt if available
                    if (
                        hasattr(agent_config, "real_time_context_prompt")
                        and agent_config.real_time_context_prompt
                    ):
                        context_prompt = agent_config.real_time_context_prompt
                    elif (
                        isinstance(agent_config, dict)
                        and "real_time_context_prompt" in agent_config
                        and agent_config["real_time_context_prompt"]
                    ):
                        context_prompt = agent_config["real_time_context_prompt"]

                    # Get maximum context size
                    if hasattr(agent_config, "max_context_size"):
                        max_context_size = agent_config.max_context_size
                    elif (
                        isinstance(agent_config, dict)
                        and "max_context_size" in agent_config
                    ):
                        max_context_size = agent_config["max_context_size"]

                    # Check if memory context is enabled in the config
                    if hasattr(agent_config, "use_memory_context"):
                        use_memory_context = agent_config.use_memory_context
                    elif (
                        isinstance(agent_config, dict)
                        and "use_memory_context" in agent_config
                    ):
                        use_memory_context = agent_config["use_memory_context"]

                    logger.info(
                        "Applied context settings from agent config",
                        props={
                            "thread_id": state.get("thread_id", "unknown"),
                            "use_real_time_context": use_real_time_context,
                            "use_memory_context": use_memory_context,
                            "max_context_size": max_context_size,
                            "has_custom_prompt": context_prompt
                            != (
                                "You have been supplied with the JSON of the tickets that the user is currently "
                                "seeing on the Thena AI dashboard. Use this ticket information to answer accordingly "
                                "if the user query is related to tickets."
                            ),
                        },
                    )
            except Exception as e:
                logger.error(f"Error getting agent config: {str(e)}")
                agent_config = None

            # Check for ticket data and team id in the config
            ticket_data = None
            team_id = None
            if "metadata" in config:
                if "team_id" in config["metadata"]:
                    team_id = config["metadata"]["team_id"]
                    logger.info(
                        "Found team_id in metadata section",
                        props={
                            "thread_id": state.get("thread_id", "unknown"),
                            "team_id": team_id,
                        },
                    )
                    # If team_id is present, store it in state for access by tools and other components
                    state["team_id"] = team_id
                    logger.info(
                        "Added team_id to state for tool usage",
                        props={
                            "thread_id": state.get("thread_id", "unknown"),
                            "team_id": team_id,
                        },
                    )

                # Check in metadata section (primary location for ticket_data)
                if use_real_time_context and "ticket_data" in config["metadata"]:
                    ticket_data = config["metadata"]["ticket_data"]
                    logger.info(
                        "Found ticket_data in metadata section",
                        props={
                            "thread_id": state.get("thread_id", "unknown"),
                            "ticket_data": ticket_data,
                        },
                    )

            # Check for teams_data in metadata
            teams_data = None
            if "metadata" in config and "teams_data" in config["metadata"]:
                teams_data = config["metadata"]["teams_data"]
                logger.info(
                    "Found teams_data in metadata section",
                    props={
                        "thread_id": state.get("thread_id", "unknown"),
                        "teams_count": len(teams_data) if teams_data else 0,
                        "teams_data_type": type(teams_data).__name__,
                        "first_item_type": type(teams_data[0]).__name__ if teams_data and len(teams_data) > 0 else "None",
                    },
                )
            
            # Attempt to load all available data into system message
            try:
                # Find the system message if it exists
                system_message_idx = None
                current_content = ""
                new_content = ""
                for i, msg in enumerate(messages_for_llm):
                    if isinstance(msg, SystemMessage):
                        system_message_idx = i
                        break

                if system_message_idx is not None:
                    # Get the existing system message
                    system_msg = messages_for_llm[system_message_idx]
                    current_content = system_msg.content
                    new_content = current_content

                # Process team_id if available
                if team_id:
                    new_content += f"\n\nThe current Team ID is : {team_id}"
                    
                # Process teams_data if available
                if teams_data and isinstance(teams_data, list) and len(teams_data) > 0:
                    user_teams = []
                    
                    # First collect all teams the user is part of
                    for team in teams_data:
                        # Handle both dictionary and object formats
                        if isinstance(team, dict):
                            # Only include teams where userIsPartOf is True
                            if not team.get("userIsPartOf", False):
                                continue
                                
                            team_name = team.get("name", "Unknown")
                            team_identifier = team.get("identifier", "")
                            team_uid = team.get("uid", "")
                            is_active = "Active" if team.get("is_active", False) else "Inactive"
                        else:
                            # Assume it's a TeamsData object
                            try:
                                # Only include teams where userIsPartOf is True
                                if not getattr(team, "userIsPartOf", False):
                                    continue
                                    
                                team_name = team.name
                                team_identifier = team.identifier
                                team_uid = team.uid
                                is_active = "Active" if team.is_active else "Inactive"
                            except AttributeError:
                                # Fallback in case of unexpected object type
                                logger.warning(
                                    "Unexpected team data object type",
                                    props={"team_type": type(team).__name__},
                                )
                                continue
                        
                        user_teams.append((team_name, team_identifier, team_uid, is_active))
                    
                    # Only add the section if there are teams the user is part of
                    if user_teams:
                        new_content += "\n\nUser is a part of the following teams:\n"
                        for team_name, team_identifier, team_uid, is_active in user_teams:
                            new_content += f"- {team_name} (Identifier: {team_identifier}, ID: {team_uid}, Status: {is_active})\n"

                # Process ticket data if enabled
                if use_real_time_context:
                    # Initialize ticket data if it's None
                    if ticket_data is None:
                        ticket_data = {"tickets": []}
                        logger.info(
                            "No ticket data provided, initializing empty ticket data",
                            props={"thread_id": state.get("thread_id", "unknown")},
                        )

                    # First ensure ticket_data is serializable
                    if not isinstance(ticket_data, (dict, list)):
                        logger.warning(
                            "Ticket data is not a dictionary or list, converting to string",
                            props={
                                "thread_id": state.get("thread_id", "unknown"),
                                "data_type": type(ticket_data).__name__,
                            },
                        )
                        ticket_data = {"data": str(ticket_data)}

                    # Limit the size of ticket data to prevent token explosion
                    ticket_json = json.dumps(ticket_data, indent=2)
                    if len(ticket_json) > max_context_size:
                        logger.warning(
                            f"Ticket data exceeds {max_context_size} characters, truncating",
                            props={
                                "thread_id": state.get("thread_id", "unknown"),
                                "original_size": len(ticket_json),
                            },
                        )
                        ticket_json = (
                            ticket_json[:max_context_size] + "...\n(truncated)"
                        )

                    # Get ticket count - handle both dictionary with 'tickets' key and list cases
                    ticket_count = 0
                    if isinstance(ticket_data, dict) and "tickets" in ticket_data:
                        ticket_count = len(ticket_data["tickets"])
                    elif isinstance(ticket_data, list):
                        ticket_count = len(ticket_data)

                    new_content += f"\n\n{context_prompt}\n\nTicket Data:\n```json\n{ticket_json}\nThe current ticket count is {ticket_count}\n```"

                # Process memory context if enabled
                if use_memory_context:
                    if hasattr(state, "entities") and state.entities:
                        memory_context += "\n\nKnown entities in conversation:\n"
                        for entity in sorted(
                            state.entities
                        ):  # Sort for consistent output
                            memory_context += f"- {entity}\n"

                    if hasattr(state, "facts") and state.facts:
                        memory_context += "\nRelevant facts:\n"
                        for entity, facts in state.facts.items():
                            if facts:  # Only show entities with facts
                                memory_context += f"\nFacts about {entity}:\n"
                                for fact in facts:
                                    memory_context += f"- {fact}\n"

                if memory_context:
                    new_content += f"\n{memory_context}"

                # Process knowledge base context
                (
                    knowledge_context,
                    topic_count,
                    file_count,
                ) = self._format_knowledge_base_context(state)

                if knowledge_context:
                    new_content += f"\n{knowledge_context}"

                    logger.info(
                        "Added knowledge base context to system prompt",
                        props={
                            "topic_count": topic_count,
                            "file_count": file_count,
                            "context_length": len(knowledge_context),
                        },
                    )

                # Add instructions for tool usage
                tool_usage_instructions = (
                    "\n\nImportant Instructions for Tool Usage:\n"
                    "1. When using query-based tools like Data Insights Explorer, pass the user's query faithfully, "
                    "preserving all specified entities, team names, filters, and conditions.\n"
                    "2. Do not simplify or rephrase the user's request in ways that lose important details.\n"
                )
                # Prepend to ensure they are at the beginning of the system message modifications
                new_content = tool_usage_instructions + new_content

                if system_message_idx is not None:
                    messages_for_llm[system_message_idx] = SystemMessage(
                        content=new_content
                    )
                else:
                    system_msg = SystemMessage(content=new_content)
                    messages_for_llm.insert(0, system_msg)
                logger.info(
                    "Created new system message",
                    props={
                        "thread_id": state.get("thread_id", "unknown"),
                        "system_length": len(new_content),
                        "has_ticket_data": bool(use_real_time_context and ticket_data),
                        "has_memory": bool(use_memory_context and memory_context),
                        "has_knowledge_context": bool(knowledge_context),
                    },
                )
            except Exception as e:
                logger.error(
                    f"Error adding ticket data and memory context to system message: {str(e)}",
                    props={
                        "thread_id": state.get("thread_id", "unknown"),
                        "error_type": type(e).__name__,
                    },
                )
                logger.error(traceback.format_exc(), props={})
                # Continue with original messages if there's an error
                messages_for_llm = original_messages

            # Safely extract deployment_type from config metadata
            deployment_type = None
            if isinstance(config, dict):
                deployment_type = config.get("metadata", {}).get("deployment_type")

            # Get tools with organization and agent context, filtered by deployment type
            tools = await get_available_tools(
                organization_id=self.organization_id,
                agent_id=self.agent_id,
                deployment_type=deployment_type,
            )

            # Add debug logging for tools
            logger.info(
                f"Retrieved {len(tools)} tools for binding",
                props={
                    "tool_names": [getattr(tool, "name", str(tool)) for tool in tools],
                    "thread_id": state.get("thread_id", "unknown"),
                },
            )

            # Credentials should now be available in the config passed from service layer

            # Log whether credentials are available in the config
            credentials_available = (
                config
                and "configurable" in config
                and "thena_api_key" in config["configurable"]
                and "thena_org_id" in config["configurable"]
            )

            logger.info(
                "LLM configuration status",
                props={
                    "thread_id": state.get("thread_id", "unknown"),
                    "credentials_available": credentials_available,
                    "config_keys": list(config.keys()) if config else [],
                },
            )

            # Ensure we have tools to bind
            if not tools:
                logger.warning(
                    "No tools available for binding, LLM will be used without tools",
                    props={"thread_id": state.get("thread_id", "unknown")},
                )
                llm_with_tools = self._llm
            else:
                # Bind tools to the LLM
                llm_with_tools = self._llm.bind_tools(tools)

            # Log the LLM configuration
            logger.info(
                "LLM configured with tools",
                props={
                    "thread_id": state.get("thread_id", "unknown"),
                    "llm_type": type(self._llm).__name__,
                    "llm_with_tools_type": type(llm_with_tools).__name__,
                    "tool_count": len(tools),
                },
            )

            # Check if we have a writer for streaming
            if writer is not None:
                # Use streaming directly from the LLM
                logger.info("Using direct LLM streaming with writer", props={})

                # Set up variables to collect the final response
                final_content = ""
                final_tool_calls = []
                current_tool_name = ""
                current_tool_id = ""

                try:
                    # Stream the LLM response using astream_chat
                    async for chunk in llm_with_tools.astream(messages_for_llm):
                        # Process the chunk
                        if (
                            hasattr(chunk, "content")
                            and chunk.content is not None
                            and chunk.content != ""
                        ):
                            # Update the final content
                            final_content += chunk.content

                            # Create metadata for the chunk
                            metadata = {
                                "thread_id": state.get("thread_id", "unknown"),
                                "content_type": "text",
                                "tags": ["llm_response"],
                            }

                            # Stream the chunk with metadata
                            writer((chunk, metadata))

                        # Check for tool calls
                        if (
                            hasattr(chunk, "additional_kwargs")
                            and chunk.additional_kwargs
                        ):
                            # Initialize final_tool_calls if it doesn't exist
                            if not final_tool_calls:
                                final_tool_calls = []

                            # Process tool calls from additional_kwargs
                            if (
                                "tool_calls" in chunk.additional_kwargs
                                and chunk.additional_kwargs["tool_calls"]
                            ):
                                for tc in chunk.additional_kwargs["tool_calls"]:
                                    # Check if we have a function name
                                    if tc.get("function").get("name"):
                                        current_tool_name = tc["function"]["name"]
                                        current_tool_id = tc.get("id", "")
                                        # Add new tool call with empty arguments
                                        new_tool_call = {
                                            "name": current_tool_name,
                                            "args": {},
                                            "args_str": "",
                                            "id": tc.get("id", ""),
                                            "type": "tool_call",
                                        }
                                        final_tool_calls.append(new_tool_call)

                                        logger.debug(
                                            "Added new tool call from additional_kwargs",
                                            props={
                                                "thread_id": state.get(
                                                    "thread_id", "unknown"
                                                ),
                                                "tool_call": new_tool_call,
                                            },
                                        )

                                    # Check if we have arguments to add
                                    if (
                                        tc.get("function", {}).get("arguments")
                                        and current_tool_name
                                    ):
                                        # Find the tool call for the current tool name
                                        existing_tc = next(
                                            (
                                                t
                                                for t in final_tool_calls
                                                if t.get("id") == current_tool_id
                                            ),
                                            None,
                                        )
                                        if existing_tc:
                                            # Accumulate arguments
                                            existing_tc["args_str"] += tc["function"][
                                                "arguments"
                                            ]
                                            # Update final_tool_calls
                                            final_tool_calls[
                                                final_tool_calls.index(existing_tc)
                                            ] = existing_tc

                    # Parse args_str into JSON and clean up
                    for tc in final_tool_calls:
                        if "args_str" in tc and tc["args_str"]:
                            try:
                                # Parse args_str as JSON
                                parsed_args = json.loads(tc["args_str"])
                                # Store parsed object in args
                                tc["args"] = parsed_args
                                # Remove args_str field
                                tc.pop("args_str", None)

                                logger.debug(
                                    "Parsed arguments for tool call",
                                    props={
                                        "thread_id": state.get("thread_id", "unknown"),
                                        "tool_name": tc.get("name"),
                                        "parsed_args": parsed_args,
                                    },
                                )
                            except json.JSONDecodeError as e:
                                logger.error(
                                    "Failed to parse arguments JSON",
                                    props={
                                        "thread_id": state.get("thread_id", "unknown"),
                                        "tool_name": tc.get("name"),
                                        "args_str": tc.get("args_str"),
                                        "error": str(e),
                                    },
                                )
                                # Keep empty args if parsing fails
                                tc.pop("args_str", None)

                except Exception as e:
                    # Handle different types of errors
                    error_message = "I am unable to process your request as the chat service is experiencing issues."
                    error_type = "llm_error"

                    # Check for authentication errors
                    if (
                        "401" in str(e)
                        or "authentication" in str(e).lower()
                        or "api key" in str(e).lower()
                        or "unauthorized" in str(e).lower()
                    ):
                        error_message = "I'm unable to respond due to an authentication issue with the AI service."
                        error_type = "authentication_error"

                    # Log the error
                    logger.error(
                        f"Error during LLM streaming: {str(e)}",
                        props={
                            "thread_id": state.get("thread_id", "unknown"),
                            "error_type": error_type,
                            "error": str(e),
                        },
                        exc_info=True,
                    )

                    # Create an error message chunk
                    error_chunk = AIMessage(content=error_message)

                    # Create metadata for the error chunk
                    metadata = {
                        "thread_id": state.get("thread_id", "unknown"),
                        "content_type": "text",
                        "tags": ["llm_response", "error", error_type],
                        "error": str(e),
                    }

                    # Stream the error message
                    writer((error_chunk, metadata))
                    state["error"] = f"{error_type}: {str(e)}"
                    final_content = error_message
                    final_tool_calls = []

                # Create the final response
                response = AIMessage(content=final_content, tool_calls=final_tool_calls)

            else:
                # Fallback to non-streaming mode
                logger.info("Using non-streaming LLM invocation", props={})

                # Safely extract deployment_type from config metadata (again for clarity, or reuse from above)
                # deployment_type is already extracted above, reuse it.

                # Get tools with organization and agent context, filtered by deployment type
                tools = await get_available_tools(
                    organization_id=self.organization_id,
                    agent_id=self.agent_id,
                    deployment_type=deployment_type,  # Pass extracted type
                )

                # Add debug logging for tools
                logger.info(
                    f"Retrieved {len(tools)} tools for non-streaming invocation",
                    props={
                        "tool_names": [
                            getattr(tool, "name", str(tool)) for tool in tools
                        ],
                        "thread_id": state.get("thread_id", "unknown"),
                    },
                )

                # Ensure we have tools to bind
                if not tools:
                    logger.warning(
                        "No tools available for non-streaming invocation",
                        props={"thread_id": state.get("thread_id", "unknown")},
                    )
                    # Invoke the LLM without tools
                    response = await self._llm.ainvoke(
                        messages_for_llm,
                    )
                else:
                    # Invoke the LLM with tools
                    response = await self._llm.ainvoke(
                        messages_for_llm,
                        tools=tools,
                    )

            # Update the state with the response
            # updated_state = state.copy()
            state["messages"].append(response)
            # state["messages"].append()

            # Return the updated state (routing will be handled by _route_from_agent)
            self.state = state
            return state
        except Exception as e:
            logger.error(f"Error in agent node: {e}", props={})
            logger.error(traceback.format_exc(), props={})

            # Return the state with an error message
            error_message = f"Error processing agent response: {str(e)}"
            # updated_state = state.copy()
            state["error"] = error_message
            self.state = state

            # Return the updated state
            return state

    async def _tools_node(
        self, state: AgentState, config: RunnableConfig = None
    ) -> Dict[str, Any]:
        """
        Process the tools node in the graph.

        Args:
            state: The current state
            config: The RunnableConfig with credentials

        Returns:
            Dict[str, Any]: The updated state with next node
        """
        try:
            # Process tool calls
            result = []

            # Log the tool calls from the message
            if state["messages"] and hasattr(state["messages"][-1], "tool_calls"):
                tool_calls = state["messages"][-1].tool_calls
                logger.info(
                    f"Processing {len(tool_calls)} tool calls",
                    props={
                        "tool_calls": [
                            {
                                "name": tc.get("name", "unknown"),
                                "args": tc.get("args", {}),
                            }
                            for tc in tool_calls
                        ],
                        "thread_id": state.get("thread_id", "unknown"),
                    },
                )
            else:
                logger.warning(
                    "No tool calls found in the last message",
                    props={"thread_id": state.get("thread_id", "unknown")},
                )

            # Log whether credentials are available in the config
            credentials_available = (
                config
                and "configurable" in config
                and "thena_api_key" in config["configurable"]
                and "thena_org_id" in config["configurable"]
            )

            logger.info(
                "Tool execution configuration status",
                props={
                    "thread_id": state.get("thread_id", "unknown"),
                    "credentials_available": credentials_available,
                    "config_keys": list(config.keys()) if config else [],
                },
            )

            for tool_call in state["messages"][-1].tool_calls:
                tool_name = tool_call["name"]

                # Safely extract deployment_type from config metadata
                node_deployment_type = None
                if isinstance(config, dict):
                    node_deployment_type = config.get("metadata", {}).get(
                        "deployment_type"
                    )

                # Get tools with organization and agent context, filtered by deployment type
                tools_by_name = {
                    tool.name: tool
                    for tool in await get_available_tools(
                        organization_id=self.organization_id,
                        agent_id=self.agent_id,
                        deployment_type=node_deployment_type,  # Use extracted type
                    )
                }

                # Log available tools
                logger.info(
                    f"Available tools for execution: {list(tools_by_name.keys())}",
                    props={
                        "thread_id": state.get("thread_id", "unknown"),
                        "tool_count": len(tools_by_name),
                    },
                )

                # Check if we have any tools available
                if not tools_by_name:
                    error_message = f"No tools available for execution. Tool '{tool_name}' cannot be found."
                    logger.error(error_message, props={"tool_name": tool_name})
                    result.append(
                        ToolMessage(content=error_message, tool_call_id=tool_call["id"])
                    )
                    continue

                if tool_name in tools_by_name:
                    tool = tools_by_name[tool_name]
                    logger.info(
                        f"Executing tool: {tool_name}",
                        props={
                            "tool_name": tool_name,
                            "args": tool_call["args"],
                            "thread_id": state.get("thread_id", "unknown"),
                        },
                    )

                    try:
                        # Check if the tool is async or sync and invoke accordingly
                        if hasattr(tool, "ainvoke"):
                            # Async tool with config
                            observation = await tool.ainvoke(
                                tool_call["args"],
                                config=config,  # Pass the config directly
                            )
                        else:
                            # Sync tool with config
                            observation = tool.invoke(
                                tool_call["args"],
                                config=config,  # Pass the config directly
                            )

                        result.append(
                            ToolMessage(
                                content=observation, tool_call_id=tool_call["id"]
                            )
                        )
                    except Exception as e:
                        # Convert the error to a tool message that the LLM can respond to
                        error_message = str(e)
                        logger.error(
                            f"Error executing tool {tool_name}: {error_message}",
                            props={
                                "tool_name": tool_name,
                                "args": tool_call["args"],
                                "error": error_message,
                                "thread_id": state.get("thread_id", "unknown"),
                            },
                        )
                        result.append(
                            ToolMessage(
                                content=f"Error: {error_message}",
                                tool_call_id=tool_call["id"],
                            )
                        )
                else:
                    error_message = f"Tool '{tool_name}' not found"
                    logger.error(error_message, props={"tool_name": tool_name})
                    result.append(
                        ToolMessage(content=error_message, tool_call_id=tool_call["id"])
                    )

            # Update the state with the tool outputs
            state["last_tool_outputs"].append(result)
            state["current_tool"] = None
            state["messages"].extend(result)
            self.state = state

            # Return the updated state
            return state
        except Exception as e:
            logger.error(f"Error in tools node: {e}", props={})
            logger.error(traceback.format_exc(), props={})

            # Create a tool message for the error instead of setting an error state
            error_message = f"Error processing tools: {str(e)}"
            state["error"] = error_message  # Add error to state
            updated_state = state.copy()

            # If we have tool calls, create error messages for each one
            if state["messages"] and hasattr(state["messages"][-1], "tool_calls"):
                tool_calls = state["messages"][-1].tool_calls
                error_messages = []

                for tool_call in tool_calls:
                    error_messages.append(
                        ToolMessage(
                            content=error_message,
                            tool_call_id=tool_call.get("id", "unknown"),
                        )
                    )

                updated_state["last_tool_outputs"].append(error_messages)
                updated_state["messages"].extend(error_messages)
            else:
                # Fallback if no tool calls are found
                dummy_tool_message = ToolMessage(
                    content=error_message, tool_call_id="error"
                )
                updated_state["last_tool_outputs"].append([dummy_tool_message])
                updated_state["messages"].append(dummy_tool_message)
            self.state = updated_state
            # Return the updated state
            return updated_state

    async def _suggestions_node(
        self, state: AgentState, config: RunnableConfig = None, writer=None
    ) -> Dict[str, Any]:
        """
        Process the suggestions node in the graph.

        This node generates contextually relevant follow-up questions based on the
        conversation history and the most recent assistant response.

        Args:
            state: The current state
            config: RunnableConfig with configuration for the node
            writer: Optional writer function for streaming

        Returns:
            Dict[str, Any]: The updated state with suggestions added
        """
        try:
            start_time = time.time()

            # Get the messages from the state
            messages = state.get("messages", [])

            # Get the thread ID for logging
            thread_id = state.get("thread_id", "unknown")

            # Get agent configuration for suggestions - check both possible locations
            prompt_source = "default"
            custom_suggestion_prompt = None
            suggestion_prompt_name = "agent_processor_suggestions"  # Default name

            # First check if state has 'config' attribute (pydantic model)
            if hasattr(state, "config") and state.config:
                # Check if config has the suggestions_prompt attribute
                if hasattr(state.config, "suggestions_prompt"):
                    custom_suggestion_prompt = state.config.suggestions_prompt
                    prompt_source = "config"
                    logger.info(
                        "Using custom suggestion prompt from state.config",
                        props={
                            "thread_id": thread_id,
                            "prompt_length": (
                                len(custom_suggestion_prompt)
                                if custom_suggestion_prompt
                                else 0
                            ),
                        },
                    )

            if prompt_source == "default":
                try:
                    prompt_data = load_prompt(suggestion_prompt_name)
                    prompt_source = "library"
                    # We'll format it later when we have the history and assistant response
                    custom_suggestion_prompt = prompt_data
                    logger.info(
                        "Using default suggestion prompt from prompt library",
                        props={"thread_id": thread_id},
                    )
                except Exception as e:
                    logger.exception(
                        f"Error loading default suggestion prompt: {e}",
                        props={"thread_id": thread_id},
                    )
                    raise

            # Find the most recent user message and assistant response
            user_message = ""
            assistant_response = ""

            # Extract the most recent user message
            for msg in reversed(messages):
                if isinstance(msg, HumanMessage):
                    user_message = msg.content
                    break

            # Extract the most recent assistant response
            for msg in reversed(messages):
                if isinstance(msg, AIMessage) and not isinstance(msg, AIMessageChunk):
                    assistant_response = msg.content
                    break

            logger.info(
                "Generating suggestions in suggestions node",
                props={
                    "thread_id": thread_id,
                    "user_message_length": len(user_message),
                    "assistant_response_length": len(assistant_response),
                    "using_custom_prompt": custom_suggestion_prompt != None,
                },
            )

            # Skip suggestion generation if we don't have enough context
            if not messages or len(messages) < 2:
                logger.info(
                    "Not enough conversation history for suggestions, using defaults",
                    props={"history_length": len(messages) if messages else 0},
                )
                suggestions = ["Tell me more", "How does this work?"]
            else:
                # Create a suggestion generation prompt using the custom prompt from agent config
                # Get the last few messages for context
                recent_history = messages[-5:]
                history_summary = "\n".join(
                    [
                        f"{'User' if isinstance(m, HumanMessage) else 'Assistant'}: {m.content}"
                        for m in recent_history
                    ]
                )
                suggestion_prompt = None
                suggestion_request = None

                # Format the prompt with parameters if it's from the prompt library
                if prompt_source == "config":
                    # This is a plain string, use it directly
                    formatted_prompt = custom_suggestion_prompt
                    suggestion_prompt = SystemMessage(content=formatted_prompt)
                    # Get the last few messages for context
                    recent_history = messages[-5:]
                    history_summary = "\n".join(
                        [
                            f"{'User' if isinstance(m, HumanMessage) else 'Assistant'}: {m.content}"
                            for m in recent_history
                        ]
                    )
                    suggestion_context = (
                        f"Conversation history:\n{history_summary}\n\n"
                        f"Current assistant response:\n{assistant_response}\n\n"
                        f"Generate 2 very concise follow-up suggestions for the user:"
                    )
                    suggestion_request = HumanMessage(content=suggestion_context)
                elif prompt_source == "library":
                    # This is a prompt from the library, format it with parameters
                    formatted_prompt = format_prompt(
                        custom_suggestion_prompt,
                        history_summary=history_summary,
                        assistant_response=assistant_response,
                    )
                    suggestion_prompt = SystemMessage(content=formatted_prompt)
                    suggestion_request = HumanMessage(
                        content="Generate follow-up suggestions"
                    )

                else:
                    logger.exception(
                        f"Invalid prompt source: {prompt_source}",
                        props={"thread_id": thread_id},
                    )
                    raise ValueError(f"Invalid prompt source: {prompt_source}")

                # Use the LLM to generate suggestions with a timeout
                try:
                    # Create a special config for suggestion generation
                    suggestion_config = {
                        "temperature": 0.7,  # Higher temperature for more diverse suggestions
                        "timeout": 25.0,  # Timeout after 5 seconds to avoid slowing down the response
                    }

                    # Generate suggestions using the LLM
                    suggestion_response = await self._llm.ainvoke(
                        [suggestion_prompt, suggestion_request],
                        config=suggestion_config,
                    )

                    # Parse the response into individual suggestions
                    suggestions = [
                        s.strip()
                        for s in suggestion_response.content.split("\n")
                        if s.strip()
                    ]

                    # Limit to max 2 suggestions and ensure they're not too long
                    valid_suggestions = []
                    for s in suggestions[:2]:
                        # Keep suggestions reasonably short
                        if len(s) <= 40:
                            valid_suggestions.append(s)
                        else:
                            # Truncate and add ellipsis if too long
                            valid_suggestions.append(s[:37] + "...")

                    # Make sure we have exactly 2 suggestions
                    if len(valid_suggestions) < 2:
                        default_suggestions = [
                            "Tell me more",
                            "What features are included?",
                        ]
                        valid_suggestions.extend(
                            default_suggestions[len(valid_suggestions) : 2]
                        )

                    suggestions = valid_suggestions[:2]  # Ensure we return exactly 2

                except Exception as e:
                    logger.warning(
                        f"Error generating suggestions: {str(e)}, falling back to defaults",
                        props={"error": str(e)},
                    )
                    suggestions = ["Tell me more", "Need any clarification?"]

            # Ensure we have exactly 2 suggestions by adding default ones if needed
            if len(suggestions) < 2:
                default_suggestions = ["Tell me more", "What else can you help with?"]
                suggestions.extend(default_suggestions[len(suggestions) :])
                logger.info(
                    f"Added {2 - len(suggestions)} default suggestions to reach minimum of 2",
                    props={"thread_id": thread_id, "final_suggestions": suggestions},
                )

            # Update the state with the raw suggestions
            updated_state = state.copy()

            duration = time.time() - start_time
            logger.info(
                f"Generated {len(suggestions)} raw suggestions in {duration:.4f}s",
                props={
                    "suggestions": suggestions,
                    "generation_time": duration,
                    "thread_id": thread_id,
                },
            )

            return updated_state
        except Exception as e:
            logger.error(f"Error in suggestions node: {e}", props={})
            logger.error(traceback.format_exc(), props={})

            # Add error information to the state before returning
            state["error"] = f"Suggestion generation failed: {str(e)}"  # Add this line

            # Return the original state without modifications if there's an error
            return state

    def _process_suggestions_content(
        self, suggestions_content: str, thread_id: str
    ) -> Tuple[List[str], str]:
        """
        Process accumulated suggestions content to extract and format suggestions.

        Args:
            suggestions_content: The accumulated content from the suggestions node
            thread_id: The thread ID for logging

        Returns:
            Tuple[List[str], str]: A tuple containing the list of suggestions and the formatted suggestions text
        """
        # Extract suggestions from the accumulated content
        suggestions = []

        # Look for the suggestions block in the accumulated content
        for marker in ["```suggestions", "```suggestion"]:
            if marker in suggestions_content:
                start_idx = suggestions_content.find(marker)
                end_idx = suggestions_content.find("```", start_idx + len(marker))
                if end_idx > start_idx:
                    # Extract just the suggestions text
                    suggestions_text = suggestions_content[
                        start_idx + len(marker) : end_idx
                    ].strip()
                    # Parse the suggestions into an array
                    suggestions = [
                        s.strip() for s in suggestions_text.split("\n") if s.strip()
                    ]
                    break

        # If we couldn't find a suggestions block, try to extract from the whole content
        if not suggestions and "```" not in suggestions_content:
            # Try to extract suggestions from the whole content
            lines = suggestions_content.split("\n")
            for line in lines:
                line = line.strip()
                if line and not line.startswith("```") and len(line) <= 40:
                    suggestions.append(line)

        # Limit to 2 suggestions
        suggestions = suggestions[:2]

        # Ensure we have exactly 2 suggestions by adding default ones if needed
        if len(suggestions) < 2:
            default_suggestions = ["Tell me more", "What else can you help with?"]
            suggestions.extend(default_suggestions[len(suggestions) : 2])
            logger.info(
                f"Added {2 - len(suggestions)} default suggestions to reach minimum of 2",
                props={"thread_id": thread_id, "final_suggestions": suggestions},
            )

        # Format the suggestions for inclusion in the completed event
        suggestions_text = ""
        if suggestions:
            suggestions_text = f"\n\n```suggestions\n{suggestions[0]}\n{suggestions[1] if len(suggestions) > 1 else ''}\n```"
            logger.info(
                "Formatted suggestions for inclusion in completed event",
                props={"thread_id": thread_id, "suggestions": suggestions},
            )

        return suggestions, suggestions_text

    async def _cache_entities_and_facts(
        self, state: AgentState, new_entities: set, new_facts: dict
    ):
        """Cache entities and facts for the given state."""
        thread_key = state.get("thread_id", "")
        if thread_key:
            # Store entities as a list (converted from set)
            await self._cache.set_thread(
                f"entities:{thread_key}",
                list(state.entities),
                ttl=3600,  # Cache for 1 hour
            )

            # Store facts as entity-keyed dictionary
            await self._cache.set_thread(
                f"facts:{thread_key}",
                {
                    entity: facts_list
                    for entity, facts_list in state.facts.items()
                    if entity in state.entities  # Only cache facts for known entities
                },
                ttl=3600,
            )

    async def process_message(
        self,
        message: str,
        thread_id: str,
        initial_state: Optional[AgentState] = None,
        config: Optional[RunnableConfig] = None,
    ) -> AsyncGenerator[Union[Dict[str, Any], Tuple[Dict[str, Any], AgentState]], None]:
        """
        Process a message and generate a response.

        Args:
            message: The message to process
            thread_id: The ID of the thread
            tools: The list of available tools
            system_message: Optional system message to include
            checkpoint_id: Optional checkpoint ID to restore from
            initial_state: Optional initial state to use
            config: Optional RunnableConfig passed from service layer with credentials and ticket data

        Yields:
            Union[Dict[str, Any], Tuple[Dict[str, Any], AgentState]]: Events during processing and final state
        """
        start_time = time.time()
        message_id = str(uuid4())
        run_id = str(uuid4())
        ai_message_id = str(uuid4())
        step_id = str(uuid4())
        assistant_id = str(uuid4())
        timestamp = int(datetime.now().timestamp())

        # Add this line to track token timings
        token_timings = []

        try:
            logger.info(
                f"Processing message for thread {thread_id}",
                props={"message_id": message_id, "run_id": run_id},
            )

            state = initial_state

            # We no longer need to generate the initial events here as they are now generated in service.py
            # First event: thread.run.created, Second event: thread.run.step.created, Third event: thread.message.created
            # are all handled by the service layer

            # Yield events in the correct sequence
            # 1. First event: thread.run.created
            run_created_event = RunCreatedEvent(
                data=RunCreatedData(
                    id=f"run_{run_id}",
                    thread_id=f"thread_{thread_id}",
                    assistant_id=f"asst_{assistant_id}",
                    created_at=timestamp,
                )
            )
            yield run_created_event.model_dump()

            # 2. Second event: thread.run.step.created
            run_step_created_event = RunStepCreatedEvent(
                data=RunStepCreatedData(
                    id=f"step_{step_id}",
                    run_id=f"run_{run_id}",
                    step_details=StepDetails(
                        type="message_creation",
                        message_creation=MessageCreation(
                            message_id=f"msg_{ai_message_id}"
                        ),
                    ),
                    completed_at=None,
                    cancelled_at=None,
                    failed_at=None,
                    status="in_progress",
                )
            )
            yield run_step_created_event.model_dump()

            # 3. Third event: thread.message.created
            message_created_event = MessageCreatedEvent(
                data=MessageCreatedData(
                    id=f"msg_{ai_message_id}",
                    role="assistant",
                    created_at=timestamp,
                    content=[],
                    run_id=f"run_{run_id}",
                    assistant_id=f"asst_{assistant_id}",
                    thread_id=f"thread_{thread_id}",
                    attachments=[],
                    metadata={},
                    runSteps=[],
                    status="in_progress",
                )
            )
            yield message_created_event.model_dump()

            # Process the message using the graph
            accumulated_content = ""
            current_tools = {}

            # Variables to accumulate suggestions
            suggestions_content = ""
            suggestions_node_detected = False

            logger.info(
                "Starting LangGraph streaming for message processing",
                props={
                    "thread_id": thread_id,
                    "message_id": message_id,
                    "run_id": run_id,
                },
            )

            # Log the config being passed to the graph
            if config:
                config_has_credentials = (
                    "configurable" in config
                    and "thena_api_key" in config.get("configurable", {})
                    and "thena_org_id" in config.get("configurable", {})
                )

                # Check for ticket data in both locations
                has_ticket_data = (
                    "metadata" in config and "ticket_data" in config.get("metadata", {})
                ) or (
                    "configurable" in config
                    and "raw_ticket_data" in config.get("configurable", {})
                )

                logger.info(
                    "Using RunnableConfig passed from service layer",
                    props={
                        "thread_id": thread_id,
                        "has_credentials": config_has_credentials,
                        "has_ticket_data": has_ticket_data,
                        "config_keys": list(config.keys()) if config else [],
                    },
                )
            else:
                logger.warning(
                    "No config provided from service layer, creating minimal config",
                    props={"thread_id": thread_id},
                )
                # Create a minimal config with thread_id if none is provided
                config = {"metadata": {"thread_id": thread_id}, "configurable": {}}

            # Use stream_mode="messages" to get streaming tokens directly from the LLM
            async for msg, metadata in self._graph.astream(
                state,
                stream_mode="messages",
                config=config,  # Use the config directly from service layer
            ):
                # Check if this is from the suggestions node
                is_suggestions_node = (
                    metadata
                    and "langgraph_node" in metadata
                    and metadata["langgraph_node"] == "suggestions"
                )

                # If this is from the suggestions node, accumulate the content
                if is_suggestions_node:
                    suggestions_node_detected = True
                    if hasattr(msg, "content") and msg.content:
                        logger.info(
                            "Accumulating content from suggestions node",
                            props={
                                "thread_id": thread_id,
                                "content_chunk": (
                                    msg.content[:50] + "..."
                                    if len(msg.content) > 50
                                    else msg.content
                                ),
                            },
                        )
                        suggestions_content += msg.content
                    continue

                # Process tool calls if present in the message
                if (
                    hasattr(msg, "additional_kwargs")
                    and msg.additional_kwargs
                    and "tool_calls" in msg.additional_kwargs
                    and msg.additional_kwargs["tool_calls"]
                ):
                    for tool_call in msg.additional_kwargs["tool_calls"]:
                        if tool_call.get("function").get("name"):
                            tool_id = str(uuid4())
                            tool_call_id = tool_call.get("id")
                            function_name = tool_call.get("function").get("name")
                            display_name = self._get_display_name(function_name)
                            current_tools[tool_id] = display_name
                            logger.info(
                                "Tool display name mapping",
                                props={
                                    "thread_id": thread_id,
                                    "tool_id": tool_id,
                                    "function_name": function_name,
                                    "display_name": display_name,
                                },
                            )

                            tool_step_created = ToolStepCreatedEvent(
                                data=ToolStepCreatedData(
                                    id=tool_id,
                                    run_id=f"run_{run_id}",
                                    step_details=StepDetails(
                                        type="tool_calls", tool_calls=[]
                                    ),
                                    completed_at=None,
                                    cancelled_at=None,
                                    failed_at=None,
                                    status="in_progress",
                                )
                            )
                            yield tool_step_created.model_dump()

                            tool_step_delta = ToolStepDeltaEvent(
                                data=ToolStepDeltaData(
                                    run_id=f"run_{run_id}",
                                    id=tool_id,
                                    delta=ToolStepDeltaDelta(
                                        step_details=ToolStepDeltaStepDetails(
                                            type="tool_calls",
                                            tool_calls=[
                                                ToolStepDeltaToolCall(
                                                    id=tool_call_id,
                                                    type="function",
                                                    index=0,
                                                    function={
                                                        "name": display_name,
                                                        "arguments": "",
                                                    },
                                                )
                                            ],
                                        )
                                    ),
                                )
                            )
                            yield tool_step_delta.model_dump()

                # Process the streamed message
                if (
                    hasattr(msg, "content")
                    and msg.content is not None
                    and msg.content != ""
                    and isinstance(msg, AIMessageChunk)
                ):
                    if current_tools:
                        completion_timestamp = int(datetime.now().timestamp())

                        for tool_id, tool_name in current_tools.items():
                            tool_step_completed = ToolStepCompletedEvent(
                                data=ToolStepCompletedData(
                                    id=tool_id,
                                    run_id=f"run_{run_id}",
                                    step_details=StepDetails(
                                        type="tool_calls",
                                        tool_calls=[
                                            ToolCall(
                                                id=tool_id,
                                                type="function",
                                                index=0,
                                                function=ToolCallFunction(
                                                    name=tool_name,
                                                    arguments="{}",
                                                    output="{}",
                                                ),
                                            )
                                        ],
                                    ),
                                    completed_at=completion_timestamp,
                                    cancelled_at=None,
                                    failed_at=None,
                                    status="completed",
                                )
                            )
                            yield tool_step_completed.model_dump()

                        message_completed = MessageCompletedEvent(
                            data=MessageCompletedData(
                                id=f"msg_{ai_message_id}",
                                role="assistant",
                                created_at=completion_timestamp,
                                content=[
                                    TextItem(
                                        index=0,
                                        type="text",
                                        text=TextContent(
                                            value="",
                                            annotations=[],
                                        ),
                                    )
                                ],
                                run_id=f"run_{run_id}",
                                assistant_id=f"asst_{assistant_id}",
                                thread_id=f"thread_{thread_id}",
                                attachments=[],
                                metadata={},
                                runSteps=[],
                                status="completed",
                            ),
                        )
                        yield message_completed.model_dump()

                        ai_message_id = str(uuid4())
                        run_id = str(uuid4())
                        step_id = str(uuid4())

                        run_created_event = RunCreatedEvent(
                            data=RunCreatedData(
                                id=f"run_{run_id}",
                                thread_id=f"thread_{thread_id}",
                                assistant_id=f"asst_{assistant_id}",
                                created_at=timestamp,
                            )
                        )
                        yield run_created_event.model_dump()

                        run_step_created = RunStepCreatedEvent(
                            data=RunStepCreatedData(
                                id=f"step_{step_id}",
                                run_id=f"run_{run_id}",
                                step_details=StepDetails(
                                    type="message_creation",
                                    message_creation=MessageCreation(
                                        message_id=f"msg_{ai_message_id}"
                                    ),
                                ),
                                completed_at=None,
                                cancelled_at=None,
                                failed_at=None,
                                status="in_progress",
                            )
                        )
                        yield run_step_created.model_dump()

                        message_created_event = MessageCreatedEvent(
                            data=MessageCreatedData(
                                id=f"msg_{ai_message_id}",
                                role="assistant",
                                created_at=int(datetime.now().timestamp()),
                                content=[],
                                run_id=f"run_{run_id}",
                                assistant_id=f"asst_{assistant_id}",
                                thread_id=f"thread_{thread_id}",
                                attachments=[],
                                metadata={},
                                runSteps=[],
                                status="in_progress",
                            )
                        )
                        yield message_created_event.model_dump()

                        current_tools = {}

                    # Get the new content (delta)
                    current_content = msg.content

                    # Record token timing
                    current_time = time.time()
                    time_from_start = current_time - start_time
                    token_timings.append(
                        {
                            "token": current_content,
                            "time_from_start": time_from_start,
                            "timestamp": current_time,
                        }
                    )

                    logger.debug(
                        "Received new content chunk from LLM",
                        props={
                            "content_length": len(current_content),
                            "accumulated_length": len(accumulated_content),
                            "thread_id": thread_id,
                            "time_from_start": time_from_start,  # Add timing info to logs
                        },
                    )

                    # # Check if the current content contains the original message
                    # # This prevents the original question from being included in the response
                    # if message in current_content:
                    #     # Remove the original message from the content
                    #     current_content = current_content.replace(message, "").strip()
                    #     logger.debug(
                    #         "Removed original message from content",
                    #         props={
                    #             "new_content_length": len(current_content),
                    #             "thread_id": thread_id,
                    #         },
                    #     )

                    # # Skip empty content
                    # if not current_content or not current_content.strip():
                    #     logger.debug(
                    #         "Skipping empty content", props={"thread_id": thread_id}
                    #     )
                    #     continue

                    # Process the content based on what we've accumulated so far
                    # --- REMOVED COMPLEX OVERLAP LOGIC ---
                    # The original complex logic (if/elif/else for startswith and partial overlap)
                    # spanning approximately from line 1978 to 2040 has been removed.

                    # Assume current_content IS the delta
                    delta_content = current_content

                    # # Skip empty deltas
                    # if not delta_content or not delta_content.strip():
                    #     logger.debug(
                    #         "Skipping empty delta content",
                    #         props={"thread_id": thread_id},
                    #     )
                    #     continue  # Use continue to skip the rest of the loop for this chunk

                    logger.debug(
                        "Processing delta content chunk",
                        props={
                            "delta_length": len(delta_content),
                            "accumulated_length_before": len(accumulated_content),
                            "thread_id": thread_id,
                        },
                    )

                    # Update accumulated_content by appending the delta
                    accumulated_content += delta_content

                    # Only yield if we have new content to send
                    # The check `if delta_content and delta_content.strip():` is implicitly handled
                    # by the `continue` statement earlier for empty deltas.

                    # Generate delta event
                    delta_event = MessageDeltaEvent(
                        data=MessageDeltaData(
                            id=f"msg_{ai_message_id}",
                            object="thread.message.delta",
                            delta=MessageDeltaContent(
                                content=[
                                    TextItem(
                                        index=0,
                                        type="text",
                                        text=TextContent(
                                            value=delta_content,  # Yield the actual delta
                                            annotations=[],
                                        ),
                                    )
                                ]
                            ),
                        ),
                    )
                    yield delta_event.model_dump()

            # If no content was accumulated, use a default message
            if not accumulated_content:
                if self.state.get("error") and (
                    "401" in self.state["error"] or "API key" in self.state["error"]
                ):
                    accumulated_content = f"I'm unable to respond due to an authentication issue with the AI service."
                else:
                    accumulated_content = "I don't have a specific response to that. Could you please provide more details or ask a different question?"

                # Generate a delta event for the default message
                default_event = MessageDeltaEvent(
                    data=MessageDeltaData(
                        id=f"msg_{ai_message_id}",
                        object="thread.message.delta",
                        delta=MessageDeltaContent(
                            content=[
                                TextItem(
                                    index=0,
                                    type="text",
                                    text=TextContent(
                                        value=accumulated_content,
                                        annotations=[],
                                    ),
                                )
                            ]
                        ),
                    ),
                )
                yield default_event.model_dump()

            # Final check to ensure the original message is not in the accumulated content
            if message in accumulated_content:
                accumulated_content = accumulated_content.replace(message, "").strip()
                # If after removing the original message the content is empty, use the default message
                if not accumulated_content:
                    accumulated_content = "I don't have a specific response to that. Could you please provide more details or ask a different question?"

            # Clean any suggestion markers from accumulated content
            suggestion_markers = [
                "```suggestions",
                "```suggestion",
                "suggestions```",
                "suggestion```",
                "```suggestion",
                "suggestionsReady",
                "suggestionsWhat",
                "suggestionsHow",
                "suggestionsCan",
                "suggestionsIs",
                "suggestionsWhere",
                "suggestionsDo",
                "suggestionsAre",
                "suggestionsTell",
            ]

            # First try to remove any instances where suggestions appear directly in the text
            for marker in suggestion_markers:
                if marker in accumulated_content:
                    # Find the position where the first suggestion marker appears
                    marker_pos = accumulated_content.find(marker)
                    if marker_pos > 0:
                        # Only keep the content before the suggestion marker
                        accumulated_content = accumulated_content[:marker_pos].strip()
                        logger.warning(
                            f"Removed suggestion marker and everything after it",
                            props={
                                "thread_id": thread_id,
                                "marker": marker,
                                "position": marker_pos,
                                "content_length_before": len(accumulated_content)
                                + len(marker),
                                "content_length_after": len(accumulated_content),
                            },
                        )

            # Clean any other potential suggestion formatting issues
            lines = accumulated_content.split("\n")
            cleaned_lines = []
            for line in lines:
                if not any(marker in line for marker in suggestion_markers):
                    cleaned_lines.append(line)
            accumulated_content = "\n".join(cleaned_lines)

            # Additional check for suggestion blocks with proper markdown formatting
            suggestion_block_start = accumulated_content.find("```suggestions")
            if suggestion_block_start >= 0:
                suggestion_block_end = accumulated_content.find(
                    "```", suggestion_block_start + 14
                )
                if suggestion_block_end > suggestion_block_start:
                    # Remove the entire suggestions block
                    accumulated_content = accumulated_content[
                        :suggestion_block_start
                    ].strip()
                    logger.info(
                        "Removed properly formatted suggestions block from final content",
                        props={
                            "thread_id": thread_id,
                            "block_start": suggestion_block_start,
                            "block_end": suggestion_block_end,
                        },
                    )

            # Process accumulated suggestions content if we detected the suggestions node
            suggestions_text = ""
            if suggestions_node_detected and suggestions_content:
                suggestions_start_time = time.time()
                logger.info(
                    "Processing accumulated suggestions content",
                    props={
                        "thread_id": thread_id,
                        "content_length": len(suggestions_content),
                    },
                )

                # Process suggestions using the helper function
                suggestions, suggestions_text = self._process_suggestions_content(
                    suggestions_content, thread_id
                )

                suggestions_duration = time.time() - suggestions_start_time
                logger.info(
                    f"Processed suggestions in {suggestions_duration:.4f}s",
                    props={
                        "thread_id": thread_id,
                        "processing_time": suggestions_duration,
                    },
                )
            else:
                # No suggestions, but log the absence
                logger.info(
                    "No suggestions available for this message",
                    props={"thread_id": thread_id},
                )

            # Log message completion
            logger.info(
                f"Message processing complete, yielding completed event",
                props={
                    "thread_id": thread_id,
                    "content_length": len(accumulated_content),
                    "content_preview": (
                        accumulated_content[:50] + "..."
                        if len(accumulated_content) > 50
                        else accumulated_content
                    ),
                },
            )

            # Yield the message completed event with the accumulated content and suggestions
            completion_timestamp = int(datetime.now().timestamp())
            yield MessageCompletedEvent(
                event="thread.message.completed",
                data=MessageCompletedData(
                    id=f"msg_{ai_message_id}",
                    role="assistant",
                    created_at=completion_timestamp,
                    content=[
                        TextItem(
                            index=0,
                            type="text",
                            text=TextContent(
                                value=accumulated_content + suggestions_text,
                                annotations=[],
                            ),
                        )
                    ],
                    run_id=f"run_{run_id}",
                    assistant_id=f"asst_{assistant_id}",
                    thread_id=f"thread_{thread_id}",
                    attachments=[],
                    metadata={},
                    runSteps=[],
                    status="completed",
                ),
            ).model_dump()

            # Yield the run step completed event
            yield RunStepEvent(
                event="thread.run.step.completed",
                data=RunStepData(
                    id=f"step_{step_id}",
                    run_id=f"run_{run_id}",
                    step_details=StepDetails(
                        type="message_creation",
                        message_creation={"message_id": f"msg_{ai_message_id}"},
                    ),
                    completed_at=completion_timestamp,
                    cancelled_at=None,
                    failed_at=None,
                    status="completed",
                ),
            ).model_dump()

            # After processing is complete, log the token timing statistics
            if token_timings:
                total_tokens = len(token_timings)
                first_token_time = (
                    token_timings[0]["time_from_start"] if token_timings else 0
                )
                # TO DO: DO NOT YIELD the below event in the final version of code
                yield {"event": "token_timing", "content": first_token_time}

                # Instead of using props, log the timing information directly in the message
                logger.info(
                    f"Token timing statistics - Thread: {thread_id}, Total tokens: {total_tokens}, "
                    f"First token time: {first_token_time:.4f}s , Total generation time: {time.time() - start_time:.4f}s",
                    props={
                        "thread_id": thread_id,
                        "total_tokens": total_tokens,
                        "first_token_time": first_token_time,
                        "total_generation_time": time.time() - start_time,
                    },
                )

                # Log individual token timings in a single line
                if token_timings:
                    # Calculate time differences between successive tokens
                    time_diffs = []
                    for i in range(len(token_timings)):
                        if i == 0:
                            # First token: time from start
                            time_diffs.append(
                                f"{token_timings[i]['time_from_start']*1000:.1f}ms"
                            )
                        else:
                            # Subsequent tokens: time since previous token
                            diff = (
                                token_timings[i]["time_from_start"]
                                - token_timings[i - 1]["time_from_start"]
                            )
                            time_diffs.append(f"{diff*1000:.1f}ms")

                    # Join all time differences into a single string
                    time_diffs_str = ", ".join(time_diffs[:10])  # First 10 tokens

                    # Log in a single line
                    logger.info(
                        f"Token timing differences (ms): {time_diffs_str}",
                        props={"thread_id": thread_id},
                    )

            duration = time.time() - start_time
            logger.info(
                f"Message processing completed in {duration:.4f} seconds",
                props={"thread_id": thread_id, "duration": duration},
            )

            # Return the final updated state as the last yield
            # We'll wrap it in a special event type that service.py can recognize
            temp_messages = self.state["messages"]
            last_message = temp_messages[-1]
            if hasattr(last_message, "content") and isinstance(last_message, AIMessage):
                last_message.content = accumulated_content + suggestions_text
            temp_messages[-1] = last_message
            self.state["messages"] = temp_messages

            # Initialize state from cache with proper typing
            thread_key = self.state.get("thread_id", "")
            if thread_key:
                cached_entities = await self._cache.get_thread(f"entities:{thread_key}")
                cached_facts = await self._cache.get_thread(f"facts:{thread_key}")

                # Restore entities as a set
                if cached_entities:
                    self.state.entities = set(cached_entities)

                # Restore facts dictionary with entity-fact mapping
                if cached_facts and isinstance(cached_facts, dict):
                    self.state.facts = {
                        entity: facts_list
                        for entity, facts_list in cached_facts.items()
                        if isinstance(facts_list, list)  # Type safety check
                    }

            yield {"type": "final_state", "state": self.state}

        except Exception as e:
            logger.error(
                f"Error in process_message: {e}", props={"thread_id": thread_id}
            )
            logger.error(traceback.format_exc(), props={})

            # Check if this is a recursion limit error
            error_message = str(e)
            if (
                "Recursion limit" in error_message
                and "reached without hitting a stop condition" in error_message
            ):
                # Create a user-friendly message for the recursion limit error
                user_friendly_message = "\nMaximum tool call limit reached. Please try rephrasing your question to be more specific or ask a different question."

                # Send this as a delta event first so it appears in the streaming UI
                yield MessageDeltaEvent(
                    data=MessageDeltaData(
                        id=f"msg_{ai_message_id}",
                        object="thread.message.delta",
                        delta=MessageDeltaContent(
                            content=[
                                TextItem(
                                    index=0,
                                    type="text",
                                    text=TextContent(
                                        value=user_friendly_message,
                                        annotations=[],
                                    ),
                                )
                            ]
                        ),
                    ),
                ).model_dump()

                # Then send as a completed message
                yield MessageCompletedEvent(
                    event="thread.message.completed",
                    data=MessageCompletedData(
                        id=f"msg_{ai_message_id}",
                        role="assistant",
                        created_at=timestamp,
                        content=[
                            TextItem(
                                index=0,
                                type="text",
                                text=TextContent(
                                    value=user_friendly_message,
                                    annotations=[],
                                ),
                            )
                        ],
                        run_id=f"run_{run_id}",
                        assistant_id=f"asst_{assistant_id}",
                        thread_id=f"thread_{thread_id}",
                        attachments=[],
                        metadata={},
                        runSteps=[],
                        status="completed",
                    ),
                ).model_dump()
            else:
                # For other errors, yield the generic error event
                yield ErrorEvent(
                    event="error",
                    data={"error": error_message},
                ).model_dump()

    def _get_display_name(self, function_name: str) -> str:
        """
        Get a user-friendly display name for a tool function.

        Args:
            function_name: The original function name

        Returns:
            A formatted display name, either from the mapping or auto-formatted
        """
        # First check if we have a custom mapping
        if function_name in self.TOOL_DISPLAY_NAMES:
            return self.TOOL_DISPLAY_NAMES[function_name]

        # Otherwise, format the name nicely: replace underscores with spaces and capitalize words
        return " ".join(word.capitalize() for word in function_name.split("_"))

    def _format_knowledge_base_context(self, state: AgentState) -> Tuple[str, int, int]:
        """
        Format knowledge base information for the system prompt.

        Args:
            state: The current state

        Returns:
            Tuple containing:
            - Formatted knowledge base context string
            - Count of topics
            - Count of files
        """
        knowledge_context = ""

        # Get knowledge base topics and file details from state
        all_topics = []
        file_details = []

        if hasattr(state, "knowledge_base_topics"):
            all_topics = state.knowledge_base_topics

        if hasattr(state, "knowledge_base_files"):
            file_details = state.knowledge_base_files

        # Format knowledge base context
        if all_topics or file_details:
            knowledge_context = "\nKnowledge Base Information:\n"

            # Add topics section
            if all_topics:
                knowledge_context += "Your knowledge base contains information on the following topics:\n"
                # Add topics
                for topic in sorted(all_topics):  # Sort for consistent output
                    knowledge_context += f"- {topic}\n"
                knowledge_context += "\n"

            # Add file details section
            if file_details:
                knowledge_context += (
                    "Your knowledge base includes the following resources:\n\n"
                )

                for file in file_details:
                    # Skip files without required fields
                    if not file.get("type") or not file.get("title"):
                        continue

                    file_type = file.get("type")
                    file_title = file.get("title")

                    knowledge_context += f"- {file_title} ({file_type})\n"

                    # Add description if available
                    if file.get("description"):
                        knowledge_context += (
                            f"  Description: {file.get('description')}\n"
                        )

                    # Add domain for URLs
                    if file_type == "url" and file.get("domain"):
                        knowledge_context += f"  Domain: {file.get('domain')}\n"

                    # Add sub-URLs if available
                    if file_type == "url" and file.get("sub_urls"):
                        sub_urls = file.get("sub_urls")
                        if sub_urls and len(sub_urls) > 0:
                            knowledge_context += (
                                f"  Related pages from the same site:\n"
                            )

                            # Display up to 3 sub-URLs
                            displayed_count = 0
                            for sub_url in sub_urls:
                                if displayed_count >= 3:
                                    break

                                # Skip sub-URLs without title
                                if not sub_url.get("title"):
                                    continue

                                sub_title = sub_url.get("title")
                                knowledge_context += f"    - {sub_title}\n"
                                displayed_count += 1

                            # Show count of remaining sub-URLs
                            remaining = len(sub_urls) - displayed_count
                            if remaining > 0:
                                knowledge_context += (
                                    f"    - And {remaining} more pages...\n"
                                )

                    knowledge_context += "\n"

            # Add usage instructions
            knowledge_context += "When a user asks about any of these topics or resources, ALWAYS use the agent_knowledge_search tool to find relevant information before responding.\n"

        return knowledge_context, len(all_topics), len(file_details)
