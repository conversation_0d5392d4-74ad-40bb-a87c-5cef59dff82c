"""Tools for managing saved views in the Thena platform."""

import traceback
from typing import Any, Dict, List, Optional
from uuid import UUI<PERSON>

from fastapi import HTTPException
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import ToolException

from backend.core.config import get_config
from backend.core.logging_config import get_logger
from backend.core.platform_database import get_platform_supabase
from backend.services.database_operation_service import get_agent_studio_db_service, get_platform_db_service

logger = get_logger(__name__)
config = get_config()


async def create_view_impl(
    organization_id: UUID,
    name: str,
    ticket_ids: List[str],
    scope: str,
    team_id: Optional[str] = None,
    config: RunnableConfig = None,
) -> Dict[str, Any]:
    """Implementation of the create_view tool.
    
    Args:
        organization_id: The UUID of the organization
        name: Name for the saved view
        ticket_ids: List of ticket IDs to include in the view
        scope: Scope of the view, must be either "USER" or "TEAM"
        team_id: Optional ID of the team to associate with the view. If not provided, the team_id from config metadata will be used.
        config: RunnableConfig for additional configuration
        
    Returns:
        A dictionary containing success status, message, and view details
        
    Raises:
        ToolException: If an error occurs during view creation
    """
    try:
        # Extract metadata from config
        metadata = config.get("metadata", {}) if config else {}
        auth_token = metadata.get("auth_token")
        user_id = metadata.get("user_id")
        
        # Get organization_id from metadata if available
        x_org_id = metadata.get("thena_org_id")
        
        # Get team_id from metadata if not provided directly
        if not team_id and metadata.get("team_id"):
            team_id = metadata.get("team_id")
            logger.info(f"Using team_id from config metadata: {team_id}", props={})
        
        if not team_id:
            raise ToolException("Team ID is required for creating a view. Please provide a team_id or ensure it's available in the config metadata.")
        
        # Get thena_platform_user_id using the database service
        thena_platform_user_id = None
        if user_id:
            # Get the agent studio database service
            agent_studio_db_service = await get_agent_studio_db_service()
            
            # Get platform database service
            platform_db_service = await get_platform_db_service()
        
            # Get the Thena platform user ID
            thena_platform_user_id = await agent_studio_db_service.get_thena_platform_user_id(str(user_id))
        
        if not thena_platform_user_id:
            raise ToolException("Could not determine Thena platform user ID. Please ensure the user is properly authenticated.")
            
        # Convert x_org_id to numeric ID
        numeric_org_id = await platform_db_service.get_numeric_organization_id(str(x_org_id), auth_token)
        if not numeric_org_id:
            raise ToolException(f"Could not find organization id for {x_org_id}")
            
        # Convert team_id to numeric ID
        numeric_team_id = await platform_db_service.get_numeric_team_id(str(team_id), auth_token)
        if not numeric_team_id:
            raise ToolException(f"Could not find team id for {team_id}")
            
        # Convert user ID to numeric ID
        numeric_user_id = await platform_db_service.get_numeric_user_id(thena_platform_user_id, auth_token)
        if not numeric_user_id:
            raise ToolException(f"Could not find user id for {thena_platform_user_id}")
        
        # Validate scope
        if scope not in ["USER", "TEAM"]:
            raise ToolException("Scope must be either 'USER' or 'TEAM'")
        
        # Create simplified metadata with required structure
        metadata = {
            "name": name,
            "pinned": False,
            "ticketMap": {
                "ticket_ids": ticket_ids
            },
            "created_by_chat": True
        }
        
        # Prepare the data for insertion
        user_config_data = {
            "organization_id": numeric_org_id,
            "team_id": numeric_team_id,
            "scope": scope,
            "type": "TEAM_SAVED_VIEW_CONFIG",
            "metadata": metadata,
            "created_by_id": numeric_user_id
        }
        
        # Get the platform database client directly
        platform_db = await get_platform_supabase(auth_token=auth_token)
        
        # Insert record into user_config table
        result = await platform_db.from_("user_config").insert(user_config_data).execute()
        
        if not result.data:
            raise ToolException("Failed to create view in the database")
        
        return {
            "success": True,
            "message": f"Successfully created view '{name}' with {len(ticket_ids)} tickets",
            "view": {
                "id": result.data[0]["uid"],
                "name": name,
                "ticket_count": len(ticket_ids)
            }
        }
    
    except ToolException:
        # Re-raise tool exceptions directly
        raise
    except Exception as e:
        logger.exception("Error creating view", props={
            "organization_id": str(x_org_id),
            "name": name,
            "ticket_count": len(ticket_ids) if ticket_ids else 0,
            "scope": scope,
            "team_id": team_id
        })
        raise ToolException(f"Error creating view: {str(e)}") from e



async def update_view_impl(
    organization_id: UUID,
    original_name: str,
    updated_name: Optional[str] = None,
    updated_ticket_ids: Optional[List[str]] = None,
    updated_scope: Optional[str] = None,
    team_id: Optional[str] = None,
    config: RunnableConfig = None,
) -> Dict[str, Any]:
    """Implementation of the update_view tool.
    
    Args:
        organization_id: The UUID of the organization
        original_name: The original name of the view to update
        updated_name: Optional new name for the view
        updated_ticket_ids: Optional new list of ticket IDs to include in the view
        updated_scope: Optional new scope for the view (must be either "USER" or "TEAM")
        team_id: Optional ID of the team associated with the view. If not provided, the team_id from config metadata will be used.
        config: RunnableConfig for additional configuration
        
    Returns:
        A dictionary containing success status, message, and view details
        
    Raises:
        ToolException: If an error occurs during view update
    """
    try:
        # Extract metadata from config
        metadata = config.get("metadata", {}) if config else {}
        auth_token = metadata.get("auth_token")
        user_id = metadata.get("user_id")
        
        # Get organization_id from metadata if available
        x_org_id = metadata.get("thena_org_id")
        if not x_org_id and not organization_id:
            raise ToolException("Organization ID is required for updating a view.")
        elif not x_org_id:
            x_org_id = organization_id
        
        # Get team_id from metadata if not provided directly
        if not team_id and metadata.get("team_id"):
            team_id = metadata.get("team_id")
            logger.info(f"Using team_id from config metadata: {team_id}", props={})
        
        if not team_id:
            raise ToolException("Team ID is required for updating a view. Please provide a team_id or ensure it's available in the config metadata.")
        
        # Get thena_platform_user_id using the database service
        thena_platform_user_id = None
        if user_id:
            # Get the agent studio database service
            agent_studio_db_service = await get_agent_studio_db_service()
            
            # Get platform database service
            platform_db_service = await get_platform_db_service()
        
            # Get the Thena platform user ID
            thena_platform_user_id = await agent_studio_db_service.get_thena_platform_user_id(str(user_id))
        
        if not thena_platform_user_id:
            raise ToolException("Could not determine Thena platform user ID. Please ensure the user is properly authenticated.")
            
        # Convert x_org_id to numeric ID
        numeric_org_id = await platform_db_service.get_numeric_organization_id(str(x_org_id), auth_token)
        if not numeric_org_id:
            raise ToolException(f"Could not find organization id for {x_org_id}")
            
        # Convert team_id to numeric ID
        numeric_team_id = await platform_db_service.get_numeric_team_id(str(team_id), auth_token)
        if not numeric_team_id:
            raise ToolException(f"Could not find team id for {team_id}")
            
        # Convert user ID to numeric ID
        numeric_user_id = await platform_db_service.get_numeric_user_id(thena_platform_user_id, auth_token)
        if not numeric_user_id:
            raise ToolException(f"Could not find user id for {thena_platform_user_id}")
        
        # Validate updated_scope if provided
        if updated_scope and updated_scope not in ["USER", "TEAM"]:
            raise ToolException("Scope must be either 'USER' or 'TEAM'")
        
        # Get the platform database client directly
        platform_db = await get_platform_supabase(auth_token=auth_token)
        
        # Fetch existing views for the organization and team
        views_response = await platform_db.from_("user_config") \
            .select("id, uid, metadata, scope") \
            .eq("organization_id", numeric_org_id) \
            .eq("team_id", numeric_team_id) \
            .eq("type", "TEAM_SAVED_VIEW_CONFIG") \
            .execute()
        
        if not views_response.data:
            instructions = "No views found. You may want to create a new view first using the create_view tool."
            return {
                "success": False,
                "message": f"No views found for the organization and team",
                "instructions": instructions,
                "views": []
            }
        
        # Find the view with the original name
        target_view = None
        available_views = []
        
        for view in views_response.data:
            view_name = view.get("metadata", {}).get("name", "")
            available_views.append({
                "id": view.get("uid"),
                "name": view_name,
                "scope": view.get("scope")
            })
            
            if view_name == original_name:
                target_view = view
        
        # If no matching view is found, return available views for user to choose from
        if not target_view:
            instructions = "Please try again with one of the following view names, or check if you need to create a new view instead."
            return {
                "success": False,
                "message": f"Could not find a view with the name '{original_name}'",
                "instructions": instructions,
                "views": available_views
            }
        
        # Update the view with the provided values
        view_metadata = target_view.get("metadata", {})
        
        # Update name if provided
        if updated_name:
            view_metadata["name"] = updated_name
        
        # Update ticket IDs if provided
        if updated_ticket_ids:
            if "ticketMap" not in view_metadata:
                view_metadata["ticketMap"] = {}
                existing_ticket_ids = []
            else:
                existing_ticket_ids = view_metadata["ticketMap"].get("ticket_ids", [])
            
            # Combine existing and new ticket IDs, removing duplicates using a set
            combined_ticket_ids = list(set(existing_ticket_ids + updated_ticket_ids))
            view_metadata["ticketMap"]["ticket_ids"] = combined_ticket_ids
            
            # Update the message to indicate we're appending rather than replacing
            logger.info(f"Appended {len(updated_ticket_ids)} ticket IDs to view '{original_name}', "
                       f"total unique tickets: {len(combined_ticket_ids)}", props={})
        
        # Prepare update data
        update_data = {
            "metadata": view_metadata
        }
        
        # Update scope if provided
        if updated_scope:
            update_data["scope"] = updated_scope
        
        # Update the record in the database
        result = await platform_db.from_("user_config") \
            .update(update_data) \
            .eq("id", target_view.get("id")) \
            .execute()
        
        if not result.data:
            raise ToolException("Failed to update view in the database")
        
        # Prepare response message
        update_details = []
        if updated_name:
            update_details.append(f"name to '{updated_name}'")
        if updated_ticket_ids:
            # Calculate how many new unique tickets were added
            original_ticket_count = len(target_view.get("metadata", {}).get("ticketMap", {}).get("ticket_ids", []))
            updated_ticket_count = len(view_metadata.get("ticketMap", {}).get("ticket_ids", []))
            new_unique_count = updated_ticket_count - original_ticket_count
            
            if new_unique_count > 0:
                update_details.append(f"added {new_unique_count} new unique tickets (total: {updated_ticket_count})")
            else:
                update_details.append(f"no new unique tickets added (total: {updated_ticket_count})")
        if updated_scope:
            update_details.append(f"scope to '{updated_scope}'")
        
        update_message = ", ".join(update_details)
        
        # Prepare the response with ticket details if tickets were updated
        response = {
            "success": True,
            "message": f"Successfully updated view '{original_name}' ({update_message})",
            "view": {
                "id": target_view.get("uid"),
                "name": updated_name or original_name,
                "ticket_count": len(view_metadata.get("ticketMap", {}).get("ticket_ids", [])),
                "scope": updated_scope or target_view.get("scope")
            }
        }
        
        # If ticket IDs were updated, include details about which ones were added
        if updated_ticket_ids:
            # Get the original ticket IDs
            original_ticket_ids = target_view.get("metadata", {}).get("ticketMap", {}).get("ticket_ids", [])
            # Find which tickets were newly added (not in the original list)
            newly_added_tickets = [ticket_id for ticket_id in updated_ticket_ids if ticket_id not in original_ticket_ids]
            
            # Add ticket details to the response
            response["ticket_details"] = {
                "added_ticket_ids": newly_added_tickets,
                "added_count": len(newly_added_tickets),
                "total_count": len(view_metadata.get("ticketMap", {}).get("ticket_ids", []))
            }
            
        return response
    
    except ToolException:
        # Re-raise tool exceptions directly
        raise
    except Exception as e:
        logger.exception("Error updating view", props={
            "organization_id": str(x_org_id),
            "original_name": original_name,
            "updated_name": updated_name,
            "team_id": team_id
        })
        raise ToolException(f"Error updating view: {str(e)}") from e
