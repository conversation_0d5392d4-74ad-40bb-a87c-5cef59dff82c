import asyncio
import hashlib
import hmac
import json
import time
import uuid
from http.cookies import <PERSON><PERSON><PERSON><PERSON>
from typing import Any, Optional

import httpx
from backend.core.config import get_config
from backend.core.database import get_supabase
from backend.core.logging_config import get_logger
from backend.core.redis_manager import RedisConnectionManager
from backend.models.chat_socketio import (
    BroadcastResult,
    ClientIdentificationRequest,
    CommentCreateResult,
    CommentErrorResponse,
    CommentHandlingResult,
    CommentMessageResponse,
    CommentRequest,
    ConnectionMetadata,
    ConnectionResponse,
    GenericMessage,
    IdentificationResponse,
    TicketCreationRequest,
    TicketCreationResult,
)
from backend.models.webhook_handler import TicketCreatedBroadcast
from backend.services.non_plat_auth_service import (
    NonPlatAuthService,
    get_non_plat_auth_service,
)

# Note: Import of sio from backend.routes.chat_websocket is done inside methods to avoid circular imports

logger = get_logger("chat_websocket_service")
config = get_config()


class ChatWebSocketService:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.initialized = False
        return cls._instance

    async def initialize(self) -> None:
        """Initialize the service with necessary connections"""
        if not self.initialized:
            logger.info("Initializing ChatWebSocketService")

            # Initialize Redis manager for connection tracking
            self.redis_manager = RedisConnectionManager()

            # Initialize service - rely entirely on Redis for storage
            self.initialized = True
            logger.info("ChatWebSocketService initialized successfully")

    async def handle_connect(
        self,
        sid: str,
        environ: dict[str, Any],
        auth: dict[str, Any],
    ) -> ConnectionResponse:
        """
        Handle Socket.IO connection, aligning authentication with HTTP flow.
        Verifies API Key, performs HMAC check if applicable, gets/creates user
        using NonPlatAuthService, and stores connection details.

        Args:
            sid: Socket.IO session ID
            environ: WSGI environment
            auth: Authentication data (expected: x-anon-key, userEmail, userName, userHash)

        Returns:
            ConnectionResponse: Connection info response model, potentially including a new cookie.
        """
        logger.info(
            f"Socket.IO connection request received: {sid}",
            props={"sid": sid, "auth": auth or {}, "has_environ": bool(environ)},
        )

        # --- Initialize variables --- #
        auth_success = False
        auth_error = None
        user_id_str: Optional[str] = None
        org_id: Optional[uuid.UUID] = None
        agent_id: Optional[uuid.UUID] = None
        hmac_secret_key: Optional[str] = None
        user_data: Optional[dict[str, Any]] = None
        new_cookie: Optional[str] = None
        client_id = str(uuid.uuid4())  # Generate unique client ID

        # --- Extract data --- #
        # 1. From auth object (sent by client)
        anon_key = auth.get("x-anon-key")
        provided_email = auth.get("x-user-email")
        provided_name = auth.get("x-user-name")
        provided_hash = auth.get("x-user-hash")

        # 2. From environ (cookie, IP)
        raw_cookie_header = environ.get("HTTP_COOKIE", "")
        cookie_parser = SimpleCookie()
        cookie_parser.load(raw_cookie_header)
        thena_session_cookie_obj = cookie_parser.get("thena_session_id")
        session_cookie = (
            thena_session_cookie_obj.value if thena_session_cookie_obj else None
        )
        client_ip = self._extract_client_ip(environ)

        logger.info(
            f"Extracted connection details for auth: {sid}",
            props={
                "sid": sid,
                "api_key_present": bool(anon_key),
                "email_provided": bool(provided_email),
                "name_provided": bool(provided_name),
                "hash_provided": bool(provided_hash),
                "session_cookie_present": bool(session_cookie),
                "client_ip": client_ip,
            },
        )

        # --- Authentication Logic --- #
        try:
            if not anon_key:
                raise ValueError("Missing API key (x-anon-key)")

            # 3. Verify API Key (get deployment info)
            non_plat_auth_svc: NonPlatAuthService = await get_non_plat_auth_service()
            deployment_data = await non_plat_auth_svc.get_deployment_by_key(anon_key)

            if not deployment_data:
                raise ValueError("Invalid API key")

            agent_id = uuid.UUID(deployment_data["agent_id"])
            org_id = uuid.UUID(deployment_data["org_id"])
            hmac_secret_key = deployment_data.get("hmac_secret_key")
            logger.info(
                f"API Key verified for {sid}: org={org_id}, agent={agent_id}, hmac_enabled={hmac_secret_key is not None}",
                props={"sid": sid, "org_id": str(org_id)},
            )

            # 4. HMAC Check (if applicable)
            hmac_verified_successfully = False
            is_hmac_attempt = bool(
                provided_hash and provided_email and provided_name and hmac_secret_key,
            )
            if is_hmac_attempt:
                logger.info(
                    f"Attempting HMAC verification for {sid}",
                    props={"sid": sid},
                )
                message_payload = f"{provided_email}:{provided_name}"
                message_bytes = message_payload.encode("utf-8")
                secret_bytes = hmac_secret_key.encode("utf-8")
                calculated_hash = hmac.new(
                    secret_bytes,
                    message_bytes,
                    hashlib.sha256,
                ).hexdigest()

                if hmac.compare_digest(calculated_hash, provided_hash):
                    logger.info(
                        f"HMAC verification successful for {sid}",
                        props={"sid": sid},
                    )
                    hmac_verified_successfully = True
                else:
                    logger.warning(
                        f"HMAC verification failed for {sid}. Hash mismatch.",
                        props={"sid": sid},
                    )
                    raise ValueError(
                        "HMAC verification failed",
                    )  # Fail connection if HMAC provided but invalid

            # 5. Get or Create Non-Platform User (using the service)
            # Pass extracted details directly, request=None as we extracted needed info
            user_data, new_cookie = await non_plat_auth_svc.get_or_create_user(
                org_id=org_id,
                cookie=session_cookie,
                ip_address=client_ip,
                provided_email=provided_email,
                provided_name=provided_name,
                request=None,  # Explicitly pass None
            )

            if not user_data or not user_data.get("id"):
                # Should not happen if service works correctly, but defensively check
                raise ValueError("Failed to get or create user session")

            user_id_str = str(user_data["id"])
            # Update session_cookie if a new one was generated
            if new_cookie:
                session_cookie = new_cookie
                logger.info(
                    f"New session cookie generated by get_or_create_user for {sid}",
                    props={"sid": sid},
                )

            # 6. Mark as verified if HMAC was successful
            if hmac_verified_successfully:
                mark_success = await non_plat_auth_svc.mark_user_as_verified(
                    user_id=uuid.UUID(user_id_str),
                    verified_email=provided_email,
                    verified_name=provided_name,
                )
                if mark_success:
                    user_data["verified"] = True  # Update local dict for response
                    logger.info(
                        f"Successfully marked user {user_id_str} as verified for {sid}",
                        props={"sid": sid},
                    )
                else:
                    logger.error(
                        f"Failed to mark user {user_id_str} as verified in DB after HMAC success for {sid}",
                        props={"sid": sid},
                    )
                    # Decide if this is a fatal error. For now, let connection succeed but log error.
                    # auth_error = "Failed to finalize verification" # Optionally set error

            # --- Demotion Check: If user WAS verified but current connect attempt is NOT HMAC verified --- #
            elif not hmac_verified_successfully and user_data.get("verified"):
                # This case handles when a previously verified user connects again without HMAC
                logger.warning(
                    f"User {user_id_str} is marked verified in DB, but current WS connect is not HMAC verified. Unverifying.",
                    props={
                        "sid": sid,
                        "user_id": user_id_str,
                        "org_id": str(org_id),
                        "hmac_attempted_in_this_request": is_hmac_attempt,
                        "hmac_successful_in_this_request": hmac_verified_successfully,
                    },
                )
                unverify_success = await non_plat_auth_svc.unverify_user_session(
                    uuid.UUID(user_id_str),
                )
                if unverify_success:
                    # Update local user_data to reflect the change for the current response
                    user_data["verified"] = False
                    user_data.pop("verified_user_email", None)
                    user_data.pop("verified_user_name", None)
                else:
                    logger.error(
                        f"Failed to unverify user {user_id_str} in database during WS connect demotion.",
                    )
                    # Proceed with potentially stale 'verified' status, but log error
            # --- End WS Demotion Check --- #

            # If we reached here without fatal errors, authentication is successful
            auth_success = True

        except Exception as e:
            auth_error = f"Authentication error: {str(e)}"
            logger.error(
                f"Auth failed for {sid}: {auth_error}",
                exc_info=True,
                props={"sid": sid},
            )
            auth_success = False  # Ensure auth is false on error
            # Clear sensitive details on auth failure before storing connection?
            user_id_str = None
            org_id = None
            agent_id = None
            anon_key = None  # Clear anon_key if auth failed
            user_data = None
            new_cookie = None
            hmac_verified_successfully = False

        # --- Store Connection State --- #
        # Extract thread/ticket IDs from query params (as before)
        query = environ.get("QUERY_STRING", "")
        query_params = dict(
            param.split("=", 1) for param in query.split("&") if "=" in param
        )
        thread_id = query_params.get("thread_id")
        ticket_id = query_params.get("ticket_id")

        await self.store_socket_connection(
            sid=sid,
            client_id=client_id,
            thread_id=thread_id,
            ticket_id=ticket_id,
            user_id=user_id_str,  # Store the user ID string
            organization_id=str(org_id) if org_id else None,
            api_key=anon_key,  # Store the key used for connection
        )

        # --- Log and Respond --- #
        logger.info(
            f"Socket.IO client connection processed: {sid}",
            props={
                "sid": sid,
                "client_id": client_id,
                "api_key_prefix": anon_key[:10] if anon_key else None,
                "ticket_id": ticket_id,
                "thread_id": thread_id,
                "authenticated": auth_success,
                "user_id": user_id_str,
                "org_id": str(org_id) if org_id else None,
                "auth_error": auth_error,
                "hmac_attempted": (
                    is_hmac_attempt if "is_hmac_attempt" in locals() else False
                ),
                "hmac_successful": (
                    hmac_verified_successfully
                    if "hmac_verified_successfully" in locals()
                    else False
                ),
                "new_cookie_generated": new_cookie is not None,
            },
        )

        # Create and return connection response model, including the new cookie if generated
        response = ConnectionResponse(
            sid=sid,
            client_id=client_id,
            ticket_id=ticket_id if ticket_id else None,
            thread_id=thread_id if thread_id else None,
            authenticated=auth_success,
            auth_error=auth_error,
            user_id=user_id_str,
            new_cookie=new_cookie,  # Pass the new cookie back to the client
        )
        return response

    def _extract_client_ip(self, environ: dict[str, Any]) -> Optional[str]:
        """Helper to extract client IP from WSGI environ, handling common headers."""
        client_ip = environ.get("REMOTE_ADDR") or environ.get("REMOTE_HOST")
        if not client_ip and "headers" in environ:  # Fallback for Uvicorn/Hypercorn
            try:
                headers_dict = {
                    k.decode("utf-8").lower(): v.decode("utf-8")
                    for k, v in environ.get("headers", [])
                }
                forwarded_for = headers_dict.get("x-forwarded-for", "")
                if forwarded_for:
                    client_ip = forwarded_for.split(",")[0].strip()
                else:
                    # Standard Uvicorn client tuple (host, port)
                    client_tuple = environ.get("client")
                    if (
                        isinstance(client_tuple, (list, tuple))
                        and len(client_tuple) > 0
                    ):
                        client_ip = client_tuple[0]
            except Exception as e:
                logger.warning(f"Error decoding headers to find IP: {e}", props={})
                # Fallback to basic client info if headers processing fails
                client_tuple = environ.get("client")
                if isinstance(client_tuple, (list, tuple)) and len(client_tuple) > 0:
                    client_ip = client_tuple[0]
        return client_ip

    async def handle_disconnect(self, sid: str) -> None:
        """
        Handle Socket.IO disconnection.

        Args:
            sid: Socket.IO session ID
        """
        # Get client ID from Redis
        client_id = None
        thread_id = None
        ticket_id = None

        try:
            # Use redis_manager's context manager
            async with self.redis_manager.connection(
                "socketio_disconnect",
            ) as redis_connection:
                # Get client ID from sid mapping
                client_id = await redis_connection.get(f"sio:sid:{sid}")
                if client_id and isinstance(client_id, bytes):
                    client_id = client_id.decode("utf-8")

                if client_id:
                    # Get connection info
                    conn_data = await redis_connection.get(
                        f"sio:connection:{client_id}",
                    )
                    if conn_data:
                        if isinstance(conn_data, bytes):
                            conn_data = conn_data.decode("utf-8")
                        conn_info = json.loads(conn_data)
                        thread_id = conn_info.get("thread_id")
                        ticket_id = conn_info.get("ticket_id")
        except Exception as e:
            logger.error(
                f"Error getting connection info for disconnection: {str(e)}",
                props={"sid": sid, "error": str(e)},
            )

        # Remove connection from Redis
        if client_id:
            await self.remove_socket_connection(
                sid=sid,
                client_id=client_id,
                thread_id=thread_id,
                ticket_id=ticket_id,
            )

        logger.info(
            f"Socket.IO client disconnected: {sid}",
            props={
                "sid": sid,
                "client_id": client_id,
                "ticket_id": ticket_id,
                "thread_id": thread_id,
            },
        )

    async def handle_identify(
        self,
        sid: str,
        data: dict[str, Any],
    ) -> IdentificationResponse:
        """
        Handle client identification.

        Args:
            sid: Socket.IO session ID
            data: Identification data

        Returns:
            IdentificationResponse: Identification response model
        """
        # Parse the identification request
        identification_request = ClientIdentificationRequest(**data)

        # Get client info from Redis
        client_id = None
        previous_thread_id = None
        previous_ticket_id = None
        org_id = None
        api_key = None
        user_id = None  # Initialize user_id
        existing_initial_widget_comment_id = None  # Initialize

        try:
            # Use redis_manager's context manager
            async with self.redis_manager.connection(
                "socketio_identify",
            ) as redis_connection:
                # Get client ID from sid mapping
                client_id = await redis_connection.get(f"sio:sid:{sid}")
                if client_id and isinstance(client_id, bytes):
                    client_id = client_id.decode("utf-8")

                if client_id:
                    # Get connection info
                    conn_data_raw = await redis_connection.get(
                        f"sio:connection:{client_id}",
                    )
                    if conn_data_raw:
                        if isinstance(conn_data_raw, bytes):
                            conn_data_raw = conn_data_raw.decode("utf-8")
                        conn_info_dict = json.loads(conn_data_raw)
                        # Reconstruct to access fields easily, including initial_widget_comment_id
                        existing_conn_metadata = ConnectionMetadata(**conn_info_dict)
                        previous_thread_id = existing_conn_metadata.thread_id
                        previous_ticket_id = existing_conn_metadata.ticket_id
                        org_id = existing_conn_metadata.organization_id
                        api_key = existing_conn_metadata.api_key
                        user_id = existing_conn_metadata.user_id
                        existing_initial_widget_comment_id = (
                            existing_conn_metadata.initial_widget_comment_id
                        )
        except Exception as e:
            logger.error(
                f"Error getting connection info for identification: {str(e)}",
                props={"sid": sid, "error": str(e)},
            )

        # Extract data from the request
        thread_id = identification_request.thread_id or previous_thread_id

        # Important: Get ticket_id from the identify data, but fallback to existing client_info
        # This ensures we preserve ticket mapping even if the frontend doesn't send it during reconnection
        ticket_id = identification_request.ticket_id

        # If no ticket_id in the identify data but we had one before, preserve it
        if not ticket_id and previous_ticket_id:
            ticket_id = previous_ticket_id
            logger.info(
                f"Preserving previous ticket_id: {ticket_id} for client {sid} during identify",
                props={"sid": sid, "client_id": client_id, "thread_id": thread_id},
            )

        # Determine the parent comment ID to store.
        # Prioritize the one from the current identify request (frontend might have a fresher one on full reload).
        # Fallback to the one already stored in Redis if the client doesn't send one.
        parent_comment_id_to_store = (
            identification_request.parent_comment_id
            or existing_initial_widget_comment_id
        )

        # Update Redis tracking info
        if client_id:
            # Re-store connection with updated information
            await self.store_socket_connection(
                sid=sid,
                client_id=client_id,
                thread_id=thread_id,
                ticket_id=ticket_id,
                user_id=user_id,  # Pass user_id
                organization_id=org_id,  # Pass org_id
                api_key=api_key,  # Pass api_key
                passed_initial_widget_comment_id=parent_comment_id_to_store,  # Use the determined ID
            )

        logger.info(
            f"Socket.IO client identified: {sid}",
            props={
                "sid": sid,
                "client_id": client_id,
                "ticket_id": ticket_id,
                "thread_id": thread_id,
                "user_id": user_id,
                "preserved_ticket_id": ticket_id == previous_ticket_id
                and previous_ticket_id is not None,
            },
        )

        # Create and return identification response model
        return IdentificationResponse(
            sid=sid,
            client_id=client_id,
            ticket_id=ticket_id,
            thread_id=thread_id,
        )

    async def create_ticket_comment(
        self,
        ticket_id: str,
        content: str,
        api_key: str,
        org_id: str,
        parent_comment_id: Optional[str] = None,
        impersonated_user_name: Optional[str] = None,
        impersonated_user_avatar: Optional[str] = None,
    ) -> CommentCreateResult:
        """
        Create a comment on a ticket via the Thena Platform API.

        Args:
            ticket_id: The ID of the ticket to comment on
            content: The comment content
            api_key: The API key for Thena Platform
            org_id: The organization ID for Thena Platform
            parent_comment_id: Optional ID of the parent comment for threading
            impersonated_user_name: Optional name for impersonating the user posting the comment
            impersonated_user_avatar: Optional avatar URL for impersonating the user

        Returns:
            CommentCreateResult: Result containing success status and comment data or error
        """
        start_time = time.time()
        logger.info(
            f"Creating comment on ticket #{ticket_id}",
            props={
                "ticket_id": ticket_id,
                "parent_comment_id": parent_comment_id,
                "impersonated_user_name": impersonated_user_name,
                "impersonated_user_avatar_present": impersonated_user_avatar
                is not None,
            },
        )

        try:
            # Prepare the request payload
            payload = {
                "content": content,
                "parentCommentId": (
                    parent_comment_id if parent_comment_id else ""
                ),  # Use provided parent_comment_id
                "contentHtml": content,
            }
            #
            if impersonated_user_name:
                payload["impersonatedUserName"] = impersonated_user_name
                # NOTE:
                # Sending "Null" as a string for the avatar.
                # This is a convention to instruct the Thena Platform API
                # to display the user's initials instead of a specific avatar image.
                payload["impersonatedUserAvatar"] = "Null"

            # Ensure metadata for ignoreSelf is present if any impersonation is happening
            # or if it's a general requirement for agent-originated comments via this path.
            # Current behavior of tools.py sets this for agent comments.
            # For user messages via widget, if impersonatedUserName is set, ignoreSelf might be relevant.
            if impersonated_user_name:  # Add metadata if user is being impersonated
                payload["metadata"] = {"ignoreSelf": True}  # Example, adjust if needed

            # Set up headers
            headers = {
                "x-api-key": api_key,
                "x-org-id": org_id,
                "Content-Type": "application/json",
            }

            # Log request details at debug level
            logger.debug(
                "Sending comment request to Thena Platform",
                props={
                    "ticket_id": ticket_id,
                    "url": f"{config.THENA_PLATFORM_URL}/v1/tickets/{ticket_id}/comment",
                    "content_length": len(content),
                    "org_id": org_id,
                },
            )

            # Make the API request
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{config.THENA_PLATFORM_URL}/v1/tickets/{ticket_id}/comment",
                    json=payload,
                    headers=headers,
                )

                # Log the response time
                elapsed = time.time() - start_time
                logger.info(
                    f"Comment creation request completed in {elapsed:.2f}s",
                    props={"ticket_id": ticket_id},
                )

                # Handle response
                if response.status_code == 200 or response.status_code == 201:
                    try:
                        comment_data = response.json()
                        logger.info(
                            f"Successfully created comment on ticket #{ticket_id}",
                            props={
                                "ticket_id": ticket_id,
                                "comment_id": comment_data.get("data", {}).get("id"),
                            },
                        )

                        # Create the result model with the complete response
                        return CommentCreateResult(**comment_data)
                    except json.JSONDecodeError:
                        logger.error(
                            f"Error parsing comment creation response for ticket #{ticket_id}",
                            props={
                                "ticket_id": ticket_id,
                                "status_code": response.status_code,
                            },
                        )
                        return CommentCreateResult(
                            status=False,
                            error="Invalid response format from platform API",
                        )
                else:
                    # Log the error response
                    try:
                        error_data = response.json()
                        error_message = error_data.get("message", "Unknown error")
                    except json.JSONDecodeError:
                        error_message = response.text or "Unknown error"

                    logger.error(
                        f"Failed to create comment on ticket #{ticket_id}: {error_message}",
                        props={
                            "ticket_id": ticket_id,
                            "status_code": response.status_code,
                            "error": error_message,
                        },
                    )

                    return CommentCreateResult(
                        status=False,
                        error=error_message,
                        status_code=response.status_code,
                    )
        except httpx.TimeoutException as e:
            logger.error(
                f"Timeout creating comment on ticket #{ticket_id}",
                props={"ticket_id": ticket_id, "error": str(e)},
            )
            return CommentCreateResult(status=False, error="Request timed out")
        except Exception as e:
            logger.error(
                f"Error creating comment on ticket #{ticket_id}: {str(e)}",
                props={"ticket_id": ticket_id, "error": str(e)},
            )
            return CommentCreateResult(status=False, error=str(e))

    async def store_socket_connection(
        self,
        sid: str,
        client_id: str,
        thread_id: Optional[str],
        ticket_id: Optional[str],
        user_id: Optional[str],
        organization_id: Optional[str],
        api_key: Optional[str],
        passed_initial_widget_comment_id: Optional[str] = None,
    ) -> bool:
        """
        Store Socket.IO connection information in Redis.

        Args:
            sid: Socket.IO session ID
            client_id: Unique client ID
            thread_id: Optional thread ID associated with this connection
            ticket_id: Optional ticket ID associated with this connection
            user_id: Optional user ID associated with this connection
            organization_id: Optional organization ID associated with this connection
            api_key: Optional API key associated with this connection
            passed_initial_widget_comment_id: Optional ID of the first widget comment in the session

        Returns:
            bool: Success status
        """
        try:
            # Use redis_manager's context manager
            async with self.redis_manager.connection(
                "socketio_store",
            ) as redis_connection:
                logger.info(
                    f"Storing Socket.IO connection in Redis for client_id: {client_id}",
                    props={
                        "client_id": client_id,
                        "sid": sid,
                        "user_id": user_id,
                        "org_id": organization_id,
                        "initial_widget_comment_id": passed_initial_widget_comment_id,
                    },
                )

                # Create connection metadata model
                connection_meta = ConnectionMetadata(
                    client_id=client_id,
                    sid=sid,
                    user_id=user_id,
                    organization_id=organization_id,
                    api_key=api_key,
                    thread_id=thread_id,
                    ticket_id=ticket_id,
                    initial_widget_comment_id=passed_initial_widget_comment_id,
                    connected_at=time.time(),
                    instance_id=config.INSTANCE_ID,
                )

                # Set the connection info with TTL
                await redis_connection.setex(
                    f"sio:connection:{client_id}",
                    3600,  # 1 hour TTL
                    json.dumps(connection_meta.model_dump()),
                )

                # Store SID to client_id mapping for faster lookups
                await redis_connection.setex(
                    f"sio:sid:{sid}",
                    3600,
                    client_id,  # 1 hour TTL
                )

                # Add to thread mapping if thread_id provided
                if thread_id:
                    await redis_connection.sadd(f"sio:thread:{thread_id}", client_id)
                    await redis_connection.expire(f"sio:thread:{thread_id}", 3600)

                return True

        except Exception as e:
            logger.error(
                f"Error storing Socket.IO connection in Redis: {str(e)}",
                props={"client_id": client_id},
            )
            return False

    async def store_ticket_thread_mapping(
        self,
        ticket_id: str,
        thread_id: str,
        impersonated_name: Optional[str] = None,
        impersonated_avatar: Optional[str] = None,  # Placeholder for future use
        chat_history_parent_comment_id: Optional[str] = None,
    ) -> bool:
        """
        Store a mapping between a ticket ID, its associated thread ID,
        optional impersonation details, and the parent comment ID of the chat history.

        Args:
            ticket_id: The ticket ID
            thread_id: The associated thread ID
            impersonated_name: Optional name for impersonation
            impersonated_avatar: Optional avatar URL for impersonation (future use)
            chat_history_parent_comment_id: Optional ID of the comment that contains the initial chat history

        Returns:
            bool: Success status
        """
        try:
            # Ensure both IDs are strings
            ticket_id_str = str(ticket_id)
            thread_id_str = str(thread_id)

            mapping_data = {
                "thread_id": thread_id_str,
                "impersonated_name": impersonated_name,
                "impersonated_avatar": impersonated_avatar,  # Store even if None
                "chat_history_parent_comment_id": chat_history_parent_comment_id,
            }
            mapping_json = json.dumps(mapping_data)

            # Use redis_manager's context manager
            async with self.redis_manager.connection(
                "ticket_thread_map",
            ) as redis_connection:
                logger.info(
                    f"Storing ticket-thread-impersonation-history_comment mapping: ticket={ticket_id_str}, data={mapping_json}",
                    props={
                        "ticket_id": ticket_id_str,
                        "thread_id": thread_id_str,
                        "impersonated_name": impersonated_name,
                        "impersonated_avatar": impersonated_avatar,
                        "chat_history_parent_comment_id": chat_history_parent_comment_id,
                    },
                )

                # Store mapping with 24 hour TTL
                await redis_connection.setex(
                    f"sio:ticket_thread_map:{ticket_id_str}",
                    86400,  # 24 hour TTL
                    mapping_json,
                )

                return True

        except Exception as e:
            logger.error(
                f"Error storing ticket-thread-impersonation mapping: {str(e)}",
                props={
                    "ticket_id": ticket_id,
                    "thread_id": thread_id,
                    "impersonated_name": impersonated_name,
                    "chat_history_parent_comment_id": chat_history_parent_comment_id,
                },
            )
            return False

    async def get_thread_id_for_ticket(self, ticket_id: str) -> Optional[str]:
        """
        Get the thread ID associated with a ticket ID from the stored mapping.

        Args:
            ticket_id: The ticket ID to look up

        Returns:
            Optional[str]: The associated thread ID if found
        """
        try:
            ticket_id_str = str(ticket_id)
            async with self.redis_manager.connection(
                "ticket_thread_lookup",
            ) as redis_connection:
                mapping_json = await redis_connection.get(
                    f"sio:ticket_thread_map:{ticket_id_str}",
                )
                if mapping_json:
                    if isinstance(mapping_json, bytes):
                        mapping_json = mapping_json.decode("utf-8")

                    mapping_data = json.loads(mapping_json)
                    thread_id = mapping_data.get("thread_id")

                    if thread_id:
                        logger.info(
                            f"Found thread ID {thread_id} for ticket {ticket_id_str} from mapping.",
                        )
                        return thread_id
                    else:
                        logger.warning(
                            f"'thread_id' not found in mapping for ticket {ticket_id_str}. Data: {mapping_json}",
                        )
                        return None
                else:
                    logger.info(
                        f"No ticket-thread mapping found for ticket {ticket_id_str}",
                    )
                    return None

        except json.JSONDecodeError as e:
            logger.exception(
                f"Error decoding JSON for ticket-thread mapping {ticket_id_str}: {str(e)}",
                props={
                    "ticket_id": ticket_id_str,
                    "error": str(e),
                    "raw_data": mapping_json if "mapping_json" in locals() else "N/A",
                },
            )
            return None
        except Exception as e:
            logger.exception(
                f"Error getting thread ID from ticket-thread mapping for ticket {ticket_id_str}: {str(e)}",
                props={"ticket_id": ticket_id_str, "error": str(e)},
            )
            return None

    async def get_ticket_impersonation_details(
        self,
        ticket_id: str,
    ) -> Optional[dict[str, Optional[str]]]:
        """
        Get the impersonation details (name, avatar) associated with a ticket ID.

        Args:
            ticket_id: The ticket ID to look up.

        Returns:
            Optional[Dict[str, Optional[str]]]: A dict with "name" and "avatar" if found, else None.
        """
        try:
            ticket_id_str = str(ticket_id)
            async with self.redis_manager.connection(
                "ticket_impersonation_lookup",
            ) as redis_connection:
                mapping_json = await redis_connection.get(
                    f"sio:ticket_thread_map:{ticket_id_str}",
                )
                if mapping_json:
                    if isinstance(mapping_json, bytes):
                        mapping_json = mapping_json.decode("utf-8")

                    mapping_data = json.loads(mapping_json)
                    impersonated_name = mapping_data.get("impersonated_name")
                    impersonated_avatar = mapping_data.get("impersonated_avatar")

                    if (
                        impersonated_name is not None
                    ):  # Name could be an empty string, but None means not set
                        logger.info(
                            f"Found impersonation details for ticket {ticket_id_str}: name='{impersonated_name}'",
                            props={
                                "ticket_id": ticket_id_str,
                                "impersonated_name": impersonated_name,
                                "impersonated_avatar": impersonated_avatar,
                            },
                        )
                        return {
                            "name": impersonated_name,
                            "avatar": impersonated_avatar,
                        }
                    else:
                        logger.info(
                            f"No impersonation name set in mapping for ticket {ticket_id_str}.",
                        )
                        return None
                else:
                    logger.info(
                        f"No ticket-thread mapping found for ticket {ticket_id_str} (for impersonation details).",
                    )
                    return None
        except json.JSONDecodeError as e:
            logger.exception(
                f"Error decoding JSON for ticket-thread mapping {ticket_id_str} (for impersonation): {str(e)}",
                props={
                    "ticket_id": ticket_id_str,
                    "error": str(e),
                    "raw_data": mapping_json if "mapping_json" in locals() else "N/A",
                },
            )
            return None
        except Exception as e:
            logger.exception(
                f"Error getting impersonation details for ticket {ticket_id_str}: {str(e)}",
                props={"ticket_id": ticket_id_str, "error": str(e)},
            )
            return None

    async def get_ticket_chat_history_parent_comment_id(
        self,
        ticket_id: str,
    ) -> Optional[str]:
        """
        Get the chat history parent comment ID associated with a ticket ID.

        Args:
            ticket_id: The ticket ID to look up.

        Returns:
            Optional[str]: The chat history parent comment ID if found, else None.
        """
        try:
            ticket_id_str = str(ticket_id)
            async with self.redis_manager.connection(
                "ticket_chat_history_parent_lookup",
            ) as redis_connection:
                mapping_json = await redis_connection.get(
                    f"sio:ticket_thread_map:{ticket_id_str}",
                )
                if mapping_json:
                    if isinstance(mapping_json, bytes):
                        mapping_json = mapping_json.decode("utf-8")

                    mapping_data = json.loads(mapping_json)
                    parent_comment_id = mapping_data.get(
                        "chat_history_parent_comment_id",
                    )

                    if parent_comment_id:
                        logger.info(
                            f"Found chat_history_parent_comment_id {parent_comment_id} for ticket {ticket_id_str}.",
                        )
                        return parent_comment_id
                    else:
                        logger.info(
                            f"No chat_history_parent_comment_id found in mapping for ticket {ticket_id_str}.",
                        )
                        return None
                else:
                    logger.info(
                        f"No ticket-thread mapping found for ticket {ticket_id_str} (for chat_history_parent_comment_id).",
                    )
                    return None
        except json.JSONDecodeError as e:
            logger.exception(
                f"Error decoding JSON for ticket-thread mapping {ticket_id_str} (for chat_history_parent_comment_id): {str(e)}",
                props={
                    "ticket_id": ticket_id_str,
                    "error": str(e),
                    "raw_data": mapping_json if "mapping_json" in locals() else "N/A",
                },
            )
            return None
        except Exception as e:
            logger.exception(
                f"Error getting chat_history_parent_comment_id for ticket {ticket_id_str}: {str(e)}",
                props={"ticket_id": ticket_id_str, "error": str(e)},
            )
            return None

    async def remove_socket_connection(
        self,
        sid: str,
        client_id: str,
        thread_id: Optional[str],
        ticket_id: Optional[str],
    ) -> bool:
        """
        Remove Socket.IO connection information from Redis.

        Args:
            sid: Socket.IO session ID
            client_id: Unique client ID
            thread_id: Optional thread ID associated with this connection
            ticket_id: Optional ticket ID associated with this connection

        Returns:
            bool: Success status
        """
        try:
            # Use redis_manager's context manager
            async with self.redis_manager.connection(
                "socketio_cleanup",
            ) as redis_connection:
                logger.info(
                    f"Removing Socket.IO connection from Redis for client_id: {client_id}",
                    props={
                        "client_id": client_id,
                        "sid": sid,
                        "thread_id": thread_id,
                        "ticket_id": ticket_id,
                    },
                )

                # Always remove connection info and SID mapping
                await redis_connection.delete(f"sio:connection:{client_id}")
                await redis_connection.delete(f"sio:sid:{sid}")

                # Handle thread membership and conditional ticket map cleanup
                if thread_id:
                    thread_key = f"sio:thread:{thread_id}"
                    # Remove client from the thread set
                    await redis_connection.srem(thread_key, client_id)
                    logger.debug(
                        f"Removed client {client_id} from thread set {thread_key}",
                        props={"sid": sid},
                    )

                    # Check if the thread is now empty
                    thread_size = await redis_connection.scard(thread_key)
                    logger.debug(
                        f"Thread set {thread_key} size after removal: {thread_size}",
                        props={"sid": sid},
                    )

                # If no thread_id, just log (shouldn't happen if ticket_id exists)
                elif ticket_id:
                    logger.warning(
                        f"Client {client_id} has ticket_id {ticket_id} but no thread_id during disconnect cleanup",
                        props={"sid": sid, "ticket_id": ticket_id},
                    )

                return True

        except Exception as e:
            logger.error(
                f"Error removing Socket.IO connection from Redis: {str(e)}",
                props={"client_id": client_id},
            )
            return False

    async def get_clients_for_broadcast(
        self,
        thread_id: Optional[str] = None,
        ticket_id: Optional[str] = None,
    ) -> list:
        """
        Get client IDs that should receive a broadcast.

        Args:
            thread_id: Optional thread ID to broadcast to
            ticket_id: Optional ticket ID to broadcast to

        Returns:
            List[str]: List of client IDs
        """
        try:
            # If ticket_id is provided but not thread_id, try to get thread_id from ticket
            if ticket_id and not thread_id:
                thread_id = await self.get_thread_id_for_ticket(ticket_id)

            # Use redis_manager's context manager
            async with self.redis_manager.connection(
                "socketio_broadcast",
            ) as redis_connection:
                clients = set()

                # Get clients for specific thread if provided
                if thread_id:
                    thread_clients = await redis_connection.smembers(
                        f"sio:thread:{thread_id}",
                    )
                    if thread_clients:
                        for client in thread_clients:
                            # Safely convert bytes to string if needed
                            if isinstance(client, bytes):
                                clients.add(client.decode("utf-8"))
                            else:
                                clients.add(client)

                # If no specific thread provided, get all active clients
                if not thread_id:
                    # Can't easily get all clients from Redis, so we'll get a sample of recent connections
                    # This is a limitation we can address later if needed
                    keys = await redis_connection.keys("sio:connection:*")
                    if keys:
                        for key in keys:
                            # Safely convert bytes to string if needed
                            if isinstance(key, bytes):
                                client_id = key.decode("utf-8").replace(
                                    "sio:connection:",
                                    "",
                                )
                            else:
                                client_id = key.replace("sio:connection:", "")
                            clients.add(client_id)

                return list(clients)

        except Exception as e:
            logger.error(f"Error getting clients for broadcast: {str(e)}")
            return []

    async def get_sid_from_client_id(self, client_id: str) -> Optional[str]:
        """
        Get Socket.IO session ID for a client ID.

        Args:
            client_id: The client ID to look up

        Returns:
            Optional[str]: The Socket.IO session ID if found
        """
        try:
            # Use redis_manager's context manager
            async with self.redis_manager.connection(
                "socketio_lookup",
            ) as redis_connection:
                # Get connection metadata
                conn_data = await redis_connection.get(f"sio:connection:{client_id}")
                if conn_data:
                    # Convert bytes to string if needed
                    if isinstance(conn_data, bytes):
                        conn_data = conn_data.decode("utf-8")
                    conn_info = json.loads(conn_data)
                    return conn_info.get("sid")
                return None

        except Exception as e:
            logger.error(
                f"Error getting SID for client ID: {str(e)}",
                props={"client_id": client_id},
            )
            return None

    async def _get_platform_credentials(
        self,
        anon_key: str,
    ) -> tuple[Optional[str], Optional[str]]:
        """
        Fetches the platform API key and x-org-id based on the agent's anon key.

        Args:
            anon_key: The agent_key from the deployments table (x-anon-key from widget).

        Returns:
            A tuple containing (platform_api_key, platform_x_org_id), or (None, None) if not found or on error.
        """
        platform_api_key: Optional[str] = None
        platform_x_org_id: Optional[str] = None
        agent_id_uuid: Optional[uuid.UUID] = None
        org_id_uuid: Optional[uuid.UUID] = None

        if not anon_key:
            logger.warning("Cannot fetch platform credentials without anon_key.")
            return None, None

        try:
            db = await get_supabase()  # Get Supabase client directly

            # Step 1: Query deployments table using the anon_key (agent_key)
            logger.debug(f"Querying deployment for anon_key prefix: {anon_key[:5]}...")
            deployment_response = (
                await db.table("deployments")
                .select("agent_id, org_id")
                .eq("agent_key", anon_key)
                .limit(1)
                .execute()
            )

            if deployment_response.data:
                # Check if the values are not None before casting
                agent_id_str = deployment_response.data[0].get("agent_id")
                org_id_str = deployment_response.data[0].get("org_id")
                if agent_id_str:
                    agent_id_uuid = uuid.UUID(agent_id_str)
                if org_id_str:
                    org_id_uuid = uuid.UUID(org_id_str)

                if agent_id_uuid and org_id_uuid:
                    logger.info(
                        f"Found deployment info: agent_id={agent_id_uuid}, org_id={org_id_uuid}",
                    )
                else:
                    logger.warning(
                        f"Deployment found for anon_key prefix {anon_key[:5]}, but agent_id or org_id is null.",
                    )
                    return None, None
            else:
                logger.warning(
                    f"No deployment found for anon_key prefix: {anon_key[:5]}",
                )
                return None, None  # Cannot proceed without deployment info

            # --- Concurrently fetch org and auth details ---
            org_task = None
            auth_task = None

            # Step 3: Query organizations table if org_id found
            if org_id_uuid:
                logger.debug(f"Querying organization for org_id: {org_id_uuid}...")
                # Use string casting for UUID in query
                # Ensure correct quoting for the column name "x-org-id"
                org_task = (
                    db.table("organizations")
                    .select('"x-org-id"')
                    .eq("id", str(org_id_uuid))
                    .limit(1)
                    .execute()
                )

            # Step 2: Query agent_auth table if agent_id found
            if agent_id_uuid:
                logger.debug(
                    f"Querying agent_auth for agent_id: {agent_id_uuid} and category 'thena platform'...",
                )
                # Use string casting for UUID in query
                auth_task = (
                    db.table("agent_auth")
                    .select("api_key")
                    .eq("agent_id", str(agent_id_uuid))
                    .eq("category", "thena platform")
                    .is_("deleted_at", None)
                    .limit(1)
                    .execute()
                )

            # Await concurrent tasks
            results = await asyncio.gather(org_task, auth_task, return_exceptions=True)

            # Process organization result
            if org_task:
                if isinstance(results[0], Exception):
                    logger.error(
                        f"Error fetching organization details for {org_id_uuid}: {results[0]}",
                        exc_info=results[0],
                    )
                elif results[0].data:  # Check if data list is not empty
                    platform_x_org_id = (
                        results[0].data[0].get("x-org-id")
                    )  # Adjust key access if quoting affects it
                    if platform_x_org_id:
                        logger.info(f"Found platform x-org-id: {platform_x_org_id}")
                    else:
                        logger.warning(
                            f"Column 'x-org-id' not found or is null for org_id: {org_id_uuid}",
                        )
                else:
                    logger.warning(f"No organization found for org_id: {org_id_uuid}")

            # Process agent_auth result
            if auth_task:
                if isinstance(results[1], Exception):
                    logger.error(
                        f"Error fetching agent_auth details for {agent_id_uuid}: {results[1]}",
                        exc_info=results[1],
                    )
                elif results[1].data:  # Check if data list is not empty
                    platform_api_key = (
                        results[1].data[0].get("api_key")
                    )  # Access the specific column
                    if platform_api_key:
                        logger.info(
                            f"Found platform api_key for agent_id {agent_id_uuid}",
                        )
                    else:
                        logger.warning(
                            f"Column 'api_key' not found or is null for agent_id: {agent_id_uuid}, category 'thena platform'",
                        )
                else:
                    logger.warning(
                        f"No 'thena platform' agent_auth found for agent_id: {agent_id_uuid}",
                    )

        except Exception as e:
            logger.error(
                f"Database error fetching platform credentials for anon_key prefix {anon_key[:5]}: {str(e)}",
                exc_info=True,
            )
            return (
                None,
                None,
            )  # Return None on any unexpected error during DB interaction

        # Final check: Only return if BOTH credentials were successfully found
        if not platform_api_key or not platform_x_org_id:
            logger.warning(
                f"Could not retrieve both required platform credentials (api_key or x-org-id missing) for anon_key prefix {anon_key[:5]}",
            )
            return None, None

        return platform_api_key, platform_x_org_id

    async def handle_comment(
        self,
        sid: str,
        data: dict[str, Any],
    ) -> CommentHandlingResult:
        """
        Handle a comment from a connected client.

        Args:
            sid: Socket.IO session ID
            data: Comment data

        Returns:
            CommentHandlingResult: Result of comment handling
        """
        # Start with empty result
        start_time = time.time()
        result = CommentHandlingResult(success=False)
        timestamp = time.time()

        try:
            # Parse the request
            try:
                comment_request = CommentRequest(**data)
            except Exception as e:
                logger.error(
                    f"Invalid comment request format: {str(e)}",
                    props={"data": data},
                )
                result.error_message = CommentErrorResponse(
                    message=f"Invalid request format: {str(e)}",
                    timestamp=timestamp,
                )
                return result

            # Get client info from Redis
            client_info = {}
            client_id = None
            thread_id = None  # Initialize here
            ticket_id = None  # Initialize here
            api_key = None  # Initialize api_key, already initialized but good to group
            # Initialize explicitly to ensure we have a value or None
            initial_widget_comment_id: Optional[str] = None

            try:
                # Use redis_manager's context manager
                async with self.redis_manager.connection(
                    "socketio_comment",
                ) as redis_connection:
                    # Get client ID from sid mapping
                    client_id = await redis_connection.get(f"sio:sid:{sid}")
                    if client_id and isinstance(client_id, bytes):
                        client_id = client_id.decode("utf-8")

                    if client_id:
                        # Get connection info
                        conn_data_raw = await redis_connection.get(
                            f"sio:connection:{client_id}",
                        )
                        if conn_data_raw:
                            if isinstance(conn_data_raw, bytes):
                                conn_data_raw = conn_data_raw.decode("utf-8")
                            client_info = json.loads(conn_data_raw)
                            client_info = ConnectionMetadata(**client_info)
                            thread_id = client_info.thread_id
                            ticket_id = client_info.ticket_id
                            api_key = client_info.api_key
                            initial_widget_comment_id = (
                                client_info.initial_widget_comment_id
                            )
            except Exception as e:
                logger.error(
                    f"Error getting client info for comment: {str(e)}",
                    props={"sid": sid, "error": str(e)},
                )

            if not client_info or not api_key:
                logger.error(f"No client info or api_key found in Redis for sid: {sid}")
                result.error_message = CommentErrorResponse(
                    message="Not authenticated or session invalid.",
                    timestamp=timestamp,
                )
                return result

            content = comment_request.content
            # Prioritize IDs from request, fallback to Redis data
            current_ticket_id = comment_request.ticket_id or ticket_id
            current_thread_id = comment_request.thread_id or thread_id

            # Check required fields from request/Redis
            if not content or not current_ticket_id:
                logger.error(
                    f"Missing required comment data: content or ticket_id for sid {sid}",
                )
                result.error_message = CommentErrorResponse(
                    message="Missing required comment data",
                    timestamp=timestamp,
                    thread_id=current_thread_id,
                    ticket_id=current_ticket_id,
                )
                return result

            # --- Fetch dynamic platform credentials ---
            platform_api_key, platform_x_org_id = await self._get_platform_credentials(
                api_key,
            )

            # Validate that we received the necessary platform credentials
            if not platform_api_key or not platform_x_org_id:
                logger.error(
                    f"Failed to retrieve platform credentials using api_key for comment processing: ticket #{current_ticket_id}, sid {sid}",
                    props={
                        "sid": sid,
                        "ticket_id": current_ticket_id,
                        "api_key_present": api_key is not None,
                    },
                )
                result.error_message = CommentErrorResponse(
                    message="Internal server error: Could not verify credentials for processing.",
                    timestamp=timestamp,
                    thread_id=current_thread_id,
                    ticket_id=current_ticket_id,
                )
                return result
            # --- End credential fetching ---

            logger.info(
                f"Processing comment for ticket #{current_ticket_id} using dynamic credentials",
                props={
                    "sid": sid,
                    "ticket_id": current_ticket_id,
                    "thread_id": current_thread_id,
                    "platform_org_id": platform_x_org_id,
                    "initial_widget_comment_id_from_connection": initial_widget_comment_id,
                },
            )

            # --- Determine the correct parent_comment_id ---
            parent_comment_for_platform = initial_widget_comment_id  # Default
            if current_ticket_id:
                chat_history_main_comment_id = (
                    await self.get_ticket_chat_history_parent_comment_id(
                        current_ticket_id,
                    )
                )
                if chat_history_main_comment_id:
                    parent_comment_for_platform = chat_history_main_comment_id
                    logger.info(
                        f"Using chat_history_parent_comment_id {chat_history_main_comment_id} for ticket {current_ticket_id} as parent.",
                        props={
                            "sid": sid,
                            "ticket_id": current_ticket_id,
                            "selected_parent_comment_id": parent_comment_for_platform,
                        },
                    )
                else:
                    logger.info(
                        f"No chat_history_parent_comment_id found for ticket {current_ticket_id}. Falling back to initial_widget_comment_id: {initial_widget_comment_id}",
                        props={
                            "sid": sid,
                            "ticket_id": current_ticket_id,
                            "fallback_parent_comment_id": initial_widget_comment_id,
                        },
                    )

            logger.info(  # Add this new log line
                f"Final parent_comment_id for platform: {parent_comment_for_platform} for ticket {current_ticket_id}",
                props={
                    "sid": sid,
                    "ticket_id": current_ticket_id,
                    "final_parent_id": parent_comment_for_platform,
                    "initial_id_available": initial_widget_comment_id is not None,
                    "history_id_retrieved": "chat_history_main_comment_id" in locals()
                    and chat_history_main_comment_id is not None,
                },
            )

            # --- Fetch impersonation details for the user based on ticket_id ---
            user_impersonation_name: Optional[str] = None
            user_impersonation_avatar: Optional[str] = None
            if current_ticket_id:
                impersonation_details = await self.get_ticket_impersonation_details(
                    current_ticket_id,
                )
                if impersonation_details:
                    user_impersonation_name = impersonation_details.get("name")
                    user_impersonation_avatar = impersonation_details.get("avatar")
                    logger.info(
                        f"Retrieved impersonation details for comment: name='{user_impersonation_name}' for ticket {current_ticket_id}",
                        props={
                            "sid": sid,
                            "ticket_id": current_ticket_id,
                            "user_impersonation_name": user_impersonation_name,
                            "user_impersonation_avatar_present": user_impersonation_avatar
                            is not None,
                        },
                    )
            # --- End impersonation detail fetching ---

            # Call create_ticket_comment with dynamic credentials, potential parent_comment_id,
            # and fetched user impersonation details.
            comment_result = await self.create_ticket_comment(
                ticket_id=current_ticket_id,
                content=content,
                api_key=platform_api_key,
                org_id=platform_x_org_id,
                parent_comment_id=parent_comment_for_platform,  # Use the determined parent
                impersonated_user_name=user_impersonation_name,
                impersonated_user_avatar=user_impersonation_avatar,
            )

            elapsed = time.time() - timestamp
            logger.info(
                f"Comment processing completed in {elapsed:.2f}s",
                props={"ticket_id": current_ticket_id},
            )

            if comment_result.success and comment_result.comment:
                # If this was the first comment in the widget session, try to atomically store its ID
                if not initial_widget_comment_id and client_id and client_info:
                    lock_key = f"sio:initial_comment_lock:{client_id}"
                    lock_acquired = False
                    try:
                        # Attempt to acquire a lock using SETNX to ensure only one process sets the initial ID
                        async with self.redis_manager.connection(
                            owner="initial_comment_lock",
                        ) as redis:
                            is_new_lock = await redis.setnx(lock_key, time.time())
                            if is_new_lock:
                                lock_acquired = True
                                # Set a short TTL on the lock key to prevent it from staying forever if something goes wrong
                                await redis.expire(
                                    lock_key,
                                    60,
                                )  # Lock expires after 60 seconds
                                logger.info(
                                    f"Acquired lock to set initial_widget_comment_id for client {client_id}",
                                )
                            else:
                                logger.info(
                                    f"Lock for initial_widget_comment_id for client {client_id} already held. Another process likely set it.",
                                )
                    except Exception as redis_err:
                        # If Redis fails during lock acquisition, proceed without the lock (fail-open for setting the ID)
                        # This might lead to the race condition if Redis is flaky, but prioritizes potentially setting the ID
                        logger.error(
                            f"Redis error checking/setting lock for initial_widget_comment_id for client {client_id}: {redis_err}. Proceeding without lock.",
                        )
                        lock_acquired = True  # Treat as acquired in case of error to allow update attempt

                    # Only proceed with the update if the lock was acquired (or Redis failed open)
                    if lock_acquired:
                        logger.info(
                            f"Storing initial_widget_comment_id: {comment_result.comment.id} for client {client_id}",
                            props={
                                "client_id": client_id,
                                "comment_id": comment_result.comment.id,
                            },
                        )
                        # Update the client_info object first (this is safe, it's local)
                        updated_client_info = client_info.model_copy(
                            update={
                                "initial_widget_comment_id": comment_result.comment.id,
                            },
                        )

                        # Store the updated client_info back to Redis
                        await self.store_socket_connection(
                            sid=sid,
                            client_id=client_id,
                            thread_id=updated_client_info.thread_id,
                            ticket_id=updated_client_info.ticket_id,
                            user_id=updated_client_info.user_id,
                            organization_id=updated_client_info.organization_id,
                            api_key=updated_client_info.api_key,
                            passed_initial_widget_comment_id=updated_client_info.initial_widget_comment_id,
                        )
                        # Note: We don't explicitly release the lock key here.
                        # Its TTL handles cleanup. If the update fails, the lock expires anyway.
                        # If the update succeeds, subsequent checks for `if not initial_widget_comment_id`
                        # in later comments will fail correctly based on the updated main connection data.

                # Create success message for sender (This happens regardless of the lock outcome for the initial ID)
                sender_message = CommentMessageResponse(
                    thread_id=current_thread_id,
                    ticket_id=current_ticket_id,
                    comment=comment_result.comment,
                    timestamp=timestamp,
                )

                # Get other clients in the same thread using Redis
                broadcast_sids = []
                if current_thread_id:
                    thread_clients = await self.get_clients_for_broadcast(
                        thread_id=current_thread_id,
                    )
                    for client_id in thread_clients:
                        other_sid = await self.get_sid_from_client_id(client_id)
                        if (
                            other_sid and other_sid != sid
                        ):  # Don't send to the original sender
                            broadcast_sids.append(other_sid)

                # Update the result with success info
                result.success = True
                result.sender_message = sender_message
                result.broadcast = len(broadcast_sids) > 0
                result.broadcast_message = (
                    sender_message  # Same message for all recipients in this case
                )
                result.broadcast_sids = broadcast_sids
                result.timestamp = timestamp

            else:
                # Create error message
                error_message = CommentErrorResponse(
                    message=f"Failed to add comment: {comment_result.error or 'Unknown error'}",
                    thread_id=current_thread_id,
                    ticket_id=current_ticket_id,
                    timestamp=timestamp,
                )

                # Update the result with error info
                result.success = False
                result.error_message = error_message
                result.timestamp = timestamp

        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(
                f"Error handling comment: {str(e)}",
                props={"elapsed_seconds": elapsed},
            )

            # Update the result with the exception error
            result.success = False
            result.error_message = CommentErrorResponse(
                message=f"Error processing comment: {str(e)}",
                timestamp=timestamp,
            )

        return result

    async def handle_ticket_created(
        self,
        sid: str,
        data: dict[str, Any],
    ) -> TicketCreationResult:
        """
        Handle ticket creation notification.

        Args:
            sid: Socket.IO session ID
            data: Ticket created data

        Returns:
            TicketCreationResult: Result containing response info
        """
        timestamp = time.time()

        # Initialize with a failure result
        result = TicketCreationResult(success=False, timestamp=timestamp)

        try:
            # Parse the ticket creation request
            ticket_request = TicketCreationRequest(**data)

            # Get client info from Redis
            client_info = {}
            client_id = None
            thread_id = None
            api_key = None  # Initialize api_key
            # Initialize explicitly to ensure we have a value or None
            initial_widget_comment_id: Optional[str] = None

            try:
                # Use redis_manager's context manager
                async with self.redis_manager.connection(
                    "socketio_ticket_created",
                ) as redis_connection:
                    # Get client ID from sid mapping
                    client_id = await redis_connection.get(f"sio:sid:{sid}")
                    if client_id and isinstance(client_id, bytes):
                        client_id = client_id.decode("utf-8")

                    if client_id:
                        # Get full connection info to preserve existing fields
                        conn_data_raw = await redis_connection.get(
                            f"sio:connection:{client_id}",
                        )
                        if conn_data_raw:
                            if isinstance(conn_data_raw, bytes):
                                conn_data_raw = conn_data_raw.decode("utf-8")
                            client_info = json.loads(conn_data_raw)
                            thread_id = client_info.get("thread_id")
                            api_key = client_info.get("api_key")
                            # Retrieve the existing initial comment ID
                            initial_widget_comment_id = client_info.get(
                                "initial_widget_comment_id",
                            )
            except Exception as e:
                logger.error(
                    f"Error getting client info for ticket created: {str(e)}",
                    props={"sid": sid, "error": str(e)},
                )

            # Get thread ID from client info or request (should usually match)
            thread_id = client_info.get("thread_id") or ticket_request.thread_id
            new_ticket_id = ticket_request.ticket_id
            ticket_status = ticket_request.status

            if not new_ticket_id or not thread_id:
                logger.error("Missing ticket_id or thread_id in ticket_created event")
                return result

            # Update client info in Redis if needed
            if client_id:
                # Update the connection info to include the new ticket ID,
                # preserving the initial_widget_comment_id
                try:
                    logger.info(
                        f"Updating connection {client_id} with ticket_id {new_ticket_id}, preserving initial_widget_comment_id: {initial_widget_comment_id}",
                        props={
                            "sid": sid,
                            "client_id": client_id,
                            "thread_id": thread_id,
                            "ticket_id": new_ticket_id,
                            "initial_widget_comment_id": initial_widget_comment_id,
                        },
                    )
                    await self.store_socket_connection(
                        sid=sid,
                        client_id=client_id,
                        thread_id=thread_id,
                        ticket_id=new_ticket_id,
                        user_id=client_info.get("user_id"),
                        organization_id=client_info.get("organization_id"),
                        api_key=api_key,
                        # Explicitly pass the retrieved initial comment ID
                        passed_initial_widget_comment_id=initial_widget_comment_id,
                    )

                    # Conditionally store ticket-thread mapping only if it doesn't exist
                    # (to avoid overwriting impersonation name set by the tool)
                    ticket_map_key = f"sio:ticket_thread_map:{new_ticket_id}"
                    async with self.redis_manager.connection(
                        "ticket_map_check",
                    ) as redis:
                        mapping_exists = await redis.exists(ticket_map_key)

                    if not mapping_exists:
                        logger.info(
                            f"Ticket-thread mapping for {new_ticket_id} does not exist. Creating basic mapping.",
                            props={"ticket_id": new_ticket_id, "thread_id": thread_id},
                        )
                        await self.store_ticket_thread_mapping(
                            new_ticket_id,
                            thread_id,
                            impersonated_name=None,
                        )
                    else:
                        logger.info(
                            f"Ticket-thread mapping for {new_ticket_id} already exists. Skipping basic mapping creation.",
                            props={"ticket_id": new_ticket_id, "thread_id": thread_id},
                        )

                except Exception as e:
                    logger.error(
                        f"Error updating client info with new ticket ID: {str(e)}",
                        props={
                            "sid": sid,
                            "thread_id": thread_id,
                            "ticket_id": new_ticket_id,
                            "error": str(e),
                        },
                    )

            logger.info(
                f"Thread {thread_id} is now linked to ticket #{new_ticket_id}",
                props={"sid": sid, "thread_id": thread_id, "ticket_id": new_ticket_id},
            )

            # Create notification message
            ticket_created_msg = TicketCreatedBroadcast(
                thread_id=thread_id,
                ticket_id=new_ticket_id,
                status=ticket_status,
                timestamp=timestamp,
                title="Ticket Created",  # Add a default title since we don't have it from the client
            )

            # Log the full message payload for debugging
            logger.info(
                f"Created ticket message with type: {ticket_created_msg.type}",
                props={"ticket_id": new_ticket_id, "thread_id": thread_id},
            )

            # Get other clients in the same thread using Redis
            broadcast_sids = []
            if thread_id:
                thread_clients = await self.get_clients_for_broadcast(
                    thread_id=thread_id,
                )
                for client_id in thread_clients:
                    other_sid = await self.get_sid_from_client_id(client_id)
                    if (
                        other_sid and other_sid != sid
                    ):  # Don't send to the original sender
                        broadcast_sids.append(other_sid)

            # Update the result with success info
            result.success = True
            result.message = ticket_created_msg
            result.broadcast = len(broadcast_sids) > 0
            result.broadcast_sids = broadcast_sids
            result.timestamp = timestamp

        except Exception as e:
            logger.error(f"Error handling ticket created event: {str(e)}")
            result.success = False
            result.error = str(e)
            result.timestamp = timestamp

        return result

    async def broadcast_to_all(
        self,
        event_type: str,
        data: dict[str, Any],
    ) -> BroadcastResult:
        """
        Broadcast a message to all connected Socket.IO clients.

        Args:
            event_type: The Socket.IO event type
            data: The data to broadcast

        Returns:
            BroadcastResult: Result containing success status and broadcast info
        """
        try:
            # Create a generic message with the provided data
            message = GenericMessage(type=event_type, data=data)

            # Add timestamp to the data if not present
            if "timestamp" not in data:
                data["timestamp"] = time.time()

            logger.info(
                f"Broadcasting {event_type} event to all clients",
                props={"data_keys": list(data.keys())},
            )

            # Import here to avoid circular import
            from backend.routes.chat_websocket import sio

            # Broadcast to all connected clients using Socket.IO
            await sio.emit(event_type, data)

            return BroadcastResult(
                success=True,
                message="Broadcast sent to all clients",
            )

        except Exception as e:
            logger.error(f"Error broadcasting to all clients: {str(e)}")
            return BroadcastResult(success=False, error=str(e))

    async def broadcast_to_thread(
        self,
        thread_id: str,
        event_type: str,
        data: dict[str, Any],
    ) -> BroadcastResult:
        """
        Broadcast a message to all clients in a specific thread.

        Args:
            thread_id: The thread ID to broadcast to
            event_type: The Socket.IO event type
            data: The data to broadcast

        Returns:
            BroadcastResult: Result containing success status and broadcast count
        """
        try:
            # Create a generic message with the provided data
            message = GenericMessage(type=event_type, data=data)

            # Add timestamp to the data if not present
            if "timestamp" not in data:
                data["timestamp"] = time.time()

            logger.info(
                f"Broadcasting {event_type} event to thread {thread_id}",
                props={"thread_id": thread_id, "data_keys": list(data.keys())},
            )

            # Import here to avoid circular import
            from backend.routes.chat_websocket import sio

            # Get all client IDs for this thread from Redis
            redis_client_ids = await self.get_clients_for_broadcast(thread_id=thread_id)

            # Get SIDs for each client ID
            all_sids = set()
            for client_id in redis_client_ids:
                sid = await self.get_sid_from_client_id(client_id)
                if sid:
                    all_sids.add(sid)

            # Broadcast to each client
            broadcast_count = 0
            for sid in all_sids:
                await sio.emit(event_type, data, room=sid)
                broadcast_count += 1

            logger.info(
                f"Broadcast complete: sent to {broadcast_count} clients",
                props={"thread_id": thread_id, "event_type": event_type},
            )

            return BroadcastResult(success=True, count=broadcast_count)

        except Exception as e:
            logger.error(f"Error broadcasting to thread {thread_id}: {str(e)}")
            return BroadcastResult(success=False, error=str(e))

    async def broadcast_platform_event(
        self,
        ticket_id: str,
        event_type: str,
        data: dict[str, Any],
    ) -> BroadcastResult:
        """
        Broadcast a platform event to all clients in the thread associated with a ticket.

        Args:
            ticket_id: The ticket ID to broadcast to
            event_type: The Socket.IO event type
            data: The data to broadcast

        Returns:
            BroadcastResult: Result containing success status and broadcast count
        """
        try:
            # Look up the thread ID for this ticket
            thread_id = await self.get_thread_id_for_ticket(ticket_id)
            if not thread_id:
                logger.warning(
                    f"No thread mapping found for ticket {ticket_id}, broadcasting will fail",
                    props={"ticket_id": ticket_id},
                )
                return BroadcastResult(
                    success=False,
                    error=f"No thread mapping found for ticket {ticket_id}",
                    ticket_id=ticket_id,
                )

            # Add ticket_id to the data for context
            data["ticket_id"] = ticket_id

            # Use the broadcast_to_thread method with our thread ID
            logger.info(
                f"Broadcasting platform event for ticket {ticket_id} to thread {thread_id}",
                props={
                    "ticket_id": ticket_id,
                    "thread_id": thread_id,
                    "event_type": event_type,
                },
            )

            # Delegate to the thread broadcasting method
            result = await self.broadcast_to_thread(thread_id, event_type, data)

            # Add ticket context to the result
            updated_result = BroadcastResult(**result.model_dump())
            updated_result.ticket_id = ticket_id
            return updated_result

        except Exception as e:
            logger.error(
                f"Error broadcasting platform event for ticket {ticket_id}: {str(e)}",
            )
            return BroadcastResult(success=False, error=str(e), ticket_id=ticket_id)


async def store_thread_for_ticket(
    ticket_id: str,
    thread_id: str,
    impersonated_name: Optional[str] = None,
    chat_history_parent_comment_id: Optional[str] = None,
) -> bool:
    """
    External helper function to store thread-ticket mapping (including impersonation name
    and the chat history's parent comment ID) from tools.
    This allows external services to associate threads with tickets, user names,
    and the primary comment ID for chat history.

    Args:
        ticket_id: The ticket ID
        thread_id: The associated thread ID
        impersonated_name: Optional user-provided name for the ticket context
        chat_history_parent_comment_id: Optional ID of the comment that contains the initial chat history

    Returns:
        bool: Success status
    """
    try:
        # Ensure both IDs are strings
        ticket_id_str = str(ticket_id)
        thread_id_str = str(thread_id)

        logger.info(
            f"Storing thread-ticket mapping: {ticket_id_str} → {thread_id_str}, impersonated_name: {impersonated_name}, chat_history_parent_comment_id: {chat_history_parent_comment_id}",
            props={
                "ticket_id": ticket_id_str,
                "thread_id": thread_id_str,
                "impersonated_name": impersonated_name,
                "chat_history_parent_comment_id": chat_history_parent_comment_id,
            },
        )

        service = await get_chat_websocket_service()
        # Pass impersonated_name and chat_history_parent_comment_id. Avatar will default to None.
        return await service.store_ticket_thread_mapping(
            ticket_id_str,
            thread_id_str,
            impersonated_name=impersonated_name,
            chat_history_parent_comment_id=chat_history_parent_comment_id,
        )
    except Exception as e:
        logger.error(
            f"Error storing thread-ticket mapping: {str(e)}",
            props={
                "ticket_id": ticket_id,
                "thread_id": thread_id,
                "impersonated_name": impersonated_name,
                "chat_history_parent_comment_id": chat_history_parent_comment_id,
            },
        )
        return False


# Singleton instance
_service_instance = None
_init_lock = asyncio.Lock()  # Thread safety lock


async def get_chat_websocket_service() -> ChatWebSocketService:
    """Get or create the Chat Web Socket Service singleton instance.

    Returns:
        ChatWebSocketService: The initialized singleton instance
    """
    global _service_instance
    if _service_instance is None:
        async with _init_lock:  # Thread safety
            if _service_instance is None:  # Double-check pattern
                logger.info("Creating new ChatWebSocketService instance")
                service = ChatWebSocketService()
                await service.initialize()
                _service_instance = service
                logger.info("ChatWebSocketService instance initialized")
    return _service_instance


async def cleanup_chat_websocket_service() -> None:
    """Clean up the Chat Web Socket Service resources."""
    global _service_instance
    if _service_instance is not None:
        logger.info("Cleaning up Chat Web Socket Service")
        _service_instance = None
