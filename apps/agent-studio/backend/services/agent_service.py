import asyncio
import time
import traceback
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

import httpx
from fastapi import HTTPException

from backend.core.config import get_config
from backend.core.database import get_supabase
from backend.core.logging_config import get_logger
from backend.repositories.agent_cache_repository import AgentCacheRepository

settings = get_config()
_agent_service_instances = {}
_init_lock = asyncio.Lock()

# Define logger at module level
logger = get_logger(__name__)


class AgentService:
    def __init__(self, organization_id: UUID):
        """Initialize the agent service.

        Args:
            organization_id: The organization ID to which the agents belong
        """
        self.organization_id = organization_id
        self.db = None
        self.cache_repository = AgentCacheRepository(organization_id=organization_id)
        logger.info(
            f"AgentService initialized for organization {organization_id}", props={}
        )

    async def initialize(self):
        """Initialize the database connection for this service instance."""
        try:
            self.db = await get_supabase()
            logger.info(
                f"Database connection initialized for organization {self.organization_id}",
                props={},
            )
        except Exception as e:
            logger.error(f"Failed to initialize database connection: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500, detail=f"Database initialization failed: {str(e)}"
            )

    async def _get_db(self):
        """Get the database client, initializing it if necessary."""
        if self.db is None:
            await self.initialize()
        return self.db

    async def cleanup(self):
        """Clean up resources used by this service instance."""
        try:
            self.db = None
            logger.info(
                f"Cleaned up AgentService for organization {self.organization_id}",
                props={},
            )
            return True
        except Exception as e:
            logger.error(
                f"Error cleaning up AgentService for organization {self.organization_id}: {e}",
                props={},
            )
            logger.error(traceback.format_exc(), props={})
            return False

    async def list_agents(self) -> List[Dict[str, Any]]:
        """List all agents for the organization.

        Returns:
            A list of agent objects with their associated files
        """
        start_time = time.time()
        try:
            # Try to get agents from cache first
            cached_agents = await self.cache_repository.get_agents_list()

            if cached_agents is not None:
                logger.info(
                    f"Cache HIT: Retrieved agents list from cache",
                    props={
                        "organization_id": str(self.organization_id),
                        "source": "cache",
                    },
                )
                # Filter out soft-deleted agents from cache
                return [agent for agent in cached_agents if not agent.get("deleted_at")]

            # If not in cache, get from database
            logger.debug(
                f"Cache miss for agents list, querying database",
                props={"organization_id": str(self.organization_id)},
            )

            db = await self._get_db()
            select_fields = "id, name, description, template_id, organization_id, configuration, metadata, avatar_url, status, created_at, updated_at, deleted_at, team_id, completed_flows_count, pending_flows_count, bot_sub"
            result = (
                await db.table("agents")
                .select(select_fields)
                .eq("organization_id", str(self.organization_id))
                .is_("deleted_at", None)
                .execute()
            )

            # Debug log to check the raw database response
            logger.debug(
                "Raw database response for agents",
                props={
                    "organization_id": str(self.organization_id),
                    "first_agent_sample": result.data[0] if result.data else None,
                    "flow_counts_sample": (
                        {
                            "completed": (
                                result.data[0].get("completed_flows_count")
                                if result.data
                                else None
                            ),
                            "pending": (
                                result.data[0].get("pending_flows_count")
                                if result.data
                                else None
                            ),
                        }
                        if result.data
                        else None
                    ),
                },
            )

            logger.info(
                f"Database HIT: Retrieved {len(result.data)} agents from database",
                props={
                    "organization_id": str(self.organization_id),
                    "count": len(result.data),
                    "source": "database",
                },
            )

            # Cache the full result
            await self.cache_repository.set_agents_list(result.data)

            return result.data
        except Exception as e:
            logger.error(f"Error listing agents: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500, detail=f"Failed to list agents: {str(e)}"
            )
        finally:
            duration = time.time() - start_time
            logger.info(
                f"list_agents completed in {duration:.2f}s for org {self.organization_id}",
                props={},
            )

    async def get_agent(self, agent_id: UUID) -> Optional[Dict[str, Any]]:
        """Get a single agent by ID.

        Args:
            agent_id: The ID of the agent to retrieve

        Returns:
            The agent object or None if not found
        """
        start_time = time.time()
        try:
            # Try to get agent from cache first
            cached_agent = await self.cache_repository.get_agent(agent_id)

            if cached_agent:
                logger.info(
                    f"Cache HIT: Retrieved agent {agent_id} from cache",
                    props={
                        "agent_id": str(agent_id),
                        "organization_id": str(self.organization_id),
                        "source": "cache",
                    },
                )
                # Don't return soft-deleted agents
                if cached_agent.get("deleted_at"):
                    logger.info(
                        f"Agent {agent_id} is soft-deleted, returning None",
                        props={
                            "agent_id": str(agent_id),
                            "organization_id": str(self.organization_id),
                        },
                    )
                    return None
                return cached_agent

            # If not in cache, get from database
            logger.info(
                f"Cache miss for agent {agent_id}, querying database",
                props={
                    "agent_id": str(agent_id),
                    "organization_id": str(self.organization_id),
                },
            )

            db = await self._get_db()
            select_fields = "id, name, description, template_id, organization_id, configuration, metadata, avatar_url, status, created_at, updated_at, deleted_at, team_id, completed_flows_count, pending_flows_count, bot_sub"
            result = (
                await db.table("agents")
                .select(select_fields)
                .eq("id", str(agent_id))
                .eq("organization_id", str(self.organization_id))
                .is_("deleted_at", "null")
                .execute()
            )

            if not result.data:
                logger.info(
                    f"Agent {agent_id} not found or is deleted",
                    props={
                        "agent_id": str(agent_id),
                        "organization_id": str(self.organization_id),
                    },
                )
                return None

            # Cache the result
            await self.cache_repository.set_agent(agent_id, result.data[0])

            logger.info(
                f"Database HIT: Retrieved agent {agent_id} from database",
                props={
                    "agent_id": str(agent_id),
                    "organization_id": str(self.organization_id),
                    "source": "database",
                },
            )

            return result.data[0]
        except Exception as e:
            logger.error(f"Error retrieving agent {agent_id}: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500, detail=f"Failed to retrieve agent: {str(e)}"
            )
        finally:
            duration = time.time() - start_time
            logger.info(
                f"get_agent completed in {duration:.2f}s for agent {agent_id}", props={}
            )

    async def create_agent(self, agent_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new agent.

        Args:
            agent_data: The agent data

        Returns:
            The created agent object
        """
        start_time = time.time()
        try:
            db = await self._get_db()
            agent_data["organization_id"] = str(self.organization_id)
            result = await db.table("agents").insert(agent_data).execute()
            if not result.data:
                raise HTTPException(
                    status_code=500, detail="Failed to create agent: No data returned"
                )
            logger.info(f"Created agent with ID {result.data[0].get('id')}", props={})

            # Cache the new agent
            await self.cache_repository.set_agent(result.data[0]["id"], result.data[0])

            # Invalidate agents list cache
            await self.cache_repository.invalidate_agents_list()

            return result.data[0]
        except Exception as e:
            logger.error(f"Error creating agent: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500, detail=f"Failed to create agent: {str(e)}"
            )
        finally:
            duration = time.time() - start_time
            logger.info(f"create_agent completed in {duration:.2f}s", props={})

    async def update_agent(
        self, agent_id: UUID, update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update an existing agent.

        Args:
            agent_id: The ID of the agent to update
            update_data: The data to update

        Returns:
            The updated agent object
        """
        start_time = time.time()
        try:
            db = await self._get_db()
            current = (
                await db.table("agents")
                .select("*")
                .eq("id", str(agent_id))
                .eq("organization_id", str(self.organization_id))
                .is_("deleted_at", "null")
                .execute()
            )
            if not current.data:
                logger.warning(
                    f"Agent {agent_id} not found for update or is deleted", props={}
                )
                raise HTTPException(
                    status_code=404, detail="Agent not found or is deleted"
                )
            if "status" not in update_data:
                update_data["status"] = current.data[0].get("status")
            if "metadata" in update_data:
                current_metadata = current.data[0].get("metadata", {})
                update_data["metadata"] = {
                    **current_metadata,
                    **update_data["metadata"],
                }
            result = (
                await db.table("agents")
                .update(update_data)
                .eq("id", str(agent_id))
                .eq("organization_id", str(self.organization_id))
                .is_("deleted_at", "null")
                .execute()
            )
            if not result.data:
                raise HTTPException(
                    status_code=500, detail="Failed to update agent: No data returned"
                )
            logger.info(f"Updated agent {agent_id}", props={})

            # Update agent in cache
            await self.cache_repository.set_agent(agent_id, result.data[0])

            # Invalidate agents list cache
            await self.cache_repository.invalidate_agents_list()

            return result.data[0]
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating agent {agent_id}: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500, detail=f"Failed to update agent: {str(e)}"
            )
        finally:
            duration = time.time() - start_time
            logger.info(
                f"update_agent completed in {duration:.2f}s for agent {agent_id}",
                props={},
            )

    async def delete_agent(self, agent_id: UUID) -> bool:
        """Delete an agent.

        Args:
            agent_id: The ID of the agent to delete

        Returns:
            True if deleted successfully
        """
        start_time = time.time()
        try:
            db = await self._get_db()
            agent = await self.get_agent(agent_id)
            if not agent:
                logger.warning(f"Agent {agent_id} not found for deletion", props={})
                raise HTTPException(status_code=404, detail="Agent not found")

            # Implement soft delete instead of hard delete
            update_data = {"deleted_at": datetime.now().isoformat()}

            result = (
                await db.table("agents")
                .update(update_data)
                .eq("id", str(agent_id))
                .eq("organization_id", str(self.organization_id))
                .execute()
            )

            logger.info(f"Soft deleted agent {agent_id}", props={})

            # Update agent in cache with deleted flag
            agent.update(update_data)
            await self.cache_repository.set_agent(agent_id, agent)

            # Invalidate agents list cache
            await self.cache_repository.invalidate_agents_list()

            return True
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting agent {agent_id}: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500, detail=f"Failed to delete agent: {str(e)}"
            )
        finally:
            duration = time.time() - start_time
            logger.info(
                f"delete_agent completed in {duration:.2f}s for agent {agent_id}",
                props={},
            )

    async def uninstall_agent(self, agent_id: UUID) -> Dict[str, Any]:
        """Uninstall an agent completely, removing it from database and platform.

        Args:
            agent_id: The ID of the agent to uninstall

        Returns:
            Dict containing uninstallation results
        """
        start_time = time.time()
        try:
            # First, remove from database and get necessary details
            uninstall_result = await self.remove_agent_from_database(str(agent_id))

            if uninstall_result.get("status") == "success" and uninstall_result.get(
                "app_id"
            ):
                # Get API key from auth if available
                api_key = None
                if uninstall_result.get("auth") and uninstall_result["auth"].get(
                    "api_key"
                ):
                    api_key = uninstall_result["auth"]["api_key"]

                # Notify platform about uninstallation
                app_id = uninstall_result["app_id"]
                platform_result = await self.uninstall_agent_from_platform(
                    app_id, api_key
                )
                uninstall_result["platform_uninstall"] = platform_result

                # Delete associated workflows if any
                if api_key and uninstall_result.get("workflow_ids"):
                    workflow_result = await self.delete_agent_workflows(
                        uninstall_result["workflow_ids"], api_key
                    )
                    uninstall_result["workflows_deletion"] = workflow_result

            return uninstall_result

        except Exception as e:
            logger.error(f"Error uninstalling agent {agent_id}: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500, detail=f"Failed to uninstall agent: {str(e)}"
            )
        finally:
            duration = time.time() - start_time
            logger.info(
                f"uninstall_agent completed in {duration:.2f}s",
                props={
                    "agent_id": str(agent_id),
                    "organization_id": str(self.organization_id),
                },
            )

    async def remove_agent_from_database(self, agent_id: str) -> Dict[str, Any]:
        """Remove agent and all associated data from database

        Args:
            agent_id: ID of the agent to remove

        Returns:
            Dict containing agent details and cleanup info
        """
        try:
            db = await self._get_db()
            current_time = datetime.now().isoformat()

            # Get agent details first
            agent_result = (
                await db.table("agents")
                .select("*")
                .eq("id", agent_id)
                .is_("deleted_at", "null")
                .execute()
            )

            if not agent_result.data:
                logger.warning(f"No active agent found with ID: {agent_id}")
                return {
                    "status": "success",
                    "message": "No active agent found to uninstall",
                    "agent_id": agent_id,
                }

            agent = agent_result.data[0]
            org_id = agent["organization_id"]
            app_id = (
                agent["metadata"].get("agent_id_platform")
                if agent.get("metadata")
                else None
            )

            # Get workflow IDs before deletion
            agent_flows_result = (
                await db.table("agent_flows")
                .select("workflow_id")
                .eq("agent_id", agent_id)
                .is_("deleted_at", "null")
                .execute()
            )

            workflow_ids = [
                flow["workflow_id"]
                for flow in agent_flows_result.data
                if flow.get("workflow_id")
            ]

            # Get auth details before deletion
            agent_auth_result = (
                await db.table("agent_auth")
                .select("*")
                .eq("agent_id", agent_id)
                .is_("deleted_at", "null")
                .execute()
            )

            # Soft delete all associated records
            await db.table("agent_flows").update({"deleted_at": current_time}).eq(
                "agent_id", agent_id
            ).execute()

            await db.table("agent_auth").update({"deleted_at": current_time}).eq(
                "agent_id", agent_id
            ).execute()

            await db.table("functions").update({"deleted_at": current_time}).eq(
                "agent_id", agent_id
            ).execute()

            # Update agent with uninstall metadata
            await db.table("agents").update(
                {
                    "deleted_at": current_time,
                    "metadata": {
                        **(agent["metadata"] or {}),
                        "uninstalled_at": current_time,
                    },
                }
            ).eq("id", agent_id).execute()

            # Clear cache
            await self.cache_repository.delete_agent(agent_id)
            await self.cache_repository.invalidate_agents_list()

            return {
                "status": "success",
                "agent": agent,
                "auth": agent_auth_result.data[0] if agent_auth_result.data else None,
                "workflow_ids": workflow_ids,
                "organization_id": org_id,
                "app_id": app_id,
            }

        except Exception as e:
            logger.error(f"Error removing agent from database: {str(e)}")
            raise

    async def uninstall_agent_from_platform(
        self, app_id: str, api_key: Optional[str] = None
    ) -> bool:
        """Notify platform API about agent uninstallation

        Args:
            app_id: Platform app ID to uninstall
            api_key: Optional API key for authentication

        Returns:
            bool indicating success/failure
        """
        try:
            platform_app_url = settings.PLATFORM_APP_URL

            async with httpx.AsyncClient() as client:
                headers = {"Content-Type": "application/json"}

                if api_key:
                    headers["x-api-key"] = api_key
                else:
                    # Fall back to environment variable
                    platform_api_key = settings.PLATFORM_API_KEY
                    headers["x-api-key"] = platform_api_key

                response = await client.post(
                    f"{platform_app_url}/apps/uninstall",
                    json={"appId": app_id},
                    headers=headers,
                    timeout=10.0,
                )

                if response.status_code in [200, 201]:
                    logger.info(
                        f"Successfully notified platform about uninstallation of {app_id}"
                    )
                    return True
                else:
                    logger.warning(
                        f"Platform notification failed with status {response.status_code}: {response.text}"
                    )
                    return False

        except Exception as e:
            logger.error(f"Error notifying platform about uninstallation: {str(e)}")
            return False

    async def delete_agent_workflows(
        self, workflow_ids: List[str], api_key: str
    ) -> Dict[str, List[str]]:
        """Delete workflows associated with the agent

        Args:
            workflow_ids: List of workflow IDs to delete
            api_key: API key for authentication

        Returns:
            Dict containing lists of successful and failed workflow deletions
        """
        try:
            workflows_url = settings.WORKFLOWS_URL
            successful_deletes = []
            failed_deletes = []

            async with httpx.AsyncClient(timeout=120.0) as client:
                headers = {"x-api-key": api_key}

                for workflow_id in workflow_ids:
                    if not workflow_id:
                        continue

                    try:
                        delete_url = f"{workflows_url}/workflows/{workflow_id}"
                        response = await client.delete(delete_url, headers=headers)

                        if response.status_code in [200, 201, 204]:
                            successful_deletes.append(workflow_id)
                            logger.info(f"Successfully deleted workflow {workflow_id}")
                        else:
                            failed_deletes.append(workflow_id)
                            logger.warning(
                                f"Failed to delete workflow {workflow_id}",
                                props={
                                    "status_code": response.status_code,
                                    "response": response.text,
                                },
                            )
                    except Exception as e:
                        failed_deletes.append(workflow_id)
                        logger.error(f"Error deleting workflow {workflow_id}: {str(e)}")

            return {
                "successful_deletes": successful_deletes,
                "failed_deletes": failed_deletes,
            }

        except Exception as e:
            logger.error(f"Error deleting workflows: {str(e)}")
            return {"successful_deletes": [], "failed_deletes": workflow_ids}

    async def get_agent_details_by_platform_id(
        self, platform_id: str
    ) -> Dict[str, Any]:
        """Get agent and organization IDs from a platform ID

        Args:
            platform_id: The platform ID of the agent

        Returns:
            Dict containing agent_id and organization_id if found, or None if not found
        """
        try:
            db = await self._get_db()

            # Find the agent by platform ID
            agent_result = (
                await db.table("agents")
                .select("*")
                .eq("id", platform_id)
                .is_("deleted_at", "null")
                .execute()
            )

            if not agent_result.data:
                logger.warning(f"No active agent found with platform ID: {platform_id}")
                return {
                    "status": "not_found",
                    "message": "No active agent found with the provided platform ID",
                    "platform_id": platform_id,
                }

            # Get the organization ID and agent ID
            agent = agent_result.data[0]
            org_id = agent["organization_id"]
            agent_id = agent["id"]

            return {
                "status": "success",
                "agent_id": agent_id,
                "organization_id": org_id,
                "platform_id": platform_id,
                "agent": agent,
            }

        except Exception as e:
            logger.error(f"Error getting agent details by platform ID: {str(e)}")
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get agent details by platform ID: {str(e)}",
            )

    async def create_or_get_system_agent(self) -> Dict[str, Any]:
        """Create or get the system agent for an organization.

        Returns:
            The system agent object, either existing or newly created
        """
        start_time = time.time()
        try:
            logger.info(
                f"Looking for system agent for organization {self.organization_id}",
                props={},
            )

            # Get the system agent template
            db = await self._get_db()
            template_result = (
                await db.table("agent_templates")
                .select("*")
                .eq("name", "System Agent")
                .eq("is_public", True)
                .execute()
            )

            if not template_result.data or len(template_result.data) == 0:
                logger.error("System agent template not found", props={})
                raise HTTPException(
                    status_code=404, detail="System agent template not found"
                )

            template_id = template_result.data[0]["id"]

            # Check if system agent already exists
            agents = await self.list_agents()
            system_agents = [
                agent for agent in agents if agent.get("template_id") == template_id
            ]

            if not system_agents:
                logger.info(
                    f"Creating system agent for organization {self.organization_id}",
                    props={},
                )
                agent_data = {
                    "name": "System Agent",
                    "description": "Your personal system-wide assistant",
                    "template_id": template_id,
                    "configuration": {
                        "role": "system",
                        "goal": "Help users with system-wide tasks and code operations",
                        "tone": "professional",
                        "core_capabilities": [
                            "code_understanding",
                            "file_operations",
                            "general_assistance",
                        ],
                        "communication_style": "formal",
                        "expertise_level": "expert",
                        "personality_traits": ["efficient", "precise", "helpful"],
                        "knowledge_domains": [
                            "programming",
                            "system operations",
                            "general assistance",
                        ],
                    },
                    "status": "active",
                    "metadata": {},
                }
                return await self.create_agent(agent_data)

            logger.info(
                f"Found existing system agent for organization {self.organization_id}",
                props={},
            )
            return system_agents[0]

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating/getting system agent: {e}", props={})
            logger.error(traceback.format_exc(), props={})
            raise HTTPException(
                status_code=500, detail=f"Failed to create/get system agent: {str(e)}"
            )
        finally:
            duration = time.time() - start_time
            logger.info(
                f"create_or_get_system_agent completed in {duration:.2f}s for org {self.organization_id}",
                props={},
            )

    async def get_team_id_for_anonymous_thread(self, thread_id: str) -> Optional[str]:
        """
        Retrieves the team_id associated with an agent deployed for an anonymous chat thread.

        Args:
            thread_id: The string representation of the chat thread's UUID.

        Returns:
            The team_id if found for an anonymous thread, otherwise None.
        """
        start_time = time.time()
        logger.debug(
            f"Attempting to find team_id for anonymous thread {thread_id}",
            props={"thread_id": thread_id},
        )
        try:
            db = await self._get_db()  # <- ensure connection
            # Ensure thread_id is passed as string
            thread_query = (
                await db.table("chat_threads")
                .select("agent_id, anon_user_id")
                .eq("id", thread_id)
                .limit(1)
                .execute()
            )

            if not thread_query.data:
                logger.warning(
                    f"Chat thread {thread_id} not found.",
                    props={"thread_id": thread_id},
                )
                return None

            thread_data = thread_query.data[0]
            agent_id = thread_data.get("agent_id")
            anon_user_id = thread_data.get("anon_user_id")

            # 2. Check if it's an anonymous user thread
            if not anon_user_id:
                logger.debug(
                    f"Thread {thread_id} does not belong to an anonymous user.",
                    props={"thread_id": thread_id},
                )
                return None

            if not agent_id:
                logger.warning(
                    f"Thread {thread_id} (anonymous) has no associated agent_id.",
                    props={"thread_id": thread_id},
                )
                return None

            # 3. Query deployments table using agent_id (ensure it's a string)
            deployment_query = (
                await db.table("deployments")
                .select("team_id")
                .eq("agent_id", str(agent_id))
                .limit(1)
                .execute()
            )

            if not deployment_query.data:
                logger.warning(
                    f"No deployment found for agent {agent_id} associated with thread {thread_id}.",
                    props={"thread_id": thread_id, "agent_id": str(agent_id)},
                )
                return None

            deployment_data = deployment_query.data[0]
            team_id = deployment_data.get("team_id")

            duration = time.time() - start_time
            if team_id:
                logger.info(
                    f"Found team_id '{team_id}' for anonymous thread {thread_id} via agent {agent_id} in {duration:.4f} seconds.",
                    props={
                        "thread_id": thread_id,
                        "agent_id": str(agent_id),
                        "team_id": team_id,
                        "duration": duration,
                    },
                )
                return team_id

            # If team_id is None, log warning and return None (dedented from original else)
            logger.warning(
                "Deployment found for agent %s, but no team_id is set.",
                agent_id,
                props={"thread_id": thread_id, "agent_id": str(agent_id)},
            )
            return None

        except asyncio.CancelledError:
            # Propagate cancellation errors
            raise
        except Exception as e:
            duration = time.time() - start_time
            logger.exception(
                "Error fetching team_id for anonymous thread %s after %.4f seconds: %s",
                thread_id,
                duration,
                str(e),
                props={"thread_id": thread_id, "duration": duration, "error": str(e)},
            )
            return None

    async def get_agent_id_by_bot_sub(self, bot_sub: str) -> Optional[UUID]:
        """Get agent ID by matching bot_sub field.

        Args:
            bot_sub: The bot subscription ID to match

        Returns:
            The agent UUID if found, None otherwise
        """
        start_time = time.time()
        try:
            logger.info(
                f"Looking up agent ID for bot_sub: {bot_sub}",
                props={
                    "bot_sub": bot_sub,
                },
            )

            db = await self._get_db()
            result = (
                await db.table("agents")
                .select("id")
                .eq("bot_sub", bot_sub)
                .is_("deleted_at", "null")
                .execute()
            )

            if not result.data:
                logger.info(
                    f"No agent found with bot_sub: {bot_sub}",
                    props={
                        "bot_sub": bot_sub,
                    },
                )
                return None

            agent_id = UUID(result.data[0]["id"])
            logger.info(
                f"Found agent ID {agent_id} for bot_sub: {bot_sub}",
                props={
                    "agent_id": str(agent_id),
                    "bot_sub": bot_sub,
                },
            )

            return agent_id
        except Exception as e:
            logger.error(
                f"Error retrieving agent ID for bot_sub {bot_sub}: {e}", props={}
            )
            logger.error(traceback.format_exc(), props={})
            return None
        finally:
            duration = time.time() - start_time
            logger.info(
                f"get_agent_id_by_bot_sub completed in {duration:.2f}s for bot_sub {bot_sub}",
                props={},
            )


async def get_agent_service(organization_id: UUID) -> AgentService:
    """Get or create an AgentService instance for the given organization.

    Implements a singleton pattern to avoid creating multiple instances
    for the same organization_id.

    Args:
        organization_id: The organization ID to get the service for

    Returns:
        An initialized AgentService instance
    """
    instance_key = str(organization_id)
    if instance_key not in _agent_service_instances:
        async with _init_lock:
            if instance_key not in _agent_service_instances:
                logger.info(
                    f"Creating new AgentService instance for organization {organization_id}",
                    props={},
                )
                service = AgentService(organization_id=organization_id)
                await service.initialize()
                _agent_service_instances[instance_key] = service
    return _agent_service_instances[instance_key]


async def cleanup_agent_service():
    """Clean up all AgentService instances during application shutdown."""
    logger.info(
        f"Cleaning up {len(_agent_service_instances)} AgentService instances", props={}
    )
    for org_id, service in _agent_service_instances.items():
        try:
            cleanup_result = await service.cleanup()
            if cleanup_result:
                logger.info(
                    f"Successfully cleaned up AgentService for org {org_id}", props={}
                )
            else:
                logger.warning(
                    f"Failed to clean up AgentService for org {org_id}", props={}
                )
        except Exception as e:
            logger.error(
                f"Error during AgentService cleanup for org {org_id}: {e}", props={}
            )
    _agent_service_instances.clear()
    logger.info("All AgentService instances cleaned up", props={})
