from typing import Any, Dict, List, Optional, TypedDict

from pydantic import BaseModel, Field


class TicketData(BaseModel):
    id: str
    ticketId: int
    title: str
    description: str
    status: str
    priority: str
    teamId: str
    teamName: str
    isPrivate: bool
    formId: str
    assignedAgent: Optional[str] = None
    assignedAgentId: Optional[str] = None
    assignedAgentEmail: Optional[str] = None
    requestorEmail: str
    submitterEmail: Optional[str] = None
    customFieldValues: List[Any]
    customerContactId: str
    customerContactFirstName: str
    customerContactLastName: str
    customerContactEmail: str
    statusId: str
    priorityId: str
    storyPoints: Optional[int]
    aiGeneratedTitle: Optional[str]
    aiGeneratedSummary: Optional[str]
    createdAt: str
    updatedAt: str


class TicketStatus(BaseModel):
    id: str
    name: str
    displayName: str
    description: str
    isDefault: bool
    teamId: str
    organizationId: str
    parentStatusId: Optional[str] = None
    createdAt: str
    updatedAt: str


class TicketComment(BaseModel):
    id: str
    content: str
    contentHtml: str
    contentMarkdown: str
    commentVisibility: str
    commentType: str
    createdAt: str
    author: str
    authorId: str
    authorUserType: str
    metadata: Dict[str, Any]


class TicketStatusInput(BaseModel):
    ticket: TicketData = Field(..., description="The ticket information from API")
    current_status: str = Field(..., description="Current status of the ticket")
    available_statuses: List[TicketStatus] = Field(
        ..., description="Available ticket statuses"
    )
    agent_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Agent configuration"
    )
    org_id: Optional[str] = Field(None, description="Organization ID")


class TicketStatusOutput(BaseModel):
    reasoning: str = Field(..., description="Reasoning for the status recommendation")
    confidence: float = Field(
        ..., description="Confidence score for the recommendation"
    )
    recommended_status: str = Field(..., description="Recommended status")
    status_updated: bool = Field(
        default=False, description="Whether the status was updated"
    )
    error: str = Field(default="", description="Error message if any")


class StatusState(TypedDict):
    """State for the ticket status change workflow."""
    flow_id: str
    ticket_id: str
    agent_id: str
    org_id: str
    agent_config: Dict[str, Any]
    public_comment_thread: Optional[List[Dict[str, Any]]] = None
    private_comment_thread: Optional[List[Dict[str, Any]]] = None
    public_author_mapping: Optional[Dict[str, Any]] = None
    private_author_mapping: Optional[Dict[str, Any]] = None
    ticket_data: Optional[Dict[str, Any]] = None
    available_statuses: Optional[List[str]] = None
    public_comments: Optional[List[Dict[str, Any]]] = None
    private_comments: Optional[List[Dict[str, Any]]] = None
    configuration: Optional[Dict[str, Any]] = None
    analysis: Optional[Dict[str, Any]] = None
    recommended_status: Optional[str] = None
    confidence: Optional[float] = None
    reasoning: Optional[str] = None
    agent_data: Optional[Dict[str, Any]] = None
    x_org_id: Optional[str] = None
    attempt_count: int
    feedback: Optional[str]
    status_updated: bool


class SummaryState(TypedDict):
    """State for the ticket summary workflow."""

    ticket_id: str
    agent_id: str
    flow_id: str
    org_id: str
    ticket_data: Optional[Dict[str, Any]] = None
    comments: Optional[List[Dict[str, Any]]] = None
    comment_thread: Optional[List[Dict[str, Any]]] = None
    private_comments: Optional[List[Dict[str, Any]]] = None
    private_comment_thread: Optional[List[Dict[str, Any]]] = None
    analysis: Optional[Dict[str, Any]] = None
    configuration: Optional[Dict[str, Any]] = None
    x_org_id: Optional[str] = None
    agent_data: Optional[Dict[str, Any]] = None
    tldr: str = ""
    detailed_summary: str = ""
    key_metadata: dict[str, str] = Field(default_factory=dict)
    config: Dict[str, Any] = Field(default_factory=dict)
    error: str = ""


class CommentAuthor(BaseModel):
    """Model for comment author in API response"""

    id: str
    email: Optional[str] = None
    name: Optional[str] = None
    avatarUrl: Optional[str] = None


class ResponseState(TypedDict):
    """State for the ticket status change workflow."""

    flow_id: str
    ticket_id: str
    agent_id: str
    source: str
    org_id: str
    agent_config: Dict[str, Any]
    x_org_id: Optional[str] = None
    agent_data: Optional[Dict[str, Any]] = None
    parentCommentId: str
    comment_id: str
    context_data: Optional[Dict[str, Any]] = None
    ticket_data: Optional[Dict[str, Any]] = None
    comments: Optional[List[Dict[str, Any]]] = None
    comment_thread: Optional[List[Dict[str, Any]]] = None
    configuration: Optional[Dict[str, Any]] = None

    response: Optional[str] = ""
    confidence: Optional[float] = None
    reasoning: Optional[str] = ""

    attempt_count: int
    judge_llm_feedback: Optional[str] = ""
    response_added: bool

    is_frustrated: Optional[bool] = False
    frustration_confidence: Optional[float] = None
    frustration_reasoning: Optional[str] = ""


class TitleState(BaseModel):
    """State for the title generation workflow."""

    ticket_id: str
    agent_id: str
    flow_id: str
    org_id: str
    ticket_data: Optional[Dict[str, Any]] = None
    comments: Optional[List[Dict[str, Any]]] = None
    comment_thread: Optional[List[Dict[str, Any]]] = None
    analysis: Optional[Dict[str, Any]] = None
    configuration: Optional[Dict[str, Any]] = None
    x_org_id: Optional[str] = None
    agent_data: Optional[Dict[str, Any]] = None
    ai_title: str = ""
    confidence: float = 0.0
    config: Dict[str, Any] = Field(default_factory=dict)
    error: str = ""


class SubTeamResponse(BaseModel):
    id: str
    name: str
    identifier: str
    teamId: str
    isActive: bool


class SubTeamAssignmentInput(BaseModel):
    ticket_id: str
    team_id: str
    org_id: str
    api_key: str
    configuration: Optional[Dict[str, Any]] = None


class SubTeamAssignmentOutput(BaseModel):
    success: bool
    assigned_team_id: Optional[str] = None
    assigned_team_name: Optional[str] = None
    message: str
