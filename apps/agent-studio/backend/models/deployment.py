from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class CreateDeploymentKeyPayload(BaseModel):
    """Payload for creating a deployment key."""

    team_id: str = Field(..., min_length=1)
    allowed_origins: Optional[list[str]] = Field(
        None, description="List of allowed origins (domains) for CORS."
    )
    deployment_type: Optional[str] = None


class DeploymentKey(BaseModel):
    id: UUID
    agent_id: UUID
    agent_key: str
    org_id: UUID
    team_id: Optional[str]
    created_at: str
    updated_at: str
    hmac_secret_key: Optional[str]
    allowed_origins: Optional[list[str]] = None
