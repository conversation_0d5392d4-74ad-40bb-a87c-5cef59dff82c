from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, HttpUrl


class WidgetPosition(BaseModel):
    top: Optional[str] = None
    left: Optional[str] = None
    bottom: Optional[str] = None
    right: Optional[str] = None


class WidgetDimensions(BaseModel):
    width: Optional[str] = None
    height: Optional[str] = None


class UserContextExample(BaseModel):
    email: Optional[str] = None
    name: Optional[str] = None
    # hash is not stored here, it's generated by the end-user's server using hmac_secret_key


class WidgetSettings(BaseModel):
    targetElementId: Optional[str] = None
    useCustomLauncher: Optional[bool] = Field(default=False)
    initialPosition: Optional[WidgetPosition] = None
    themeColorStart: Optional[str] = None
    themeColorEnd: Optional[str] = None
    gradientDirection: Optional[str] = None
    brandLogoUrl: Optional[HttpUrl] = None
    userContextExample: Optional[UserContextExample] = None
    darkMode: Optional[bool] = Field(default=False)
    autoclose: Optional[bool] = Field(default=False)

    class Config:
        # For Pydantic v2, this is the way to handle by_alias and exclude_none
        # For Pydantic v1, dict() method arguments achieve this.
        # We'll handle this in the service layer during model_dump/dict call
        pass


class CreateDeploymentKeyPayload(BaseModel):
    """Payload for creating a deployment key."""

    team_id: str = Field(..., min_length=1)
    allowed_origins: Optional[list[str]] = Field(
        None,
        description="List of allowed origins (domains) for CORS.",
    )
    deployment_type: Optional[str] = None
    widget_settings: Optional[WidgetSettings] = None


class DeploymentKey(BaseModel):
    id: UUID
    agent_id: UUID
    agent_key: str
    org_id: UUID
    team_id: Optional[str]
    created_at: str
    updated_at: str
    hmac_secret_key: Optional[str]
    allowed_origins: Optional[list[str]] = None
