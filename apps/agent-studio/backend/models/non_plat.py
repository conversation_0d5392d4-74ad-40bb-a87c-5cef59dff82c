from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field

from backend.models.deployment import WidgetSettings


class Deployment(BaseModel):
    """Model for deployment."""

    id: UUID
    agent_id: UUID
    agent_key: str
    org_id: UUID
    team_id: Optional[str] = None
    allowed_origins: Optional[list[str]] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    deployment_type: Optional[str] = None
    widget_settings: Optional[WidgetSettings] = None
    hmac_secret_key: Optional[str] = None

    class Config:
        from_attributes = True


class DeploymentCreateResponse(Deployment):
    pass


class NonPlatformUser(BaseModel):
    """Model for non-platform user."""

    id: UUID
    name: Optional[str] = None
    cookie: str
    email: Optional[str] = None
    ip_address: Optional[str] = None
    verified: bool = False
    org_id: UUID
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    class Config:
        from_attributes = True


class NonPlatformUserCreate(BaseModel):
    """Model for creating a non-platform user."""

    name: Optional[str] = None
    cookie: Optional[str] = None
    email: Optional[str] = None
    ip_address: Optional[str] = None
    verified: bool = False
    org_id: UUID


class NonPlatformUserUpdate(BaseModel):
    """Model for updating a non-platform user."""

    name: Optional[str] = None
    email: Optional[str] = None
    verified: Optional[bool] = None
