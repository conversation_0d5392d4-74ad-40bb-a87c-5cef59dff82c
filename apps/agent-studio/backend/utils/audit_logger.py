import httpx
import json
from typing import Dict, Any, Optional
from ..core.config import get_config
from ..utils.logger import get_logger

logger = get_logger("audit_logger")
settings = get_config()

# Define constants for audit log entity types and operations
class AuditLogEntityType:
    TICKET = "ticket"
    USER = "user"
    ORGANIZATION = "organization"
    TEAM = "team"
    BUSINESS_HOURS = "business_hours"
    COMMENTS = "comments"
    REACTIONS = "reactions"
    CUSTOM_FIELDS = "custom_fields"
    DRAFTS = "drafts"
    STORAGE = "storage"
    TAGS = "tags"
    VIEWS = "views"
    ACCOUNT = "account"
    ACCOUNT_CONFIGURATION = "account_configuration"
    ACCOUNT_ATTRIBUTE_VALUE = "account_attribute_value"
    CUSTOMER_CONTACT = "customer_contact"
    ACCOUNT_RELATIONSHIP_TYPE = "account_relationship_type"
    ACCOUNT_RELATIONSHIP = "account_relationship"
    ACCOUNT_ACTIVITY = "account_activity"
    ACCOUNT_NOTE = "account_note"
    ACCOUNT_TASK = "account_task"

class AuditLogOp:
    INFO = "info"
    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    ARCHIVED = "archived"
    RESTORED = "restored"
    ERROR = "error"

class AuditLogVisibility:
    SYSTEM = "system"
    TEAM = "team"
    ORGANIZATION = "organization"

async def record_audit_log(
    entity_type: str,
    entity_id: str,
    operation: str,
    description: str,
    user_id: str,
    x_org_id: str,
    api_key: str,
    team_id: str,
    visibility: str = AuditLogVisibility.TEAM,
    metadata: Optional[Dict[str, Any]] = None,
    
    
) -> Dict[str, Any]:
    """
    Record an audit log entry via the platform API.
    
    Args:
        organization_id: The ID of the organization
        entity_type: Type of entity (use AuditLogEntityType constants)
        entity_id: ID of the entity being audited
        operation: Operation performed (use AuditLogOp constants)
        description: Human-readable description of the activity
        user_id: ID of the user who performed the action
        visibility: Visibility level (use AuditLogVisibility constants)
        team_id: Optional team ID if applicable
        metadata: Optional additional metadata
        api_key: Optional API key (will use config if not provided)
        x_org_id: Optional org ID header (will use config if not provided)
        
    Returns:
        The created audit log entry or error information
    """
    # Use provided API credentials or get from config
    api_key = api_key
    x_org_id = x_org_id
    
    try:
        if not api_key or not x_org_id:
            logger.error("Missing API credentials for audit logging")
            return {"error": "Missing API credentials"}
            
        # Prepare the payload
        payload = {
            "entityType": entity_type,
            "entityUid": entity_id,
            "op": operation,
            "visibility": visibility,
            "activity": description,  # Use description as activity text
            "description": description,  # Optional detailed description
            "source": "agent-studio",  # Identify source as agent-studio
            "isAutomated": True,  # Mark as automated since it's from agent-studio
            "metadata": metadata or {},
            "organization": x_org_id,
            "activityPerformedBy": user_id
        }
        
        if team_id:
            payload["teamId"] = team_id
    except Exception as e:
        logger.error(f"Unexpected error preparing payload: {str(e)}", exc_info=True)
        return {"error": str(e)}
            
    # Set up headers
    headers = {
            "x-api-key": api_key,
            "x-org-id": x_org_id,
            "Content-Type": "application/json"
        }
        
    # Make the request
    url = f"{settings.THENA_PLATFORM_URL}/v1/activities/agent/audit-logs"
    
    logger.info(f"Sending audit log to {url}")
    logger.info(f"Headers: {headers}")
    logger.info(f"Payload: {payload}")
        
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            logger.info("Making HTTP request...")
            response = await client.post(url, headers=headers, json=payload)
            
            logger.info(f"Response status: {response.status_code}")
            logger.info(f"Response headers: {response.headers}")
            
            if response.status_code >= 400:
                error_text = response.text
                try:
                    error_json = response.json()
                    error_text = str(error_json)
                except:
                    pass
                        
                logger.error(
                    f"Error recording audit log: {response.status_code}",
                    extra={
                            "response": error_text,
                            "request_url": url,
                            "request_headers": headers,
                            "request_payload": payload
                        }
                    )
                return {"error": f"API error: {response.status_code}", "details": error_text}
                
            return response.json().get("data", {})
                
    except httpx.ConnectError as e:
        logger.error(f"Connection error: {str(e)}. Make sure the platform service is running and accessible.")
        return {"error": "Connection error", "details": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error recording audit log: {str(e)}", exc_info=True)
        return {"error": str(e)}
