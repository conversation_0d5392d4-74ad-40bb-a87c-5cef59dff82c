{"name": "@thena-backend/thena-web", "version": "0.1.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "preview": "next build && next start", "test": "jest --config jest.config.js --forceExit", "test:watch": "jest --config jest.config.js --watch", "test:coverage": "jest --config jest.config.js --coverage", "test:integration": "jest --config jest.integration.config.js", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "PWDEBUG=1 playwright test", "test:e2e:headless": "playwright test --reporter=list", "test:e2e:codegen": "playwright codegen https://platform-web.thena.tools", "test:all": "pnpm test && pnpm test:integration && pnpm test:e2e", "build-and-test": "pnpm build && (pnpm start & sleep 10 && pnpm test:e2e:headless; kill $(lsof -t -i:3000))", "dev:fast": "./dev-fast.sh"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.4.0", "@atlaskit/pragmatic-drag-and-drop-flourish": "1.1.2", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.0.3", "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator": "^1.2.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hello-pangea/dnd": "^17.0.0", "@heroicons/react": "^2.2.0", "@hocuspocus/provider": "2.13.5", "@hocuspocus/transformer": "2.13.5", "@hookform/resolvers": "^3.10.0", "@knocklabs/client": "0.12.0-rc.3.0", "@knocklabs/node": "^0.6.17", "@knocklabs/react": "0.6.0-rc.3.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.5", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@radix-ui/themes": "^3.2.0", "@sentry/nextjs": "^9.12.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.5", "@superinterface/react": "^3.17.1", "@tabler/icons-react": "^3.26.0", "@tanstack/react-query": "^5.64.1", "@tanstack/react-table": "^8.20.6", "@tanstack/react-virtual": "^3.11.2", "@tanstack/table-core": "^8.20.5", "@tiptap-pro/extension-ai": "2.11.8", "@tiptap-pro/extension-ai-advanced": "2.11.8", "@tiptap-pro/extension-collaboration-history": "2.11.2", "@tiptap-pro/extension-comments": "2.11.6", "@tiptap-pro/extension-drag-handle": "2.11.2", "@tiptap-pro/extension-drag-handle-react": "2.11.2", "@tiptap-pro/extension-emoji": "2.10.11", "@tiptap-pro/extension-file-handler": "2.10.11", "@tiptap-pro/extension-node-range": "2.11.2", "@tiptap-pro/extension-snapshot-compare": "^2.15.0", "@tiptap-pro/extension-table-of-contents": "2.11.2", "@tiptap-pro/extension-unique-id": "2.10.11", "@tiptap/core": "2.10.1", "@tiptap/extension-collaboration": "2.10.1", "@tiptap/extension-collaboration-cursor": "2.10.1", "@tiptap/extension-dropcursor": "2.10.1", "@tiptap/extension-focus": "2.10.1", "@tiptap/extension-history": "2.10.1", "@tiptap/extension-image": "2.10.1", "@tiptap/extension-link": "2.10.1", "@tiptap/extension-mention": "2.10.1", "@tiptap/extension-paragraph": "2.10.1", "@tiptap/extension-placeholder": "2.10.1", "@tiptap/extension-table": "2.10.1", "@tiptap/extension-table-cell": "2.10.1", "@tiptap/extension-table-header": "2.10.1", "@tiptap/extension-table-row": "2.10.1", "@tiptap/extension-task-item": "2.10.1", "@tiptap/extension-task-list": "2.10.1", "@tiptap/extension-text-align": "2.10.1", "@tiptap/extension-text-style": "2.10.1", "@tiptap/extension-underline": "2.10.1", "@tiptap/pm": "2.10.1", "@tiptap/react": "2.10.1", "@tiptap/starter-kit": "2.10.1", "@tiptap/suggestion": "2.10.1", "@types/classnames": "^2.3.4", "@uppy/core": "^4.4.3", "@uppy/dashboard": "^4.3.2", "@uppy/drag-drop": "^4.1.1", "@uppy/file-input": "^4.1.1", "@uppy/progress-bar": "^4.2.1", "@uppy/react": "^4.2.2", "@uppy/xhr-upload": "^4.3.3", "aceternity": "link:@/components/aceternity", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "dompurify": "^3.2.3", "emoji-mart": "^5.6.0", "framer-motion": "^11.15.0", "fuse.js": "^7.1.0", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "html-entities": "^2.5.2", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "launchdarkly-react-client-sdk": "^3.6.1", "lodash": "^4.17.21", "logrocket": "^9.0.2", "lucide-react": "^0.469.0", "luxon": "^3.5.0", "mark.js": "^8.11.1", "memoize-one": "^6.0.0", "nanoid": "^5.0.9", "next": "15.2.4", "next-themes": "^0.4.6", "openai": "^4.78.1", "react": "^19.0.0", "react-bootstrap-icons": "^1.11.5", "react-color": "^2.19.3", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-hotkeys-hook": "^4.6.1", "react-markdown": "^10.1.0", "react-photo-view": "^1.2.7", "react-resizable-panels": "^2.1.7", "react-select": "^5.9.0", "react-virtuoso": "^4.12.3", "reactflow": "^11.11.4", "recharts": "^2.15.1", "remark-gfm": "^4.0.1", "remeda": "^2.21.3", "shiki": "^3.3.0", "slugify": "^1.6.6", "sonner": "^1.7.1", "styled-components": "^6.1.13", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss-animate": "^1.0.7", "timescape": "^0.7.1", "tippy.js": "6.3.7", "typesense": "^1.8.2", "ui": "link:@/components/ui", "uuid": "^10.0.0", "y-prosemirror": "1.2.12", "y-protocols": "1.0.6", "yjs": "13.6.18", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@babel/preset-react": "^7.26.3", "@eslint/eslintrc": "^3", "@next/swc-darwin-arm64": "^15.2.4", "@opentelemetry/api": "^1.9.0", "@opentelemetry/instrumentation": "^0.201.1", "@playwright/test": "^1.51.1", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/node": "^22.9.0", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-grid-layout": "^1.3.5", "allure-commandline": "^2.33.0", "babel-jest": "^29.7.0", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.2.4", "jest": "^29.7.0", "jest-allure": "^0.1.3", "jest-environment-jsdom": "^29.7.0", "msw": "^1.3.5", "node-fetch": "2", "null-loader": "^4.0.1", "postcss": "^8", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "react-grid-layout": "^1.5.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.1", "typescript": "^5"}}