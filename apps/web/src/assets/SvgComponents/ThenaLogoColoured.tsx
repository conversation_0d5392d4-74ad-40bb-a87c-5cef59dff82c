const ThenaLogoColoured = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="10"
      viewBox="0 0 12 10"
      fill="none"
      role="img"
      aria-label="Thena Logo"
    >
      <title>Then<PERSON></title>
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M9.64432 9.99511C9.64432 7.38623 7.80011 5.22715 5.32617 4.77734V9.99511H9.64432Z"
        fill="#6A00FF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.7207 0.503906V4.28229H10.0493H11.0389C11.0389 2.30314 10.2292 0.503906 6.7207 0.503906Z"
        fill="#6A00FF"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M5.23606 4.28229V0.503906H1.95247H0.962891C0.962891 2.48306 1.77254 4.28229 5.23606 4.28229Z"
        fill="#6A00FF"
      />
    </svg>
  );
};

export default ThenaLogoColoured;
