import { getTypesenseMultiSearchUrl, getTypesenseSearchUrl } from "@/config/typesense";
import axios from "axios";
import { getRealtimeToken } from "../app/actions/auth";
import { getOrgDetails } from "../utils/browserUtils";

// Search result interfaces
export interface SearchResult {
  id?: string;
  title?: string;
  subject?: string;
  content?: string;
  name?: string;
  email?: string;
  collection?: string;
  highlights?: {
    field: string;
    snippet: string;
  }[];
  document?: Record<string, unknown>;
  [key: string]: unknown;
}

export interface SearchResponse {
  results: {
    collection: string;
    hits: SearchResult[];
    found: number;
    request_params: {
      collection_name: string;
      first_q: string;
      per_page: number;
      q: string;
    };
    search_cutoff: boolean;
  }[];
}

const ticketQueryBy = ["title", "description", "ticketIdentifier", "ticketId", "customerContactName", "accountName"]

// Function to search tickets and comments
export async function searchTicketsAndComments(
  query: string,
  apiKey: string,
): Promise<SearchResponse> {
  if (!query || typeof query !== "string" || !query.trim()) {
    return { results: [] };
  }

  try {
    const response = await fetch(getTypesenseMultiSearchUrl(), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-typesense-api-key": apiKey,
      },
      body: JSON.stringify({
        searches: [
          {
            collection: "tickets",
            q: query,
            query_by: "*",
            per_page: 5,
          },
          {
            collection: "comments",
            q: query,
            query_by: "*",
            per_page: 5,
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`Search failed with status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in tickets and comments search:", error);
    throw error;
  }
}

// Function to search accounts
export async function searchAccounts(
  query: string,
  apiKey: string,
): Promise<SearchResponse> {
  if (!query || typeof query !== "string" || !query.trim()) {
    return { results: [] };
  }

  try {
    const response = await fetch(getTypesenseMultiSearchUrl(), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-typesense-api-key": apiKey,
      },
      body: JSON.stringify({
        searches: [
          {
            collection: "accounts",
            q: query,
            query_by: "*",
            per_page: 5,
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`Search failed with status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in accounts search:", error);
    throw error;
  }
}


// Helper function to search a specific collection
export const searchCollection = async (
  collection: string,
  query: string,
  queryBy: string = "*",
  perPage: number = 250,
) => {
  try {
    const token = await getRealtimeToken()

    if (!token) {
      throw new Error('Authentication token is required');
    }
    const { orgUid } = getOrgDetails()

    const response = await axios.get(getTypesenseSearchUrl(collection), {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        'x-org-id': orgUid,
      },
      params: {
        q: query,
        query_by: queryBy,
        per_page: perPage,
      },
    });

    if (response.status !== 200) {
      throw new Error(`Search failed with status: ${response.status}`);
    }

    return response.data;
  } catch (error) {
    console.error(`Error searching collection ${collection}:`, error);
    throw error;
  }
};

// Combined search function that merges results from both searches
export async function searchAll(
  query: string,

): Promise<SearchResponse> {
  try {
    const [
      ticketsAndCommentsResults
    ] = await Promise.all([
      searchCollection("tickets", query, ticketQueryBy.join(",")),
    ]);

    return {
      results: [
        {
          collection: "tickets",
          found: ticketsAndCommentsResults.result?.found || 0,
          hits: ticketsAndCommentsResults.result?.hits || [],
          request_params: ticketsAndCommentsResults.result?.request_params || {},
          search_cutoff: ticketsAndCommentsResults.result?.search_cutoff || false,
        },
      ]
    };
  } catch (error) {
    console.error("Error in combined search:", error);
    throw error;
  }
}
