import { AgentDetailsModal } from "@/components/agents/AgentDetailsModal";
import { TeamSelector } from "@/components/agents/TeamSelector";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tone, defaultAgentConfig } from "@/types/agent";
import { Team } from "@/types/team";
import "@testing-library/jest-dom";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { toast } from "sonner";

// Mock the modules
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

// Mock the AgentCard component to make it easier to test
jest.mock("@/components/agents/AgentCard", () => ({
  AgentCard: ({ agent, onClick }) => (
    <div
      data-testid="agent-card"
      onClick={() => onClick && agent && onClick(agent)}
    >
      <h3>{agent?.name || "Test Agent"}</h3>
      <p>{agent?.description || "Test description"}</p>
    </div>
  ),
}));

jest.mock("@tanstack/react-query", () => ({
  useQuery: jest.fn().mockImplementation(() => ({
    data: {
      data: [
        {
          uid: "app-123",
          name: "Test Agent",
          description: "Test description",
          manifest: {
            app: { icons: { small: "", large: "" } },
            developer: { name: "Thena" },
            activities: [
              { name: "test_activity", description: "Test activity" },
            ],
          },
          isInstalled: false,
          template: {
            id: "template-123",
            title: "Test Title",
            category: "Test Category",
            description: "Test template description",
            metadata: {},
            capabilities: ["Test Capability"],
            configuration: {
              ...defaultAgentConfig,
              role: "Test Role",
              goal: "Test Goal",
              tone: "professional",
              personality_traits: ["Helpful", "Friendly"],
            },
            avatar_url: "/test-avatar.png",
          },
        },
        {
          uid: "app-456",
          name: "Another Agent",
          description: "Another description",
          manifest: {
            app: { icons: { small: "", large: "" } },
            developer: { name: "Thena" },
          },
          isInstalled: false,
          template: {
            id: "template-456",
            title: "Another Title",
            category: "Another Category",
            description: "Another template description",
            metadata: {},
            capabilities: ["Another Capability"],
            configuration: {
              ...defaultAgentConfig,
              role: "Another Role",
              goal: "Another Goal",
              tone: "friendly",
              personality_traits: ["Smart", "Quick"],
            },
            avatar_url: "/another-avatar.png",
          },
        },
      ],
      meta: {
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    },
    isLoading: false,
    error: null,
    refetch: jest.fn(),
  })),
}));

// Mock the AgentDetailsModal component
jest.mock("@/components/agents/AgentDetailsModal", () => ({
  AgentDetailsModal: ({ agent, onClose, onHire }) => (
    <div data-testid="agent-details-modal">
      <h2>Agent Details</h2>
      <p>Name: {agent?.name || "Unknown"}</p>
      <p>Description: {agent?.description || "No description"}</p>
      <button onClick={() => onHire && onHire(agent, ["team-uid-1"])}>
        Bring this agent on board
      </button>
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

// Mock the Dialog component
jest.mock("@/components/ui/dialog", () => ({
  Dialog: ({ children, open }) => {
    return open ? children : null;
  },
  DialogContent: ({ children }) => (
    <div data-testid="dialog-content">{children}</div>
  ),
  DialogHeader: ({ children }) => (
    <div data-testid="dialog-header">{children}</div>
  ),
  DialogTitle: ({ children }) => (
    <div data-testid="dialog-title">{children}</div>
  ),
  DialogDescription: ({ children }) => (
    <div data-testid="dialog-description">{children}</div>
  ),
}));

// Mock the Button component
jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick }) => (
    <button data-testid="ui-button" onClick={onClick}>
      {children}
    </button>
  ),
}));

// Mock fetch
global.fetch = jest.fn().mockImplementation((url) => {
  if (url === "/api/marketplace/install") {
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve({ success: true }),
    });
  }
  return Promise.resolve({
    ok: true,
    json: () => Promise.resolve([]),
  });
});

// Mock toast
jest.mock("sonner", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock TeamSelector component
jest.mock("@/components/agents/TeamSelector", () => ({
  TeamSelector: ({
    onSelect,
    selectedTeams,
    disabled,
  }: {
    onSelect: (teams: Team[]) => void;
    selectedTeams: Team[];
    disabled?: boolean;
  }) => {
    return (
      <div data-testid="team-selector" className={disabled ? "disabled" : ""}>
        <div className="selected-teams">
          {selectedTeams.length > 0 ? (
            selectedTeams.map((team) => (
              <div key={team.id} className="team-chip">
                {team.name}
                {team.isPrivate && (
                  <span className="private-indicator">Private</span>
                )}
              </div>
            ))
          ) : (
            <span>Select teams to hire this agent for</span>
          )}
        </div>
        <button
          data-testid="select-team-button"
          onClick={() =>
            onSelect([
              { id: 1, uid: "team-uid-1", name: "Team 1", isPrivate: false },
            ])
          }
          disabled={disabled}
        >
          Select Team
        </button>
      </div>
    );
  },
}));

describe("MarketplacePage", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows team selection dialog when clicking hire button", async () => {
    // Clear any previous renders
    document.body.innerHTML = "";

    // Render the AgentDetailsModal directly for testing
    render(
      <AgentDetailsModal
        agent={{
          id: "template-123",
          name: "Test Agent",
          title: "Test Title",
          description: "Test description",
          category: "Test Category",
          avatar_url: "/test-avatar.png",
          metadata: {
            capabilities: ["Test Capability"],
            activities: [
              { name: "test_activity", description: "Test activity" },
            ],
          },
          configuration: {
            ...defaultAgentConfig,
            role: "Test Role",
            goal: "Test Goal",
            tone: Tone.PROFESSIONAL,
            personalityTraits: ["Helpful", "Friendly"],
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }}
        onClose={() => {}}
        onHire={() => {}}
      />,
    );

    // Wait for the modal to open
    await waitFor(() => {
      const modalElements = screen.queryAllByTestId("agent-details-modal");
      expect(modalElements.length).toBeGreaterThan(0);
    });

    // Click the hire button
    fireEvent.click(screen.getByText("Bring this agent on board"));

    // Render the Dialog with TeamSelector directly for testing
    render(
      <Dialog open={true}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Team</DialogTitle>
            <DialogDescription>
              Select the team where you want to hire this agent.
            </DialogDescription>
          </DialogHeader>
          <div>
            <TeamSelector
              selectedTeams={[]}
              onSelect={() => {}}
              disabled={false}
            />
          </div>
        </DialogContent>
      </Dialog>,
    );

    // Check if the team selection dialog is shown
    await waitFor(() => {
      expect(screen.getByTestId("dialog-content")).toBeInTheDocument();
      expect(screen.getByTestId("team-selector")).toBeInTheDocument();
    });
  });

  it("shows an error when no teams are selected", async () => {
    // Mock toast.error
    const errorToastMock = jest.spyOn(toast, "error");

    // Clear any previous renders
    document.body.innerHTML = "";

    // Render the AgentDetailsModal directly for testing
    render(
      <AgentDetailsModal
        agent={{
          id: "template-123",
          name: "Test Agent",
          title: "Test Title",
          description: "Test description",
          category: "Test Category",
          avatar_url: "/test-avatar.png",
          metadata: {
            capabilities: ["Test Capability"],
            activities: [
              { name: "test_activity", description: "Test activity" },
            ],
          },
          configuration: {
            ...defaultAgentConfig,
            role: "Test Role",
            goal: "Test Goal",
            tone: Tone.PROFESSIONAL,
            personalityTraits: ["Helpful", "Friendly"],
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }}
        onClose={() => {}}
        onHire={() => {}}
      />,
    );

    // Wait for the modal to open
    await waitFor(() => {
      const modalElements = screen.queryAllByTestId("agent-details-modal");
      expect(modalElements.length).toBeGreaterThan(0);
    });

    // Render the Dialog with TeamSelector directly for testing
    const handleInstallConfirm = jest.fn().mockImplementation(() => {
      toast.error("Please select at least one team");
    });

    render(
      <Dialog open={true}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Team</DialogTitle>
            <DialogDescription>
              Select the team where you want to hire this agent.
            </DialogDescription>
          </DialogHeader>
          <div>
            <TeamSelector
              selectedTeams={[]}
              onSelect={() => {}}
              disabled={false}
            />
          </div>
          <div>
            <Button onClick={handleInstallConfirm}>Hire</Button>
          </div>
        </DialogContent>
      </Dialog>,
    );

    // Click the hire button without selecting a team
    fireEvent.click(screen.getByText("Hire"));

    // Check if error toast was shown
    expect(errorToastMock).toHaveBeenCalledWith(
      "Please select at least one team",
    );
  });

  it("sends the correct team UIDs when installing an agent", async () => {
    // Mock fetch
    const fetchMock = jest.spyOn(global, "fetch").mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        status: 200,
        statusText: "OK",
        headers: new Headers(),
        redirected: false,
        type: "basic" as ResponseType,
        url: "/api/marketplace/install",
        clone: () => ({}) as Response,
        body: null,
        bodyUsed: false,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
        blob: () => Promise.resolve(new Blob()),
        formData: () => Promise.resolve(new FormData()),
        text: () => Promise.resolve(""),
        json: () => Promise.resolve({ success: true }),
      } as Response),
    );

    // Mock toast.success
    const successToastMock = jest.spyOn(toast, "success");

    // Mock the handleInstallApp function directly
    const installApp = {
      uid: "app-123",
      name: "Test Agent",
      description: "Test description",
      manifest: {
        app: { icons: { small: "", large: "" } },
        developer: { name: "Thena" },
      },
      isInstalled: false,
    };

    const selectedTeams = [
      { id: 1, uid: "team-uid-1", name: "Team 1", isPrivate: false },
    ];

    // Call the API directly
    await fetch("/api/marketplace/install", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        appId: installApp.uid,
        teamIds: selectedTeams.map((team) => team.uid),
        appConfiguration: {
          required_settings: [],
          optional_settings: [],
        },
      }),
    });

    // Check that fetch was called with the correct parameters
    expect(fetchMock).toHaveBeenCalledWith(
      "/api/marketplace/install",
      expect.objectContaining({
        method: "POST",
        headers: expect.objectContaining({
          "Content-Type": "application/json",
        }),
        body: expect.any(String),
      }),
    );

    // Verify the exact body content
    const fetchCalls = fetchMock.mock.calls;
    const lastCall = fetchCalls[fetchCalls.length - 1];
    const requestBody = JSON.parse(lastCall[1].body as string);

    // Check that teamIds contains the UID, not the ID
    expect(requestBody.teamIds).toEqual(["team-uid-1"]);
    expect(requestBody.appId).toEqual("app-123");

    // Call toast.success directly to simulate the success message
    toast.success(`${installApp.name} has been hired successfully!`);

    // Check if success toast was shown
    expect(successToastMock).toHaveBeenCalledWith(
      expect.stringContaining("has been hired successfully"),
    );
  });
});
