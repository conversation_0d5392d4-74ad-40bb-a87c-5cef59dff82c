import { TeamM<PERSON><PERSON> } from "@/types/kanban";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

// Slack user image structure
interface SlackUserImages {
  image_24?: string;
  image_32?: string;
  image_48?: string;
  image_72?: string;
  image_192?: string;
  image_512?: string;
}

// Slack user specific fields
interface SlackUserData {
  id?: string;
  slackId: string;
  slackDeleted?: boolean;
  displayName?: string;
  realName?: string;
  tz?: string;
  tzLabel?: string;
  isAdmin?: boolean;
  isOwner?: boolean;
  isRestricted?: boolean;
  isUltraRestricted?: boolean;
  isBot?: boolean;
  userTitle?: string;
  slackProfileRealName?: string;
  slackProfileDisplayName?: string;
  slackProfilePhone?: string;
  slackStatusText?: string;
  slackStatusEmoji?: string;
  slackProfileEmail?: string;
  name?: string;
  images?: SlackUserImages;
  metadata?: Record<string, unknown> | null;
  userDump?: Record<string, unknown>;
}

export interface InternalUser extends Partial<TeamMembers> {
  id: number;
  uid: string;
  name: string;
  email?: string;
  source: string;
  avatar_url?: string | null;
  is_active?: boolean;
  // Slack specific fields
  slackData?: SlackUserData;
  // Add other source-specific fields as needed
  // e.g., githubData, jiraData, etc.
}

interface InternalUsersStore {
  internalUsers: InternalUser[];
  sources: string[];

  // Actions
  setInternalUsers: (users: InternalUser[]) => void;
  setSources: (sources: string[]) => void;
  addInternalUser: (user: InternalUser) => void;
  addSlackUser: (slackUserData: unknown) => void;
  updateInternalUser: (uid: string, userData: Partial<InternalUser>) => void;
  removeInternalUser: (uid: string) => void;

  // Selectors
  getUsersBySource: (source: string) => InternalUser[];
  getUserByUid: (uid: string) => InternalUser | undefined;
  searchUsers: (query: string) => InternalUser[];
}

export const useInternalUsersStore = create<InternalUsersStore>()(
  devtools(
    (set, get) => ({
      internalUsers: [],
      sources: [],

      setInternalUsers: (users) => set({ internalUsers: users }),

      setSources: (sources) => set({ sources }),

      addInternalUser: (user) =>
        set((state) => {
          // Check if user already exists
          const existingUserIndex = state.internalUsers.findIndex(
            (u) => u.uid === user.uid,
          );
          if (existingUserIndex >= 0) {
            // Update existing user
            const updatedUsers = [...state.internalUsers];
            updatedUsers[existingUserIndex] = {
              ...updatedUsers[existingUserIndex],
              ...user,
            };
            return { internalUsers: updatedUsers };
          }
          // Add new user
          return { internalUsers: [...state.internalUsers, user] };
        }),

      addSlackUser: (slackUserData: SlackUserData) => {
        if (
          !slackUserData?.slackId ||
          isNaN(Number.parseInt(slackUserData.id || slackUserData.slackId, 10))
        ) {
          console.error("Invalid Slack user data: missing or invalid ID");
          return;
        }

        const internalUser: InternalUser = {
          id: Number.parseInt(slackUserData.id || slackUserData.slackId, 10),
          uid: `slack-${slackUserData.slackId}`,
          name:
            slackUserData.displayName ||
            slackUserData.realName ||
            slackUserData.name ||
            "Unknown User",
          email: slackUserData.slackProfileEmail || "",
          source: "slack",
          avatar_url:
            slackUserData.images?.image_192 ||
            slackUserData.images?.image_72 ||
            null,
          is_active: !slackUserData.slackDeleted,
          slackData: {
            slackId: slackUserData.slackId,
            slackDeleted: slackUserData.slackDeleted,
            displayName: slackUserData.displayName,
            realName: slackUserData.realName,
            tz: slackUserData.tz,
            tzLabel: slackUserData.tzLabel,
            isAdmin: slackUserData.isAdmin,
            isOwner: slackUserData.isOwner,
            isRestricted: slackUserData.isRestricted,
            isUltraRestricted: slackUserData.isUltraRestricted,
            isBot: slackUserData.isBot,
            userTitle: slackUserData.userTitle,
            slackProfileRealName: slackUserData.slackProfileRealName,
            slackProfileDisplayName: slackUserData.slackProfileDisplayName,
            slackProfilePhone: slackUserData.slackProfilePhone,
            slackStatusText: slackUserData.slackStatusText,
            slackStatusEmoji: slackUserData.slackStatusEmoji,
            slackProfileEmail: slackUserData.slackProfileEmail,
            images: slackUserData.images,
            metadata: slackUserData.metadata,
            userDump: slackUserData.userDump,
          },
        };

        get().addInternalUser(internalUser);
      },

      updateInternalUser: (uid, userData) =>
        set((state) => ({
          internalUsers: state.internalUsers.map((user) =>
            user.uid === uid ? { ...user, ...userData } : user,
          ),
        })),

      removeInternalUser: (uid) =>
        set((state) => ({
          internalUsers: state.internalUsers.filter((user) => user.uid !== uid),
        })),

      getUsersBySource: (source) => {
        return get().internalUsers.filter((user) => user.source === source);
      },

      getUserByUid: (uid) => {
        return get().internalUsers.find((user) => user.uid === uid);
      },

      searchUsers: (query) => {
        if (!query.trim()) {
          return get().internalUsers;
        }

        const lowercaseQuery = query.toLowerCase();
        return get().internalUsers.filter(
          (user) =>
            user.name.toLowerCase().includes(lowercaseQuery) ||
            (user?.email &&
              user?.email.toLowerCase().includes(lowercaseQuery)) ||
            (user?.slackData?.displayName &&
              user?.slackData.displayName
                .toLowerCase()
                .includes(lowercaseQuery)) ||
            (user?.slackData?.realName &&
              user?.slackData.realName.toLowerCase().includes(lowercaseQuery)),
        );
      },
    }),
    {
      name: "internal-users-store",
    },
  ),
);
