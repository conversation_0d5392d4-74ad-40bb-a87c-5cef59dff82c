import { create } from "zustand";
import { CustomField } from "../types/accounts";

export type FieldType = {
  id: string;
  label: string;
  type: "standard" | "custom";
  visible: boolean;
  order: number;
  subFields?: FieldType[];
  options?: { id: string; value: string }[];
};

export type TicketDetailsDisplayState = {
  fields: FieldType[];
  expandedField: string | null;
  conversationDetails: {
    title: boolean;
    description: boolean;
  };
  setFields: (fields: FieldType[]) => void;
  toggleFieldVisibility: (fieldId: string, parentId?: string) => void;
  setExpandedField: (fieldId: string | null) => void;
  toggleConversationDetail: (key: "title" | "description") => void;
  reorderFields: (sourceIndex: number, destinationIndex: number) => void;
  reorderSubFields: (
    parentId: string,
    sourceIndex: number,
    destinationIndex: number,
  ) => void;
  getOrderedFields: () => FieldType[];
  setStateOverwrite: (state: TicketDetailsDisplayState) => void;
  updateAccountCustomFields: (customFields: CustomField[]) => void;
};

const defaultFields: FieldType[] = [
  {
    id: "form",
    label: "Form & standard fields",
    type: "standard",
    visible: true,
    order: 0,
    subFields: [
      {
        id: "assignee-sub",
        label: "Assignee",
        type: "standard",
        visible: true,
        order: 0,
      },
      {
        id: "sub-team",
        label: "Sub team",
        type: "standard",
        visible: true,
        order: 1,
      },
      {
        id: "status-sub",
        label: "Status",
        type: "standard",
        visible: true,
        order: 2,
      },
      {
        id: "urgency-sub",
        label: "Priority",
        type: "standard",
        visible: true,
        order: 3,
      },
      {
        id: "sentiment-sub",
        label: "Sentiment",
        type: "standard",
        visible: true,
        order: 4,
      },
      {
        id: "type-sub",
        label: "Type",
        type: "standard",
        visible: true,
        order: 5,
      },
    ],
  },
  {
    id: "account",
    label: "Account fields",
    type: "standard",
    visible: true,
    order: 1,
    subFields: [
      {
        id: "account-name",
        label: "Name",
        type: "standard",
        visible: true,
        order: 0,
      },
      {
        id: "account-owner",
        label: "Account owner",
        type: "standard",
        visible: true,
        order: 1,
      },
      {
        id: "account-status",
        label: "Status",
        type: "standard",
        visible: true,
        order: 2,
      },
      {
        id: "account-primary-domain",
        label: "Primary Domain",
        type: "standard",
        visible: true,
        order: 3,
      },
      {
        id: "account-secondary-domain",
        label: "Secondary domain",
        type: "standard",
        visible: true,
        order: 4,
      },
      {
        id: "account-source",
        label: "Source",
        type: "standard",
        visible: true,
        order: 5,
      },
    ],
  },
  {
    id: "custom",
    label: "Custom fields",
    type: "custom",
    visible: true,
    order: 2,
    subFields: [],
  },
  {
    id: "slas",
    label: "SLAs",
    type: "standard",
    visible: true,
    order: 3,
    subFields: [],
  },
  {
    id: "tags",
    label: "Tags",
    type: "standard",
    visible: true,
    order: 4,
    subFields: [],
  },
  {
    id: "recent-issue",
    label: "Recent tickets",
    type: "standard",
    visible: true,
    order: 5,
    subFields: [],
  },
];

export const useTicketDetailsDisplayOptions =
  create<TicketDetailsDisplayState>()((set, get) => ({
    fields: defaultFields,
    expandedField: null,
    conversationDetails: {
      title: true,
      description: true,
    },
    setFields: (fields) => set({ fields }),
    toggleFieldVisibility: (fieldId, parentId) =>
      set((state) => ({
        fields: state.fields.map((field) => {
          if (parentId) {
            if (field.id === parentId && field.subFields) {
              return {
                ...field,
                subFields: field.subFields.map((subField) =>
                  subField.id === fieldId
                    ? { ...subField, visible: !subField.visible }
                    : subField,
                ),
              };
            }
            return field;
          }
          return field.id === fieldId
            ? { ...field, visible: !field.visible }
            : field;
        }),
      })),
    setExpandedField: (fieldId) => set({ expandedField: fieldId }),
    toggleConversationDetail: (key) =>
      set((state) => ({
        conversationDetails: {
          ...state.conversationDetails,
          [key]: !state.conversationDetails[key],
        },
      })),
    reorderFields: (sourceIndex: number, destinationIndex: number) =>
      set((state) => {
        const newFields = [...state.fields];
        const [removed] = newFields.splice(sourceIndex, 1);

        const updatedFields = newFields.map((field, index) => ({
          ...field,
          order: index >= destinationIndex ? index + 1 : index,
        }));

        updatedFields.splice(destinationIndex, 0, {
          ...removed,
          order: destinationIndex,
        });

        return { fields: updatedFields };
      }),
    reorderSubFields: (
      parentId: string,
      sourceIndex: number,
      destinationIndex: number,
    ) =>
      set((state) => ({
        fields: state.fields.map((field) => {
          if (field.id === parentId && field.subFields) {
            const newSubFields = [...field.subFields];
            const [removed] = newSubFields.splice(sourceIndex, 1);

            const updatedSubFields = newSubFields.map((subField, index) => ({
              ...subField,
              order: index >= destinationIndex ? index + 1 : index,
            }));

            updatedSubFields.splice(destinationIndex, 0, {
              ...removed,
              order: destinationIndex,
            });

            return {
              ...field,
              subFields: updatedSubFields,
            };
          }
          return field;
        }),
      })),
    getOrderedFields: () => {
      const state = get();
      return [...state.fields].sort((a, b) => a.order - b.order);
    },
    setStateOverwrite: (state) => set(state),
    updateAccountCustomFields: (customFields) =>
      set((state) => {
        const accountFieldIndex = state.fields.findIndex(
          (field) => field.id === "account",
        );

        if (accountFieldIndex === -1) return state;

        const accountField = state.fields[accountFieldIndex];
        const existingSubFields = accountField.subFields || [];

        // Filter out existing custom fields (those with type "custom")
        const nonCustomFields = existingSubFields.filter(
          (field) => field.type !== "custom",
        );

        // Create new custom subfields from the provided customFields
        const newCustomSubFields = customFields.map((field, index) => ({
          id: `accounts-custom-${field.id}`,
          label: field.name,
          type: "custom" as const,
          visible: true,
          options: field.options || [],
          order: nonCustomFields.length + index,
        }));

        const updatedFields = [...state.fields];
        updatedFields[accountFieldIndex] = {
          ...accountField,
          subFields: [...nonCustomFields, ...newCustomSubFields],
        };

        return {
          ...state,
          fields: updatedFields,
        };
      }),
  }));
