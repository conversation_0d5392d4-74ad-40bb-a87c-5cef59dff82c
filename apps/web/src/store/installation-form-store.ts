import { AppManifest } from "@/types/app-studio";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export type InstallationStep = "review" | "settings" | "complete";

export interface App {
  uid: string;
  name: string;
  description: string;
  appManifest?: AppManifest;
}

interface InstallationFormState {
  settings: Record<string, unknown>;
  currentStep: InstallationStep;
  app: App | null;
  setSettings: (settings: Record<string, unknown>) => void;
  setCurrentStep: (step: InstallationStep) => void;
  setApp: (app: App) => void;
  clearForm: () => void;
}

const defaultState = {
  settings: {},
  currentStep: "review" as InstallationStep,
  app: null,
};

export const useInstallationFormStore = create<InstallationFormState>()(
  persist(
    (set) => ({
      ...defaultState,
      setSettings: (settings) => set({ settings }),
      setCurrentStep: (currentStep) => set({ currentStep }),
      setApp: (app) => set({ app }),
      clearForm: () => set(defaultState),
    }),
    {
      name: "installation-form-state",
      storage: createJSONStorage(() => sessionStorage),
      version: 2,
    },
  ),
);
