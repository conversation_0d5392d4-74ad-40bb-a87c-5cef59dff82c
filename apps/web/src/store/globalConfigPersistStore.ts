/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Organization, User } from "@/types/global";
import { create } from "zustand";
import {
  createJSONStorage,
  devtools,
  persist,
  redux,
} from "zustand/middleware";

export type StoreType = {
  orgs: Organization[];
  currentOrgId: string | null;
  currentUser: User | null;
  search?: {
    ticketsAndComments?: {
      key: string;
      parameters: {
        filter_by: string;
        expires_at: number;
      };
    };
    accounts?: {
      key: string;
      parameters: {
        filter_by: string;
        expires_at: number;
      };
    };
    teams?: string[];
  };
  loader: boolean;
  showWidget: boolean;
};

export type ActionType = {
  type:
    | "SET_ORGS"
    | "SET_CURRENT_ORG"
    | "SET_CURRENT_USER"
    | "SET_CURRENT_USER_UID"
    | "UPDATE_ORG_LOGO"
    | "UPDATE_CURRENT_USER"
    | "SET_SEARCH_KEYS"
    | "SET_LOADER"
    | "SET_SHOW_WIDGET"
    | "RESET";
  payload?: NonNullable<any>;
};

const initialState: StoreType = {
  orgs: [],
  currentOrgId: null,
  currentUser: null,
  search: undefined,
  loader: true,
  showWidget: true,
};

const reducer = (state: StoreType, action: ActionType): StoreType => {
  const { type, payload } = action;

  switch (type) {
    case "SET_ORGS":
      return {
        ...state,
        orgs: payload.orgs,
        currentOrgId: payload.currentOrgId,
      };
    case "SET_CURRENT_ORG":
      return {
        ...state,
        currentOrgId: payload.currentOrgId,
        orgs: state.orgs.map((org) =>
          org.id === payload.currentOrgId
            ? { ...org, logoUrl: payload.logoUrl || org.logoUrl }
            : org,
        ),
      };
    case "SET_CURRENT_USER":
      return {
        ...state,
        currentUser: payload.currentUser,
      };
    case "UPDATE_CURRENT_USER":
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          name: payload.name || state.currentUser.name,
          avatarUrl: payload.avatarUrl || state.currentUser.avatarUrl,
          allUserIds: payload.allUserIds || state.currentUser.allUserIds,
        },
      };
    case "SET_CURRENT_USER_UID":
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          uid: payload.uid,
          avatarUrl: payload.avatarUrl,
          name: payload.name,
        },
      };
    case "UPDATE_ORG_LOGO":
      return {
        ...state,
        orgs: state.orgs.map((org) =>
          org.id === state.currentOrgId
            ? {
                ...org,
                logoUrl: payload.logoUrl || org.logoUrl,
                name: payload.name || org.name,
                allowSameDomainJoin:
                  payload.allowSameDomainJoin !== undefined
                    ? payload.allowSameDomainJoin
                    : org.allowSameDomainJoin,
              }
            : org,
        ),
      };
    case "SET_SEARCH_KEYS":
      return {
        ...state,
        search: {
          ticketsAndComments: payload.ticketsAndComments,
          accounts: payload.accounts,
          teams: payload.teams,
        },
      };
    case "SET_SHOW_WIDGET":
      return {
        ...state,
        showWidget: payload.showWidget,
      };
    case "RESET":
      return initialState;
    case "SET_LOADER":
      return {
        ...state,
        loader: payload.loader,
      };
    default:
      return state;
  }
};

const useGlobalConfigPersistStore = create(
  persist(
    devtools(redux(reducer, initialState), {
      name: "useGlobalConfigPersistStore",
    }),
    {
      name: "useGlobalConfigPersistStore",
      version: 2.5,
      migrate() {
        return initialState;
      },
      storage: createJSONStorage(() => sessionStorage),
    },
  ),
);

export { useGlobalConfigPersistStore };
