import { create } from "zustand";
import { createJSONStorage, devtools, persist } from "zustand/middleware";

type HelpcenterPersistStore = {
  selectedFilters: string[]; // for maintaining the order of filters
  filters: Record<string, string | string[]>;
  setSelectedFilters: (filters: string[]) => void;
  addFilter: (filter: string, value: string | string[]) => void;
  removeFilter: (filter: string) => void;
  visibility: {
    title: true; // always show title
    lastEditedAt: boolean;
    lastEditedByName: boolean;
    collectionNames: boolean;
    helpCenterNames: boolean;
    authorsArr: boolean;
    state: boolean;
    articleTags: boolean;
    _id: true; // always show _id
  };
  setVisibility: (value: string, bool: boolean) => void;
  filtersOrder: string[];
  setFiltersOrder: (filters: string[]) => void;
};

export const useHelpcenterPersistStore = create<HelpcenterPersistStore>()(
  persist(
    devtools(
      (set) => ({
        selectedFilters: [],
        filters: {},
        setSelectedFilters: (filters) => set({ selectedFilters: filters }),
        addFilter: (filter, value) =>
          set((state) => {
            const filters = { ...state.filters, [filter]: value };
            return { filters };
          }),
        removeFilter: (filter) =>
          set((state) => {
             
            const { [filter]: _, ...rest } = state.filters;
            return { filters: rest };
          }),
        visibility: {
          title: true,
          lastEditedAt: true,
          lastEditedByName: false,
          collectionNames: true,
          helpCenterNames: true,
          authorsArr: false,
          state: true,
          articleTags: false,
          _id: true,
        },
        setVisibility: (value, bool) =>
          set((state) => {
            const visibility = { ...state.visibility, [value]: bool };
            return { visibility };
          }),
        // filtersOrder default should be truthy of visibility keys
        filtersOrder: [
          "title",
          "lastEditedAt",
          "collectionNames",
          "helpCenterNames",
          "state",
          "_id",
        ],
        setFiltersOrder: (filters) => set({ filtersOrder: filters }),
      }),
      {
        name: "helpcenter-store-persist",
      },
    ),
    {
      name: "helpcenter-store-persist",
      storage: createJSONStorage(() => sessionStorage),
      version: 2,
    },
  ),
);
