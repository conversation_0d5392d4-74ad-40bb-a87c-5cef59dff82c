import { z } from "zod";

export const installAppSchema = z.object({
  appId: z.string().min(1, "App ID is required"),
  teamIds: z.array(z.string()),
  appConfiguration: z
    .object({
      required_settings: z.array(z.record(z.unknown())).optional(),
      optional_settings: z.array(z.record(z.unknown())).optional(),
    })
    .optional(),
});

export const uninstallAppSchema = z.object({
  appId: z.string().min(1, "App ID is required"),
});

export const paginationSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
});

export type InstallAppRequest = z.infer<typeof installAppSchema>;
export type UninstallAppRequest = z.infer<typeof uninstallAppSchema>;
export type PaginationRequest = z.infer<typeof paginationSchema>;
