import { USER_CONFIG_SCOPE } from "@/config/user-config-constants";

// Define sidebar preference types
export const SIDEBAR_PREFERENCE_TYPES = {
  TEAM_ORDER: "SIDEBAR_TEAM_ORDER",
  PINNED_TEAMS: "SIDEBAR_PINNED_TEAMS",
  LAST_TEAM: "SIDEBAR_LAST_TEAM",
  OPEN_TEAMS: "SIDEBAR_OPEN_TEAMS",
};

// Type for sidebar preferences
export type SidebarPreferenceKey =
  | typeof SIDEBAR_PREFERENCE_TYPES.TEAM_ORDER
  | typeof SIDEBAR_PREFERENCE_TYPES.PINNED_TEAMS
  | typeof SIDEBAR_PREFERENCE_TYPES.LAST_TEAM
  | typeof SIDEBAR_PREFERENCE_TYPES.OPEN_TEAMS;

/**
 * Saves a sidebar preference to the user_config table with fallback to localStorage
 * @param key The preference key
 * @param userId Optional user ID (will use session user if not provided)
 * @param orgId Organization ID
 * @param value The preference value to save
 */
export async function saveSidebarPreference(
  key: SidebarPreferenceKey,
  userId: string | undefined,
  orgId: string,
  value: unknown,
): Promise<boolean> {
  // Save to localStorage first for immediate access
  try {
    const storageKey = `${key}_${orgId}`;
    localStorage.setItem(storageKey, JSON.stringify(value));
  } catch (localError) {
    console.error(`Error saving ${key} to localStorage:`, localError);
  }

  // Then try to save to user_config via API
  try {
    const response = await fetch("/api/user-config", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: key,
        userId, // This is now optional
        orgId,
        scope: USER_CONFIG_SCOPE.USER,
        value,
      }),
    });

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: "Unknown error" }));
      console.error(`Error saving ${key} to user_config:`, errorData.error);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error saving ${key} to user_config:`, error);
    return false;
  }
}

/**
 * Retrieves a sidebar preference from the user_config table with fallback to localStorage
 * @param key The preference key
 * @param userId Optional user ID (will use session user if not provided)
 * @param orgId Organization ID
 */
export async function getSidebarPreference<T>(
  key: SidebarPreferenceKey,
  userId: string | undefined,
  orgId: string,
): Promise<T | null> {
  // Try localStorage first for immediate response
  try {
    const storageKey = `${key}_${orgId}`;
    const savedValue = localStorage.getItem(storageKey);
    if (savedValue) {
      // If we have a value in localStorage, use it right away
      // This prevents UI flicker while waiting for API response
      const parsedValue = JSON.parse(savedValue) as T;

      // Then try to get from user_config via API (async)
      fetchFromUserConfig().catch((error) => {
        console.error(`Background fetch from user_config failed:`, error);
      });

      return parsedValue;
    }
  } catch (localError) {
    console.error(`Error fetching ${key} from localStorage:`, localError);
  }

  // If no localStorage value, try API directly
  try {
    return await fetchFromUserConfig();
  } catch (error) {
    console.error(`Error fetching ${key} from user_config:`, error);
    return null;
  }

  // Helper function to fetch from user_config API
  async function fetchFromUserConfig(): Promise<T | null> {
    // Build URL with optional userId
    let url = `/api/user-config?type=${key}&orgId=${orgId}`;
    if (userId) {
      url += `&userId=${userId}`;
    }

    try {
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ error: "Unknown error" }));
        throw new Error(
          errorData.error || "Failed to fetch preference from user_config",
        );
      }

      const result = await response.json();
      if (result.data !== null) {
        // Save to localStorage for future fast access
        try {
          const storageKey = `${key}_${orgId}`;
          localStorage.setItem(storageKey, JSON.stringify(result.data.value));
        } catch (saveError) {
          console.error(`Error saving ${key} to localStorage:`, saveError);
        }

        return result.data.value as T;
      }

      return null;
    } catch (apiError) {
      console.error(`API error fetching ${key}:`, apiError);
      throw apiError;
    }
  }
}
