import { useDivHeight } from "@/hooks/use-div-height";
import { useInboxPersistStore } from "@/store/inbox-persist";
import { useInboxStore } from "@/store/inbox-store";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { useTicketDrawerStore } from "@/store/ticketDrawerStore";
import { isEmpty } from "lodash";
import {
  Bell,
  CheckIcon,
  ChevronLeftIcon,
  FilterIcon,
  Users2,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Fragment, useEffect, useMemo, useRef, useState } from "react";
import { HeroIcon } from "../hero-icon";
import ToggleItems from "../toggle-items";
import { Button } from "../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Separator } from "../ui/separator";
import { InboxBulkActions } from "./inbox-bulk-actions";

const buildInboxFilters = async (teams) => {
  const filterList = [];
  filterList.push({
    name: "Notification type",
    groups: [
      {
        name: "Ticket updates",
        values: [
          {
            value: "ticket_created",
            label: "Created",
          },
          {
            value: "ticket_assigned",
            label: "Assigned",
          },
          {
            value: "ticket_status_changed",
            label: "Status changed",
          },
          {
            value: "ticket_priority_changed",
            label: "Priority changed",
          },
          {
            value: "ticket_escalated",
            label: "Escalated",
          },
          {
            value: "ticket_archived",
            label: "Archived",
          },
          {
            value: "ticket_custom_field_updated",
            label: "Custom field update",
          },
          {
            value: "ticket_tag_updated",
            label: "Tag updated",
          },
        ],
      },
      {
        name: "Conversations & mentions",
        values: [
          {
            value: "ticket_customer_thread_reply",
            label: "Customer thread reply",
          },
          {
            value: "ticket_customer_thread_mention",
            label: "Customer thread mention",
          },
          {
            value: "ticket_internal_thread_reply",
            label: "Internal thread reply",
          },
          {
            value: "ticket_internal_thread_mention",
            label: "Internal thread mention",
          },
          {
            value: "ticket_note_mention",
            label: "Note mention",
          },
        ],
      },
      {
        name: "SLAs and feedback",
        values: [
          {
            value: "ticket_sla_breach_warning",
            label: "SLA breach warning",
          },
          {
            value: "ticket_sla_breached",
            label: "SLA Breached",
          },
          {
            value: "ticket_csat_received",
            label: "CSAT Received",
          },
          {
            value: "ticket_csat_updated",
            label: "CSAT Updated",
          },
        ],
      },
    ],
  });
  filterList.push({
    name: "Team",
    icon: "TeamIcon",
    values: teams.map((s) => ({
      value: s.uid,
      label: s.name,
      icon: {
        value: s.icon || "TeamIcon",
        color: s.color,
      },
    })),
  });
  return filterList;
};

const handleOnFilterClick = () => {
  window.dispatchEvent(new Event("INBOX_FILTER_BUILD"));
};
const buildFiltersList = async (teams) => {
  try {
    const filtersList = await buildInboxFilters(teams);
    return filtersList;
  } catch (error) {
    console.log("Error building inbox filters", error);
  }
};
export const InboxListHeader = ({ feedClient }) => {
  const currentToggle = useInboxPersistStore((state) => state.currentToggle);
  const inboxDispatch = useInboxPersistStore((state) => state.dispatch);
  const [filterMenuOpen, setFilterMenuOpen] = useState(false);
  const allFilters = useInboxPersistStore((state) => state.allFilterList);
  const isFilterDirtyRef = useRef(true);
  const filterQueryMap = useInboxPersistStore((state) => state.queryValuesMap);
  const { closeDrawer } = useTicketDrawerStore();
  const notifications = useInboxStore((state) => state.filteredNotifications);
  const router = useRouter();
  const isFilterApplied = useMemo(() => {
    return Object.values(filterQueryMap || []).some(
      (item) => item.values.length > 0,
    );
  }, [filterQueryMap]);
  const teams = useTicketMetaStore((state) => state.teams);
  const [selectedFilterType, setSelectedFilterType] = useState<string | null>(
    null,
  );

  useEffect(() => {
    const init = () => {
      if (isFilterDirtyRef.current) {
        isFilterDirtyRef.current = false;
        buildFiltersList(teams).then((data) => {
          inboxDispatch({
            type: "SET_ALL_FILTERS",
            payload: {
              allFilters: data?.map((item) => ({
                ...item,
                values: (item?.values || []).map((val) => ({
                  label: val.label,
                  value: val.value,
                  icon: val.icon, //This is a string (safe to store)
                })),
              })),
            },
          });
        });
      }
    };
    window.addEventListener("INBOX_FILTER_BUILD", init);
    return () => {
      window.removeEventListener("INBOX_FILTER_BUILD", init);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const unreadTab = useMemo(() => {
    return currentToggle === "unread";
  }, [currentToggle]);
  const hasUnread = useMemo(() => {
    return notifications.some((item) => !item.read_at);
  }, [notifications]);

  const hasRead = useMemo(() => {
    return notifications.some((item) => item.read_at);
  }, [notifications]);

  const renderBulkActionComponent = () => {
    if (isEmpty(notifications)) {
      return null;
    }
    if (!unreadTab) {
      return (
        <InboxBulkActions
          filteredData={notifications}
          isFilterApplied={isFilterApplied}
          hasUnread={hasUnread}
          unreadTab={unreadTab}
          feedClient={feedClient}
          hasRead={hasRead}
        />
      );
    }
    if (unreadTab && hasUnread) {
      return (
        <InboxBulkActions
          filteredData={notifications}
          isFilterApplied={isFilterApplied}
          hasUnread={hasUnread}
          unreadTab={unreadTab}
          feedClient={feedClient}
          hasRead={hasRead}
        />
      );
    }
    return null;
  };
  const { elementRef } = useDivHeight();
  return (
    <div
      className="flex h-[60px] items-center gap-2 px-4 justify-between py-2"
      ref={elementRef}
    >
      <div className="flex items-center justify-start gap-2">
        <div className="border rounded-md flex items-center gap-[0.25rem] h-7 px-1">
          <ToggleItems
            options={[
              { id: "all", value: "All" },
              { id: "unread", value: "Unread" },
            ]}
            currentToggle={currentToggle}
            setCurrentToggle={(id) => {
              inboxDispatch({
                type: "SET_CURRENT_TOGGLE",
                payload: { id },
              });
              inboxDispatch({
                type: "SET_CURRENT_SELECTED_NOTIFICATION",
                payload: {
                  currentNotificationId: undefined,
                },
              });
              closeDrawer(router);
            }}
            className="text-xs h-5 py-[2px] px-2 w-max"
          />
        </div>
        {renderBulkActionComponent()}
      </div>

      <div className="flex items-center gap-2 justify-end">
        <Popover open={filterMenuOpen} onOpenChange={setFilterMenuOpen}>
          <PopoverTrigger
            asChild
            onClick={() => {
              handleOnFilterClick();
              setSelectedFilterType(null);
            }}
          >
            <Button
              variant="outline"
              className="p-0 w-7 h-7 text-color-icon-muted hover:text-color-icon-muted"
            >
              <FilterIcon size={16} className="text-secondary-foreground" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[280px] p-0">
            <Command>
              <CommandInput
                placeholder={
                  selectedFilterType === "Notification type"
                    ? "Search notification type"
                    : selectedFilterType === "Team"
                    ? "Search team"
                    : "Search filter"
                }
                className="h-9"
              />
              <CommandEmpty>No value found.</CommandEmpty>
              <CommandList>
                {!selectedFilterType ? (
                  // Initial view with icons
                  <CommandGroup>
                    <CommandItem
                      value="Notification type"
                      onSelect={() =>
                        setSelectedFilterType("Notification type")
                      }
                    >
                      <div className="flex items-center gap-2 w-full">
                        <Bell size={16} />
                        Notification type
                      </div>
                    </CommandItem>
                    <CommandItem
                      value="Team"
                      onSelect={() => setSelectedFilterType("Team")}
                    >
                      <div className="flex items-center gap-2 w-full">
                        <Users2 size={16} />
                        Team
                      </div>
                    </CommandItem>
                  </CommandGroup>
                ) : selectedFilterType === "Notification type" ? (
                  // Notification type groups view
                  <>
                    <div className="flex items-center border-b justify-start">
                      <Button
                        variant="ghost"
                        className="h-7 w-7 p-0 hover:bg-transparent"
                        onClick={() => setSelectedFilterType(null)}
                      >
                        <ChevronLeftIcon className="h-4 w-4 text-muted-text" />
                      </Button>
                      <span className="font-medium text-xs text-muted-text">
                        Back
                      </span>
                    </div>
                    {allFilters
                      ?.find((f) => f.name === "Notification type")
                      ?.groups?.map((group, index, array) => (
                        <Fragment key={group.name}>
                          <CommandGroup heading={group.name}>
                            {group.values.map((item) => (
                              <CommandItem
                                key={item.value}
                                value={item.label}
                                onSelect={() => {
                                  useInboxPersistStore.dispatch({
                                    type: "SET_QUERY_VALUES_MAP",
                                    payload: {
                                      query: "Notification type",
                                      values: [item.value],
                                      id: "category",
                                    },
                                  });
                                  setFilterMenuOpen(false);
                                  setSelectedFilterType(null);
                                }}
                              >
                                <div className="flex items-center gap-2 w-full">
                                  <div className="h-4 w-4 flex items-center justify-center rounded border">
                                    {filterQueryMap[
                                      "Notification type"
                                    ]?.values.includes(item.value) && (
                                      <CheckIcon className="h-3 w-3" />
                                    )}
                                  </div>
                                  {item.label}
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                          {index < array.length - 1 && (
                            <Separator className="my-2" />
                          )}
                        </Fragment>
                      ))}
                  </>
                ) : (
                  // Team filters view with icons
                  <>
                    <div className="flex items-center border-b justify-start">
                      <Button
                        variant="ghost"
                        className="h-7 w-7 p-0 hover:bg-transparent"
                        onClick={() => setSelectedFilterType(null)}
                      >
                        <ChevronLeftIcon className="h-4 w-4 text-muted-text" />
                      </Button>
                      <span className="font-medium text-xs text-muted-text">
                        Back
                      </span>
                    </div>
                    <CommandGroup>
                      {allFilters
                        ?.find((f) => f.name === "Team")
                        ?.values?.map((item) => (
                          <CommandItem
                            key={item.value}
                            value={item.label}
                            onSelect={() => {
                              useInboxPersistStore.dispatch({
                                type: "SET_QUERY_VALUES_MAP",
                                payload: {
                                  query: "Team",
                                  values: [item.value],
                                  id: "team",
                                },
                              });
                              setFilterMenuOpen(false);
                              setSelectedFilterType(null);
                            }}
                          >
                            <div className="flex items-center gap-2 w-full">
                              <div className="h-4 w-4 flex items-center justify-center rounded border">
                                {filterQueryMap["Team"]?.values.includes(
                                  item.value,
                                ) && <CheckIcon className="h-3 w-3" />}
                              </div>
                              {item.icon && (
                                <HeroIcon
                                  name={item.icon.value}
                                  color={item.icon.color}
                                  className="flex shrink-0 w-4 h-4"
                                />
                              )}
                              {item.label}
                            </div>
                          </CommandItem>
                        ))}
                    </CommandGroup>
                  </>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
