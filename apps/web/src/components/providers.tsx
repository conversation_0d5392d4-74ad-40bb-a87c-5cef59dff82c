"use client";

import { AUTH_URL } from "@/config/constant";
import { initLogRocket } from "@/logrocketSetup";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { TooltipProvider } from "@radix-ui/react-tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import LogRocket from "logrocket";
import { useEffect, useRef, useState, type ReactNode } from "react";
import { Toaster } from "sonner";
import { getCurrentUser, getRealtimeToken } from "../app/actions/auth";
import { useSignupPersistStore } from "../store/signupPersistStore";
import { getOrgDetails, setOrgDetails } from "../utils/browserUtils";
const IGNORE_PATHS = [
  "/login",
  "/signup",
  "/signup-error",
  "/expired-links",
  "/password-recovery",
  "/reset-password",
  "/signup-verify",
  "/set-password",
];
export default function Providers({ children }: { children: ReactNode }) {
  const { orgUid } = getOrgDetails();
  const [slug, setSlug] = useState<string | null>(null);
  const [invitedOrgId, setInvitedOrgId] = useState<string | null>(null);

  useEffect(() => {
    useGlobalConfigPersistStore.getState().dispatch({
      type: "SET_LOADER",
      payload: {
        loader: true,
      },
    });
    const params = new URLSearchParams(window.location.search);
    setSlug(params.get("slug"));
    setInvitedOrgId(params.get("invitedOrgId"));
    if (params.get("fetchOrgs")) {
      setInvitedOrgId(params.get("fetchOrgs"));
    }
    if (params.get("email")) {
      useSignupPersistStore.getState().setSignupEmail(params.get("email"));
    }
    const fetchDetails = async () => {
      const access_token = await getRealtimeToken();
      if (
        (access_token && !orgUid) ||
        orgUid === "null" ||
        orgUid === "undefined"
      ) {
        const currentUser = await getCurrentUser();
        setSlug(params.get("slug"));
        setInvitedOrgId(params.get("invitedOrgId"));
        if (!currentUser?.user_metadata?.signupComplete) {
          useSignupPersistStore.getState().setSignupEmail(currentUser?.email);
          useSignupPersistStore
            .getState()
            .setSignupName(currentUser?.user_metadata?.name);
        }
        if (currentUser?.user_metadata?.invitedOrgId) {
          setInvitedOrgId(currentUser?.user_metadata?.invitedOrgId);
        }
        useGlobalConfigPersistStore.getState().dispatch({
          type: "SET_LOADER",
          payload: {
            loader: false,
          },
        });
      }
    };
    if (
      IGNORE_PATHS.some((path) => window.location.pathname.startsWith(path))
    ) {
      return;
    } else {
      fetchDetails();
    }
  }, [orgUid, slug, invitedOrgId]);

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // During SSR, we want to keep the cache fresh
            staleTime: 60 * 1000, // 1 minute
            refetchOnWindowFocus: false,
          },
        },
      }),
  );
  const initRef = useRef(false);
  const logrocketRef = useRef(false);
  useEffect(() => {
    if (logrocketRef.current) return;
    try {
      logrocketRef.current = true;
      initLogRocket();
    } catch (error) {
      console.error(error);
    }
  }, []);
  useEffect(() => {
    const fetchOrgDetails = async () => {
      if (
        IGNORE_PATHS.some((path) => window.location.pathname.startsWith(path))
      ) {
        return;
      }
      if (!orgUid || orgUid === "null" || orgUid === "undefined") {
        const access_token = await getRealtimeToken();
        if (slug) {
          try {
            const orgResponse = await fetch(
              `${AUTH_URL}/v1/authentication/organization/${slug}`,
              {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${access_token}`,
                },
              },
            );

            if (!orgResponse.ok) {
              throw new Error("Failed to fetch organization details");
            }

            const orgData = await orgResponse.json();
            setOrgDetails(orgData.id, orgData.orgId);
            window.location.href = `/dashboard`;
          } catch (error) {
            console.error("Error fetching organization details:", error);
          }
        }
        if (invitedOrgId) {
          {
            setOrgDetails(invitedOrgId, invitedOrgId);
            try {
              const orgResponse = await fetch(`/api/organizations`, {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${access_token}`,
                  "x-org-uid": invitedOrgId,
                },
              });

              if (!orgResponse.ok) {
                throw new Error("Failed to fetch organization details");
              }

              const orgData = await orgResponse.json();
              setOrgDetails(orgData.data.id, orgData.data.orgId);
              window.location.href = `/dashboard`;
            } catch (error) {
              console.error("Error fetching organization details:", error);
            }
          }
        }
      }
    };
    fetchOrgDetails();
  }, [orgUid, slug, invitedOrgId]);

  const init = async () => {
    if (initRef.current) return;
    try {
      initRef.current = true;
      const res = useGlobalConfigPersistStore.getState().currentUser;
      if (res?.uid) {
        LogRocket.identify(res.uid, {
          name: res.name,
          email: res.email,
        });
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (logrocketRef.current) init();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>{children}</TooltipProvider>
      <Toaster
        richColors
        duration={3000}
        visibleToasts={5}
        closeButton
        toastOptions={{
          // Add custom class to prevent duplicates (Sonner will group identical toasts)
          className: "group-toast",
        }}
      />
    </QueryClientProvider>
  );
}
