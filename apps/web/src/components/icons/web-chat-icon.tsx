import React from "react";

interface WebChatIconProps {
  className?: string;
  size?: number;
}

const WebChatIcon: React.FC<WebChatIconProps> = ({
  className = "",
  size = 16,
}) => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 288 312"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_1046_263)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M231.992 264.553C231.992 205.089 186.836 156.436 128.509 143.822L128.509 264.553L231.992 264.553Z"
          fill="url(#paint0_linear_1046_263)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M162.377 46.5464L162.377 133.04L241.4 133.04L263.978 133.04C263.978 87.9914 245.163 46.5464 162.377 46.5464Z"
          fill="url(#paint1_linear_1046_263)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M126.628 133.04L126.628 46.5464L47.605 46.5464L23.1455 46.5464C23.1455 91.5954 41.9605 133.04 126.628 133.04Z"
          fill="url(#paint2_linear_1046_263)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_1046_263"
          x1="180.251"
          y1="143.822"
          x2="180.251"
          y2="264.553"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6A00FF" />
          <stop offset="1" stop-color="#008CFF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_1046_263"
          x1="213.178"
          y1="46.5464"
          x2="213.178"
          y2="133.04"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6A00FF" />
          <stop offset="1" stop-color="#008CFF" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_1046_263"
          x1="74.8868"
          y1="46.5464"
          x2="74.8868"
          y2="133.04"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#6A00FF" />
          <stop offset="1" stop-color="#008CFF" />
        </linearGradient>
        <clipPath id="clip0_1046_263">
          <rect
            width="268.05"
            height="265.564"
            fill="white"
            transform="matrix(0.984808 -0.173648 -0.0871557 0.996195 23.1455 46.5464)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default WebChatIcon;
