"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Chat } from "@/components/ui/chat";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { useChatStore } from "@/store/chat-store";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { Theme } from "@radix-ui/themes";
import "@radix-ui/themes/styles.css";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ArrowLeft, Loader2, Plus, Zap } from "lucide-react";
import { useParams } from "next/navigation";
import * as React from "react";
import {
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { ChatHome } from "./chat-home";

type ChatAgent = {
  id: string;
  name: string;
  avatar_url?: string;
};

interface Message {
  id: string;
  role: "user" | "assistant" | "system" | "tool";
  content: string;
  createdAt?: Date;
  suggestions?: string[];
  finalToolStatus?: string | null;
}

interface ChatState {
  isInChat: boolean;
  currentConversationId: string | null;
  publicApiKey: string;
  agentId: string;
  userApiKey: string;
  initialMessage: string | null;
}

const parseContentAndSuggestions = (content: string) => {
  const regex = /^(.*?)```suggestions\n([\s\S]*?)\n```([\s\S]*?)$/s;
  const match = content.match(regex);
  if (match) {
    const before = match[1].trim();
    const suggestionsText = match[2];
    const after = match[3].trim();
    const suggestions = suggestionsText
      .split("\n")
      .map((s) => s.trim())
      .filter((s) => s.length > 0);
    const mainContent = [before, after].filter(Boolean).join("\n\n").trim();
    return {
      mainContent: mainContent || " ",
      extractedSuggestions: suggestions,
    };
  }
  return { mainContent: content, extractedSuggestions: [] };
};

export function ChatDialog(): React.ReactNode {
  // console.log("[ChatDialog Render] Component rendering...");

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 10000,
          },
        },
      }),
  );
  const [isCreatingThread, setIsCreatingThread] = useState(false);
  const [loadingAgentId, setLoadingAgentId] = useState<string | null>(null);
  const [_loadingConversationId, setLoadingConversationId] = useState<
    string | null
  >(null);
  const {
    publicApiKey,
    agentId,
    userApiKey,
    currentAgent,
    setCurrentAgent,
    isOpen,
    setIsOpen,
  } = useChatStore();

  // Get current organization ID from global store - prefix with _ since we don't use it directly yet
  const _currentOrgId = useGlobalConfigPersistStore(
    (state) => state.currentOrgId,
  );

  const tickets = useTicketMetaStore((state) => state.tickets);
  const params = useParams<{ teamId?: string }>();
  const teamId = params?.teamId;
  // console.log("[ChatDialog Render] useParams result:", params, "Extracted teamId:", teamId);

  const _currentTickets = useMemo(() => tickets, [tickets]);

  const [isInChatView, setIsInChatView] = useState(false);

  const [chatContext, setChatContext] = useState<
    Omit<ChatState, "isInChat"> & { currentConversationId: string | null }
  >({
    currentConversationId: null,
    publicApiKey,
    agentId,
    userApiKey,
    initialMessage: null,
  });

  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showTypingIndicator, setShowTypingIndicator] =
    useState<boolean>(false);
  const [liveToolStatusMessage, setLiveToolStatusMessage] = useState<
    string | null
  >(null);
  const runIdToFinalStatusMapRef = useRef<Record<string, string>>({});
  const initialMount = useRef(false);

  useEffect(() => {
    initialMount.current = false;
  }, [chatContext.currentConversationId]);

  useEffect(() => {
    // console.log("[History Effect] Running. isInChatView:", isInChatView, "ID:", chatContext.currentConversationId, "Mount Flag:", initialMount.current);

    const fetchMessages = async () => {
      if (!isInChatView || !chatContext.currentConversationId) {
        // console.log("[History Effect] Conditions not met for fetch (not in chat or no ID). Clearing messages.");
        setMessages([]);
        setIsLoading(false);
        return;
      }

      if (initialMount.current === true) {
        // console.log("[History Effect] Already mounted/fetched for this ID. Skipping fetch.");
        return;
      }

      // console.log("[History Effect] Conditions met, setting mount flag to true and fetching...");
      initialMount.current = true;

      const threadId = chatContext.currentConversationId;
      const currentAgentId = chatContext.agentId;
      const currentPublicApiKey = chatContext.publicApiKey;
      const currentUserApiKey = chatContext.userApiKey;
      const initialMsg = chatContext.initialMessage;

      // console.log("[History] Fetching messages for thread:", threadId);
      setIsLoading(true);

      // Only reset messages if there's no initial message - otherwise we might lose the optimistic message
      if (!initialMsg) {
        setMessages([]);
      }

      try {
        const apiUrl = new URL("/api/chat/messages", window.location.origin);
        apiUrl.searchParams.append("threadId", threadId);
        apiUrl.searchParams.append("agentId", currentAgentId);
        apiUrl.searchParams.append("publicApiKey", currentPublicApiKey);
        apiUrl.searchParams.append("userApiKey", currentUserApiKey);

        // console.log("[History] Fetching messages from URL:", apiUrl.toString());

        const response = await fetch(apiUrl.toString(), {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(
            "Failed to fetch messages:",
            response.status,
            errorText,
          );
          initialMount.current = false;
          throw new Error(`Failed to fetch messages: ${response.status}`);
        }

        const data = await response.json();
        // Define a type for the raw message from history API
        type RawHistoryMessage = {
          id: string;
          role: "user" | "assistant";
          created_at?: number;
          content?: { type: string; text?: { value?: string } }[];
        };
        const formattedMessages: Message[] = data.data
          .map((msg: RawHistoryMessage) => {
            const rawContent = msg.content?.[0]?.text?.value || "";
            let finalContent = rawContent;
            let suggestions: string[] = [];
            if (msg.role === "assistant") {
              const parsed = parseContentAndSuggestions(rawContent);
              finalContent = parsed.mainContent;
              suggestions = parsed.extractedSuggestions;
            }

            return {
              id: msg.id,
              role: msg.role,
              content: finalContent,
              createdAt: msg.created_at
                ? new Date(msg.created_at * 1000)
                : undefined,
              suggestions: suggestions,
              finalToolStatus: null,
            };
          })
          .sort(
            (a: Message, b: Message) =>
              (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0),
          );

        // console.log("[History] Successfully fetched and formatted messages:", formattedMessages.length);
        setMessages(formattedMessages);

        if (initialMsg) {
          // console.log("Initial message present, triggering send:", initialMsg);
          // Add the user message to the UI first so it appears immediately
          const optimisticId = `initial-${Date.now()}`;
          const optimisticUserMessage: Message = {
            id: optimisticId,
            role: "user",
            content: initialMsg,
            createdAt: new Date(),
          };

          // Add to messages array so it displays in the UI
          setMessages((prevMessages) => [
            ...prevMessages,
            optimisticUserMessage,
          ]);

          // Then process on the server side using sendMessageToServer
          queueMicrotask(async () => {
            try {
              await sendMessageToServer(initialMsg, true);
            } catch (submitError) {
              console.error(
                "Error during initial message sendMessageToServer:",
                submitError,
              );
            } finally {
              setInput("");
              setChatContext((prev) => ({ ...prev, initialMessage: null }));
              setIsLoading(false);
            }
          });
        } else {
          // console.log("[History] Fetch complete, no initial message.");
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Error fetching or processing messages:", error);
        // Only clear messages if there's no initial message in progress
        if (!chatContext.initialMessage) {
          setMessages([]);
        }
        setIsLoading(false);
        initialMount.current = false;
      }
    };

    if (isInChatView && chatContext.currentConversationId) {
      // console.log("[History Effect] Calling fetchMessages...");
      fetchMessages();
    } else {
      // console.log("[History Effect] Not in chat view or no conversation ID. Clearing state.");
      setMessages([]);
      if (isLoading) setIsLoading(false);
    }
  }, [
    isInChatView,
    chatContext.currentConversationId,
    chatContext.agentId,
    chatContext.publicApiKey,
    chatContext.userApiKey,
    chatContext.initialMessage,
    isLoading,
  ]);
  // Note: sendMessageToServer is intentionally omitted from the dependency array because it would create a circular dependency

  const handleInputChange = (
    e:
      | React.ChangeEvent<HTMLTextAreaElement>
      | React.ChangeEvent<HTMLInputElement>,
  ) => {
    setInput(e.target.value);
  };

  const sendMessageToServer = useCallback(
    async (content: string, isInitialMessage = false) => {
      // Store the optimistic ID in function scope so it can be accessed in the error handlers
      let optimisticId: string | null = null;

      if (!content.trim() || !chatContext.currentConversationId) return;

      const threadId = chatContext.currentConversationId;
      const currentAgentId = chatContext.agentId;

      const freshTickets = useTicketMetaStore.getState().tickets;
      const filteredTicketData = freshTickets
        .filter((t) => !t.archived_at && !t.deleted_at)
        .map((t) => ({
          uid: t.uid,
          title: t.title,
          description: t.description,
          created_at: t.created_at,
          updated_at: t.updated_at,
        }));

      // Get fresh teams data from the store
      const freshTeams = useTicketMetaStore.getState().teams;
      // Filter teams data to only include essential fields
      // Available fields: id, uid, icon, color, identifier, organization_id, parent_team_id, name, description, configuration_id, team_owner_id, is_active, is_private, created_at, updated_at, deleted_at, archived_at, userIsPartOf
      const filteredTeamsData = freshTeams.map((team) => ({
        uid: team.uid,
        identifier: team.identifier,
        name: team.name,
        is_active: team.is_active,
        userIsPartOf: team.userIsPartOf,
      }));
      // console.log("[sendMessageToServer] Preparing fetch. teamId:", teamId, "ticketData count:", filteredTicketData.length);

      // Only add optimistic message if this isn't an initial message (which would already be added to UI)
      if (!isInitialMessage) {
        optimisticId = `user-${Date.now()}`;
        const optimisticUserMessage: Message = {
          id: optimisticId,
          role: "user",
          content: content,
          createdAt: new Date(),
        };
        setMessages((prev) => [...prev, optimisticUserMessage]);
      }

      if (input === content) {
        setInput("");
      }
      setIsLoading(true);
      setShowTypingIndicator(true);
      setLiveToolStatusMessage(null);

      let assistantMessageId: string | null = null;
      let accumulatedContent = "";

      try {
        // console.log("[sendMessageToServer] Sending fetch with body containing teamId:", teamId);
        const response = await fetch(`/api/chat/messages`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            threadId: threadId,
            agentId: currentAgentId,
            content: content,
            ticketData: filteredTicketData,
            teamId: teamId,
            teamData: filteredTeamsData,
          }),
        });

        if (!response.ok) {
          // Remove optimistic message if it was added
          if (!isInitialMessage) {
            setMessages((prev) => prev.filter((m) => m.id !== optimisticId));
          }
          const errorText = await response.text();
          console.error(
            "API Error sending message:",
            response.status,
            errorText,
          );
          setShowTypingIndicator(false);
          throw new Error(`API Error: ${response.status}`);
        }

        if (!response.body) {
          setShowTypingIndicator(false);
          throw new Error("Response body is missing");
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let leftover = "";

        while (true) {
          const { done, value } = await reader.read();
          const chunkToProcess = value
            ? leftover + decoder.decode(value, { stream: true })
            : leftover;
          leftover = "";

          if (!chunkToProcess && done) break;

          const lines = chunkToProcess.split("\n");
          if (!done) {
            leftover = lines.pop() || "";
          }

          for (const line of lines) {
            const trimmedLine = line.trim();
            if (trimmedLine && trimmedLine !== "[DONE]") {
              try {
                const parsedData = JSON.parse(trimmedLine);
                const eventType = parsedData.event;
                const eventData = parsedData.data;

                if (
                  eventType === "thread.run.step.created" &&
                  eventData?.step_details?.type === "tool_calls"
                ) {
                  // console.log("[Tool Status] Step Created (tool_calls)");
                  setLiveToolStatusMessage("Starting tool execution...");
                } else if (
                  eventType === "thread.run.step.delta" &&
                  eventData?.delta?.step_details?.type === "tool_calls"
                ) {
                  const toolCall = eventData.delta.step_details.tool_calls?.[0];
                  if (toolCall?.function?.name) {
                    const toolName = toolCall.function.name;
                    // console.log(`[Tool Status] Step Delta: Calling ${toolName}`);
                    setLiveToolStatusMessage(`Calling tool ${toolName}...`);
                  }
                } else if (
                  eventType === "thread.run.step.completed" &&
                  eventData?.step_details?.type === "tool_calls"
                ) {
                  const toolCall = eventData.step_details.tool_calls?.[0];
                  const runId = eventData.run_id;
                  if (toolCall?.function?.name && runId) {
                    const toolName = toolCall.function.name;
                    const finalStatus = `✅ Finished using ${toolName}`;
                    // console.log(`[Tool Status] Step Completed: Storing final status "${finalStatus}" for runId: ${runId}`);
                    runIdToFinalStatusMapRef.current[runId] = finalStatus;
                    setLiveToolStatusMessage(null);
                  } else {
                    // console.warn("[Tool Status] Step Completed event missing tool name or run ID.", { eventData });
                    setLiveToolStatusMessage(null);
                  }
                } else if (eventType === "thread.message.delta") {
                  if (
                    parsedData.data?.delta?.content?.[0]?.type === "text" &&
                    parsedData.data.delta.content[0].text?.value != null
                  ) {
                    const textDelta =
                      parsedData.data.delta.content[0].text.value;
                    if (textDelta) {
                      accumulatedContent += textDelta;
                      let firstChunkProcessed = false;

                      setMessages((prevMessages) => {
                        if (assistantMessageId === null) {
                          assistantMessageId =
                            parsedData.data?.id || `assistant-${Date.now()}`;
                        }
                        const assistantMessageExists = prevMessages.some(
                          (msg) => msg.id === assistantMessageId,
                        );
                        if (!assistantMessageExists) {
                          const newAssistantMessage: Message = {
                            id: assistantMessageId,
                            role: "assistant",
                            content: accumulatedContent,
                            createdAt: new Date(),
                            suggestions: [],
                            finalToolStatus: null,
                          };
                          firstChunkProcessed = true;
                          return [...prevMessages, newAssistantMessage];
                        } else {
                          return prevMessages.map((msg) =>
                            msg.id === assistantMessageId
                              ? { ...msg, content: accumulatedContent }
                              : msg,
                          );
                        }
                      });
                      if (firstChunkProcessed) {
                        setShowTypingIndicator(false);
                        setLiveToolStatusMessage(null);
                      }
                    }
                  }
                } else if (eventType === "thread.message.completed") {
                  const finalData = eventData;
                  const finalContent =
                    finalData?.content?.[0]?.text?.value || accumulatedContent;
                  const finalMessageId = finalData?.id;
                  const finalRunId = finalData?.run_id;

                  setLiveToolStatusMessage(null);

                  if (finalMessageId) {
                    const { mainContent, extractedSuggestions } =
                      parseContentAndSuggestions(finalContent);
                    const statusFromRef = finalRunId
                      ? runIdToFinalStatusMapRef.current[finalRunId]
                      : null;
                    // console.log(`[Stream] Message Completed ${finalMessageId}: Looking up status for runId ${finalRunId}. Found: "${statusFromRef}"`);

                    const newCompleteAssistantMessage: Message = {
                      id: finalMessageId,
                      role: "assistant",
                      content: mainContent,
                      createdAt: new Date(),
                      suggestions: extractedSuggestions,
                      finalToolStatus: statusFromRef,
                    };
                    // console.log(`[Stream] Creating final message object for ${finalMessageId}:`, newCompleteAssistantMessage);

                    setMessages((prevMessages) => {
                      const filteredPrevMessages = prevMessages.filter(
                        (msg) => msg.id !== finalMessageId,
                      );
                      return [
                        ...filteredPrevMessages,
                        newCompleteAssistantMessage,
                      ];
                    });

                    if (finalRunId) {
                      // console.log(`[Stream] Cleaning up stored status for runId ${finalRunId}`);
                      delete runIdToFinalStatusMapRef.current[finalRunId];
                    }
                  } else {
                    // console.warn("[Stream] Message Completed event missing message ID.");
                  }
                  setShowTypingIndicator(false);
                }
              } catch (parseError) {
                console.warn(
                  "[Stream] Could not parse line as JSON:",
                  line,
                  parseError,
                );
              }
            }
          }
          if (done) break;
        }
      } catch (error) {
        console.error("Error in sendMessageToServer:", error);
        // If this is not an initial message (which might have a different ID format),
        // filter out the failed optimistic message
        if (!isInitialMessage) {
          setMessages((prev) => prev.filter((m) => m.id !== optimisticId));
        }
        setShowTypingIndicator(false);
        setLiveToolStatusMessage(null);
      } finally {
        setIsLoading(false);
        setShowTypingIndicator(false);
        setLiveToolStatusMessage(null);
      }
    },
    [chatContext.currentConversationId, chatContext.agentId, teamId, input],
  );
  // Note: sendMessageToServer is intentionally omitted from the dependency array because it would create a circular dependency

  const handleSubmit = async (
    e?: React.FormEvent<HTMLFormElement> | { preventDefault?: () => void },
    _options?: {
      data?: Record<string, string>;
      experimental_attachments?: FileList;
    },
  ) => {
    if (
      e &&
      typeof (e as React.FormEvent<HTMLFormElement>).preventDefault ===
        "function"
    ) {
      (e as React.FormEvent<HTMLFormElement>).preventDefault();
    } else if (
      e &&
      typeof (e as { preventDefault?: () => void }).preventDefault ===
        "function"
    ) {
      (e as { preventDefault?: () => void }).preventDefault?.();
    }

    if (!input.trim()) return;

    await sendMessageToServer(input, false);
  };

  const handleAppend = useCallback(
    (message: { role: "user"; content: string }) => {
      sendMessageToServer(message.content);
    },
    [sendMessageToServer],
  );

  const handleStartChat = useCallback(
    async (
      conversationId?: string,
      agent?: ChatAgent,
      initialQuery?: string,
      forceNewThread?: boolean,
    ) => {
      if (!agent) {
        console.warn("[handleStartChat] No agent provided.");
        return;
      }

      setLoadingAgentId(agent.id);
      if (conversationId) {
        setLoadingConversationId(conversationId);
      }

      try {
        const finalAgent = agent;
        setCurrentAgent({
          id: finalAgent.id,
          name: finalAgent.name,
          avatar_url: finalAgent.avatar_url || "",
        });

        let threadId = conversationId;

        if (!threadId || forceNewThread) {
          const createResponse = await fetch(
            `/api/chat/${finalAgent.id}/threads`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                agent_id: finalAgent.id,
                title: `${finalAgent.name} - New chat`,
                metadata: {
                  created_at: new Date().toISOString(),
                  initial_query: initialQuery,
                },
              }),
            },
          );

          if (!createResponse.ok) {
            throw new Error("Failed to create new thread");
          }

          const newThread = await createResponse.json();
          threadId = newThread.id;
        }

        setMessages([]);
        setInput("");
        setIsLoading(true);
        setShowTypingIndicator(false);

        setChatContext({
          currentConversationId: threadId || null,
          publicApiKey,
          agentId: finalAgent.id,
          userApiKey,
          initialMessage: initialQuery || null,
        });

        setIsInChatView(true);
      } catch (error) {
        console.error("Error handling chat start:", error);
        setIsLoading(false);
      } finally {
        setLoadingAgentId(null);
        setLoadingConversationId(null);
      }
    },
    [
      publicApiKey,
      userApiKey,
      setCurrentAgent,
      setMessages,
      setInput,
      setIsLoading,
      setShowTypingIndicator,
    ],
  );

  const handleBackToHome = useCallback(() => {
    setMessages([]);
    setInput("");
    setIsLoading(false);
    setShowTypingIndicator(false);

    setChatContext({
      currentConversationId: null,
      publicApiKey,
      agentId,
      userApiKey,
      initialMessage: null,
    });
    setCurrentAgent(null);
    setIsInChatView(false);
  }, [publicApiKey, agentId, userApiKey, setCurrentAgent]);

  const handleNewThread = useCallback(async () => {
    if (!currentAgent) return;
    setIsCreatingThread(true);
    try {
      const response = await fetch(`/api/chat/${currentAgent.id}/threads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          agent_id: currentAgent.id,
          title: `${currentAgent?.name || "Chat"} - New chat`,
          metadata: {},
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create new thread");
      }

      const thread = await response.json();
      await handleStartChat(thread.id, currentAgent, undefined, false);
    } catch (error) {
      console.error("Error creating new thread:", error);
    } finally {
      setIsCreatingThread(false);
    }
  }, [currentAgent, handleStartChat]);
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);
  return (
    <QueryClientProvider client={queryClient}>
      <Sheet
        open={isOpen}
        onOpenChange={(open) => {
          setIsOpen(open);
          dispatch({
            type: "SET_SHOW_WIDGET",
            payload: { showWidget: !open },
          });
        }}
        modal={false}
      >
        <SheetTrigger asChild>
          <Button
            className={cn(
              "relative flex items-center gap-2 px-3 h-[28px] rounded-sm bg-gradient-to-br from-[#6A00FF] to-[#3B01B7] text-white overflow-hidden",
              "shadow-[0_2px_8px_rgba(106,0,255,0.25)] hover:shadow-[0_2px_12px_rgba(106,0,255,0.35)]",
              "hover:from-[#7B1AFF] hover:to-[#4C12C8] transition-all duration-300",
              isOpen &&
                "from-[#7B1AFF] to-[#4C12C8] shadow-[0_2px_12px_rgba(106,0,255,0.35)]",
              loadingAgentId && "opacity-75 cursor-not-allowed",
            )}
            aria-label="Open chat"
            disabled={loadingAgentId !== null}
          >
            <style jsx global>{`
              @keyframes icon-glow {
                0%,
                100% {
                  filter: drop-shadow(0 0 8px rgba(106, 0, 255, 0.8));
                }
                50% {
                  filter: drop-shadow(0 0 16px rgba(106, 0, 255, 1));
                }
              }
              @keyframes sparkle {
                0%,
                100% {
                  transform: translate(var(--tx-start), var(--ty-start))
                    scale(0.6);
                  opacity: 0;
                  filter: blur(0px) brightness(1);
                }
                50% {
                  transform: translate(var(--tx-end), var(--ty-end)) scale(1);
                  opacity: 1;
                  filter: blur(1px) brightness(1.5);
                }
              }
            `}</style>
            <div className="relative z-0 flex items-center gap-2">
              <div className="relative">
                <Zap
                  className="size-4 shrink-0 -rotate-12 text-white relative z-10"
                  style={{ animation: "icon-glow 2s ease-in-out infinite" }}
                />
              </div>
              <span className="text-sm font-medium">Thena AI </span>
              <span
                className="absolute w-[3px] h-[3px] bg-white rounded-full left-1 top-1 z-0 shadow-[0_0_2px_#fff]"
                style={
                  {
                    animation: "sparkle 4s ease-in-out infinite",
                    "--tx-start": "0px",
                    "--ty-start": "0px",
                    "--tx-end": "-10px",
                    "--ty-end": "-10px",
                  } as React.CSSProperties & {
                    "--tx-start": string;
                    "--ty-start": string;
                    "--tx-end": string;
                    "--ty-end": string;
                  }
                }
              />
              <span
                className="absolute w-[3px] h-[3px] bg-white rounded-full right-8 top-2 z-0 shadow-[0_0_2px_#fff]"
                style={
                  {
                    animation: "sparkle 4s ease-in-out infinite 0.5s",
                    "--tx-start": "0px",
                    "--ty-start": "0px",
                    "--tx-end": "8px",
                    "--ty-end": "-8px",
                  } as React.CSSProperties & {
                    "--tx-start": string;
                    "--ty-start": string;
                    "--tx-end": string;
                    "--ty-end": string;
                  }
                }
              />
              <span
                className="absolute w-[3px] h-[3px] bg-white rounded-full right-2 bottom-1 z-0 shadow-[0_0_2px_#fff]"
                style={
                  {
                    animation: "sparkle 4s ease-in-out infinite 1s",
                    "--tx-start": "0px",
                    "--ty-start": "0px",
                    "--tx-end": "12px",
                    "--ty-end": "8px",
                  } as React.CSSProperties & {
                    "--tx-start": string;
                    "--ty-start": string;
                    "--tx-end": string;
                    "--ty-end": string;
                  }
                }
              />
              <span
                className="absolute w-[3px] h-[3px] bg-white rounded-full left-6 bottom-2 z-0 shadow-[0_0_2px_#fff]"
                style={
                  {
                    animation: "sparkle 4s ease-in-out infinite 1.5s",
                    "--tx-start": "0px",
                    "--ty-start": "0px",
                    "--tx-end": "-8px",
                    "--ty-end": "10px",
                  } as React.CSSProperties & {
                    "--tx-start": string;
                    "--ty-start": string;
                    "--tx-end": string;
                    "--ty-end": string;
                  }
                }
              />
              <span
                className="absolute w-[3px] h-[3px] bg-white rounded-full right-12 top-1 z-0 shadow-[0_0_2px_#fff]"
                style={
                  {
                    animation: "sparkle 4s ease-in-out infinite 2s",
                    "--tx-start": "0px",
                    "--ty-start": "0px",
                    "--tx-end": "6px",
                    "--ty-end": "-12px",
                  } as React.CSSProperties & {
                    "--tx-start": string;
                    "--ty-start": string;
                    "--tx-end": string;
                    "--ty-end": string;
                  }
                }
              />
            </div>
          </Button>
        </SheetTrigger>
        <SheetContent
          side="right"
          showOverlay={false}
          className="w-[432px] p-0 flex flex-col border-l border-l-border shadow-[-4px_0px_15px_rgba(0,0,0,0.05)]"
          onInteractOutside={(e) => {
            if (e.target && e.target instanceof Element) {
              const isCloseButton = e.target.closest('[aria-label="Close"]');
              if (!isCloseButton) {
                e.preventDefault();
              }
            } else {
              e.preventDefault();
            }
          }}
          onPointerDownOutside={(e) => {
            if (e.target && e.target instanceof Element) {
              const isCloseButton = e.target.closest('[aria-label="Close"]');
              if (!isCloseButton) {
                e.preventDefault();
              }
            } else {
              e.preventDefault();
            }
          }}
        >
          <Theme
            accentColor="violet"
            grayColor="gray"
            radius="medium"
            scaling="100%"
          >
            {!isInChatView ? (
              <ChatHome onStartChat={handleStartChat} />
            ) : (
              <div className="flex flex-col h-full">
                <div className="px-2 py-2 border-b bg-card">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {currentAgent && (
                        <>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={handleBackToHome}
                            className="hover:bg-transparent"
                            title="Back to home"
                          >
                            <ArrowLeft className="h-4 w-4" />
                          </Button>

                          <div className="flex items-center gap-2">
                            <span className="font-medium">
                              {currentAgent.name}
                            </span>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={handleNewThread}
                                className="hover:bg-accent relative"
                                title="Start a new conversation"
                                disabled={isCreatingThread}
                                aria-busy={isCreatingThread}
                              >
                                {isCreatingThread ? (
                                  <Loader2
                                    className="h-4 w-4 animate-spin text-[#7C3AED]"
                                    role="status"
                                    aria-label="Creating new conversation"
                                  />
                                ) : (
                                  <Plus className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                    <div className="flex-1" />
                  </div>
                </div>
                <div className="flex-1 overflow-hidden bg-background px-4 pt-4">
                  <Suspense
                    fallback={
                      <div className="flex items-center justify-center h-full">
                        <div className="flex flex-col items-center justify-center py-8 space-y-6 h-full">
                          <div className="flex gap-1.5">
                            <div className="w-3 h-3 rounded-full bg-[#7C3AED] animate-[bounce_0.9s_infinite] [animation-delay:0.1s]"></div>
                            <div className="w-3 h-3 rounded-full bg-[#7C3AED] animate-[bounce_0.9s_infinite] [animation-delay:0.3s]"></div>
                            <div className="w-3 h-3 rounded-full bg-[#7C3AED] animate-[bounce_0.9s_infinite] [animation-delay:0.5s]"></div>
                          </div>
                          <p className="text-sm text-[#7C3AED] animate-pulse font-medium">
                            Loading messages...
                          </p>
                        </div>
                      </div>
                    }
                  >
                    <Chat
                      messages={messages}
                      input={input}
                      handleInputChange={handleInputChange}
                      handleSubmit={handleSubmit}
                      isGenerating={isLoading}
                      showTypingIndicator={showTypingIndicator}
                      append={handleAppend}
                      className="h-full flex flex-col"
                      liveToolStatusMessage={liveToolStatusMessage}
                    />
                  </Suspense>
                </div>
              </div>
            )}
          </Theme>
        </SheetContent>
      </Sheet>
    </QueryClientProvider>
  );
}
