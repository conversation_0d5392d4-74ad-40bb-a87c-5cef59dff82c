import Then<PERSON><PERSON>oader from "@/components/thena-loader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { cn } from '@/lib/utils'; 
import { AccountAttributeOption } from "@/types/account";
import { CalendarFold, Clock, MapPin, Plus, X } from 'lucide-react'; 
import * as HeroIcons from "@heroicons/react/24/solid"; 
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { User as LucideUser } from "lucide-react"; 
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import Image from "next/image";
import { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Define form schema - only validate title on submit
const activityFormSchema = z.object({
  title: z.string(),
  // Remove validation from these fields to prevent dropdown issues
  type: z.string(),
  status: z.string(),
  description: z.string().optional(),
  activityTimestamp: z.string(),
  duration: z.any(), // Use z.any() to accept any value including undefined
  location: z.string().optional(),
  assigneeId: z.string().optional(),
  // We'll handle participants separately from the form
});

type ActivityFormSchema = z.infer<typeof activityFormSchema>;

// Define User interface
interface User {
  id: string;
  name: string;
  email: string;
  userType: string;
  status: string;
  isActive: boolean;
  avatarUrl: string | null;
  timezone: string;
  metadata: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

// Define Activity interface
interface ActivityType {
  id: string;
  title: string;
  description?: string;
  type: string;
  typeId?: string;
  status: string;
  statusId?: string;
  activityTimestamp?: string;
  duration?: number;
  location?: string;
  assigneeId?: string;
  participants?: string[]; // Add participants array
  createdAt: string;
  updatedAt: string;
}

// Define component props
interface InlineActivityEditorProps {
  accountId: string;
  onClose: () => void;
  onActivityCreated: () => void;
  statusAttributes?: AccountAttributeOption[];
  typeAttributes?: AccountAttributeOption[];
  users?: User[];
   
  defaultActivityStatus?: {
    value: string;
    id: string;
    attributeId: string;
  } | null;
  currentUser?: User;
  activity?: ActivityType;
  mode?: 'create' | 'edit';
}

// Helper function to get Hero icon
const getHeroIcon = (
  iconName: string | undefined,
  className: string = "h-[16px] w-[16px]",
  color?: string
) => {
  const style = color ? { color: color } : {};

  if (!iconName) return <HeroIcons.ClockIcon className={className} style={style} />;

  try {
    const iconKey = iconName.endsWith("Icon") ? iconName : `${iconName}Icon`;

    const IconComponent = HeroIcons[iconKey as keyof typeof HeroIcons];

    if (IconComponent) {
      return <IconComponent className={className} style={style} />;
    } else {
      if (iconName.toLowerCase().includes("check")) {
        return <HeroIcons.CheckIcon className={className} style={style} />;
      } else if (iconName.toLowerCase().includes("clock")) {
        return <HeroIcons.ClockIcon className={className} style={style} />;
      } else if (iconName.toLowerCase().includes("exclamation")) {
        return <HeroIcons.ExclamationTriangleIcon className={className} style={style} />;
      } else if (iconName.toLowerCase().includes("x")) {
        return <HeroIcons.XCircleIcon className={className} style={style} />;
      } else {
        return <HeroIcons.ClockIcon className={className} style={style} />;
      }
    }
  } catch (error) {
    console.error("Error getting hero icon:", error);
    return <HeroIcons.QuestionMarkCircleIcon className={className} style={style} />;
  }
};

// Helper function to get attribute by ID
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getAttribute = (attributes: AccountAttributeOption[] = [], id: string) => {
  return attributes.find(attr => attr.id === id);
};

// Helper function to get attribute value (name)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getAttributeValue = (attribute: AccountAttributeOption | undefined) => {
  return attribute?.name || "";
};

// Helper function to get attribute color
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getAttributeColor = (attribute: AccountAttributeOption | undefined) => {
  return attribute?.color || "#3b82f6";
};

// Helper function to get attribute icon
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getAttributeIcon = (attribute: AccountAttributeOption | undefined) => {
  return attribute?.icon || undefined;
};

const InlineActivityEditor: React.FC<InlineActivityEditorProps> = ({
  accountId,
  onClose,
  onActivityCreated,
  statusAttributes = [],
  typeAttributes = [],
  users: passedUsers = [],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  defaultActivityStatus,
  currentUser,
  activity,
  mode = 'create'
}) => {
  const isEditMode = mode === 'edit';
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectKey, setSelectKey] = useState(Date.now()); // Used to force re-render Select
  const [orgMembers, setOrgMembers] = useState<User[]>([]);
  const [isLoadingMembers, setIsLoadingMembers] = useState(false);
  
  // Find a valid current user from props
  const validCurrentUser = useMemo(() => {
    if (!currentUser) return null;
    
    // If currentUser has all required fields, use it directly
    if (currentUser.id && currentUser.name) {
      return currentUser;
    }
    
    // Otherwise, try to find the user in passedUsers
    if (currentUser.id) {
      return passedUsers.find(u => u.id === currentUser.id) || null;
    }
    
    return null;
  }, [currentUser, passedUsers]);
  
  // Initialize participants state - empty initially
  const [selectedParticipants, setSelectedParticipants] = useState<User[]>([]);
  
  // Effect to set current user as participant once we have valid data
  useEffect(() => {
    if (validCurrentUser && !selectedParticipants.some(p => p.id === validCurrentUser.id)) {
      setSelectedParticipants(prev => {
        // Don't add duplicates
        if (prev.some(p => p.id === validCurrentUser.id)) {
          return prev;
        }
        return [validCurrentUser, ...prev];
      });
    }
  }, [validCurrentUser, selectedParticipants]);

  // Refs for inputs and measurement spans
  const locationInputRef = useRef<HTMLInputElement>(null);
  const locationMeasureRef = useRef<HTMLSpanElement>(null);
  const durationInputRef = useRef<HTMLInputElement>(null);
  const durationMeasureRef = useRef<HTMLSpanElement>(null);

  // --- Calculate Default Form Values ---
  const defaultActivityType = useMemo(
    () => typeAttributes?.find((attr) => attr.isDefault),
    [typeAttributes]
  );
  const calculatedDefaultStatus = useMemo(
    () => statusAttributes?.find((attr) => attr.isDefault),
    [statusAttributes]
  );

  // Find attribute IDs based on names for edit mode default values
  const initialTypeId = useMemo(() => {
    if (mode === 'edit' && activity?.type && typeAttributes) {
      return typeAttributes.find(attr => attr.name?.toLowerCase() === activity.type?.toLowerCase())?.id || defaultActivityType?.id || "";
    }
    return defaultActivityType?.id || "";
  }, [mode, activity, typeAttributes, defaultActivityType]);

  const initialStatusId = useMemo(() => {
    if (mode === 'edit' && activity?.status && statusAttributes) {
      // Find status by name, fallback to calculated default, fallback to empty string
      return statusAttributes.find(attr => attr.name?.toLowerCase() === activity.status?.toLowerCase())?.id || calculatedDefaultStatus?.id || "";
    }
    // Create mode: Use calculated default, fallback to empty string
    return calculatedDefaultStatus?.id || "";
  }, [mode, activity, statusAttributes, calculatedDefaultStatus]);

  const initialAssigneeId = useMemo(() => {
    if (mode === 'edit' && activity?.assigneeId) {
      return activity.assigneeId;
    }
    if (mode === 'create' && validCurrentUser?.id) {
      return validCurrentUser.id; // Default to current user for creation
    }
    return ""; // No default assignee otherwise
  }, [mode, activity, validCurrentUser]);

  // Calculate initial timestamp ensuring correct format
  const initialTimestamp = useMemo(() => {
    if (mode === 'edit' && activity?.activityTimestamp) {
      try {
        // Attempt to parse and format existing timestamp
        return format(new Date(activity.activityTimestamp), "yyyy-MM-dd'T'HH:mm");
      } catch (e) {
        console.error("Error formatting timestamp:", e);
        // Fallback to current time if formatting fails
        return format(new Date(), "yyyy-MM-dd'T'HH:mm"); // Fallback to now
      }
    }
    // Default to current time for create mode
    return format(new Date(), "yyyy-MM-dd'T'HH:mm"); // Default to now for create
  }, [mode, activity]);

  const form = useForm<ActivityFormSchema>({
    resolver: zodResolver(activityFormSchema),
    defaultValues: {
      // Set initial values based on edit mode (activity prop) or defaults for create mode
      title: activity?.title || "",
      description: activity?.description || "",
      type: initialTypeId,
      status: initialStatusId,
      activityTimestamp: initialTimestamp,
      duration: mode === 'edit' ? activity?.duration : undefined, // Only set duration if editing
      location: activity?.location || "",
      assigneeId: initialAssigneeId,
    },
    // Only validate on submit, not on field change or blur
    mode: "onSubmit"
  });

  // Fetch organization members
  useEffect(() => {
    const fetchOrgMembers = async () => {
      setIsLoadingMembers(true);
      try {
        const response = await fetch('/api/organizations/members');
        if (!response.ok) {
          throw new Error('Failed to fetch organization members');
        }
        const data = await response.json();
        setOrgMembers(data.members || []);
        
        // For edit mode, use the existing participants
        if (isEditMode && activity?.participants?.length) {
          const initialParticipants = activity.participants
            .map(participantId => {
              return data.members.find((member: User) => member.id === participantId);
            })
            .filter(Boolean);
            
          // Make sure current user is included
          if (validCurrentUser && !initialParticipants.some(p => p.id === validCurrentUser.id)) {
            const currentUserInMembers = data.members.find((member: User) => member.id === validCurrentUser.id);
            if (currentUserInMembers) {
              initialParticipants.push(currentUserInMembers);
            }
          }
          
          setSelectedParticipants(initialParticipants);
        } else if (validCurrentUser) {
          // For create mode, find the current user in the members list to ensure we have complete data
          const currentUserInMembers = data.members.find((member: User) => member.id === validCurrentUser.id);
          if (currentUserInMembers) {
            setSelectedParticipants([currentUserInMembers]);
          }
        }
      } catch (error) {
        console.error('Error fetching org members:', error);
        toast.error('Failed to load organization members');
      } finally {
        setIsLoadingMembers(false);
      }
    };

    fetchOrgMembers();
  }, [isEditMode, activity, validCurrentUser]);

  // Log participants whenever they change
  useEffect(() => {
    console.log("Selected participants updated:", selectedParticipants);
  }, [selectedParticipants]);

  // Function to remove a participant
  const removeParticipant = (userId: string) => {
    // Don't allow removing the current user (creator)
    if (validCurrentUser && userId === validCurrentUser.id) {
      return;
    }
    setSelectedParticipants(prev => prev.filter(p => p.id !== userId));
  };

  // Filter out already selected participants
  const availableParticipants = useMemo(() => {
    return orgMembers.filter(
      member => !selectedParticipants.some(p => p.id === member.id)
    );
  }, [orgMembers, selectedParticipants]);

  const onSubmit = async (values: ActivityFormSchema) => {
    // Validate required fields manually
    if (!values.title.trim()) {
      toast.error("Title is required");
      return;
    }

    if (!values.type) {
      toast.error("Type is required");
      return;
    }

    if (!values.status) {
      toast.error("Status is required");
      return;
    }

    setIsSubmitting(true);
    const toastId = toast.loading(isEditMode ? "Updating activity..." : "Creating activity...");

    try {
      // Find the full attribute objects based on selected IDs
      const selectedType = typeAttributes.find(attr => attr.id === values.type);
      const selectedStatus = statusAttributes.find(attr => attr.id === values.status);

      // Start with base payload
      const payload: Record<string, unknown> = {
        title: values.title,
        description: values.description || "", // Keep description, even if empty
        type: selectedType?.name || undefined, // Send name, use key 'type'
        status: selectedStatus?.name || undefined, // Send name, use key 'status'
        activityTimestamp: values.activityTimestamp
          ? new Date(values.activityTimestamp).toISOString() // Ensure ISO format
          : new Date().toISOString(), // Send current time if picker somehow fails
        // accountId will be added by the API route
      };

      // Conditionally add assigneeId
      if (values.assigneeId) {
        payload.assigneeId = values.assigneeId;
      }

      // Add participants
      if (selectedParticipants.length > 0) {
        payload.participants = selectedParticipants.map(p => p.id);
      }

      // Conditionally add optional fields
      if (values.duration && values.duration > 0) {
        payload.duration = values.duration;
      }
      if (values.location && values.location.trim() !== "") {
        payload.location = values.location;
      }

      const endpoint = isEditMode
        ? `/api/accounts//activities/${activity?.id}`
        : `/api/accounts/${accountId}/activities`;

      const method = isEditMode ? "PUT" : "POST";

      const response = await fetch(endpoint, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${isEditMode ? "update" : "create"} activity`);
      }

      toast.success(
        isEditMode ? "Activity updated successfully" : "Activity created successfully",
        { id: toastId }
      );

      onActivityCreated();

      onClose();
    } catch (error) {
      console.error("Error submitting activity:", error);
      toast.error(
        `Failed to ${isEditMode ? "update" : "create"} activity. Please try again.`,
        { id: toastId }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // useEffect to set default values only in create mode
  useEffect(() => {
    // Only set defaults if NOT in edit mode
    if (!isEditMode) {
      const defaultStatus = statusAttributes.find((attr) => attr.isDefault);
      const defaultType = typeAttributes.find((attr) => attr.isDefault);

      // Use setTimeout to ensure setValue happens after initial render potentially finishes
      setTimeout(() => {
        const currentStatus = form.getValues("status");
        const currentType = form.getValues("type");

        // Only set if the field doesn't already have a value AND a default exists
        if (defaultStatus?.id && !currentStatus) {
          form.setValue("status", defaultStatus.id, { shouldValidate: false });
          setSelectKey(prev => prev + 1); // Increment key only if value set
        }
        if (defaultType?.id && !currentType) {
          form.setValue("type", defaultType.id, { shouldValidate: false });
          setSelectKey(prev => prev + 1); // Increment key only if value set
        }
      }, 0); // Delay of 0ms pushes to end of event loop tick
    }
  }, [statusAttributes, typeAttributes, form, isEditMode]); // Add isEditMode

  // Watch form values for reactive updates in UI
  const watchedLocation = form.watch('location');
  const watchedAssigneeId = form.watch("assigneeId");
  const watchedDuration = form.watch("duration");

  // Effect for Location width
  useEffect(() => {
    if (locationMeasureRef.current && locationInputRef.current) {
      const input = locationInputRef.current;
      const measure = locationMeasureRef.current;

      // Get computed styles from the input
      const styles = window.getComputedStyle(input);
      // Apply relevant font styles to the measurement span
      measure.style.fontSize = styles.fontSize;
      measure.style.fontFamily = styles.fontFamily;
      measure.style.fontWeight = styles.fontWeight;
      measure.style.letterSpacing = styles.letterSpacing;
      measure.style.paddingLeft = styles.paddingLeft;
      measure.style.paddingRight = styles.paddingRight;
      measure.style.borderLeftWidth = styles.borderLeftWidth;
      measure.style.borderRightWidth = styles.borderRightWidth;
      measure.style.boxSizing = styles.boxSizing;

      const text = input.value || input.placeholder || '';
      measure.textContent = text;
      // Use scrollWidth
      const width = measure.scrollWidth;
      const finalWidth = width + 2;
      // Use a small buffer (e.g., 2px)
      input.style.width = `${finalWidth}px`;
    }
  }, [watchedLocation]); // Trigger on value change

  // Effect for Duration width
  useEffect(() => {
    if (durationMeasureRef.current && durationInputRef.current) {
      const input = durationInputRef.current;
      const measure = durationMeasureRef.current;

      // Get computed styles from the input
      const styles = window.getComputedStyle(input);
      // Apply relevant font styles to the measurement span
      measure.style.fontSize = styles.fontSize;
      measure.style.fontFamily = styles.fontFamily;
      measure.style.fontWeight = styles.fontWeight;
      measure.style.letterSpacing = styles.letterSpacing;
      measure.style.paddingLeft = styles.paddingLeft;
      measure.style.paddingRight = styles.paddingRight;
      measure.style.borderLeftWidth = styles.borderLeftWidth;
      measure.style.borderRightWidth = styles.borderRightWidth;
      measure.style.boxSizing = styles.boxSizing;

      // For number inputs, ensure we have at least enough space for the placeholder
      const text = input.value || input.placeholder || '';
      measure.textContent = text;
      
      // Use scrollWidth with minimum width
      const width = Math.max(measure.scrollWidth, 40); // Ensure at least 40px width
      const finalWidth = width + 8; // Add more buffer for numbers
      
      // Set minimum and maximum width constraints
      const constrainedWidth = Math.min(Math.max(finalWidth, 40), 100);
      input.style.width = `${constrainedWidth}px`;
    }
  }, [watchedDuration]); // Trigger on duration value change

  // Function to add a participant
  const addParticipant = (user: User) => {
    if (!selectedParticipants.some(p => p.id === user.id)) {
      setSelectedParticipants(prev => [...prev, user]);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const selectedAssignee = useMemo(() => {
    if (!watchedAssigneeId) return null;
    return passedUsers.find(u => u.id === watchedAssigneeId);
  }, [watchedAssigneeId, passedUsers]);

  return (
    <Form {...form}>
      {isSubmitting && (
        <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-10">
          <ThenaLoader />
        </div>
      )}
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="w-full border border-gray-200 dark:border-gray-700 rounded-sm shadow-sm bg-white dark:bg-gray-800"
      >
        <div className="space-y-2 p-4">
          {/* Title and Description */}
          <div className="space-y-2">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="space-y-0">
                  <FormControl>
                    <Input
                      placeholder="Activity title"
                      {...field}
                      className="border-none h-7 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 text-base font-medium dark:bg-gray-800"
                      autoFocus
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem className="space-y-0">
                  <FormControl>
                    <Textarea
                      placeholder="Add description..."
                      {...field}
                      className="resize-none min-h-[60px] max-h-[200px] text-sm border-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 dark:bg-gray-800 dark:text-gray-200 overflow-y-auto"
                      rows={2}
                      onInput={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.height = 'auto';
                        const newHeight = Math.min(target.scrollHeight, 200);
                        target.style.height = `${newHeight}px`;
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Participants */}
          <div className="flex items-center gap-1 flex-wrap">
            <div className="text-sm text-muted-foreground whitespace-nowrap">Participants:</div>
            
            {/* Display selected participants */}
            <div className="flex flex-wrap gap-1">
              {selectedParticipants.map(participant => {
                const isCreator = validCurrentUser && participant.id === validCurrentUser.id;
                return (
                  <Badge 
                    key={participant.id} 
                    variant="outline"
                    className={`flex items-center gap-1 py-0.5 pl-0.5 pr-1.5 h-6 ${isCreator ? 'bg-primary/10' : ''}`}
                  >
                    <Avatar className="h-4 w-4 mr-1">
                      {participant.avatarUrl ? (
                        <AvatarImage src={participant.avatarUrl} alt={participant.name} />
                      ) : (
                        <AvatarFallback className="text-[8px]">
                          {participant.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <span className="text-xs font-medium">{participant.name}</span>
                    {!isCreator && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-3 w-3 rounded-full p-0 ml-0.5"
                        onClick={() => removeParticipant(participant.id)}
                      >
                        <X className="h-2 w-2" />
                      </Button>
                    )}
                  </Badge>
                );
              })}
            </div>
            
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-6 text-xs px-2 flex items-center gap-1 text-muted-foreground hover:text-foreground"
                  disabled={isLoadingMembers || availableParticipants.length === 0}
                >
                  <Plus className="h-3 w-3" />
                  <span>Add</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-60 p-0" align="start">
                <div className="max-h-60 overflow-auto py-1">
                  {isLoadingMembers ? (
                    <div className="px-3 py-4 flex justify-center">
                      <ThenaLoader />
                    </div>
                  ) : availableParticipants.length > 0 ? (
                    availableParticipants.map(member => (
                      <div
                        key={member.id}
                        className="flex items-center gap-2 px-3 py-2 hover:bg-accent cursor-pointer"
                        onClick={() => {
                          addParticipant(member);
                        }}
                      >
                        <Avatar className="h-6 w-6">
                          {member.avatarUrl ? (
                            <AvatarImage src={member.avatarUrl} alt={member.name} />
                          ) : (
                            <AvatarFallback className="text-xs">
                              {member.name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div className="flex flex-col">
                          <span className="text-sm font-medium">{member.name}</span>
                          <span className="text-xs text-muted-foreground">{member.email}</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="px-3 py-2 text-sm text-muted-foreground">
                      All members have been added
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
            
            <div className="flex-1"></div>
          </div>

          {/* Activity Details Row */}
          <div className="mt-6 pt-2">
            <div className="flex items-center space-x-2 text-sm">
              {/* Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => {
                  const currentAttribute = typeAttributes.find(attr => attr.id === field.value);
                  return (
                    <FormItem className="flex-none">
                      <Select key={`type-${selectKey}`} onValueChange={field.onChange} value={field.value || ""}>
                        <FormControl>
                          <SelectTrigger className="h-7 text-xs px-2 min-w-0 w-auto">
                            <span className="flex items-center gap-1.5">
                              {currentAttribute ? getHeroIcon(currentAttribute.icon, "h-3.5 w-3.5", currentAttribute.color) : null}
                              <span>{currentAttribute?.name || "Type"}</span>
                            </span>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {typeAttributes.map((attr) => (
                            <SelectItem key={attr.id} value={attr.id}>
                              <div className="flex items-center gap-2">
                                {getHeroIcon(attr.icon, "h-4 w-4", attr.color)}
                                <span>{attr.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  );
                }}
              />

              {/* Status */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => {
                  const currentAttribute = statusAttributes.find(attr => attr.id === field.value);
                  return (
                    <FormItem className="flex-none">
                      <Select key={`status-${selectKey}`} onValueChange={field.onChange} value={field.value || ""}>
                        <FormControl>
                          <SelectTrigger className="h-7 text-xs px-2 min-w-0 w-auto">
                            <span className="flex items-center gap-1.5">
                              {currentAttribute ? getHeroIcon(currentAttribute.icon, "h-3.5 w-3.5", currentAttribute.color) : null}
                              <span>{currentAttribute?.name || "Status"}</span>
                            </span>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {statusAttributes.map((attr) => (
                            <SelectItem key={attr.id} value={attr.id}>
                              <div className="flex items-center gap-2">
                                {getHeroIcon(attr.icon, "h-4 w-4", attr.color)}
                                <span>{attr.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  );
                }}
              />

              {/* Date */}
              <FormField
                control={form.control}
                name="activityTimestamp"
                render={({ field }) => (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "h-7 px-2 w-auto justify-start text-left font-normal text-xs",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        <CalendarFold className="h-3.5 w-3.5 text-muted-foreground" />
                        {field.value ? format(new Date(field.value), "MMM d") : <span>Date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={(date) => {
                          const currentVal = field.value ? new Date(field.value) : new Date();
                          const newDate = date || new Date(); // Use selected date or today if none
                          // Combine selected date with current time (or start of day if no current time)
                          const newDateTime = new Date(
                            newDate.getFullYear(),
                            newDate.getMonth(),
                            newDate.getDate(),
                            currentVal.getHours(),
                            currentVal.getMinutes()
                          );
                          field.onChange(format(newDateTime, "yyyy-MM-dd'T'HH:mm"));
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                )}
              />

              {/* Duration */}
              <div className="flex items-center h-7 px-2 rounded-sm border border-input bg-background text-xs">
                <Clock className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                <FormField
                  control={form.control}
                  name="duration"
                  render={({ field: { onChange, value, ...rest } }) => (
                    <FormItem className="flex-none m-0 p-0">
                      <FormControl>
                        <Input
                          ref={durationInputRef}
                          type="number"
                          placeholder="Minutes"
                          {...rest}
                          value={value === undefined || value === null ? '' : value}
                          onChange={(e) => {
                            const val = e.target.value;
                            if (val === '') {
                              onChange(undefined);
                            } else {
                              const num = parseInt(val, 10);
                              if (!isNaN(num)) {
                                onChange(num);
                              }
                            }
                          }}
                          className="h-full border-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 text-xs bg-transparent"
                          style={{ appearance: 'textfield' }} // Added style for spinners
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* Location */}
              <div className="flex items-center h-7 px-2 rounded-sm border border-input bg-background text-xs">
                <MapPin className="h-3.5 w-3.5 mr-1.5 text-muted-foreground" />
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem className="flex-none m-0 p-0">
                      <FormControl>
                        <Input
                          ref={locationInputRef}
                          placeholder="Location"
                          {...field}
                          className="h-full border-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 text-xs bg-transparent"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end items-center pt-4 px-4 pb-4 border-t border-gray-200 dark:border-gray-700 space-x-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-7 px-3 text-sm"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            size="sm"
            disabled={isSubmitting}
            className="h-7 px-4 text-sm bg-primary text-primary-foreground hover:bg-primary/90"
          >
            {isSubmitting ? (
              <div className="h-4 w-4">
                <ThenaLoader className="scale-50" />
              </div>
            ) : (
              isEditMode ? "Save changes" : "Create activity"
            )}
          </Button>
        </div>
      </form>
      {/* Hidden spans for measurement - Apply same text styles as inputs */}
      <span
        ref={locationMeasureRef}
        // Match relevant input styles for accurate font rendering
        className="absolute invisible whitespace-nowrap h-full border-none p-0 text-xs bg-transparent"
        style={{ left: '-9999px', top: '-9999px' }}
      ></span>
      <span
        ref={durationMeasureRef}
        // Match relevant input styles for accurate font rendering
        className="absolute invisible whitespace-nowrap h-full border-none p-0 text-xs bg-transparent"
        style={{ left: '-9999px', top: '-9999px' }}
      ></span>
    </Form>
  );
};

export default InlineActivityEditor;
