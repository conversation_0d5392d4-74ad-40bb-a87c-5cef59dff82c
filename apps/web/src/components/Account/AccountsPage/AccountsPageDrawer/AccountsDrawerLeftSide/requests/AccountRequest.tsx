import Avatar from "@/components/common/Avatar";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import * as HeroIcons from "@heroicons/react/24/solid";
import { format, formatDistanceToNowStrict } from "date-fns";
import {
  AlertTriangle,
  CircleFadingPlus,
  Info,
  Lock,
  Mail,
  MessageSquare,
  Network,
  PlugZap,
  Search,
  Slack,
  Ticket,
  X,
} from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
// Only import the type we need
import MsTeamsLogo from "@/assets/SvgComponents/MsTeamsLogo";
import Then<PERSON><PERSON>ogo from "@/assets/SvgComponents/ThenaLogo";
import { CreateTicketDialog } from "@/components/create-ticket-dialog";
import type { HeroIconName } from "@/components/hero-icon";
import ThenaLoader from "@/components/thena-loader";
import { Badge } from "@/components/ui/badge";
import { useApi } from "@/hooks/use-api";
import useOrderedKanbanColumns from "@/hooks/use-ordered-kanban-columns";
import { GET_ALL_ACCOUNT_TICKETS } from "@/services/kanban";
import { AccountTicket } from "@/types/account";
import { Status, ThenaTicket } from "@/types/kanban";
import { getInitials, getLaneIcon, getUrgencyIcon } from "@/utils/kanban";
import { ListSkeleton } from "../../shared/LoadingStates";

import { useTicketMetaStore } from "@/store/ticket-meta-store";

type Tprops = {
  accountId: string;
  requests: AccountTicket[];
  statuses?: Status[];
};

const AccountRequest = ({ accountId, requests, statuses = [] }: Tprops) => {
  const [filteredRequests, setFilteredRequests] = useState<AccountTicket[]>(
    requests || [],
  );
  const [isLoading, setIsLoading] = useState(
    !requests || requests.length === 0,
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [showCreateRequest, setShowCreateRequest] = useState(false);
  const [showSearchInput, setShowSearchInput] = useState(false);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch requests if not provided or when refresh is triggered
  const {
    data: fetchedRequests,
    loading: apiLoading,
    refetch: _refetch,
  } = useApi<AccountTicket[]>(
    GET_ALL_ACCOUNT_TICKETS(accountId),
    {},
    {
      enabled: !requests || requests.length === 0 || refreshTrigger > 0,
      isNextApi: true,
    },
  );

  // Locally normalize statuses to include parent_status object
  const normalizedStatuses = useMemo(() => {
    return statuses.map((status) => {
      if (status.parent_status_id) {
        const parent = statuses.find((s) => s.id === status.parent_status_id);
        return {
          ...status,
          parent_status: parent ? { name: parent.name, uid: parent.uid } : null,
        };
      }
      return { ...status, parent_status: null };
    });
  }, [statuses]);

  // Get ordered statuses for proper ordering
  const orderedStatuses = useOrderedKanbanColumns();

  // Update loading state when API call completes
  useEffect(() => {
    if (apiLoading !== undefined) {
      setIsLoading(apiLoading);
    } else if (requests?.length > 0) {
      // If we have initial requests, we're not loading
      setIsLoading(false);
    }
  }, [apiLoading, requests]);

  // Use provided requests or fetched requests
  const displayRequests = useMemo(() => {
    return requests?.length > 0 ? requests : fetchedRequests || [];
  }, [requests, fetchedRequests]);

  // Get unique teams from requests
  const teams = useMemo(() => {
    const uniqueTeams = new Map();
    displayRequests.forEach((request) => {
      if (request.team && request.team.uid) {
        uniqueTeams.set(request.team.uid, {
          uid: request.team.uid,
          name: request.team.name,
          identifier: request.team.identifier,
          color: request.team.color,
          icon: request.team.icon,
        });
      }
    });
    return Array.from(uniqueTeams.values());
  }, [displayRequests]);

  // Update filtered requests when requests change
  useEffect(() => {
    if (displayRequests.length > 0) {
      setFilteredRequests(displayRequests);
    }
  }, [displayRequests]);

  // Filter requests based on search query and selected teams
  useEffect(() => {
    if (!displayRequests) return;

    let filtered = displayRequests;

    // Apply team filter if any teams are selected
    if (selectedTeams.length > 0) {
      filtered = filtered.filter(
        (request) => request.team && selectedTeams.includes(request.team.uid),
      );
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (request) =>
          request.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (request.description &&
            request.description
              .toLowerCase()
              .includes(searchQuery.toLowerCase())),
      );
    }

    setFilteredRequests(filtered);
  }, [displayRequests, searchQuery, selectedTeams]);

  // Group requests by team
  const requestsByTeam = useMemo(() => {
    const grouped: Record<
      string,
      {
        team: {
          uid: string;
          name: string;
          identifier: string;
          color?: string;
          icon?: string;
        };
        requests: AccountTicket[];
      }
    > = {};

    // (removed – variables were never used)

    // Second pass: group tickets by the appropriate team
    filteredRequests.forEach((request) => {
      if (request.team) {
        // Determine if this team is a group and has a parent team with complete data
        const hasParentTeam =
          !!request.team.parent_team && !!request.team.parent_team.uid;

        // Use the parent team UID if available, otherwise use this team's UID
        const teamId = hasParentTeam
          ? request.team.parent_team.uid
          : request.team.uid;

        // Use parent team data if available, otherwise use the team's own data
        const teamToUse = hasParentTeam
          ? request.team.parent_team
          : request.team;

        // Initialize the team entry if it doesn't exist yet
        if (!grouped[teamId]) {
          grouped[teamId] = {
            team: {
              uid: teamToUse.uid,
              name: teamToUse.name,
              identifier: teamToUse.identifier,
              color: teamToUse.color,
              icon: teamToUse.icon,
            },
            requests: [],
          };
        }

        // Add the request to the appropriate team
        grouped[teamId].requests.push(request);
      } else {
        // Handle tickets without a team
        if (!grouped["no-team"]) {
          grouped["no-team"] = {
            team: {
              uid: "no-team",
              name: "No Team",
              identifier: "TKT",
              color: "#6A00FF",
              icon: "TicketIcon",
            },
            requests: [],
          };
        }
        grouped["no-team"].requests.push(request);
      }
    });

    return grouped;
  }, [filteredRequests]);

  // Function to get the appropriate source icon based on the source string
  const getSourceIcon = (source?: string) => {
    if (!source) return <Slack className="h-3.5 w-3.5" />;

    switch (source.toLowerCase()) {
      case "slack":
        return <Slack className="h-3.5 w-3.5" />;
      case "email":
        return <Mail className="h-3.5 w-3.5" />;
      case "web":
        return <MessageSquare className="h-3.5 w-3.5" />;
      case "api":
        return <PlugZap className="h-3.5 w-3.5" />;
      case "manual":
        return <ThenaLogo />;
      case "ms-teams":
        return <MsTeamsLogo />;
      default:
        return <Slack className="h-3.5 w-3.5" />;
    }
  };

  // Get a valid HeroIconName from string or use fallback
  const getIconName = (iconStr?: string): HeroIconName => {
    // Check if the icon is undefined, null, or empty string
    if (!iconStr || iconStr.trim() === "") {
      return "RocketLaunchIcon";
    }

    // Check if the icon string already ends with "Icon"
    const iconName = iconStr.endsWith("Icon") ? iconStr : `${iconStr}Icon`;

    // Check if it's a valid HeroIconName by checking if it exists in HeroIcons
    return iconName in HeroIcons
      ? (iconName as HeroIconName)
      : "RocketLaunchIcon";
  };

  // Create a memoized map of status names to their parent status names for efficient lookups
  const statusNameToParentMap = useMemo(() => {
    const map = new Map<string, string>();

    normalizedStatuses.forEach((status) => {
      // Store both regular and lowercase versions for case-insensitive lookups
      const keys = [
        status.name,
        status.name?.toLowerCase(),
        status.display_name,
        status.display_name?.toLowerCase(),
      ].filter(Boolean) as string[];

      // Determine the parent status name
      let parentName: string;

      if (status.parent_status) {
        parentName = status.parent_status.name.toUpperCase();
      } else if (status.parent_status_id) {
        const parentStatus = normalizedStatuses.find(
          (s) => s.id === status.parent_status_id,
        );
        parentName = parentStatus
          ? parentStatus.name.toUpperCase()
          : status.name.toUpperCase();
      } else {
        parentName = status.name.toUpperCase();
      }

      // Add all variations of the status name to the map
      keys.forEach((key) => {
        if (key) map.set(key, parentName);
      });
    });

    return map;
  }, [normalizedStatuses]);

  // Helper function to get the correct status icon for a ticket
  const getStatusIcon = useMemo(() => {
    // Return a function that uses the memoized map
    const StatusIconComponent = (statusName: string) => {
      // Defensive: if statusName is falsy, fallback to a default icon
      if (!statusName) return getLaneIcon("DEFAULT", "16px");

      // Get the parent status name from our map (much faster than multiple .find() calls)
      const determinedStatusName =
        statusNameToParentMap.get(statusName) ||
        statusNameToParentMap.get(statusName.toLowerCase()) ||
        statusName.toUpperCase();

      // Call the original getLaneIcon
      const iconElement = getLaneIcon(determinedStatusName, "16px");

      // Check if the returned element is the text fallback (span)
      // If so, return a generic Info icon instead
      if (
        iconElement &&
        typeof iconElement === "object" &&
        "type" in iconElement &&
        iconElement.type === "span"
      ) {
        return (
          <Info
            style={{
              height: "16px",
              width: "16px",
              minWidth: "16px",
              minHeight: "16px",
            }}
            className="text-muted-foreground"
          />
        );
      }

      // Otherwise, return the icon provided by getLaneIcon
      return iconElement;
    };

    // Add display name to fix the ESLint error
    StatusIconComponent.displayName = "StatusIconComponent";

    return StatusIconComponent;
  }, [statusNameToParentMap]);

  // Team header component for accordion
  const TeamHeader = ({
    team,
    count,
  }: {
    team: {
      uid: string;
      name: string;
      identifier: string;
      color?: string;
      icon?: string;
    };
    count: number;
  }) => {
    // Get valid icon name
    const iconName = getIconName(team?.icon);

    // Create a custom icon component with the exact color from API
    const IconComponent = () => {
      const Icon = HeroIcons[iconName];
      return (
        <div className="shrink-0 size-5 flex items-center justify-center">
          <Icon
            className="h-5 w-5"
            style={{ color: team?.color || "#6A00FF" }}
          />
        </div>
      );
    };

    // Add display name to fix the ESLint error
    IconComponent.displayName = "TeamIconComponent";

    return (
      <div className="flex items-center gap-2 w-full">
        <IconComponent />
        <div className="flex items-center">
          <span className="text-sm font-medium">
            {team?.name || "Unknown Team"}
          </span>
          <span className="text-xs text-muted-foreground ml-1">{count}</span>
        </div>
      </div>
    );
  };

  // Status header row component - simplified to match Linear style
  const StatusSectionHeader = ({
    title,
    count,
    statusKey,
  }: {
    title: string;
    count: number;
    statusKey: string;
  }) => {
    // Use getStatusIcon helper for icon logic
    const iconElement = getStatusIcon(statusKey);

    return (
      <div className="flex items-center py-3 px-4 border-b border-border/50 bg-color-bg-subtle">
        <div className="flex items-center gap-1.5">
          {iconElement}
          <span className="text-sm font-medium">{title}</span>
          <span className="text-xs text-muted-foreground ml-0.5">{count}</span>
        </div>
      </div>
    );
  };

  // Individual request item
  const RequestItem = ({
    request,
    _index,
  }: {
    request: AccountTicket;
    _index: number;
  }) => {
    const teamMembers = useTicketMetaStore((state) => state.teamMembers);
    const [checked, setChecked] = useState(false);

    const hasAttachments =
      request.description?.includes("[attachment]") || false;

    // Use getStatusIcon helper for icon logic
    const statusIcon = getStatusIcon(request.status?.name || "");

    let assigneeAvatarUrl: string | null | undefined = undefined;
    let assigneeNameForFallback = "";

    if (request.assignee) {
      assigneeNameForFallback = request.assignee.name || ""; // Ensure name is not null

      if (request.assignee.avatar_url) {
        // Prioritize direct avatar_url from ticket
        assigneeAvatarUrl = request.assignee.avatar_url;
      } else if (request.assignee.uid) {
        // Fallback to teamMembers if UID exists but no direct URL
        const agent = teamMembers.find(
          (member) => member.uid === request.assignee?.uid,
        );
        if (agent && agent.avatar_url) {
          assigneeAvatarUrl = agent.avatar_url;
        }
      }
    }

    return (
      <div className="flex items-center justify-between py-3 px-4 hover:bg-accent/20 transition-colors cursor-pointer border-b border-border/50">
        {/* Left side with checkbox, priority, ID, and title */}
        <div className="flex items-center gap-2 flex-1">
          {/* Checkbox */}
          <Checkbox
            checked={checked}
            onCheckedChange={(value) => setChecked(value === true)}
            className="h-4 w-4 border-border shadow-none data-[state=checked]:bg-primary"
          />

          {/* Icon button with priority */}
          <div className="text-muted-foreground">
            {getUrgencyIcon(request.priority.name.toUpperCase())}
          </div>

          {/* ID with status icon */}
          <div className="flex items-center gap-1.5 min-w-[80px]">
            {statusIcon}
            <span className="text-sm font-normal">
              {request.team?.parent_team?.identifier ||
                request.team?.identifier ||
                "TKT"}
              -{request.ticket_id}
            </span>
          </div>

          {/* Company logo and title */}
          <div className="flex items-center gap-1.5 flex-1 min-w-0 overflow-hidden max-w-[180px]">
            {/* Title with truncation */}
            <span
              className="text-sm font-medium truncate block max-w-full"
              title={request.title}
            >
              {request.title}
            </span>

            {/* Special indicators for private and escalated tickets */}
            {(request.is_private || request.is_escalated || hasAttachments) && (
              <div className="flex items-center gap-1 flex-shrink-0">
                {request.is_private && (
                  <Lock size={14} className="text-muted-foreground" />
                )}
                {request.is_escalated && (
                  <AlertTriangle className="h-4 w-4 text-warning" />
                )}
                {/* Attachment icon if we determine there are attachments */}
                {hasAttachments && (
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="text-muted-foreground"
                  >
                    <path
                      d="M8 13.3333C5.05448 13.3333 2.66667 10.9455 2.66667 8C2.66667 5.05448 5.05448 2.66667 8 2.66667C10.9455 2.66667 13.3333 5.05448 13.3333 8C13.3333 10.9455 10.9455 13.3333 8 13.3333ZM8 12C10.2091 12 12 10.2091 12 8C12 5.79086 10.2091 4 8 4C5.79086 4 4 5.79086 4 8C4 10.2091 5.79086 12 8 12ZM7.33333 5.33333H8.66667V8.66667H7.33333V5.33333ZM7.33333 10H8.66667V11.3333H7.33333V10Z"
                      fill="currentColor"
                    />
                  </svg>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Right side with timestamp, source, and assignee */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {/* Reply count and time */}
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <span
              className="text-xs text-muted-foreground"
              title={format(new Date(request.updatedAt), "MMM d, yyyy, h:mm a")}
            >
              {formatDistanceToNowStrict(new Date(request.updatedAt), {
                addSuffix: true,
                roundingMethod: "round",
              })}
            </span>
            <span>via</span>
            <span className="flex items-center">
              {getSourceIcon(request.source)}
            </span>
          </div>

          {/* Assignee avatar or add button */}
          {request.assignee &&
          (assigneeAvatarUrl || getInitials(assigneeNameForFallback)) ? (
            <Avatar
              src={assigneeAvatarUrl || undefined}
              alt={assigneeNameForFallback}
              fallbackText={getInitials(assigneeNameForFallback)}
              imgClassnames="h-4 w-4 rounded-md"
              fallbackTextClassnames="h-4 w-4 rounded-md text-xs"
            />
          ) : (
            <CircleFadingPlus className="h-4 w-4 text-muted-foreground cursor-pointer hover:text-primary" />
          )}
        </div>
      </div>
    );
  };

  // Loading skeleton for request items
  if (apiLoading && displayRequests.length === 0) {
    return <ListSkeleton count={2} />;
  }

  // Render a status section with its tickets (dynamic, based on API status names)
  const renderStatusSection = (
    statusName: string,
    teamRequests: AccountTicket[],
  ) => {
    // Find the status object to check if it's a child status
    const statusObj = normalizedStatuses.find((s) => s.name === statusName);
    const isChildStatus = statusObj && statusObj.parent_status_id !== null;

    // For parent statuses with children, only show tickets directly in this status
    // For statuses without children, show all tickets with this status
    const statusRequests = teamRequests.filter((r) => {
      const ticketStatus = r.status?.name || "";

      if (isChildStatus) {
        // For child statuses, only show tickets directly in this status
        return ticketStatus === statusName;
      } else {
        // For parent statuses or regular statuses, show all tickets with this status
        return ticketStatus === statusName;
      }
    });

    // Skip rendering child statuses with no direct tickets
    if (isChildStatus && statusRequests.length === 0) {
      return null;
    }

    // Always render the section, even if there are no tickets
    return (
      <div key={statusName}>
        <StatusSectionHeader
          title={statusName}
          statusKey={statusName}
          count={statusRequests.length}
        />
        {isLoading ? (
          <>
            <ListSkeleton />
            <ListSkeleton />
          </>
        ) : (
          statusRequests.map((request, _index) => (
            <RequestItem key={request.uid} request={request} _index={_index} />
          ))
        )}
      </div>
    );
  };

  // Render team section with accordion (dynamic statuses)
  const renderTeamSection = (teamData: {
    team: {
      uid: string;
      name: string;
      identifier: string;
      color?: string;
      icon?: string;
    };
    requests: AccountTicket[];
  }) => {
    if (teamData.requests.length === 0) return null;

    // Get the statuses in the exact order they appear in the Kanban board
    const statusesInOrder = orderedStatuses.map((status) => ({
      uid: status.uid,
      id: status.id,
      name: status.name,
      display_name: status.display_name || status.name,
      parent_status: status.parent_status,
      parent_status_id: status.parent_status_id,
    }));

    // Create a map of status names to their display order
    const statusNameToOrder: Record<string, number> = {};
    statusesInOrder.forEach((status, index) => {
      statusNameToOrder[status.name] = index;
    });

    // Get all unique status names from the tickets
    const ticketStatusNames = new Set<string>();
    teamData.requests.forEach((ticket) => {
      if (ticket.status && ticket.status.name) {
        ticketStatusNames.add(ticket.status.name);
      }
    });

    // Create an array of status names in the correct order
    const orderedStatusNames = Array.from(ticketStatusNames).sort((a, b) => {
      const orderA =
        statusNameToOrder[a] !== undefined ? statusNameToOrder[a] : 999;
      const orderB =
        statusNameToOrder[b] !== undefined ? statusNameToOrder[b] : 999;
      return orderA - orderB;
    });

    return (
      <AccordionItem
        value={teamData.team.uid}
        key={teamData.team.uid}
        className="border-0"
      >
        <AccordionTrigger
          className="py-3 px-4 border-b border-border/50 hover:no-underline group"
          style={{
            backgroundColor: teamData.team.color
              ? `${teamData.team.color
                  .replace("rgb", "rgba")
                  .replace(")", ", 0.1)")}`
              : "rgba(106, 0, 255, 0.1)",
          }}
        >
          <TeamHeader team={teamData.team} count={teamData.requests.length} />
          <div className="opacity-70 group-hover:opacity-100 transition-opacity ml-1">
            {/* Chevron is automatically added by AccordionTrigger */}
          </div>
        </AccordionTrigger>
        <AccordionContent className="p-0">
          {orderedStatusNames.map((statusName) =>
            renderStatusSection(statusName, teamData.requests),
          )}
        </AccordionContent>
      </AccordionItem>
    );
  };

  // Team filter dropdown items
  const renderTeamFilterItems = () => {
    return teams.map((team) => {
      const isSelected = selectedTeams.includes(team.uid);
      return (
        <DropdownMenuCheckboxItem
          key={team.uid}
          checked={isSelected}
          onCheckedChange={() => {
            if (isSelected) {
              setSelectedTeams(selectedTeams.filter((id) => id !== team.uid));
            } else {
              setSelectedTeams([...selectedTeams, team.uid]);
            }
          }}
          className="flex items-center gap-2"
        >
          <div className="shrink-0 size-4 flex items-center justify-center">
            {team.icon && team.icon in HeroIcons ? (
              React.createElement(
                HeroIcons[team.icon as keyof typeof HeroIcons],
                {
                  className: "h-4 w-4",
                  style: { color: team.color || "#6A00FF" },
                },
              )
            ) : (
              <HeroIcons.RocketLaunchIcon
                className="h-4 w-4"
                style={{ color: team.color || "#6A00FF" }}
              />
            )}
          </div>
          {team.name}
        </DropdownMenuCheckboxItem>
      );
    });
  };

  // Handle successful ticket creation
  const handleTicketCreated = (_ticket: ThenaTicket) => {
    // Always trigger a refetch to get the latest tickets with proper team information
    setRefreshTrigger((prev) => prev + 1);

    // Close the dialog
    setShowCreateRequest(false);
  };

  return (
    <div className="flex flex-col gap-4">
      {displayRequests && displayRequests.length > 0 && (
        <div className="flex justify-between items-center px-4 mt-4">
          <div className="flex items-center gap-2">
            {/* Team filter dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-7 rounded-sm border-border shadow-none data-[state=checked]:bg-primary"
                >
                  <Network size={14} />
                  <span>Teams</span>
                  {selectedTeams.length > 0 && (
                    <Badge
                      variant="secondary"
                      className="ml-1 h-5 px-1 text-xs"
                    >
                      {selectedTeams.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-48">
                {renderTeamFilterItems()}
                {selectedTeams.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full mt-2 h-7 text-xs"
                    onClick={() => setSelectedTeams([])}
                  >
                    Clear filters
                  </Button>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="flex items-center gap-2">
            {/* Search button/input */}
            {showSearchInput ? (
              <div className="relative">
                <Input
                  placeholder="Search tickets"
                  className="h-7 pl-9 pr-8 text-sm rounded-sm border-border w-48"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  autoFocus
                />
                <Search className="h-4 w-4 text-muted-foreground absolute left-2.5 top-1.5" />
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5 absolute right-1.5 top-1 p-0"
                  onClick={() => {
                    setSearchQuery("");
                    setShowSearchInput(false);
                  }}
                >
                  <X size={12} />
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                size="icon"
                className="h-7 w-7 rounded-sm border-border"
                onClick={() => setShowSearchInput(true)}
              >
                <Search size={14} />
              </Button>
            )}

            <Button
              variant="default"
              className="h-7 rounded-sm bg-primary text-primary-foreground hover:bg-primary/90 transition-colors px-4"
              onClick={() => setShowCreateRequest(true)}
            >
              <CircleFadingPlus size={14} className="mr-1" /> Ticket
            </Button>
          </div>
        </div>
      )}

      {/* Create Ticket Dialog */}
      <CreateTicketDialog
        open={showCreateRequest}
        setOpen={setShowCreateRequest}
        accountId={accountId}
        onTicketCreated={handleTicketCreated}
      />

      <div className="flex flex-col gap-2">
        {!displayRequests || displayRequests.length === 0 ? (
          <div className="flex flex-col items-center justify-center gap-3 py-12 px-6">
            <div className="rounded-full bg-muted p-3">
              <Ticket className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="text-sm text-muted-foreground mb-2">
              Create requests to manage your needs and requirements.
            </p>
            <Button
              variant="default"
              onClick={() => setShowCreateRequest(true)}
              className="h-7 rounded-sm px-4"
            >
              <CircleFadingPlus size={14} className="mr-1" /> Ticket
            </Button>
          </div>
        ) : filteredRequests.length === 0 ? (
          <div className="flex flex-col items-center justify-center gap-3 py-8 text-center">
            <div className="rounded-full bg-muted p-3">
              <Search className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="text-sm text-muted-foreground">
              No requests match your search. Try a different query.
            </p>
          </div>
        ) : (
          <Accordion
            type="multiple"
            defaultValue={Object.keys(requestsByTeam)}
            className="flex flex-col border-t border-border"
          >
            {Object.values(requestsByTeam).map((teamData) =>
              renderTeamSection(teamData),
            )}
            {isLoading && <ThenaLoader />}
          </Accordion>
        )}
      </div>
    </div>
  );
};

export default AccountRequest;
