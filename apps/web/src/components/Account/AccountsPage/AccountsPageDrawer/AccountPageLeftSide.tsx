import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useApi } from "@/hooks/use-api";
import {
  GET_ACTIVITIES_BY_ID,
  GET_CONTACTS_BY_ACCOUNT_ID,
  GET_NOTES_BY_ACCOUNT_ID,
  GET_TASK_BY_ACCOUNT_ID,
} from "@/services/accounts";
import { GET_ALL_ACCOUNT_TICKETS } from "@/services/kanban";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import {
  AccountActivityType,
  AccountNote as AccountNoteType,
  AccountTaskType,
  AccountTicket,
  ContactType,
} from "@/types/account";
import {
  Calendar,
  Contact2,
  CopyCheck,
  StickyNote,
  Ticket,
} from "lucide-react";
import { useEffect, useState } from "react";
import AccountContacts from "./AccountsDrawerLeftSide/AccountContacts";
import ActivityList from "./AccountsDrawerLeftSide/activities/ActivityList";
import AccountNote from "./AccountsDrawerLeftSide/notes/AccountNote";
import AccountRequest from "./AccountsDrawerLeftSide/requests/AccountRequest";
import AccountTask from "./AccountsDrawerLeftSide/tasks/AccountTask";

interface AccountPageLeftSideProps {
  accountId: string;
}

const AccountPageLeftSide = ({ accountId }: AccountPageLeftSideProps) => {
  const [tabValue, setTabValue] = useState("requests");
  const [currentTaskPage, setCurrentTaskPage] = useState(1);
  const [allTasks, setAllTasks] = useState<AccountTaskType[]>([]);
  const [hasMoreTasks, setHasMoreTasks] = useState(false);
  const [isLoadingMoreTasks, setIsLoadingMoreTasks] = useState(false);

  const { data: notesFromApi, refetch: notesRefetch } = useApi<{
    results: AccountNoteType[];
  }>(
    GET_NOTES_BY_ACCOUNT_ID(accountId),
    {},
    { isNextApi: false, enabled: true },
  );

  const { data: tasksFromApi, refetch: taskRefetch } = useApi<{
    data: {
      results: AccountTaskType[];
      meta: {
        totalCount: number;
        totalPages: number;
        currentPage: number;
        currentPageCount: number;
      };
    };
    status: boolean;
    message: string;
    timestamp: string;
  }>(
    `${GET_TASK_BY_ACCOUNT_ID(accountId)}?page=${currentTaskPage}&limit=50`,
    {},
    { isNextApi: true, enabled: true },
  );

  const { data: _activitiesFromApi } = useApi<{
    data: {
      results: AccountActivityType[];
      meta: {
        totalCount: number;
        totalPages: number;
        currentPage: number;
        currentPageCount: number;
      };
    };
  }>(GET_ACTIVITIES_BY_ID(accountId), {}, { isNextApi: true, enabled: true });

  const { data: contactsFromApi, refetch: contactRefetch } = useApi<{
    results: ContactType[];
  }>(
    GET_CONTACTS_BY_ACCOUNT_ID(accountId),
    {},
    { isNextApi: false, enabled: true },
  );

  const { data: tickets, loading: _ticketsLoading } = useApi<AccountTicket[]>(
    GET_ALL_ACCOUNT_TICKETS(accountId),
    {},
    { isNextApi: true, enabled: true },
  );

  const contacts = contactsFromApi?.results || [];
  const notes = notesFromApi?.results || [];
  const tasks = allTasks;
  const activitiesCount = _activitiesFromApi?.data?.meta?.totalCount || 0;
  const statuses = useTicketMetaStore((state) => state.statuses);

  useEffect(() => {
    if (tasksFromApi?.data?.results) {
      const newTasks = tasksFromApi.data.results;
      const meta = tasksFromApi.data.meta;

      if (currentTaskPage === 1) {
        setAllTasks(newTasks);
      } else {
        setAllTasks((prev) => {
          const existingTaskIds = new Set(prev.map((task) => task.id));
          const uniqueNewTasks = newTasks.filter(
            (task) => !existingTaskIds.has(task.id),
          );
          return [...prev, ...uniqueNewTasks];
        });
      }

      setHasMoreTasks(meta.currentPage < meta.totalPages);
    }
  }, [tasksFromApi, currentTaskPage]);

  const refetchTasks = () => {
    setCurrentTaskPage(1);
    taskRefetch();
  };

  const loadMoreTasks = async () => {
    if (hasMoreTasks && !isLoadingMoreTasks) {
      setIsLoadingMoreTasks(true);
      setCurrentTaskPage((prev) => prev + 1);
      setIsLoadingMoreTasks(false);
    }
  };

  return (
    <div className="h-full w-full overflow-auto bg-background border rounded-sm flex flex-col gap-4">
      <Tabs
        value={tabValue}
        onValueChange={setTabValue}
        defaultValue="contacts"
        className="w-full rounded-sm"
      >
        <TabsList className="w-full justify-start bg-background border-b rounded-[8px_8px_0px_0px] p-0 shadow-none h-min gap-2">
          <TabsTrigger
            value="requests"
            className="gap-2 rounded-[8px_0px_0px_0px] data-[state=active]:shadow-none data-[state=active]:border-b data-[state=active]:border-b-primary py-3"
          >
            <Ticket size={16} />
            Tickets{" "}
            <span className="rounded px-1.5 text-[var(--brand-gradient-end)] bg-[#6A00FF]/10 py-0.5 text-xs font-medium">
              {tickets?.length}
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="contacts"
            className="gap-2 rounded-[8px_0px_0px_0px] data-[state=active]:shadow-none data-[state=active]:border-b data-[state=active]:border-b-primary py-3"
          >
            <Contact2 className="h-4 w-4" />
            Contacts{" "}
            <span className="rounded px-1.5 text-[#2563eb] bg-[#3d6eef]/10 py-0.5 text-xs font-medium">
              {contacts?.length}
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="notes"
            className="gap-2 rounded-[8px_0px_0px_0px] data-[state=active]:shadow-none data-[state=active]:border-b data-[state=active]:border-b-primary py-3"
          >
            <StickyNote className="h-4 w-4" />
            Notes{" "}
            <span className="rounded px-1.5 text-[#6366F1] bg-[#8B5CF6]/10 py-0.5 text-xs font-medium">
              {notes?.length}
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="tasks"
            className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b data-[state=active]:border-b-primary py-3"
          >
            <CopyCheck className="h-4 w-4" />
            Tasks
            <span className="rounded px-1.5 text-[#ed9740] bg-[#ed9740]/10 py-0.5 text-xs font-medium">
              {tasks?.length}
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="activities"
            className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b data-[state=active]:border-b-primary py-3"
          >
            <Calendar className="h-4 w-4" />
            Activities
            <span className="rounded px-1.5 text-[#4CAF50] bg-[#4CAF50]/10 py-0.5 text-xs font-medium">
              {activitiesCount}
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="contacts"
          className="p-5 relative h-[calc(100vh-130px)] overflow-auto"
        >
          <AccountContacts
            accountId={accountId}
            contacts={contacts}
            contactRefetch={contactRefetch}
          />
        </TabsContent>

        <TabsContent
          value="notes"
          className="p-5 relative h-[calc(100vh-130px)] overflow-auto"
        >
          <AccountNote
            accountId={accountId}
            notesData={notes}
            refetch={notesRefetch}
          />
        </TabsContent>
        <TabsContent
          value="tasks"
          className="p-5 relative h-[calc(100vh-130px)] overflow-auto"
        >
          <AccountTask
            accountId={accountId}
            tasks={tasks}
            refetch={refetchTasks}
          />
          {hasMoreTasks && (
            <div className="flex justify-center mt-4">
              <Button
                variant="outline"
                onClick={loadMoreTasks}
                disabled={isLoadingMoreTasks}
                className="w-full max-w-md"
              >
                {isLoadingMoreTasks ? (
                  <Skeleton className="h-4 w-24 inline-block" />
                ) : (
                  "Load More Tasks"
                )}
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent
          value="requests"
          className="relative h-[calc(100vh-130px)] overflow-auto min-w-0"
        >
          <AccountRequest
            accountId={accountId}
            requests={tickets || []}
            statuses={statuses || []}
          />
        </TabsContent>

        <TabsContent
          value="activities"
          className="p-5 relative h-[calc(100vh-130px)] overflow-auto"
        >
          <ActivityList accountId={accountId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AccountPageLeftSide;
