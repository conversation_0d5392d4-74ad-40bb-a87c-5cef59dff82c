import { RoutingConfig } from '@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/routing/types';
import { fireEvent, render, screen } from '@testing-library/react';
import { AssignmentStrategyCard } from '../assignment-strategy-card';

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: jest.fn().mockReturnValue({
    push: jest.fn(),
  }),
}));

describe('AssignmentStrategyCard', () => {
  const mockOnUpdateConfig = jest.fn();
  const mockTeamId = 'team-123';
  
  const mockRoutingConfig: RoutingConfig = {
    teamId: 'team-123',
    userRoutingStrategy: 'manual',
    routingRespectsTimezone: false,
    routingRespectsUserTimezone: false,
    routingRespectsUserAvailability: false,
    routingRespectsUserCapacity: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with manual strategy correctly', () => {
    render(
      <AssignmentStrategyCard 
        routingConfig={mockRoutingConfig}
        onUpdateConfig={mockOnUpdateConfig}
        teamId={mockTeamId}
      />
    );

    // Check if title and description are rendered
    expect(screen.getByText('Assignment logic')).toBeInTheDocument();
    expect(screen.getByText('Configure basic routing behavior')).toBeInTheDocument();
    
    // Check if the select has the correct value
    expect(screen.getByText('Manual')).toBeInTheDocument();
    
    // Assignment factors should not be visible with manual strategy
    expect(screen.queryByText('Assignment factors')).not.toBeInTheDocument();
  });

  it('renders with round robin strategy correctly', () => {
    const roundRobinConfig = {
      ...mockRoutingConfig,
      userRoutingStrategy: 'round_robin',
    };

    render(
      <AssignmentStrategyCard 
        routingConfig={roundRobinConfig}
        onUpdateConfig={mockOnUpdateConfig}
        teamId={mockTeamId}
      />
    );

    // Check if title and description are rendered
    expect(screen.getByText('Assignment logic')).toBeInTheDocument();
    
    // Check if the select has the correct value
    expect(screen.getByText('Round robin')).toBeInTheDocument();
    
    // Assignment factors should be visible with round robin strategy
    expect(screen.getByText('Assignment factors')).toBeInTheDocument();
    expect(screen.getByText('Working hours')).toBeInTheDocument();
    expect(screen.getByText('Timezone')).toBeInTheDocument();
    expect(screen.getByText('Availability')).toBeInTheDocument();
    expect(screen.getByText('Capacity')).toBeInTheDocument();

    // Check if all checkboxes are disabled
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      expect(checkbox).toBeDisabled();
    });
  });

  it('calls onUpdateConfig when strategy is changed', () => {
    render(
      <AssignmentStrategyCard 
        routingConfig={mockRoutingConfig}
        onUpdateConfig={mockOnUpdateConfig}
        teamId={mockTeamId}
      />
    );

    // Open the select dropdown
    fireEvent.click(screen.getByRole('combobox'));
    
    // Select Round robin
    fireEvent.click(screen.getByText('Round robin'));
    
    // Check if onUpdateConfig was called with the correct value
    expect(mockOnUpdateConfig).toHaveBeenCalledWith({
      userRoutingStrategy: 'round_robin',
    });
  });

  it('renders in read-only mode correctly', () => {
    render(
      <AssignmentStrategyCard 
        routingConfig={mockRoutingConfig}
        onUpdateConfig={mockOnUpdateConfig}
        teamId={mockTeamId}
        readOnly={true}
      />
    );

    // The select should be disabled
    expect(screen.getByRole('combobox')).toHaveAttribute('disabled');
  });

  // TODO: Re-enable once backend supports assignment logic configuration

  // it('calls onUpdateConfig when checkboxes are toggled', () => {
  //   const roundRobinConfig = {
  //     ...mockRoutingConfig,
  //     userRoutingStrategy: 'round_robin',
  //   };

  //   render(
  //     <AssignmentStrategyCard 
  //       routingConfig={roundRobinConfig}
  //       onUpdateConfig={mockOnUpdateConfig}
  //       teamId={mockTeamId}
  //     />
  //   );

  //   // Toggle Timezone checkbox
  //   fireEvent.click(screen.getByLabelText('Timezone'));
    
  //   // Check if onUpdateConfig was called with the correct value
  //   expect(mockOnUpdateConfig).toHaveBeenCalledWith({
  //     routingRespectsUserTimezone: false, // Changed it because currently adding true to the checkboxes in the assignment logic UI (Will change it back once the reponse comes from backend)
  //   });
  // });

  it('shows groups info when showGroupsInfo is true', () => {
    render(
      <AssignmentStrategyCard 
        routingConfig={mockRoutingConfig}
        onUpdateConfig={mockOnUpdateConfig}
        teamId={mockTeamId}
        showGroupsInfo={true}
      />
    );

    // Check if groups info is rendered
    expect(screen.getByText('Want more control over ticket routing?')).toBeInTheDocument();
    expect(screen.getByText(/Create groups to enable specific routing rules/)).toBeInTheDocument();
    expect(screen.getByText('Create groups')).toBeInTheDocument();
  });
});
