"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAgentDrawerStore } from "@/store/agentDrawerStore";
import { Agent } from "@/types/agent";
import { Copy, MoreHorizontal, UserMinus, TrendingUp, CheckCircle, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { duplicateAgent, uninstallAgent } from '@/lib/api/agents';

// Helper function to format time (copied from AgentsTable)
function formatTimeAgo(timestamp: string): string {
  if (!timestamp) return "Never";
  const date = new Date(timestamp);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (seconds < 60) return "just now";
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) return `${minutes}m ago`;
  const hours = Math.floor(minutes / 60);
  if (hours < 24) return `${hours}h ago`;
  const days = Math.floor(hours / 24);
  if (days < 7) return `${days}d ago`;
  return date.toLocaleDateString();
}

interface AgentListItemProps { // Renamed interface
  agent: Agent;
}

export function AgentListItem({ agent }: AgentListItemProps) { // Renamed component
  const router = useRouter();
  const { openDrawer } = useAgentDrawerStore();

  const handleCardClick = () => {
    // Open drawer instead of navigating to a new page
    openDrawer(agent.id, router);
  };

  const handleActionClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click when interacting with dropdown
  };

  const handleDuplicate = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      toast.promise(duplicateAgent(agent.id), {
        loading: 'Duplicating agent...',
        success: () => {
          router.refresh();
          return 'Agent duplicated successfully';
        },
        error: (err) => `Failed to duplicate agent: ${err.message}`,
      });
    } catch (error) {
      console.error('Error duplicating agent:', error);
    }
  };

  const handleUnhire = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      toast.promise(uninstallAgent(agent.id), {
        loading: 'Unhiring agent...',
        success: () => {
          router.refresh();
          return 'Agent unhired successfully';
        },
        error: (err) => `Failed to unhire agent: ${err.message}`,
      });
    } catch (error) {
      console.error('Error unhiring agent:', error);
    }
  };

  return (
    <div
      className="group flex flex-col items-start justify-between p-4 rounded-sm border bg-card hover:bg-muted/50 cursor-pointer transition-all duration-150 ease-in-out hover:shadow-sm space-y-4"
      onClick={handleCardClick}
    >
      {/* Top Section: Avatar, Name, Last Active, Actions */}
      <div className="flex items-center justify-between space-x-4 w-full">
        {/* Left part: Avatar & Name */} 
        <div className="flex items-center space-x-4 min-w-0">
           <div className="relative flex-shrink-0">
              <Avatar className="h-12 w-12 border border-border/50">
                  <AvatarImage src={agent.avatar_url} alt={agent.name} />
                  <AvatarFallback className="bg-primary/5 text-primary text-sm font-medium">
                      {agent.name
                          ?.split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()}
                  </AvatarFallback>
              </Avatar>
              <div
                  className={cn(
                      "absolute -top-1 -right-1 w-3.5 h-3.5 rounded-full border-2 border-card",
                      agent.status === "active" ? "bg-green-500" : "bg-gray-400"
                  )}
                  title={agent.status === "active" ? "Active" : "Inactive"}
              />
            </div>
            <div className="min-w-0">
                <p className="text-lg font-medium text-foreground truncate" title={agent.name}>
                    {agent.name}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                    Last active: {formatTimeAgo(agent.updated_at)}
                </p>
            </div>
        </div>

        {/* Right part: Actions only (removed Last Active) */} 
        <div className="flex items-center flex-shrink-0">
            <div onClick={handleActionClick}>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 hover:bg-muted-foreground/10 opacity-50 group-hover:opacity-100 transition-opacity duration-150"
                        >
                            <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[160px]">
                        <DropdownMenuItem onClick={handleDuplicate}>
                            <Copy className="mr-2 h-4 w-4" />
                            Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={handleUnhire}
                        >
                            <UserMinus className="mr-2 h-4 w-4" />
                            Unhire
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </div>
      </div>

      {/* Second Row: Teams & Stats */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-3 sm:space-y-0 sm:space-x-6 w-full pt-4 border-t border-border/20">
        {/* Teams */}
        <div className="flex gap-1.5 flex-wrap justify-start items-center flex-grow" title="Assigned Teams">
           <span className="text-sm font-medium text-muted-foreground mr-2">Teams:</span>
            {agent.team_id && agent.team_id.length > 0 ? (
                agent.team_id.slice(0, 4).map((teamId) => ( // Show max 4 teams
                <Badge
                    key={teamId}
                    variant="outline"
                    className="px-2 py-0.5 text-sm font-medium border-border/70 bg-background text-muted-foreground hover:bg-muted/50 transition-all rounded-sm truncate"
                    title={`Team ID: ${teamId}`}
                >
                    {teamId}
                </Badge>
                ))
            ) : (
                <span className="text-sm text-muted-foreground/60 italic">No teams</span>
            )}
            {agent.team_id && agent.team_id.length > 4 && ( // Adjust count check
                 <Badge
                    variant="outline"
                    className="px-1.5 py-0.5 text-sm font-medium border-border/70 bg-background text-muted-foreground hover:bg-muted/50 transition-all rounded-sm"
                    title={`${agent.team_id.length - 4} more teams`}
                >
                   +{agent.team_id.length - 4}
                </Badge>
            )}
        </div>

        {/* Stats - Moved to right with justify-end */}
        <div className="flex items-center gap-3 text-sm text-muted-foreground justify-end flex-shrink-0">
          {(agent.pending_flows_count === 0 && agent.completed_flows_count === 0) ? (
            <Button 
              variant="default" 
              size="sm" 
              className="flex items-center gap-1.5"
              onClick={(e) => {
                e.stopPropagation();
                openDrawer(agent.id, router);
              }}
            >
              Configure your agent
              <ArrowRight className="h-3.5 w-3.5 ml-1" />
            </Button>
          ) : (
            <>
              <div className="flex items-center" title="Active flows">
                <Badge 
                  className="flex items-center gap-1.5 bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800/50 dark:hover:bg-blue-900/40 shadow-none"
                >
                  <TrendingUp className="h-3.5 w-3.5 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                  <span>{agent.pending_flows_count || 0} Active</span>
                </Badge>
              </div>
              <div className="flex items-center" title="Completed flows">
                <Badge 
                  className="flex items-center gap-1.5 bg-green-50 text-green-700 border-green-200 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800/50 dark:hover:bg-green-900/40 shadow-none"
                >
                  <CheckCircle className="h-3.5 w-3.5 text-green-500 dark:text-green-400 flex-shrink-0" />
                  <span>{agent.completed_flows_count || 0} Completed</span>
                </Badge>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
