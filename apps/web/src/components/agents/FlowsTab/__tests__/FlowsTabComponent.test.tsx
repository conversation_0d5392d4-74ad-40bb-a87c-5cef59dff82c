import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { FlowsTab } from '../FlowsTabComponent';
import { Agent } from '@/types/agent';
// Mock the child components
 
jest.mock('../FlowAccordionItem', () => ({
  FlowAccordionItem: ({ flow, onToggle, onViewExecutions, onViewInputSchema }: {
    flow: { id: string; name: string };
    onToggle?: (id: string) => void;
    onViewExecutions?: (id: string) => void;
    onViewInputSchema?: (id: string) => void;
  }) => {
    const handleToggle = () => {
      if (typeof onToggle === 'function') {
        onToggle(flow.id);
      }
    };

    return (
      <div data-testid={`flow-item-${flow.id}`}>
        <button 
          onClick={handleToggle}
          data-testid={`toggle-flow-${flow.id}`}
        >
          Toggle {flow.name}
        </button>
        <button 
          onClick={() => onViewExecutions?.(flow.id)}
          data-testid={`view-executions-${flow.id}`}
        >
          View Executions
        </button>
        <button 
          onClick={() => onViewInputSchema?.(flow.id)}
          data-testid={`view-schema-${flow.id}`}
        >
          View Schema
        </button>
      </div>
    );
  },
}));

// Mock shadcn/ui Dialog
 
jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ open, _onOpenChange, children }: {
    open?: boolean;
    _onOpenChange?: (open: boolean) => void;
    children: React.ReactNode;
  }) => (
    open ? (
      <div role="dialog" aria-modal="true">
        {children}
      </div>
    ) : null
  ),
  DialogContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DialogHeader: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DialogTitle: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DialogDescription: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DialogFooter: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

 
jest.mock('../DialogComponents', () => ({
  ExecutionDataDialog: ({ isOpen, onClose }: { isOpen?: boolean; onClose?: () => void }) => (
    isOpen ? (
      <div data-testid="execution-data-dialog">
        <h2>Execution Data</h2>
        <button onClick={() => onClose?.()}>Close</button>
      </div>
    ) : null
  ),
  SchemaDialog: ({ isOpen, onClose }: { isOpen?: boolean; onClose?: () => void }) => (
    isOpen ? (
      <div data-testid="schema-dialog">
        <h2>Schema</h2>
        <button onClick={() => onClose?.()}>Close</button>
      </div>
    ) : null
  ),
  EditFlowDialog: ({ isOpen, onClose }: { isOpen?: boolean; onClose?: () => void }) => (
    isOpen ? (
      <div data-testid="edit-flow-dialog">
        <h2>Edit Flow</h2>
        <button onClick={() => onClose?.()}>Close</button>
      </div>
    ) : null
  ),
  ExecuteFlowDialog: ({ isOpen, onClose }: { isOpen?: boolean; onClose?: () => void }) => (
    isOpen ? (
      <div data-testid="execute-flow-dialog">
        <h2>Execute Flow</h2>
        <button onClick={() => onClose?.()}>Close</button>
      </div>
    ) : null
  ),
}));

// Mock fetch
global.fetch = jest.fn();

const mockAgent: Agent = {
  id: 'test-agent-1',
  name: 'Test Agent',
  description: 'Test Description',
  team_id: ['test-team-1'],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  role: 'support',
  is_active: true,
  status: 'active',
  metadata: {},
};

const mockFlows = [
  {
    id: 'flow-1',
    name: 'Test Flow 1',
    description: 'Test Description 1',
    isActive: true,
    configuration: {},
    inputSchema: {},
    outputSchema: {},
  },
  {
    id: 'flow-2',
    name: 'Test Flow 2',
    description: 'Test Description 2',
    isActive: false,
    configuration: {},
    inputSchema: {},
    outputSchema: {},
  },
];

 
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  
  // Add display name
  Wrapper.displayName = 'QueryClientWrapper';
  
  return Wrapper;
};

describe('FlowsTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockReset();
  });

  it('renders loading state when fetching flows', () => {
    (global.fetch as jest.Mock).mockImplementation(() => new Promise(() => {}));

    render(<FlowsTab agent={mockAgent} />, {
      wrapper: createWrapper(),
    });

    expect(screen.getByText(/Loading/i)).toBeInTheDocument();
  });

  it('renders flows when data is loaded', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockFlows),
      })
    );

    render(<FlowsTab agent={mockAgent} />, {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(screen.getByTestId('flow-item-flow-1')).toBeInTheDocument();
      expect(screen.getByTestId('flow-item-flow-2')).toBeInTheDocument();
    });
  });

  it.skip('handles flow toggle', async () => {
    const mockToggleResponse = { success: true };
    
    (global.fetch as jest.Mock)
      .mockImplementationOnce(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockFlows),
        })
      )
      .mockImplementationOnce(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockToggleResponse),
        })
      );

    render(<FlowsTab agent={mockAgent} />, {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(screen.getByTestId('flow-item-flow-1')).toBeInTheDocument();
    });

    // Click toggle button
    fireEvent.click(screen.getByTestId('toggle-flow-flow-1'));
    
    // Confirm dialog should appear
    const confirmDialog = await screen.findByRole('dialog');
    expect(confirmDialog).toBeInTheDocument();
    expect(screen.getByText('Disable flow')).toBeInTheDocument();

    // Click confirm button
    fireEvent.click(screen.getByRole('button', { name: /disable/i }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        `/api/agents/${mockAgent.id}/flows/flow-1/toggle`,
        expect.any(Object)
      );
    });
  });

  it('handles error when fetching flows', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: false,
        status: 500,
      })
    );

    render(<FlowsTab agent={mockAgent} />, {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(screen.getByText('No flows available for this agent.')).toBeInTheDocument();
    });
  });

  it.skip('handles view executions', async () => {
    (global.fetch as jest.Mock)
      .mockImplementationOnce(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockFlows),
        })
      )
      .mockImplementationOnce(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve([{ id: 'exec-1', status: 'completed' }]),
        })
      );

    render(<FlowsTab agent={mockAgent} />, {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(screen.getByTestId('flow-item-flow-1')).toBeInTheDocument();
    });

    // Click view executions button
    fireEvent.click(screen.getByTestId('view-executions-flow-1'));

    // Dialog should appear
    const executionDialog = await screen.findByRole('dialog');
    expect(executionDialog).toBeInTheDocument();
    expect(screen.getByText('Execution Data')).toBeInTheDocument();

    // Close dialog
    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);

    // Wait for dialog to be removed
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  it.skip('handles view input schema', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockFlows),
      })
    );

    render(<FlowsTab agent={mockAgent} />, {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(screen.getByTestId('flow-item-flow-1')).toBeInTheDocument();
    });

    // Click view schema button
    fireEvent.click(screen.getByTestId('view-schema-flow-1'));

    // Dialog should appear
    const schemaDialog = await screen.findByRole('dialog');
    expect(schemaDialog).toBeInTheDocument();
    expect(screen.getByText('Schema')).toBeInTheDocument();

    // Close dialog
    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);

    // Wait for dialog to be removed
    await waitFor(
      () => {
        const dialog = screen.queryByRole('dialog');
        expect(dialog).not.toBeInTheDocument();
      },
      { timeout: 1000 }
    );
  });

  it('handles request new flow dialog', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockFlows),
      })
    );

    render(<FlowsTab agent={mockAgent} />, {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(screen.getByText('Request capability')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Request capability'));

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toHaveTextContent('Request a new AI capability');
      expect(screen.getByLabelText('Title')).toBeInTheDocument();
      expect(screen.getByLabelText('Description')).toBeInTheDocument();
    });

    // Fill and submit the form
    await userEvent.type(screen.getByLabelText('Title'), 'New Flow');
    await userEvent.type(screen.getByLabelText('Description'), 'New Flow Description');
    
    fireEvent.click(screen.getByRole('button', { name: /submit request/i }));

    // Dialog should close
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });
});
