"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Fix dropdown menu imports
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useApi } from "@/hooks/use-api";
import { Agent, AgentFile } from "@/types/agent";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Uppy from "@uppy/core";
import "@uppy/core/dist/style.min.css";
import "@uppy/dashboard/dist/style.min.css";
import { Dashboard } from "@uppy/react";
import XHRUpload from "@uppy/xhr-upload";
import {
  AlertCircle,
  ArrowUpRight,
  CheckCircle,
  FileText,
  Key,
  Link,
  PanelRight,
  Pencil,
  Plus,
  Trash2,
  Upload,
} from "lucide-react";
import Image from "next/image"; // Import Next.js Image component
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Panel } from "react-resizable-panels";
import { toast } from "sonner";
import * as z from "zod";

// Create a simple event emitter for file operations
const createEventEmitter = () => {
  const listeners = new Set<() => void>();
  
  return {
    emit: () => {
      listeners.forEach(listener => listener());
    },
    subscribe: (callback: () => void) => {
      listeners.add(callback);
      return () => listeners.delete(callback);
    }
  };
};

// Create a file operations event emitter
const fileOperationsEmitter = createEventEmitter();

interface AgentDetailsRightsideProps {
  agentId: string;
  setIsRightSideOpen: (isOpen: boolean) => void;
}

interface AgentAuth {
  id: string;
  organization_id: string;
  api_key: string;
  app_name: string;
  category: string;
  installed_at: string;
  webhook_url: string | null;
  created_at: string;
  agent_id: string;
  team_id: string[];
}

const authConfigSchema = z.object({
  api_key: z.string().min(1, "API Key is required"),
  app_name: z.string().min(1, "App name is required"),
});

type AuthConfigFormValues = z.infer<typeof authConfigSchema>;

// Helper function to format relative time
const getRelativeTimeString = (date: Date | string): string => {
  const now = new Date();
  const then = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - then.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return "Just now";
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? "s" : ""} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays > 1 ? "s" : ""} ago`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths > 1 ? "s" : ""} ago`;
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} year${diffInYears > 1 ? "s" : ""} ago`;
};

const AgentDetailsRightside = ({
  agentId,
  setIsRightSideOpen,
}: AgentDetailsRightsideProps) => {
  const [detailsPanelSize, setDetailsPanelSize] = useState(35); // Default 35% width
  const [isCollapsing, setIsCollapsing] = useState(false);
  const [isUrlDialogOpen, setIsUrlDialogOpen] = useState(false);
  const [url, setUrl] = useState("");
  const [isAuthDialogOpen, setIsAuthDialogOpen] = useState(false);
  const [isFileUploadModalOpen, setIsFileUploadModalOpen] = useState(false);
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize Uppy
  const [uppy] = useState(() =>
    new Uppy({
      restrictions: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxNumberOfFiles: 10,
        allowedFileTypes: [
          "text/*",
          "application/pdf",
          "application/json",
          "application/xml",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
      },
      autoProceed: false,
    }).use(XHRUpload, {
      endpoint: `/api/agents/${agentId}/files`,
      formData: true,
      fieldName: "file",
    }),
  );

  // Set up Uppy event listeners
  useEffect(() => {
    uppy.on("upload-success", (file) => {
      toast.success(`${file.name} uploaded successfully`);
      
      // Trigger immediate refetch
      queryClient.refetchQueries({ queryKey: ["agent", agentId] });
      queryClient.refetchQueries({ queryKey: ["agent-files", agentId] });
      
      // Emit event for any other components that might need to know
      fileOperationsEmitter.emit();
      
      // Close the modal
      setIsFileUploadModalOpen(false);
    });

    uppy.on("upload-error", (file, error) => {
      toast.error(`Failed to upload ${file.name}: ${error.message}`);
    });

    return () => {
      uppy.off("upload-success", () => {});
      uppy.off("upload-error", () => {});
      uppy.removeFiles([]);
    };
  }, [uppy, queryClient, agentId]);

  // Subscribe to file operations events
  useEffect(() => {
    // Function to handle file operations events
    const handleFileOperations = () => {
      // Force refetch of data
      queryClient.refetchQueries({ queryKey: ["agent", agentId] });
      queryClient.refetchQueries({ queryKey: ["agent-files", agentId] });
    };

    // Subscribe to the event emitter
    const unsubscribe = fileOperationsEmitter.subscribe(handleFileOperations);

    // Cleanup subscription on component unmount
    return () => {
      unsubscribe();
    };
  }, [queryClient, agentId]);

  // Fetch agent data
  const { data: agent, loading: isAgentLoading } = useApi<Agent>(
    `/api/agents/${agentId}`,
    {},
    { isNextApi: true, enabled: true },
  );

  // Fetch agent files
  const { data: files, loading: isFilesLoading } = useApi<AgentFile[]>(
    `/api/agents/${agentId}/files`,
    {},
    { isNextApi: true, enabled: true },
  );

  // Fetch agent auth
  const { data: auths, loading: isAuthsLoading } = useApi<AgentAuth[]>(
    `/api/agents/${agentId}/auth`,
    {},
    { isNextApi: true, enabled: true },
  );

  const form = useForm<AuthConfigFormValues>({
    resolver: zodResolver(authConfigSchema),
    defaultValues: {
      api_key: "",
      app_name: "",
    },
  });

  const handleCollapsePanel = () => {
    setIsCollapsing(true);
    // Add a small delay to allow the animation to play
    setTimeout(() => {
      setIsRightSideOpen(false);
      setIsCollapsing(false);
    }, 200);
  };

  // URL upload mutation
  const uploadUrlMutation = useMutation({
    mutationFn: async (url: string) => {
      const response = await fetch(`/api/agents/${agentId}/files`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(error);
      }

      return response.json();
    },
    onSuccess: () => {
      // Force immediate refetch
      queryClient.refetchQueries({ queryKey: ["agent", agentId] });
      queryClient.refetchQueries({ queryKey: ["agent-files", agentId] });
      
      // Emit event for any other components that might need to know
      fileOperationsEmitter.emit();
      
      toast.success("URL content imported successfully");
      setIsUrlDialogOpen(false);
      setUrl("");
    },
    onError: (error) => {
      toast.error(`Failed to import URL: ${error.message}`);
    },
  });

  // File deletion mutation
  const deleteFileMutation = useMutation({
    mutationFn: async (fileId: string) => {
      const response = await fetch(`/api/agents/${agentId}/files/${fileId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(error);
      }
    },
    onSuccess: () => {
      // Force immediate refetch
      queryClient.refetchQueries({ queryKey: ["agent", agentId] });
      queryClient.refetchQueries({ queryKey: ["agent-files", agentId] });
      
      // Emit event for any other components that might need to know
      fileOperationsEmitter.emit();
      
      toast.success("File deleted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to delete file: ${error.message}`);
    },
  });

  // Auth update mutation
  const updateAuthMutation = useMutation({
    mutationFn: async (data: {
      config: { api_key: string; app_name: string };
    }) => {
      const response = await fetch(`/api/agents/${agentId}/auth`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data.config),
      });

      if (!response.ok) {
        throw new Error(
          `Failed to update auth configuration: ${response.status} ${response.statusText}`,
        );
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agent", agentId] });
      queryClient.invalidateQueries({ queryKey: ["auth", agentId] });
      toast.success("Auth configuration updated successfully");
      setIsAuthDialogOpen(false);
    },
    onError: (error) => {
      toast.error(`Error updating auth: ${error.message}`);
    },
  });

  const handleEditAuth = (auth: AgentAuth) => {
    form.reset({
      api_key: auth.api_key,
      app_name: auth.app_name,
    });
    setIsAuthDialogOpen(true);
  };

  const onSubmitAuth = (values: AuthConfigFormValues) => {
    updateAuthMutation.mutate({
      config: {
        api_key: values.api_key,
        app_name: values.app_name,
      },
    });
  };

  if (!agent && !isAgentLoading) {
    return (
      <Panel
        defaultSize={detailsPanelSize}
        minSize={35}
        maxSize={50}
        onResize={setDetailsPanelSize}
        className="bg-background border rounded-sm"
      >
        <div className="flex flex-col items-center justify-center h-full">
          <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-muted-foreground">Failed to load agent details</p>
        </div>
      </Panel>
    );
  }

  return (
    <Panel
      defaultSize={detailsPanelSize}
      minSize={35}
      maxSize={50}
      onResize={setDetailsPanelSize}
      className={`bg-background border rounded-sm transition-all duration-200 ${
        isCollapsing ? "opacity-0 transform translate-x-10" : "opacity-100"
      }`}
    >
      <div className="h-full relative">
        <div className="flex flex-col h-full overflow-auto">
          {/* Header with title and collapse button */}
          <div className="flex justify-between items-center px-6 py-2 border-b">
            <h2 className="font-medium">Agent details</h2>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 flex items-center justify-center text-muted-foreground hover:text-foreground hover:bg-accent/50"
                onClick={handleCollapsePanel}
                title="Hide details panel"
              >
                <PanelRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Content area */}
          <div className="flex-1 overflow-auto p-6">
            {isAgentLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
              </div>
            ) : agent ? (
              <div className="space-y-4">
                {/* About section - Modern Profile Card */}
                <div className="rounded-sm overflow-hidden border">
                  {/* Profile Header with Image */}
                  <div className="relative">
                    {/* Dark background that ends at half image height */}
                    <div className="bg-[#2D3748] h-20 w-full absolute top-0 left-0"></div>

                    <div className="flex p-4 pb-0 relative z-10">
                      {/* Image */}
                      <div className="h-24 w-24 rounded-sm overflow-hidden mr-4 flex-shrink-0">
                        <Image
                          src={agent.avatar_url || "/placeholder-avatar.png"}
                          alt={agent.name || "Agent"}
                          width={80}
                          height={80}
                          className="h-full w-full object-cover"
                        />
                      </div>

                      {/* Name and role - aligned to top */}
                      <div className="flex flex-col justify-start">
                        <h3 className="text-lg font-medium text-white">
                          {agent.name}
                        </h3>
                        <p className="text-sm text-gray-300">Agent</p>
                      </div>
                    </div>
                  </div>

                  {/* Profile Content */}
                  <div className="bg-[var(--modal-bg)] p-5 space-y-4">
                    {/* About */}
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground mb-1">
                        About
                      </h4>
                      <p className="text-sm color-text">
                        {agent.description || "No description provided."}
                      </p>
                    </div>

                    {/* Last Active */}
                    <div className="grid grid-cols-2 gap-3">
                      <h4 className="text-sm text-muted-foreground">
                        Last active
                      </h4>
                      <div className="text-sm color-text">
                        {getRelativeTimeString(agent.updated_at)}
                      </div>
                    </div>

                    {/* Teams */}
                    <div className="grid grid-cols-2 gap-3">
                      <h4 className="text-sm text-muted-foreground">Teams</h4>
                      <div className="text-sm color-text">
                        {agent.team_id
                          ? String(agent.team_id)
                          : "No team assigned"}
                      </div>
                    </div>

                    {/* Flow Stats - with Lucide icons and fixed height */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="border border-blue-200 bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-800/50 rounded-sm h-6 flex items-center justify-center gap-1 px-1">
                        <ArrowUpRight className="h-3.5 w-3.5 text-blue-500 dark:text-blue-400" />
                        <span className="text-xs">
                          {agent.pending_flows_count || 0} executed
                        </span>
                      </div>
                      <div className="border border-green-200 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800/50 rounded-sm h-6 flex items-center justify-center gap-1 px-1">
                        <CheckCircle className="h-3.5 w-3.5 text-green-500 dark:text-green-400" />
                        <span className="text-xs">
                          {agent.completed_flows_count || 0} completed
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Knowledge section - More Minimal */}
                <Accordion
                  type="single"
                  collapsible
                  className="w-full"
                  defaultValue="knowledge"
                >
                  <AccordionItem value="knowledge" className="border-none">
                    <AccordionTrigger
                      className="hover:no-underline px-0 py-2"
                      iconLeft
                    >
                      <div className="flex w-full items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Knowledge</span>
                        </div>
                        {files && files.length > 0 && (
                          <div className="flex items-center gap-2">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                  }}
                                  className="h-7"
                                >
                                  <Plus className="h-3.5 w-3.5" />
                                  <span className="text-xs">Add</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                align="end"
                                className="w-40"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setIsFileUploadModalOpen(true);
                                  }}
                                  className="text-xs"
                                >
                                  <Upload className="h-3.5 w-3.5 mr-2" />
                                  Upload File
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setIsUrlDialogOpen(true);
                                  }}
                                  className="text-xs"
                                >
                                  <Link className="h-3.5 w-3.5 mr-2" />
                                  Add URL
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        )}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="pt-4 px-0">
                      {/* Hidden file input */}
                      <input
                        type="file"
                        ref={fileInputRef}
                        style={{ display: "none" }}
                        accept=".pdf,.doc,.docx,.txt,.csv,.md"
                      />

                      {isFilesLoading ? (
                        <div className="space-y-2">
                          <Skeleton className="h-12 w-full" />
                          <Skeleton className="h-12 w-full" />
                        </div>
                      ) : files && files.length > 0 ? (
                        <div className="space-y-2">
                          {files.map((file) => (
                            <Card key={file.id} className="bg-card border">
                              <CardContent className="p-3 flex justify-between items-center">
                                <div className="flex items-center gap-3">
                                  <div className="bg-[var(--color-bg-document)] p-2 rounded-sm">
                                    <FileText className="h-5 w-5 text-[var(--color-text-document)]" />
                                  </div>
                                  <div>
                                    <p className="font-medium text-sm">
                                      {file.name}
                                    </p>
                                    <div className="flex items-center gap-2 mt-0.5">
                                      <Badge
                                        variant="secondary"
                                        className="uppercase text-xs font-normal h-5 flex items-center"
                                      >
                                        {file.mime_type
                                          ? file.mime_type.split("/")[1]
                                          : "unknown"}
                                      </Badge>
                                      <span className="text-xs text-muted-foreground">
                                        {(file.size / 1024).toFixed(1)} KB
                                      </span>
                                      {file.created_at && (
                                        <span className="text-xs text-muted-foreground hidden sm:inline">
                                          {new Date(
                                            file.created_at,
                                          ).toLocaleDateString()}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={() => {
                                    if (
                                      confirm(
                                        "Are you sure you want to delete this file?",
                                      )
                                    ) {
                                      deleteFileMutation.mutate(file.id);
                                    }
                                  }}
                                  disabled={deleteFileMutation.isPending}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center py-6 px-4 border rounded-sm bg-muted/10">
                          <FileText className="h-8 w-8 text-muted-foreground/50 mb-3" />
                          <p className="text-sm text-muted-foreground text-center mb-4">
                            No knowledge files yet. Add content to help your
                            agent access information.
                          </p>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Plus className="h-4 w-4 mr-2" />
                                Add Content
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                              align="end"
                              className="w-40"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setIsFileUploadModalOpen(true);
                                }}
                              >
                                <Upload className="h-4 w-4 mr-2" />
                                Upload File
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setIsUrlDialogOpen(true);
                                }}
                              >
                                <Link className="h-4 w-4 mr-2" />
                                Add URL
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>

                <Separator className="my-2" />

                {/* Auth section - More Minimal */}
                <Accordion
                  type="single"
                  collapsible
                  className="w-full"
                  defaultValue="auth"
                >
                  <AccordionItem value="auth" className="border-none">
                    <AccordionTrigger
                      className="hover:no-underline px-0 py-2"
                      iconLeft
                    >
                      <div className="flex w-full items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">
                            Authentication
                          </span>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="pt-4 px-0">
                      {isAuthsLoading ? (
                        <div className="space-y-2">
                          <Skeleton className="h-12 w-full" />
                          <Skeleton className="h-12 w-full" />
                        </div>
                      ) : auths && auths.length > 0 ? (
                        <div className="space-y-2">
                          {auths.map((auth) => (
                            <Card key={auth.id} className="bg-card border">
                              <CardContent className="p-3 flex justify-between items-center">
                                <div className="flex items-center gap-3">
                                  <div className="bg-[var(--color-bg-auth)] p-2 rounded-sm">
                                    <Key className="h-5 w-5 text-[var(--color-text-auth)]" />
                                  </div>
                                  <div>
                                    <p className="font-medium text-sm">
                                      {auth.app_name}
                                    </p>
                                    <div className="flex items-center gap-2 mt-0.5">
                                      <Badge
                                        variant="outline"
                                        className="text-xs h-5 flex items-center"
                                      >
                                        {auth.category}
                                      </Badge>
                                      <span className="text-xs text-muted-foreground">
                                        {auth.api_key.slice(0, 10)}...
                                      </span>
                                    </div>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEditAuth(auth)}
                                  className="h-8 w-8"
                                >
                                  <Pencil className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                                </Button>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center py-6 px-4 border rounded-sm bg-muted/10">
                          <Key className="h-8 w-8 text-muted-foreground/50 mb-3" />
                          <p className="text-sm text-muted-foreground text-center mb-4">
                            No authentication configured. Add API keys to
                            connect your agent to external services.
                          </p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              toast.info(
                                "Auth creation would be integrated here",
                              )
                            }
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add API Key
                          </Button>
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            ) : null}
          </div>
        </div>

        {/* URL Input Dialog */}
        <Dialog open={isUrlDialogOpen} onOpenChange={setIsUrlDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add URL</DialogTitle>
              <DialogDescription>
                Enter a URL to add to your agent&apos;s knowledge base
              </DialogDescription>
            </DialogHeader>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (url.trim()) {
                  uploadUrlMutation.mutate(url.trim());
                }
              }}
            >
              <div className="grid gap-4 py-4">
                <Input
                  placeholder="https://..."
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  type="url"
                  required
                />
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsUrlDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={uploadUrlMutation.isPending || !url.trim()}
                >
                  {uploadUrlMutation.isPending ? "Adding..." : "Add URL"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* File Upload Modal */}
        <Dialog
          open={isFileUploadModalOpen}
          onOpenChange={setIsFileUploadModalOpen}
        >
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Upload Files</DialogTitle>
              <DialogDescription>
                Upload files to add to your agent&apos;s knowledge
              </DialogDescription>
            </DialogHeader>
            <div className="my-4">
              <Dashboard
                uppy={uppy}
                proudlyDisplayPoweredByUppy={false}
                showProgressDetails
                height={320}
                width="100%"
              />
            </div>
          </DialogContent>
        </Dialog>

        {/* Auth Edit Dialog */}
        <Dialog open={isAuthDialogOpen} onOpenChange={setIsAuthDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Edit Auth Configuration</DialogTitle>
              <DialogDescription>
                Update the API key and app name for this agent.
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmitAuth)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="api_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Key</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="text"
                          placeholder="Enter API key"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="app_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>App Name</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="text"
                          placeholder="Enter app name"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsAuthDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">Save Changes</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </Panel>
  );
};

export default AgentDetailsRightside;
