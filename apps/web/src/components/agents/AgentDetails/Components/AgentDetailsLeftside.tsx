"use client";

import { DeployTab } from "@/components/agents/deploy-tab";
import { ExecutionsTab } from "@/components/agents/executions-tab";
import { FlowsTab } from "@/components/agents/flows-tab";
import { TrainerTab } from "@/components/agents/trainer-tab";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useApi } from "@/hooks/use-api";
import { Agent } from "@/types/agent";
import { Box, FlaskConical, Play, Rocket } from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { Panel } from "react-resizable-panels";

interface AgentDetailsLeftsideProps {
  agentId: string;
}

const AgentDetailsLeftside = ({ agentId }: AgentDetailsLeftsideProps) => {
  const [tabValue, setTabValue] = useState("flows");
  const [isLoading] = useState(false);
  const [contentHeight, setContentHeight] = useState(600); // Default height
  const tabsRef = useRef<HTMLDivElement>(null);
  const tabsListRef = useRef<HTMLDivElement>(null);

  // Fetch agent data
  const { data: agent, loading: isLoadingAgent, refetch: refetchAgent, error } = useApi<Agent>(
    `/api/agents/${agentId}`,
    {},
    { isNextApi: true, enabled: true },
  );

  // Calculate available height for content - wrapped in useCallback to prevent listener churn
  const calculateHeight = useCallback(() => {
    if (tabsRef.current && tabsListRef.current) {
      const tabsHeight = tabsRef.current.clientHeight;
      const tabsListHeight = tabsListRef.current.clientHeight;
      const paddingHeight = 48; // 24px padding top + 24px padding bottom
      // Guard against negative height with Math.max
      const availableHeight = Math.max(
        0,
        tabsHeight - tabsListHeight - paddingHeight
      );
      setContentHeight(availableHeight);
    }
  }, [tabsRef, tabsListRef]);

  // Set up resize listener just once
  useEffect(() => {
    calculateHeight();
    window.addEventListener('resize', calculateHeight);
    
    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, [calculateHeight]);

  // Force a refetch if needed
  useEffect(() => {
    // If there's an error and we're not loading, try to refetch
    if (!agent && !isLoadingAgent && error) {

      refetchAgent();
    }
    
    // Set a timeout to refetch if loading takes too long (10 seconds)
    let timeoutId: NodeJS.Timeout;
    if (isLoadingAgent && !agent) {
      timeoutId = setTimeout(() => {

        refetchAgent();
      }, 10000);
    }
    
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [agent, isLoadingAgent, error, refetchAgent]);

  // Hotkeys for tab navigation
  useHotkeys("shift+f", () => {
    setTabValue("flows");
  });
  useHotkeys("shift+e", () => {
    setTabValue("executions");
  });
  useHotkeys("shift+t", () => {
    setTabValue("trainer");
  });
  useHotkeys("shift+d", () => {
    setTabValue("deploy");
  });

  // Hotkeys setup complete

  // Reusable loading component with modern UI
  const LoadingState = () => (
    <div className="flex flex-col items-center justify-center min-h-[300px] space-y-4">
      <div className="relative h-12 w-12">
        <div className="absolute inset-0 rounded-full border-2 border-muted-foreground/20"></div>
        <div className="absolute inset-0 rounded-full border-t-2 border-brand animate-spin"></div>
      </div>
      <div className="text-center space-y-2">
        <p className="text-muted-foreground font-medium">Loading agent data...</p>
        {error && (
          <div className="text-destructive text-sm bg-destructive/10 px-4 py-2 rounded-md max-w-md">
            {error.message}
            <button 
              onClick={() => refetchAgent()} 
              className="ml-2 text-brand hover:text-brand/80 transition-colors underline underline-offset-2"
            >
              Retry
            </button>
          </div>
        )}
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <Panel
        defaultSize={65}
        minSize={50}
        className="h-full bg-background border rounded-sm flex flex-col"
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </Panel>
    );
  }

  return (
    <Panel
      defaultSize={65}
      minSize={50}
      className="h-full bg-background border rounded-sm flex flex-col"
    >
      <Tabs
        value={tabValue}
        onValueChange={setTabValue}
        defaultValue="flows"
        className="w-full rounded-sm h-full"
        ref={tabsRef}
      >
        <TabsList 
          className="w-full justify-start bg-background border-b rounded-[8px_8px_0px_0px] p-0 shadow-none h-min gap-2"
          ref={tabsListRef}
        >
          <TabsTrigger
            value="flows"
            className="gap-2 rounded-[8px_0px_0px_0px] data-[state=active]:shadow-none data-[state=active]:border-b-[2px] data-[state=active]:border-b-[hsl(var(--primary))] py-3"
          >
            <Box className="h-4 w-4" />
            Flows
          </TabsTrigger>
          <TabsTrigger
            value="executions"
            className="gap-2 rounded-[8px_0px_0px_0px] data-[state=active]:shadow-none data-[state=active]:border-b-[2px] data-[state=active]:border-b-[hsl(var(--primary))] py-3"
          >
            <Play className="h-4 w-4" />
            Executions
          </TabsTrigger>
          <TabsTrigger
            value="trainer"
            className="gap-2 rounded-[8px_0px_0px_0px] data-[state=active]:shadow-none data-[state=active]:border-b-[2px] data-[state=active]:border-b-[hsl(var(--primary))] py-3"
          >
            <FlaskConical className="h-4 w-4" />
            Trainer
          </TabsTrigger>
          <TabsTrigger
            value="deploy"
            className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b-[2px] data-[state=active]:border-b-[hsl(var(--primary))] py-3"
          >
            <Rocket className="h-4 w-4" />
            Deployment
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="flows"
          className="p-6 overflow-y-auto" style={{ height: `${contentHeight}px` }}
        >
          {agent ? (
            <FlowsTab agent={agent} />
          ) : (
            <LoadingState />
          )}
        </TabsContent>

        <TabsContent
          value="executions"
          className="p-6 overflow-y-auto" style={{ height: `${contentHeight}px` }}
        >
          {agent ? (
            <ExecutionsTab agent={agent} />
          ) : (
            <LoadingState />
          )}
        </TabsContent>

        <TabsContent
          value="trainer"
          className="p-6 overflow-y-auto" style={{ height: `${contentHeight}px` }}
        >
          {agent ? (
            <TrainerTab agent={agent} />
          ) : (
            <LoadingState />
          )}
        </TabsContent>

        <TabsContent
          value="deploy"
          className="p-6 overflow-y-auto" style={{ height: `${contentHeight}px` }}
        >
          {agent ? (
            <DeployTab agent={agent} />
          ) : (
            <LoadingState />
          )}
        </TabsContent>
      </Tabs>
    </Panel>
  );
};

export default AgentDetailsLeftside;
