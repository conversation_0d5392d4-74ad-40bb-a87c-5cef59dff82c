"use client";

import { SettingsTab } from "@/components/agents/settings-tab";
import Then<PERSON><PERSON>oader from "@/components/thena-loader";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useSidebar } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { useAgentDrawerStore } from "@/store/agentDrawerStore";
import { Agent } from "@/types/agent";
import { ChevronLeft, Grip, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { HotkeysProvider } from "react-hotkeys-hook";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import AgentDetailsHeader from "./Components/AgentDetailsHeader";
import AgentDetailsLeftside from "./Components/AgentDetailsLeftside";
import AgentDetailsRightside from "./Components/AgentDetailsRightside";

// Resize handle component with three dots
const ResizeHandle = () => (
  <PanelResizeHandle className="relative flex items-center justify-center w-2 mx-0.5 group">
    <div className="absolute h-12 flex items-center justify-center">
      <Grip
        size={12}
        className="text-muted-foreground/40 group-hover:text-muted-foreground/70 transition-colors"
      />
    </div>
  </PanelResizeHandle>
);

export function AgentDetails() {
  const router = useRouter();
  const { isOpen, agentId, closeDrawer } = useAgentDrawerStore();
  const { state } = useSidebar();
  const isSidebarOpen = state === "expanded";
  const [isRightSideOpen, setIsRightSideOpen] = useState(true);
  const [isExpanding, setIsExpanding] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [agentData, setAgentData] = useState<Agent | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [showBanner, setShowBanner] = useState(true);

  // Fetch agent data
  useEffect(() => {
    if (isOpen && agentId) {
      setIsLoading(true);

      // Fetch agent data
      fetch(`/api/agents/${agentId}`)
        .then((response) => {
          if (!response.ok) {
            throw new Error("Failed to fetch agent data");
          }
          return response.json();
        })
        .then((data) => {
          setAgentData(data);
          setIsLoading(false);
        })
        .catch((error) => {
          console.error("Error fetching agent data:", error);
          setIsLoading(false);
        });
    }
  }, [isOpen, agentId]);

  // Listen for agent updates from the settings tab
  useEffect(() => {
    const handleAgentUpdate = (event: CustomEvent) => {
      const updatedAgent = event.detail;
      if (updatedAgent && updatedAgent.id === agentId) {
        setAgentData(updatedAgent);
        // Hide the banner when agent is updated
        setShowBanner(false);
      }
    };

    // Add event listener
    window.addEventListener(
      "agent-updated",
      handleAgentUpdate as EventListener,
    );

    // Clean up
    return () => {
      window.removeEventListener(
        "agent-updated",
        handleAgentUpdate as EventListener,
      );
    };
  }, [agentId]);

  const handleExpandPanel = () => {
    setIsExpanding(true);
    setIsRightSideOpen(true);
    // Reset expanding state after animation completes
    setTimeout(() => {
      setIsExpanding(false);
    }, 200);
  };

  if (!isOpen || !agentId) return null;

  return (
    <HotkeysProvider initiallyActiveScopes={["agent-details"]}>
      <div
        className={cn(
          "fixed inset-0 bg-color-bg-subtle px-4 pb-4 rounded-[4px] z-[10]",
          //  below widths are from ui/sidebar.tsx
          isSidebarOpen ? "left-[14rem]" : "left-12",
        )}
      >
        <div className="h-full flex flex-col">
          {isLoading ? (
            <ThenaLoader loaderText="Loading agent details..." />
          ) : (
            <>
              <AgentDetailsHeader
                agentId={agentId}
                closeDrawer={(_r) => closeDrawer(router)}
                agent={agentData}
              />

              {/* Agent Team Member Banner */}
              {showBanner && (
                <div className="mb-4 mt-2 p-4 rounded-sm border border-[var(--color-border-magic)] bg-[#f5f0ff] dark:bg-[#150f1f]">
                <div className="flex items-center justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0 text-4xl">
                      🧑‍💻
                    </div>
                    <div className="flex-1">
                      <h3 className="text-base font-medium mb-0.5">Your AI teammate is here</h3>
                      <p className="text-sm text-muted-foreground">
                        Tailor how they think, speak, and solve problems—set up their personality and expertise to match your needs.
                      </p>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <Button
                      variant="default"
                      size="sm"
                      className="bg-gradient-to-r from-[var(--brand-gradient-start)] to-[var(--brand-gradient-end)] hover:from-[var(--brand-gradient-hover-start)] hover:to-[var(--brand-gradient-hover-end)] text-white"
                      onClick={() => setIsSettingsOpen(true)}
                    >
                      Start configuring
                    </Button>
                  </div>
                </div>
              </div>
              )}

              <div className="flex-1 overflow-hidden">
                <PanelGroup direction="horizontal" className="flex gap-1">
                  <AgentDetailsLeftside agentId={agentId} />

                  {/* Three dots resize handle */}
                  <ResizeHandle />

                  {isRightSideOpen && (
                    <AgentDetailsRightside
                      agentId={agentId}
                      setIsRightSideOpen={setIsRightSideOpen}
                    />
                  )}
                </PanelGroup>

                {!isRightSideOpen && (
                  <div
                    className={`absolute right-0 top-1/4 transition-all duration-200 ${isExpanding ? "opacity-0" : "opacity-100"
                      }`}
                  >
                    <button
                      onClick={handleExpandPanel}
                      className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-l-md shadow-md hover:from-primary/90 hover:to-primary/70 transition-all flex flex-row items-center py-2 px-3 gap-2"
                      title="Show details panel"
                      aria-label="Show details panel"
                    >
                      <ChevronLeft className="h-4 w-4" />
                      <span className="text-xs font-medium">Details</span>
                    </button>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
      {agentData && (
        <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <DialogContent
            className="max-w-5xl max-h-[90vh] overflow-hidden p-0"
            hideClose={true}
          >
            <div className="h-full overflow-hidden">
              <div className="flex justify-between items-center px-6 py-3 bg-muted/70 border-b">
                <h2 className="text-lg font-medium">Agent configuration</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSettingsOpen(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="p-4 pt-6 overflow-auto h-[calc(90vh-80px)]">
                <SettingsTab agent={agentData} />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </HotkeysProvider>
  );
}
