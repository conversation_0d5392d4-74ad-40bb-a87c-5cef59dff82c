"use client";

import { TeamSelector } from "@/components/agents/TeamSelector";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Agent } from "@/types/agent";
import { Team } from "@/types/team";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  ChevronDown,
  Copy,
  LightbulbIcon,
  Loader2,
  Plus,
  Sparkles,
  X,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

// Define the structure of the expected API response
interface DeploymentResponse {
  id: string;
  agent_id: string;
  agent_key: string;
  org_id: string;
  team_id: string;
  allowed_origins: string[];
  created_at: string;
  updated_at: string;
  hmac_secret_key: string;
  widget_settings?: WidgetSettings;
}

interface WidgetSettings {
  targetElementId?: string;
  useCustomLauncher?: boolean;
  initialPosition?: {
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  };
  themeColorStart?: string;
  themeColorEnd?: string;
  gradientDirection?: string;
  brandLogoUrl?: string;
  userContextExample?: {
    email?: string;
    name?: string;
  };
  darkMode?: boolean;
  autoclose?: boolean;
}

interface WidgetConfig {
  baseUrl: string;
  apiKey: string;
  agentId: string;
  wsEndpoint: string;
  useCustomLauncher: boolean;
  user?: {
    email?: string;
    name?: string;
    hash?: string;
  };
  initialPosition?: {
    top: string;
    left: string;
    bottom: string;
    right: string;
  };
  targetElementId?: string;
  themeColorStart?: string;
  themeColorEnd?: string;
  gradientDirection?: string;
  brandLogoUrl?: string;
  darkMode?: boolean;
  autoclose?: boolean;
  [key: string]: unknown;
}

interface DeployTabProps {
  agent: Agent;
}

// Define function to fetch teams data
const fetchTeams = async (): Promise<Team[]> => {
  try {
    const response = await fetch(`/api/marketplace/teams`, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch teams");
    }

    const data = await response.json();

    if (data && Array.isArray(data.data)) {
      return data.data.map((team: Team | Record<string, unknown>) => ({
        id: team.id,
        uid: team.uid || team.id?.toString(),
        name: team.name,
        icon: team.icon,
        color: team.color,
        identifier: team.identifier,
        parentTeamId: team.parentTeamId,
        isPrivate: team.isPrivate,
        isActive: team.isActive,
      }));
    }

    return [];
  } catch (error) {
    console.error("Error fetching teams:", error);
    return [];
  }
};

export function DeployTab({ agent }: DeployTabProps) {
  const [_showChat, _setShowChat] = useState(false);
  const [_selectedOption, _setSelectedOption] = useState<
    "widget" | "react" | "webpage" | "slack" | "phone"
  >("widget");
  const [allowedDomains, setAllowedDomains] = useState<string[]>([]);
  const [deploymentData, setDeploymentData] =
    useState<DeploymentResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDeployments, setIsLoadingDeployments] = useState(true);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [deploymentMode, setDeploymentMode] = useState<"floating" | "embedded">(
    "floating",
  );
  const [userEmail, setUserEmail] = useState<string>("<EMAIL>");
  const [userName, setUserName] = useState<string>("Test User (HMAC)");

  // Widget specific settings states
  const [targetElementId, setTargetElementId] =
    useState<string>("my-thena-widget");
  const [useCustomLauncher, setUseCustomLauncher] = useState<boolean>(false);
  const [initialPositionTop, setInitialPositionTop] = useState<string>("auto");
  const [initialPositionLeft, setInitialPositionLeft] =
    useState<string>("auto");
  const [initialPositionBottom, setInitialPositionBottom] =
    useState<string>("20px");
  const [initialPositionRight, setInitialPositionRight] =
    useState<string>("20px");
  const [themeColorStart, setThemeColorStart] = useState<string>("");
  const [themeColorEnd, setThemeColorEnd] = useState<string>("");
  const [gradientDirection, setGradientDirection] = useState<string>("");
  const [brandLogoUrl, setBrandLogoUrl] = useState<string>("");
  const [darkMode, setDarkMode] = useState<boolean>(false);
  const [autoclose, setAutoclose] = useState<boolean>(false);

  // State for Chat Widget Instructions
  const [chatWidgetInstructions, setChatWidgetInstructions] =
    useState<string>("");
  const [isChatWidgetExpanded, setIsChatWidgetExpanded] = useState(false);
  const [isEditingChatWidgetInstructions, setIsEditingChatWidgetInstructions] =
    useState(false);
  const [isSavingChatWidgetInstructions, setIsSavingChatWidgetInstructions] =
    useState(false);

  // QueryClient for mutations
  const queryClient = useQueryClient();

  // Get Base URL and WS Endpoint from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_AGENT_STUDIO_URL;
  const wsEndpoint = process.env.NEXT_PUBLIC_AGENT_STUDIO_WS_URL;
  const widgetUrl = process.env.NEXT_PUBLIC_WIDGET_CDN_URL;

  // Initialize Chat Widget Instructions from agent configuration
  useEffect(() => {
    if (agent?.configuration?.chat_widget_instructions !== undefined) {
      setChatWidgetInstructions(
        agent.configuration.chat_widget_instructions || "",
      );
    }
  }, [agent, agent?.configuration?.chat_widget_instructions]);

  // Add teams query
  const { data: teamsData = [], isLoading: isLoadingTeams } = useQuery<Team[]>({
    queryKey: ["marketplace-teams"],
    queryFn: fetchTeams,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Fetch existing deployments when component mounts
  useEffect(() => {
    async function fetchDeployments() {
      setIsLoadingDeployments(true);
      try {
        const response = await fetch(`/api/agents/${agent.id}/deployments`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to fetch deployments", errorData);
          setIsLoadingDeployments(false);
          return;
        }

        const deployments = await response.json();

        // If we have deployments for this agent, use the first one
        if (deployments && deployments.length > 0) {
          const latestDeployment = deployments[0];
          setDeploymentData({
            ...latestDeployment,
            // Since the GET endpoint doesn't return hmac_secret_key, we'll set an empty one
            // The actual key is only shown once during creation
            // hmac_secret_key: "Secret key only shown during creation" // REMOVE THIS LINE
            // hmac_secret_key will now be part of latestDeployment from the API
          });

          // If the deployment has allowed origins, update state
          if (
            latestDeployment.allowed_origins &&
            latestDeployment.allowed_origins.length > 0
          ) {
            setAllowedDomains(latestDeployment.allowed_origins);
          }

          // Try to set selected team based on team_id if available and teams data is loaded
          if (latestDeployment.team_id && teamsData.length > 0) {
            // Find the actual team object that matches the team_id from deployment
            const matchingTeam = teamsData.find(
              (team) =>
                team.uid === latestDeployment.team_id ||
                team.id?.toString() === latestDeployment.team_id,
            );

            if (matchingTeam) {
              setSelectedTeam(matchingTeam);
            } else {
              // Fallback to placeholder if team not found
              setSelectedTeam({
                id: latestDeployment.team_id,
                name: "Deployment Team (Not Found)",
                uid: latestDeployment.team_id,
              } as Team);
            }
          }

          // Populate widget settings states from latestDeployment.widget_settings
          if (latestDeployment.widget_settings) {
            const settings = latestDeployment.widget_settings;
            setTargetElementId(settings.targetElementId || "my-thena-widget");
            setUseCustomLauncher(
              settings.useCustomLauncher === undefined
                ? false
                : settings.useCustomLauncher,
            );
            if (settings.initialPosition) {
              setInitialPositionTop(settings.initialPosition.top || "auto");
              setInitialPositionLeft(settings.initialPosition.left || "auto");
              setInitialPositionBottom(
                settings.initialPosition.bottom || "20px",
              );
              setInitialPositionRight(settings.initialPosition.right || "20px");
            }
            setThemeColorStart(settings.themeColorStart || "");
            setThemeColorEnd(settings.themeColorEnd || "");
            setGradientDirection(settings.gradientDirection || "");
            setBrandLogoUrl(settings.brandLogoUrl || "");
            setDarkMode(settings.darkMode || false);
            setAutoclose(settings.autoclose || false);
            if (settings.userContextExample) {
              setUserEmail(
                settings.userContextExample.email || "<EMAIL>",
              );
              setUserName(
                settings.userContextExample.name || "Test User (HMAC)",
              );
            }
            // Determine deploymentMode based on targetElementId presence in fetched settings
            if (settings.targetElementId) {
              setDeploymentMode("embedded");
            } else {
              setDeploymentMode("floating");
            }
          }
        }
      } catch (err) {
        console.error("Error fetching deployments:", err);
      } finally {
        setIsLoadingDeployments(false);
      }
    }

    // Only fetch deployments when we have teams data or if teams failed to load
    if (teamsData.length > 0 || !isLoadingTeams) {
      fetchDeployments();
    }
  }, [agent.id, baseUrl, wsEndpoint, teamsData, isLoadingTeams]);

  // Effect to manage useCustomLauncher based on deploymentMode
  useEffect(() => {
    if (deploymentMode === 'embedded') {
      setUseCustomLauncher(true);
    } 
    // For 'floating' mode, useCustomLauncher remains as set by the user/checkbox
  }, [deploymentMode]);

  const generateWidgetCode = (apiKey: string, agentId: string) => {
    const thenaWidgetConfig: WidgetConfig = {
      baseUrl: baseUrl || "http://localhost:8008",
      apiKey: apiKey,
      agentId: agentId,
      wsEndpoint: wsEndpoint || "ws://localhost:8008",
      useCustomLauncher: useCustomLauncher, // Will be false if not configured
    };

    if (userEmail || userName) {
      thenaWidgetConfig.user = {
        email: userEmail || undefined,
        name: userName || undefined,
        hash: "YOUR_SERVER_SIDE_GENERATED_HMAC_HASH", // Placeholder
      };
      if (!thenaWidgetConfig.user.email && !thenaWidgetConfig.user.name) {
        delete thenaWidgetConfig.user; // Clean up if both are empty
      } else {
        if (!thenaWidgetConfig.user.email) delete thenaWidgetConfig.user.email;
        if (!thenaWidgetConfig.user.name) delete thenaWidgetConfig.user.name;
      }
    }

    if (deploymentMode === "floating") {
      thenaWidgetConfig.initialPosition = {
        top: initialPositionTop,
        left: initialPositionLeft,
        bottom: initialPositionBottom,
        right: initialPositionRight,
      };
      // Defaults are handled by the widget itself, so only include if not default/empty
      // thenaWidgetConfig.dimensions = { width: dimensionsWidth, height: dimensionsHeight }; // Removed as per previous request
      // thenaWidgetConfig.animationStyle = animationStyle; // Removed as per previous request
    } else if (deploymentMode === "embedded") {
      if (targetElementId) {
        thenaWidgetConfig.targetElementId = targetElementId;
      }
      // When embedded, useCustomLauncher is implicitly true from the widget's perspective
      // The shim won't render its own launcher if targetElementId is present.
      // We reflect this by setting it to true in the generated config if embedded.
      thenaWidgetConfig.useCustomLauncher = true; 
    }

    if (themeColorStart) {
      thenaWidgetConfig.themeColorStart = themeColorStart;
    }
    if (themeColorEnd) {
      thenaWidgetConfig.themeColorEnd = themeColorEnd;
    }
    if (gradientDirection) {
      thenaWidgetConfig.gradientDirection = gradientDirection;
    }
    if (brandLogoUrl) {
      thenaWidgetConfig.brandLogoUrl = brandLogoUrl;
    }
    if (darkMode) {
      thenaWidgetConfig.darkMode = darkMode;
    }
    if (autoclose) {
      thenaWidgetConfig.autoclose = autoclose;
    }

    // Function to convert the object to a nicely formatted string for the script tag
    const configToString = (obj: Record<string, unknown>, indent = "  ") => {
      let result = "{\n";
      const keys = Object.keys(obj);
      keys.forEach((key, index) => {
        const value = obj[key];
        result += `${indent}  ${key}: `;
        if (typeof value === "string") {
          result += `'${value.replace(/'/g, "\\'")}'`;
        } else if (typeof value === "boolean" || typeof value === "number") {
          result += value;
        } else if (typeof value === "object" && value !== null) {
          result += configToString(
            value as Record<string, unknown>,
            `${indent}  `,
          ); // Recursive call for nested objects
        } else if (value === undefined) {
          // Skip undefined values
          result = result.substring(
            0,
            result.lastIndexOf(`${indent}  ${key}: `),
          ); // Remove the key line
          return; // and skip adding comma
        } else {
          result += "null";
        }
        if (
          index < keys.length - 1 &&
          !(
            value === undefined &&
            keys.slice(index + 1).every((k) => obj[k] === undefined)
          )
        ) {
          // Add comma if not the last real key
          const nextRealKeyIndex = keys.findIndex(
            (k, i) => i > index && obj[k] !== undefined,
          );
          if (nextRealKeyIndex !== -1) {
            result += ",\n";
          }
        } else if (value !== undefined) {
          result += "\n";
        }
      });
      result += `${indent.substring(0, indent.length - 2)}}`;
      return result;
    };

    const configString = configToString(thenaWidgetConfig);

    return `<script>\n  window.thenaWidget = ${configString};\n</script>\n<script src="${(
      widgetUrl || "https://widget.thena.tools/shim.js"
    ).replace(/'/g, "\\'")}"></script>`;
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        console.log(`${type} copied to clipboard.`);
        toast.success(`${type} copied to clipboard!`);
      },
      (err) => {
        console.error(`Could not copy ${type}: ${err}`);
        toast.error(`Failed to copy ${type} to clipboard.`);
      },
    );
  };

  const handleDomainChange = (index: number, value: string) => {
    const newDomains = [...allowedDomains];
    newDomains[index] = value;
    setAllowedDomains(newDomains);
  };

  const addDomain = () => {
    setAllowedDomains([...allowedDomains, ""]);
  };

  const removeDomain = (index: number) => {
    setAllowedDomains(allowedDomains.filter((_, i) => i !== index));
  };

  const handleDeploy = async () => {
    setIsLoading(true);
    const teamId = selectedTeam?.uid || selectedTeam?.id?.toString();

    if (!teamId) {
      toast.error("Please select a Team before deploying.");
      setIsLoading(false);
      return;
    }

    const validDomains = allowedDomains.filter(
      (domain) => domain.trim() !== "",
    );

    const widgetSettingsForApi: WidgetSettings = {
      useCustomLauncher: useCustomLauncher,
      themeColorStart: themeColorStart || undefined,
      themeColorEnd: themeColorEnd || undefined,
      gradientDirection: gradientDirection || undefined,
      brandLogoUrl: brandLogoUrl || undefined,
      userContextExample: {
        email: userEmail || undefined,
        name: userName || undefined,
      },
      darkMode: darkMode,
      autoclose: autoclose,
    };

    if (deploymentMode === "floating") {
      widgetSettingsForApi.initialPosition = {
        top: initialPositionTop,
        left: initialPositionLeft,
        bottom: initialPositionBottom,
        right: initialPositionRight,
      };
    } else if (deploymentMode === "embedded") {
      widgetSettingsForApi.targetElementId = targetElementId;
    }

    // Remove userContextExample if both fields are empty
    if (
      !widgetSettingsForApi.userContextExample.email &&
      !widgetSettingsForApi.userContextExample.name
    ) {
      delete widgetSettingsForApi.userContextExample;
    }

    try {
      const response = await fetch(`/api/agents/${agent.id}/deployments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          team_id: teamId,
          allowed_origins: validDomains,
          deployment_type: "widget",
          widget_settings: widgetSettingsForApi,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Deployment failed");
      }

      toast.success(
        "Deployment successful! Your widget configuration is ready.",
      );
      setDeploymentData(result);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred.";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Mutation to update agent configuration (specifically for chat widget instructions)
  const updateAgentConfigMutation = useMutation({
    mutationFn: async (newInstructions: string) => {
      if (!agent?.id) throw new Error("Agent data not available for update.");

      const updatedConfiguration = {
        ...agent.configuration,
        chat_widget_instructions: newInstructions,
      };

      const response = await fetch(`/api/agents/${agent.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: agent.name,
          status: agent.status,
          configuration: updatedConfiguration,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to update agent configuration",
        );
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success("Chat widget instructions saved successfully.");
      queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
      setIsEditingChatWidgetInstructions(false);
      setIsSavingChatWidgetInstructions(false);
    },
    onError: (error: Error) => {
      toast.error(`Error saving instructions: ${error.message}`);
      setChatWidgetInstructions(
        agent.configuration?.chat_widget_instructions || "",
      );
      setIsSavingChatWidgetInstructions(false);
    },
  });

  // Handler to save chat widget instructions
  const handleSaveChatWidgetInstructions = () => {
    setIsSavingChatWidgetInstructions(true);
    updateAgentConfigMutation.mutate(chatWidgetInstructions);
  };

  // Toggle chat widget expanded state
  const toggleChatWidgetExpanded = () => {
    setIsChatWidgetExpanded(!isChatWidgetExpanded);
  };

  return (
    <div className="space-y-6 pb-24">
      {/* Deployment Heading */}
      <div>
        <h3 className="text-base font-medium">Website Deployment</h3>
      </div>

      {/* TODO: Re-enable later - Other deployment options
        <Card>React Components</Card>
        <Card>Hosted Webpage</Card>
        <Card>Slack</Card>
        <Card>Phone</Card>
        */}

      <div className="space-y-4">
        {/* Configure Website Deployment Section - Top */}
        <Card style={{ maxHeight: "800px", overflowY: "auto" }}>
          <CardHeader>
            <CardTitle className="text-sm font-medium">
              Configure website deployment
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-2">
              <Label>Deployment Mode</Label>
              <RadioGroup
                value={deploymentMode}
                onValueChange={(value: "floating" | "embedded") => {
                  setDeploymentMode(value);
                  if (value === 'embedded') {
                    setUseCustomLauncher(true);
                    setAutoclose(false); // Also set autoclose to false when embedded
                  }
                }}
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="floating" id="mode-floating" />
                  <Label htmlFor="mode-floating" className="font-normal">
                    Floating Widget
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="embedded" id="mode-embedded" />
                  <Label htmlFor="mode-embedded" className="font-normal">
                    Embed in my page
                  </Label>
                </div>
              </RadioGroup>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="team">Select team</Label>
              <TeamSelector
                selectedTeams={selectedTeam ? [selectedTeam] : []}
                onSelect={(teams) => {
                  setSelectedTeam(teams[0] || null);
                }}
                disabled={isLoading || isLoadingDeployments || isLoadingTeams}
              />
            </div>
            <div>
              <h4 className="text-sm font-medium">
                User Context (Example for Snippet)
              </h4>
              <p className="text-sm text-muted-foreground pt-1 pb-3">
                Provide example user details for the generated widget snippet.
                Your actual user data should be populated by your website.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1.5">
                  <Label htmlFor="user-email-example">Example User Email</Label>
                  <Input
                    id="user-email-example"
                    type="email"
                    value={userEmail}
                    onChange={(e) => setUserEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    disabled={isLoading || isLoadingDeployments}
                  />
                </div>
                <div className="space-y-1.5">
                  <Label htmlFor="user-name-example">Example User Name</Label>
                  <Input
                    id="user-name-example"
                    type="text"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    placeholder="Test User (HMAC)"
                    disabled={isLoading || isLoadingDeployments}
                  />
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium">Allowed origins</h4>
              <p className="text-sm text-muted-foreground pt-1 pb-3">
                Enter the full domains (including http/https) where your widget
                will be hosted. This helps secure your deployment.
                {/* TODO: Add documentation link when available
                <a href="#" className="text-primary hover:underline ml-1">
                  Learn more
                </a>
                */}
              </p>
              <div className="space-y-3">
                {allowedDomains.map((domain, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      type="text"
                      value={domain}
                      onChange={(e) =>
                        handleDomainChange(index, e.target.value)
                      }
                      placeholder="https://www.example.com"
                      className="flex-grow"
                      disabled={isLoading || isLoadingDeployments}
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeDomain(index)}
                      aria-label="Remove domain"
                      disabled={isLoading || isLoadingDeployments}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={addDomain}
                className=""
                disabled={isLoading || isLoadingDeployments}
              >
                <Plus className="h-4 w-4" />
                Add origin
              </Button>
            </div>

            {/* Conditional UI for Floating mode */}
            {deploymentMode === "floating" && (
              <>
                <div>
                  <h4 className="text-sm font-medium">
                    Initial Position (Floating)
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2 items-baseline">
                    <div className="space-y-1.5">
                      <Label htmlFor="pos-top">Top (e.g., 20px, auto)</Label>
                      <Input
                        id="pos-top"
                        value={initialPositionTop}
                        onChange={(e) => setInitialPositionTop(e.target.value)}
                        disabled={isLoading || isLoadingDeployments}
                      />
                    </div>
                    <div className="space-y-1.5">
                      <Label htmlFor="pos-left">Left (e.g., 20px, auto)</Label>
                      <Input
                        id="pos-left"
                        value={initialPositionLeft}
                        onChange={(e) => setInitialPositionLeft(e.target.value)}
                        disabled={isLoading || isLoadingDeployments}
                      />
                    </div>
                    <div className="space-y-1.5">
                      <Label htmlFor="pos-bottom">
                        Bottom (e.g., 20px, auto)
                      </Label>
                      <Input
                        id="pos-bottom"
                        value={initialPositionBottom}
                        onChange={(e) =>
                          setInitialPositionBottom(e.target.value)
                        }
                        disabled={isLoading || isLoadingDeployments}
                      />
                    </div>
                    <div className="space-y-1.5">
                      <Label htmlFor="pos-right">
                        Right (e.g., 20px, auto)
                      </Label>
                      <Input
                        id="pos-right"
                        value={initialPositionRight}
                        onChange={(e) =>
                          setInitialPositionRight(e.target.value)
                        }
                        disabled={isLoading || isLoadingDeployments}
                      />
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Conditional UI for Embedded mode */}
            {deploymentMode === "embedded" && (
              <div>
                <h4 className="text-sm font-medium">
                  Embedded Widget Configuration
                </h4>
                <div className="space-y-1.5 mt-2">
                  <Label htmlFor="target-element-id">Target Element ID</Label>
                  <Input
                    id="target-element-id"
                    value={targetElementId}
                    onChange={(e) => setTargetElementId(e.target.value)}
                    placeholder="div-id-to-embed-widget"
                    disabled={isLoading || isLoadingDeployments}
                  />
                  <p className="text-xs text-muted-foreground">
                    The ID of the div on your page where the widget will be
                    rendered.
                  </p>
                </div>
              </div>
            )}

            {/* Common Customization Options */}
            <div>
              <h4 className="text-sm font-medium">Customization</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                <div className="space-y-1.5">
                  <Label htmlFor="theme-color-start">Theme Color Start (hex)</Label>
                  <Input
                    id="theme-color-start"
                    value={themeColorStart}
                    onChange={(e) => setThemeColorStart(e.target.value)}
                    placeholder="#RRGGBB (e.g. #8A2BE2)"
                    disabled={isLoading || isLoadingDeployments}
                  />
                </div>
                <div className="space-y-1.5">
                  <Label htmlFor="theme-color-end">Theme Color End (hex)</Label>
                  <Input
                    id="theme-color-end"
                    value={themeColorEnd}
                    onChange={(e) => setThemeColorEnd(e.target.value)}
                    placeholder="#RRGGBB (e.g. #32CD32)"
                    disabled={isLoading || isLoadingDeployments}
                  />
                </div>
                <div className="space-y-1.5 md:col-span-2">
                  <Label htmlFor="gradient-direction">Gradient Direction</Label>
                  <Input
                    id="gradient-direction"
                    value={gradientDirection}
                    onChange={(e) => setGradientDirection(e.target.value)}
                    placeholder="e.g., to bottom right, 45deg"
                    disabled={isLoading || isLoadingDeployments}
                  />
                   <p className="text-xs text-muted-foreground pt-1">
                    Examples: <code>to right</code>, <code>to bottom left</code>, <code>45deg</code>, <code>135deg</code>. Default is <code>135deg</code>. If Theme Color End is empty or same as Start, a solid color (Start) will be used.
                  </p>
                </div>
                <div className="space-y-1.5">
                  <Label htmlFor="brand-logo-url">Brand Logo URL</Label>
                  <Input
                    id="brand-logo-url"
                    type="url"
                    value={brandLogoUrl}
                    onChange={(e) => setBrandLogoUrl(e.target.value)}
                    placeholder="https://example.com/logo.svg"
                    disabled={isLoading || isLoadingDeployments}
                  />
                </div>
                <div className="flex items-center space-x-2 pt-4 col-span-1 md:col-span-2">
                  <Checkbox
                    id="use-custom-launcher"
                    checked={useCustomLauncher}
                    onCheckedChange={(checkedState) =>
                      setUseCustomLauncher(
                        checkedState === true ||
                          checkedState === "indeterminate"
                          ? true
                          : false,
                      )
                    }
                    disabled={isLoading || isLoadingDeployments}
                  />
                  <Label
                    htmlFor="use-custom-launcher"
                    className="font-normal cursor-pointer"
                  >
                    Use Custom Launcher Button
                  </Label>
                </div>
                <div className="flex items-center space-x-2 pt-4 col-span-1 md:col-span-2">
                  <Checkbox
                    id="dark-mode"
                    checked={darkMode}
                    onCheckedChange={(checkedState) =>
                      setDarkMode(checkedState === true)
                    }
                    disabled={isLoading || isLoadingDeployments}
                  />
                  <Label
                    htmlFor="dark-mode"
                    className="font-normal cursor-pointer"
                  >
                    Initial Dark Mode
                  </Label>
                </div>
                {/* Autoclose Checkbox - Conditional Rendering */}
                {deploymentMode === 'floating' && (
                  <div className="flex items-center space-x-2 pt-4 col-span-1 md:col-span-2">
                    <Checkbox
                      id="autoclose-widget"
                      checked={autoclose}
                      onCheckedChange={(checkedState) =>
                        setAutoclose(checkedState === true)
                      }
                      disabled={isLoading || isLoadingDeployments}
                    />
                    <Label
                      htmlFor="autoclose-widget"
                      className="font-normal cursor-pointer"
                    >
                      Auto-close widget on outside click
                    </Label>
                  </div>
                )}
              </div>
              {/* API information - now always visible */}
              <div className="mt-6 p-4 border rounded-md bg-muted/50 text-sm text-muted-foreground space-y-2">
                <p className="font-medium text-foreground mb-1">
                  Using the Widget JavaScript API:
                </p>
                <p>
                  You can control the widget programmatically using the following JavaScript functions on the <code>window.thena</code> object:
                </p>
                <ul className="list-disc pl-5 my-2 space-y-1">
                  <li>
                    <code>window.thena.open()</code> - Opens the widget.
                  </li>
                  <li>
                    <code>window.thena.close()</code> - Closes the widget.
                  </li>
                  <li>
                    <code>window.thena.toggle()</code> - Toggles the widget
                    visibility.
                  </li>
                  <li>
                    <code>window.thena.toggleDarkMode()</code> - Toggles between light and dark themes.
                  </li>
                  <li>
                    <code>window.thena.toggleVisibility()</code> - Shows/hides the widget and its launcher (if default launcher is used).
                  </li>
                </ul>
                <p>
                  Example (for a custom button with ID &apos;myChatButton&apos; to open the chat):
                </p>
                <pre className="mt-1 p-2 rounded-sm bg-background font-mono text-xs overflow-x-auto">
                  {`document.getElementById('myChatButton').addEventListener('click', function() {\n  if (window.thena && window.thena.open) {\n    window.thena.open();\n  }\n});`}
                </pre>
              </div>
            </div>

            <div className="">
              <Button
                onClick={handleDeploy}
                disabled={isLoading || isLoadingDeployments}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {deploymentData ? "Redeploy" : "Deploy"}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Add Chat Widget Section - Bottom */}
        <Card className="p-6">
          <div className="mb-4">
            <h4 className="text-sm font-medium">
              Add chat widget to your website
            </h4>
            <p className="text-sm text-muted-foreground mt-1">
              Copy the code below and add it to your website.
            </p>
          </div>
          {isLoadingDeployments ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-sm text-muted-foreground">
                Loading deployment configuration...
              </span>
            </div>
          ) : !deploymentData ? (
            <div className="space-y-4">
              <div className="relative">
                <pre className="p-4 rounded-sm bg-muted font-mono text-sm overflow-x-auto">
                  {generateWidgetCode("YOUR_API_KEY", agent.id)}
                </pre>
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute top-2 right-2"
                  onClick={() =>
                    copyToClipboard(
                      generateWidgetCode("YOUR_API_KEY", agent.id),
                      "Widget code",
                    )
                  }
                >
                  <Copy className="h-4 w-4" />
                  <span className="sr-only">Copy code</span>
                </Button>
              </div>
              <div className="text-sm text-muted-foreground">
                Add this code to your website&apos;s HTML, just before the
                closing &lt;/body&gt; tag. Customize the appearance and behavior
                using the configuration options.
                {/* TODO: Add documentation link when available
                    <a href="#" className="text-primary ml-1 hover:underline">
                      View documentation
                      <ExternalLink className="h-3 w-3 inline-block ml-1" />
                    </a>
                    */}
              </div>
            </div>
          ) : (
            <div className="space-y-6 overflow-y-auto">
              <div>
                <div className="relative">
                  <pre className="p-4 rounded-sm bg-muted font-mono text-sm overflow-x-auto">
                    {generateWidgetCode(
                      deploymentData.agent_key,
                      deploymentData.agent_id,
                    )}
                  </pre>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute top-2 right-2"
                    onClick={() =>
                      copyToClipboard(
                        generateWidgetCode(
                          deploymentData.agent_key,
                          deploymentData.agent_id,
                        ),
                        "Widget code",
                      )
                    }
                  >
                    <Copy className="h-4 w-4" />
                    <span className="sr-only">Copy widget code</span>
                  </Button>
                </div>
              </div>

              <div className="mt-6 space-y-2">
                <div>
                  <label
                    className="text-sm font-medium"
                    htmlFor="hmac-key-display"
                  >
                    HMAC secret key
                  </label>
                  <p className="text-sm text-muted-foreground mt-1">
                    {
                      "Store this key securely on your server. It is used to authenticate user context data and should NEVER be exposed client-side."
                    }
                  </p>
                </div>
                <div className="relative flex items-center">
                  <Input
                    id="hmac-key-display"
                    readOnly
                    value={deploymentData.hmac_secret_key}
                    className="font-mono text-xs pr-10 bg-background"
                  />
                  {deploymentData.hmac_secret_key && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className="absolute right-1 h-7 w-7 p-0"
                      onClick={() =>
                        copyToClipboard(
                          deploymentData.hmac_secret_key,
                          "HMAC Secret Key",
                        )
                      }
                    >
                      <Copy className="h-3.5 w-3.5" />
                      <span className="sr-only">Copy HMAC Secret Key</span>
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* TODO: Re-enable later
        {selectedOption === "react" && (
          <Card className="p-6 col-span-2">
            <h4 className="text-sm font-medium mb-4">Use React Components</h4>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Install our React components package and integrate the agent
                into your React/Next.js application.
              </div>
              <div className="relative">
                <pre className="p-4 rounded-sm bg-muted font-mono text-sm">
                  npm install @thena/react-components
                </pre>
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute top-2 right-2"
                  onClick={() =>
                    copyToClipboard("npm install @thena/react-components", "NPM Command")
                  }
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <a
                href="#"
                className="text-primary text-sm hover:underline inline-flex items-center"
              >
                View documentation
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
          </Card>
        )}

        {selectedOption === "webpage" && (
          <Card className="p-6 col-span-2">
            <h4 className="text-sm font-medium mb-4">Get a Hosted Webpage</h4>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Your agent is available at:
              </div>
              <div className="relative">
                <pre className="p-4 rounded-sm bg-muted font-mono text-sm">
                  https://chat.thena.ai/{agent.id}
                </pre>
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute top-2 right-2"
                  onClick={() =>
                    copyToClipboard(`https://chat.thena.ai/${agent.id}`, "Webpage URL")
                  }
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <div className="text-sm text-muted-foreground">
                Want to use your own domain?
                <a href="#" className="text-primary ml-1 hover:underline">
                  Set up custom domain
                  <ExternalLink className="h-3 w-3 inline-block ml-1" />
                </a>
              </div>
            </div>
          </Card>
        )}

        {selectedOption === "slack" && (
          <Card className="p-6 col-span-2">
            <h4 className="text-sm font-medium mb-4">Add to Slack</h4>
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <Button className="w-full sm:w-auto">
                  <Image
                    src="/slack-logo.svg"
                    alt="Slack"
                    className="h-4 w-4 mr-2"
                    width={16}
                    height={16}
                  />
                  Add to Slack
                </Button>
                <div className="text-sm text-muted-foreground">or</div>
                <Button variant="outline" className="w-full sm:w-auto">
                  Install manually
                </Button>
              </div>
              <div className="text-sm text-muted-foreground">
                Deploy this agent as a Slack bot. Your team can interact with it
                directly in Slack channels or via direct messages.
              </div>
              <div>
                <h5 className="text-sm font-medium mb-2">Features</h5>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li>• Direct messages with the bot</li>
                  <li>• Mention the bot in channels</li>
                  <li>• Rich message formatting</li>
                  <li>• File attachments support</li>
                  <li>• Thread conversations</li>
                </ul>
              </div>
            </div>
          </Card>
        )}

        {selectedOption === "phone" && (
          <Card className="p-6 col-span-2">
            <h4 className="text-sm font-medium mb-4">Get a phone number</h4>
            <div className="space-y-6">
              <div className="grid gap-4">
                <div>
                  <label className="text-sm font-medium mb-1.5 block">
                    Select country
                  </label>
                  <select className="w-full rounded-sm border border-input bg-background px-3 py-2 text-sm">
                    <option value="US">United States (+1)</option>
                    <option value="GB">United Kingdom (+44)</option>
                    <option value="IN">India (+91)</option>
                    <option value="CA">Canada (+1)</option>
                    <option value="AU">Australia (+61)</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-1.5 block">
                    Area code (optional)
                  </label>
                  <Input type="text" placeholder="e.g. 415" />
                </div>
                <div>
                  <label className="text-sm font-medium mb-1.5 block">
                    Phone number type
                  </label>
                  <select className="w-full rounded-sm border border-input bg-background px-3 py-2 text-sm">
                    <option value="local">Local number</option>
                    <option value="tollfree">Toll-free number</option>
                    <option value="mobile">Mobile number</option>
                  </select>
                </div>
              </div>

              <Button className="w-full sm:w-auto">Get phone number</Button>
            </div>
          </Card>
        )}
        */}
      </div>

      {/* Widget Configuration - always rendered in deploy tab */}
      <div className="border rounded-sm overflow-hidden transition-all hover:shadow-sm">
        {/* AccordionTrigger style header */}
        <div
          className="px-4 py-3 hover:bg-muted/50 transition-colors cursor-pointer"
          onClick={toggleChatWidgetExpanded}
        >
          <div className="flex items-start w-full">
            {/* Name and description - main content */}
            <div className="flex-1 min-w-0 text-left">
              <div className="flex flex-col">
                <div className="flex items-center">
                  <h3 className="text-sm font-medium truncate">
                    Widget configuration
                  </h3>
                </div>
                <p className="text-sm font-normal text-muted-foreground line-clamp-1">
                  Configure how the agent behaves specifically within the chat
                  widget.
                </p>

                {/* Quick action buttons */}
                <div className="flex items-center space-x-2 mt-3">
                  <button
                    className="h-7 px-2.5 flex items-center justify-center gap-1.5 rounded-sm border border-purple-200 bg-purple-50 hover:bg-purple-100 text-purple-600 dark:border-purple-900/50 dark:bg-purple-900/20 dark:text-purple-400 dark:hover:bg-purple-900/30 transition-colors text-xs font-medium"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleChatWidgetExpanded();
                    }}
                    aria-label="Customize widget configuration"
                  >
                    <Sparkles className="h-3.5 w-3.5" />
                    Customize instructions
                  </button>
                </div>
              </div>
            </div>

            {/* Custom chevron positioned at the top right */}
            <div className="pt-1">
              <ChevronDown
                className={`h-4 w-4 shrink-0 transition-transform duration-200 ${
                  isChatWidgetExpanded ? "rotate-180" : ""
                }`}
              />
            </div>
          </div>
        </div>

        {/* AccordionContent style expanded content with proper animation */}
        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out ${
            isChatWidgetExpanded ? "max-h-[2000px] border-t" : "max-h-0"
          }`}
        >
          <div className="px-4 pb-4 pt-2 animate-in fade-in-0 slide-in-from-t-1 duration-500">
            <div className="space-y-4">
              {/* Instructions Section */}
              <div className="mt-2">
                <div className="flex items-center justify-between mb-2">
                  <label
                    htmlFor="widget-configuration"
                    className="text-sm font-medium flex items-center"
                  >
                    <Sparkles className="h-4 w-4 mr-2 text-purple-500" />
                    Custom instructions
                  </label>
                  {/* Example button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toast.info("Sample templates coming soon!");
                    }}
                    className="text-xs flex items-center gap-1 text-purple-500 hover:text-purple-600 dark:text-purple-400 dark:hover:text-purple-300 transition-colors"
                  >
                    <LightbulbIcon className="h-3.5 w-3.5" />
                    View examples
                  </button>
                </div>
                <textarea
                  id="widget-configuration"
                  className="w-full p-3 text-sm border rounded-sm resize-y min-h-[100px] bg-background focus:ring-1 focus:ring-primary/30 focus:border-primary/30 outline-none transition-colors"
                  value={chatWidgetInstructions}
                  onChange={(e) => {
                    setChatWidgetInstructions(e.target.value);
                    setIsEditingChatWidgetInstructions(true);
                  }}
                  placeholder="Enter custom instructions to guide the AI's behavior in the widget..."
                />

                {/* Helper text */}
                <div className="p-3 border rounded-sm bg-muted/30 mt-4">
                  <p className="text-sm text-muted-foreground">
                    These instructions help guide how the agent responds when
                    users interact with it through the widget. Be specific about
                    tone, response length, and when to escalate or create
                    tickets.
                  </p>
                </div>

                {/* Save button displayed when edited */}
                {isEditingChatWidgetInstructions && (
                  <div className="flex justify-end mt-3">
                    <button
                      className="text-xs px-3 h-7 rounded-sm border border-primary bg-primary hover:bg-primary/90 text-primary-foreground transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSaveChatWidgetInstructions();
                      }}
                      disabled={isSavingChatWidgetInstructions}
                      aria-label="Save widget configuration"
                    >
                      {isSavingChatWidgetInstructions
                        ? "Saving..."
                        : "Save changes"}
                    </button>
                  </div>
                )}
              </div>

              {/* Simplified Metadata */}
              <div className="text-sm text-muted-foreground mt-4 flex items-center">
                <span>
                  Last updated:{" "}
                  {agent?.updated_at
                    ? new Date(agent.updated_at).toLocaleDateString()
                    : "N/A"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
