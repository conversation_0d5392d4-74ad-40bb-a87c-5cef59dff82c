import { TeamSelector } from "@/components/agents/TeamSelector";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { getAgentAvatarUrl } from "@/lib/utils";
import { AgentTemplate } from "@/types/agent";
import { Team } from "@/types/team";
import {
  ArrowRight,
  Bot,
  BrainCircuit,
  Check,
  FileText,
  MessageSquare,
  Pencil,
  Search,
  Star,
  Tag,
  TicketCheck,
} from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { AgentHireSuccessCard } from "./AgentHireSuccessCard";

interface AgentDetailsModalProps {
  agent: AgentTemplate | null;
  onClose: () => void;
  onHire: (agent: AgentTemplate, teams: Team[]) => void;
  isInstalled?: boolean;
}

export function AgentDetailsModal({
  agent: _agent,
  onClose,
  onHire,
  isInstalled = false,
}: AgentDetailsModalProps) {
  const [showTeamSelector, setShowTeamSelector] = useState(false);
  const [selectedTeams, setSelectedTeams] = useState<Team[]>([]);
  const [isHiring, setIsHiring] = useState(false);
  const [animateCapabilities, setAnimateCapabilities] = useState(false);
  const [animateOnboarding, setAnimateOnboarding] = useState(false);
  const [showSuccessCard, setShowSuccessCard] = useState(false);

  useEffect(() => {
    // Stagger the animations
    const capabilitiesTimeout = setTimeout(() => {
      setAnimateCapabilities(true);
    }, 300);
    
    const onboardingTimeout = setTimeout(() => {
      setAnimateOnboarding(true);
    }, 800);
    
    return () => {
      clearTimeout(capabilitiesTimeout);
      clearTimeout(onboardingTimeout);
    };
  }, []);

  if (!_agent) return null;

  const _avatarUrl = getAgentAvatarUrl(_agent.avatar_url);

  const handleHireClick = async () => {
    if (showTeamSelector) {
      if (selectedTeams.length === 0) {
        // Show error or toast message
        return;
      }
      setIsHiring(true);
      try {
        await onHire(_agent, selectedTeams);
        // Show success card instead of resetting the team selector
        setShowSuccessCard(true);
      } catch (error) {
        console.error("Error hiring agent:", error);
        // Reset on error
        setShowTeamSelector(false);
        setSelectedTeams([]);
      } finally {
        setIsHiring(false);
      }
    } else {
      setShowTeamSelector(true);
    }
  };

  const handleConfigureAgent = (_agent: AgentTemplate) => {
    // Close both modals and navigate to configuration
    setShowSuccessCard(false);
    onClose();
    // For now, we'll just close the modal
    // Later we can implement navigation to the configuration page
  };

  // Show success card if hiring was successful
  if (showSuccessCard) {
    return (
      <AgentHireSuccessCard
        agent={_agent}
        teams={selectedTeams}
        onClose={() => {
          setShowSuccessCard(false);
          setShowTeamSelector(false);
          setSelectedTeams([]);
          onClose();
        }}
        onConfigure={handleConfigureAgent}
        open={showSuccessCard}
      />
    );
  }

  return (
    <Dialog open={!!_agent} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-3xl p-0 max-h-[90vh] flex flex-col">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-4">
            <div className="h-16 w-16 rounded-sm bg-muted overflow-hidden flex-shrink-0 relative">
              {_agent.avatar_url ? (
                <Image
                  src={getAgentAvatarUrl(_agent.avatar_url)}
                  alt={_agent.name}
                  className="w-full h-full object-cover"
                  layout="fill"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-xl font-medium bg-muted text-muted-foreground">
                  {_agent.name.slice(0, 2).toUpperCase()}
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3">
                <h2 className="text-xl font-medium tracking-tight">
                  {_agent.name}
                </h2>
                {_agent.metadata.rating && (
                  <Badge
                    variant="outline"
                    className="flex items-center gap-1 text-xs font-medium"
                  >
                    <Star className="w-3 h-3 fill-current text-yellow-500" />
                    <span>{_agent.metadata.rating.toFixed(1)}</span>
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {_agent.category || "Agent"}
              </p>
            </div>
          </div>
          {/* Default DialogContent close button will be used */}
        </div>

        <div className="px-6 pb-6 space-y-6 overflow-y-auto flex-1">
          <div className={`transition-all duration-500 ease-in-out transform ${animateCapabilities ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <h3 className="text-base font-medium">About</h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {_agent.description.replace(/'/g, "&apos;")}
            </p>
          </div>

          {_agent.metadata?.tags && _agent.metadata.tags.length > 0 && (
            <div className={`transition-all duration-500 ease-in-out transform ${animateCapabilities ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <h3 className="text-base font-medium mb-3">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {_agent.metadata.tags.map((tag) => (
                  <Badge key={tag} variant="secondary">
                    <Tag className="w-3 h-3 mr-1.5" />
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {_agent.metadata?.capabilities &&
            _agent.metadata.capabilities.length > 0 && (
              <div className={`transition-all duration-500 ease-in-out transform ${animateCapabilities ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
                <h3 className="text-base font-medium mb-2">Capabilities</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {_agent.metadata.capabilities.map((capability, index) => (
                    <div
                      key={capability}
                      className="flex items-center gap-4 p-4 rounded-sm border bg-muted/30 transition-all duration-300 ease-in-out transform"
                      style={{ 
                        opacity: animateCapabilities ? 1 : 0,
                        transform: animateCapabilities ? 'translateY(0)' : 'translateY(10px)',
                        transitionDelay: `${index * 100}ms`
                      }}
                    >
                      <Check className="w-5 h-5 text-slate-600 dark:text-slate-300" />
                      <span>{capability}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

          {_agent.metadata?.activities && _agent.metadata.activities.length > 0 && (
            <div className={`transition-all duration-500 ease-in-out transform ${animateCapabilities ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <h3 className="text-base font-medium">Agent&apos;s capabilities</h3>
              <p className="text-sm text-muted-foreground mb-4">
                These are the specialized functions this agent can perform to help with your tasks.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {_agent.metadata.activities.map((activity, index) => {
                  // Determine icon based on activity name
                  const activityName = activity.name.toLowerCase();
                  let Icon = BrainCircuit; // Default icon

                  if (activityName.includes('ticket') && activityName.includes('summary')) {
                    Icon = FileText;
                  } else if (activityName.includes('title') || activityName.includes('description')) {
                    Icon = Pencil;
                  } else if (activityName.includes('search')) {
                    Icon = Search;
                  } else if (activityName.includes('deflection')) {
                    Icon = MessageSquare;
                  } else if (activityName.includes('status')) {
                    Icon = TicketCheck;
                  } else if (activityName.includes('bot') || activityName.includes('chat')) {
                    Icon = Bot;
                  }

                  // Format activity name to sentence case
                  const formattedActivityName = activity.name
                    .replace(/_/g, " ")
                    .replace(/\b\w/g, (char) => char.toUpperCase())
                    .replace(/Ai/g, "AI");

                  return (
                    <div
                      key={activity.name}
                      className="flex items-start gap-4 p-4 rounded-sm border bg-muted/30 transition-all duration-300 ease-in-out transform"
                      style={{ 
                        opacity: animateCapabilities ? 1 : 0,
                        transform: animateCapabilities ? 'translateY(0)' : 'translateY(10px)',
                        transitionDelay: `${(index + 4) * 100}ms`
                      }}
                    >
                      <div className="flex items-center justify-center w-10 h-10 rounded-sm bg-muted">
                        <Icon className="w-5 h-5 text-slate-600 dark:text-slate-300" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-sm mb-1">{formattedActivityName}</h4>
                        <p className="text-xs text-muted-foreground">
                          {activity.description.replace(/&/g, '&amp;')}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {_agent.flows && _agent.flows.length > 0 && (
            <div className={`transition-all duration-500 ease-in-out transform ${animateCapabilities ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <h3 className="text-base font-medium mb-3">
                Example workflows
              </h3>
              <div className="space-y-3">
                {_agent.flows.map((flow) => (
                  <div
                    key={flow.id}
                    className="p-2 rounded-sm border bg-muted/30"
                  >
                    <h4 className="font-medium text-sm mb-1">{flow.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {flow.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Hire Agent Section */}
          {!isInstalled && (
            <div className={`transition-all duration-500 ease-in-out transform ${animateOnboarding ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
              <h3 className="text-base font-medium mb-3 mt-6">
                Bring this agent on board
              </h3>

              <div className="flex items-center mb-4">
                <div className={`transition-all duration-300 ease-in-out ${showTeamSelector ? 'flex-1 opacity-100' : 'w-0 opacity-0'} overflow-hidden mr-3`}>
                  {showTeamSelector && (
                    <TeamSelector
                      selectedTeams={selectedTeams}
                      onSelect={setSelectedTeams}
                      disabled={isHiring}
                    />
                  )}
                </div>
                <Button
                  onClick={handleHireClick}
                  variant={showTeamSelector ? "default" : "gradient"}
                  disabled={showTeamSelector && (isHiring || selectedTeams.length === 0)}
                  className="flex items-center gap-2"
                >
                  {showTeamSelector
                    ? (isHiring ? "Hiring..." : "Add to teams")
                    : "Hire agent"}
                  {!showTeamSelector && <ArrowRight className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
