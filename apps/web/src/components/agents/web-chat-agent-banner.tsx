"use client";

import { ArrowRight, SparklesIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import { Button } from "../ui/button";

const AgentInfoWrapper = styled.div`
  padding: 16px;
`;
export const WebChatAgentBanner = ({ AGENT_NAME, agentDetails }) => {
  const router = useRouter();
  return (
    <AgentInfoWrapper className="flex items-center justify-between gap-2 bg-[var(--color-bg-magic-muted)] rounded-sm">
      <SparklesIcon className="w-6 h-6 text-color-border-magic" />
      <p className="text-sm text-color-text font-normal">
        Web chat is managed by {AGENT_NAME}, your AI agent. Customize{" "}
        {AGENT_NAME}&apos;s knowledge in Agent settings.
      </p>
      <Button
        size="sm"
        variant="link"
        className="text-color-text-magic"
        onClick={() => {
          router.push(
            `/organization/settings/agent-studio/agents?agentId=${agentDetails?.id}`,
          );
        }}
      >
        Explore agent <ArrowRight />
      </Button>
    </AgentInfoWrapper>
  );
};
