import Avatar from "@/components/common/Avatar";
import CopyToClipboard from "@/components/common/CopyToClipboard";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { cn } from "@/lib/utils";
import { ADD_REACTION } from "@/services/kanban";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { DialogDescription } from "@radix-ui/react-dialog";
import DOMPurify from "dompurify";
import { Ellipsis, Pencil, Trash2 } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";
import { getTimeAgo, highlightMentions } from "../../../../../utils/kanban";
import SlackColoredIcon from "../../../../icons/SlackColoredIcon";
import TooltipWrapper from "../../../../tooltip-wrapper";
import EmojiPicker from "./EmojiPicker";

const ConversationContentWrapper = styled.div`
  /* Specific styling for links in Lumen theme */
  a {
    color: var(--url-color);
  }

  ol,
  ul {
    margin-left: 15px;
  }

  blockquote {
    color: var(--color-text);
    border-left: 4px solid var(--slack-blockquote-left-border);
    padding-left: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
    margin: 8px 0;
    background-color: var(--slack-code-block-bg);
    border-radius: 4px;
    min-height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  blockquote p {
    color: var(--color-text);
    padding: 0;
    margin: 0;
    line-height: 1.5;
  }

  p:empty {
    padding-top: 16px;
  }

  h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
    font-weight: 500;
  }

  h3 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
    font-weight: 500;
  }

  p {
    font-size: 14px; /* 14px */
    line-height: 1.5rem; /* 24px */
  }

  p.is-empty::before {
    color: var(--color-text-muted);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  code {
    font-size: 0.875rem; /* 14px */
    font-family: monospace;
  }

  word-break: break-word;
  overflow-wrap: anywhere;

  ul,
  ol {
    padding: 0 1rem;
    margin: 0rem 1rem 0rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  hr[contenteditable="false"] {
    margin-bottom: 0;
    border-top: 1px solid var(--color-border);
  }

  .mention {
    color: var(--mention-color);
    background-color: var(--mention-bg);
    padding: var(--mention-padding);
    border-radius: var(--mention-border-radius);
    font-weight: var(--mention-font-weight);
  }
`;

const ConversationMenu = ({
  deleteMutate,
  setEditOpen,
  conversation,
  authorAvatar,
  authorName,
  _setIsDefaultOpen,
  refetchEmoji = null,
  _showReply = true,
  handleForceUpdate = null,
}) => {
  const [open, setOpen] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const currentUserId = useGlobalConfigPersistStore(
    (state) => state.currentUser.uid,
  );

  const { mutate } = useApiMutation(ADD_REACTION(conversation.id), {}, "POST");

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleMouseLeave = () => {
    if (!open && !isPickerOpen) {
      setIsHovering(false);
    }

    if (!isHovering) {
      setIsPickerOpen(false);
    }
  };

  const handlePopOverMouseLeave = () => {
    setOpen(false);
  };

  const isAllowedToEdit = conversation.authorId === currentUserId;

  return (
    <>
      <div
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={handleMouseLeave}
        className={cn(
          "w-min px-4 rounded absolute right-2.5 top-2 cursor-pointer",
          isHovering || open ? "flex" : "hidden group-hover:flex",
        )}
      >
        <EmojiPicker
          onClickOutside={() => {}}
          onEmojiSelect={(emoji) => {
            mutate({ name: emoji.id }).then(() => {
              setIsPickerOpen(false);

              if (refetchEmoji) {
                refetchEmoji();
              }
            });
          }}
          open={isPickerOpen}
          setOpen={setIsPickerOpen}
          pickerClassname="border-none shadow-none"
          pickerEmojiSize={14}
        />
        <TooltipWrapper tooltipContent="Copy link to message">
          <div>
            <CopyToClipboard
              text={`${window.location.href}&cid=${conversation.id}`}
              className="border-none shadow-none hover:bg-color-strong bg-transparent"
              size={14}
            />
          </div>
        </TooltipWrapper>

        {!isAllowedToEdit &&
          conversation.metadata?.external_sinks?.slack?.slackThreadLink &&
          (() => {
            const slackThreadLink =
              conversation.metadata?.external_sinks?.slack?.slackThreadLink;
            return (
              <TooltipWrapper tooltipContent="Open in Slack">
                <div className="rounded flex items-center justify-center">
                  <a
                    href={slackThreadLink}
                    target="_blank"
                    rel="noreferrer"
                    className="border-none shadow-none hover:bg-color-strong bg-transparent flex items-center justify-center p-2"
                  >
                    <SlackColoredIcon className="h-3.5 w-3.5" />
                  </a>
                </div>
              </TooltipWrapper>
            );
          })()}
        {isAllowedToEdit && (
          <Popover open={open} onOpenChange={setOpen}>
            <TooltipWrapper tooltipContent="More options">
              <PopoverTrigger asChild>
                <div
                  className="px-2 py-0.5 flex items-center justify-center hover:bg-color-strong bg-transparent rounded cursor-pointer"
                  onClick={() => setOpen(true)}
                >
                  <Ellipsis className="h-4 w-4 scale-90" />
                </div>
              </PopoverTrigger>
            </TooltipWrapper>
            <PopoverContent
              align="end"
              className="w-40 text-sm p-0.5 shadow-md border-border"
              onMouseLeave={handlePopOverMouseLeave}
            >
              <div className="flex flex-col">
                <div
                  className="flex items-center gap-2 cursor-pointer hover:bg-color-bg-subtle p-2 rounded"
                  onClick={() => {
                    setEditOpen({
                      id: conversation.id,
                      state: true,
                    });
                    setOpen(false);
                  }}
                >
                  <Pencil className="h-4 w-4 scale-90" />
                  <span>Edit</span>
                </div>
                <div
                  className="flex items-center gap-2 cursor-pointer hover:bg-color-bg-subtle p-2 text-destructive hover:text-destructive-hover rounded"
                  onClick={() => {
                    setIsDialogOpen(true);
                    setOpen(false);
                  }}
                >
                  <Trash2 className="h-4 w-4 scale-90" />
                  <span>Delete</span>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogTitle>Delete message</DialogTitle>
          <DialogDescription className="text-sm text-muted-text">
            Are you sure you want to delete this message? This cannot be undone.
          </DialogDescription>
          <div className="w-full max-h-[300px] overflow-y-auto flex items-start p-5 gap-4 rounded-sm border">
            <Avatar
              src={authorAvatar}
              fallbackImage={authorAvatar}
              fallbackText={authorName}
              fallbackTextClassnames="h-9 w-9"
              imgClassnames="h-9 w-9"
            />
            <div className="w-full h-auto space-y-2">
              <div className="flex gap-4 justify-start items-center">
                <p className="text-sm font-bold">{authorName}</p>
                <span className="text-xs text-muted-foreground">
                  {getTimeAgo(conversation.createdAt)}
                </span>
              </div>
              <ConversationContentWrapper
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(
                    highlightMentions(conversation.contentHtml),
                  ),
                }}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="ghost" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() =>
                deleteMutate().then(() => {
                  setIsDialogOpen(false);
                  if (handleForceUpdate) {
                    handleForceUpdate();
                  }
                })
              }
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ConversationMenu;
