import { useApi } from "@/hooks/use-api";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { cn } from "@/lib/utils";
import {
  GET_TICKET_COMMENTS_REPLY,
  POST_COMMENT_ON_TICKET,
} from "@/services/kanban";
import { useTicketDrawerStore } from "@/store/ticketDrawerStore";
import { Comment } from "@/types/kanban";
import { useParams } from "next/navigation";
import EmailEditor from "./EmailEditor";
import EmailMessageBox from "./EmailMessageBox";

type Tprops = {
  conversations: Comment[];
};
export type EmailForwarding = {
  isEmailEnabled: boolean;
  id: string;
  uid: string;
  userId: string;
  teamId: string;
  organizationId: string;
  forwardingEmailAddress: string;
  forwardingVerificationCode: string;
  customEmail: string;
  domain: string;
  isEmailForwardingVerified: boolean;
  sendersPreferredChoice: string;
  sendersPreferredChoiceValue: string;
  deletedAt: string | null;
  lastVerifiedAt: string;
  createdAt: string;
  updatedAt: string;
};

const EmailConversation = ({ conversations }: Tprops) => {
  const { mutate } = useApiMutation(POST_COMMENT_ON_TICKET);
  const forceUpdate = useTicketDrawerStore((state) => state.forceUpdateReply);
  const teamId = useParams().teamId as string;

  const firstConversation = conversations[0];

  const { data: emailConfig } = useApi<EmailForwarding>(
    `/email-config/get-email-settings?teamId=${teamId}`,
    {},
    {
      enabled: true,
      isNextApi: false,
      urlOption: "email",
    },
  );

  const { data: replyData } = useApi<Comment[]>(
    GET_TICKET_COMMENTS_REPLY(conversations[0].id),
    {},
    {
      enabled: true,
      isNextApi: false,
    },
    forceUpdate,
  );

  const sortedReplyData = replyData?.sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  );

  const lastConversation = sortedReplyData?.[sortedReplyData?.length - 1];

  const lastConversationForEditor = lastConversation || firstConversation;

  return (
    <div>
      <EmailMessageBox
        conversations={conversations}
        replyData={sortedReplyData}
      />
      {emailConfig?.isEmailEnabled && (
        <div className={cn("mt-6", conversations.length === 0 && "mt-10")}>
          <EmailEditor
            lastConversation={lastConversationForEditor}
            firstConversation={firstConversation}
            mutate={mutate}
            currentUserEmailConfig={emailConfig}
          />
        </div>
      )}
    </div>
  );
};

export default EmailConversation;
