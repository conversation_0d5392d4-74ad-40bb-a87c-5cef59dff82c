"use client";

import { uploadFile } from "@/app/actions/uploadFile";
import {
  EmojiConverter,
  EmojiSuggestion,
} from "@/components/tiptap/extensions/Emojis";
import { cn } from "@/lib/utils";
import { Comment } from "@/types/kanban";
import FileHandler from "@tiptap-pro/extension-file-handler";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import TextAlign from "@tiptap/extension-text-align";
import { Editor, EditorProvider } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import styled from "styled-components";
import { useGlobalConfigPersistStore } from "../../../../../../store/globalConfigPersistStore";
import { getOrgDetails } from "../../../../../../utils/browserUtils";
import AttachmentPreview from "../../AttachmentPreview";
import { EmailForwarding } from "./EmailConversation";
import "./EmailEditor.css";
import EmailEditorBottom from "./EmailEditorBottom";
import EmailEditorHeader from "./EmailEditorHeader";

interface WrapperProps {
  isFocused: boolean;
}

const Wrapper = styled.div<WrapperProps>`
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 6px;
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
  background-color: var(--color-bg);
  color: var(--color-text);
  box-shadow: ${({ isFocused }) =>
    isFocused
      ? "0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06)"
      : "none"};

  a {
    color: var(--url-color);
  }

  .ProseMirror {
    min-height: 40px;
  }
`;

const getExtensions = (onSend, setIsLoading, orgUid) => {
  return [
    EmojiConverter,
    EmojiSuggestion,
    StarterKit.configure({
      bulletList: {
        keepMarks: true,
        keepAttributes: false,
      },
      orderedList: {
        keepMarks: true,
        keepAttributes: false,
      },
    }),
    TextAlign.configure({ types: ["heading", "paragraph"] }),
    Placeholder.configure({
      emptyNodeClass: "is-empty",
      placeholder({ node }) {
        if (node.type.name === "paragraph") {
          return "Write your email here...";
        }
        return "";
      },
    }),
    Image,
    Link.configure({
      openOnClick: false,
      HTMLAttributes: {
        target: "_blank",
        rel: "noopener noreferrer nofollow",
        class: "text-blue-500",
      },
    }),
    // CommandEnterExtension.configure({
    //   onCommandEnter(editor) {
    //     onSend(editor);
    //   },
    // }),
    FileHandler.configure({
      allowedMimeTypes: ["image/png", "image/jpeg", "image/gif", "image/webp"],

      onDrop: (currentEditor, files) => {
        files.forEach(async (file) => {
          if (!file) {
            return false;
          }
          setIsLoading(true);
          try {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("upload_preset", "platform");

            const uploadData = await uploadFile(formData, orgUid);

            if (uploadData?.urls?.publicUrl) {
              currentEditor.commands.insertContentAt(
                currentEditor.state.selection.anchor,
                `<img src="${uploadData.urls.publicUrl}" alt="${
                  file?.name || "platform"
                }" />`,
              );

              currentEditor.commands.focus();
            }
          } catch (error) {
            console.error("Upload failed:", error);
          } finally {
            setIsLoading(false);
          }
        });
      },

      onPaste: (currentEditor, files, htmlContent) => {
        files.forEach(async (file) => {
          if (htmlContent) {
            return false;
          }
          if (!file) {
            return false;
          }
          setIsLoading(true);
          try {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("upload_preset", "platform");

            const uploadData = await uploadFile(formData, orgUid);

            if (uploadData?.urls?.publicUrl) {
              currentEditor.commands.insertContentAt(
                currentEditor.state.selection.anchor,
                `<img src="${uploadData.urls.publicUrl}" alt="${
                  file?.name || "platform"
                }" />`,
              );
              currentEditor.commands.insertContentAt(
                currentEditor.state.selection.anchor,
                "<p><br></p>",
              );
              currentEditor.commands.focus();
            }
          } catch (error) {
            console.error("Upload failed:", error);
          } finally {
            setIsLoading(false);
          }
        });
      },
    }),
  ];
};

const validateFileType = (file) => {
  const fileType = file.type.split("/")[0];
  const isExecutable = [
    "application/x-msdownload",
    "application/x-dosexec",
  ].includes(fileType);
  return !isExecutable;
};

interface EmailEditorProps {
  firstConversation: Comment;
  lastConversation: Comment;
  mutate: (body?: unknown, fetchOptions?: RequestInit, param?: string) => void;
  currentUserEmailConfig: EmailForwarding | undefined;
}

const EmailEditor = ({
  lastConversation,
  firstConversation,
  mutate,
  currentUserEmailConfig,
}: EmailEditorProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [tiptapIsFocused, setTiptapIsFocused] = useState(false);
  const [content, setContent] = useState("");
  const [localFiles, setLocalFiles] = useState([]);
  const params = useSearchParams();
  const ticketId = params.get("ticketId");
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const { orgUid } = getOrgDetails();
  const emailMetaData = lastConversation?.metadata?.email;

  const editorRef = useRef<Editor | null>(null);
  const attachmentPreviewRef = useRef<HTMLDivElement>(null);
  const prevFilesLengthRef = useRef(localFiles.length);

  const defaultCC = emailMetaData?.headers?.find(
    (e) => e?.Name === "Delivered-To",
  );

  const [ccEmails, setCcEmails] = useState<string[]>([
    defaultCC?.Value,
    // ...emailMetaData.cc?.map((cc) => cc.Email),
  ]);
  const [bccEmails, setBccEmails] = useState<string[]>(
    emailMetaData?.bcc
      ?.map((bcc) => bcc?.Email)
      ?.filter((e) => e !== currentUserEmailConfig?.forwardingEmailAddress) ||
      [],
  );
  const [toEmails, setToEmails] = useState<string[]>([
    emailMetaData?.from?.Email || "",
  ]);
  const [emailEditorOption, setEmailEditorOption] = useState("");

  const handleChange = (editor) => {
    setContent(editor.getHTML());
  };

  const onDrop = async (acceptedFiles: File[]) => {
    setIsLoading(true);
    const validFiles = acceptedFiles.filter((file) => {
      const isFileTypeValid = validateFileType(file);
      const isSizeValid = file.size <= 52428800; // 50 MB
      return isFileTypeValid && isSizeValid;
    });

    if (validFiles.length !== acceptedFiles.length) {
      const invalidFiles = acceptedFiles.filter((file) => file.size > 52428800);
      invalidFiles.forEach((file) => {
        toast.error(`File "${file.name}" exceeds the maximum allowed size.`);
      });
    } else {
      const formData = new FormData();
      validFiles.forEach((file) => {
        formData.append("files", file);
      });

      try {
        const data = await uploadFile(formData, orgUid);
        const urls = [
          {
            url: data.urls?.publicUrl,
            type: data.data?.contentType,
            id: data.data?.uid,
          },
        ];
        setLocalFiles([...localFiles, ...urls]);
      } catch (error) {
        toast.error("Failed to upload file");
        console.error(error);
      } finally {
        setIsLoading(false);
        if (editorRef.current) {
          editorRef.current.commands.focus();
        }
      }
    }

    setIsLoading(false);
  };

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop,
    noClick: true,
    noKeyboard: true,
  });

  useEffect(() => {
    // Only scroll if files were added (length increased)
    if(localFiles.length > prevFilesLengthRef.current && attachmentPreviewRef.current) {
      // Use requestAnimationFrame for better timing and to ensure DOM has updated
      const frameId = requestAnimationFrame(() => {
        const previewElement = attachmentPreviewRef.current;
        if (!previewElement) return;

        const rect = previewElement.getBoundingClientRect();
        const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

        // Check if element is partially or completely out of view
        const isOutOfView = rect.bottom > viewportHeight || rect.top < 0;

        if (isOutOfView) {
          previewElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      })
      // Cleanup function to cancel the animation frame if component unmounts
      return () => cancelAnimationFrame(frameId);
    }
    prevFilesLengthRef.current = localFiles.length;
  }, [localFiles.length])

  useEffect(() => {
    if (!emailMetaData) return;
    const cc = emailMetaData?.cc?.map((cc) => cc?.Email);

    if (cc) {
      setCcEmails((prev) => [...new Set([...prev, ...cc])]);
    }
  }, [emailMetaData]);

  const addComment = async (editor: Editor) => {
    if (editor.isEmpty && localFiles.length === 0) {
      toast.error("Comment cannot be empty");
      return null;
    }
    const cc = [...new Set(ccEmails)]?.filter(Boolean)?.map((email) => {
      const meta = emailMetaData?.cc?.find((cc) => cc.Email === email);
      if (meta) return meta;
      return {
        Name: "",
        Email: email,
        MailboxHash: "",
      };
    });
    const bcc = bccEmails.map((email) => {
      const meta = emailMetaData?.cc?.find((cc) => cc.Email === email);
      if (meta) return meta;
      return {
        Name: "",
        Email: email,
        MailboxHash: "",
      };
    });
    const to = toEmails.map((email) => {
      const meta = emailMetaData?.cc?.find((cc) => cc.Email === email);
      if (meta) return meta;
      return {
        Name: "",
        Email: email,
        MailboxHash: "",
      };
    });

    const nameToSend =
      currentUserEmailConfig.sendersPreferredChoice === "original_name"
        ? null
        : currentUserEmailConfig.sendersPreferredChoice === "both"
        ? `${currentUser?.name} | ${currentUserEmailConfig.sendersPreferredChoiceValue}`
        : currentUserEmailConfig.sendersPreferredChoiceValue;

    try {
      // styles for image
      const htmlContent = editor.getHTML();
      const styledHtmlContent = htmlContent.replace(
        /<img([^>]*?)(?:\s+style="[^"]*")?([^>]*?)>/g,
        '<img$1$2 class="email-image">',
      );

      await mutate({
        content: editor.getText(),
        contentJson: JSON.stringify(editor.getJSON()),
        contentHtml: styledHtmlContent,
        commentVisibility: "public",
        impersonatedUserName: nameToSend,
        commentType: "comment",
        attachmentIds: localFiles?.map((file) => file.id),
        parentCommentId: firstConversation.id,
        entityType: "ticket",
        entityId: ticketId,
        metadata: {
          ...lastConversation.metadata,
          email: {
            ...lastConversation.metadata.email,
            cc: cc,
            bcc: bcc,
            to: to,
            isIgnoreSelf: false,
          },
        },
      });
      editor.commands.clearContent();
      setLocalFiles([]);
    } catch (err) {
      console.error("Error posting comment:", err);
    }
  };

  const onSend = (editor: Editor) => addComment(editor);

  const handleSend = (editor) => {
    if (editor.isEmpty) {
      toast.error("Cannot send empty email");
      return;
    }
    onSend(editor);
  };

  return (
    <div className="flex flex-col gap-2">
      <Wrapper isFocused={tiptapIsFocused} className={cn("relative")}>
        <input {...getInputProps()} />

        <EditorProvider
          extensions={getExtensions(handleSend, setIsLoading, orgUid)}
          slotBefore={
            <EmailEditorHeader
              ccEmails={ccEmails}
              setCcEmails={setCcEmails}
              bccEmails={bccEmails}
              setBccEmails={setBccEmails}
              toEmails={toEmails}
              setToEmails={setToEmails}
              emailEditorOption={emailEditorOption}
              setEmailEditorOption={setEmailEditorOption}
              emailMetaData={emailMetaData}
              defaultCC={defaultCC}
            />
          }
          slotAfter={
            <EmailEditorBottom
              openDropzone={open}
              getRootProps={getRootProps}
              onSend={(editor) => onSend(editor)}
              isLoading={isLoading}
              localFiles={localFiles}
            />
          }
          onCreate={({ editor }) => {
            editorRef.current = editor as Editor;
          }}
          content={content}
          onUpdate={({ editor }) => handleChange(editor)}
          editorContainerProps={{
            className: cn(
              "max-h-auto w-full overflow-auto *:outline-none p-2",
              "[&_h2]:text-2xl [&_h3]:text-xl",
            ),
          }}
          onFocus={() => {
            setTiptapIsFocused(true);
          }}
          onBlur={() => {
            setTiptapIsFocused(false);
          }}
        >
          {null}
        </EditorProvider>
      </Wrapper>

      {localFiles?.length > 0 && (
        <div ref={attachmentPreviewRef} className="mt-2 pl-2">
          <div className="text-xs text-muted-foreground mb-1">
            Pending uploads:
          </div>
          <div className="max-w-[400px]">
            <AttachmentPreview
              showRemoveButton={true}
              urls={localFiles}
              onRemove={(index) => {
                const newFiles = [...localFiles];
                newFiles.splice(index, 1);
                setLocalFiles(newFiles);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailEditor;
