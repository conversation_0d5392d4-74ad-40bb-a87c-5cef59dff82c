import Avatar from "@/components/common/Avatar";
import { useApi } from "@/hooks/use-api";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { cn } from "@/lib/utils";
import {
  DELETE_COMMENT_BY_ID,
  GET_TICKET_COMMENTS_REPLY,
  POST_COMMENT_ON_TICKET,
  UPDATE_COMMENT_BY_ID,
} from "@/services/kanban";
import { useTicketDrawerStore } from "@/store/ticketDrawerStore";
import { Comment } from "@/types/kanban";
import { getTimeAgo, highlightMentions } from "@/utils/kanban";
import { Editor } from "@tiptap/core";
import { format } from "date-fns";
import DOMPurify from "dompurify";
import { ChevronUp } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import styled from "styled-components";
import TooltipWrapper from "../../../../tooltip-wrapper";
import AttachmentPreview from "../AttachmentPreview";
import Tiptap from "../Tiptap/Tiptap";
import ConversationMenu from "./ConversationMenu";
import ConversationReplies from "./ConversationReplies";
import FailedReplies from "./FailedReplies";
import Reactions from "./Reactions";
import { SlackCodeStyle } from "./SlackCodeStyle";
import WebChatConversation from "./WebChatConversation";

const ConversationWrapper = styled.div`
  /* Specific styling for links in Lumen theme */
  a {
    color: var(--url-color);
  }

  ol,
  ul {
    margin-left: 15px;
  }

  word-break: break-word;
  overflow-wrap: anywhere;

  blockquote {
    color: var(--color-text);
    border-left: 4px solid var(--slack-blockquote-left-border);
    padding-left: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
    margin: 8px 0;
    background-color: var(--slack-code-block-bg);
    border-radius: 4px;
    min-height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  blockquote p {
    color: var(--color-text);
    padding: 0;
    margin: 0;
    line-height: 1.5;
  }

  p:empty {
    padding-top: 16px;
  }

  h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
    font-weight: 500;
  }

  h3 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
    font-weight: 500;
  }

  p {
    font-size: 14px; /* 14px */
    line-height: 1.5rem; /* 24px */
  }

  p.is-empty::before {
    color: var(--color-text-muted);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  code {
    font-size: 0.875rem; /* 14px */
    font-family: monospace;
  }

  ul,
  ol {
    padding: 0 1rem;
    margin: 0rem 1rem 0rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  hr[contenteditable="false"] {
    margin-bottom: 0;
    border-top: 1px solid var(--color-border);
  }

  .mention {
    color: var(--mention-color);
    background-color: var(--mention-bg);
    padding: var(--mention-padding);
    border-radius: var(--mention-border-radius);
    font-weight: var(--mention-font-weight);
  }
`;

const ConversationThread = ({
  conversation,
  emojis,
  editOpen,
  setEditOpen,
  index,
  allConversations,
  source,
}: {
  conversation: Comment;
  allConversations: Comment[];
  index: number;
  source: string;
  editOpen: {
    id: string | null;
    state: boolean;
  };
  setEditOpen: (value: { id: string | null; state: boolean }) => void;
  emojis: Record<
    string,
    {
      id: string;
      name: string;
      keywords: string[];
      skins: {
        unified: string;
        native: string;
      }[];
    }
  >;
}) => {
  const [showPrevReply, setShowPrevReply] = useState(false);

  const attachmentUrls =
    conversation.attachments?.map((item) => item.url) || [];

  const [content, setContent] = useState(conversation.contentHtml || "");
  const [localFiles, setLocalFiles] = useState(attachmentUrls || []);
  const [forceUpdate, setForceUpdate] = useState(0);
  const forceUpdateReply = useTicketDrawerStore(
    (state) => state.forceUpdateReply,
  );
  const [retryReplyData, setRetryReplyData] = useState([]);

  const [replyContent, setReplyContent] = useState("");
  const [replyLocalFiles, setReplyLocalFiles] = useState([]);

  const [replyEditOpen, setReplyEditOpen] = useState({
    id: null,
    state: false,
  });

  const params = useSearchParams();
  const [ticketId, setTicketId] = useState<string | null>(
    params.get("ticketId"),
  );

  useEffect(() => {
    setTicketId(params.get("ticketId"));
  }, [params]);

  const [isDefaultOpen, setIsDefaultOpen] = useState(
    index === 0 || allConversations?.length === 1 ? true : false,
  );

  const { mutate } = useApiMutation(
    DELETE_COMMENT_BY_ID(conversation.id),
    {},
    "DELETE",
  );

  const { mutate: updateMutation } = useApiMutation(
    UPDATE_COMMENT_BY_ID(conversation.id),
    {},
    "PATCH",
  );

  const { data: replyData, refetch } = useApi<Comment[]>(
    GET_TICKET_COMMENTS_REPLY(conversation.id),
    {},
    { enabled: true, isNextApi: false },
    forceUpdate,
  );

  const handleForceUpdate = () => setForceUpdate((prev) => prev + 1);

  useEffect(() => {
    if (ticketId) {
      refetch();
    }

    if (forceUpdateReply) {
      handleForceUpdate();
    }
  }, [ticketId, forceUpdateReply]);

  const { mutate: replyToComment } = useApiMutation(POST_COMMENT_ON_TICKET);

  const sanitizeContentHtml = (html: string): string | null => {
    const trimmed = html.trim();
    if (trimmed === "<p></p>" || trimmed === "<p><br></p>") {
      return null;
    }
    return html;
  };

  const updateComment = async (editor: Editor) => {
    if (!ticketId) {
      toast.error("Failed to update comment");
      return;
    }

    if (editor.isEmpty && localFiles.length === 0) {
      toast.error("Comment cannot be empty");
      return;
    }

    if (editor.getHTML()?.length > 5000) {
      toast.error("Comment cannot be more than 5000 characters");
      return;
    }
    await updateMutation({
      content: editor.getText(),
      contentHtml: editor.getHTML(),
      contentJson: JSON.stringify(editor.getJSON()),
      // attachmentIds: localFiles,
    });
    editor.commands.clearContent();
    setEditOpen({ id: null, state: false });
    setReplyEditOpen({ id: null, state: false });
  };

  const addReplyToComment = async (editor: Editor) => {
    if (!ticketId) {
      toast.error("Failed to send reply");
      return;
    }

    if (editor.isEmpty && replyLocalFiles.length === 0) {
      toast.error("Reply cannot be empty");
      return null;
    }

    const replyData = {
      content: editor.getText(),
      contentHtml: sanitizeContentHtml(editor.getHTML()),
      contentJson: JSON.stringify(editor.getJSON()),
      commentVisibility: "public",
      commentType: "comment",
      parentCommentId: conversation.id,
      attachmentIds: replyLocalFiles?.map((file) => file.id),
      entityType: "ticket",
      entityId: ticketId,
    };

    const retryData = {
      content: editor.getText(),
      contentHtml: sanitizeContentHtml(editor.getHTML()),
      contentJson: JSON.stringify(editor.getJSON()),
      parentCommentId: conversation.id,
      attachments: replyLocalFiles?.map((file) => ({
        id: file.id,
        url: file.url,
        contentType: file.contentType,
      })),
      createdAt: new Date().toISOString(),
    };

    // Implement retry mechanism - try up to 3 times
    const MAX_RETRIES = 3;
    let retryCount = 0;
    let success = false;

    while (retryCount < MAX_RETRIES && !success) {
      try {
        await replyToComment({ ...replyData, entityId: ticketId });
        success = true;
      } catch (err) {
        retryCount++;
        console.error(
          `Error posting reply to comment (attempt ${retryCount}/${MAX_RETRIES}):`,
          err,
        );

        if (retryCount < MAX_RETRIES) {
          // Wait for a short delay before retrying (exponential backoff)
          const delayMs = 1000 * Math.pow(2, retryCount - 1); // 1s, 2s, 4s
          await new Promise((resolve) => setTimeout(resolve, delayMs));
        } else {
          setRetryReplyData((prev) => [...prev, retryData]);
          toast.error(
            `Failed to send reply after ${MAX_RETRIES} attempts. Added to retry queue.`,
          );
        }
      }
    }

    // Always execute these actions regardless of success/failure
    refetch();
    editor.commands.clearContent();
    handleForceUpdate();
    setReplyLocalFiles([]);
  };

  const retryReplyToComment = async (index: number) => {
    if (!ticketId) {
      toast.error("Failed to retry reply");
      return;
    }
    const retryReply = retryReplyData[index];

    try {
      await replyToComment({
        content: retryReply.content,
        contentHtml: retryReply.contentHtml,
        contentJson: retryReply.contentJson,
        commentVisibility: "public",
        commentType: "comment",
        parentCommentId: conversation.id,
        attachmentIds: retryReply.attachments?.map((file) => file.id),
        entityType: "ticket",
        entityId: ticketId,
      });
      setRetryReplyData((prev) => prev.filter((_, i) => i !== index));
    } catch (err) {
      console.error("Error posting reply to comment:", err);
    } finally {
      refetch();
      handleForceUpdate();
    }
  };

  const sortedReplies = replyData?.sort(
    (a, b) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime(),
  );

  const lastThreeReplies = sortedReplies?.slice(
    Math.max(sortedReplies.length - 3, 0),
  );

  const replyToShow = showPrevReply
    ? sortedReplies
    : sortedReplies?.length > 3
    ? lastThreeReplies
    : sortedReplies;

  const avatarUrl =
    conversation.impersonatedUserAvatar ?? conversation.authorAvatarUrl;
  const authorName = conversation.impersonatedUserName ?? conversation.author;

  // For the first message of web-chat source tickets, we'll convert it to a chat-like component
  // but still allow replies to the thread
  if (index === 0 && source === "web-chat") {
    return (
      <div
        id={conversation.id}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") setIsDefaultOpen(true);
        }}
        className={cn(
          "flex gap-0 flex-col rounded-sm relative group border border-border cursor-pointer",
          allConversations.length === 1 && "border-none",
        )}
        onClick={() => setIsDefaultOpen(true)}
      >
        <SlackCodeStyle />
        <div className="w-full mb-3">
          <WebChatConversation
            content={conversation.contentHtml}
            className="text-sm"
            isFirstComment={true}
            authorAvatar={avatarUrl}
          />
        </div>

        {sortedReplies?.length > 3 && !showPrevReply && (
          <div className="flex relative px-3">
            <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-px bg-border" />
            <div className="w-10 flex-shrink-0" />
            <TooltipWrapper tooltipContent="View earlier messages">
              <button
                type="button"
                onClick={() => setShowPrevReply(!showPrevReply)}
                className="relative z-10 px-3 py-1 rounded text-xs text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center gap-1.5 ml-4 bg-background"
              >
                <ChevronUp size={14} className="text-muted-foreground" />
                <span>{sortedReplies?.length - 3} earlier messages</span>
              </button>
            </TooltipWrapper>
          </div>
        )}

        {/* Show replies if any */}
        <ConversationReplies
          replyData={replyToShow}
          setReplyEditOpen={setReplyEditOpen}
          replyEditOpen={replyEditOpen}
          emojis={emojis}
          refetch={refetch}
          handleForceUpdate={handleForceUpdate}
        />
        {/* Add reply input field - using the same pattern as Slack */}
        {isDefaultOpen && (
          <div
            className={cn(
              "border-t border-border",
              allConversations.length === 1 && "border rounded-sm mt-3",
            )}
          >
            <Tiptap
              content={replyContent}
              setContent={setReplyContent}
              onSend={(editor) => addReplyToComment(editor)}
              localFiles={replyLocalFiles}
              setLocalFiles={setReplyLocalFiles}
              isSideActionBarVisible={true}
              className={cn(
                "!border-none min-h-10",
                allConversations.length === 1 && "h-24",
              )}
            />
          </div>
        )}
      </div>
    );
  }
  // Standard comment UI for all other comments
  return (
    <div
      id={conversation.id}
      className={cn(
        "flex gap-0 flex-col rounded-sm relative group border border-border",
        allConversations.length === 1 && "border-none",
      )}
      onClick={() => {
        setIsDefaultOpen(true);
      }}
    >
      <SlackCodeStyle />
      <div className="flex rounded w-full p-3 items-start hover:bg-accent/50 transition-colors duration-200">
        <div className="flex-shrink-0 w-10 flex flex-col items-center mt-1">
          <Avatar
            src={avatarUrl}
            fallbackImage={avatarUrl}
            fallbackText={authorName}
            fallbackTextClassnames="h-9 w-9"
            imgClassnames="h-9 w-9"
          />
        </div>
        <div className="w-full ml-4">
          {!editOpen.state && editOpen?.id !== conversation.id && (
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-1">
                <span className="text-[14px] font-medium">{authorName}</span>
                <TooltipWrapper
                  tooltipContent={format(
                    new Date(conversation.createdAt),
                    "d MMM, h:mm a",
                  )}
                >
                  <span className="text-xs text-muted-foreground">
                    {getTimeAgo(conversation.createdAt)}
                  </span>
                </TooltipWrapper>
                {conversation.isEdited && (
                  <span className="text-xs text-muted-foreground relative top-[1.5px]">
                    (edited)
                  </span>
                )}
                {/* {conversation.impersonatedUserName && (
                  <div className="border border-border rounded text-xs px-2 text-muted-foreground">
                    App
                  </div>
                )} */}
              </div>
              {!editOpen.state && editOpen?.id !== conversation.id && (
                <ConversationMenu
                  conversation={conversation}
                  authorAvatar={avatarUrl}
                  authorName={authorName}
                  deleteMutate={mutate}
                  setEditOpen={setEditOpen}
                  _setIsDefaultOpen={setIsDefaultOpen}
                />
              )}
            </div>
          )}

          {editOpen.state && editOpen?.id === conversation.id ? (
            <Tiptap
              content={content}
              setContent={setContent}
              onSend={(editor) => updateComment(editor)}
              isFileUploadEnabled={false}
              localFiles={localFiles}
              setLocalFiles={setLocalFiles}
              setEditOpen={setEditOpen}
            />
          ) : (
            <ConversationWrapper
              className={cn(
                "text-sm",
                source === "slack" && "slack-message-content",
              )}
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(
                  highlightMentions(conversation.contentHtml),
                ),
              }}
            />
          )}

          {conversation.attachments?.length > 0 && (
            <div className="mt-2">
              <AttachmentPreview
                urls={conversation.attachments.map((item) => ({
                  url: item.url,
                  type: item.contentType,
                }))}
              />
            </div>
          )}

          <Reactions conversation={conversation} emojis={emojis} />
        </div>
      </div>

      {sortedReplies?.length > 3 && !showPrevReply && (
        <div className="flex relative px-3">
          <div className="absolute left-0 right-0 top-1/2 transform -translate-y-1/2 h-px bg-border"></div>
          <div className="w-10 flex-shrink-0"></div>
          <TooltipWrapper tooltipContent="View earlier messages">
            <button
              type="button"
              onClick={() => setShowPrevReply(!showPrevReply)}
              className="relative z-10 px-3 py-1 rounded text-xs text-muted-foreground hover:text-foreground transition-colors duration-200 flex items-center gap-1.5 ml-4 bg-background"
            >
              <ChevronUp size={14} className="text-muted-foreground" />
              <span>{sortedReplies?.length - 3} earlier messages</span>
            </button>
          </TooltipWrapper>
        </div>
      )}

      {/* conversation.metadata.replies */}
      <ConversationReplies
        replyData={replyToShow}
        setReplyEditOpen={setReplyEditOpen}
        replyEditOpen={replyEditOpen}
        emojis={emojis}
        refetch={refetch}
        handleForceUpdate={handleForceUpdate}
      />

      {/* Failed Replies */}
      <FailedReplies replyData={retryReplyData} retry={retryReplyToComment} />
      {/* This is kind of a redundant conditon , for safety purpose that nothing else break  */}
      {isDefaultOpen && source === "ms-teams" && (
        <div
          className={cn(
            "border-t border-border",
            allConversations.length === 1 && "border rounded-sm mt-3",
          )}
        >
          <Tiptap
            content={replyContent}
            setContent={setReplyContent}
            onSend={(editor) => addReplyToComment(editor)}
            localFiles={replyLocalFiles}
            setLocalFiles={setReplyLocalFiles}
            isSideActionBarVisible={true}
            className={cn("!border-none min-h-10")}
          />
        </div>
      )}
      {isDefaultOpen && source!== "ms-teams" &&
        !(
          source !== "slack" &&
          source !== "web-chat" &&
          allConversations.length === 1
        ) && (
          <div
            className={cn(
              "border-t border-border",
              allConversations.length === 1 && "border rounded-sm mt-3",
            )}
          >
            <Tiptap
              content={replyContent}
              setContent={setReplyContent}
              onSend={(editor) => addReplyToComment(editor)}
              localFiles={replyLocalFiles}
              setLocalFiles={setReplyLocalFiles}
              isSideActionBarVisible={true}
              className={cn(
                "!border-none min-h-10",
                source === "slack" && allConversations.length === 1 && "h-24",
              )}
            />
          </div>
        )}
    </div>
  );
};

export default ConversationThread;
