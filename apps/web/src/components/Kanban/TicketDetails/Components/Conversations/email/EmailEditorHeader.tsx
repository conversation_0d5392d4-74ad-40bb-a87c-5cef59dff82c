import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from "@/components/ui/menubar";
import { cn } from "@/lib/utils";
import { ChevronDown, Reply, ReplyAll } from "lucide-react";
import React, { useRef, useState } from "react";
import { Lane } from "./Lane";

const validateEmail = (inputText: string) => {
  const mailFormat = /\S+@\S+\.\S+/;
  if (inputText.match(mailFormat)) {
    return true;
  }
  return false;
};

const EmailEditorHeader = ({
  ccEmails,
  setCcEmails,
  bccEmails,
  setBccEmails,
  toEmails,
  setToEmails,
  emailEditorOption,
  setEmailEditorOption,
  emailMetaData,
  defaultCC,
}) => {
  const [ccLane, setCcLane] = useState(false);
  const [bccLane, setBccLane] = useState(bccEmails?.length > 0);
  const [replyState, setReplyState] = useState("reply");

  const handleReplyDropdown = () => {
    if (emailMetaData?.from?.Email) {
      setToEmails([emailMetaData.from.Email]);
    }

    setReplyState("reply");
  };

  const handleReplyAllDropdown = () => {
    if (emailMetaData?.to) {
      setToEmails((prev) => [
        ...prev,
        ...emailMetaData?.to?.map((to) => to?.Email),
      ]);
    }

    setReplyState("reply_all");
  };

  return (
    <div className={cn("flex border-b p-0.5")}>
      <Menubar className="border-none">
        <MenubarMenu>
          <MenubarTrigger
            className="h-9"
            style={{
              backgroundColor:
                replyState || emailEditorOption !== ""
                  ? "var(--color-bg-subtle)"
                  : "transparent",
            }}
          >
            {replyState === "reply" ? (
              <Reply size={16} />
            ) : (
              <ReplyAll size={16} />
            )}
            <ChevronDown className="ml-2" size={16} />
          </MenubarTrigger>
          <MenubarContent className=" ">
            <MenubarItem
              onClick={() => {
                setEmailEditorOption("reply");
                handleReplyDropdown();
              }}
            >
              <Reply size={16} className="mr-2" />
              Reply
            </MenubarItem>
            <MenubarItem
              onClick={() => {
                setEmailEditorOption("reply_all");
                handleReplyAllDropdown();
              }}
            >
              <ReplyAll size={16} className="mr-2" />
              Reply all
            </MenubarItem>
          </MenubarContent>
        </MenubarMenu>
      </Menubar>
      <div className="flex flex-col grow">
        {/* To Emails starts */}
        <div className="h-[36px] flex items-center">
          <span className="text-[var(--color-icon-muted)] text-sm mr-2 w-6">
            To:
          </span>
          <Lane emails={toEmails} setEmails={setToEmails} />
          <InputBox emails={toEmails} setEmails={setToEmails} />
          {ccEmails.length === 0 && (
            <Button
              size={"sm"}
              variant={"ghost"}
              onClick={() => setCcLane((prev) => !prev)}
              className="mt-1"
            >
              Cc:
            </Button>
          )}
          {bccEmails.length === 0 && (
            <Button
              size={"sm"}
              variant={"ghost"}
              onClick={() => setBccLane((prev) => !prev)}
              className="mr-1 mt-1"
            >
              Bcc:
            </Button>
          )}
        </div>
        {/* To Emails ends */}
        {/* {CC emails starts} */}
        {(ccLane || ccEmails.length > 0) && (
          <div className="h-[36px] flex items-center">
            <span className="text-[var(--color-icon-muted)] text-sm mr-2 w-6">
              Cc:
            </span>
            <Lane
              emails={ccEmails}
              setEmails={setCcEmails}
              defaultCC={defaultCC}
            />
            <InputBox emails={ccEmails} setEmails={setCcEmails} />
          </div>
        )}
        {/* {CC emails ends} */}
        {/* {Bcc emails starts} */}
        {bccLane && (
          <div className="h-[36px] flex items-center">
            <span className="text-[var(--color-icon-muted)] text-sm mr-2 w-6">
              Bcc:
            </span>
            <Lane emails={bccEmails} setEmails={setBccEmails} />
            <InputBox emails={bccEmails} setEmails={setBccEmails} />
          </div>
        )}
        {/* {Bcc emails ends} */}
      </div>
    </div>
  );
};

export default EmailEditorHeader;

const InputBox = ({
  setEmails,
}: {
  emails: string[];
  setEmails: React.Dispatch<React.SetStateAction<string[]>>;
}) => {
  const ref = useRef<HTMLInputElement>(null);
  const insertNewEmail = (email: string) => {
    if (!validateEmail(email)) {
      return;
    }
    if (ref.current) ref.current.value = "";
    setEmails((prev) => [...prev, email]);
  };
  return (
    <input
      ref={ref}
      className="w-1 grow text-sm border-none outline-none focus:outline-none text-[var(--color-text)] bg-transparent"
      onKeyDown={(e) => {
        if (e.key === " ") {
          const email = (e.target as HTMLInputElement).value;
          insertNewEmail(email);
        }
      }}
      onBlur={(e) => {
        const email = e.target.value;
        insertNewEmail(email);
      }}
    />
  );
};
