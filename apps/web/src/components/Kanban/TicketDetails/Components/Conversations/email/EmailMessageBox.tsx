"use client";
import Avatar from "@/components/common/Avatar";
import IconButton from "@/components/icon-button";
import ShadowDom from "@/components/Kanban/ShadowDom";
import TooltipWrapper from "@/components/tooltip-wrapper";
import { cn } from "@/lib/utils";
import { Comment } from "@/types/kanban";
import { getTimeAgo } from "@/utils/kanban";
import { format } from "date-fns";
import { ChevronsUpDown, MailWarning, Plus } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";
import AttachmentPreview from "../../AttachmentPreview";

const Wrapper = styled.div`
  word-break: break-word;
  width: 100%;
  overflow-x: auto;
  padding-bottom: 8px;
`;

const EmailMessageBox = ({ conversations, replyData }) => {
  const firstConverSation: Comment = conversations[0];
  const [showPrevReply, setShowPrevReply] = useState(false);

  const lastRepliesData = replyData?.slice(replyData?.length - 1);

  const replyToShow = showPrevReply ? replyData : lastRepliesData;

  const firstConverSationEmailMetaData = firstConverSation.metadata?.email;
  const firstDefaultCC = firstConverSationEmailMetaData?.headers?.find(
    (e) => e?.Name === "Delivered-To",
  );

  const firstCc =
    firstConverSationEmailMetaData &&
    [
      firstDefaultCC?.Value,
      ...firstConverSationEmailMetaData?.cc?.map((cc) => cc?.Email),
    ]?.filter(Boolean);

  const uniqueCc = firstCc.length > 0 ? [...new Set(firstCc)] : [];

  const firstTo = firstConverSationEmailMetaData
    ? firstConverSationEmailMetaData.to?.map((to) => to?.Email)?.filter(Boolean)
    : [];

  const uniqueTo = [...new Set(firstTo)];

  return (
    <div className="w-full overflow-hidden">
      <div
        className={cn(
          "flex gap-4 items-start p-2 hover:bg-color-bg-subtle w-full",
        )}
      >
        <Avatar
          fallbackTextClassnames="h-10 min-w-10"
          imgClassnames="h-10 min-w-10"
          src={firstConverSation?.impersonatedUserAvatar}
          fallbackText={
            firstConverSation.impersonatedUserName || firstConverSation.author
          }
        />

        <div className="w-full overflow-hidden">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium">
              {firstConverSation.impersonatedUserName ||
                firstConverSation.author}
            </span>
            <TooltipWrapper
              tooltipContent={format(
                new Date(firstConverSation.createdAt),
                "d MMM, h:mm a",
              )}
            >
              <span className="text-xs text-muted-foreground">
                {getTimeAgo(firstConverSation.createdAt)}
              </span>
            </TooltipWrapper>
            {!firstConverSationEmailMetaData && (
              <TooltipWrapper tooltipContent="This comment is not delivered to your email">
                <MailWarning
                  size={16}
                  className="text-[var(--color-text-warning)]"
                />
              </TooltipWrapper>
            )}
          </div>

          <div className="text-color-text-placeholder text-xs">
            To:{" "}
            <span>
              {" "}
              {uniqueTo && uniqueTo.length > 0 && (
                <div className="text-color-text-placeholder text-xs">
                  Cc:{" "}
                  {uniqueTo.length > 2 ? (
                    <>
                      {uniqueTo.slice(0, 2).join(", ")}
                      <TooltipWrapper
                        tooltipContent={uniqueTo.slice(2).join(", ")}
                      >
                        <div className="text-xs text-muted-foreground w-5 h-5 ml-2 text-center rounded bg-color-bg-elevated flex items-center justify-center">
                          <Plus size={10} /> {uniqueTo.slice(2)?.length}
                        </div>
                      </TooltipWrapper>
                    </>
                  ) : (
                    <>{uniqueTo.join(", ")}</>
                  )}
                </div>
              )}
            </span>
          </div>
          {uniqueCc && uniqueCc.length > 0 && (
            <div className="text-color-text-placeholder text-xs">
              Cc:{" "}
              {uniqueCc.length > 2 ? (
                <>
                  {uniqueCc.slice(0, 2).join(", ")}
                  <TooltipWrapper tooltipContent={uniqueCc.slice(2).join(", ")}>
                    <div className="text-xs text-muted-foreground w-5 h-5 ml-2 text-center rounded bg-color-bg-elevated flex items-center justify-center">
                      <Plus size={10} /> {uniqueCc.slice(2)?.length}
                    </div>
                  </TooltipWrapper>
                </>
              ) : (
                <>{uniqueCc.join(", ")}</>
              )}
            </div>
          )}
          <Wrapper className="pt-3 w-full overflow-hidden">
            <ShadowDom
              key={firstConverSation.id}
              html={firstConverSation.contentHtml}
            />
          </Wrapper>
          {firstConverSation.attachments?.length > 0 && (
            <div className="mt-3">
              <AttachmentPreview
                urls={firstConverSation.attachments.map((item) => ({
                  url: item.url,
                  type: item.contentType,
                }))}
              />
            </div>
          )}
        </div>
      </div>

      {!showPrevReply && replyData?.length > 1 && (
        <div
          className="text-sm flex items-center gap-2 cursor-pointer px-5 my-4"
          onClick={() => setShowPrevReply(!showPrevReply)}
        >
          <div className="flex items-center w-min gap-2">
            <IconButton
              size={24}
              Icon={ChevronsUpDown}
              className="text-muted-text"
            />
            <div className="flex whitespace-nowrap">
              {replyData?.length - 1} messages
            </div>
          </div>
          <div className="border-t h-[2px] w-full"></div>
        </div>
      )}

      {replyToShow?.map((conversation: Comment, id: number) => {
        const reply = conversation?.metadata?.email;
        const defaultCC = reply?.headers?.find(
          (e) => e?.Name === "Delivered-To",
        );

        const replyCc =
          reply?.cc &&
          [defaultCC?.Value, ...reply?.cc?.map((cc) => cc?.Email)]?.filter(
            Boolean,
          );

        const uniqueReplyCc = replyCc.length > 0 ? [...new Set(replyCc)] : [];

        const replyTo = reply
          ? reply.to?.map((to) => to?.Email)?.filter(Boolean)
          : [];

        const uniqueTo = [...new Set(replyTo)];

        if (!reply) return null;
        return (
          <>
            <div
              key={id}
              className={cn(
                "flex gap-4 items-start p-2 hover:bg-color-bg-subtle w-full",
              )}
            >
              <Avatar
                fallbackTextClassnames="h-10 min-w-10"
                imgClassnames="h-10 min-w-10"
                src={conversation?.impersonatedUserAvatar}
                fallbackText={
                  conversation.impersonatedUserName?.charAt(0) ||
                  conversation.author?.charAt(0)
                }
              />

              <div className="w-full overflow-hidden">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium">
                    {conversation.impersonatedUserName || conversation.author}
                  </span>

                  <TooltipWrapper
                    tooltipContent={format(
                      new Date(conversation.createdAt),
                      "d MMM, h:mm a",
                    )}
                  >
                    {" "}
                    <span className="text-xs text-muted-foreground">
                      {getTimeAgo(conversation.createdAt)}
                    </span>
                  </TooltipWrapper>
                  {!reply && (
                    <TooltipWrapper tooltipContent="This comment is not delivered to your email">
                      <MailWarning
                        size={16}
                        className="text-[var(--color-text-warning)]"
                      />
                    </TooltipWrapper>
                  )}
                </div>
                <div className="text-color-text-placeholder text-xs">
                  To:{" "}
                  {uniqueTo?.length > 2 ? (
                    <>
                      {uniqueTo.slice(0, 2).join(", ")}
                      <TooltipWrapper
                        tooltipContent={uniqueTo.slice(2).join(", ")}
                      >
                        <div className="text-xs text-muted-foreground w-5 h-5 ml-2 text-center rounded bg-color-bg-elevated flex items-center justify-center">
                          <Plus size={10} /> {uniqueTo.slice(2)?.length}
                        </div>
                      </TooltipWrapper>
                    </>
                  ) : (
                    <>{uniqueTo.join(", ")}</>
                  )}
                </div>
                <div className="text-color-text-placeholder text-xs">
                  Cc:{" "}
                  {uniqueReplyCc?.length > 2 ? (
                    <>
                      {uniqueReplyCc.slice(0, 2).join(", ")}
                      <TooltipWrapper
                        tooltipContent={uniqueReplyCc.slice(2).join(", ")}
                      >
                        <div className="text-xs text-muted-foreground w-5 h-5 ml-2 text-center rounded bg-color-bg-elevated flex items-center justify-center">
                          <Plus size={10} /> {uniqueReplyCc.slice(2)?.length}
                        </div>
                      </TooltipWrapper>
                    </>
                  ) : (
                    <>{uniqueReplyCc.join(", ")}</>
                  )}
                </div>
                <Wrapper className="pt-3 w-full overflow-hidden">
                  <ShadowDom
                    key={conversation.id}
                    html={conversation.contentHtml}
                  />
                </Wrapper>
                {conversation.attachments?.length > 0 && (
                  <div className="mt-3">
                    <AttachmentPreview
                      urls={conversation.attachments.map((item) => ({
                        url: item.url,
                        type: item.contentType,
                      }))}
                    />
                  </div>
                )}
              </div>
            </div>
          </>
        );
      })}
    </div>
  );
};

export default EmailMessageBox;
