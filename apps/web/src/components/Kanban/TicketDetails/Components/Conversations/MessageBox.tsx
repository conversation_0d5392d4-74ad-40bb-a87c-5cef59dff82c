import { Comment } from "@/types/kanban";
import { useState } from "react";
import ConversationThread from "./ConversationThread";
import { SlackCodeStyle } from "./SlackCodeStyle";

const MessageBox = ({ conversations, emojis, source }) => {
  const [editOpen, setEditOpen] = useState({
    id: null,
    state: false,
  });

  if (conversations.length === 0) return null;

  return (
    <>
      <SlackCodeStyle />
      <div className="space-y-4 pb-4">
        {conversations?.map((conversation: Comment, id: number) => {
          return (
            <ConversationThread
              key={id}
              conversation={conversation}
              allConversations={conversations}
            emojis={emojis}
              index={id}
              editOpen={editOpen}
              setEditOpen={setEditOpen}
              source={source}
            />
          );
        })}
      </div>
    </>
  );
};

export default MessageBox;
