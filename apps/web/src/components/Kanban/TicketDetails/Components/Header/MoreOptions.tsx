"use client";

import AccountSelector from "@/components/common/AccountSelector";
import Agent<PERSON>abel from "@/components/common/AgentLabel";
import Avatar from "@/components/common/Avatar";
import WarningModal from "@/components/common/WarningModal";
// Tooltip imports removed as we're not using tooltips anymore
import { TicketSearch } from "@/components/common/TicketSearch";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useApiMutation } from "@/hooks/use-api-mutation";
import useOrderedKanbanColumns from "@/hooks/use-ordered-kanban-columns";
import {
  ARCHIVE_TICKET_BY_ID,
  DELETE_TICKET_BY_ID,
  PATCH_LINK_TICKET,
  UPDATE_TICKET_BY_ID,
} from "@/services/kanban";
import { useTicketDetailsDisplayOptions } from "@/store/ticket-details-display-options";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { SingleTicket, Team } from "@/types/kanban";
import { getLaneIcon, getUrgencyIcon } from "@/utils/kanban";
import { toUpper } from "lodash";
import {
  AlertCircle,
  Archive,
  BarChart2,
  Building2,
  CheckIcon,
  Copy,
  Ellipsis,
  Link,
  Smile,
  SquareEqual,
  Trash2,
  TypeOutline,
  User,
  UsersRound,
} from "lucide-react";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { toast } from "sonner";
import { useTicketDrawerStore } from "../../../../../store/ticketDrawerStore";
import { getOrgDetails } from "../../../../../utils/browserUtils";

type Tprops = {
  ticketData: SingleTicket;
  drawerNavigate: (direction: "up" | "down") => void;
  closeDrawer: (router: AppRouterInstance) => void;
  isLastTicket: boolean;
  team: Team;
};

const MoreOptions = ({
  team,
  ticketData,
  drawerNavigate,
  closeDrawer,
  isLastTicket,
}: Tprops) => {
  // State for UI elements
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isArchiveModalOpen, setIsArchiveModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAccountSelectorOpen, setIsAccountSelectorOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showRelatedTicketModal, setShowRelatedTicketModal] = useState(false);
  const { orgUid } = getOrgDetails();
  const setForceUpdateRelateTicket = useTicketDrawerStore(
    (state) => state.setForceUpdateRelateTicket,
  );
  // Removed recentlyClosed state as we're using a different approach

  // Update global state to disable shortcuts when dropdown or modals are open
  const setDisableShortcuts = useTicketDetailsDisplayOptions(
    (state) => state.setDisableShortcuts,
  );

  // Reference to the dropdown trigger button
  const dropdownTriggerRef = useRef<HTMLButtonElement>(null);

  // Shortcut to open the dropdown menu (Alt+M)
  useHotkeys(
    "alt+m",
    (e) => {
      e.preventDefault();
      setIsDropdownOpen(true);
    },
    {
      enableOnFormTags: true,
      preventDefault: true,
    },
  );

  // Function to handle relating tickets
  const handleRelateTicket = () => {
    if (!selectedTicket || !selectedTicket?.document?.uid) return;

    // Safely access the document uid
    const relatedTicketId = selectedTicket.document.uid;
    if (!relatedTicketId) {
      console.error("Related ticket ID is missing");
      return;
    }

    fetch(PATCH_LINK_TICKET(ticketData.uid, relatedTicketId), {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "x-org-id": orgUid,
      },
    })
      .then((response) => {
        response.json().then((data) => {
          if (response.status === 200 && data.status === true) {
            toast.success("Tickets related successfully");
            setShowRelatedTicketModal(false);
            setSelectedTicket(null);
            setForceUpdateRelateTicket();
          } else {
            console.error("Error relating tickets:", response);
            toast.error("Failed to relate tickets");
          }
        });
      })
      .catch((error) => {
        console.error("Error relating tickets:", error);
        toast.error("Failed to relate tickets");
      });
  };

  // Handle Escape key for dropdown menu
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isDropdownOpen) {
        e.preventDefault();
        e.stopPropagation();
        setIsDropdownOpen(false);
      }
    };

    // Add the event listener when the dropdown is open
    if (isDropdownOpen) {
      window.addEventListener("keydown", handleKeyDown, true);
    }

    // Clean up the event listener
    return () => {
      window.removeEventListener("keydown", handleKeyDown, true);
    };
  }, [isDropdownOpen, setIsDropdownOpen]);

  useEffect(() => {
    // Disable global shortcuts when any modal or dropdown is open
    const shouldDisableShortcuts =
      isDropdownOpen ||
      isArchiveModalOpen ||
      isDeleteModalOpen ||
      isAccountSelectorOpen;
    setDisableShortcuts(shouldDisableShortcuts);
    return () => setDisableShortcuts(false);
  }, [
    isDropdownOpen,
    isArchiveModalOpen,
    isDeleteModalOpen,
    isAccountSelectorOpen,
    setDisableShortcuts,
  ]);
  const statuses = useOrderedKanbanColumns();
  const priorities = useTicketMetaStore((state) => state.priorities);
  const teamMembers = useTicketMetaStore((state) => state.teamMembers);
  const ticketTypes = useTicketMetaStore((state) => state.ticketType);
  const groups = useTicketMetaStore((state) => state.groups);
  const accounts = useTicketMetaStore((state) => state.accounts);
  const sentiment = useTicketMetaStore((state) => state.sentiments);

  const handleCopyId = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(
        `${team.identifier}-${ticketData.ticket_id}`,
      );
      toast(
        <div className="flex items-center gap-2">
          <Copy size={16} className="text-foreground" />
          <span>ID copied to clipboard</span>
        </div>,
        {
          style: {
            backgroundColor: "white",
            color: "#333",
            border: "1px solid #eaeaea",
          },
        },
      );
    } catch (err) {
      console.error("Failed to copy ID:", err);
      toast.error("Failed to copy ID");
    }
  }, [team.identifier, ticketData.ticket_id]);

  const handleCopyLink = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast(
        <div className="flex items-center gap-2">
          <Link size={16} className="text-foreground" />
          <span>Link copied to clipboard</span>
        </div>,
        {
          style: {
            backgroundColor: "white",
            color: "#333",
            border: "1px solid #eaeaea",
          },
        },
      );
    } catch (err) {
      console.error("Failed to copy link:", err);
      toast.error("Failed to copy link");
    }
  }, []);

  const { mutate: archiveMutation } = useApiMutation(
    ARCHIVE_TICKET_BY_ID(ticketData.uid),
    {},
    "PATCH",
  );

  const { mutate: updateTicket } = useApiMutation(
    UPDATE_TICKET_BY_ID(ticketData.uid),
    {},
    "PATCH",
  );

  // Modal states are now declared at the top of the component

  useHotkeys("mod+a", (e) => {
    e.preventDefault();
    setIsArchiveModalOpen((prev) => !prev);
  });

  const { mutate: deleteMutation } = useApiMutation(
    DELETE_TICKET_BY_ID(ticketData.uid),
    {},
    "DELETE",
  );
  // Delete modal state is now declared at the top of the component

  const router = useRouter();

  useHotkeys("mod+d", (e) => {
    e.preventDefault();
    setIsDeleteModalOpen((prev) => !prev);
  });

  const { mutate: updateTicketAccount } = useApiMutation(
    `/v1/tickets/${ticketData.uid}`,
    {},
    "PATCH",
  );

  const currentStatus = statuses?.find(
    (status) => status.uid === ticketData.status?.uid,
  );
  const currentPriority = priorities?.find(
    (priority) => priority.uid === ticketData.priority?.uid,
  );
  const currentSentiment = sentiment?.find(
    (s) => s.id === ticketData.sentiment?.uid,
  );
  const currentType = ticketTypes?.find(
    (type) => type.id === ticketData.type?.uid,
  );
  const currentGroup = groups?.find(
    (group) => group.id === ticketData.team?.uid,
  );
  const currentAccount = accounts?.find(
    (account) => account.uid === ticketData.account?.uid,
  );

  const assignedAgent = teamMembers.find(
    (agent) => agent.uid === ticketData.assignee?.uid,
  );

  const filteredStatuses = useMemo(() => {
    return statuses?.filter((status) => status.child_statuses?.length === 0);
  }, [statuses]);

  const handleAccountSelect = (accountId: string) => {
    updateTicketAccount({ accountId });
    setIsAccountSelectorOpen(false);
  };

  const archiveTicket = () => {
    archiveMutation({ archived: true });
    setIsArchiveModalOpen(false);
  };

  const { mutate: unarchiveMutation } = useApiMutation(
    `/v1/tickets/${ticketData.uid}/unarchive`,
    {},
    "PATCH",
  );

  const unArchive = () => {
    unarchiveMutation({});
  };

  const deleteTicket = async () => {
    try {
      await deleteMutation({});

      // Only navigate after successful deletion
      if (!isLastTicket) {
        drawerNavigate("down");
      } else {
        closeDrawer(router);
      }
    } catch (error) {
      console.error("Failed to delete ticket:", error);
      toast.error("Failed to delete ticket");
    } finally {
      setIsDeleteModalOpen(false);
    }
  };

  return (
    <div className="flex items-center">
      <DropdownMenu
        open={isDropdownOpen}
        onOpenChange={(open) => {
          setIsDropdownOpen(open);
        }}
        modal={true}
      >
        <DropdownMenuTrigger asChild>
          <button
            ref={dropdownTriggerRef}
            type="button"
            className="focus:outline-none"
          >
            <Ellipsis size={16} />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[220px]">
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={handleCopyId}>
              <Copy size={16} className="text-muted-foreground mr-2" />
              <span>Copy ID</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleCopyLink}>
              <Link size={16} className="text-muted-foreground mr-2" />
              <span>Copy link</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>

          <DropdownMenuSeparator />

          <DropdownMenuGroup>
            {/* Status submenu */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <BarChart2 size={16} className="text-muted-foreground mr-2" />
                <div className="flex items-center justify-between w-full">
                  <span>Status</span>
                  <span className="text-xs text-muted-foreground">S</span>
                </div>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent className="min-w-[220px]">
                {filteredStatuses?.map((option) => (
                  <DropdownMenuItem
                    key={option.uid}
                    onClick={() => {
                      if (option.uid === currentStatus?.uid) return;
                      updateTicket({ statusId: option.uid }, {});
                    }}
                  >
                    <div className="flex items-center gap-2">
                      {getLaneIcon(
                        toUpper(
                          option.parent_status?.name || option.display_name,
                        ),
                      )}
                      <span>{option.display_name}</span>
                    </div>
                    {currentStatus?.uid === option?.uid && (
                      <CheckIcon strokeWidth={3} className="h-4 w-4 ml-auto" />
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuSub>

            {/* Urgency submenu */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <AlertCircle size={16} className="text-muted-foreground mr-2" />
                <div className="flex items-center justify-between w-full">
                  <span>Priority</span>
                  <span className="text-xs text-muted-foreground">P</span>
                </div>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent className="min-w-[180px]">
                {priorities?.map((option) => (
                  <DropdownMenuItem
                    key={option.uid}
                    onClick={() => {
                      if (option.uid === currentPriority?.uid) return;
                      updateTicket({ priorityId: option.uid });
                    }}
                  >
                    <div className="flex items-center gap-2">
                      {getUrgencyIcon(toUpper(option?.name))}
                      <span>{option.name}</span>
                    </div>
                    {currentPriority?.uid === option?.uid && (
                      <CheckIcon strokeWidth={3} className="h-4 w-4 ml-auto" />
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuSub>

            {/* Assignee submenu */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <User size={16} className="text-muted-foreground mr-2" />
                <div className="flex items-center justify-between w-full">
                  <span>Assignee</span>
                  <span className="text-xs text-muted-foreground">A</span>
                </div>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent className="min-w-[220px] max-h-[300px] overflow-y-auto">
                <DropdownMenuItem
                  onClick={() => {
                    updateTicket({ assignedAgentId: "UNASSIGN" });
                  }}
                >
                  <div className="flex items-center gap-2">
                    <span>Unassigned</span>
                  </div>
                  {!assignedAgent && (
                    <CheckIcon strokeWidth={3} className="h-4 w-4 ml-auto" />
                  )}
                </DropdownMenuItem>
                {teamMembers?.map((option) => (
                  <DropdownMenuItem
                    key={option.id}
                    onClick={() => {
                      if (option.uid === assignedAgent?.uid) return;
                      updateTicket({ assignedAgentId: option.uid });
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <Avatar
                        src={option.avatar_url}
                        alt={option.name}
                        fallbackText={option.name}
                        imgClassnames="h-4 w-4 rounded"
                      />
                      <span>{option.name}</span>
                      {option?.metadata?.isAgent && (
                        <AgentLabel className="w-12 ml-[-12px]" />
                      )}
                    </div>
                    {assignedAgent?.uid === option?.uid && (
                      <CheckIcon strokeWidth={3} className="h-4 w-4 ml-auto" />
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuSub>

            {/* Type submenu */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <TypeOutline size={16} className="text-muted-foreground mr-2" />
                <div className="flex items-center justify-between w-full">
                  <span>Type</span>
                  <span className="text-xs text-muted-foreground">T</span>
                </div>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent className="min-w-[180px]">
                {ticketTypes?.map((option) => (
                  <DropdownMenuItem
                    key={option.id}
                    onClick={() => {
                      if (option.id === currentType?.id) return;
                      updateTicket({ typeId: option.id });
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <span>{option.name}</span>
                    </div>
                    {currentType?.id === option?.id && (
                      <CheckIcon strokeWidth={3} className="h-4 w-4 ml-auto" />
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuSub>

            {/* Group submenu */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <UsersRound size={16} className="text-muted-foreground mr-2" />
                <span>Group</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent className="min-w-[180px]">
                {groups && groups.length > 0 ? (
                  groups.map((option) => (
                    <DropdownMenuItem
                      key={option.id}
                      onClick={() => {
                        if (option.id === currentGroup?.id) return;
                        updateTicket({ teamId: option.id });
                      }}
                    >
                      <div className="flex items-center gap-2">
                        <span>{option.name}</span>
                      </div>
                      {currentGroup?.id === option?.id && (
                        <CheckIcon
                          strokeWidth={3}
                          className="h-4 w-4 ml-auto"
                        />
                      )}
                    </DropdownMenuItem>
                  ))
                ) : (
                  <DropdownMenuItem disabled>
                    <span className="text-muted-foreground">
                      No groups available
                    </span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuSubContent>
            </DropdownMenuSub>

            {/* Sentiment submenu */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Smile size={16} className="text-muted-foreground mr-2" />
                <div className="flex items-center justify-between w-full">
                  <span>Sentiment</span>
                  <span className="text-xs text-muted-foreground">E</span>
                </div>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent className="min-w-[180px]">
                {sentiment?.map((option) => (
                  <DropdownMenuItem
                    key={option.id}
                    onClick={() => {
                      if (option.id === currentSentiment?.id) return;
                      updateTicket({ sentimentId: option.id });
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <span>{option.name}</span>
                    </div>
                    {currentSentiment?.id === option?.id && (
                      <CheckIcon strokeWidth={3} className="h-4 w-4 ml-auto" />
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuSub>

            {/* Account submenu */}
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Building2 size={16} className="text-muted-foreground mr-2" />
                <span>Account</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent className="min-w-[220px]">
                <DropdownMenuItem
                  onClick={() => setIsAccountSelectorOpen(true)}
                  className="border-b"
                >
                  <span>Search accounts...</span>
                </DropdownMenuItem>
                {accounts?.map((option) => (
                  <DropdownMenuItem
                    key={option.id}
                    onClick={() => {
                      if (option.uid === currentAccount?.uid) return;
                      updateTicket({ accountId: option.uid });
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <span>{option.name}</span>
                    </div>
                    {currentAccount?.uid === option?.uid && (
                      <CheckIcon strokeWidth={3} className="h-4 w-4 ml-auto" />
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          </DropdownMenuGroup>

          <DropdownMenuSeparator />
          {/* Mark as related ticket , Move to team */}
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={() => setShowRelatedTicketModal(true)}>
              <div className="flex items-center gap-2">
                <SquareEqual size={16} className="text-muted-foreground" />
                <span>Mark as related ticket</span>
              </div>
            </DropdownMenuItem>
            {/* <DropdownMenuItem>
              <div className="flex items-center gap-2">
                <PersonStanding size={16} className="text-muted-foreground" />
                <span>Move to team</span>
              </div>
            </DropdownMenuItem> */}
          </DropdownMenuGroup>

          <DropdownMenuSeparator />

          <DropdownMenuGroup>
            {ticketData.archivedAt ? (
              <DropdownMenuItem onClick={unArchive}>
                <Archive size={16} className="text-muted-foreground mr-2" />
                <span>Unarchive</span>
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem onClick={() => setIsArchiveModalOpen(true)}>
                <Archive size={16} className="text-muted-foreground mr-2" />
                <span>Archive</span>
                <DropdownMenuShortcut>⌘A</DropdownMenuShortcut>
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => setIsDeleteModalOpen(true)}>
              <Trash2 size={16} className="text-muted-foreground mr-2" />
              <span>Delete</span>
              <DropdownMenuShortcut>⌘D</DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>

      <WarningModal
        title="Archive ticket"
        description="Are you sure you want to archive this ticket?"
        okText="Archive"
        onOk={archiveTicket}
        isOpen={isArchiveModalOpen}
        setIsOpen={setIsArchiveModalOpen}
      />
      <WarningModal
        title="Delete ticket"
        description="Are you sure you want to delete this ticket?"
        okText="Delete"
        onOk={deleteTicket}
        isOpen={isDeleteModalOpen}
        setIsOpen={setIsDeleteModalOpen}
      />

      {/* Related Ticket Dialog */}
      <Dialog
        open={showRelatedTicketModal}
        onOpenChange={setShowRelatedTicketModal}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Mark as related ticket</DialogTitle>
            <DialogDescription>
              Adds a visible link between two tickets so they appear in each
              other&apos;s related section.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <TicketSearch
              onSelectTicket={setSelectedTicket}
              selectedTicket={selectedTicket}
              required={true}
              debounceMs={300}
              currentTicketId={ticketData.uid}
            />
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowRelatedTicketModal(false);
                setSelectedTicket(null);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleRelateTicket} disabled={!selectedTicket}>
              Relate Ticket
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog
        open={isAccountSelectorOpen}
        onOpenChange={setIsAccountSelectorOpen}
      >
        <DialogContent
          className="sm:max-w-md p-0 overflow-hidden bg-background border border-color-border rounded-md shadow-md transition-all"
          style={{
            transform: "translate(-50%, -60%)",
          }}
        >
          <div className="flex justify-between items-center px-6 pt-3 pb-2 border-b border-color-border bg-color-card-bg-light">
            <DialogTitle className="text-base font-semibold text-foreground">
              {ticketData.account ? "Change account" : "Select account"}
            </DialogTitle>
          </div>
          <div className="px-6 pt-4 pb-6 flex items-center justify-center">
            <AccountSelector
              onSelect={(accountId) => handleAccountSelect(accountId)}
              placeholder="Search for an account"
              emptyMessage="No accounts found"
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MoreOptions;
