"use client";

import { Separator } from "@/components/ui/separator";
import { useTicketDetailsDisplayOptions } from "@/store/ticket-details-display-options";
import { SingleTicket } from "@/types/kanban";
import React from "react";
import AccountView from "./AccountView";
import AppsView from "./AppsView";
import CustomFieldsView from "./CustomFieldsView";
import Forms from "./Forms";
import RecentIssueView from "./RecentIssueView";
import RelatedTicketView from "./RelatedTicketView";
import SlaView from "./SlaView";
import TagsView from "./TagsView";

const getDisplayComponent = (
  id: string,
  ticketData: SingleTicket,
  customFieldsIds: string[],
) => {
  // Check if this is an app-specific ID (format: app-{appId})
  if (id.startsWith("app-")) {
    const appId = id.substring(4); // Extract the appId from the ID
    return <AppsView ticketData={ticketData} specificAppId={appId} />;
  }

  switch (id) {
    case "form":
      return <Forms ticketData={ticketData} />;
    case "slas":
      return <SlaView ticketData={ticketData} />;
    case "tags":
      return <TagsView ticketData={ticketData} />;
    case "account":
      if (!ticketData.account?.uid) return null;
      return <AccountView ticketData={ticketData} />;
    case "custom":
      if (!customFieldsIds?.length || customFieldsIds?.length === 0)
        return null;

      return (
        <CustomFieldsView
          customFieldsIds={customFieldsIds}
          ticketData={ticketData}
        />
      );
    case "recent-issue":
      if (!ticketData.account?.uid) return null;
      return <RecentIssueView ticketData={ticketData} />;

    case "related-tickets":
      return <RelatedTicketView />;

    default:
      return null;
  }
};

type Tprops = {
  ticketData: SingleTicket;
  customFieldsIds: string[];
};

const Details = ({ ticketData, customFieldsIds }: Tprops) => {
  const fields = useTicketDetailsDisplayOptions((state) => state.fields);
  return (
    <div className="h-[calc(100vh-10rem)]">
      {fields
        .filter((field) => field.visible)
        .map((field, index) => {
          const component = getDisplayComponent(
            field.id,
            ticketData,
            customFieldsIds,
          );

          return (
            <React.Fragment key={field.id}>
              {index > 0 && <Separator className="my-4" />}
              {component}
            </React.Fragment>
          );
        })}
    </div>
  );
};

export default Details;
