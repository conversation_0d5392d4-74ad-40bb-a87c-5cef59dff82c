"use client";

import Avatar from "@/components/common/Avatar";
import TooltipWrapper from "@/components/tooltip-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useApi } from "@/hooks/use-api";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { format, formatDistanceToNow } from "date-fns";
import {
  Calendar as CalendarLucide,
  ExternalLink,
  Loader2,
  Plus,
  Search,
  Unlink2,
  Unplug,
  User,
  X,
} from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import styled from "styled-components";
import { z } from "zod";
import { CustomAppComponentProps } from ".";

 
interface JiraTicketData {
  jiraUrl: string;
  platformTicketId?: string;
  platformRecordId: string;
  summary?: string;
  description?: string;
  status?: string;
  priority?: string;
  createdAt?: string;
  assignee?: {
    displayName: string;
    avatarUrls?: Record<string, string>;
  } | null;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface JiraSearchResult {
  jiraId: string;
  jiraKey: string;
  jiraUrl: string;
  summary: string;
  projectKey: string;
  status: string;
  priority: string;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface JiraProject {
  key: string;
  name: string;
  jiraId: string;
  organizationId: string;
}

interface ProjectsApiResponse<T> {
  data: T[];
  pagination?: {
    limit: number;
    offset: number;
    total: number;
  };
}

interface JiraIssueTypeField {
  key: string;
  name: string;
  required: boolean;
  hasDefaultValue: boolean;
  type: string;
  options?: Array<{ id: string; name: string }>;
  customId?: number;
  items?: string;
  defaultValue?: { id: string; name: string };
}

interface JiraIssueType {
  id: string;
  jiraId: string;
  name: string;
  isSubTask: boolean;
  fields: JiraIssueTypeField[];
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface JiraProjectMetadata {
  projectKey: string;
  projectName: string;
  projectId: string;
  description: string | null;
  metadata: Record<string, unknown>;
  issueTypes: JiraIssueType[];
}

interface LoadingIndicatorProps {
  message?: string;
  size?: "small" | "medium";
  timeout?: number; // Timeout in milliseconds
}

// Loading Indicator Component
const LoadingIndicator = ({
  message = "Loading...",
  size = "medium",
  timeout = 30000, // Default 30 second timeout
}: LoadingIndicatorProps) => {
  const sizeClass = size === "small" ? "h-4 w-4" : "h-5 w-5";
  const textClass = size === "small" ? "text-sm" : "text-base";
  const [showTimeout, setShowTimeout] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowTimeout(true);
    }, timeout);

    return () => clearTimeout(timer);
  }, [timeout]);

  return (
    <div className="flex flex-col items-center py-2">
      <div className="flex items-center">
        <Loader2 className={`${sizeClass} animate-spin text-primary`} />
        <span className={`ml-2 ${textClass}`}>{message}</span>
      </div>
      {showTimeout && (
        <p className="text-xs text-amber-500 mt-1">
          This is taking longer than expected...
        </p>
      )}
    </div>
  );
};

interface ErrorMessageProps {
  message?: string;
  details?: string;
}

// Error Message Component
const ErrorMessage = ({
  message = "An error occurred. Please try again.",
  details,
}: ErrorMessageProps) => (
  <div className="text-center py-4">
    <p className="text-sm text-red-500">{message}</p>
    {details && <p className="text-xs text-muted-foreground mt-1">{details}</p>}
  </div>
);

interface EmptyStateProps {
  onSearch: () => void;
  onCreate: () => void;
}

const EmptyState = ({ onSearch, onCreate }: EmptyStateProps) => (
  <div className="flex flex-col items-center justify-center w-full py-4 text-center">
    <div className="bg-muted rounded-md p-3 mb-2">
      <Unplug className="h-5 w-5 text-muted-foreground" />
    </div>
    <p className="text-sm text-muted-foreground mb-4">
      No issues have been linked yet.
    </p>
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1"
        onClick={onSearch}
      >
        <Search className="h-4 w-4" />
        <span>Search issue</span>
      </Button>
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1"
        onClick={onCreate}
      >
        <Plus className="h-4 w-4" />
        <span>Create issue</span>
      </Button>
    </div>
  </div>
);

interface ModalContainerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: React.ReactNode;
  children: React.ReactNode;
  maxHeight?: string;
  noPadding?: boolean;
}

const ModalContainer = ({
  open,
  onOpenChange,
  title,
  children,
  maxHeight = "calc(85vh - 100px)",
  noPadding = false,
}: ModalContainerProps) => (
  <Dialog open={open} onOpenChange={onOpenChange}>
    <DialogContent
      className="sm:max-w-xl p-0 overflow-hidden bg-background border border-color-border rounded-[4px] shadow-md transition-all"
      style={{
        transform: "translate(-50%, -50%)",
        width: "90vw",
        maxWidth: "600px",
        maxHeight: "85vh",
      }}
    >
      <div className="flex justify-between items-center px-6 pt-3 pb-2 border-b border-color-border bg-color-card-bg-light">
        <DialogTitle className="text-base font-semibold text-foreground">
          {title}
        </DialogTitle>
      </div>
      <div
        className={`px-6 pt-2 ${noPadding ? "pb-2" : "pb-6"} space-y-4`}
        style={{ maxHeight, overflowY: "auto" }}
      >
        {children}
      </div>
    </DialogContent>
  </Dialog>
);

interface ActionButtonsProps {
  onSearch: () => void;
  onCreate: () => void;
}

const ActionButtons = ({ onSearch, onCreate }: ActionButtonsProps) => (
  <div className="flex space-x-2">
    <Button
      variant="outline"
      size="icon"
      className="h-6 w-6 rounded-[4px]"
      onClick={onSearch}
    >
      <Search className="h-3 w-3" />
    </Button>
    <Button
      variant="outline"
      size="icon"
      className="h-6 w-6 rounded-[4px]"
      onClick={onCreate}
    >
      <Plus className="h-3 w-3" />
    </Button>
  </div>
);

interface JiraApiResponse<T> {
  data?: T;
  error?: string;
  details?: unknown;
  status?: number;
  message?: string;
  statusText?: string;
}

// Add debouncing to prevent multiple rapid calls
const linkJiraIssue = async (
  ticketId: string,
  jiraKey: string,
): Promise<JiraApiResponse<unknown>> => {
  // Return early if any required parameters are missing
  if (!ticketId || !jiraKey) {
    return {
      error: "Missing required parameters",
      details: !ticketId
        ? "Platform ticket ID is required"
        : "JIRA ticket ID is required",
      status: 400,
    };
  }

  try {
    const response = await fetch("/api/custom-apps/jira/issue/link", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        platformTicketId: ticketId,
        jiraTicketId: jiraKey,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        error: "Failed to link issue",
        details: errorData,
        message:
          errorData.message ||
          errorData.error ||
          `Error ${response.status}: ${response.statusText}`,
        status: response.status,
      };
    }

    const data = await response.json();
    return { data };
  } catch (error) {
    // Only log errors in development
    if (process.env.NODE_ENV === "development") {
      console.debug("Error linking JIRA issue:", error);
    }

    return {
      error: "Network error while linking issue",
      details: error instanceof Error ? error.message : String(error),
      message:
        error instanceof Error
          ? error.message
          : "Failed to connect to the server",
    };
  }
};

// Function to unlink a JIRA issue
const unlinkJiraIssue = async (
  platformTicketId: string,
  jiraTicketId: string,
): Promise<JiraApiResponse<unknown>> => {
  try {
    const response = await fetch(`/api/custom-apps/jira/issue/unlink`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-org-uid": localStorage.getItem("orgUid") || "",
      },
      body: JSON.stringify({
        platformTicketId,
        jiraTicketId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        error: errorData.error || `Failed to unlink Jira issue: ${response.status} ${response.statusText}`,
        details: errorData.details || errorData,
      };
    }

    const data = await response.json();
    return {
      data,
      error: null,
    };
  } catch (error) {
    console.error("Error unlinking Jira issue:", error);
    return {
      error: "Failed to unlink Jira issue",
      details: error,
    };
  }
};

interface CreateJiraIssuePayload {
  projectKey: string;
  platformTicketId: string;
  summary: string;
  description: string;
  issueType: string;
  priority?: string;
  labels?: string[];
  customFields?: Record<string, unknown>;
  [key: string]: unknown;
}

// Create Jira Issue
// Add validation and improved error handling
const createJiraIssue = async (
  payload: CreateJiraIssuePayload,
): Promise<JiraApiResponse<unknown>> => {
  // Validate required fields before making the API call
  if (!payload.projectKey) {
    return {
      error: "Missing required field",
      details: "Project key is required",
      message: "Project key is required",
      status: 400,
    };
  }

  if (!payload.platformTicketId) {
    return {
      error: "Missing required field",
      details: "Platform ticket ID is required",
      message: "Platform ticket ID is required",
      status: 400,
    };
  }

  if (!payload.summary) {
    return {
      error: "Missing required field",
      details: "Summary is required",
      message: "Summary is required",
      status: 400,
    };
  }

  if (!payload.issueType) {
    return {
      error: "Missing required field",
      details: "Issue type is required",
      message: "Issue type is required",
      status: 400,
    };
  }

  try {
    const response = await fetch("/api/custom-apps/jira/issue", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        error: "Failed to create issue",
        details: errorData,
        message:
          errorData.message ||
          errorData.error ||
          `Error ${response.status}: ${response.statusText}`,
        status: response.status,
      };
    }

    const data = await response.json();
    return { data };
  } catch (error) {
    // Only log errors in development
    if (process.env.NODE_ENV === "development") {
      console.debug("Error creating JIRA issue:", error);
    }

    return {
      error: "Network error while creating issue",
      details: error instanceof Error ? error.message : String(error),
      message:
        error instanceof Error
          ? error.message
          : "Failed to connect to the server",
    };
  }
};

// Renamed to avoid collision with imported FormField component
interface JiraFormField {
  key: string;
  name: string;
  type: string;
  required: boolean;
  hasDefaultValue?: boolean;
  defaultValue?: { id: string; name: string };
  options?: Array<{ id: string; name: string }>;
  items?: string;
}

const createFormSchema = (fields: JiraFormField[]) => {
  const schemaFields = {};

  fields.forEach((field) => {
    // Skip certain fields that are handled separately or not needed
    if (["issuetype", "project", "platformticketid"].includes(field.key)) {
      return;
    }

    let fieldSchema;

    // Determine schema type based on field type
    switch (field.type) {
      case "string":
        fieldSchema = z.string();
        break;
      case "date":
      case "datetime":
        fieldSchema = z.string();
        break;
      case "number":
        fieldSchema = z.number();
        break;
      case "array":
        fieldSchema = z.array(z.string());
        break;
      case "option":
      case "status":
      case "priority":
        // For option-like fields, we accept either a string ID or an object with id/name
        fieldSchema = z.union([
          z.string(),
          z.object({
            id: z.string(),
            name: z.string(),
          }),
        ]);
        break;
      default:
        fieldSchema = z.any();
    }

    // Add requirements
    if (
      field.required &&
      (field.type === "string" || field.type === "option")
    ) {
      fieldSchema = fieldSchema.min(1, {
        message: `${field.name} is required`,
      });
    } else {
      fieldSchema = fieldSchema.optional();
    }

    schemaFields[field.key] = fieldSchema;
  });

  return z.object(schemaFields);
};

const renderFieldInput = (field, formField) => {
  switch (field.type) {
    case "string":
      if (field.key === "description") {
        return (
          <Textarea
            placeholder={`Enter ${field.name.toLowerCase()}`}
            className="resize-max min-h-[80px]"
            {...formField}
            required={true}
          />
        );
      }

      return (
        <Input
          placeholder={`Enter ${field.name.toLowerCase()}`}
          {...formField}
        />
      );

    case "date":
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !formField.value && "text-muted-foreground",
              )}
            >
              {formField.value ? (
                format(new Date(formField.value), "PPP")
              ) : (
                <span>Pick a date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={formField.value ? new Date(formField.value) : undefined}
              onSelect={(date) =>
                formField.onChange(date ? date.toISOString() : "")
              }
              initialFocus
              className="p-2"
            />
          </PopoverContent>
        </Popover>
      );

    case "datetime":
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !formField.value && "text-muted-foreground",
              )}
            >
              {formField.value ? (
                format(new Date(formField.value), "PPP p")
              ) : (
                <span>Pick date and time</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="p-3">
              <Calendar
                mode="single"
                selected={
                  formField.value ? new Date(formField.value) : undefined
                }
                onSelect={(date) => {
                  if (date) {
                    // Keep the time part if it exists, otherwise set to current time
                    const currentDate = formField.value
                      ? new Date(formField.value)
                      : new Date();
                    date.setHours(currentDate.getHours());
                    date.setMinutes(currentDate.getMinutes());
                    formField.onChange(date.toISOString());
                  } else {
                    formField.onChange("");
                  }
                }}
                initialFocus
                className="p-2"
              />
              <div className="mt-3">
                <Input
                  type="time"
                  value={
                    formField.value
                      ? format(new Date(formField.value), "HH:mm")
                      : ""
                  }
                  onChange={(e) => {
                    if (e.target.value && formField.value) {
                      const [hours, minutes] = e.target.value.split(":");
                      const date = new Date(formField.value);
                      date.setHours(parseInt(hours, 10));
                      date.setMinutes(parseInt(minutes, 10));
                      formField.onChange(date.toISOString());
                    }
                  }}
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      );

    case "number":
      return (
        <Input
          type="number"
          placeholder={`Enter ${field.name.toLowerCase()}`}
          {...formField}
          onChange={(e) => formField.onChange(Number(e.target.value))}
        />
      );

    case "array":
      if (field.items === "option" && field.options) {
        return (
          <div className="space-y-2">
            {field.options.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`${field.key}-${option.id}`}
                  checked={
                    Array.isArray(formField.value) &&
                    formField.value.includes(option.id)
                  }
                  onCheckedChange={(checked) => {
                    const currentValues = Array.isArray(formField.value)
                      ? formField.value
                      : [];
                    if (checked) {
                      formField.onChange([...currentValues, option.id]);
                    } else {
                      formField.onChange(
                        currentValues.filter((v) => v !== option.id),
                      );
                    }
                  }}
                />
                <label
                  htmlFor={`${field.key}-${option.id}`}
                  className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {option.name || `Option ${option.id}`}
                </label>
              </div>
            ))}
          </div>
        );
      }
      return (
        <Input
          placeholder={`Enter ${field.name.toLowerCase()}`}
          {...formField}
        />
      );

    case "option":
    case "status":
    case "priority":
      if (field.options) {
        return (
          <Select
            value={formField.value?.id || formField.value}
            onValueChange={(value) => {
              // Find the option with this ID to get the name
              const selectedOption = field.options.find(
                (opt) => opt.id === value,
              );
              // Store both ID and name
              if (selectedOption) {
                formField.onChange({
                  id: value,
                  name: selectedOption.name,
                });
              } else {
                formField.onChange(value);
              }
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={`Select ${field.name.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options.map((option) => (
                <SelectItem key={option.id} value={option.id}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      }
      return (
        <Input
          placeholder={`Enter ${field.name.toLowerCase()}`}
          {...formField}
        />
      );

    case "option-with-child":
      if (field.options) {
        return (
          <RadioGroup
            value={formField.value}
            onValueChange={formField.onChange}
            className="flex flex-col space-y-1"
          >
            {field.options.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={option.id}
                  id={`${field.key}-${option.id}`}
                />
                <label
                  htmlFor={`${field.key}-${option.id}`}
                  className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {option.name || `Option ${option.id}`}
                </label>
              </div>
            ))}
          </RadioGroup>
        );
      }
      return (
        <Input
          placeholder={`Enter ${field.name.toLowerCase()}`}
          {...formField}
        />
      );

    default:
      return (
        <Input
          placeholder={`Enter ${field.name.toLowerCase()}`}
          {...formField}
        />
      );
  }
};

const renderFormField = (field, form) => {
  // Skip certain fields that are handled separately or not needed
  if (["issuetype", "project", "platformticketid"].includes(field.key)) {
    return null;
  }

  return (
    <FormField
      key={field.key}
      control={form.control}
      name={field.key}
      render={({ field: formField }) => (
        <FormItem>
          <FormLabel>
            {field.name}
            {field.required ? <span className="text-red-500"> *</span> : ""}
          </FormLabel>
          <FormControl>{renderFieldInput(field, formField)}</FormControl>
          {field.hasDefaultValue && field.defaultValue && (
            <FormDescription>
              Default: {field.defaultValue.name || "Set automatically"}
            </FormDescription>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

const JiraSearchModal = ({
  open,
  onOpenChange,
  onLinkIssue,
  isLinking,
  _ticketId, // Renamed with underscore to indicate it's intentionally unused
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  // Track which specific issue is being linked
  const [linkingIssueKey, setLinkingIssueKey] = useState("");

  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery.length >= 3) {
        setDebouncedQuery(searchQuery);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  useEffect(() => {
    const fetchSearchResults = async () => {
      if (debouncedQuery.length < 3) return;

      setSearchLoading(true);
      try {
        const response = await fetch(
          `/api/custom-apps/jira/issue/search?query=${encodeURIComponent(
            debouncedQuery,
          )}&limit=20`,
        );
        if (!response.ok) throw new Error("Search failed");

        const data = await response.json();

        if (data && data.data) {
          setSearchResults(data.data || []);
        } else {
          setSearchResults(data || []);
        }
      } catch (error) {
        console.error("Error searching Jira issues:", error);
        setSearchResults([]);
      } finally {
        setSearchLoading(false);
      }
    };

    fetchSearchResults();
  }, [debouncedQuery]);

  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchQuery("");
    setDebouncedQuery("");
    setSearchResults([]);
  }, []);

  const _closeSearch = useCallback(() => {
    onOpenChange(false);
    clearSearch();
    // Reset linking issue key when closing
    setLinkingIssueKey("");
  }, [clearSearch, onOpenChange]);

  const renderSearchResults = () => {
    if (searchLoading) {
      return <LoadingIndicator message="Searching..." />;
    }

    if (!debouncedQuery || debouncedQuery.length < 3) {
      return null;
    }

    if (searchResults.length === 0) {
      return (
        <div className="text-center py-4 text-sm text-muted-foreground">
          No results found
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {searchResults.map((result) => (
          <div
            key={result.jiraKey}
            className="flex items-center justify-between p-2 rounded-[4px] border bg-background hover:bg-muted"
          >
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <span className="font-medium text-xs">{result.jiraKey}</span>
                <span className="ml-2 text-[10px] px-1 py-0.5 rounded-[3px] bg-muted">
                  {result.status}
                </span>
                <span className="ml-1 text-[10px] px-1 py-0.5 rounded-[3px] bg-muted">
                  {result.priority}
                </span>
              </div>
              <p className="text-xs text-muted-foreground truncate">
                {result.summary}
              </p>
            </div>
            <Button
              size="sm"
              variant="outline"
              className="ml-2 h-8 px-2"
              onClick={() => {
                setLinkingIssueKey(result.jiraKey);
                onLinkIssue(result.jiraKey);
              }}
              disabled={isLinking && linkingIssueKey === result.jiraKey}
            >
              {isLinking && linkingIssueKey === result.jiraKey ? (
                <>
                  <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                  Linking...
                </>
              ) : (
                "Link"
              )}
            </Button>
          </div>
        ))}
      </div>
    );
  };

  return (
    <ModalContainer
      open={open}
      onOpenChange={onOpenChange}
      noPadding={true}
      title={
        <div className="flex items-center gap-2">
          <img src="/jira.svg" alt="Jira Logo" className="h-5 w-5" />
          <span>Search Jira issues</span>
        </div>
      }
    >
      <div className="space-y-4">
        <div className="relative">
          <Input
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            className="pr-8"
            placeholder="Search by issue key or summary"
            autoFocus
          />
          {/* Only show the X button inside the input when there's text */}
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-0 h-full w-8 p-0"
              onClick={clearSearch}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        <div className="max-h-80 overflow-y-auto">{renderSearchResults()}</div>
      </div>
    </ModalContainer>
  );
};

const CreateJiraIssueModal = ({
  open,
  onOpenChange,
  onSuccess,
  ticketData,
}) => {
  const [selectedProject, setSelectedProject] = useState("");
  const [selectedIssueType, setSelectedIssueType] = useState("");
  const [formFields, setFormFields] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    data: projectsData,
    loading: projectsLoading,
    error: projectsError,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } = useApi<ProjectsApiResponse<any>>(
    "/api/custom-apps/jira/project",
    {},
    { enabled: open, isNextApi: true },
  );

  const {
    data: projectMetadata,
    loading: metadataLoading,
    error: metadataError,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } = useApi<any>(
    selectedProject
      ? `/api/custom-apps/jira/project/${selectedProject}/metadata`
      : null,
    {},
    { enabled: !!selectedProject, isNextApi: true },
  );

  const projects = projectsData?.data || [];

  useEffect(() => {}, [projectMetadata]);

  const issueTypes =
    selectedProject && projectMetadata?.issueTypes
      ? projectMetadata.issueTypes
      : [];

  useEffect(() => {}, [issueTypes, selectedProject]);

  useEffect(() => {
    if (selectedIssueType && projectMetadata) {
      const currentProjectIssueTypes = projectMetadata.issueTypes || [];
      const selectedType = currentProjectIssueTypes.find(
        (type) => type.id === selectedIssueType,
      );

      if (selectedType) {
        const typeFields = selectedType.fields.filter(
          (field) => !["issuetype", "project"].includes(field.key),
        );

        const processedFields = typeFields.map((field) => {
          if (field.key === "description") {
            return { ...field, required: true };
          }
          return field;
        });

        setFormFields(processedFields);

        form.reset();
      }
    }
  }, [selectedIssueType, projectMetadata]);

  useEffect(() => {
    setSelectedIssueType("");
    setFormFields([]);
  }, [selectedProject]);

  const form = useForm({
    resolver: zodResolver(createFormSchema(formFields)),
    defaultValues: {},
    mode: "onChange",
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onSubmit = async (data: Record<string, any>) => {
    setIsSubmitting(true);
    try {
      const ticketId = ticketData.uid;

      const currentProjectIssueTypes = projectMetadata?.issueTypes || [];
      const selectedTypeObj = currentProjectIssueTypes.find(
        (type) => type.id === selectedIssueType,
      );
      const issueTypeName = selectedTypeObj?.name || "";

      const standardFields: {
        projectKey: string;
        platformTicketId: string;
        summary: string;
        description: string;
        issueType: string;
        priority?: string;
        labels: string[];
        status?: string;
        [key: string]: unknown;
      } = {
        projectKey: selectedProject,
        platformTicketId: ticketId,
        summary: data.summary || "",
        description: data.description || "",
        issueType: issueTypeName,
        priority: data.priority?.name || "",
        labels: data.labels || [],
      };

      const customFields: Record<string, unknown> = {};

      if (data.status) {
        if (
          typeof data.status === "object" &&
          data.status !== null &&
          "name" in data.status
        ) {
          standardFields.status = data.status.name;
        } else {
          const statusField = formFields.find(
            (f) => f.key === "status" || f.type === "status",
          );
          if (statusField && statusField.options) {
            const statusOption = statusField.options.find(
              (opt) => opt.id === data.status,
            );
            if (statusOption) {
              standardFields.status = statusOption.name;
            } else {
              standardFields.status = String(data.status);
            }
          } else {
            standardFields.status = String(data.status);
          }
        }
      }

      const processFieldValue = (
        value: unknown,
        fieldDef?: JiraIssueTypeField,
      ) => {
        if (!fieldDef) return value;

        if (
          fieldDef.type === "option" &&
          typeof value === "object" &&
          value !== null
        ) {
          // For option fields, return the name instead of the ID
          if ("name" in value) {
            return value.name;
          } else if ("id" in value) {
            // If we only have the ID, try to find the name from options
            if (fieldDef.options) {
              const option = fieldDef.options.find(
                (opt) => opt.id === value.id,
              );
              if (option) return option.name;
            }
            return value.id;
          }
        } else if (fieldDef.type === "array" && Array.isArray(value)) {
          // For array fields, convert to comma-separated string of option names
          if (fieldDef.items === "option" && fieldDef.options) {
            // Map IDs to names and join with commas
            return value
              .map((id) => {
                const option = fieldDef.options?.find((opt) => opt.id === id);
                return option ? option.name : id;
              })
              .join(",");
          }
          // If it's not an array of options or we don't have option definitions,
          // just join the values
          return value.join(",");
        }

        return value;
      };

      const knownStandardFields = [
        "summary",
        "description",
        "priority",
        "labels",
        "status",
        "issueType",
        "projectKey",
        "platformTicketId",
      ];

      for (const [key, value] of Object.entries(data)) {
        if (
          ["summary", "description", "priority", "labels", "status"].includes(
            key,
          )
        ) {
          continue;
        }

        // Determine if this is a custom field
        const fieldDef = formFields.find((f) => f.key === key);
        const isCustomField =
          // Check if key starts with customfield_ (Jira's naming convention)
          key.startsWith("customfield_") ||
          // Check if field has a customId property
          (fieldDef && fieldDef.customId !== undefined) ||
          // If not in known standard fields list, treat as custom
          !knownStandardFields.includes(key);

        const processedValue = processFieldValue(value, fieldDef);

        if (isCustomField) {
          customFields[key] = processedValue;
        } else {
          standardFields[key] = processedValue;
        }
      }

      const payload: CreateJiraIssuePayload = {
        ...standardFields,
        customFields:
          Object.keys(customFields).length > 0 ? customFields : undefined,
      };

      const result = await createJiraIssue(payload);

      if (result.error) {
        console.error(
          "Error creating JIRA issue:",
          result.error,
          result.details,
        );
        setIsSubmitting(false);
        return;
      }

      const responseData =
        (result.data as {
          key?: string;
          self?: string;
          status?: { name?: string };
        }) || {};
      const jiraKey = responseData.key || "UNKNOWN";

      const statusName = responseData.status?.name || standardFields.status;

      const newIssueData: JiraTicketData = {
        platformRecordId: `temp-${Date.now()}`,
        jiraUrl:
          responseData.self || `https://jira.example.com/browse/${jiraKey}`,
        summary: standardFields.summary,
        description: standardFields.description,
        status: statusName || "To Do",
        priority: standardFields.priority || "Medium",
        createdAt: new Date().toISOString(),
        assignee: null,
      };

      setIsSubmitting(false);
      onOpenChange(false);
      form.reset();

      if (typeof onSuccess === "function") {
        onSuccess(newIssueData);
      }
    } catch (error) {
      console.error("Error creating JIRA issue:", error);
      setIsSubmitting(false);
    }
  };

  const renderProjectSelection = () => (
    <div className="space-y-2">
      <label htmlFor="project" className="text-sm font-medium">
        Project <span className="text-red-500">*</span>
      </label>
      {projectsLoading ? (
        <LoadingIndicator message="Loading projects..." size="small" />
      ) : projectsError ? (
        <ErrorMessage message="Failed to load JIRA projects. Please try again." />
      ) : (
        <Select value={selectedProject} onValueChange={setSelectedProject}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a project" />
          </SelectTrigger>
          <SelectContent>
            {projects.map((project) => (
              <SelectItem key={project.key} value={project.key}>
                {project.name} ({project.key})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </div>
  );

  const renderIssueTypeSelection = () => {
    if (!selectedProject) return null;

    const currentProjectIssueTypes = projectMetadata?.issueTypes || [];

    return (
      <div className="space-y-2">
        <label htmlFor="issueType" className="text-sm font-medium">
          Issue Type <span className="text-red-500">*</span>
        </label>
        {metadataLoading ? (
          <LoadingIndicator message="Loading issue types..." size="small" />
        ) : metadataError ? (
          <ErrorMessage message="Failed to load issue types" />
        ) : (
          <Select
            value={selectedIssueType}
            onValueChange={setSelectedIssueType}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select an issue type" />
            </SelectTrigger>
            <SelectContent>
              {currentProjectIssueTypes.map((issueType) => (
                <SelectItem key={issueType.id} value={issueType.id}>
                  {issueType.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    );
  };

  const renderDynamicForm = () => {
    if (!selectedIssueType || formFields.length === 0) return null;

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {formFields.map((field) => renderFormField(field, form))}

          <div className="pt-4 flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !form.formState.isValid}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create issue"
              )}
            </Button>
          </div>
        </form>
      </Form>
    );
  };

  return (
    <ModalContainer
      open={open}
      onOpenChange={onOpenChange}
      title={
        <div className="flex items-center gap-2">
          <img src="/jira.svg" alt="Jira Logo" className="h-5 w-5" />
          <span>Create Jira issue</span>
        </div>
      }
    >
      <div className="space-y-4">
        {renderProjectSelection()}
        {renderIssueTypeSelection()}
        {renderDynamicForm()}
      </div>
    </ModalContainer>
  );
};

const JiraApp = ({ ticketData }: CustomAppComponentProps) => {
  const [searchOpen, setSearchOpen] = useState(false);
  const [createOpen, setCreateOpen] = useState(false);

  const [localJiraData, setLocalJiraData] = useState<JiraTicketData[]>([]);

  // Fix null endpoint usage by computing the endpoint conditionally
  const endpoint = ticketData?.uid
    ? `/api/custom-apps/jira/issue/linked/${ticketData.uid}`
    : "";

  const {
    data: _jiraData,
    loading,
    error: _error,
    refetch,
  } = useApi<JiraApiResponse<JiraTicketData[]>>(
    endpoint,
    {},
    { enabled: !!ticketData?.uid && endpoint !== "", isNextApi: true },
  );

  const [isLinking, setIsLinking] = useState(false);
  const [isUnlinking, setIsUnlinking] = useState(false);

  // Add error message display for users
  const [_linkError, setLinkError] = useState<string | null>(null);

  // Debounce the link issue function to prevent multiple rapid calls
  const handleLinkIssue = useCallback(
    async (jiraKey: string) => {
      if (!ticketData?.uid) {
        console.error("No ticket ID available");
        setLinkError("No ticket ID available");
        return;
      }

      setIsLinking(true);
      setLinkError(null);

      try {
        const result = await linkJiraIssue(ticketData.uid, jiraKey);

        if (result.error) {
          console.error("Error linking Jira issue:", result.error);
          setLinkError(result.error);
        } else {
          // Force a refetch on success
          refetch();
        }

        await refetch();
        setSearchOpen(false);
      } catch (error) {
        // Only log errors in development
        if (process.env.NODE_ENV === "development") {
          console.debug("Error linking Jira issue:", error);
        }

        // Display error to user
        setLinkError(
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        );
      } finally {
        setIsLinking(false);
      }
    },
    [ticketData.uid, isLinking, refetch],
  );

  // Handle unlinking a Jira issue
  const handleUnlinkIssue = useCallback(
    async (jiraKey: string) => {
      if (!ticketData?.uid) {
        console.error("No ticket ID available");
        return;
      }

      setIsUnlinking(true);

      try {
        const result = await unlinkJiraIssue(ticketData.uid, jiraKey);

        if (result.error) {
          console.error("Error unlinking Jira issue:", result.error);
        } else {
          // Update local state immediately
          setLocalJiraData((prevData) => prevData.filter(issue => issue.jiraUrl.split("/").pop() !== jiraKey));
          // Force a refetch to ensure server sync
          refetch();
        }
      } catch (error) {
        // Only log errors in development
        if (process.env.NODE_ENV === "development") {
          console.debug("Error unlinking Jira issue:", error);
        }
      } finally {
        setIsUnlinking(false);
      }
    },
    [ticketData.uid, refetch],
  );

  // Debounce the link issue function using a ref to store the timeout
  const linkTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  // Debounce the unlink issue function using a ref to store the timeout
  const unlinkTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedLinkIssue = useCallback(
    (jiraKey: string) => {
      // Simple debounce implementation
      if (linkTimeoutRef.current) {
        clearTimeout(linkTimeoutRef.current);
      }

      linkTimeoutRef.current = setTimeout(() => {
        handleLinkIssue(jiraKey);
      }, 300); // 300ms debounce
    },
    [handleLinkIssue],
  );

  const debouncedUnlinkIssue = useCallback(
    (jiraKey: string) => {
      // Simple debounce implementation
      if (unlinkTimeoutRef.current) {
        clearTimeout(unlinkTimeoutRef.current);
      }

      unlinkTimeoutRef.current = setTimeout(() => {
        handleUnlinkIssue(jiraKey);
      }, 300); // 300ms debounce
    },
    [handleUnlinkIssue],
  );

  // Using optional chaining for cleaner code
  const apiJiraData = Array.isArray(_jiraData)
    ? _jiraData
    : _jiraData?.data
    ? Array.isArray(_jiraData?.data)
      ? _jiraData.data
      : [_jiraData.data]
    : [];

  useEffect(() => {
    if (apiJiraData.length > 0) {
      setLocalJiraData(apiJiraData);
    }
  }, [apiJiraData]);

  const jiraData = useMemo(() => {
    if (apiJiraData.length > 0) {
      return apiJiraData;
    }

    return localJiraData;
  }, [apiJiraData, localJiraData]);

  // Removed empty useEffect

  const handleCreateSuccess = useCallback(
    (newIssueData: JiraTicketData) => {
      setLocalJiraData((prev) => [newIssueData, ...prev]);

      refetch();
    },
    [refetch],
  );

  const hasLinkedIssues = jiraData.length > 0;

  const getTimeAgo = useCallback((date: Date) => {
    return formatDistanceToNow(date, { addSuffix: true });
  }, []);

  const formatCreatedAt = useCallback((date: Date) => {
    return format(date, "MMM dd, yyyy 'at' hh:mm a");
  }, []);

  const getStatusIcon = useCallback((status: string) => {
    // Format the status for display - convert to sentence case
    const formattedStatus = status
      .split(/[ _]/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");

    // Always display the status as text in a boxed label
    return (
      <span className="text-xs px-1.5 py-0.5 rounded-[4px] border border-border bg-background">
        {formattedStatus}
      </span>
    );
  }, []);

  const getPriorityIcon = useCallback((priority: string) => {
    // Format the priority for display - convert to sentence case
    const formattedPriority = priority
      .split(/[ _]/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");

    // Always display the priority as text in a boxed label
    return (
      <span className="text-xs px-1.5 py-0.5 rounded-[4px] border border-border bg-background">
        {formattedPriority}
      </span>
    );
  }, []);

  // Moved styled components outside of render function for better performance
  const CardWrapper = styled.div`
    display: flex;
    justify-content: start;
    flex-direction: column;
    width: 100%;
    padding: 8px;
    gap: 8px;
    border: 1px solid var(--modal-border);
    border-radius: 4px;
    position: relative;

    padding-left: 12px;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background-color: rgba(38, 132, 255, 0.6); /* Lighter JIRA blue color */
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
  `;

  // Moved styled components outside of render function for better performance
  const ConversationWrapper = styled.div`
    a {
      color: var(--color-text-info);
    }
    ol,
    ul {
      margin-left: 15px;
    }
  `;

  const renderLinkedIssues = useCallback(() => {
    if (loading) {
      return <LoadingIndicator message="Loading Jira data..." size="small" />;
    }

    if (!hasLinkedIssues) {
      return (
        <EmptyState
          onSearch={() => setSearchOpen(true)}
          onCreate={() => setCreateOpen(true)}
        />
      );
    }

    // Sort issues by createdAt date in descending order (newest first)
    const sortedIssues = [...jiraData].sort((a, b) => {
      const dateA = new Date(a.createdAt || 0).getTime();
      const dateB = new Date(b.createdAt || 0).getTime();
      return dateB - dateA; // Descending order (newest first)
    });

    return sortedIssues.map((issue) => {
      const jiraKey = issue.jiraUrl.split("/").pop() || `JIRA-${issue.jiraId}`;
      const projectKey = jiraKey.split("-")[0];

      // Handle keyboard interaction for accessibility
      const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          window.open(issue.jiraUrl, "_blank");
        }
      };

      return (
        <button
          key={issue.platformRecordId}
          className="text-left w-full hover:bg-accent cursor-pointer rounded-[4px] border-0 bg-transparent p-0"
          onClick={() => window.open(issue.jiraUrl, "_blank")}
          onKeyDown={handleKeyDown}
          aria-label={`Open JIRA issue ${jiraKey}: ${issue.summary}`}
          tabIndex={0}
        >
          <CardWrapper className="rounded-[4px]">
            <div className="flex justify-between items-center text-xs">
              <div className="flex items-start gap-2">
                <TooltipWrapper
                  tooltipContent={`Status: ${issue.status}, Priority: ${issue.priority}`}
                >
                  <div className="flex items-center gap-1">
                    {getStatusIcon(issue.status)}
                    {getPriorityIcon(issue.priority)}
                    {projectKey} - {jiraKey.split("-")[1]}
                  </div>
                </TooltipWrapper>
              </div>
              <div className="flex items-center gap-2">
                <TooltipWrapper tooltipContent="Open in Jira">
                  <a
                    href={issue.jiraUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                </TooltipWrapper>
                <TooltipWrapper tooltipContent="Unlink from ticket">
                  <button
                    type="button"
                    className="text-muted-foreground hover:text-destructive flex items-center gap-1 border border-muted-foreground/50 rounded-sm px-1.5 py-0.5 hover:border-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Extract jiraKey from URL
                      const jiraKey =
                        issue.jiraUrl.split("/").pop() ||
                        `JIRA-${issue.jiraId}`;
                      debouncedUnlinkIssue(jiraKey);
                    }}
                    disabled={isUnlinking}
                  >
                    <Unlink2 className="h-4 w-4" />{" "}
                    <span className="text-xs">Unlink</span>
                  </button>
                </TooltipWrapper>
                {/* <TooltipWrapper tooltipContent={issue.priority}>
                  {getPriorityIcon(issue.priority)}
                </TooltipWrapper> */}
              </div>
            </div>
            <ConversationWrapper className="text-sm line-clamp-2 font-medium text-[var(--color-text)] break-all">
              {issue.summary}
            </ConversationWrapper>
            <div className="flex justify-between items-center">
              {issue.assignee ? (
                <TooltipWrapper
                  tooltipContent={`Assigned to ${issue.assignee.displayName}`}
                >
                  <div className="flex items-center justify-start gap-1">
                    <Avatar
                      fallbackText={issue.assignee.displayName}
                      src={
                        issue.assignee.avatarUrls?.["32x32"] ||
                        issue.assignee.avatarUrls?.["24x24"] ||
                        ""
                      }
                      imgClassnames="h-4 w-4"
                      fallbackTextClassnames="h-4 w-4"
                    />
                    <div className="text-xs font-light text-left">
                      {issue.assignee.displayName}
                    </div>
                  </div>
                </TooltipWrapper>
              ) : (
                <TooltipWrapper tooltipContent="No assignee">
                  <div className="flex items-center justify-start gap-1 text-muted-foreground">
                    <User size={16} className="h-4 w-4" />
                    <div className="text-xs font-light text-left">
                      No assignee
                    </div>
                  </div>
                </TooltipWrapper>
              )}
              <TooltipWrapper
                tooltipContent={formatCreatedAt(new Date(issue.createdAt))}
              >
                <div className="flex text-zinc-500 dark:text-zinc-400 items-center text-[12px]">
                  <CalendarLucide size={11} className="mr-1" />
                  {getTimeAgo(new Date(issue.createdAt))}
                </div>
              </TooltipWrapper>
            </div>
          </CardWrapper>
        </button>
      );
    });
  }, [
    jiraData,
    loading,
    hasLinkedIssues,
    getStatusIcon,
    getPriorityIcon,
    getTimeAgo,
    formatCreatedAt,
  ]);

  // Fallback UI for when JIRA component isn't available
  if (!ticketData) {
    return (
      <div className="p-4 text-center border rounded-md">
        <div className="mx-auto h-8 w-8 mb-2 opacity-50 flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="lucide lucide-app-window"
          >
            <rect x="2" y="4" width="20" height="16" rx="2" />
            <path d="M10 4v4" />
            <path d="M2 8h20" />
            <path d="M6 4v4" />
          </svg>
        </div>
        <p className="text-sm text-muted-foreground">
          JIRA integration is not available
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <CreateJiraIssueModal
        open={createOpen}
        onOpenChange={setCreateOpen}
        onSuccess={handleCreateSuccess}
        ticketData={ticketData}
      />

      <JiraSearchModal
        open={searchOpen}
        onOpenChange={setSearchOpen}
        onLinkIssue={debouncedLinkIssue}
        isLinking={isLinking}
        _ticketId={ticketData?.uid || ""}
      />

      {hasLinkedIssues && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Linked issues</span>
          </div>
          <ActionButtons
            onSearch={() => setSearchOpen(true)}
            onCreate={() => setCreateOpen(true)}
          />
        </div>
      )}

      <div className="space-y-3">{renderLinkedIssues()}</div>
    </div>
  );
};

export { JiraApp };
