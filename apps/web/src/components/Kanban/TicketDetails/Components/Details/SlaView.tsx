"use client";
import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { useApi } from "@/hooks/use-api";
import { cn } from "@/lib/utils";
import { GET_SLA_MATRIC } from "@/services/kanban";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { SingleTicket } from "@/types/kanban";
import { Plus } from "lucide-react";
import * as Icon from "react-bootstrap-icons";
import { getOrgDetails } from "../../../../../utils/browserUtils";

type TProps = {
  ticketData: SingleTicket;
};

type SlaJobStatus = {
  durationToBreachWorkingMinutes: number;
  status: string;
  nextAttemptAt?: string;
  pausedAt?: string;
  scheduledAt?: string;
};

type SlaMetricsResponse = {
  first_time_response: SlaJobStatus;
  total_resolution_time: SlaJobStatus;
  next_time_response: SlaJobStatus;
  update_time: SlaJobStatus;
};
function extractTeamIdFromUrl(url) {
  try {
    const urlObject = new URL(url);
    const pathSegments = urlObject.pathname.split("/");
    return pathSegments[pathSegments.length - 1];
  } catch (error) {
    console.error("Invalid URL:", error);
    return null;
  }
}

export function getTimeDifference(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);
  const diff = Math.abs(endDate.getTime() - startDate.getTime());

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours >= 1) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

export function getFormattedTimeForSLa(timestamp: string): string {
  const targetDate = new Date(timestamp);
  const now = new Date();
  const diff = Math.abs(targetDate.getTime() - now.getTime());

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 1) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

export const getIconObject = (type: string, size = 16) => {
  switch (type) {
    case "first_time_response":
      return {
        overdue: <Icon.PersonX size={size} />,
        achieved: <Icon.PersonCheck size={size} />,
        due: <Icon.PersonExclamation size={size} />,
        delayed: <Icon.PersonDash size={size} />,
      };
    case "total_resolution_time":
      return {
        overdue: <Icon.CalendarX size={size} />,
        achieved: <Icon.CalendarCheck size={size} />,
        due: <Icon.Calendar size={size} />,
        delayed: <Icon.CalendarMinus size={size} />,
      };
    case "next_time_response":
      return {
        overdue: <Icon.SendX size={size} />,
        achieved: <Icon.SendCheck size={size} />,
        due: <Icon.SendExclamation size={size} />,
        delayed: <Icon.SendDash size={size} />,
      };
    case "update_time":
      return {
        overdue: <Icon.WindowX size={size} />,
        achieved: <Icon.Window size={size} />,
        due: <Icon.Window size={size} />,
        delayed: <Icon.WindowDash size={size} />,
      };
    default:
      return null;
  }
};

const getTimeAndStatus = (sla, type) => {
  if (!sla) return null;
  const getIconObj = getIconObject(type);
  switch (sla.status) {
    case "breached":
      if (sla.achievedAt) {
        return {
          time: sla.achievedAt,
          status: sla.status,
          className: "text-muted-foreground",
          icon: getIconObj.delayed,
          text: `Delayed by ${getTimeDifference(
            sla.breachedAt,
            sla.achievedAt,
          )}`,
        };
      }
      return {
        time: sla.breachedAt,
        icon: getIconObj.overdue,
        status: sla.status,
        className: "text-[var(--color-text-error)]",
        text: `Overdue by ${getTimeDifference(
          String(new Date()),
          sla.breachedAt,
        )}`,
      };
    case "achieved":
      return {
        time: sla.durationToBreachWorkingMinutes,
        status: sla.status,
        icon: getIconObj.achieved,
        className: "text-[var(--color-text-success)]",
        text: `Achieved in ${getTimeDifference(sla.achievedAt, sla.createdAt)}`,
      };
    case "paused":
      return {
        time: sla.pausedAt,
        status: sla.status,
        icon: <Icon.PauseCircle size={16} />,
        className: "text-[var(--color-text-success)]",
        text: `Paused for ${getFormattedTimeForSLa(sla.pausedAt)}`,
      };
    case "running":
    case "scheduled":
      return {
        time: sla.nextAttemptAt,
        status: sla.status,
        className: "text-[var(--color-icon-warning)]",
        icon: getIconObj.due,
        text:
          "Due in " + getTimeDifference(String(new Date()), sla.nextAttemptAt),
      };
    case "not_scheduled":
    case "cancelled":
      return {
        time: "-",
        status: sla.status,
        icon: <Icon.SlashCircle size={16} />,
        text: "-",
        className: "text-muted-foreground",
      };
    default:
      return {
        time: "-",
        status: "-",
      };
  }
};

const SlaView = ({ ticketData }: TProps) => {
  // const currentOrgId = useGlobalConfigPersistStore(
  //   (state) => state.currentOrgId,
  // );
  const { orgId: currentOrgId } = getOrgDetails();
  const orgs = useGlobalConfigPersistStore((state) => state.orgs);

  const orgUid = orgs.find((org) => org.id === currentOrgId)?.orgId;
  const isInbox = window?.location?.pathname?.includes("inbox");
  const teamId = isInbox
    ? ticketData.team.uid
    : extractTeamIdFromUrl(window.location.href);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { data } = useApi<SlaMetricsResponse>(
    `${GET_SLA_MATRIC}?entityId=${ticketData.uid}&entityType=ticket&organizationId=${orgUid}&teamId=${teamId}`,
    {},
    { enabled: true, isNextApi: false, urlOption: "sla", method: "GET" },
  );

  const firstResponse = data?.first_time_response;
  const totalResolutionTime = data?.total_resolution_time;
  const nextResponse = data?.next_time_response;
  const ticketUpdates = data?.update_time;

  const firstResponseConfig = getTimeAndStatus(
    firstResponse,
    "first_time_response",
  );
  const totalResolutionTimeConfig = getTimeAndStatus(
    totalResolutionTime,
    "total_resolution_time",
  );
  const nextResponseConfig = getTimeAndStatus(
    nextResponse,
    "next_time_response",
  );
  const ticketUpdatesConfig = getTimeAndStatus(ticketUpdates, "update_time");

  return (
    <>
      <Accordion
        type="single"
        collapsible
        className="w-full"
        defaultValue="sla"
      >
        <AccordionItem value="sla" className="border-none">
          <AccordionTrigger className="hover:no-underline" iconLeft>
            <span className="text-sm font-medium">SLAs</span>
          </AccordionTrigger>
          <AccordionContent className="pt-4 px-0">
            {!firstResponse?.status &&
            !totalResolutionTime?.status &&
            !nextResponse?.status &&
            !ticketUpdates?.status ? (
              <div className="flex flex-col items-center justify-center w-full py-4 text-center">
                <div className="bg-muted rounded-md p-3 mb-2">
                  <Icon.Stopwatch className="h-5 w-5 text-muted-foreground" />
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  No SLA policies have been set up yet.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => {
                    // Navigate to SLA settings page
                    window.open(`/dashboard/${teamId}/settings/sla`, "_blank");
                  }}
                >
                  <Plus className="h-4 w-4" />
                  <span>Add SLA policy</span>
                </Button>
              </div>
            ) : (
              <div className="flex flex-col gap-4">
                {/* First Response SLA */}
                {firstResponse?.status && (
                  <div
                    className={cn(
                      "flex items-center text-sm text-muted-foreground",
                    )}
                    style={{ gap: "120px" }}
                  >
                    <span className="w-24 whitespace-nowrap">
                      First response SLA
                    </span>
                    <div
                      className={cn(
                        "flex items-center gap-2",
                        firstResponseConfig.className,
                      )}
                    >
                      <span>{firstResponseConfig.icon}</span>
                      <span>{firstResponseConfig.text}</span>
                    </div>
                  </div>
                )}

                {/* Next Response SLA */}
                {nextResponse?.status && (
                  <div
                    className="flex items-center text-sm text-muted-foreground"
                    style={{ gap: "120px" }}
                  >
                    <span className="w-24 whitespace-nowrap">
                      Next response SLA
                    </span>
                    <div
                      className={cn(
                        "flex items-center gap-2",
                        nextResponseConfig.className,
                      )}
                    >
                      <span>{nextResponseConfig.icon}</span>
                      <span>{nextResponseConfig.text}</span>
                    </div>
                  </div>
                )}

                {/* Ticket Updates */}
                {ticketUpdates?.status && (
                  <div
                    className="flex items-center text-sm text-muted-foreground"
                    style={{ gap: "120px" }}
                  >
                    <span className="w-24 whitespace-nowrap">
                      Ticket updates
                    </span>
                    <div
                      className={cn(
                        "flex items-center gap-2",
                        ticketUpdatesConfig.className,
                      )}
                    >
                      <span>{ticketUpdatesConfig.icon}</span>
                      <span>{ticketUpdatesConfig.text}</span>
                    </div>
                  </div>
                )}

                {/* Resolution SLA */}
                {totalResolutionTime?.status && (
                  <div
                    className={cn(
                      "flex items-center text-sm text-muted-foreground",
                    )}
                    style={{ gap: "120px" }}
                  >
                    <span className="w-24 whitespace-nowrap">
                      Resolution SLA
                    </span>
                    <div
                      className={cn(
                        "flex items-center gap-2",
                        totalResolutionTimeConfig.className,
                      )}
                    >
                      <span>{totalResolutionTimeConfig.icon}</span>
                      <span>{totalResolutionTimeConfig.text}</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </>
  );
};

export default SlaView;
