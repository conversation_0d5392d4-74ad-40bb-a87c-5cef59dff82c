import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useInternalUsersStore } from "@/store/internal-users-store";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { isEmpty } from "@/utils/kanban";
import { debounce } from "lodash";
import { Loader2 } from "lucide-react";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import { ThreadType } from "../../../../../../constants/thread";
import { useAppsSourcesStore } from "../../../../../../store/apps-sources-store";
import { useGlobalConfigPersistStore } from "../../../../../../store/globalConfigPersistStore";
import { useTicketDrawerStore } from "../../../../../../store/ticketDrawerStore";
import type { ThenaTicket } from "../../../../../../types/kanban";
import { fetchInternalUsers } from "../../../../../../utils/internal-user";
import { getSourceIcon } from "../../TicketDetailsLeftside";
import "../style.css";

// Get only the first letter of the name
const getFirstLetter = (name = ""): string => {
  return name.charAt(0).toUpperCase();
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const MentionList = forwardRef((props: any, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const teamMembers = useTicketMetaStore((state) => state.teamMembers);
  // Use the store directly instead of local state to avoid synchronization issues
  const internalUsers = useInternalUsersStore((state) => state.internalUsers);
  const [customerContacts, setCustomerContacts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  // const [fetchAttempts, setFetchAttempts] = useState(0);
  const ticketId = useTicketDrawerStore((state) => state.ticketId);
  const sources = useAppsSourcesStore((state) => state.sources);
  // Use the thread type from props or default to PUBLIC
  const threadType = props.threadType || ThreadType.PUBLIC;

  const tickets = useTicketMetaStore((state) => state.tickets);
  const { currentOrgId, orgs } = useGlobalConfigPersistStore((state) => state);
  const ticket: ThenaTicket = tickets.find((ticket) => ticket.uid === ticketId);

  const accountId = ticket?.account?.id || 0;

  const handleFetchInternalUsers = useCallback(async () => {
    setIsLoading(true);

    try {
      const fetchedUsers = await fetchInternalUsers(
        sources,
        orgs.find((org) => org.id === currentOrgId)?.orgId,
      );

      useInternalUsersStore.getState().setInternalUsers(fetchedUsers);
    } catch (error) {
      console.error("Error fetching internal users:", error);
    } finally {
      setIsLoading(false);
    }
  }, [sources, currentOrgId]);

  const fetchCustomerContacts = useCallback(
    debounce(async (query: string) => {
      if (!query || query.trim() === "" || threadType !== ThreadType.PUBLIC) {
        setCustomerContacts([]);
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch(
          `/api/customer-contacts/search?account_id=${accountId}&search=${encodeURIComponent(
            query,
          )}`,
        );
        const result = await response.json();

        if (result.data) {
          const formattedContacts = result.data.map((contact) => ({
            label: `${contact.first_name} ${contact.last_name}`.trim(),
            id: contact.uid,
            email: contact.email,
            avatar_url: contact.avatar_url,
            isCustomer: true,
          }));
          setCustomerContacts(formattedContacts);
        }
      } catch (error) {
        console.error("Error fetching customer contacts:", error);
        setCustomerContacts([]);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    [accountId, threadType],
  );

  useEffect(() => {
    fetchCustomerContacts(props.query);
  }, [props.query, fetchCustomerContacts]);

  useEffect(() => {
    if (internalUsers.length === 0) {
      handleFetchInternalUsers();
    }
  }, [internalUsers]);

  const filteredTeamMembers = teamMembers
    .filter(
      (user) => user?.name?.toLowerCase()?.includes(props.query.toLowerCase()),
    )
    .map((user) => ({
      ...user,
      label: user.name,
      isCustomer: false,
      source: "Member",
    }));

  const filteredInternalUsers = internalUsers
    .filter(
      (user) => user?.name?.toLowerCase()?.includes(props.query.toLowerCase()),
    )
    .map((user) => ({
      ...user,
      id: user.uid,
      label: user.name,
      isCustomer: false,
    }));

  const deduplicateByEmail = (users) => {
    const seen = new Map();
    return users.filter((user) => {
      if (!user.email || seen.has(user.email)) {
        return false;
      }
      seen.set(user.email, true);
      return true;
    });
  };

  // Business logic: Only show customer contacts in public threads
  // For internal threads, we only want to show team members and internal users to prevent
  // accidentally mentioning customers in internal communications, and also remove duplicates
  const filteredUsers =
    threadType === ThreadType.PUBLIC
      ? deduplicateByEmail([
          ...filteredTeamMembers,
          ...filteredInternalUsers,
          ...customerContacts,
        ])
      : deduplicateByEmail([...filteredTeamMembers, ...filteredInternalUsers]);

  const selectItem = (index) => {
    const item = filteredUsers[index];
    if (!isEmpty(item)) {
      // Validate the item to ensure it has valid id and email (not null or undefined)
      if (
        !item.id ||
        item.id === "null" ||
        !item.email ||
        item.email === "null"
      ) {
        console.error("Invalid mention data:", item);
        return; // Skip invalid mentions
      }

      props.command({
        label: item.label || "Unknown User", // Provide fallback for label
        id: item.id,
        email: item.email,
      });
    }
  };

  const upHandler = () => {
    setSelectedIndex(
      (selectedIndex + filteredUsers.length - 1) % filteredUsers.length,
    );
  };

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % filteredUsers.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useEffect(() => setSelectedIndex(0), [props.items]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      // Let Command+Enter pass through to the parent handler
      if ((event.metaKey || event.ctrlKey) && event.key === "Enter") {
        return false;
      }

      if (event.key === "ArrowUp") {
        upHandler();
        return true;
      }

      if (event.key === "ArrowDown") {
        downHandler();
        return true;
      }

      if (event.key === "Enter") {
        enterHandler();
        return true;
      }

      return false;
    },
  }));

  return (
    <div className="items">
      {filteredUsers.length ? (
        filteredUsers.map((item, index) => {
          return (
            <div
              key={index}
              onClick={() => selectItem(index)}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  selectItem(index);
                  e.preventDefault();
                }
              }}
              tabIndex={0}
              role="option"
              aria-selected={index === selectedIndex}
              className={`item ${index === selectedIndex ? "is-selected" : ""}`}
            >
              <Avatar className="w-5 h-5">
                {item.avatar_url && (
                  <AvatarImage src={item.avatar_url} alt={item.label} />
                )}
                <AvatarFallback>{getFirstLetter(item.label)}</AvatarFallback>
              </Avatar>
              <button key={index} type="button">
                {item.label.charAt(0).toUpperCase() + item.label.slice(1)}
              </button>
              {item.isCustomer && (
                <span className="text-[0.65rem] px-1 py-0.5 rounded-sm  bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 ml-1">
                  Customer contact
                </span>
              )}
              {!item.isCustomer && item.source && (
                <span className="flex gap-1 items-center text-[0.65rem] px-1 py-0.5 rounded-sm bg-muted text-muted-foreground ml-1">
                  {item.source == "Slack member" && getSourceIcon("slack", 12)}
                  {item.source}
                </span>
              )}
            </div>
          );
        })
      ) : isLoading ? (
        <div className="item flex justify-center py-2">
          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Loading...</span>
        </div>
      ) : (
        <div className="item">No result</div>
      )}
    </div>
  );
});

MentionList.displayName = "MentionList";
