import CustomBubbleMenu from "@/components/tiptap/extensions/CustomBubbleMenu";
import LinkMenu from "@/components/tiptap/extensions/link-menu";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useCurrentEditor } from "@tiptap/react";
import { Loader2, Paperclip, Send, X } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { useHotkeys } from "react-hotkeys-hook";

const BottomMenuBar = ({
  onSend,
  openDropzone,
  getRootProps,
  isLoading,
  isFileUploadEnabled,
  isSideActionBarVisible,
  setEditOpen,
  menuContainerRef,
}) => {
  const { editor } = useCurrentEditor();

  useHotkeys("mod+enter", () => {
    if (onSend) {
      onSend(editor);
    }
  })

  const ticketId = useSearchParams().get("ticketId");

  useEffect(() => {
    if (ticketId && !setEditOpen) {
      editor.commands.setContent(``);
    }
  }, [ticketId]);

  const contentLength = editor.getHTML().length;

  return (
    <div
      onClick={() => editor.commands.focus()}
      className={cn(
        "px-2 pb-2 flex items-center justify-end gap-2",
        isSideActionBarVisible && "absolute right-1 bottom-0.5 ",
      )}
    >
      <div {...getRootProps?.()} className="dropzone flex items-center gap-2">
        {isLoading && (
          <div className="flex items-center gap-2 animate-pulse text-sm">
            <Loader2 size={14} className="animate-spin" />
            Uploading file...
          </div>
        )}
        {isFileUploadEnabled && (
          <Button
            variant="ghost"
            size="icon"
            className="w-8 h-8"
            onClick={() => {
              openDropzone?.();
            }}
          >
            <Paperclip size={16} />
          </Button>
        )}
      </div>
      <CustomBubbleMenu editor={editor} enableAI isNotFromSlack />
      <LinkMenu editor={editor} appendTo={menuContainerRef} />
      <div className="flex items-center gap-2">
        {setEditOpen && (
          <Button
            onClick={() => {
              setEditOpen({ id: null, state: false });
            }}
            // variant="secondary"
            className="h-8 w-8 border-none "
          >
            <X size={24} />
          </Button>
        )}

        <Button
          onClick={() => {
            onSend(editor);
          }}
          disabled={isLoading}
          variant="outline"
          className={cn(
            "h-8 w-8 border-none bg-secondary",
            contentLength > 7 && " shadow",
          )}
        >
          <Send
            size={24}
            className={cn(
              "text-muted-text rotate-[45deg] mr-1",
              contentLength > 7 && "text-color-text",
            )}
          />
        </Button>
      </div>
    </div>
  );
};

export default BottomMenuBar;
