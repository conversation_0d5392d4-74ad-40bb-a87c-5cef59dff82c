import CustomBubbleMenu from "@/components/tiptap/extensions/CustomBubbleMenu";
import LinkMenu from "@/components/tiptap/extensions/link-menu";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useCurrentEditor } from "@tiptap/react";
import { Loader2, Paperclip, Send, X } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect } from "react";
import { toast } from "sonner";

interface LocalFile {
  id: string;
  type: string;
  url: string;
}

const BottomMenuBar = ({
  onSend,
  openDropzone,
  getRootProps,
  isLoading,
  isFileUploadEnabled,
  isSideActionBarVisible,
  setEditOpen,
  menuContainerRef,
  localFiles = [] as LocalFile[],
}) => {
  const { editor } = useCurrentEditor();

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!editor?.isFocused || isLoading) return;
    if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      // if (!editor.getText().trim()) return;

      // Check if there's any content to send
      const hasTextContent = editor.getText().trim().length > 0;
      const hasMediaContent = editor.getHTML().includes('<img') || editor.getHTML().includes('<video');
      const hasLocalFiles = localFiles.length > 0;

      if (!hasTextContent && !hasMediaContent && !hasLocalFiles) {
        toast.error("Reply cannot be empty");
        return;
      };


      onSend(editor);
    }
  }, [editor, onSend]);

  useEffect(() => {
    if (editor && onSend) {
      editor.view.dom.addEventListener('keydown', handleKeyDown, true);
      return () => {
        editor.view.dom.removeEventListener('keydown', handleKeyDown, true);
      };
    }
  }, [editor, onSend, handleKeyDown]);

  const ticketId = useSearchParams().get("ticketId");

  useEffect(() => {
    if (ticketId && !setEditOpen) {
      editor.commands.setContent(``);
    }
  }, [ticketId]);

  const contentLength = editor.getHTML().length;

  return (
    <div
      onClick={() => editor.commands.focus()}
      className={cn(
        "flex items-center justify-end gap-2 p-1",
        isSideActionBarVisible && "absolute right-1 bottom-0.5 ",
      )}
    >
      <div {...getRootProps?.()} className="dropzone flex items-center gap-2">
        {isLoading && (
          <div className="flex items-center gap-2 animate-pulse text-sm">
            <Loader2 size={14} className="animate-spin" />
            Uploading file...
          </div>
        )}
        {isFileUploadEnabled && (
          <Button
            variant="ghost"
            size="icon"
            disabled={isLoading}
            className="w-8 h-8"
            onClick={() => {
              openDropzone?.();
            }}
          >
            <Paperclip size={16} />
          </Button>
        )}
      </div>
      <CustomBubbleMenu editor={editor} enableAI isNotFromSlack />
      <LinkMenu editor={editor} appendTo={menuContainerRef} />
      <div className="flex items-center gap-2">
        {setEditOpen && (
          <Button
            onClick={() => {
              setEditOpen({ id: null, state: false });
            }}
            // variant="secondary"
            className="h-8 w-8 border-none "
          >
            <X size={24} />
          </Button>
        )}

        <Button
          onClick={() => {
            onSend(editor);
          }}
          disabled={isLoading}
          variant="outline"
          className={cn(
            "h-8 w-8 border-none bg-secondary",
            contentLength > 7 && " shadow",
          )}
        >
          <Send
            size={24}
            className={cn(
              "text-muted-text rotate-[45deg] mr-1",
              contentLength > 7 && "text-color-text",
            )}
          />
        </Button>
      </div>
    </div>
  );
};

export default BottomMenuBar;
