"use client";

import { uploadFile } from "@/app/actions/uploadFile";
import { useAiToken } from "@/components/tiptap/editor/useAiToken";
import { CommandEnterExtension } from "@/components/tiptap/extensions/CommandEnter";
import { EmbedVideoExtension } from "@/components/tiptap/extensions/EmbedVideo/EmbedVideoExtension";
import { KanbanSlashCommand } from "@/components/tiptap/extensions/SlashCommand/KanbanSlashCommand";
import { TableCell } from "@/components/tiptap/extensions/TableExtension/Cell";
import { TIPTAP_AI_APP_ID } from "@/config/constant";
import { cn } from "@/lib/utils";
import Ai from "@tiptap-pro/extension-ai";
import FileHandler from "@tiptap-pro/extension-file-handler";
import type { Editor } from "@tiptap/core";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import Table from "@tiptap/extension-table";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import TextAlign from "@tiptap/extension-text-align";
import { EditorProvider } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import styled from "styled-components";
import AttachmentPreview from "../AttachmentPreview";
import BottomMenuBar from "./BottomMenuBar";
import { MentionWithEmail } from "./Mention/MentionWithEmail";
import mentionSuggestion from "./Mention/mentionSuggestion";
import { EmojiSuggestion } from "@/components/tiptap/extensions/Emojis";

import { ThreadType } from "../../../../../constants/thread";
import { getOrgDetails } from "../../../../../utils/browserUtils";
import "./styles.css";

interface WrapperProps {
  isFocused: boolean;
}

const Wrapper = styled.div<WrapperProps>`
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 4px;
  border: 1px solid var(--modal-border);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
  box-shadow: ${({ isFocused }) =>
    isFocused
      ? "4px 4px 6px -4px rgba(16, 24, 40, 0.10), -4px 4px 15px -6px rgba(0, 0, 0, 0.10)"
      : "none"};
  /* background: ${({ isFocused }) => (isFocused ? "blue" : "red")}; */

  a {
    color: var(--url-color);
  }

  .tiptap,
  .ProseMirror {
    min-height: 30px;
    max-height: auto;
    width: 100%;
    padding-top: 4px;
  }

  blockquote {
    color: var(--color-text);
    border-left: 4px solid var(--slack-blockquote-left-border);
    padding-left: 12px;
    margin: 8px 0;
    background-color: var(--slack-code-block-bg);
    border-radius: 4px;
  }

  blockquote p {
    color: var(--color-text);
    padding: 8px 0;
  }

  .drag-handle {
    align-items: center;
    cursor: grab;
    display: flex;
    height: 1.5rem;
    justify-content: center;
    width: 1.5rem;
  }

  h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
    font-weight: 500;
  }

  h3 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
    font-weight: 500;
  }

  p {
    font-size: 14px; /* 14px */
    line-height: 1.5rem; /* 24px */
  }

  p.is-empty::before {
    color: var(--color-text-muted);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  code {
    font-size: 0.875rem; /* 14px */
    font-family: monospace;
  }

  ul,
  ol {
    padding: 0 1rem;
    margin: 0 1rem 0 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  hr[contenteditable="false"] {
    margin-bottom: 0;
    border-top: 1px solid var(--color-border);
  }

  .tiptap-thread,
  .tiptap-thread * {
    transition: all 0.2s cubic-bezier(0.65, 0.05, 0.36, 1);
  }

  .tiptap-thread--block {
    &:first-child {
      margin-top: 0;
    }

    &:not(.tiptap-thread--resolved) {
      > :not(p, h1, h2, h3, h4, h5, h6, ul, li) {
        background-color: var(--color-warning-50);

        &:hover,
        &.tiptap-thread--selected,
        &.tiptap-thread--hovered {
          background-color: var(--color-warning);
        }
      }

      :is(p, h1, h2, h3, h4, h5, h6, ul, li) {
        &:not(:has(.tiptap-thread--inline)) {
          background-color: var(--color-warning-25);
          border-bottom: 2px dashed var(--color-warning);

          &:hover {
            background-color: var(--color-warning-50);
          }
        }
      }

      &.tiptap-thread--selected,
      &.tiptap-thread--hovered {
        :is(p, h1, h2, h3, h4, h5, h6, ul, li) {
          &:not(:has(.tiptap-thread--inline)) {
            background-color: var(--color-warning-50);
          }
        }
      }
    }
  }

  .tiptap-thread--inline {
    &:not(.tiptap-thread--resolved) {
      border-bottom: 2px dashed var(--color-warning);

      &:hover,
      &.tiptap-thread--selected,
      &.tiptap-thread--hovered {
        background-color: var(--color-warning-50);
      }
    }
  }

  .is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: var(--color-text-muted);
    pointer-events: none;
    height: 0;
  }
`;

const getExtensions = (
  onSend,
  aiToken,
  setIsLoading,
  setLocalFiles,
  orgUid,
  threadType = ThreadType.PUBLIC,
  editorRef,
) => {
  return [
    //   Color.configure({ types: [TextStyle.name, ListItem.name] }),
    StarterKit,
    EmojiSuggestion,
    // EmojiSuggestion.configure({
    //   suggestion: {
    //     char: '::',  // Use :: instead of :
    //   }
    // }),
    TextAlign.configure({ types: ["heading", "paragraph", "callout"] }),
    Placeholder.configure({
      emptyNodeClass: "is-editor-empty",
      placeholder({ node }) {
        if (node.type.name === "paragraph") {
          return "Leave a comment.";
        }
        return "";
      },
    }),
    ,
    Image,
    Link.configure({
      openOnClick: false,
      HTMLAttributes: {
        target: "_blank",
        rel: "noopener noreferrer nofollow",
        class: "text-blue-300 hover:underline",
      },
      validate: (href) => {
        return href.trim().length > 0;
      },
    }).extend({
      inclusive: false,
    }),
    MentionWithEmail.configure({
      HTMLAttributes: {
        class: "mention",
      },
      renderHTML({ options: _opts, node }) {
        const id = node.attrs.id;
        const email = node.attrs.email;
        const label = node.attrs.label;

        if (!id || id === "null" || !email || email === "null") {
          console.warn("Skipping invalid mention with missing/null data", {
            id,
            email,
            label,
          });
          return ["span", {}, "@" + (label || "Unknown")];
        }

        return [
          "span",
          {
            class: "mention",
            "data-id": id,
            "data-email": email,
            "data-label": label || id,
            "data-type": "mention",
          },
          `@${label || id}`,
        ];
      },
      renderText({ options, node }) {
        const id = node.attrs.id;
        const email = node.attrs.email;

        if (!id || id === "null" || !email || email === "null") {
          return `@${node.attrs.label || "Unknown"}`;
        }

        return `<${options.suggestion.char}${id}|${email}>`;
      },
      suggestion: {
        ...mentionSuggestion,
        render: () => {
          const currentThreadType = threadType;

          const originalRender = mentionSuggestion.render();
          return {
            ...originalRender,
            onStart: (props) => {
              const newProps = { ...props, threadType: currentThreadType };
              return originalRender.onStart(newProps);
            },
            onUpdate: (props) => {
              const newProps = { ...props, threadType: currentThreadType };
              return originalRender.onUpdate(newProps);
            },
          };
        },
      },
    }),

    Ai.configure({
      appId: TIPTAP_AI_APP_ID,
      token: aiToken,
      autocompletion: true,
      onError: (error, context) => {
        console.log({ error, context });
        toast.error(error.message ?? "Failed to generate content");
      },
    }),

    FileHandler.configure({
      allowedMimeTypes: ["image/png", "image/jpeg", "image/gif", "image/webp"],

      onDrop: (_, files) => {
        files.forEach(async (file) => {
          if (!file) {
            return false;
          }
          setIsLoading(true);
          try {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("upload_preset", "platform");

            const uploadData = await uploadFile(formData, orgUid);

            if (uploadData?.urls?.publicUrl) {
              setLocalFiles((prev) => [
                ...prev,
                {
                  url: uploadData.urls.publicUrl,
                  type: file.type,
                  id: uploadData.data.uid,
                },
              ]);
            }
          } catch (error) {
            console.error("Upload failed:", error);
          } finally {
            setIsLoading(false);
            if (editorRef.current && editorRef.current.commands.blur) {
              editorRef.current.commands.blur();
            }
          }
        });
      },

      onPaste: (_, files, htmlContent) => {
        files.forEach(async (file) => {
          if (htmlContent) {
            return false;
          }
          if (!file) {
            return false;
          }
          setIsLoading(true);
          try {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("upload_preset", "platform");

            const uploadData = await uploadFile(formData, orgUid);

            if (uploadData?.urls?.publicUrl) {
              setLocalFiles((prev) => [
                ...prev,
                {
                  url: uploadData.urls.publicUrl,
                  type: file.type,
                  id: uploadData.data.uid,
                },
              ]);
            }
          } catch (error) {
            console.error("Upload failed:", error);
          } finally {
            setIsLoading(false);
          }
        });
      },
    }),

    // Emoji.configure({
    //   emojis,
    //   enableEmoticons: false,
    //   suggestion,
    // }),
    KanbanSlashCommand(),
    Table,
    TableHeader,
    TableCell,
    TableRow,
    EmbedVideoExtension,
    // Always include CommandEnterExtension to ensure Command+Enter works in all contexts
    CommandEnterExtension.configure({
      onCommandEnter(editor) {
        onSend(editor);
      },
    }),
  ].filter(Boolean);
};

const validateFileType = (file) => {
  const fileType = file.type.split("/")[0];
  const isExecutable = [
    "application/x-msdownload",
    "application/x-dosexec",
  ].includes(fileType);
  return !isExecutable;
};

const Tiptap = ({
  content,
  setContent,
  isFileUploadEnabled = true,
  localFiles = [],
  setLocalFiles = (prev) => prev,
  onSend = (editor) => editor,
  className = "",
  externalBottomMenuBar = null,
  editorClassName = "",
  isSideActionBarVisible = false,
  // aiToken = null,
  setEditOpen = null,
  threadType = ThreadType.PUBLIC,
}) => {
  const [files, setFiles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [tiptapIsFocused, setTiptapIsFocused] = useState(false);
  const menuContainerRef = useRef<HTMLDivElement>(null);
  // const editorRef = useRef<any>(null);
  const editorRef = useRef<Editor | null>(null);

  // const localFilesRef = useRef(localFiles);

  // useEffect(() => {
  //   localFilesRef.current = localFiles;
  // }, [localFiles]);
  const { orgUid } = getOrgDetails();
  const aiToken = useAiToken();

  const handleChange = (editor) => {
    setContent(editor.getHTML());
  };

  const onDrop = async (acceptedFiles: File[]) => {
    setIsLoading(true);
    const validFiles = acceptedFiles.filter((file) => {
      const isFileTypeValid = validateFileType(file);
      const isSizeValid = file.size <= 52428800; // 50 MB
      return isFileTypeValid && isSizeValid;
    });
    if (validFiles.length !== acceptedFiles.length) {
      const invalidFiles = acceptedFiles.filter((file) => file.size > 52428800);
      invalidFiles.forEach((file) => {
        toast.error(`File "${file.name}" exceeds the maximum allowed size.`);
      });
      setIsLoading(false);
    } else {
      setFiles([...files, ...validFiles]);
      try {
        const formData = new FormData();
        validFiles.forEach((file) => {
          formData.append("files", file);
        });
        const data = await uploadFile(formData, orgUid);

        const urls = [
          {
            url: data.urls?.publicUrl,
            type: data.data?.contentType,
            id: data?.data?.uid,
          },
        ];
        setLocalFiles([...localFiles, ...urls]);

        // if (onFileAdd) {
        //   onFileAdd(urls);
        // }
      } catch (error) {
        console.error("File upload failed:", error);
        toast.error("Failed to upload file. Please try again.");
      } finally {
        setIsLoading(false);
        // editorRef.current?.commands.blur();
        if (editorRef.current && editorRef.current.commands.blur) {
          console.log("editorRef", editorRef.current);
          editorRef.current.commands.blur();
        }
      }
    }
  };

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop,
    noClick: true,
    noKeyboard: true,
  });

  const slotAfter = () => {
    if (externalBottomMenuBar) {
      return externalBottomMenuBar;
    }
    return (
      <BottomMenuBar
        openDropzone={open}
        getRootProps={getRootProps}
        onSend={(editor) => onSend(editor)}
        isLoading={isLoading}
        isFileUploadEnabled={isFileUploadEnabled}
        isSideActionBarVisible={isSideActionBarVisible}
        setEditOpen={setEditOpen}
        menuContainerRef={menuContainerRef}
      />
    );
  };

  return (
    <div ref={menuContainerRef} className="flex flex-col gap-2">
      <Wrapper
        isFocused={tiptapIsFocused}
        className={cn("border rounded-lg min-h-28", className)}
        key={aiToken}
      >
        <input {...getInputProps()} />
        <EditorProvider
          slotBefore={null}
          slotAfter={slotAfter()}
          onCreate={({ editor }) => {
            editorRef.current = editor;
          }}
          extensions={getExtensions(
            onSend,
            aiToken,
            setIsLoading,
            setLocalFiles,
            orgUid,
            threadType,
            editorRef,
          )}
          content={content}
          onUpdate={({ editor }) => handleChange(editor)}
          editorContainerProps={{
            className: cn(
              "min-h-[50px] max-h-auto w-[90%] overflow-auto *:outline-none p-2 pl-4",
              "[&_h2]:text-2xl [&_h3]:text-xl",
              editorClassName,
            ),
          }}
          onFocus={() => {
            setTiptapIsFocused(true);
          }}
          onBlur={() => {
            setTiptapIsFocused(false);
          }}
        >
          {null}
        </EditorProvider>
      </Wrapper>

      {/* Display pending uploads outside the conversation border */}
      {localFiles?.length > 0 && (
        <div className="mt-2 pl-2">
          <div className="text-xs text-muted-foreground mb-1">
            Pending uploads:
          </div>
          <div className="max-w-[400px]">
            <AttachmentPreview
              showRemoveButton={true}
              urls={localFiles}
              onRemove={(index) => {
                const newFiles = [...localFiles];
                newFiles.splice(index, 1);
                setLocalFiles(newFiles);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Tiptap;
