import AccountSelector from "@/components/common/AccountSelector";
import Avatar from "@/components/common/Avatar";
import { HeroIcon } from "@/components/hero-icon";
import IconButton from "@/components/icon-button";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useApi } from "@/hooks/use-api";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { useKanbanStore } from "@/store/kanbanStore";
import { useTicketDetailsDisplayOptions } from "@/store/ticket-details-display-options";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { InstalledAppsResponseDto } from "@/types/app-studio";
import {
  Building2,
  CheckIcon,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  SlidersHorizontal,
  Users,
  X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { toast } from "sonner";
import { cn } from "../../../../lib/utils";
import { useKanbanDisplayOptions } from "../../../../stores/use-kanban-display-options";
import { SingleTicket } from "../../../../types/kanban";
import { FieldsList } from "./FieldsList/FieldsList";
import MoreOptions from "./Header/MoreOptions";

type Tprops = {
  ticketData: SingleTicket;
  closeDrawer: (router: ReturnType<typeof useRouter>) => void;
};

const TicketDetailsHeader = ({ closeDrawer, ticketData }: Tprops) => {
  const { data: installedAppsResponse } = useApi<InstalledAppsResponseDto>(
    "/api/app-studio/installed-apps",
    {},
    { enabled: true, isNextApi: true },
  );

  const { fields, setFields } = useTicketDetailsDisplayOptions();

  useEffect(() => {
    const installedApps = installedAppsResponse?.apps || [];
    if (installedApps.length > 0) {
      // Check if app fields already exist
      const existingAppIds = fields
        .filter((field) => field.id.startsWith("app-"))
        .map((field) => field.id.substring(4)); // Extract appId from 'app-{appId}'

      // Find apps that aren't already in fields
      const newApps = installedApps.filter(
        (app) => !existingAppIds.includes(app.appId),
      );

      if (newApps.length > 0) {
        // Find the max order to append new fields
        const maxOrder =
          fields.length > 0
            ? Math.max(...fields.map((field) => field.order))
            : -1;

        // Create field types for new apps
        const newAppFields = newApps.map((app, index) => ({
          id: `app-${app.appId}`,
          label: app.name || "Unknown App",
          type: "custom" as const,
          visible: true,
          order: maxOrder + 1 + index,
          subFields: [],
          appId: app.appId,
        }));

        // Add new app fields to existing fields
        setFields([...fields, ...newAppFields]);
      }
    }
  }, [installedAppsResponse, fields, setFields]);
  const router = useRouter();
  const teams = useTicketMetaStore((state) => state.teams);
  const { statusOrderMap } = useKanbanStore();

  const team = teams.find((team) => team.uid === ticketData.team.uid);

  const ticketsInColumn = statusOrderMap[ticketData.status.uid] || [];

  const isArchivedView = useKanbanDisplayOptions(
    (state) => state.showDiscardedRequests,
  );

  const localTickets = isArchivedView
    ? ticketsInColumn
    : ticketsInColumn.filter((ticket) => !ticket.archived_at);

  const uidList = localTickets?.map((l) => l.uid);

  const currentIndex = uidList?.indexOf(ticketData.uid);

  const { mutate: updateTicketAccount } = useApiMutation(
    `/v1/tickets/${ticketData.uid}`,
    {},
    "PATCH",
  );

  const { mutate: updateTicket } = useApiMutation(
    `/v1/tickets/${ticketData.uid}`,
    {},
    "PATCH",
  );

  const drawerNavigate = (direction: "up" | "down") => {
    if (!uidList) return;

    if ((currentIndex !== 0 && !currentIndex) || currentIndex === -1) return;

    let nextIndex;
    if (direction === "up") {
      nextIndex = currentIndex > 0 ? currentIndex - 1 : uidList.length - 1;
    } else {
      nextIndex = currentIndex < uidList.length - 1 ? currentIndex + 1 : 0;
    }

    const nextTicketId = uidList[nextIndex];
    const currentPath = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set("ticketId", nextTicketId);

    const url: string = `${currentPath}?${searchParams.toString()}`;

    //@ts-expect-error URL type error
    router.push(url);
  };

  const isLastTicket = currentIndex == uidList.length - 1;
  const ticketId = ticketData?.uid;

  const [copied, setCopied] = useState(false);
  const [isUpdatingGroup, setIsUpdatingGroup] = useState(false);
  const [updatedGroupName, setUpdatedGroupName] = useState<string | null>(null);
  const [updatedGroupId, setUpdatedGroupId] = useState<string | null>(null);
  const [groupPopoverOpen, setGroupPopoverOpen] = useState(false);
  const [isAccountDialogOpen, setIsAccountDialogOpen] = useState(false);
  const [showColor, setShowColor] = useState({
    up: false,
    down: false,
  });

  const { mutate } = useApiMutation(
    `/v1/tickets/${ticketData.uid}/unarchive`,
    {},
    "PATCH",
  );

  // Determine the current group display name
  const groupDisplayName = (() => {
    if (updatedGroupName) {
      return updatedGroupName;
    }

    if (!ticketData.subTeam || !ticketData.subTeam.uid) {
      return "No group";
    }

    // Check if the subTeam exists in the available subTeams
    const subTeamExists = useTicketMetaStore
      .getState()
      .subTeams.some((subTeam) => subTeam.id === ticketData.subTeam?.uid);

    return subTeamExists ? ticketData.subTeam.name : "No group";
  })();

  const handleCopyTicketId = async () => {
    // Use the exact ticket ID from the API without any transformation
    const ticketId = `${team?.identifier}-${ticketData.ticket_id}`;
    try {
      await navigator.clipboard.writeText(ticketId);
      setCopied(true);
      toast.success("Ticket ID copied to clipboard.");

      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } catch (error) {
      console.error("Failed to copy ticket ID:", error);
      toast.error("Failed to copy ticket ID.");
    }
  };

  useEffect(() => {
    if (ticketData) {
      setUpdatedGroupName(null);
    }
  }, [ticketId]);

  useHotkeys("j", () => {
    setShowColor((prev) => ({
      ...prev,
      down: true,
    }));
    drawerNavigate("down");
  });
  useHotkeys("k", () => {
    setShowColor((prev) => ({
      ...prev,
      up: true,
    }));
    drawerNavigate("up");
  });

  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;
    if (showColor.up || showColor.down) {
      timeoutId = setTimeout(() => {
        setShowColor({
          down: false,
          up: false,
        });
      }, 500);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [showColor]);
  // if (!team) return null;

  return (
    <div className="flex items-center justify-between h-14">
      <div className="flex items-center gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className="flex items-center justify-center rounded hover:bg-muted transition-colors cursor-pointer border border-transparent hover:border-border"
                onClick={() => closeDrawer(router)}
                role="button"
                style={{ width: "22px", height: "22px" }}
              >
                <X
                  size={18}
                  width={18}
                  height={18}
                  className="text-foreground"
                  strokeWidth={2.5}
                />
              </div>
            </TooltipTrigger>
            <TooltipContent
              side="bottom"
              sideOffset={5}
              className="font-medium text-foreground bg-background border border-border shadow-md px-3 py-2"
            >
              <div className="flex items-center gap-2">
                <span>Close ticket window</span>
                <span className="bg-primary/10 text-primary border border-primary/20 px-1.5 py-0.5 rounded text-xs font-semibold">
                  Esc
                </span>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <Separator orientation="vertical" className="h-[14px]" />

        <HeroIcon
          name={team.icon || "RocketLaunchIcon"}
          color={team.color}
          className="h-3.5 w-3.5 cursor-pointer"
          onClick={() => closeDrawer(router)}
        />

        <span
          className="text-muted-foreground cursor-pointer"
          onClick={() => closeDrawer(router)}
        >
          {team?.name}
        </span>

        <span className="text-muted-foreground">
          <ChevronRight size={16} />
        </span>

        <div
          className="inline-flex items-center px-2.5 py-0.5 text-xs font-normal cursor-pointer transition-colors bg-background text-foreground border border-border"
          style={{
            backgroundColor: copied ? "var(--color-primary-50)" : undefined,
            borderRadius: "4px",
            borderLeftWidth: "4px",
            borderLeftStyle: "solid",
            borderLeftColor: team?.color || undefined,
          }}
          onClick={handleCopyTicketId}
          title="Click to copy ticket ID"
        >
          {copied ? (
            <span className="flex items-center">
              {team?.identifier}-{ticketData.ticket_id}
              <CheckIcon className="ml-1 h-3 w-3" />
            </span>
          ) : (
            <span>
              {team?.identifier}-{ticketData.ticket_id}
            </span>
          )}
        </div>

        <div className="mx-2">
          <Dialog
            open={isAccountDialogOpen}
            onOpenChange={setIsAccountDialogOpen}
          >
            <DialogTrigger asChild>
              {ticketData.account?.name ? (
                <div className="flex items-center gap-2 cursor-pointer">
                  <Avatar
                    src={ticketData.account?.logo}
                    fallbackText={ticketData.account?.name.charAt(0)}
                    fallbackTextClassnames="h-5 w-5"
                    imgClassnames="h-5 w-5"
                  />
                  {ticketData.account?.name}
                </div>
              ) : (
                <div>
                  <IconButton
                    Icon={Building2}
                    size={16}
                    className="bg-transparent"
                  />
                </div>
              )}
            </DialogTrigger>
            <DialogContent
              className="sm:max-w-md p-0 overflow-hidden bg-background border border-color-border rounded-md shadow-md transition-all"
              style={{
                transform: "translate(-50%, -60%)",
              }}
            >
              <div className="flex justify-between items-center px-6 pt-3 pb-2 border-b border-color-border bg-color-card-bg-light">
                <DialogTitle className="text-base font-semibold text-foreground">
                  {ticketData.account?.name
                    ? "Change account"
                    : "Select account"}
                </DialogTitle>
              </div>
              <div className="px-6 pt-4 pb-6 flex items-center justify-center">
                <AccountSelector
                  onSelect={(accountId) => {
                    updateTicketAccount({
                      accountId,
                    })
                      .then(() => {
                        toast.success("Account updated successfully");
                        setIsAccountDialogOpen(false);
                      })
                      .catch((error) => {
                        console.error("Error updating account:", error);
                        toast.error("Failed to update account");
                      });
                  }}
                  placeholder="Search for an account"
                  emptyMessage="No accounts found"
                />
              </div>
            </DialogContent>
          </Dialog>
        </div>
        <MoreOptions
          team={team}
          ticketData={ticketData}
          drawerNavigate={drawerNavigate}
          closeDrawer={closeDrawer}
          isLastTicket={isLastTicket}
        />

        {ticketData.archivedAt && (
          <div className="text-sm bg-color-bg-subtle border border-border rounded px-2">
            Archived
          </div>
        )}
      </div>
      <div className="flex items-center gap-2">
        {ticketData.archivedAt && (
          <Button
            variant="secondary"
            className="shadow-sm border bg-background h-6"
            onClick={() => {
              mutate()
                .then(() => {
                  toast.success("Ticket restored from archive.");
                })
                .catch((error) => {
                  console.error("Error restoring ticket:", error);
                  toast.error("Failed to restore ticket.");
                });
            }}
          >
            Restore from archive
          </Button>
        )}

        {/* Group field */}
        <Popover open={groupPopoverOpen} onOpenChange={setGroupPopoverOpen}>
          <PopoverTrigger asChild>
            <div className="flex items-center gap-2 border rounded-md bg-background h-7 px-2.5 hover:border-primary/60 hover:bg-primary/5 transition-colors overflow-hidden cursor-pointer shadow-sm">
              <Users
                size={14}
                className={`${
                  isUpdatingGroup ? "animate-pulse" : ""
                } text-muted-foreground`}
              />
              <span className="text-sm text-muted-foreground font-medium">
                Group:
              </span>
              <span
                className={`text-sm font-semibold truncate max-w-[120px] ${
                  isUpdatingGroup
                    ? "text-muted-foreground animate-pulse"
                    : "text-primary/90"
                }`}
              >
                {groupDisplayName}
              </span>
              {isUpdatingGroup ? (
                <span className="ml-1 h-3.5 w-3.5 rounded-full border-2 border-t-transparent border-primary animate-spin"></span>
              ) : (
                <ChevronDown size={14} className="text-muted-foreground ml-1" />
              )}
            </div>
          </PopoverTrigger>
          <PopoverContent
            className="w-[180px] p-0"
            align="start"
            sideOffset={5}
          >
            <Command>
              <CommandInput placeholder="Search group..." />
              <CommandList>
                <CommandEmpty>No group found.</CommandEmpty>
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      // Close dropdown
                      setGroupPopoverOpen(false);

                      // Optimistic UI update
                      setIsUpdatingGroup(true);
                      setUpdatedGroupName("No group");
                      setUpdatedGroupId(null);

                      updateTicket({ subTeamId: null })
                        .then(() => {
                          setIsUpdatingGroup(false);
                        })
                        .catch((error) => {
                          setIsUpdatingGroup(false);
                          setUpdatedGroupName(null);
                          setUpdatedGroupId(null);
                          console.error("Error updating group:", error);
                        });
                    }}
                    className="flex items-center"
                  >
                    <span>No group</span>
                    {!ticketData.subTeam?.uid && !updatedGroupId && (
                      <CheckIcon className="ml-auto h-4 w-4" />
                    )}
                  </CommandItem>
                  {useTicketMetaStore.getState().subTeams.map((subTeam) => (
                    <CommandItem
                      key={subTeam.id}
                      onSelect={() => {
                        // Close dropdown
                        setGroupPopoverOpen(false);

                        // Optimistic UI update
                        setIsUpdatingGroup(true);
                        setUpdatedGroupName(subTeam.name);
                        setUpdatedGroupId(subTeam.id);

                        updateTicket({ subTeamId: subTeam.id })
                          .then(() => {
                            setIsUpdatingGroup(false);
                          })
                          .catch((error) => {
                            setIsUpdatingGroup(false);
                            setUpdatedGroupName(null);
                            setUpdatedGroupId(null);
                            console.error("Error updating group:", error);
                          });
                      }}
                      className="flex items-center"
                    >
                      {subTeam.icon && (
                        <HeroIcon
                          name={subTeam.icon || "RocketLaunchIcon"}
                          color={subTeam.color}
                          className="h-3.5 w-3.5 mr-2"
                        />
                      )}
                      <span>{subTeam.name}</span>
                      {(ticketData.subTeam?.uid === subTeam.id ||
                        updatedGroupId === subTeam.id) && (
                        <CheckIcon className="ml-auto h-4 w-4" />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="shadow-sm border bg-background h-6 w-6 p-0"
            >
              <SlidersHorizontal size={16} />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[312px] p-0" align="end" side="bottom">
            <FieldsList />
          </PopoverContent>
        </Popover>
        <Separator orientation="vertical" className="h-[14px]" />
        <span className="text-sm">
          <span>{uidList?.indexOf(ticketData.uid) + 1}</span> /{" "}
          <span className="text-muted-text">{uidList?.length || 0}</span>
        </span>
        <Button
          variant="outline"
          disabled={currentIndex === 0}
          className={cn(
            "shadow-sm border bg-background h-6 w-6 p-0 cursor-pointer",
            showColor.up && "bg-accent",
          )}
          onClick={() => drawerNavigate("up")}
        >
          <ChevronUp size={16} />
        </Button>
        <Button
          variant="outline"
          className={cn(
            "shadow-sm border bg-background h-6 w-6 p-0 cursor-pointer",
            showColor.down && "bg-accent",
          )}
          onClick={() => drawerNavigate("down")}
          disabled={isLastTicket}
        >
          <ChevronDown size={16} />
        </Button>
      </div>
    </div>
  );
};

export default TicketDetailsHeader;
