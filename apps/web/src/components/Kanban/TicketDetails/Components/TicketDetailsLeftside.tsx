import Thena<PERSON>ogo from "@/assets/SvgComponents/ThenaLogo";
import Avatar from "@/components/common/Avatar";
import CopyToClipboard from "@/components/common/CopyToClipboard";
import SlackColoredIcon from "@/components/icons/SlackColoredIcon";
import { useAiToken } from "@/components/tiptap/editor/useAiToken";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useApi } from "@/hooks/use-api";
import { GET_TICKET_ACTIVITY } from "@/services/kanban";
import { ActivityLog, Comment, SingleTicket } from "@/types/kanban";
import { getTimeAgo } from "@/utils/kanban";
import { format } from "date-fns";
import {
  Activity,
  Mail,
  MessageSquare,
  MessageSquareText,
  PlugZap,
  StickyNote,
} from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { Panel } from "react-resizable-panels";
import MsTeamsLogo from "../../../../assets/SvgComponents/MsTeamsLogo";
import TooltipWrapper from "../../../tooltip-wrapper";
import Activities from "./Activities/Activities";
import { AITicketInsightsModal } from "./AITicketInsightsModal";
import Conversations from "./Conversations/Conversations";
import DescriptionEditor from "./DescriptionEditor";
import Notes from "./Notes/Notes";
import TicketDetailsTitle from "./TicketDetailsTitle";

type Tprops = {
  ticketData: SingleTicket;
  allConversations: {
    comments: Comment[];
    notes: Comment[];
  };
  refetch: () => void;
  loading: boolean;
};

export const getSourceIcon = (source: string) => {
  switch (source) {
    case "slack":
      return <SlackColoredIcon size={14} />;
    case "email":
      return <Mail size={16} />;
    case "web":
      return <MessageSquare size={16} />;
    case "api":
      return <PlugZap size={16} />;
    case "manual":
      return <ThenaLogo />;
    case "ms-teams":
      return <MsTeamsLogo />;
    default:
  }
};

const TicketDetailsLeftside = ({
  ticketData,
  allConversations,
  refetch,
  loading,
}: Tprops) => {
  const params = useSearchParams();

  const cid = params.get("cid");
  const nid = params.get("nid");

  const [tabValue, setTabValue] = useState(nid ? "notes" : "conversation");

  const { data: activity } = useApi<ActivityLog[]>(
    GET_TICKET_ACTIVITY(ticketData.uid),
    {},
    { enabled: true, isNextApi: true },
  );

  useHotkeys("shift+c", () => {
    setTabValue("conversation");
  });
  useHotkeys("shift+n", () => {
    setTabValue("notes");
  });
  useHotkeys("shift+a", () => {
    setTabValue("activity");
  });

  const aiToken = useAiToken();

  useEffect(() => {
    if (cid) {
      document.getElementById(cid)?.scrollIntoView({ behavior: "smooth" });
    } else if (nid) {
      document.getElementById(nid)?.scrollIntoView({ behavior: "smooth" });
    }
  }, [cid, nid]);

  const notes: Comment[] = allConversations?.notes;

  const conversations =
    allConversations?.comments?.sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    ) || [];

  return (
    <Panel
      defaultSize={65}
      minSize={50}
      className="h-full bg-background border rounded-[4px] flex justify-center !overflow-auto"
    >
      <div className="h-full w-full large-screen:max-w-[784px]">
        <div className="p-4 pt-8 px-2 sm:px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-24 [@media(min-width:1664px)_and_(max-width:1792px)]:px-32">
          <TicketDetailsTitle title={ticketData.title} id={ticketData.uid} />

          <div className="text-sm text-foreground mb-2 mt-3">
            <DescriptionEditor
              description={ticketData?.description}
              ticketId={ticketData.uid}
              aiToken={aiToken}
              // Why key? Need to rerender for the aiToken to be used by the editor. Can be improved.
              key={aiToken}
            />
          </div>
          <div className="flex items-center gap-2 mb-6 text-xs text-muted-foreground">
            {ticketData.created_by?.first_name && (
              <Tooltip>
                <TooltipTrigger>
                  <div className="flex items-center gap-2 border w-min whitespace-nowrap rounded p-0.5 px-2">
                    Created by{" "}
                    <Avatar
                      src={ticketData.created_by.avatar_url as string}
                      imgClassnames="h-5 min-w-5 max-w-5"
                    />
                    <span className="font-medium text-color-text">
                      {ticketData.created_by?.first_name || ""}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent className="bg-background border shadow p-1 pl-2">
                  <div className="flex items-center text-color-text gap-8">
                    {ticketData.authorEmail}{" "}
                    <CopyToClipboard
                      text={ticketData.authorEmail}
                      className="border-none"
                    />
                  </div>
                </TooltipContent>
              </Tooltip>
            )}
            {/* {format(new Date(ticketData.createdAt), "MMM d")} */}
            <TooltipWrapper
              tooltipContent={format(
                new Date(ticketData.createdAt),
                "d MMM, h:mm a",
              )}
            >
              {" "}
              {getTimeAgo(ticketData.createdAt)}{" "}
            </TooltipWrapper>
            via{" "}
            <span className="text-muted-foreground">
              {getSourceIcon(ticketData.source?.toLowerCase())}
            </span>
          </div>

          <AITicketInsightsModal
            ai_generated_title={ticketData.ai_generated_title}
            ai_generated_summary={ticketData.ai_generated_summary}
          />

          <Tabs
            value={tabValue}
            onValueChange={setTabValue}
            defaultValue="conversation"
            className="w-full"
          >
            <TabsList className="w-full justify-start bg-background border-b rounded-none p-0 shadow-none h-min">
              <TabsTrigger
                value="conversation"
                className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b-[1px] data-[state=active]:border-b-primary py-3 pl-0"
              >
                <MessageSquareText className="h-4 w-4" />
                Conversation{" "}
                <span
                  className="rounded-md px-1.5 py-0.5 text-xs font-medium"
                  style={{
                    backgroundColor: "var(--counter-blue-bg)",
                    color: "var(--counter-blue-text)",
                  }}
                >
                  {conversations?.length}
                </span>
              </TabsTrigger>
              <TabsTrigger
                value="notes"
                className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b-[1px] data-[state=active]:border-b-primary py-3"
              >
                <StickyNote className="h-4 w-4" />
                Notes{" "}
                <span
                  className="rounded-md px-1.5 py-0.5 text-xs font-medium"
                  style={{
                    backgroundColor: "var(--counter-orange-bg)",
                    color: "var(--counter-orange-text)",
                  }}
                >
                  {notes?.length}
                </span>
              </TabsTrigger>
              <TabsTrigger
                value="activity"
                className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b-[1px] data-[state=active]:border-b-primary py-3"
              >
                <Activity className="h-4 w-4" />
                Activity
              </TabsTrigger>

              <div className="flex items-center gap-2 ml-auto">
                {/* <Button variant="outline" className="h-6 text-xs">
                  Open in <Slack size={16} />
                </Button> */}
                <CopyToClipboard text={window.location.href} />
              </div>
            </TabsList>

            <TabsContent
              value="conversation"
              className="mt-6 relative h-[calc(100vh-265px)] pb-8"
            >
              <Conversations
                conversations={conversations}
                ticketId={ticketData.uid}
                refetch={refetch}
                source={ticketData.source}
              />
            </TabsContent>
            <TabsContent
              value="notes"
              className="mt-6 relative  h-[calc(100vh-240px)]"
            >
              <Notes
                loading={loading}
                notes={notes}
                refetch={refetch}
                ticketId={ticketData.uid}
              />
            </TabsContent>
            <TabsContent
              value="activity"
              className="mt-6 relative  h-[calc(100vh-240px)]"
            >
              <Activities activity={activity} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Panel>
  );
};

export default TicketDetailsLeftside;
