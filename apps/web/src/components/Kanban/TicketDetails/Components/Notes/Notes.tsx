import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useApiMutation } from "@/hooks/use-api-mutation";
import {
  POST_COMMENT_ON_TICKET,
  UPDATE_COMMENT_BY_ID,
} from "@/services/kanban";
import { getTimeAgo, highlightMentions } from "@/utils/kanban";
import { Editor } from "@tiptap/core";
import { format } from "date-fns";
import DOMPurify from "dompurify";
import { FilePlus2, Lock, StickyNote } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Comment } from "../../../../../types/kanban";
import Avatar from "../../../../common/Avatar";
import TooltipWrapper from "../../../../tooltip-wrapper";
import Tiptap from "../Tiptap/Tiptap";
import CreateNote from "./CreateNote";
import NoteOptions from "./NoteOptions";
import styled from "styled-components";
import { cn } from "../../../../../lib/utils";

const NotesWrapper = styled.div`

  a {
    color: var(--url-color);
  }

  .mention {
    color: var(--mention-color);
    background-color: var(--mention-bg);
    padding: var(--mention-padding);
    border-radius: var(--mention-border-radius);
    font-weight: var(--mention-font-weight);
  }
`;

const Notes = ({ ticketId, refetch, notes, loading }) => {
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [content, setContent] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);

  const [editOpen, setEditOpen] = useState({
    id: null,
    state: false,
  });

  const { mutate, isSuccess } = useApiMutation(POST_COMMENT_ON_TICKET);

  useEffect(() => {
    if (isSuccess) {
      refetch();
      setContent(" ");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccess]);

  const addNote = async (editor: Editor) => {
    if (editor.isEmpty) {
      toast.error("Note cannot be empty");
      return null;
    }
    try {
      await mutate({
        content: editor.getText(),
        contentJson: JSON.stringify(editor.getJSON()),
        contentHtml: editor.getHTML(),
        commentVisibility: isPrivate ? "private" : "public",
        commentType: "note",
        entityType: "ticket",
        entityId: ticketId,
      });
      setIsAddingNote(false);
      editor.commands.clearContent();
    } catch (err) {
      console.error("Error posting note:", err);
    }
  };

  if ((!notes || notes.length === 0) && !loading) {
    return (
      <div className="w-full flex justify-center">
        {!isAddingNote && (
          <div className="flex flex-col items-center justify-center w-full py-4 text-center">
            <div className="bg-muted rounded-md p-3 mb-2">
              <StickyNote className="h-5 w-5 text-muted-foreground" />
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Add notes for team updates or personal reminders.
            </p>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() => setIsAddingNote(true)}
            >
              <FilePlus2 className="h-4 w-4" /> New note
            </Button>
          </div>
        )}

        <CreateNote
          isAddingNote={isAddingNote}
          setIsAddingNote={setIsAddingNote}
          isPrivate={isPrivate}
          setIsPrivate={setIsPrivate}
          content={content}
          setContent={setContent}
          addNote={addNote}
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">All notes</h2>
        <Button
          variant="default"
          className="h-7"
          onClick={() => setIsAddingNote(true)}
        >
          <span className="mr-2">
            <FilePlus2 size={16} />
          </span>{" "}
          New note
        </Button>
      </div>

      <ScrollArea className="flex-1 my-4 pb-10">
        <div className="space-y-2">
          <CreateNote
            isAddingNote={isAddingNote}
            setIsAddingNote={setIsAddingNote}
            isPrivate={isPrivate}
            setIsPrivate={setIsPrivate}
            content={content}
            setContent={setContent}
            addNote={addNote}
          />

          {(notes || []).map((note, id) => {
            return (
              <NoteCard
                key={id}
                note={note}
                setEditOpen={setEditOpen}
                editOpen={editOpen}
              />
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
};

export default Notes;

const NoteCard = ({
  note,
  setEditOpen,
  editOpen,
}: {
  note: Comment;
  setEditOpen: ({ id, state }: { id: string; state: boolean }) => void;
  editOpen: { id: string; state: boolean };
}) => {
  const { mutate: updateMutation } = useApiMutation(
    UPDATE_COMMENT_BY_ID(note.id),
    {},
    "PATCH",
  );
  const [editContent, setEditContent] = useState(note.contentHtml);
  const updateComment = async (editor: Editor) => {
    if (editor.isEmpty) {
      toast.error("Comment cannot be empty");
      return;
    }

    if (editor.getHTML()?.length > 5000) {
      toast.error("Comment cannot be more than 5000 characters");
      return;
    }
    await updateMutation({
      content: editor.getText(),
      contentJson: JSON.stringify(editor.getJSON()),
      contentHtml: editor.getHTML(),
    });
    editor.commands.clearContent();
    setEditOpen({ id: null, state: false });
  };

  return (
    <div className="p-4 rounded-sm border bg-color-bg-subtle group">
      <div className="flex items-start gap-3">
        <Avatar
          src={note.impersonatedUserAvatar || note.authorAvatarUrl}
          fallbackText={note.impersonatedUserName || note.author}
          imgClassnames="h-8 min-w-8 max-w-8"
          fallbackTextClassnames="h-8 min-w-8 max-w-8"
        />
        <div className="flex-1">
          <div className="flex justify-between items-center">
            <div className="flex gap-2 items-center mb-1">
              <span className="font-medium">
                {note.impersonatedUserName || note.author}
              </span>

              <TooltipWrapper
                tooltipContent={format(
                  new Date(note.createdAt),
                  "d MMM, h:mm a",
                )}
              >
                {" "}
                <span className="text-sm text-muted-foreground">
                  {getTimeAgo(note.createdAt)}
                </span>
              </TooltipWrapper>
              {note.commentVisibility === "private" && (
                <div className="bg-[var(--color-strong)] flex items-center gap-1 text-xs p-0.5 px-2 rounded-sm">
                  {" "}
                  <Lock size={12} /> Private
                </div>
              )}
            </div>

            <div className="relative">
              <NoteOptions conversation={note} setEditOpen={setEditOpen} />
            </div>
          </div>

          <div className="max-w-[40rem] tab:max-w-[30rem] w-full overflow-hidden">
            {editOpen.state && editOpen?.id === note.id ? (
              <Tiptap
                content={editContent}
                setContent={setEditContent}
                onSend={(editor) => updateComment(editor)}
                isFileUploadEnabled={false}
                localFiles={[]}
                setLocalFiles={() => {}}
                setEditOpen={setEditOpen}
              />
            ) : (
              <NotesWrapper
                className={cn("text-sm leading-relaxed")}
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(highlightMentions(note.contentHtml)),
                }}
              ></NotesWrapper>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
