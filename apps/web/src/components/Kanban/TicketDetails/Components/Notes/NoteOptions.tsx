import Avatar from "@/components/common/Avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
} from "@/components/ui/dialog";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { DELETE_COMMENT_BY_ID } from "@/services/kanban";
import { copyTextOnClipboard, getTimeAgo, highlightMentions } from "@/utils/kanban";
import { Ellipsis, Link, Pencil, Trash2 } from "lucide-react";
import styled from "styled-components";
import DOMPurify from "dompurify";
import { useState } from "react";
import { useGlobalConfigPersistStore } from "../../../../../store/globalConfigPersistStore";

const NoteContentWrapper = styled.div`  
  /* Specific styling for links in Lumen theme */
  a {
    color: var(--url-color);
  }
  
  ol,
  ul {
    margin-left: 15px;
  }

  blockquote {
    color: var(--color-text);
    border-left: 4px solid var(--slack-blockquote-left-border);
    padding-left: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
    margin: 8px 0;
    background-color: var(--slack-code-block-bg);
    border-radius: 4px;
    min-height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  blockquote p {
    color: var(--color-text);
    padding: 0;
    margin: 0;
    line-height: 1.5;
  }

  p:empty {
    padding-top: 16px;
  }

  h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
    font-weight: 500;
  }

  h3 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
    font-weight: 500;
  }

  p {
    font-size: 14px; /* 14px */
    line-height: 1.5rem; /* 24px */
  }

  p.is-empty::before {
    color: var(--color-text-muted);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  code {
    font-size: 0.875rem; /* 14px */
    font-family: monospace;
  }

  word-break: break-word;
  overflow-wrap: anywhere;

  ul,

  ol {
    padding: 0 1rem;
    margin: 0rem 1rem 0rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  hr[contenteditable="false"] {
    margin-bottom: 0;
    border-top: 1px solid var(--color-border);
  }

  .mention {
    color: var(--mention-color);
    background-color: var(--mention-bg);
    padding: var(--mention-padding);
    border-radius: var(--mention-border-radius);
    font-weight: var(--mention-font-weight);
  }
`;

const NoteOptions = ({ conversation, setEditOpen }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const isAuthor = conversation.authorId === currentUser.uid;
  const isImpersonatedUser =
    conversation.impersonatedUserId === currentUser.uid;

  const isAllowed = isAuthor || isImpersonatedUser;

  const { mutate } = useApiMutation(
    DELETE_COMMENT_BY_ID(conversation.id),
    {},
    "DELETE",
  );

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <Ellipsis size={16} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          side="bottom"
          sideOffset={5}
          className="w-[180px]"
        >
          {isAllowed && (
            <DropdownMenuItem
              onClick={() => {
                setEditOpen({ id: conversation.id, state: true });
              }}
            >
              <Pencil size={16} className="mr-2" />
              <span>Edit</span>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={() => {
              copyTextOnClipboard(
                `${window.location.href}&nid=${conversation.id}`,
              );
            }}
          >
            <Link size={16} className="mr-2" />
            <span>Copy link</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {isAllowed && (
            <DropdownMenuItem
              onClick={() => setIsDialogOpen(true)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 size={16} className="mr-2" />
              <span>Delete</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogTitle>Delete note</DialogTitle>
          <DialogDescription className="test-sm text-muted-text">
            Are you sure you want to delete this note? This cannot be undone.
          </DialogDescription>
          <div className="w-full max-h-[300px] flex items-start p-5 gap-4 rounded-sm border overflow-y-auto">
            <Avatar src={conversation.impersonatedUserAvatar || conversation.authorAvatarUrl}
              fallbackText={conversation.impersonatedUserName || conversation.author}
               fallbackTextClassnames="h-9 w-9"
              imgClassnames="h-9 w-9" />
            <div className="w-full h-auto space-y-2">
              <div className="flex gap-4 justify-start items-center">
                <p className="text-sm font-bold">{conversation.impersonatedUserName || conversation.author}</p>
                <span className="text-xs text-muted-foreground">
                  {getTimeAgo(conversation.createdAt)}
                </span>
              </div>
              <NoteContentWrapper dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(highlightMentions(conversation.contentHtml)) }} />
            </div>
          </div>
          <DialogFooter>
            <Button variant="ghost" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                mutate().then(() => setIsDialogOpen(false));
              }}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default NoteOptions;
