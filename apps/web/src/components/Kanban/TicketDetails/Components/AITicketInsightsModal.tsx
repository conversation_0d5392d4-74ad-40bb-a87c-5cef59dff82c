import {
  Frosted<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FrostedDialogTrigger,
} from "@/components/ui/FrostedGlassDialog";
import { cn } from "@/lib/utils";
import { highlightMentions } from "@/utils/kanban";
import DOMPurify from "dompurify";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronDown, ChevronUp, Expand, Sparkles } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import styled from "styled-components";

interface AITicketInsightsModalProps {
  ai_generated_title: string | null;
  ai_generated_summary: string | null;
}

const AiSummaryWrapper = styled.div`
  color: var(--color-text);
  h1,
  h2,
  h3,
  h4 {
    color: var(--color-text);
  }

  input[type="checkbox"] {
    height: 16px;
    width: 16px;
    border-radius: 4px;
    border: 1px solid var(--color-text-muted);
    background-color: var(--color-background);
    cursor: default;
    margin-right: 8px;
    vertical-align: middle;
    pointer-events: none;
  }

  input[type="checkbox"]:checked {
    background-color: var(--color-success, #22c55e);
    border-color: var(--color-success, #22c55e);
  }

  ul,
  ol {
    padding-left: 24px;
    margin: 8px 0;
    color: var(--color-text);
  }

  li {
    margin: 4px 0;
    color: var(--color-text);
  }

  ul {
    list-style-type: disc;
    color: var(--color-text);
  }

  ol {
    list-style-type: decimal;
    color: var(--color-text);
  }

  a {
    color: var(--color-primary, #3b82f6);
    text-decoration: underline;
  }

  code {
    background-color: var(--color-bg-subtle, #f3f4f6);
    border-radius: 3px;
    padding: 0.2em 0.4em;
    font-family: monospace;
  }

  blockquote {
    border-left: 3px solid var(--color-border, #e5e7eb);
    padding-left: 1em;
    margin-left: 0;
    margin-right: 0;
    color: var(--color-text-muted, #4b5563);
  }
`;

export function AITicketInsightsModal({
  ai_generated_title,
  ai_generated_summary,
}: AITicketInsightsModalProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const currentContent = contentRef.current;
    if (currentContent && (ai_generated_title || ai_generated_summary)) {
      if (!isExpanded) {
        setIsOverflowing(
          currentContent.scrollHeight > currentContent.clientHeight,
        );
      } else {
        setIsOverflowing(!!ai_generated_summary);
      }
    }
  }, [ai_generated_summary, ai_generated_title, isExpanded]);

  // Early return if no AI content is available
  if (!ai_generated_title && !ai_generated_summary) {
    return null;
  }

  const getBannerText = () => {
    const hasTitle = !!ai_generated_title;
    const hasSummary = !!ai_generated_summary;

    if (hasTitle && hasSummary) {
      return "AI title & recap";
    } else if (hasTitle) {
      return "AI title";
    } else if (hasSummary) {
      return "AI recap";
    }
    return "View AI insights";
  };
  // Extract max-height value based on isExpanded state
  const maxHeightClass = isExpanded ? "max-h-[85vh]" : "max-h-[70vh]";

  return (
    <FrostedDialog>
      <AnimatePresence>
        <FrostedDialogTrigger asChild>
          <div className="flex items-center gap-2 mb-6 px-4 py-2 border rounded-sm bg-gradient-to-r from-primary/5 to-muted/10 border-border/50 cursor-pointer hover:bg-primary/10 transition-all duration-200">
            <Sparkles className="h-4 w-4 text-[var(--color-icon-magic)]" />
            <span className="text-sm color-text font-medium flex-1">
              {getBannerText()}
            </span>
            <Expand className="h-3.5 w-3.5 text-muted-foreground/70" />
          </div>
        </FrostedDialogTrigger>
        <FrostedDialogContent
          hideClose
          className={cn(
            "w-auto sm:min-w-[600px] min-h-[400px] max-w-[95vw] sm:max-w-[90vw] md:max-w-[720px] border border-border/50 rounded-sm p-0 overflow-hidden",
            maxHeightClass,
          )}
        >
          <motion.div
            className={cn(
              "relative z-10 flex flex-col h-full overflow-",
              maxHeightClass,
            )}
            initial={{ opacity: 0, y: -12 }}
            animate={{
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.4,
                ease: [0.2, 0, 0.1, 1],
                opacity: { duration: 0.35 },
              },
            }}
            exit={{
              opacity: 0,
              y: -8,
              transition: {
                duration: 0.25,
                ease: [0.4, 0, 0.2, 1],
                opacity: { duration: 0.2 },
              },
            }}
          >
            <div className="space-y-6 p-8 w-full min-h-[400px] max-w-full overflow-y-auto">
              {ai_generated_title && (
                <div className="mb-4">
                  <div className="inline-flex items-center gap-2 text-sm font-medium text-foreground/80 mb-3 px-3 py-1 bg-background/50 backdrop-blur-sm border border-border/30 rounded-sm">
                    <Sparkles className="h-4 w-4 text-[var(--color-icon-magic)]" />
                    <span>AI title</span>
                  </div>
                  <div className="text-foreground text-sm leading-relaxed">
                    {ai_generated_title}
                  </div>
                </div>
              )}

              {ai_generated_summary && (
                <div>
                  <div className="inline-flex items-center gap-2 text-sm font-medium text-foreground/80 mb-3 px-3 py-1 bg-background/50 backdrop-blur-sm border border-border/30 rounded-sm">
                    <Sparkles className="h-4 w-4 text-[var(--color-icon-magic)]" />
                    <span>AI ticket recap</span>
                  </div>
                  <div className="relative">
                    <div
                      ref={contentRef}
                      className={cn(
                        "text-foreground text-sm leading-relaxed",
                        isExpanded ? "" : "max-h-[400px]",
                      )}
                    >
                      <AiSummaryWrapper
                        className="prose prose-sm max-w-none py-6"
                        dangerouslySetInnerHTML={{
                          __html: DOMPurify.sanitize(
                            highlightMentions(ai_generated_summary),
                          ),
                        }}
                      />
                    </div>
                    {(isOverflowing || isExpanded) && ai_generated_summary && (
                      <div
                        className={cn(
                          "flex items-end justify-center w-full mt-4",
                          !isExpanded &&
                            "absolute bottom-0 left-0 right-0 h-20 pointer-events-none bg-gradient-to-t from-background/70 to-transparent",
                        )}
                      >
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsExpanded(!isExpanded);
                          }}
                          className="pointer-events-auto mb-2 px-3 py-1.5 text-xs font-medium text-primary hover:text-primary/80 bg-background/80 backdrop-blur-sm border border-border/30 rounded-full flex items-center gap-1.5 shadow-sm hover:shadow transition-all"
                        >
                          {isExpanded ? (
                            <>
                              <span>Show less</span>
                              <ChevronUp className="h-3.5 w-3.5" />
                            </>
                          ) : (
                            <>
                              <span>Show more</span>
                              <ChevronDown className="h-3.5 w-3.5" />
                            </>
                          )}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </FrostedDialogContent>
      </AnimatePresence>
    </FrostedDialog>
  );
}
