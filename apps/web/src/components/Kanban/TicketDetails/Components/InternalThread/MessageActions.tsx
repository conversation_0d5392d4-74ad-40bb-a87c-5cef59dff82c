import Avatar from "@/components/common/Avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import DOMPurify from "dompurify";
import { MoreHorizontal } from "lucide-react";
import { useState } from "react";
import styled from "styled-components";
import { Comment } from "../../../../../types/kanban";
import { getTimeAgo, highlightMentions } from "../../../../../utils/kanban";
import { SlackIcon } from "../../../../icons/slack";

const MessageContentWrapper = styled.div`
  
  /* Specific styling for links in Lumen theme */
  a {
    color: var(--url-color);
  }
  
  ol,
  ul {
    margin-left: 15px;
  }

  blockquote {
    color: var(--color-text);
    border-left: 4px solid var(--slack-blockquote-left-border);
    padding-left: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
    margin: 8px 0;
    background-color: var(--slack-code-block-bg);
    border-radius: 4px;
    min-height: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  blockquote p {
    color: var(--color-text);
    padding: 0;
    margin: 0;
    line-height: 1.5;
  }

  p:empty {
    padding-top: 16px;
  }

  h2 {
    font-size: 1.5rem; /* 24px */
    line-height: 2rem; /* 32px */
    font-weight: 500;
  }

  h3 {
    font-size: 1.25rem; /* 20px */
    line-height: 1.75rem; /* 28px */
    font-weight: 500;
  }

  p {
    font-size: 14px; /* 14px */
    line-height: 1.5rem; /* 24px */
  }

  p.is-empty::before {
    color: var(--color-text-muted);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  code {
    font-size: 0.875rem; /* 14px */
    font-family: monospace;
  }

  word-break: break-word;
  overflow-wrap: anywhere;

  ul,

  ol {
    padding: 0 1rem;
    margin: 0rem 1rem 0rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  hr[contenteditable="false"] {
    margin-bottom: 0;
    border-top: 1px solid var(--color-border);
  }

  .mention {
    color: var(--mention-color);
    background-color: var(--mention-bg);
    padding: var(--mention-padding);
    border-radius: var(--mention-border-radius);
    font-weight: var(--mention-font-weight);
  }
`;

interface MessageActionsProps {
  message: Comment;
  onEdit: (message: Comment) => void;
  onDelete: (messageId: string) => void;
  setDeleteMessageId: (messageId: string) => void;
  setEditOpen: (editState: { id: string | null; state: boolean }) => void;
}

const MessageActions = ({
  message,
  onEdit,
  onDelete,
  setDeleteMessageId,
  setEditOpen,
}: MessageActionsProps) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleDelete = () => {
    onDelete(message.id);
    setShowDeleteDialog(false);
  };

  return (
    <>
      <div
        className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center"
        onMouseEnter={() => {
          setDeleteMessageId(message.id);
        }}
      >
        {message.metadata?.external_sinks?.slack?.slackThreadLink && (
          <div className="rounded flex items-center justify-center">
            <a
              href={message.metadata?.external_sinks?.slack?.slackThreadLink}
              target="_blank"
              rel="noreferrer"
              className="px-2 py-0.5 flex items-center justify-center hover:bg-color-strong bg-transparent rounded"
            >
              <SlackIcon className="h-4 w-4 scale-90" />
            </a>
          </div>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-7 w-7">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => {
                onEdit(message);
                setEditOpen({
                  id: message.id,
                  state: true,
                });
              }}
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onClick={() => setShowDeleteDialog(true)}
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete message</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this message? This action cannot
              be undone.
            </DialogDescription>
            <div className="w-full max-h-[300px] flex items-start p-5 gap-4 rounded-sm border overflow-y-auto">
              <Avatar src={message.impersonatedUserAvatar || message.authorAvatarUrl}
                fallbackText={message.impersonatedUserName || message.author}
                fallbackTextClassnames="h-9 w-9"
                imgClassnames="h-9 w-9" />
              <div className="w-full h-auto space-y-2">
                <div className="flex gap-4 justify-start items-center">
                  <p className="text-sm font-bold">{message.impersonatedUserName || message.author}</p>
                  <span className="text-xs text-muted-foreground">
                    {getTimeAgo(message.createdAt)}
                  </span>
                </div>
                <MessageContentWrapper dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(highlightMentions(message.contentHtml)) }} />
              </div>
            </div>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MessageActions;
