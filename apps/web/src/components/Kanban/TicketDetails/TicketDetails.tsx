import { getRealtimeToken } from "@/app/actions/auth";
import { useApi } from "@/hooks/use-api";
import { createClient } from "@/lib/supabase-client";
import { cn } from "@/lib/utils";
import { GET_TICKET_BY_ID } from "@/services/kanban";
import { useTicketDetailsDisplayOptions } from "@/store/ticket-details-display-options";
import { useTicketDrawerStore } from "@/store/ticketDrawerStore";
import { Comment, SingleTicket } from "@/types/kanban";
import { GripVertical } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { HotkeysProvider, useHotkeys } from "react-hotkeys-hook";
import { PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { useGlobalConfigPersistStore } from "../../../store/globalConfigPersistStore";
import { useInboxStore } from "../../../store/inbox-store";
import { useSidebar } from "../../ui/sidebar";
import TicketDetailsHeader from "./Components/TicketDetailsHeader";
import TicketDetailsLeftside from "./Components/TicketDetailsLeftside";
import TicketDetailsRightside from "./Components/TicketDetailsRightside";

// Resize handle component with three dots that can toggle the right panel
const ResizeHandle = ({
  isRightSideOpen,
  setIsRightSideOpen,
}: {
  isRightSideOpen: boolean;
  setIsRightSideOpen: (isOpen: boolean) => void;
}) => {
  const handleToggleRightPanel = () => {
    setIsRightSideOpen(!isRightSideOpen);
  };

  return (
    <PanelResizeHandle className="relative flex items-center justify-center w-2 mx-0.5 group">
      <div
        className="absolute h-12 flex items-center justify-center cursor-pointer"
        onClick={handleToggleRightPanel}
        title={
          isRightSideOpen
            ? "Hide details panel (Alt+D / ⌥+D) | Toggle both panels (Alt+F / ⌥+F)"
            : "Show details panel (Alt+D / ⌥+D) | Toggle both panels (Alt+F / ⌥+F)"
        }
      >
        <GripVertical
          size={12}
          className="text-muted-foreground/40 group-hover:text-muted-foreground/70 transition-colors"
        />
      </div>
    </PanelResizeHandle>
  );
};

export function TicketDetails() {
  const { isOpen, ticketId, closeDrawer, setForceUpdateReply } =
    useTicketDrawerStore();
  const { state, toggleSidebar } = useSidebar();
  const isSidebarOpen = state === "expanded";
  const pathname = usePathname();
  const router = useRouter();
  const isInboxPage = pathname.startsWith("/inbox");
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);
  const [isRightSideOpen, setIsRightSideOpen] = useState(
    isInboxPage ? false : true,
  );
  // We don't need the isExpanding state anymore as we'll use CSS transitions
  const supabase = createClient();

  const { data: ticketData, refetch: ticketRefetch } = useApi<SingleTicket>(
    GET_TICKET_BY_ID(ticketId),
    {},
    { enabled: true, isNextApi: true },
  );
  const {
    data: commentsData,
    refetch,
    isRefetching,
  } = useApi<Comment[]>(
    `/v1/tickets/${ticketId}/comments?visibility=public&commentType=comment`,
    {},
    { enabled: true, isNextApi: false },
  );

  const { data: notesData, refetch: notesRefetch } = useApi<Comment[]>(
    `/v1/tickets/${ticketId}/comments?visibility=public&commentType=note`,
    {},
    { enabled: true, isNextApi: false },
  );

  const { data: internalConversation, refetch: internalRefetch } = useApi<
    Comment[]
  >(
    `/v1/tickets/${ticketId}/comments?visibility=private&commentType=comment`,
    {},
    { enabled: true, isNextApi: false },
  );

  const groupedNotificationsArray = useInboxStore(
    (state) => state.groupedNotificationsArray,
  );
  const setGroupedNotificationsArray = useInboxStore(
    (state) => state.setGroupedNotificationsArray,
  );

  const notificationsRefetch = useCallback(() => {
    if (groupedNotificationsArray) {
      const updatedNotifications = groupedNotificationsArray.filter((group) => {
        // Keep notifications that don't match the deleted ticket
        return !(
          group.latestNotification.data.entityData.ticketId === ticketId &&
          ticketData?.deletedAt
        );
      });

      // Update the notifications array if it has changed
      if (updatedNotifications.length !== groupedNotificationsArray.length) {
        setGroupedNotificationsArray(updatedNotifications);
      }
    }
  }, [groupedNotificationsArray, ticketId, ticketData?.deletedAt]);

  // Update document title when ticket data is available
  useEffect(() => {
    if (ticketData) {
      dispatch({
        type: "SET_SHOW_WIDGET",
        payload: { showWidget: false },
      });
      // Store the original document title to restore it later
      const originalTitle = document.title;

      // Get the ticket identifier (e.g., CSU-253)
      const ticketIdentifier =
        ticketData.team?.identifier && ticketData.ticket_id
          ? `${ticketData.team.identifier}-${ticketData.ticket_id}`
          : ticketData.ticket_id || "";

      // Update the document title with ticket ID and title
      const newTitle = `${ticketIdentifier} ${ticketData.title}`;
      document.title = newTitle;

      // Set up an interval to ensure the title stays updated
      // This prevents other components from overriding our title
      const titleInterval = setInterval(() => {
        if (document.title !== newTitle) {
          document.title = newTitle;
        }
      }, 500);

      // Restore the original title when component unmounts
      return () => {
        clearInterval(titleInterval);
        // show the widget on unmount
        dispatch({
          type: "SET_SHOW_WIDGET",
          payload: { showWidget: true },
        });
        document.title = originalTitle;
      };
    }
  }, [ticketData]);

  useEffect(() => {
    if (!ticketData?.uid) return;

    let subscription: ReturnType<typeof supabase.channel> | undefined;

    const setupSubscription = async () => {
      // Set authentication for Supabase Realtime before creating the subscription
      const access_token = await getRealtimeToken();
      await supabase.realtime.setAuth(access_token);

      subscription = supabase
        .channel(`multitable`)
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "ticket",
            filter: `uid=eq.${ticketData.uid}`,
          },
          (payload) => {
            console.log("Ticket change received!", payload);
            if (
              payload.eventType === "INSERT" ||
              payload.eventType === "UPDATE"
            ) {
              ticketRefetch();
              notificationsRefetch();
            }
          },
        )
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "comments",
            filter: `ticket_id=eq.${ticketData.id}`,
          },
          (payload) => {
            console.log("Change received!", payload);

            if (
              payload.eventType === "INSERT" ||
              payload.eventType === "UPDATE"
            ) {
              refetch();
              notesRefetch();
              internalRefetch();
              setForceUpdateReply();
            }
          },
        )

        .subscribe();
    };

    // Execute the async function
    setupSubscription();

    return () => {
      if (subscription) subscription.unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ticketData]);

  // Escape key to close drawer
  const isModalOpen = useTicketDetailsDisplayOptions(
    (state) => state.disableShortcuts,
  );

  useHotkeys(
    "escape",
    () => {
      // Only close the drawer if no modal is open
      if (isOpen && !isModalOpen) {
        closeDrawer(router);
      }
    },
    {
      preventDefault: true,
    },
    [isOpen, closeDrawer, router, isModalOpen],
  );

  // Alt+D to toggle the right panel
  useHotkeys(
    "alt+d",
    () => {
      setIsRightSideOpen((prev) => !prev);
    },
    {
      preventDefault: true,
      enableOnFormTags: true,
    },
    [setIsRightSideOpen],
  );

  // Alt+F to toggle both left sidebar and right panel
  useHotkeys(
    "alt+f",
    () => {
      // Toggle sidebar
      toggleSidebar();
      // Toggle right panel
      setIsRightSideOpen((prev) => !prev);
    },
    {
      preventDefault: true,
      enableOnFormTags: true,
    },
    [toggleSidebar, setIsRightSideOpen],
  );

  // This function is now handled by the ResizeHandle component's toggle functionality

  if (!isOpen || !ticketData) return null;

  return (
    <HotkeysProvider initiallyActiveScopes={["ticket-details"]}>
      <div
        className={cn(
          isInboxPage
            ? "h-full bg-color-bg-subtle rounded-[4px]"
            : "fixed inset-0 bg-color-bg-subtle px-4 pb-4 rounded-[4px]",
          //  below widths are from ui/sidebar.tsx
          isSidebarOpen ? "left-[14rem]" : "left-12",
          "duration-200 transition-[left] ease-linear",
        )}
      >
        <div className="h-full flex flex-col">
          {!isInboxPage && (
            <TicketDetailsHeader
              ticketData={ticketData as SingleTicket}
              closeDrawer={closeDrawer}
            />
          )}
          <div className="flex-1 overflow-hidden">
            <PanelGroup direction="horizontal" className="flex gap-1">
              <TicketDetailsLeftside
                ticketData={ticketData as SingleTicket}
                allConversations={{
                  comments: commentsData,
                  notes: notesData,
                }}
                refetch={refetch}
                loading={isRefetching}
              />

              {/* Three dots resize handle with toggle functionality */}
              <ResizeHandle
                isRightSideOpen={isRightSideOpen}
                setIsRightSideOpen={setIsRightSideOpen}
              />

              {/* Right panel with smooth width and transform transition */}
              <div
                className={cn(
                  "transition-all duration-200 ease-linear overflow-hidden",
                  isRightSideOpen
                    ? "w-[35%] opacity-100 translate-x-0"
                    : "w-0 opacity-0 translate-x-10",
                )}
              >
                <TicketDetailsRightside
                  setIsRightSideOpen={setIsRightSideOpen}
                  ticketData={ticketData as SingleTicket}
                  internalConversation={internalConversation}
                  refetch={internalRefetch}
                />
              </div>
            </PanelGroup>

            {/* We've removed the expand button since that functionality is now in the drag handle */}
          </div>
        </div>
      </div>
    </HotkeysProvider>
  );
}
