import { useApiMutation } from "@/hooks/use-api-mutation";
import useOrderedKanbanColumns from "@/hooks/use-ordered-kanban-columns";
import { stopPropagation } from "@/lib/utils";
import {
  ARCHIVE_TICKET_BY_ID,
  DELETE_TICKET_BY_ID,
  UPDATE_TICKET_BY_ID,
} from "@/services/kanban";
import { useKanbanStore } from "@/store/kanbanStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { ThenaTicket } from "@/types/kanban";
import { getInitials, getLaneIcon, getUrgencyIcon } from "@/utils/kanban";
import { toUpper } from "lodash";
import {
  Archive,
  BarChart2,
  CheckIcon,
  Copy,
  Flag,
  Link,
  Smile,
  Trash2,
  TypeOutline,
  User,
  UsersRound,
} from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import Agent<PERSON>abel from "../common/AgentLabel";
import Avatar from "../common/Avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import {
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
} from "../ui/context-menu";

interface CardContextMenuProps {
  ticket: ThenaTicket;
}

const CardContextMenu: React.FC<CardContextMenuProps> = ({ ticket }) => {
  const statuses = useOrderedKanbanColumns();
  const priorities = useTicketMetaStore((state) => state.priorities);
  const teamMembers = useTicketMetaStore((state) => state.teamMembers);
  const ticketTypes = useTicketMetaStore((state) => state.ticketType);
  const groups = useTicketMetaStore((state) => state.groups);
  const accounts = useTicketMetaStore((state) => state.accounts);
  const sentiment = useTicketMetaStore((state) => state.sentiments);

  const [prevTicket, setPrevTicket] = useState<ThenaTicket[] | null>(null);

  // State for dialogs
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const currentStatus = statuses.find(
    (status) => status.id === ticket.status_id,
  );
  const currentPriority = priorities.find(
    (priority) => priority.id === ticket.priority_id,
  );

  const currentSentiment = sentiment.find(
    (sentiment) => sentiment.id === ticket.sentiment?.uid,
  );

  const currentType = ticketTypes.find((type) => type.id === ticket.type?.uid);
  const currentGroup = groups.find((group) => group.id === ticket.team?.uid);
  const currentAccount = accounts.find(
    (account) => account.uid === ticket.account?.uid,
  );

  const assignedAgent = teamMembers.find(
    (agent) => agent.id === ticket.assigned_agent_id,
  );

  const { mutate: updateStatus } = useApiMutation(
    UPDATE_TICKET_BY_ID(ticket.uid),
    {},
    "PATCH",
  );
  const { mutate: updateTicket } = useApiMutation(
    UPDATE_TICKET_BY_ID(ticket.uid),
    {},
    "PATCH",
  );
  const { mutate: updateAssignee, isError } = useApiMutation(
    UPDATE_TICKET_BY_ID(ticket.uid),
    {},
    "PATCH",
  );

  const { mutate: archiveTicket } = useApiMutation(
    ARCHIVE_TICKET_BY_ID(ticket.uid),
    {},
    "PATCH",
  );

  const { mutate: deleteTicket } = useApiMutation(
    DELETE_TICKET_BY_ID(ticket.uid),
    {},
    "DELETE",
  );

  const filteredStatuses = React.useMemo(() => {
    return statuses?.filter((status) => status.child_statuses?.length === 0);
  }, [statuses]);

  const assignItems = useMemo(() => {
    return [...(teamMembers ?? [])].sort((optionA) => {
      if (optionA.uid === assignedAgent?.uid) {
        return -1;
      }
      return 1;
    });
  }, [teamMembers, assignedAgent]);

  useEffect(() => {
    if (isError) {
      useTicketMetaStore.getState().setAllTickets(prevTicket);
    }
  }, [isError, prevTicket]);

  const handleCopyId = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(
      `${ticket.team?.identifier}-${ticket.ticket_id}`,
    );
  };

  const handleCopyLink = (e: React.MouseEvent) => {
    e.stopPropagation();
    const link = `${window.location.href}?ticketId=${ticket.uid}`;
    navigator.clipboard.writeText(link);
  };

  const handleArchive = (e: React.MouseEvent) => {
    e.stopPropagation();

    archiveTicket({}, {})
      .then(() => {
        setShowArchiveConfirm(false);
        useTicketMetaStore.getState().updateTicketArchived(ticket.uid, true);
        document.dispatchEvent(new KeyboardEvent("keydown", { key: "Escape" }));
      })
      .catch((error) => {
        console.error("Error archiving ticket:", error);
      });
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();

    deleteTicket({}, {})
      .then(() => {
        setShowDeleteConfirm(false);
        document.dispatchEvent(new KeyboardEvent("keydown", { key: "Escape" }));
      })
      .catch((error) => {
        console.error("Error deleting ticket:", error);
      });
  };

  const handleShowArchiveConfirm = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("Opening archive confirmation dialog");
    setShowArchiveConfirm(true);
  };

  const handleShowDeleteConfirm = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("Opening delete confirmation dialog");
    setShowDeleteConfirm(true);
  };

  const { mutate: unArchive } = useApiMutation(
    `/v1/tickets/${ticket.uid}/unarchive`,
    {},
    "PATCH",
  );

  return (
    <>
      <div className="p-1" onClick={stopPropagation}>
        <ContextMenuItem
          className="flex items-center justify-between py-1.5 px-3"
          onClick={handleCopyId}
        >
          <div className="flex items-center gap-2">
            <Copy size={16} className="text-muted-foreground" />
            <span>Copy ID</span>
          </div>
        </ContextMenuItem>
        <ContextMenuItem
          className="flex items-center justify-between py-1.5 px-3"
          onClick={handleCopyLink}
        >
          <div className="flex items-center gap-2">
            <Link size={16} className="text-muted-foreground" />
            <span>Copy link</span>
          </div>
        </ContextMenuItem>

        <ContextMenuSeparator />
        {/* Status Submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger
            className="flex items-center justify-between py-1.5 px-3"
            onClick={stopPropagation}
          >
            <div className="flex items-center gap-2">
              <BarChart2 size={16} className="text-muted-foreground" />
              <span>Status</span>
            </div>
            {/* <ContextMenuShortcut>S</ContextMenuShortcut> */}
          </ContextMenuSubTrigger>
          <ContextMenuSubContent
            className="min-w-[220px]"
            onClick={stopPropagation}
          >
            {filteredStatuses.map((option) => (
              <ContextMenuItem
                key={option.uid}
                onClick={(e) => {
                  e.stopPropagation();
                  if (option.uid === currentStatus?.uid) {
                    return;
                  }

                  updateStatus({ statusId: option.uid }, {}).then(() => {
                    useKanbanStore.getState().updateTicketStatus({
                      ticketId: ticket.uid,
                      newStatusId: option.uid,
                      ogStatusId: currentStatus?.uid,
                    });
                  });
                }}
                className="flex items-center justify-between py-1.5 px-3 cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  {getLaneIcon(
                    toUpper(option.parent_status?.name || option.display_name),
                  )}
                  <span>{option.display_name}</span>
                </div>
                {currentStatus?.uid === option?.uid && (
                  <CheckIcon strokeWidth={3} className="h-4 w-4" />
                )}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* Urgency Submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger
            className="flex items-center justify-between py-1.5 px-3"
            onClick={stopPropagation}
          >
            <div className="flex items-center gap-2">
              <Flag size={16} className="text-muted-foreground" />
              <span>Priority</span>
            </div>
          </ContextMenuSubTrigger>
          <ContextMenuSubContent
            className="min-w-[180px]"
            onClick={stopPropagation}
          >
            {priorities.map((option) => (
              <ContextMenuItem
                key={option.uid}
                onClick={(e) => {
                  e.stopPropagation();
                  if (option.uid === currentPriority?.uid) {
                    return;
                  }

                  updateTicket({
                    priorityId: option.uid,
                  });
                  useTicketMetaStore
                    .getState()
                    .updateTicketPriority(ticket.uid, option.uid);
                }}
                className="flex items-center justify-between py-1.5 px-3 cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  {getUrgencyIcon(toUpper(option?.name))}
                  <span>{option.name}</span>
                </div>
                {currentPriority?.uid === option?.uid && (
                  <CheckIcon strokeWidth={3} className="h-4 w-4" />
                )}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* Assignee Submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger
            className="flex items-center justify-between py-1.5 px-3"
            onClick={stopPropagation}
          >
            <div className="flex items-center gap-2">
              <User size={16} className="text-muted-foreground" />
              <span>Assignee</span>
            </div>
            {/* <ContextMenuShortcut>A</ContextMenuShortcut> */}
          </ContextMenuSubTrigger>
          <ContextMenuSubContent
            className="min-w-[220px] max-h-[300px] overflow-y-auto"
            onClick={stopPropagation}
          >
            <ContextMenuItem
              onClick={stopPropagation}
              onSelect={() => {
                updateAssignee({
                  assignedAgentId: "UNASSIGN",
                });

                setPrevTicket(useTicketMetaStore.getState().tickets);
                useTicketMetaStore
                  .getState()
                  .updateTicketAssignee(ticket.uid, null);
              }}
              className="flex items-center justify-between py-1.5 px-3 cursor-pointer"
            >
              <div className="flex items-center gap-2 ml-2 truncate">
                <div className="h-4 w-4 flex items-center justify-center border rounded bg-muted">
                  -
                </div>
                <span className="ml-2 truncate">No assignee</span>
              </div>
              {!assignedAgent?.uid && (
                <CheckIcon strokeWidth={3} className="h-4 w-4" />
              )}
            </ContextMenuItem>
            {assignItems.map((option) => (
              <ContextMenuItem
                key={option.uid}
                onClick={stopPropagation}
                onSelect={() => {
                  if (option.uid === assignedAgent?.uid) {
                    return;
                  }

                  updateAssignee({
                    assignedAgentId: option.uid,
                  });

                  setPrevTicket(useTicketMetaStore.getState().tickets);
                  useTicketMetaStore
                    .getState()
                    .updateTicketAssignee(ticket.uid, option.uid);
                }}
                className="flex items-center justify-between py-1.5 px-3 cursor-pointer"
              >
                <div className="flex items-center gap-2 ml-2 truncate">
                  <Avatar
                    src={option.avatar_url}
                    imgClassnames="w-4 h-4"
                    fallbackTextClassnames="w-4 h-4"
                    fallbackText={getInitials(option.name, 1)}
                  />
                  <span className="ml-2 truncate">{option.name}</span>
                  {option?.metadata?.isAgent && <AgentLabel />}
                </div>
                {assignedAgent?.uid === option.uid && (
                  <CheckIcon strokeWidth={3} className="h-4 w-4" />
                )}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* Sentiment submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger
            className="flex items-center justify-between py-1.5 px-3"
            onClick={stopPropagation}
          >
            <div className="flex items-center gap-2">
              <Smile size={16} className="text-muted-foreground" />
              <span>Sentiment</span>
            </div>
          </ContextMenuSubTrigger>
          <ContextMenuSubContent
            className="min-w-[180px]"
            onClick={stopPropagation}
          >
            {sentiment.map((option) => (
              <ContextMenuItem
                key={option.id}
                onClick={(e) => {
                  e.stopPropagation();
                  if (option.id === currentSentiment?.id) {
                    return;
                  }
                  updateTicket({
                    sentimentId: option.id,
                  });
                }}
                className="flex items-center justify-between py-1.5 px-3 cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  <span>{option.name}</span>
                </div>
                {currentSentiment?.id === option?.id && (
                  <CheckIcon strokeWidth={3} className="h-4 w-4" />
                )}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* Type submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger
            className="flex items-center justify-between py-1.5 px-3"
            onClick={stopPropagation}
          >
            <div className="flex items-center gap-2">
              <TypeOutline size={16} className="text-muted-foreground" />
              <span>Type</span>
            </div>
          </ContextMenuSubTrigger>
          <ContextMenuSubContent
            className="min-w-[180px]"
            onClick={stopPropagation}
          >
            {ticketTypes.map((option) => (
              <ContextMenuItem
                key={option.id}
                onClick={(e) => {
                  e.stopPropagation();
                  if (option.id === currentType?.id) {
                    return;
                  }

                  updateTicket({
                    typeId: option.id,
                  });
                }}
                className="flex items-center justify-between py-1.5 px-3 cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  <span>{option.name}</span>
                </div>
                {currentType?.id === option?.id && (
                  <CheckIcon strokeWidth={3} className="h-4 w-4" />
                )}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* group submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger
            className="flex items-center justify-between py-1.5 px-3"
            onClick={stopPropagation}
          >
            <div className="flex items-center gap-2">
              <UsersRound size={16} className="text-muted-foreground" />
              <span>Groups</span>
            </div>
          </ContextMenuSubTrigger>
          <ContextMenuSubContent
            className="min-w-[180px]"
            onClick={stopPropagation}
          >
            {groups?.length ? (
              groups.map((option) => (
                <ContextMenuItem
                  key={option.id}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (option.id === currentGroup?.id) {
                      return;
                    }

                    updateTicket({
                      subTeamId: option.id,
                    });
                  }}
                  className="flex items-center justify-between py-1.5 px-3 cursor-pointer"
                >
                  <div className="flex items-center gap-2">
                    <span>{option.name}</span>
                  </div>
                  {currentGroup?.id === option?.id && (
                    <CheckIcon strokeWidth={3} className="h-4 w-4" />
                  )}
                </ContextMenuItem>
              ))
            ) : (
              <div className="text-sm h-20 flex items-center justify-center text-muted-foreground">
                No group present
              </div>
            )}
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* Account submenu */}
        <ContextMenuSub>
          <ContextMenuSubTrigger
            className="flex items-center justify-between py-1.5 px-3"
            onClick={stopPropagation}
          >
            <div className="flex items-center gap-2">
              <User size={16} className="text-muted-foreground" />
              <span>Account</span>
            </div>
          </ContextMenuSubTrigger>
          <ContextMenuSubContent
            className="min-w-[180px]"
            onClick={stopPropagation}
          >
            {accounts?.map((option) => (
              <ContextMenuItem
                key={option.id}
                onClick={(e) => {
                  e.stopPropagation();
                  if (option.uid === currentAccount?.uid) {
                    return;
                  }

                  updateTicket({
                    accountId: option.uid,
                  });
                }}
                className="flex items-center justify-between py-1.5 px-3 cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  <span>{option.name}</span>
                </div>
                {currentAccount?.uid === option?.uid && (
                  <CheckIcon strokeWidth={3} className="h-4 w-4" />
                )}
              </ContextMenuItem>
            ))}
          </ContextMenuSubContent>
        </ContextMenuSub>

        {/* <ContextMenuSeparator />

        <ContextMenuItem
          className="flex items-center justify-between py-1.5 px-3"
          onClick={stopPropagation}
        >
          <div className="flex items-center gap-2">
            <Bell size={16} className="text-muted-foreground" />
            <span>Snooze</span>
          </div>
        </ContextMenuItem> */}

        <ContextMenuSeparator />

        {/* TODO: Implement -   Mark as related ticket , Move to team */}
        {/* <ContextMenuItem
          onSelect={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          <div className="flex items-center gap-2">
            <SquareEqual size={16} className="text-muted-foreground" />
            <span>Mark as related ticket</span>
          </div>
        </ContextMenuItem> */}
        {/* <ContextMenuItem>
          <div className="flex items-center gap-2">
            <PersonStanding size={16} className="text-muted-foreground" />
            <span>Move to team</span>
          </div>
        </ContextMenuItem> */}
        <ContextMenuSeparator />

        {ticket.archived_at ? (
          <ContextMenuItem
            className="flex items-center justify-between py-1.5 px-3"
            onSelect={() => {
              unArchive();
            }}
          >
            <div className="flex items-center gap-2">
              <Archive size={16} className="text-muted-foreground" />

              <span>Unarchive</span>
            </div>
          </ContextMenuItem>
        ) : (
          <ContextMenuItem
            className="flex items-center justify-between py-1.5 px-3"
            onSelect={(e) => {
              handleShowArchiveConfirm(e as unknown as React.MouseEvent);
            }}
          >
            <div className="flex items-center gap-2">
              <Archive size={16} className="text-muted-foreground" />

              <span>Archive</span>
            </div>
          </ContextMenuItem>
        )}

        <ContextMenuItem
          className="flex items-center justify-between py-1.5 px-3"
          onSelect={(e) => {
            console.log("Delete menu item selected");
            handleShowDeleteConfirm(e as unknown as React.MouseEvent);
          }}
        >
          <div className="flex items-center gap-2">
            <Trash2 size={16} className="text-muted-foreground" />
            <span>Delete</span>
          </div>
        </ContextMenuItem>
      </div>

      {/* Archive Confirmation Dialog */}
      <AlertDialog
        open={showArchiveConfirm}
        onOpenChange={setShowArchiveConfirm}
      >
        <AlertDialogContent onClick={stopPropagation}>
          <AlertDialogHeader className="space-y-0">
            <AlertDialogTitle>Archive ticket</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to archive this ticket?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={stopPropagation}
              className="border-none"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleArchive}
              className="bg-[hsl(var(--destructive))] "
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent onClick={stopPropagation}>
          <AlertDialogHeader className="space-y-0">
            <AlertDialogTitle>Delete ticket</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this ticket?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={stopPropagation}
              className="border-none"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-[hsl(var(--destructive))]"
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default CardContextMenu;
