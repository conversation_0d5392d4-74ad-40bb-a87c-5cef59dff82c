"use client";

import { ChevronsUpDown, Plus, User2 } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

import { BASE_URL_APP } from "@/config/constant";
import { SWITCH_WORKSPACE } from "@/services/sidebar";
import { useChatStore } from "@/store/chat-store";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { Organization } from "@/types/global";
import { getInitials } from "@/utils/kanban";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { setOrgDetails } from "../../utils/browserUtils";

export function TeamSwitcher({
  teams,
  defaultOrg,
}: {
  teams: Organization[];
  defaultOrg: Organization;
}) {
  const { isMobile, state } = useSidebar();
  const isCollapsed = state === "collapsed";
  const [activeTeam, setActiveTeam] = useState(defaultOrg);
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);
  const allUsers = useGlobalConfigPersistStore(
    (state) => state.currentUser?.allUserIds,
  );
  const setAllTeams = useTicketMetaStore((state) => state.setAllTeams);
  const router = useRouter();

  // Get chat store reset function
  const resetChatState = useChatStore((state) => state.resetChatState);

  const handleWorkSpaceChange = async (orgId: string, orgUid: string) => {
    try {
      // Disable any navigation listeners temporarily
      const unloadListener = (e: BeforeUnloadEvent) => {
        e.preventDefault();
        return false;
      };
      window.addEventListener("beforeunload", unloadListener);

      const response = await fetch(`${BASE_URL_APP}${SWITCH_WORKSPACE}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ orgId, orgUid }),
      });

      if (response.ok) {
        // Reset chat state before organization change
        resetChatState();

        // Clear all related caches from localStorage to ensure fresh data on reload
        sessionStorage.removeItem("chat-store");
        sessionStorage.removeItem("agents-cache");
        sessionStorage.removeItem("conversations-cache");

        // Then update the organization
        dispatch({ type: "SET_CURRENT_ORG", payload: { currentOrgId: orgId } });

        // Get updated search settings for new org
        try {
          const searchSettingsResponse = await fetch(
            `/api/search/settings?orgId=${orgId}`,
          );
          if (searchSettingsResponse.ok) {
            const searchSettings = await searchSettingsResponse.json();
            dispatch({
              type: "SET_SEARCH_KEYS",
              payload: {
                ticketsAndComments: searchSettings.ticketsAndComments,
                accounts: searchSettings.accounts,
                teams: searchSettings.teams,
              },
            });
          }
        } catch (error) {
          console.error("Error fetching search settings:", error);
        }

        setAllTeams([]);
        const currentUser = allUsers?.find((user) => user.orgId == orgId);
        setOrgDetails(orgId, orgUid);
        dispatch({
          type: "SET_CURRENT_USER_UID",
          payload: {
            uid: currentUser?.uid,
            avatarUrl: currentUser?.avatarUrl,
            name: currentUser?.name,
          },
        });
        dispatch({
          type: "SET_CURRENT_ORG",
          payload: {
            currentOrgId: orgId,
          },
        });
        // Remove the listener and force navigation
        window.removeEventListener("beforeunload", unloadListener);
        window.location.replace("/dashboard");
        return;
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (defaultOrg) {
      setActiveTeam(defaultOrg);
    }
  }, [defaultOrg]);
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="w-full data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              {activeTeam?.logoUrl ? (
                <div className="size-8">
                  <Image
                    src={activeTeam.logoUrl}
                    alt={activeTeam.name}
                    width={32}
                    height={32}
                    unoptimized
                    className="object-cover size-full rounded-sm"
                  />
                </div>
              ) : (
                <div className="size-8 rounded-sm aspect-square bg-[var(--color-bg-inverse)] text-background flex items-center justify-center font-medium">
                  {getInitials(activeTeam?.name)}
                </div>
              )}

              {!isCollapsed && (
                <>
                  <div className="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden">
                    <span className="truncate font-semibold">
                      {activeTeam?.name || ""}
                    </span>
                  </div>
                  <span className="group-data-[collapsible=icon]:hidden">
                    <ChevronsUpDown className="ml-auto size-4 opacity-60" />
                  </span>
                </>
              )}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-sm"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuItem
              onClick={() => {
                router.push("/organization/settings/admin/general");
              }}
            >
              <span>Organization settings</span>
            </DropdownMenuItem>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <span>Switch workspace</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent
                  sideOffset={6}
                  className="relative flex flex-col max-h-[350px] min-w-56 w-full"
                >
                  <div className="overflow-y-auto flex-1 max-h-[300px] pb-10">
                    {teams.map((team, index) => (
                      <DropdownMenuItem
                        key={team.name + index}
                        onClick={() => {
                          setActiveTeam(team);
                          handleWorkSpaceChange(team.id, team.orgId);
                        }}
                        className="gap-2 p-2"
                      >
                        <div className="flex size-6 items-center justify-center rounded-sm border">
                          <User2 className="size-4 shrink-0" />
                        </div>
                        <span className="truncate">{team.name}</span>
                        <DropdownMenuShortcut>
                          ⌘{index + 1}
                        </DropdownMenuShortcut>
                      </DropdownMenuItem>
                    ))}
                  </div>
                  <div className="absolute bottom-0 w-full bg-background pt-1">
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => {
                        router.push("/org/create");
                      }}
                    >
                      <Plus />
                      <span>Add an organization</span>
                    </DropdownMenuItem>
                  </div>
                  <div className="absolute bottom-10 w-full h-4 bg-gradient-to-t from-background to-transparent pointer-events-none"></div>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
