"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Check,
  ChevronsUpDown,
  Keyboard,
  LogOut,
  Paintbrush,
  Settings,
  Settings2,
  Shield,
} from "lucide-react";

import { signout } from "@/app/actions/auth";
import { useTheme } from "@/components/theme-provider";

import { clearAllCookies } from "@/app/actions/clearCookies";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useInboxPersistStore } from "@/store/inbox-persist";
import { useInboxStore } from "@/store/inbox-store";
import { useSignupPersistStore } from "@/store/signupPersistStore";
import { useRouter } from "next/navigation";
import Avatar from "../common/Avatar";
import { ThemeIndicator } from "../theme-indicator";

export function NavUser({
  user,
}: {
  user: {
    name: string;
    email: string;
    avatar: string;
  };
}) {
  const { isMobile, state } = useSidebar();
  const isCollapsed = state === "collapsed";
  const { theme, setTheme } = useTheme();
  const router = useRouter();

  const handleSignout = async () => {
    try {
      await clearAllCookies(window.location.hostname);
      await signout();
      useGlobalConfigPersistStore.dispatch({ type: "RESET" });
      useInboxPersistStore.dispatch({ type: "RESET" });
      useSignupPersistStore.getState().resetStore();
      useInboxStore.getState().clearAll();
      window.localStorage.clear();
      window.sessionStorage.clear();
      router.push("/login");
    } catch (error) {
      console.error("Signout error:", error);
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="w-full data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="size-8">
                <Avatar
                  src={user.avatar}
                  fallbackText={user.name}
                  imgClassnames="h-8 w-8"
                />
              </div>

              {!isCollapsed && (
                <>
                  <div className="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden">
                    <span className="truncate font-semibold">{user.name}</span>
                  </div>
                  <span className="group-data-[collapsible=icon]:hidden">
                    <ChevronsUpDown className="ml-auto size-4 opacity-60" />
                  </span>
                </>
              )}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-sm"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            {!isCollapsed && (
              <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                  <Avatar
                    src={user.avatar}
                    fallbackText={user.name}
                    imgClassnames="h-8 w-8"
                  />
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{user.name}</span>
                    <span className="truncate text-xs text-muted-foreground">
                      {user.email}
                    </span>
                  </div>
                </div>
              </DropdownMenuLabel>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                onClick={() =>
                  router.push("/organization/settings/personal/profile")
                }
              >
                <Settings className="size-4 !mr-0" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  router.push("/organization/settings/personal/preferences")
                }
              >
                <Settings2 className="size-4 !mr-0" />
                Preferences
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  router.push("/organization/settings/personal/notifications")
                }
              >
                <Bell className="size-4 !mr-0" />
                Notifications
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  router.push("/organization/settings/personal/security-access")
                }
              >
                <Shield className="size-4 !mr-0" />
                Security and access
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push("/guide")}>
                <BookOpen className="size-4 !mr-0" />
                Guide
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push("/shortcuts")}>
                <Keyboard className="size-4 !mr-0" />
                Keyboard shortcuts
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <Paintbrush className="size-4" />
                  <span>Theme</span>
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent className="min-w-[180px]">
                    <DropdownMenuItem onClick={() => setTheme("mist")}>
                      <ThemeIndicator theme="mist" />
                      <span>Mist</span>
                      {theme === "mist" && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme("breeze")}>
                      <ThemeIndicator theme="breeze" />
                      <span>Breeze</span>
                      {theme === "breeze" && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme("lumen")}>
                      <ThemeIndicator theme="lumen" />
                      <span>Lumen</span>
                      {theme === "lumen" && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme("carbon")}>
                      <ThemeIndicator theme="carbon" />
                      <span>Carbon</span>
                      {theme === "carbon" && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme("midnight")}>
                      <ThemeIndicator theme="midnight" />
                      <span>Midnight</span>
                      {theme === "midnight" && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setTheme("nebula")}>
                      <ThemeIndicator theme="nebula" />
                      <span>Nebula</span>
                      {theme === "nebula" && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignout}>
              <LogOut className="size-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
