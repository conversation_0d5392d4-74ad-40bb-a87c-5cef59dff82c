"use client";

import { <PERSON><PERSON><PERSON>, Hero<PERSON>conName } from "@/components/hero-icon";
import {
  TransformedTeam,
  transformTeamsData,
} from "@/components/sidebar/utils";
import { CreateTicketModal } from "@/components/ticket/create-ticket-modal";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar";
import { getAgents } from "@/lib/api/agents";
import { getAllSidebarPreferences } from "@/lib/user-preferences";
import { cn } from "@/lib/utils";
import { useChatStore } from "@/store/chat-store";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useInboxStore } from "@/store/inbox-store";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { Agent } from "@/types/agent";
import * as HeroIcons from "@heroicons/react/24/solid";
import { useQuery } from "@tanstack/react-query";
import { AnimatePresence, motion } from "framer-motion";
import { useFlags } from "launchdarkly-react-client-sdk";
import { ChevronLeft } from "lucide-react";
import { useParams, usePathname, useRouter } from "next/navigation";
import * as React from "react";
import { useCallback, useEffect, useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import styled from "styled-components";
import { getOrgDetails } from "../../utils/browserUtils";
import { LucideIcon, LucideIconType } from "../lucide-icon";
import TooltipWrapper from "../tooltip-wrapper";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { NavList } from "./nav-list";
import { NavTeams } from "./nav-teams";
import { NavUser } from "./nav-user";
import { TeamSwitcher } from "./team-switcher";

const BadgeWrapper = styled(Badge)`
  height: 20px;
  padding: 4px 6px;
  gap: var(--spacing-2xs);
  border-radius: 4px;
  border-width: 1px;
  font-weight: 500;
`;

type SettingsData = {
  name: string;
  sublist: {
    name: string;
    url: string;
    icon: LucideIconType;
    sublist?: {
      name: string;
      url: string;
      icon: LucideIconType;
    }[];
  }[];
};

const workspaceSettings: SettingsData[] = [
  {
    name: "Administration",
    sublist: [
      {
        name: "General",
        url: "/organization/settings/admin/general",
        icon: "Settings",
      },
      {
        name: "Members",
        url: "/organization/settings/admin/members",
        icon: "Users",
      },
      {
        name: "Teams",
        url: "/organization/settings/admin/teams",
        icon: "Network",
      },
      {
        name: "Roles",
        url: "/organization/settings/admin/roles",
        icon: "Shield",
      },
    ],
  },
  {
    name: "Agent studio",
    sublist: [
      {
        name: "Agents",
        url: "/organization/settings/agent-studio/agents",
        icon: "Bot",
      },
      {
        name: "Marketplace",
        url: "/organization/settings/agent-studio/marketplace",
        icon: "Store",
      },
      {
        name: "Logs",
        url: "/organization/settings/agent-studio/logs",
        icon: "ScrollText",
      },
      {
        name: "Usage",
        url: "/organization/settings/agent-studio/usage",
        icon: "CreditCard",
      },
    ],
  },
  {
    name: "Workspace",
    sublist: [
      {
        name: "Workflows",
        url: "/organization/settings/workflows",
        icon: "Workflow",
      },
      {
        name: "Sources",
        url: "/organization/settings/sources",
        icon: "Database",
      },
      {
        name: "Apps",
        url: "/organization/settings/apps-studio",
        icon: "AppWindow",
      },
    ],
  },
  {
    name: "Accounts",
    sublist: [
      {
        name: "Account fields",
        url: "/organization/settings/accounts/account-fields",
        icon: "Database",
      },
      {
        name: "Contact fields",
        url: "/organization/settings/accounts/contact-fields",
        icon: "Users",
      },
      {
        name: "Attributes",
        url: "/organization/settings/accounts/attributes",
        icon: "Tag",
      },
    ],
  },
  {
    name: "Personal",
    sublist: [
      {
        name: "Profile",
        url: "/organization/settings/personal/profile",
        icon: "Settings",
      },
      {
        name: "Preferences",
        url: "/organization/settings/personal/preferences",
        icon: "Settings2",
      },
      {
        name: "Notifications",
        url: "/organization/settings/personal/notifications",
        icon: "Bell",
      },
      {
        name: "Security and access",
        url: "/organization/settings/personal/security-access",
        icon: "Shield",
      },
    ],
  },
];

export function DashboardSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname();
  const router = useRouter();

  // State for current view
  const [currentView, setCurrentView] = useState(() => {
    if (pathname?.startsWith("/organization/settings"))
      return "organization-settings";
    if (pathname?.includes("/settings")) return "settings";
    return "main";
  });

  const teamsMeta = useTicketMetaStore((state) => state.teams);
  const [transformedTeams, setTransformedTeams] = useState<TransformedTeam[]>(
    [],
  );
  const [teamAgentsMap, setTeamAgentsMap] = useState<Record<string, Agent[]>>(
    {},
  );
  const { teamId } = useParams() as { teamId?: string };
  const allOrgs = useGlobalConfigPersistStore((state) => state.orgs);
  // const orgId = useGlobalConfigPersistStore((state) => state.currentOrgId);
  const { orgId } = getOrgDetails();
  const defaultOrg = allOrgs.find((org) => org.id === orgId);
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const isChatOpen = useChatStore((state) => state).isOpen;
  const { open, setOpen } = useSidebar();
  const [isCreateTicketModalOpen, setIsCreateTicketModalOpen] = useState(false);
  const isInboxPage = pathname.startsWith("/inbox");
  const {
    engagementSidebar,
    draftsSidebar,
    aiAgentLog,
    aiAgentUsage,
    adminRoles,
    //autoResponder,
  } = useFlags();

  const filteredWorkspaceSettings = workspaceSettings.map((section) => {
    if (section.name === "Agent studio") {
      return {
        ...section,
        sublist: section.sublist.filter((item) => {
          if (item.name === "Logs") return aiAgentLog;
          if (item.name === "Usage") return aiAgentUsage;
          return true;
        }),
      };
    }
    if (section.name === "Administration") {
      return {
        ...section,
        sublist: section.sublist.filter((item) => {
          if (item.name === "Roles") return adminRoles;
          return true;
        }),
      };
    }
    return section;
  });

  const CountBadge = ({ count }: { count: number }) => {
    return (
      <BadgeWrapper
        variant="outline"
        className="w-max ml-auto flex items-center justify-center shadow-sm rounded-[6px] px-2 bg-[var(--badge-bg)] border-[var(--badge-border)]"
      >
        {count >= 100 ? "99+" : count}
      </BadgeWrapper>
    );
  };
  useEffect(() => {
    if (isChatOpen) {
      setOpen(false);
      return;
    }
    setOpen(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isChatOpen]);

  useHotkeys(
    "alt+n",
    (event) => {
      event.preventDefault();
      setIsCreateTicketModalOpen(true);
    },
    { enableOnFormTags: true },
  );

  const [previousView, setPreviousView] = useState<string | null>(null);
  const [isBack, setIsBack] = useState(false);
  const [_teamsData, setTeams] = useState([]);
  const [_isLoadingTeams, setIsLoading] = useState(false);
  const [lastSavedTeamId, setLastSavedTeamId] = useState<string | null>(null);

  // Load the last clicked team from user preferences when the component mounts
  useEffect(() => {
    const loadLastClickedTeam = async () => {
      if (currentUser?.id && orgId) {
        try {
          const { lastTeam } = await getAllSidebarPreferences(
            currentUser.id,
            orgId,
          );

          if (lastTeam) {
            setLastSavedTeamId(lastTeam);
          }
        } catch (error) {
          console.error("Error loading last clicked team:", error);
        }
      }
    };

    loadLastClickedTeam();
  }, [currentUser?.id, orgId]);

  // Listen for custom events when last team changes
  const lastTeamListener = useCallback(
    (event: CustomEvent<{ teamId: string }>) => {
      const newTeamId = event.detail?.teamId;
      if (newTeamId) {
        setLastSavedTeamId(newTeamId);
      }
    },
    [],
  );

  useEffect(() => {
    window.addEventListener(
      "lastTeamChanged",
      lastTeamListener as EventListener,
    );
    return () => {
      window.removeEventListener(
        "lastTeamChanged",
        lastTeamListener as EventListener,
      );
    };
  }, [lastTeamListener]);

  useEffect(() => {
    const fetchTeamData = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/teams/${teamId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch team data");
        }
        const data = await response.json();
        setTeams(data);
      } catch (error) {
        console.error("Error fetching team data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (teamId) {
      fetchTeamData();
    }
  }, [teamId]);

  // Fetch all agents with a single API call
  const { data: agentsData } = useQuery({
    queryKey: ["agents"],
    queryFn: getAgents,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  useEffect(() => {
    if (teamsMeta) {
      // Create a map of team ID to agents
      const teamAgentsMap: Record<string, Agent[]> = {};

      if (agentsData && agentsData.length > 0) {
        // Map agents to their teams
        agentsData.forEach((agent) => {
          if (agent.teams && agent.teams.length > 0) {
            agent.teams.forEach((teamId) => {
              if (!teamAgentsMap[teamId]) {
                teamAgentsMap[teamId] = [];
              }
              teamAgentsMap[teamId].push(agent);
            });
          }
        });
      }

      // Transform teams with agents
      const transformedTeams = transformTeamsData(teamsMeta, teamAgentsMap);
      setTransformedTeams(transformedTeams);

      // Store the teamAgentsMap for use in NavTeams
      setTeamAgentsMap(teamAgentsMap);
    }
  }, [teamsMeta, agentsData]);

  useEffect(() => {
    if (!isBack) {
      if (pathname?.startsWith("/organization/settings")) {
        setCurrentView("organization-settings");
      } else if (pathname?.includes("/settings")) {
        setCurrentView("settings");
      } else {
        setCurrentView("main");
      }
    }
    setIsBack(false);
  }, [pathname, isBack]);

  const [_userData, _setUserData] = useState({
    name: "",
    email: "",
    avatar: "",
  });
  const handleBack = () => {
    setIsBack(true);
    if (previousView) {
      setCurrentView(previousView);
      setPreviousView(null);
      if (previousView === "main") {
        router.push(`/dashboard/${teamId}`);
      }
      return;
    }

    // If we have a current teamId in the URL, use that
    if (teamId) {
      router.push(`/dashboard/${teamId}`);
      return;
    }

    // If we have a last saved team ID from user preferences, use that
    if (lastSavedTeamId) {
      router.push(`/dashboard/${lastSavedTeamId}`);
      return;
    }

    // Fallback to the first team in the list
    const teams = useTicketMetaStore.getState().teams;
    if (!teams || teams.length === 0) {
      // If no teams are available, redirect to a safe fallback route
      router.push("/dashboard");
      return;
    }
    router.push(`/dashboard/${teams[0].uid}`);
  };

  const sidebarData: {
    user: { name: string; email: string; avatar: string };
    projects: {
      name: string;
      sublist: { name: string; url: string; icon: LucideIconType }[];
    }[];
    workspace: { name: string; logo: LucideIconType; plan: string }[];
    teams: {
      title: string;
      url: string;
      icon: LucideIconType;
      isActive?: boolean;
      items: { title: string; url: string }[];
    }[];
  } = {
    user: {
      name: currentUser.name || currentUser.email?.split("@")[0] || "",
      email: currentUser.email || "",
      avatar: currentUser.avatarUrl || "",
    },
    projects: [
      engagementSidebar && {
        name: "Engagement",
        sublist: [
          {
            name: "Broadcasts",
            url: "/organization/broadcast",
            icon: "Megaphone" as LucideIconType,
          },
          {
            name: "Audiences",
            url: "/organization/audiences",
            icon: "Crosshair" as LucideIconType,
          },
        ],
      },
      {
        name: "Customers",
        sublist: [
          {
            name: "Accounts",
            url: "/accounts",
            icon: "Building2" as LucideIconType,
          },
          {
            name: "Contacts",
            url: "/accounts/contacts",
            icon: "Contact" as LucideIconType,
          },
          {
            name: "Insights",
            url: "/accounts/insights",
            icon: "ChartLine" as LucideIconType,
          },
          {
            name: "Views",
            url: "/accounts/views",
            icon: "Layers" as LucideIconType,
          },
        ],
      },
      {
        name: "Knowledge base",
        sublist: [
          {
            name: "Help centers",
            url: "/helpcenter/all",
            icon: "LayoutGrid" as LucideIconType,
          },
          {
            name: "Articles",
            url: "/helpcenter/articles",
            icon: "FileText" as LucideIconType,
          },
          {
            name: "Tags",
            url: "/helpcenter/tags",
            icon: "Tags" as LucideIconType,
          },
        ],
      },
    ].filter(Boolean),
    workspace: [
      {
        name: "Acme Inc",
        logo: "GalleryVerticalEnd",
        plan: "Enterprise",
      },
      {
        name: "Acme Corp.",
        logo: "AudioWaveform",
        plan: "Startup",
      },
      {
        name: "Evil Corp.",
        logo: "Command",
        plan: "Free",
      },
    ],
    teams: [
      {
        title: "Customer support",
        url: "#",
        icon: "SquareTerminal",
        isActive: true,
        items: [
          {
            title: "Tickets",
            url: "/kanban",
          },
          {
            title: "Links",
            url: "#",
          },
          {
            title: "Insights",
            url: "#",
          },
        ],
      },
      {
        title: "Engineering",
        url: "#",
        icon: "Bot",
        items: [
          {
            title: "Genesis",
            url: "#",
          },
          {
            title: "Explorer",
            url: "#",
          },
          {
            title: "Quantum",
            url: "#",
          },
        ],
      },
      {
        title: "Documentation",
        url: "#",
        icon: "BookOpen",
        items: [
          {
            title: "Introduction",
            url: "#",
          },
          {
            title: "Get Started",
            url: "#",
          },
          {
            title: "Tutorials",
            url: "#",
          },
          {
            title: "Changelog",
            url: "#",
          },
        ],
      },
      {
        title: "Settings",
        url: "#",
        icon: "Settings2",
        items: [
          {
            title: "General",
            url: "#",
          },
          {
            title: "Team",
            url: "#",
          },
          {
            title: "Billing",
            url: "#",
          },
          {
            title: "Limits",
            url: "#",
          },
        ],
      },
    ],
  };
  const settingsData: SettingsData = {
    name: "Settings",
    sublist: [
      {
        name: "General",
        url: `/dashboard/${teamId}/settings/general`,
        icon: "Settings",
      },
      {
        name: "Members",
        url: `/dashboard/${teamId}/settings/members`,
        icon: "Users",
      },
      {
        name: "Groups",
        url: `/dashboard/${teamId}/settings/groups`,
        icon: "PersonStanding",
      },
      {
        name: "Statuses",
        url: `/dashboard/${teamId}/settings/statuses`,
        icon: "Square",
      },
      {
        name: "Tags",
        url: `/dashboard/${teamId}/settings/tags`,
        icon: "Tag",
      },
      {
        name: "Ticket fields",
        url: `/dashboard/${teamId}/settings/ticket-fields`,
        icon: "LayoutList",
      },
      {
        name: "Forms",
        url: `/dashboard/${teamId}/settings/forms`,
        icon: "Library",
      },
      {
        name: "Routing",
        url: `/dashboard/${teamId}/settings/routing`,
        icon: "Rotate3d",
      },
      {
        name: "Working hours",
        url: `/dashboard/${teamId}/settings/working-hours`,
        icon: "Clock",
      },
      {
        name: "Auto-responder",
        url: `/dashboard/${teamId}/settings/auto-responder`,
        icon: "MessageSquare",
      },
      {
        name: "SLAs",
        url: `/dashboard/${teamId}/settings/sla`,
        icon: "Hourglass",
      },
      {
        name: "CSAT",
        url: `/dashboard/${teamId}/settings/csat`,
        icon: "Star",
      },
      {
        name: "Emoji actions",
        url: `/dashboard/${teamId}/settings/emoji-actions`,
        icon: "Smile",
      },
      {
        name: "Workflows",
        url: `/dashboard/${teamId}/settings/workflows`,
        icon: "Workflow",
      },
    ],
  };

  const sourcesData: SettingsData = {
    name: "Sources",
    sublist: [
      {
        name: "Email",
        url: `/dashboard/${teamId}/settings/sources/email`,
        icon: "Mail",
      },
      {
        name: "Web chat",
        url: `/dashboard/${teamId}/settings/sources/web-chat`,
        icon: "MessageCircle",
      },
      {
        name: "Slack",
        url: `/dashboard/${teamId}/settings/sources/slack`,
        icon: "Slack",
      },
      {
        name: "MS Teams",
        url: `/dashboard/${teamId}/settings/sources/ms-teams`,
        icon: "MessageSquare",
      },
    ],
  };
  const unreadCount = useInboxStore((state) => state.unreadCount);

  // Fetch team data when in settings view
  const { data: _data, isLoading: _isLoading } = useQuery({
    queryKey: ["team", teamId],
    queryFn: async () => {
      if (!teamId) return null;
      try {
        const response = await fetch(`/api/teams/${teamId}`);
        if (!response.ok) {
          throw new Error("Failed to fetch team");
        }
        const data = await response.json();
        return data;
      } catch (error) {
        console.error("Error fetching team:", error);
        return null;
      }
    },
    enabled: !!teamId && currentView === "settings",
  });

  // Extract team data from nested response structure
  const teamData = _data?.data?.data;

  return (
    <Sidebar collapsible="icon" {...props} className="bg-color-bg-subtle">
      <SidebarHeader>
        {allOrgs?.length > 0 && (
          <>
            <TeamSwitcher teams={allOrgs} defaultOrg={defaultOrg} />
            {currentView !== "main" && (
              <SidebarMenuButton
                tooltip="Back to Dashboard"
                onClick={handleBack}
                className="flex items-center gap-2 py-2 text-sm rounded-sm"
              >
                <ChevronLeft size={16} />
                {currentView === "organization-settings" ? (
                  <span className="text-sm font-regular group-data-[collapsible=icon]:hidden">
                    Back
                  </span>
                ) : (
                  teamId &&
                  teamData && (
                    <div className="flex items-center gap-2 group-data-[collapsible=icon]:hidden">
                      <div
                        className="flex h-5 w-5 items-center justify-center rounded-sm"
                        style={{
                          backgroundColor:
                            `${teamData?.color
                              ?.replace("rgb", "rgba")
                              .replace(")", ", 0.1)")}` ||
                            "rgba(79, 70, 229, 0.1)",
                        }}
                      >
                        {teamData.icon && teamData.icon in HeroIcons ? (
                          <HeroIcon
                            name={teamData.icon as HeroIconName}
                            className="h-3.5 w-3.5"
                            color={teamData?.color || "#4f46e5"}
                          />
                        ) : teamData.icon ? (
                          <span style={{ color: teamData?.color || "#4f46e5" }}>
                            {teamData?.name?.charAt(0).toUpperCase() || "T"}
                          </span>
                        ) : (
                          <HeroIcon
                            name="RocketLaunchIcon"
                            className="h-3.5 w-3.5"
                            color={teamData?.color || "#4f46e5"}
                          />
                        )}
                      </div>
                      <TooltipWrapper tooltipContent={teamData?.name}>
                        <div className="max-w-32 truncate">
                          <span className="text-sm font-regular">
                            {teamData?.name || "Team"}
                          </span>
                        </div>
                      </TooltipWrapper>
                    </div>
                  )
                )}
              </SidebarMenuButton>
            )}
          </>
        )}
      </SidebarHeader>
      <SidebarContent>
        <AnimatePresence mode="wait">
          {currentView === "main" && (
            <>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
              >
                <SidebarGroup>
                  <div className="flex gap-2 group-data-[collapsible=icon]:flex-col">
                    <SidebarMenuButton
                      className="flex-1 bg-background rounded-sm border border-input h-8 gap-2"
                      onClick={() => router.push("/search")}
                    >
                      <LucideIcon
                        name="Search"
                        className="size-4 shrink-0 text-color-icon"
                      />
                      <span className="group-data-[collapsible=icon]:hidden text-color-text-placeholder">
                        Search
                      </span>
                    </SidebarMenuButton>
                    <TooltipWrapper asChild tooltipContent="Create new ticket">
                      <SidebarMenuButton
                        className="h-8 w-8 shrink-0 rounded-sm p-0 inline-flex items-center justify-center bg-background hover:bg-accent/5 transition-colors border border-input shadow-none"
                        asChild
                      >
                        <Button
                          variant="ghost"
                          className="shadow-none"
                          onClick={() => setIsCreateTicketModalOpen(true)}
                        >
                          <LucideIcon
                            name="Pen"
                            className="size-4 text-color-icon"
                          />
                        </Button>
                      </SidebarMenuButton>
                    </TooltipWrapper>
                  </div>

                  <SidebarMenu className="!gap-0">
                    <SidebarMenuItem
                      className={cn("mt-3 w-full", {
                        "border border-border": isInboxPage,
                        "rounded-[4px]": isInboxPage,
                      })}
                    >
                      <SidebarMenuButton
                        tooltip="Inbox"
                        className="group/nav-item w-full"
                        onClick={() => router.push("/inbox")}
                        isActive={isInboxPage}
                      >
                        <LucideIcon
                          name="Inbox"
                          className="text-color-icon-muted transition-all duration-200 ease-out group-hover/nav-item:scale-110 group-hover/nav-item:rotate-3 group-hover/nav-item:text-color-icon"
                          size={16}
                        />
                        {open && (
                          <div className="flex items-center gap-1 justify-between w-full">
                            <span className="text-sm font-normal transition-opacity group-data-[collapsible=icon]:hidden group-data-[collapsible=icon]:w-0 group-data-[collapsible=icon]:opacity-0">
                              Inbox
                            </span>
                            <CountBadge count={unreadCount} />
                          </div>
                        )}
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    {draftsSidebar && (
                      <SidebarMenuItem className="w-full">
                        <SidebarMenuButton
                          tooltip="Drafts"
                          className="group/nav-item w-full"
                        >
                          <LucideIcon
                            name="FilePen"
                            className="text-color-icon-muted transition-all duration-200 ease-out group-hover/nav-item:scale-110 group-hover/nav-item:rotate-3 group-hover/nav-item:text-color-icon"
                            size={16}
                          />
                          <span className="text-sm font-normal transition-opacity group-data-[collapsible=icon]:hidden group-data-[collapsible=icon]:w-0 group-data-[collapsible=icon]:opacity-0">
                            Drafts
                          </span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    )}
                  </SidebarMenu>
                </SidebarGroup>
                <NavTeams
                  items={transformedTeams}
                  isCollapsed={!open}
                  title="Your teams"
                  handleNavigation={setCurrentView}
                  teamAgentsMap={teamAgentsMap}
                />
              </motion.div>
              {sidebarData.projects.map((project, _id) => {
                return (
                  <motion.div
                    key={_id + project.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{
                      duration: 0.3,
                      ease: "easeOut",
                      delay: (_id + 1) * 0.1,
                    }}
                  >
                    <NavList projects={project.sublist} title={project.name} />
                  </motion.div>
                );
              })}
            </>
          )}
          {currentView === "settings" && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="space-y-2"
            >
              <NavList
                projects={settingsData.sublist}
                title={settingsData.name}
              />
              <NavList
                projects={sourcesData.sublist}
                title={sourcesData.name}
              />
            </motion.div>
          )}
          {currentView === "organization-settings" && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="space-y-2"
            >
              <>
                {filteredWorkspaceSettings.map((section) => (
                  <NavList
                    key={section.name}
                    projects={section.sublist}
                    title={section.name}
                  />
                ))}
                <NavTeams
                  items={transformedTeams}
                  isCollapsed={!open}
                  title="Your teams"
                  handleNavigation={setCurrentView}
                  showDetails={false}
                  teamAgentsMap={teamAgentsMap}
                />
              </>
            </motion.div>
          )}
        </AnimatePresence>
      </SidebarContent>
      <SidebarFooter className="px-2">
        <NavUser user={sidebarData.user} />
      </SidebarFooter>
      <CreateTicketModal
        isOpen={isCreateTicketModalOpen}
        onOpenChange={setIsCreateTicketModalOpen}
      />
      <SidebarRail />
    </Sidebar>
  );
}
