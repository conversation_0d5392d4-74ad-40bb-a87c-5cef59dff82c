"use client";
import {
  Ch<PERSON>ronR<PERSON>,
  GripVertical,
  Link as LinkI<PERSON>,
  Lock,
  MoreHorizontal,
  <PERSON>n,
  <PERSON>n<PERSON><PERSON>,
  Plus,
  <PERSON>ting<PERSON>,
} from "lucide-react";

import { HeroIcon } from "@/components/hero-icon";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { BASE_URL_APP } from "@/config/constant";
import {
  getSidebarPreference,
  saveSidebarPreference,
  SIDEBAR_PREFERENCE_TYPES,
} from "@/lib/user-preferences";
import { GET_ALL_TEAMS } from "@/services/kanban";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { Agent } from "@/types/agent";
import { Team } from "@/types/kanban";
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from "@hello-pangea/dnd";
import { Route } from "next";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Fragment, useEffect, useState } from "react";
import { toast } from "sonner";
import { getOrgDetails } from "../../utils/browserUtils";
import { LucideIcon } from "../lucide-icon";
import TooltipWrapper from "../tooltip-wrapper";
import { Button } from "../ui/button";
import { TeamAgents } from "./team-agents";
import { TransformedTeam } from "./utils";

export function NavTeams({
  items = [],
  title = "Your Teams",
  handleNavigation,
  isCollapsed,
  showDetails = true,
  teamAgentsMap,
}: {
  items?: TransformedTeam[];
  title?: string;
  handleNavigation?: (view: string) => void;
  isCollapsed?: boolean;
  showDetails?: boolean;
  teamAgentsMap: Record<string, Agent[]>;
}) {
  const pathname = usePathname();

  const { isMobile, state } = useSidebar();
  const teamsList = useTicketMetaStore((state) => state.teams);
  const [teamsToJoin, setTeamsToJoin] = useState<Team[]>([]);
  // const currentOrgId = useGlobalConfigPersistStore(
  //   (state) => state.currentOrgId,
  // );

  const { orgId: currentOrgId } = getOrgDetails();
  const [pinnedTeams, setPinnedTeams] = useState<Set<string>>(new Set());
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const router = useRouter();

  // Track the last clicked team
  const [lastClickedTeam, setLastClickedTeam] = useState<string | null>(null);

  // State to manage the order of teams
  const [baseOrderedTeams, setBaseOrderedTeams] = useState<TransformedTeam[]>(
    [],
  ); // Order before pinning sort
  const [orderedTeams, setOrderedTeams] = useState<TransformedTeam[]>([]); // Final displayed order

  // Update orderedTeams if the initial items prop changes, respecting saved order
  useEffect(() => {
    if (!items || items.length === 0 || !currentOrgId) {
      setBaseOrderedTeams(items || []);
      return;
    }

    const loadTeamOrder = async () => {
      try {
        // Try to get team order from Supabase first
        const savedTeamIds = await getSidebarPreference<string[]>(
          SIDEBAR_PREFERENCE_TYPES.TEAM_ORDER,
          currentUser?.id,
          currentOrgId,
        );

        if (savedTeamIds && savedTeamIds.length > 0) {
          // Create a map for quick lookup - extract team ID from URL
          const itemsMap = new Map(
            items.map((item) => [
              item.url.split("/").pop() || "", // Extract team ID from URL
              item,
            ]),
          );

          // Reconstruct the ordered list based on saved IDs, filtering out any teams that no longer exist
          const reordered = savedTeamIds
            .map((teamId) => itemsMap.get(teamId))
            .filter(Boolean) as TransformedTeam[];

          // Add any new teams (not in saved order) to the end
          const currentTeamIds = new Set(
            reordered.map((item) => item.url.split("/").pop() || ""),
          );
          const newTeams = items.filter(
            (item) => !currentTeamIds.has(item.url.split("/").pop() || ""),
          );

          setBaseOrderedTeams([...reordered, ...newTeams]);
        } else {
          // No saved order, use the default from props
          setBaseOrderedTeams(items);
        }
      } catch (error) {
        console.error("Failed to load team order:", error);
        setBaseOrderedTeams(items); // Fallback to default order on error
      }
    };

    loadTeamOrder();
  }, [items, currentOrgId, currentUser?.id]);

  // Helper function to sort teams with pinned items first
  const sortTeams = (
    teams: TransformedTeam[],
    pinnedUrls: Set<string>,
  ): TransformedTeam[] => {
    if (!teams) return [];
    const pinned = teams.filter((team) => pinnedUrls.has(team.url));
    const unpinned = teams.filter((team) => !pinnedUrls.has(team.url));
    return [...pinned, ...unpinned];
  };

  // Effect to apply pinning sort whenever base order or pinned set changes
  useEffect(() => {
    setOrderedTeams(sortTeams(baseOrderedTeams, pinnedTeams));
  }, [baseOrderedTeams, pinnedTeams]);

  // Load last clicked team from user_config
  useEffect(() => {
    const fetchLastClickedTeam = async () => {
      if (currentUser?.id && currentOrgId) {
        try {
          const preference = await getSidebarPreference(
            SIDEBAR_PREFERENCE_TYPES.LAST_TEAM,
            currentUser.id,
            currentOrgId,
          );
          if (preference) {
            setLastClickedTeam(preference as string);
          }
        } catch (error) {
          console.error("Error fetching last clicked team:", error);
        }
      }
    };

    fetchLastClickedTeam();
  }, [currentOrgId, currentUser?.id]);

  // Load pinned teams from Supabase on mount
  useEffect(() => {
    if (!currentOrgId) return;

    const loadPinnedTeams = async () => {
      try {
        // Try to get pinned teams from Supabase first
        const savedPinIds = await getSidebarPreference<string[]>(
          SIDEBAR_PREFERENCE_TYPES.PINNED_TEAMS,
          currentUser?.id,
          currentOrgId,
        );

        if (savedPinIds && savedPinIds.length > 0) {
          // Convert team IDs to full URLs for the Set
          const pinnedUrls = new Set(
            savedPinIds.map((teamId) => `/dashboard/${teamId}`),
          );
          setPinnedTeams(pinnedUrls);
        }
      } catch (error) {
        console.error("Failed to load pinned teams:", error);
      }
    };

    loadPinnedTeams();
  }, [currentOrgId, currentUser?.id]);

  // Save pinned teams to Supabase when they change
  useEffect(() => {
    if (!currentOrgId || pinnedTeams.size === 0) return;

    const savePinnedTeams = async () => {
      try {
        // Extract team IDs from full URLs
        const pinnedTeamIds = Array.from(pinnedTeams).map(
          (url) => url.split("/").pop() || "",
        );

        await saveSidebarPreference(
          SIDEBAR_PREFERENCE_TYPES.PINNED_TEAMS,
          currentUser?.id,
          currentOrgId,
          pinnedTeamIds,
        );
      } catch (error) {
        console.error("Failed to save pinned teams:", error);
      }
    };

    savePinnedTeams();
  }, [currentOrgId, currentUser?.id, pinnedTeams]);

  // Initialize openTeams state
  const [openTeams, setOpenTeams] = useState<Record<string, boolean>>({});

  // Update openTeams when lastClickedTeam or items change
  useEffect(() => {
    if (!currentOrgId) return;

    const loadOpenTeams = async () => {
      try {
        // First try to load the saved open teams state
        const savedOpenTeamIds = await getSidebarPreference<
          Record<string, boolean>
        >(SIDEBAR_PREFERENCE_TYPES.OPEN_TEAMS, currentUser?.id, currentOrgId);

        if (savedOpenTeamIds) {
          // Convert team IDs back to full URLs for the component state
          const convertedOpenTeams: Record<string, boolean> = {};
          Object.entries(savedOpenTeamIds).forEach(([teamId, isOpen]) => {
            const fullUrl = `/dashboard/${teamId}`;
            convertedOpenTeams[fullUrl] = isOpen;
          });

          console.log("Loaded open teams state:", convertedOpenTeams);
          setOpenTeams(convertedOpenTeams);
        }
      } catch (error) {
        console.error("Failed to load open teams:", error);
      }
    };

    loadOpenTeams();
  }, [currentOrgId, currentUser?.id, items]);

  // Save openTeams state when it changes
  useEffect(() => {
    if (!currentOrgId || Object.keys(openTeams).length === 0) return;

    const saveOpenTeams = async () => {
      try {
        console.log("Saving open teams state:", openTeams);

        // Convert full URLs to team IDs for storage
        const convertedOpenTeams: Record<string, boolean> = {};
        Object.entries(openTeams).forEach(([url, isOpen]) => {
          const teamId = url.split("/").pop() || "";
          convertedOpenTeams[teamId] = isOpen;
        });

        await saveSidebarPreference(
          SIDEBAR_PREFERENCE_TYPES.OPEN_TEAMS,
          currentUser?.id,
          currentOrgId,
          convertedOpenTeams,
        );
      } catch (error) {
        console.error("Failed to save open teams state:", error);
      }
    };

    saveOpenTeams();
  }, [openTeams, currentOrgId, currentUser?.id]);

  // Save team order to Supabase when it changes
  useEffect(() => {
    if (!currentOrgId || baseOrderedTeams.length === 0) return;

    const saveTeamOrder = async () => {
      try {
        // Extract team IDs from URLs
        const teamIds = baseOrderedTeams.map(
          (team) => team.url.split("/").pop() || "",
        );

        await saveSidebarPreference(
          SIDEBAR_PREFERENCE_TYPES.TEAM_ORDER,
          currentUser?.id,
          currentOrgId,
          teamIds,
        );
      } catch (error) {
        console.error("Failed to save team order:", error);
      }
    };

    saveTeamOrder();
  }, [baseOrderedTeams, currentOrgId, currentUser?.id]);

  // Implement the drag end handler
  const handleDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    // Dropped outside the list
    if (!destination) {
      return;
    }

    // Dropped in the same place
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const newOrderedTeams = Array.from(orderedTeams);
    const [reorderedItem] = newOrderedTeams.splice(source.index, 1);
    newOrderedTeams.splice(destination.index, 0, reorderedItem);

    // Update the base order state - the effect will re-apply pinning sort
    setBaseOrderedTeams(newOrderedTeams);

    // Persist the new *base* order to Supabase
    if (currentOrgId) {
      const orderedTeamIds = newOrderedTeams.map(
        (team) => team.url.split("/").pop() || "",
      );

      // Save to Supabase with localStorage fallback
      saveSidebarPreference(
        SIDEBAR_PREFERENCE_TYPES.TEAM_ORDER,
        currentUser?.id,
        currentOrgId,
        orderedTeamIds,
      ).catch((error) => {
        console.error("Failed to save team order:", error);
      });
    }
  };

  const onCopy = async (text: string) => {
    try {
      await window.navigator.clipboard.writeText(text);
    } catch (error) {
      console.error(error);
    }
  };

  const leaveTeamHandler = async (teamId: string) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/leave`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to leave team");
      }

      useTicketMetaStore.getState().updateUserIsPartOf(teamId, false);
      const teamsList = await fetch(GET_ALL_TEAMS(currentUser?.uid), {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const teamsData = await teamsList.json();
      useTicketMetaStore.getState().setAllTeams([...teamsData]);
      // Navigate to the first available team
      const team = teamsData ? teamsData[0] : null;
      router.push(`/dashboard/${team?.uid}`);
      // Redirect to teams page or refresh the teams list
      router.refresh();
      if (isMobile) {
        handleNavigation("teams");
      }
    } catch (error) {
      console.error("Error leaving team:", error);
      toast(error instanceof Error ? error.message : "Failed to leave team");
    }
  };

  const addToTeamHandler = async (teamId: string) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: currentUser?.email }),
      });

      if (!response.ok) {
        throw new Error("Failed to add member");
      }
      useTicketMetaStore.getState().updateUserIsPartOf(teamId, true);
      const teamsList = await fetch(GET_ALL_TEAMS(currentUser?.uid), {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const teamsData = await teamsList.json();
      useTicketMetaStore.getState().setAllTeams([...teamsData]);
      router.push(`/dashboard/${teamId}`);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to add member",
      );
    }
  };

  useEffect(() => {
    const fetchTeams = async () => {
      try {
        const response = await fetch("/api/teams/public");
        if (!response.ok) {
          throw new Error("Failed to fetch available teams");
        }
        const allTeams = await response.json();
        const userAddedTeamsList = teamsList.map((item) => item.uid);

        // First, get the team IDs where the user is a member
        // Filter out teams that the user is already a member of
        const teamsToJoinFiltered = allTeams?.data?.filter(
          (team) =>
            !userAddedTeamsList.includes(team.id.toString()) &&
            !team.parentTeamId,
        );

        setTeamsToJoin(teamsToJoinFiltered);
      } catch (error) {
        console.error("Failed to fetch teams:", error);
      }
    };

    // Extract the team IDs into an array
    if (currentOrgId) {
      fetchTeams();
    }
  }, [currentOrgId, teamsList]);

  const isTeamActive = (team: TransformedTeam) => {
    // Check if we're in this team's section
    const isInTeamSection = team.items.some(
      (item) => pathname?.startsWith(item.url),
    );

    // If we're not in this team's section, it's never active
    if (!isInTeamSection) {
      return false;
    }

    // If sidebar is collapsed, always show active state
    if (isCollapsed) {
      return true;
    }

    // Check if this team has a saved expanded state
    const hasExplicitState = team.url in openTeams;

    // For the first team, only default to expanded if there's no saved state
    // and we're on initial load (no teams have explicit state yet)
    const isFirstTeam = team === orderedTeams[0];
    const noTeamsHaveExplicitState = Object.keys(openTeams).length === 0;
    const shouldDefaultExpand = isFirstTeam && noTeamsHaveExplicitState;

    // Use saved state if available, otherwise only default first team to expanded on initial load
    const isTeamExpanded = hasExplicitState
      ? openTeams[team.url]
      : shouldDefaultExpand;

    // Show active state when team is NOT expanded
    return !isTeamExpanded;
  };

  // Function to toggle team pin status
  const togglePinTeam = (teamUrl: string) => {
    setPinnedTeams((prev) => {
      const newPinned = new Set(prev);
      if (newPinned.has(teamUrl)) {
        newPinned.delete(teamUrl);
      } else {
        newPinned.add(teamUrl);
      }
      return newPinned;
    });
  };

  // Save last clicked team whenever it changes
  useEffect(() => {
    if (!currentOrgId || !lastClickedTeam) return;

    // We don't need this effect anymore since we're saving directly when a team is clicked
    // This prevents duplicate API calls
  }, [currentOrgId, lastClickedTeam]);

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="flex items-center justify-between">
        <span className="text-xs font-medium text-color-text-muted">
          {title}
        </span>
        {showDetails ? (
          teamsToJoin.length === 0 ? (
            <Button
              variant="ghost"
              size="icon"
              className="w-4 h-4 text-[var(--color-text-muted)] p-[2px] rounded-[4px] hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-text-muted)]"
              onClick={() => {
                router.push("/organization/settings/new-team");
              }}
            >
              <LucideIcon name="Plus" />
            </Button>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger>
                <TooltipWrapper
                  tooltipContent={
                    state === "collapsed" ? undefined : "Create team"
                  }
                  asChild
                >
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-4 h-4 text-[var(--color-text-muted)] p-[2px] rounded-[4px] hover:bg-[var(--color-bg-elevated)] hover:text-[var(--color-text-muted)]"
                  >
                    <LucideIcon name="Plus" />
                  </Button>
                </TooltipWrapper>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-56"
                side="right"
                align="start"
                sideOffset={20}
              >
                <DropdownMenuLabel className="text-xs font-medium text-color-text-placeholder">
                  Join a team
                </DropdownMenuLabel>
                <DropdownMenuGroup>
                  {teamsToJoin.map((team) => (
                    <DropdownMenuItem
                      key={team.id.toString()}
                      onClick={() => addToTeamHandler(team.id.toString())}
                      className="flex items-center gap-2"
                    >
                      <HeroIcon name={team.icon} color={team.color} showWidth />
                      <span className="text-sm">{team.name}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={() =>
                      router.push("/organization/settings/new-team")
                    }
                  >
                    <Plus />
                    <span>Create new team</span>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          )
        ) : null}
      </SidebarGroupLabel>
      <SidebarMenu className="gap-0.5">
        {showDetails ? (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="teams-sidebar">
              {(providedDroppable) => (
                <div
                  ref={providedDroppable.innerRef}
                  {...providedDroppable.droppableProps}
                >
                  {orderedTeams.map((team, index) => (
                    <Draggable
                      key={team.url} // Ensure unique and stable key
                      draggableId={team.url} // Ensure unique and stable id
                      index={index}
                      isDragDisabled={pinnedTeams.has(team.url)} // Disable drag for pinned items
                    >
                      {(providedDraggable, snapshot) => {
                        // Start with the default style provided by dnd
                        const providedStyle =
                          providedDraggable.draggableProps.style;
                        let styleToApply = providedStyle; // Default to the original style

                        // If dragging actively and a transform is present...
                        if (snapshot.isDragging && providedStyle?.transform) {
                          // Regex to capture the Y value, accommodating various formats
                          const yMatch = providedStyle.transform.match(
                            /translate\(\s*[^,]+,\s*([^)\s]+)\s*\)/,
                          );
                          if (yMatch && yMatch[1]) {
                            const yValue = yMatch[1]; // Includes units, e.g., '123px'
                            // Create a new style object with the modified transform
                            styleToApply = {
                              ...providedStyle, // Spread original styles
                              transform: `translate(0px, ${yValue})`, // Override transform
                            };
                          }
                          // If regex fails, we keep the original transform from the copy
                        }

                        // Apply the potentially modified style
                        return (
                          <div
                            ref={providedDraggable.innerRef}
                            {...providedDraggable.draggableProps}
                            style={styleToApply} // Apply the final style object
                            className="group/item relative mb-0.5 list-none" // Use named group for hover scope
                          >
                            {/* Drag Handle (Visible on Hover) */}
                            <span
                              {...providedDraggable.dragHandleProps}
                              className="absolute left-0 top-1/2 -translate-y-1/2 -ml-2 z-10 cursor-grab text-[var(--color-text-muted)] opacity-0 group-hover/item:opacity-100 transition-opacity duration-150"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <GripVertical size={12} />
                            </span>

                            {/* Main Team Item Content */}
                            <Collapsible
                              open={!!openTeams[team.url]}
                              onOpenChange={(open) => {
                                // This directly updates the openTeams state
                                // when Collapsible's internal state changes
                                setOpenTeams((prev) => ({
                                  ...prev,
                                  [team.url]: open,
                                }));
                              }}
                              className="group/collapsible"
                            >
                              <SidebarMenuItem>
                                <TooltipWrapper
                                  tooltipContent={
                                    state === "collapsed"
                                      ? undefined
                                      : team.title
                                  }
                                  delayDuration={1000}
                                  asChild
                                >
                                  <CollapsibleTrigger
                                    asChild
                                    onClick={() => {
                                      if (state === "collapsed") {
                                        // When a collapsed team is clicked, we're actually navigating to its first sub-item
                                        // So we should save this as the last clicked team
                                        setLastClickedTeam(
                                          team.items[0].url as string,
                                        );

                                        // Also save the team ID directly to the database
                                        if (currentOrgId && team.items[0].url) {
                                          const teamId =
                                            team.items[0].url
                                              .split("/")
                                              .pop() || "";
                                          saveSidebarPreference(
                                            SIDEBAR_PREFERENCE_TYPES.LAST_TEAM,
                                            currentUser?.id,
                                            currentOrgId,
                                            teamId,
                                          ).catch((error) => {
                                            console.error(
                                              "Failed to save last clicked team:",
                                              error,
                                            );
                                          });
                                        }

                                        router.push(team.items[0].url as Route);
                                      }
                                    }}
                                  >
                                    <SidebarMenuButton
                                      tooltip={team.title}
                                      className={`w-full group/menu-item group-data-[state=open]:bg-[var(--color-bg-elevated)] ${
                                        isTeamActive(team)
                                          ? "!bg-[var(--color-strong)] !text-[var(--color-text-default)]"
                                          : ""
                                      }`}
                                    >
                                      <div className="flex items-center w-full">
                                        {/* Team Icon */}
                                        <span className="shrink-0 size-5 inline-flex items-center justify-center bg-[var(--color-bg-elevated)]">
                                          <HeroIcon
                                            name={team.icon}
                                            color={team.color}
                                            className="h-3.5 w-3.5"
                                          />
                                        </span>

                                        {/* Team Title and Chevron */}
                                        <div className="flex items-center min-w-0 flex-1 ml-2 transition-all duration-200 group-hover/menu-item:pr-4">
                                          <div className="flex items-center min-w-0 flex-1">
                                            <span className="truncate">
                                              {team.title}
                                            </span>
                                            <div className="flex items-center shrink-0">
                                              {team.isPrivate && (
                                                <Lock
                                                  size={12}
                                                  className="text-[var(--color-text-muted)] ml-1"
                                                  strokeWidth={2.25}
                                                />
                                              )}
                                              <ChevronRight
                                                size={16}
                                                className="ml-1 transition-all duration-200 ease-out group-data-[state=open]/collapsible:rotate-90 text-[var(--color-text-muted)] group-hover/menu-item:translate-x-0.5"
                                              />
                                            </div>
                                          </div>
                                        </div>

                                        {/* Pin Icon */}
                                        {pinnedTeams.has(team.url) && (
                                          <div className="relative transition-all duration-200 group-hover/menu-item:translate-x-[-20px]">
                                            <Pin className="h-3 w-3 text-muted-foreground" />
                                          </div>
                                        )}

                                        {/* Dropdown Menu */}
                                        <div className="transition-opacity group-data-[collapsible=icon]:opacity-0 group-data-[collapsible=icon]:hidden opacity-0 group-hover/menu-item:opacity-100 group-data-[state=open]:opacity-100 flex items-center">
                                          <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                              <SidebarMenuAction showOnHover>
                                                <MoreHorizontal className="text-[var(--color-text-muted)]" />
                                                <span className="sr-only">
                                                  More
                                                </span>
                                              </SidebarMenuAction>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent
                                              className="w-48 rounded-sm"
                                              side={
                                                isMobile ? "bottom" : "right"
                                              }
                                              align={isMobile ? "end" : "start"}
                                            >
                                              <DropdownMenuItem
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  router.push(
                                                    `${team.url}/settings` as Route,
                                                  );
                                                }}
                                              >
                                                <Settings />
                                                <span>Team settings</span>
                                              </DropdownMenuItem>
                                              <DropdownMenuItem
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  onCopy(
                                                    `${BASE_URL_APP}${team.url}`,
                                                  );
                                                }}
                                              >
                                                <LinkIcon />
                                                <span>Copy link</span>
                                              </DropdownMenuItem>
                                              <DropdownMenuItem
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  togglePinTeam(team.url);
                                                }}
                                              >
                                                {pinnedTeams.has(team.url) ? (
                                                  <>
                                                    <PinOff className="h-4 w-4" />
                                                    <span>
                                                      Unpin from sidebar
                                                    </span>
                                                  </>
                                                ) : (
                                                  <>
                                                    <Pin className="h-4 w-4" />
                                                    <span>Pin to sidebar</span>
                                                  </>
                                                )}
                                              </DropdownMenuItem>
                                              <DropdownMenuSeparator />
                                              <DropdownMenuItem
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  leaveTeamHandler(
                                                    team.url.split("/").pop() ||
                                                      "",
                                                  );
                                                }}
                                              >
                                                <span>Leave team</span>
                                              </DropdownMenuItem>
                                            </DropdownMenuContent>
                                          </DropdownMenu>
                                        </div>
                                      </div>
                                    </SidebarMenuButton>
                                  </CollapsibleTrigger>
                                </TooltipWrapper>
                                <CollapsibleContent>
                                  <SidebarMenuSub className="list-none !mr-0 !pr-0">
                                    {team.items?.map((subItem) => {
                                      // Create a URL object for path comparison
                                      const urlObj = new URL(
                                        `${BASE_URL_APP}${subItem.url}`,
                                      );
                                      const isActive =
                                        pathname === urlObj.pathname;

                                      // Check if this is the Views item to add agents after it
                                      const isViewsItem =
                                        subItem.title === "Views";

                                      return (
                                        <Fragment key={subItem.title}>
                                          <SidebarMenuSubItem>
                                            <SidebarMenuSubButton
                                              asChild
                                              className={`group/sub-button relative isolate ${
                                                isActive
                                                  ? "bg-[var(--color-strong)] text-[var(--color-text-default)]"
                                                  : ""
                                              } hover:bg-[var(--color-bg-elevated)]`}
                                            >
                                              <Link href={urlObj as URL}>
                                                <div
                                                  className="flex items-center gap-2 w-full"
                                                  onClick={() => {
                                                    // Save the last clicked team
                                                    setLastClickedTeam(
                                                      subItem.url as string,
                                                    );

                                                    // Also save the team ID directly to the database
                                                    if (currentOrgId) {
                                                      const teamId =
                                                        subItem.url
                                                          .split("/")
                                                          .pop() || "";

                                                      saveSidebarPreference(
                                                        SIDEBAR_PREFERENCE_TYPES.LAST_TEAM,
                                                        currentUser?.id,
                                                        currentOrgId,
                                                        teamId,
                                                      );
                                                    }

                                                    // Ensure the parent team is expanded
                                                    const parentTeamUrl =
                                                      team.url as string;
                                                    setOpenTeams((prev) => ({
                                                      ...prev,
                                                      [parentTeamUrl]: true,
                                                    }));
                                                  }}
                                                >
                                                  <LucideIcon
                                                    name={subItem.icon}
                                                    className={`size-4 transition-all duration-200 ease-out group-hover/sub-button:scale-110 group-hover/sub-button:rotate-3 group-hover/sub-button:text-color-icon ${
                                                      isActive
                                                        ? "text-[var(--color-text-default)]"
                                                        : "text-color-icon-muted"
                                                    }`}
                                                  />
                                                  {subItem.isAgent &&
                                                  subItem.agentData
                                                    ?.avatar_url ? (
                                                    <div className="h-4 w-4 rounded-full overflow-hidden mr-2">
                                                      <Image
                                                        src={
                                                          subItem.agentData
                                                            .avatar_url
                                                        }
                                                        alt={subItem.title}
                                                        width={16}
                                                        height={16}
                                                        className="h-full w-full object-cover"
                                                      />
                                                    </div>
                                                  ) : null}
                                                  <span className="truncate">
                                                    {subItem.title}
                                                  </span>
                                                </div>
                                              </Link>
                                            </SidebarMenuSubButton>
                                          </SidebarMenuSubItem>

                                          {/* Add TeamAgents component after Views */}
                                          {isViewsItem &&
                                            teamAgentsMap &&
                                            teamAgentsMap[team.uid] &&
                                            teamAgentsMap[team.uid].length >
                                              0 && (
                                              <TeamAgents
                                                teamUid={team.uid}
                                                agents={teamAgentsMap[team.uid]}
                                              />
                                            )}
                                        </Fragment>
                                      );
                                    })}
                                  </SidebarMenuSub>
                                </CollapsibleContent>
                              </SidebarMenuItem>
                            </Collapsible>
                          </div>
                        );
                      }}
                    </Draggable>
                  ))}
                  {providedDroppable.placeholder} {/* Important for dnd */}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        ) : (
          orderedTeams.map((team) => (
            <div
              className="text-sm cursor-pointer text-color-text/90 px-3 py-2 rounded-[4px] hover:bg-[var(--color-bg-elevated)] hover:text-color-text flex items-center w-full"
              key={team.url}
              onClick={(e) => {
                e.stopPropagation();
                router.push(`${team.url}/settings` as Route);
              }}
            >
              <span className="shrink-0 size-5 inline-flex items-center justify-center bg-[var(--color-bg-elevated)] rounded-sm">
                <HeroIcon
                  name={team.icon}
                  color={team.color}
                  className="h-3.5 w-3.5"
                />
              </span>
              <div className="flex items-center min-w-0 flex-1 ml-2">
                <div className="flex items-center min-w-0 flex-1">
                  <span className="truncate">{team.title}</span>
                  <div className="flex items-center shrink-0">
                    {team.isPrivate && (
                      <Lock
                        size={12}
                        className="text-[var(--color-text-muted)] ml-1"
                        strokeWidth={2.25}
                      />
                    )}
                    {pinnedTeams.has(team.url) && (
                      <Pin className="mr-1 h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}
