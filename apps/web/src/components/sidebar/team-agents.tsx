"use client";

import { Agent } from "@/types/agent";
import { usePathname } from "next/navigation";
import { SidebarMenuSubButton, SidebarMenuSubItem } from "@/components/ui/sidebar";
import Link from "next/link";
import { getAgentAvatarUrl } from "@/lib/utils";
import { useEffect } from "react";
import Image from "next/image";
import { Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";

interface TeamAgentsProps {
  teamUid: string;
  agents: Agent[]; 
}

export function TeamAgents({ teamUid, agents }: TeamAgentsProps) {
  const pathname = usePathname();

  // Debug logging
  useEffect(() => {
    console.log(`TeamAgents for team ${teamUid}:`, agents);
  }, [teamUid, agents]);

  if (!agents || agents.length === 0) {
    return null;
  }

  return (
    <>
      {agents.map((agent, index) => {
        const agentUrl = `/dashboard/${teamUid}/agent/${agent.id}`;
        const isActive = pathname === agentUrl;
        
        return (
          <SidebarMenuSubItem key={agent.id}>
            <SidebarMenuSubButton
              asChild
              className={`group/sub-button relative isolate ${
                isActive
                  ? "bg-[var(--color-strong)] text-[var(--color-text-default)]"
                  : ""
              } hover:bg-[var(--color-bg-elevated)]`}
            >
              {/* @ts-expect-error - Next.js typing issue with dynamic routes */}
              <Link href={agentUrl}>
                <div
                  className="flex items-center gap-2 w-full"
                >
                  {/* Bot icon removed as requested */}
                  {agent.avatar_url ? (
                    <div className="h-4 w-4 rounded-sm overflow-hidden">
                      <Image 
                        src={getAgentAvatarUrl(agent.avatar_url)} 
                        alt={agent.name} 
                        className="h-full w-full object-cover"
                        width={16}
                        height={16}
                      />
                    </div>
                  ) : (
                    <div className="h-4 w-4 rounded-sm bg-gray-200 flex items-center justify-center text-xs">
                      {agent.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                  <span className="truncate">
                    {agent.name}
                  </span>
                  
                  {/* Agent label with sparkle icon and animation */}
                  <div 
                    className={cn(
                      "ml-1 flex items-center gap-0.5 text-[10px] font-medium px-1.5 py-0.5 rounded-[4px]",
                      "border border-[var(--color-border-magic)] text-[var(--color-text-magic)]",
                      "max-h-[20px] min-h-[16px]",
                      "opacity-70 group-hover/sub-button:opacity-100 transition-opacity",
                      "animate-[fadeIn_0.3s_ease-in-out]"
                    )}
                    style={{
                      animationDelay: `${index * 0.1}s`,
                    }}
                  >
                    <Sparkles className="h-2.5 w-2.5" />
                    <span>Agent</span>
                  </div>
                </div>
              </Link>
            </SidebarMenuSubButton>
          </SidebarMenuSubItem>
        );
      })}
    </>
  );
}
