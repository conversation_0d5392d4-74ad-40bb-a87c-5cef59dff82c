import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import React from "react";
import {
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from "recharts";
import { ChartEmptyState } from "./ChartEmptyState";

interface Category {
  name: string;
  key: string;
  color?: string;
}

export interface BaseLineChartProps<T> {
  data: T[];
  loading?: boolean;
  error?: string;
  xKey: string;
  categories: Category[];
  height?: number | string;
  className?: string;
  noCard?: boolean;
  emptyStateMessage?: string;
}

type ValueType = number | string | Array<number | string>;
type NameType = string;

interface CustomTooltipProps extends TooltipProps<ValueType, NameType> {
  active?: boolean;
  payload?: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  label?: string;
}

const CHART_COLORS = [
  "var(--chart-color-1)",
  "var(--chart-color-2)",
  "var(--chart-color-3)",
  "var(--chart-color-4)",
  "var(--chart-color-5)",
  "var(--chart-color-6)",
  "var(--chart-color-7)",
  "var(--chart-color-8)",
  "var(--chart-color-9)",
  "var(--chart-color-10)",
];

const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-background p-2 shadow-sm font-inter">
        <p className="text-sm font-medium">{label}</p>
        {payload.map((entry) => (
          <p
            key={entry.name}
            className="text-sm"
            style={{ color: entry.color }}
          >
            {entry.name}: {entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export function BaseLineChart<T>({
  data,
  loading,
  error,
  xKey,
  categories,
  height = 300,
  className = "",
  emptyStateMessage = "No data available",
}: BaseLineChartProps<T>) {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = React.useState<number | null>(
    null,
  );

  React.useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.clientWidth);

      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setContainerWidth(entry.contentRect.width);
        }
      });

      resizeObserver.observe(containerRef.current);
      return () => {
        if (containerRef.current) {
          resizeObserver.unobserve(containerRef.current);
        }
      };
    }
  }, []);

  if (loading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-[300px] w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  // Check for empty data - either an empty array or all values are zero/missing
  const isEmpty =
    data.length === 0 ||
    categories.every((category) => {
      return data.every((item) => {
        const value = (item as Record<string, unknown>)[category.key];
        return value === undefined || value === null || value === 0;
      });
    });

  if (isEmpty) {
    return (
      <ChartEmptyState
        height={height}
        className={className}
        message={emptyStateMessage}
      />
    );
  }

  const isCompact = containerWidth !== null && containerWidth < 250;

  return (
    <div
      ref={containerRef}
      className={`w-full h-full ${className}`}
      style={{ width: "100%", height }}
    >
      <ResponsiveContainer>
        <LineChart
          data={data}
          margin={{
            top: 10,
            right: isCompact ? 5 : 20,
            bottom: 5,
            left: isCompact ? 5 : 20,
          }}
        >
          <XAxis
            dataKey={xKey}
            stroke="#888888"
            fontSize={isCompact ? 10 : 12}
            tickLine={false}
            axisLine={false}
            interval={isCompact ? 1 : 0}
          />
          <YAxis
            stroke="#888888"
            fontSize={isCompact ? 10 : 12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `${value}`}
            width={isCompact ? 25 : 35}
          />
          <Tooltip content={<CustomTooltip />} />
          {categories.map((category, index) => (
            <Line
              key={category.key}
              type="monotone"
              dataKey={category.key}
              name={category.name}
              stroke={
                category.color || CHART_COLORS[index % CHART_COLORS.length]
              }
              strokeWidth={2}
              dot={false}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
