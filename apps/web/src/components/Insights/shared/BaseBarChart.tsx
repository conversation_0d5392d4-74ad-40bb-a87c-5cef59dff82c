import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle } from "lucide-react";
import React from "react";
import {
  Bar,
  BarChart,
  Cell,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from "recharts";
import { ChartEmptyState } from "./ChartEmptyState";
import { TicketDetailsDialog, TicketSummary } from "./TicketDetailsDialog";

interface Category {
  name: string;
  key: string;
  color?: string;
  tickets?: TicketSummary[];
}

interface ChartDataItem {
  name: string;
  value: number;
  color: string;
  tickets?: TicketSummary[];
}

// Define a type that matches what we want to accept for our component
type IntervalType =
  | number
  | "preserveStart"
  | "preserveEnd"
  | ((index: number) => number);

interface BaseBarChartProps<T> {
  data: T[];
  categories: Category[];
  valueKey: keyof T;
  nameKey: keyof T;
  loading?: boolean;
  error?: string;
  height?: number | string;
  className?: string;
  xAxisInterval?: IntervalType;
  noCard?: boolean;
  emptyStateMessage?: string;
}

const CHART_COLORS = [
  "var(--chart-color-1)",
  "var(--chart-color-2)",
  "var(--chart-color-3)",
  "var(--chart-color-4)",
  "var(--chart-color-5)",
  "var(--chart-color-6)",
  "var(--chart-color-7)",
  "var(--chart-color-8)",
  "var(--chart-color-9)",
  "var(--chart-color-10)",
];

const CustomTooltip = ({
  active,
  payload,
  label,
}: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-background p-3 shadow-sm">
        <p className="font-medium">{label}</p>
        <p className="text-sm text-muted-foreground">
          {payload[0].value} tickets
        </p>
      </div>
    );
  }

  return null;
};

export function BaseBarChart<T>({
  data,
  categories,
  valueKey,
  nameKey,
  loading = false,
  error,
  height = 300,
  className = "",
  xAxisInterval = 0,
  emptyStateMessage = "No data available",
}: BaseBarChartProps<T>) {
  const [selectedCategory, setSelectedCategory] = React.useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [selectedTickets, setSelectedTickets] = React.useState<TicketSummary[]>([]);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = React.useState<number | null>(
    null,
  );

  React.useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.clientWidth);

      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setContainerWidth(entry.contentRect.width);
        }
      });

      resizeObserver.observe(containerRef.current);
      return () => {
        if (containerRef.current) {
          resizeObserver.unobserve(containerRef.current);
        }
      };
    }
  }, []);

  if (loading) {
    return (
      <div className="space-y-2 h-full w-full">
        <Skeleton
          className="w-full"
          style={{
            height: typeof height === "string" ? height : `${height}px`,
          }}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  // Check for empty data - either an empty array or all values are zero
  const isEmpty =
    data.length === 0 || data.every((item) => Number(item[valueKey]) === 0);
  if (isEmpty) {
    return (
      <ChartEmptyState
        height={height}
        className={className}
        message={emptyStateMessage}
      />
    );
  }

  const chartData: ChartDataItem[] = data.map((item, index) => {
    const category = categories.find((c) => c.key === String(item[nameKey]));
    return {
      name: String(item[nameKey]),
      value: Number(item[valueKey]),
      color: category?.color || CHART_COLORS[index % CHART_COLORS.length],
      tickets: (item as { tickets?: TicketSummary[] }).tickets || [],
    };
  });

  const isCompact = containerWidth !== null && containerWidth < 250;

  // Use a number for the interval prop
  // This is the safest approach that works with recharts
  const intervalValue =
    typeof xAxisInterval === "number"
      ? xAxisInterval
      : typeof xAxisInterval === "function"
      ? 0 // Default to 0 (show all) if a function is provided
      : xAxisInterval; // Pass through string values

  const handleBarClick = (data: ChartDataItem, index: number) => {
    const category = chartData[index];
    if (category && category.tickets && category.tickets.length > 0) {
      setSelectedCategory(category.name);
      setSelectedTickets(category.tickets);
      setIsDialogOpen(true);
    }
  };

  return (
    <div ref={containerRef} className={`w-full h-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={chartData}
          margin={{
            top: 10,
            right: isCompact ? 5 : 20,
            bottom: 5,
            left: isCompact ? 5 : 20,
          }}
        >
          <XAxis
            dataKey="name"
            stroke="#888888"
            fontSize={isCompact ? 10 : 12}
            tickLine={false}
            axisLine={false}
            interval={intervalValue}
            angle={isCompact ? -45 : 0}
            textAnchor={isCompact ? "end" : "middle"}
            height={isCompact ? 60 : 30}
          />
          <YAxis
            stroke="#888888"
            fontSize={isCompact ? 10 : 12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `${value}`}
            width={isCompact ? 25 : 35}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={false} // This removes the gray highlight
          />
          <Bar 
            dataKey="value" 
            radius={[4, 4, 0, 0]}
            onClick={handleBarClick}
            style={{ cursor: 'pointer' }}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>

      <TicketDetailsDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        tickets={selectedTickets}
        categoryName={selectedCategory || ""}
      />
    </div>
  );
}
