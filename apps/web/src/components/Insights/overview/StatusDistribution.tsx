import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useStatusDistribution } from "@/hooks/Insights/useStatusDistribution";
import { BaseBarChart } from "../shared/BaseBarChart";

interface StatusDistributionProps {
  teamId: string;
  noCard?: boolean;
}

// Function to get color based on status
const getStatusColor = (status: string): string => {
  const lowerStatus = status.toLowerCase();
  if (lowerStatus.includes("open") || lowerStatus.includes("new")) {
    return "var(--chart-color-1)"; // Chart color 1 for open/new
  } else if (
    lowerStatus.includes("progress") ||
    lowerStatus.includes("active")
  ) {
    return "var(--chart-color-3)"; // Chart color 3 for in progress/active
  } else if (lowerStatus.includes("hold") || lowerStatus.includes("pending")) {
    return "var(--chart-color-4)"; // Chart color 4 for on hold/pending
  } else if (
    lowerStatus.includes("closed") ||
    lowerStatus.includes("resolved") ||
    lowerStatus.includes("done")
  ) {
    return "var(--chart-color-2)"; // Chart color 2 for closed/resolved/done
  }
  // Default color for any other status
  return "var(--chart-color-7)";
};

export function StatusDistribution({
  teamId,
  noCard = false,
}: StatusDistributionProps) {
  const { data, isLoading, error } = useStatusDistribution(teamId);

  // Generate categories dynamically from the data with mapped colors
  const categories =
    data?.map((item) => ({
      name: item.status,
      key: item.status,
      color: getStatusColor(item.status),
      tickets: item.tickets,
    })) || [];

  const chartContent = (
    <BaseBarChart
      data={data ?? []}
      loading={isLoading}
      error={error?.message}
      categories={categories}
      valueKey="count"
      nameKey="status"
      height="100%"
      noCard={noCard}
      emptyStateMessage="No status data available"
    />
  );

  if (noCard) {
    return <div className="h-full w-full">{chartContent}</div>;
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle>Status distribution</CardTitle>
      </CardHeader>
      <CardContent className="h-[calc(100%-60px)] w-full">
        {chartContent}
      </CardContent>
    </Card>
  );
}
