import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  AssignmentDistributionData,
  useAssignmentDistribution,
} from "@/hooks/Insights/useAssignmentDistribution";
import * as React from "react";
import { Cell, Pie, PieChart, ResponsiveContainer, Tooltip } from "recharts";
import { ChartEmptyState } from "../shared/ChartEmptyState";
import { TicketDetailsDialog, TicketSummary } from "../shared/TicketDetailsDialog";

interface AssignmentDistributionProps {
  teamId: string;
  noCard?: boolean;
}

// Use the new chart colors from global CSS
const COLORS = [
  "var(--chart-color-1)",
  "var(--chart-color-2)",
  "var(--chart-color-3)",
  "var(--chart-color-4)",
  "var(--chart-color-5)",
  "var(--chart-color-6)",
  "var(--chart-color-7)",
  "var(--chart-color-8)",
  "var(--chart-color-9)",
  "var(--chart-color-10)",
];

interface TooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: AssignmentDistributionData;
  }>;
}

const CustomTooltip = ({ active, payload }: TooltipProps) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <Avatar className="h-6 w-6">
            <AvatarImage src={data.avatar_url ?? undefined} />
            <AvatarFallback>{data.agent_name[0]}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">{data.agent_name}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data.ticket_count} tickets
            </p>
          </div>
        </div>
      </div>
    );
  }
  return null;
};

// Define a proper type for the legend payload
interface LegendPayloadItem {
  color: string;
  payload: AssignmentDistributionData;
}

// Custom legend that displays agent avatars
const CustomLegend = ({ payload }: { payload?: LegendPayloadItem[] }) => {
  if (!payload) return null;

  return (
    <div className="flex flex-wrap justify-center gap-4 mt-4">
      {payload.map((entry, index) => {
        const { agent_name, ticket_count } = entry.payload;
        return (
          <div key={`legend-${index}`} className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded"
              style={{ backgroundColor: entry.color }}
            />
            <div className="flex items-center gap-1">
              <span className="text-xs font-medium">{agent_name}</span>
              <span className="text-xs text-gray-500">({ticket_count})</span>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export function AssignmentDistribution({
  teamId,
  noCard = false,
}: AssignmentDistributionProps) {
  const { data, isLoading, error } = useAssignmentDistribution(teamId);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [containerSize, setContainerSize] = React.useState({
    width: 0,
    height: 0,
  });
  
  // State for ticket details dialog
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [selectedTickets, setSelectedTickets] = React.useState<TicketSummary[]>([]);
  const [categoryName, setCategoryName] = React.useState("");

  // Use ResizeObserver to detect container size changes
  React.useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      const { width, height } = entries[0].contentRect;
      setContainerSize({ width, height });
    });

    resizeObserver.observe(containerRef.current);
    return () => resizeObserver.disconnect();
  }, []);

  // Calculate appropriate pie chart size based on container dimensions
  const calculateChartSize = () => {
    if (!containerSize.width || !containerSize.height)
      return { outerRadius: noCard ? 120 : 100 };

    // Use the smaller dimension (width or height) to determine radius
    // For larger containers, use a higher percentage of the available space
    const minDimension = Math.min(containerSize.width, containerSize.height);
    const scaleFactor = minDimension > 400 ? 0.45 : 0.35; // Use 45% for larger containers
    const outerRadius = Math.max(minDimension * scaleFactor, 80); // Increased minimum size to 80px

    return { outerRadius };
  };

  const { outerRadius } = calculateChartSize();

  // Check for empty data
  const isEmpty = !isLoading && (!data || data.length === 0);

  const chartContent = (
    <>
      {isLoading ? (
        <div className="space-y-2">
          <Skeleton className="h-[300px] w-full" />
        </div>
      ) : isEmpty ? (
        <ChartEmptyState
          height={noCard ? "100%" : 300}
          message="No assignment data available"
        />
      ) : (
        <div
          className="flex flex-col items-center justify-center w-full h-full"
          ref={containerRef}
        >
          <ResponsiveContainer
            width="100%"
            height={noCard ? "100%" : 300}
            debounce={50}
          >
            <PieChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
              <Pie
                data={data}
                dataKey="ticket_count"
                nameKey="agent_name"
                cx="50%"
                cy="50%"
                outerRadius={outerRadius}
                stroke="none"
                // Removed the label prop that was creating the lines
                onClick={(data) => {
                  if (data.tickets && data.tickets.length > 0) {
                    setSelectedTickets(data.tickets as TicketSummary[]);
                    setCategoryName(`${data.agent_name}'s`);
                    setIsDialogOpen(true);
                  }
                }}
                style={{ cursor: 'pointer' }}
              >
                {data?.map((entry, index) => (
                  <Cell
                    key={entry.agent_name}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>

          {/* Custom legend component below the chart */}
          {data && data.length > 0 && (
            <div className="mt-2">
              <CustomLegend
                payload={data.map((entry, index) => ({
                  color: COLORS[index % COLORS.length],
                  payload: entry,
                }))}
              />
            </div>
          )}
        </div>
      )}
    </>
  );

  if (error) {
    const errorContent = (
      <div className="h-[300px] flex items-center justify-center text-destructive">
        {error.message}
      </div>
    );

    if (noCard) {
      return errorContent;
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle>Assignment distribution</CardTitle>
        </CardHeader>
        <CardContent>{errorContent}</CardContent>
      </Card>
    );
  }

  if (noCard) {
    return (
      <div className="h-full w-full flex flex-col items-center justify-center">
        {chartContent}
        <TicketDetailsDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          tickets={selectedTickets}
          categoryName={categoryName}
          hideAssignee={true}
        />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Assignment distribution</CardTitle>
      </CardHeader>
      <CardContent>{chartContent}</CardContent>
      <TicketDetailsDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        tickets={selectedTickets}
        categoryName={categoryName}
        hideAssignee={true}
      />
    </Card>
  );
}
