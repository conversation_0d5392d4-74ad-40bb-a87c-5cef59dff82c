import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { useDailyTicketTrend } from "@/hooks/Insights/useDailyTicketTrend";
import { BaseBarChart } from "../shared/BaseBarChart";
import { TicketSummary } from "../shared/TicketDetailsDialog";

interface DailyTicketTrendCardProps {
  teamId: string;
  noCard?: boolean;
}

// Chart colors from CSS variables
const CHART_COLORS = [
  "var(--chart-color-1)",
  "var(--chart-color-2)",
  "var(--chart-color-3)",
  "var(--chart-color-4)",
  "var(--chart-color-5)",
  "var(--chart-color-6)",
  "var(--chart-color-7)",
  "var(--chart-color-8)",
  "var(--chart-color-9)",
  "var(--chart-color-10)",
];

// Format date to display as day of week (e.g., Monday, Tuesday)
const formatDate = (dateString: string) => {
  // Create date object from the ISO string (which is in UTC)
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    weekday: "long",
  });
};

export function DailyTicketTrendCard({
  teamId,
  noCard = false,
}: DailyTicketTrendCardProps) {
  const { data, isLoading, error } = useDailyTicketTrend(teamId);

  // Format data for the chart and create categories
  const formattedData = data?.map((item) => {
    // Parse the date string to get a Date object
    // The date string from the API is in YYYY-MM-DD format
    const date = new Date(item.date + "T00:00:00Z"); // Force UTC timezone
    const dayIndex = date.getUTCDay(); // Use UTC day (0 for Sunday, 1 for Monday, etc.)
    return {
      name: formatDate(item.date),
      count: item.count,
      date: item.date,
      dayIndex: dayIndex, // Add day index for sorting
      tickets: item.tickets,
    };
  });

  // Define the order of days for sorting (0 = Sunday, 1 = Monday, etc.)
  const daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  // Create a map to aggregate counts and tickets by day of week
  const dayCountMap: Record<number, number> = {};
  const dayTicketsMap: Record<number, TicketSummary[]> = {};
  formattedData?.forEach((item) => {
    if (dayCountMap[item.dayIndex] === undefined) {
      dayCountMap[item.dayIndex] = 0;
      dayTicketsMap[item.dayIndex] = [];
    }
    dayCountMap[item.dayIndex] += item.count;
    if (item.tickets) {
      dayTicketsMap[item.dayIndex] = [
        ...dayTicketsMap[item.dayIndex],
        ...item.tickets,
      ];
    }
  });

  // Create sorted data with all days of the week
  const sortedData = daysOfWeek.map((day, index) => ({
    name: day,
    count: dayCountMap[index] || 0,
    dayIndex: index,
    tickets: dayTicketsMap[index] || [],
  }));

  // Create categories dynamically based on the sorted days
  const dailyCategories = daysOfWeek.map((day, index) => ({
    name: day,
    key: day,
    color: CHART_COLORS[index % CHART_COLORS.length],
    tickets: dayTicketsMap[index] || [],
  }));

  const chartContent = (
    <BaseBarChart
      data={sortedData ?? []}
      categories={dailyCategories}
      valueKey="count"
      nameKey="name"
      loading={isLoading}
      error={error?.message}
      height="100%"
      noCard={noCard}
      emptyStateMessage="No daily ticket data available"
    />
  );

  if (noCard) {
    return <div className="h-full w-full">{chartContent}</div>;
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle>Daily ticket trend</CardTitle>
      </CardHeader>
      <CardContent className="h-[calc(100%-60px)] w-full">
        {chartContent}
      </CardContent>
    </Card>
  );
}
