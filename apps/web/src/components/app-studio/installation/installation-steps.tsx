import { Badge } from "@/components/ui/badge";

interface InstallationStepsProps {
  currentStep: "review" | "settings" | "complete";
}

export function InstallationSteps({ currentStep }: InstallationStepsProps) {
  return (
    <div className="flex items-center justify-center gap-4 text-sm mt-8">
      <div className="flex items-center gap-2">
        <Badge variant={currentStep === "review" ? "default" : "secondary"}>
          1
        </Badge>
        <span
          className={
            currentStep === "review" ? "font-medium" : "text-muted-foreground"
          }
        >
          Review scopes
        </span>
      </div>
      <div className="h-px w-8 bg-border" />
      <div className="flex items-center gap-2">
        <Badge variant={currentStep === "settings" ? "default" : "secondary"}>
          2
        </Badge>
        <span
          className={
            currentStep === "settings" ? "font-medium" : "text-muted-foreground"
          }
        >
          Configure settings
        </span>
      </div>
      <div className="h-px w-8 bg-border" />
      <div className="flex items-center gap-2">
        <Badge variant={currentStep === "complete" ? "default" : "secondary"}>
          3
        </Badge>
        <span
          className={
            currentStep === "complete" ? "font-medium" : "text-muted-foreground"
          }
        >
          Complete
        </span>
      </div>
    </div>
  );
}
