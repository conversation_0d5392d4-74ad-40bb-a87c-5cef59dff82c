"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form as FormRoot,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { useInstallationFormStore } from "@/store/installation-form-store";
import { AppResponseDto } from "@/types/app-studio";
import { zodResolver } from "@hookform/resolvers/zod";
import { Check, ChevronsUpDown } from "lucide-react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

interface SettingsFormProps {
  app: AppResponseDto;
  type: "required" | "optional";
}

export function SettingsForm({ app, type }: SettingsFormProps) {
  const { setSettings, settings: savedSettings } = useInstallationFormStore();

  // Create a dynamic schema based on the manifest settings
  const createFormSchema = () => {
    const schemaFields: Record<string, z.ZodTypeAny> = {};

    const settings =
      type === "required"
        ? app.manifest.configuration.required_settings
        : app.manifest.configuration.optional_settings;

    settings?.forEach((setting) => {
      let schema: z.ZodTypeAny;

      switch (setting.type) {
        case "string":
          schema = setting.enum
            ? z.enum(setting.enum as [string, ...string[]])
            : z.string().min(1, { message: `${setting.label} is required` });
          break;

        case "boolean":
          schema = z.boolean();
          break;

        case "number":
          schema = z.number().refine(
            (val) => {
              if (
                setting.validation?.min !== undefined &&
                val < setting.validation.min
              )
                return false;
              if (
                setting.validation?.max !== undefined &&
                val > setting.validation.max
              )
                return false;
              return true;
            },
            {
              message:
                setting.validation?.min !== undefined &&
                setting.validation?.max !== undefined
                  ? `Value must be between ${setting.validation.min} and ${setting.validation.max}`
                  : setting.validation?.min !== undefined
                  ? `Value must be ≥ ${setting.validation.min}`
                  : `Value must be ≤ ${setting.validation!.max}`,
            },
          );
          break;

        case "multiselect":
          schema = z.array(z.string()).refine(
            (val) => {
              if (
                setting.validation?.minItems !== undefined &&
                val.length < setting.validation.minItems
              )
                return false;
              if (
                setting.validation?.maxItems !== undefined &&
                val.length > setting.validation.maxItems
              )
                return false;
              return true;
            },
            {
              message:
                setting.validation?.minItems !== undefined &&
                setting.validation?.maxItems !== undefined
                  ? `Must select between ${setting.validation.minItems} and ${setting.validation.maxItems} items`
                  : setting.validation?.minItems !== undefined
                  ? `Must select at least ${setting.validation.minItems} items`
                  : `Must select at most ${setting.validation!.maxItems} items`,
            },
          );
          break;

        case "singleselect":
          schema = z.string();
          break;

        case "radio":
          schema = z.string();
          break;

        default:
          schema = z.string();
      }

      schemaFields[setting.key] =
        type === "required" ? schema : schema.optional();
    });

    return z.object(schemaFields);
  };

  const formSchema = createFormSchema();

  // Create form with default values
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: savedSettings || {
      ...Object.fromEntries(
        (type === "required"
          ? app.manifest.configuration.required_settings
          : app.manifest.configuration.optional_settings
        )?.map((setting) => [
          setting.key,
          setting.type === "multiselect" ? [] : setting.default,
        ]) || [],
      ),
    },
  });

  // Update installation form state when form values change
  // Update form when values change
  useEffect(() => {
    const subscription = form.watch((value) => {
      setSettings({ ...savedSettings, ...value });
    });
    return () => subscription.unsubscribe();
  }, [form, setSettings, savedSettings]);

  const settings =
    type === "required"
      ? app.manifest.configuration.required_settings
      : app.manifest.configuration.optional_settings;

  if (!settings?.length) {
    return null;
  }

  return (
    <FormRoot {...form}>
      <form className="space-y-4">
        <div className="space-y-4">
          {settings.map((setting) => (
            <div key={setting.key} className="space-y-2">
              <FormField
                control={form.control}
                name={setting.key}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {setting.label}
                      {type === "required" && (
                        <span className="text-destructive ml-1">*</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      {(() => {
                        switch (setting.type) {
                          case "boolean":
                            return (
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            );

                          case "radio":
                            return (
                              <RadioGroup
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                className="flex flex-col space-y-0.5 mt-1"
                              >
                                {setting.enum?.map((value) => (
                                  <FormItem
                                    key={value}
                                    className="flex items-center space-x-3 space-y-0"
                                  >
                                    <FormControl>
                                      <RadioGroupItem value={value} />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      {value}
                                    </FormLabel>
                                  </FormItem>
                                ))}
                              </RadioGroup>
                            );

                          case "multiselect":
                            return (
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="outline"
                                    role="combobox"
                                    className={cn(
                                      "w-full justify-between",
                                      !field.value && "text-muted-foreground",
                                    )}
                                  >
                                    {field.value?.length > 0
                                      ? `${field.value.length} selected`
                                      : `Select ${setting.label.toLowerCase()}`}
                                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-full p-0">
                                  <Command>
                                    <CommandInput
                                      placeholder={`Search ${setting.label.toLowerCase()}...`}
                                    />
                                    <CommandEmpty>
                                      No option found.
                                    </CommandEmpty>
                                    <CommandGroup>
                                      {setting.enum?.map((value) => (
                                        <CommandItem
                                          key={value}
                                          onSelect={() => {
                                            const currentValues = Array.isArray(
                                              field.value,
                                            )
                                              ? field.value
                                              : [];
                                            const newValues =
                                              currentValues.includes(value)
                                                ? currentValues.filter(
                                                    (v) => v !== value,
                                                  )
                                                : [...currentValues, value];
                                            field.onChange(newValues);
                                          }}
                                        >
                                          <Check
                                            className={cn(
                                              "mr-2 h-4 w-4",
                                              Array.isArray(field.value) &&
                                                field.value.includes(value)
                                                ? "opacity-100"
                                                : "opacity-0",
                                            )}
                                          />
                                          {value}
                                        </CommandItem>
                                      ))}
                                    </CommandGroup>
                                  </Command>
                                </PopoverContent>
                              </Popover>
                            );

                          case "singleselect":
                            return (
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue
                                    placeholder={`Select ${setting.label.toLowerCase()}`}
                                  />
                                </SelectTrigger>
                                <SelectContent>
                                  {setting.enum?.map((value) => (
                                    <SelectItem key={value} value={value}>
                                      {value}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            );

                          case "number":
                            return (
                              <Input
                                {...field}
                                type="number"
                                step={setting.validation?.step ?? 1}
                                min={setting.validation?.min}
                                max={setting.validation?.max}
                                onChange={(e) =>
                                  field.onChange(
                                    Number.parseFloat(e.target.value),
                                  )
                                }
                              />
                            );

                          default:
                            return <Input {...field} />;
                        }
                      })()}
                    </FormControl>
                    <FormDescription>{setting.description}</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}
        </div>
      </form>
    </FormRoot>
  );
}
