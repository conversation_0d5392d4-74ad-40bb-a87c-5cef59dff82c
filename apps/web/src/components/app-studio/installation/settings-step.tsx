"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useInstallationFormStore } from "@/store/installation-form-store";
import { AppResponseDto } from "@/types/app-studio";
import { ChevronRight, Settings2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { SettingsForm } from "./settings-form";

interface SettingsStepProps {
  app: AppResponseDto;
}

export function SettingsStep({ app }: SettingsStepProps) {
  const router = useRouter();
  const { clearForm, settings } = useInstallationFormStore();

  const handleCancel = () => {
    clearForm();
    router.replace("/organization/settings/apps-studio");
  };

  const validateSettings = () => {
    const requiredSettings = app.manifest.configuration.required_settings || [];
    return requiredSettings.every((setting) => {
      const value = settings[setting.key];
      return value !== undefined && value !== "";
    });
  };

  const handleNext = () => {
    if (!validateSettings()) {
      toast.error("Please fill in all required settings before proceeding.");
      return;
    }
    // Use URLSearchParams for proper URL encoding
    const searchParams = new URLSearchParams();
    searchParams.set("step", "complete");

    router.replace(
      `/organization/settings/apps-studio/${
        app.uid
      }/install?${searchParams.toString()}`,
    );
  };

  const renderEmptySettingsSection = (type: "required" | "optional") => (
    <div className="flex flex-col items-center justify-center py-8 text-center border rounded-sm bg-muted/5">
      <div className="rounded-full bg-muted p-3">
        <Settings2 className="h-6 w-6 text-muted-foreground" />
      </div>
      <h4 className="mt-4 text-base font-medium">No {type} settings</h4>
      <p className="mt-2 text-sm text-muted-foreground max-w-sm">
        {type === "required"
          ? "This app doesn't have any required configuration settings."
          : "This app doesn't have any optional configuration settings."}
      </p>
    </div>
  );

  const hasRequiredSettings =
    app.manifest.configuration.required_settings?.length > 0;
  const hasOptionalSettings =
    app.manifest.configuration.optional_settings?.length > 0;

  return (
    <div className="flex flex-col h-[calc(100vh-300px)] max-w-[640px] mx-auto w-full">
      {/* Scrollable Settings Section */}
      <div className="flex-1 overflow-y-auto mt-8">
        <div className="rounded-sm border bg-card">
          <div className="border-b px-4 py-4">
            <h3 className="text-base font-semibold">App settings</h3>
            <p className="text-sm text-muted-foreground">
              Configure the required and optional settings for{" "}
              {app.manifest.app.name}
            </p>
          </div>

          <div className="px-4 py-4 space-y-6">
            {/* Required Settings Section */}
            <div>
              <h4 className="text-base font-medium mb-4">Required Settings</h4>
              {!hasRequiredSettings ? (
                renderEmptySettingsSection("required")
              ) : (
                <SettingsForm app={app} type="required" />
              )}
            </div>

            {/* Optional Settings Section */}
            <div>
              <h4 className="text-base font-medium mb-4">Optional Settings</h4>
              {!hasOptionalSettings ? (
                renderEmptySettingsSection("optional")
              ) : (
                <SettingsForm app={app} type="optional" />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Footer Section */}
      <div className="border-t bg-background py-6">
        <div className="flex justify-end gap-3">
          <Button
            onClick={handleCancel}
            variant="outline"
            size="lg"
            className="px-3"
          >
            Cancel
          </Button>
          <Button
            onClick={handleNext}
            size="lg"
            className="px-3"
            disabled={hasRequiredSettings && !validateSettings()}
          >
            Complete installation
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
