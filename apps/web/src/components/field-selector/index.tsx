import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import capitalize from "lodash/capitalize";
import toLower from "lodash/toLower";
import { ChevronDown } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { AnnotatorField, getAnnotatorMetadata } from "../../types/annotators";
import { FieldInput } from "../field-input";

interface FieldSelectorProps {
  entityType: string;
  onChange: (value: {
    field: string;
    operator: string;
    value: string | number | boolean;
    label: string;
  }) => void;
  teamId?: string;
  initialValue?: {
    field: string;
    operator: string;
    value: string | number | boolean;
  };
  insideModal?: boolean;
}

interface TransformedField {
  label: string;
  value: string;
  type: string;
  supportedOperators: { name: string; value: string }[];
  constraints?: AnnotatorField["constraints"];
  fields?: Record<string, TransformedField>;
  relationshipValue?: string;
  isLoading?: boolean;
}

interface CustomField {
  id: string;
  name: string;
  fieldType: string;
  description: string;
  options: Array<{
    id: string;
    value: string;
    isDisabled: boolean;
    order: number;
  }>;
}

// Add a constant for fields to exclude
const EXCLUDED_FIELDS = [
  "deletedAt",
  "deleted_at",
  "customFieldValues",
  "id",
  "ticketId",
  "parentTeam",
  "team",
  "customerUser",
  "accounts",
  "contactType",
  "logo",
  "metadata",
  "avatarUrl",
  "aiGeneratedSummary",
  "aiGeneratedTitle",
];

const EXCLUDED_CUSTOM_FIELDS = [
  "calculated",
  "password",
  "regex",
  "file_upload",
];
const NarrowedDownFields = [
  "sentiment",
  "type",
  "assignedAgent",
  "priority",
  "status",
  "groups",
  "tags",
];

const NarrowedDownFieldsWithAccount = [
  "status",
  "accountOwner",
  "health",
  "classification",
  "industry",
];
const StandardFields = [
  "sentiment",
  "status",
  "priority",
  "groups",
  "tags",
  "type",
  "account",
  "customerContact",
  "assignedAgent",
];
const FieldSelector = ({
  entityType,
  onChange,
  className,
  teamId,
  initialValue,
  insideModal,
}: FieldSelectorProps & { className?: string }) => {
  const [fields, setFields] = useState<Record<string, TransformedField>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPath, setSelectedPath] = useState<string[]>([]);
  const [selectedOperator, setSelectedOperator] = useState<string>("");
  const [selectedValue, setSelectedValue] = useState<string>("");
  const [selectedFieldData, setSelectedFieldData] =
    useState<TransformedField | null>(null);
  const hasLoadedInitialRelatedFields = useRef(false);

  // Check if we're in the Slack sources page
  const isSlackSourcesPage =
    typeof window !== "undefined" &&
    window.location.pathname.includes("/settings/sources/slack");

  const getFieldLabel = (key: string, field: TransformedField) => {
    if (key === "ticket" && isSlackSourcesPage) {
      return "Standard fields";
    }
    return field.label === "Assigned Agent"
      ? "Assignee"
      : capitalize(toLower(field.label));
  };

  const fetchFields = async (type: string, relations: string[] = []) => {
    try {
      const response = await getAnnotatorMetadata(type, relations, teamId);
      return transformFields(response.data.fields, response.data.customFields);
    } catch (err) {
      console.error(`Error fetching fields for ${type}:`, err);
      throw err;
    }
  };
  const getNarrowedDownFieldValue = (
    key: string,
    field: AnnotatorField | TransformedField,
  ) => {
    if (NarrowedDownFieldsWithAccount.includes(key)) {
      if (key === "status") {
        return field.constraints?.relatedEntityType === "TicketStatus"
          ? `name`
          : `value`;
      } else if (key === "accountOwner") {
        return `name`;
      }
      return `value`;
    }
    return `name`;
  };

  const transformFields = (
    fields: Record<string, AnnotatorField>,
    customFields: CustomField[] = [],
    parentPath: string[] = [],
  ): Record<string, TransformedField> => {
    // First transform regular fields
    const transformedRegularFields = Object.entries(fields).reduce(
      (acc, [key, field]) => {
        // Skip if the field is in excluded list or exists in parent path
        if (EXCLUDED_FIELDS.includes(key) || parentPath.includes(key)) {
          return acc;
        }

        // Special handling for narrowed down fields
        if (
          NarrowedDownFields.includes(key) ||
          NarrowedDownFieldsWithAccount.includes(key)
        ) {
          acc[key] = {
            label: field.label,
            value: `${key}.${getNarrowedDownFieldValue(key, field)}`,
            type: field.type,
            supportedOperators: Array.isArray(field.supportedOperators)
              ? field.supportedOperators
              : [],
            constraints: field.constraints,
            fields: undefined,
          };
          return acc;
        }

        acc[key] = {
          label: field.label,
          value: key,
          type: field.type,
          supportedOperators: Array.isArray(field.supportedOperators)
            ? field.supportedOperators
            : [],
          constraints: field.constraints,
          fields:
            field.type === "lookup"
              ? undefined
              : field.fields
              ? transformFields(field.fields, [], [...parentPath, key])
              : undefined,
        };
        return acc;
      },
      {} as Record<string, TransformedField>,
    );
    let transformedCustomFields: Record<string, TransformedField>;
    // Then transform custom fields if they exist
    if (customFields.length > 0) {
      transformedCustomFields = customFields.reduce(
        (acc, field) => {
          if (EXCLUDED_CUSTOM_FIELDS.includes(field.fieldType)) {
            return acc;
          }
          const customFieldKey = `${field.name}`;

          // Map fieldType to appropriate type
          const fieldTypeMap: Record<string, string> = {
            checkbox: "choice",
            multi_choice: "choice",
            rich_text: "text",
            integer: "number",
            decimal: "number",
            radio: "choice",
          };

          acc[customFieldKey] = {
            label: field.name,
            value: customFieldKey,
            type: fieldTypeMap[field.fieldType] || field.fieldType,
            supportedOperators: [
              { name: "is Equal To", value: "=" },
              { name: "is Not Equal To", value: "!=" },
            ],
            constraints:
              field.options?.length > 0
                ? {
                    options: field.options.map((opt) => ({
                      label: opt.value,
                      value: opt.value,
                    })),
                    dynamicChoices: [],
                    lookupType: "manyToOne",
                    _lookupType: "lookupType",
                    relatedEntityType: "CustomField",
                    _relatedEntityType: "relatedEntityType",
                  }
                : undefined,
          };
          return acc;
        },
        {} as Record<string, TransformedField>,
      );
    }
    // Get standard fields and non-standard fields
    const standardFieldsToShow = Object.keys(transformedRegularFields).filter(
      (field) => StandardFields.includes(field),
    );
    const nonStandardFields = Object.keys(transformedRegularFields).filter(
      (field) => !StandardFields.includes(field),
    );

    // Create separate objects for standard fields and non-standard fields
    const standardFieldsObj = standardFieldsToShow.reduce(
      (acc, key) => {
        acc[key] = transformedRegularFields[key];
        return acc;
      },
      {} as Record<string, TransformedField>,
    );

    const nonStandardFieldsObj = nonStandardFields.reduce(
      (acc, key) => {
        acc[key] = transformedRegularFields[key];
        return acc;
      },
      {} as Record<string, TransformedField>,
    );

    // If we're on the Slack sources page, put custom fields at root level
    if (isSlackSourcesPage) {
      return {
        ...standardFieldsObj,
        ticket: {
          label: "Standard fields",
          value: "ticket",
          type: "group",
          supportedOperators: [],
          fields: nonStandardFieldsObj,
        },
        customFields: {
          label: "Custom Fields",
          value: "customFields",
          type: "group",
          supportedOperators: [],
          fields: transformedCustomFields,
        },
      };
    }

    // Otherwise, nest custom fields inside ticket
    return {
      ...standardFieldsObj,
      ticket: {
        label: "Ticket",
        value: "ticket",
        type: "group",
        supportedOperators: [],
        fields: {
          ...nonStandardFieldsObj,
          customFields: {
            label: "Custom Fields",
            value: "customFields",
            type: "group",
            supportedOperators: [],
            fields: transformedCustomFields,
          },
        },
      },
    };
  };

  useEffect(() => {
    // Reset state when entityType changes
    setSelectedPath([]);
    setSelectedOperator("");
    setSelectedValue("");
    setSelectedFieldData(null);
    setFields({}); // Clear existing fields when entityType changes

    const initializeFields = async () => {
      try {
        setLoading(true);
        setError(null);
        const transformedFields = await fetchFields(entityType);
        setFields(transformedFields);

        // If we have initial values and they match the current entityType, set the field data
        if (initialValue?.field) {
          const path = initialValue.field.split(".");
          let current = transformedFields;
          let fieldData: TransformedField | null = null;

          // First try to find the field in standard fields
          if (current[path[0]]) {
            fieldData = current[path[0]];
            current = current[path[0]].fields || {};
          }
          // If not found in standard fields, try to find in ticket group
          else if (current.ticket?.fields?.[path[0]]) {
            fieldData = current.ticket.fields[path[0]];
            current = current.ticket.fields[path[0]].fields || {};
          }

          // Navigate through the rest of the path
          for (let i = 1; i < path.length; i++) {
            if (current[path[i]]) {
              fieldData = current[path[i]];
              current = current[path[i]].fields || {};
            }
          }

          if (fieldData) {
            // If the field was found in ticket group, prepend 'ticket' to the path
            const wasTicketField =
              transformedFields.ticket?.fields?.[path[0]] !== undefined;
            const finalPath = wasTicketField ? ["ticket", ...path] : path;

            setSelectedPath(finalPath);
            setSelectedOperator(initialValue.operator);
            setSelectedValue(String(initialValue.value));
            setSelectedFieldData(fieldData);
          }
        }
      } catch (err) {
        console.error("Error fetching fields:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch fields");
        setFields({}); // Clear fields on error
      } finally {
        setLoading(false);
      }
    };

    initializeFields();
  }, [entityType]); // Only depend on entityType changes

  // Separate effect to handle initialValue changes without re-fetching fields
  useEffect(() => {
    if (
      initialValue &&
      fields &&
      Object.keys(fields).length > 0 &&
      !hasLoadedInitialRelatedFields.current
    ) {
      const path = initialValue.field.split(".");
      let current = fields;
      let fieldData: TransformedField | null = null;

      for (const part of path) {
        if (current[part]) {
          fieldData = current[part];
          current = current[part].fields || {};
        }
      }

      if (fieldData) {
        setSelectedPath(path);
        setSelectedOperator(initialValue.operator);
        setSelectedValue(String(initialValue.value));
        setSelectedFieldData(fieldData);

        // If the initial field is nested, fetch related fields for the parent
        if (path.length > 1) {
          const parentPath = path.slice(0, -1);
          loadRelatedFields(parentPath);
          hasLoadedInitialRelatedFields.current = true;
        }
      }
    }
  }, [initialValue, fields]);

  const loadRelatedFields = async (path: string[]) => {
    setFields((prev) => {
      const newFields = { ...prev };
      let current = newFields;

      for (let i = 0; i < path.length - 1; i++) {
        if (!current[path[i]]) return prev;
        current = current[path[i]].fields || {};
      }

      const lastKey = path[path.length - 1];
      if (!current[lastKey]) return prev;

      current[lastKey] = {
        ...current[lastKey],
        isLoading: true,
      };

      return newFields;
    });

    try {
      const relations =
        path[0] === "ticket" ? [path.slice(1).join(".")] : [path.join(".")];
      const response = await getAnnotatorMetadata(
        entityType,
        relations,
        teamId,
      );

      let targetFields = response.data.fields;
      let targetCustomFields: CustomField[] = [];
      for (const part of path) {
        targetCustomFields = targetFields[part]?.customFields || []; // Expect array
        targetFields = targetFields[part]?.fields || {};
      }
      let transformedFields = {};
      transformedFields = Object.entries(targetFields).reduce(
        (acc, [key, field]) => {
          // Skip if the field is in excluded list or matches any parent path

          if (EXCLUDED_FIELDS.includes(key) || path.includes(key)) {
            return acc;
          }

          acc[key] = {
            label: field.label,

            value: key,

            type: field.type,

            supportedOperators: Array.isArray(field.supportedOperators)
              ? field.supportedOperators
              : [],

            constraints: field.constraints,

            fields:
              field.type === "lookup"
                ? undefined
                : field.fields
                ? transformFields(field.fields, [], [...path, key])
                : undefined,
          };

          return acc;
        },

        {} as Record<string, TransformedField>,
      );

      // Then transform custom fields if they exist
      if (targetCustomFields.length > 0) {
        const transformedCustomFields = targetCustomFields.reduce(
          (acc, field) => {
            if (EXCLUDED_CUSTOM_FIELDS.includes(field.fieldType)) {
              return acc;
            }
            const customFieldKey = `${field.name}`;

            // Map fieldType to appropriate type
            const fieldTypeMap: Record<string, string> = {
              checkbox: "choice",
              multi_choice: "choice",
              rich_text: "text",
              integer: "number",
              decimal: "number",
              radio: "choice",
            };

            acc[customFieldKey] = {
              label: field.name,
              value: customFieldKey,
              type: fieldTypeMap[field.fieldType] || field.fieldType,
              supportedOperators: [
                { name: "is Equal To", value: "=" },
                { name: "is Not Equal To", value: "!=" },
              ],
              constraints:
                field.options?.length > 0
                  ? {
                      options: field.options.map((opt) => ({
                        label: opt.value,
                        value: opt.value,
                      })),
                      dynamicChoices: [],
                      lookupType: "manyToOne",
                      _lookupType: "lookupType",
                      relatedEntityType: "CustomField",
                      _relatedEntityType: "relatedEntityType",
                    }
                  : undefined,
            };
            return acc;
          },
          {} as Record<string, TransformedField>,
        );

        // Merge regular and custom fields
        transformedFields = {
          ...transformedFields,
          customFields: {
            label: "Custom Fields",
            value: "customFields",
            type: "group",
            supportedOperators: [],
            fields: transformedCustomFields,
          },
        };
      }

      setFields((prev) => {
        const newFields = { ...prev };
        let current = newFields;

        for (let i = 0; i < path.length - 1; i++) {
          if (!current[path[i]]) return prev;
          current = current[path[i]].fields || {};
        }

        const lastKey = path[path.length - 1];
        if (!current[lastKey]) return prev;

        current[lastKey] = {
          ...current[lastKey],
          fields: transformedFields,
          isLoading: false,
        };

        return newFields;
      });

      // Special handling for narrowed down fields: set selectedFieldData to the .name field if present
      if (
        NarrowedDownFields.some((f) => path[0]?.startsWith(f)) ||
        NarrowedDownFieldsWithAccount.some((f) => path[0]?.startsWith(f)) ||
        path[0]?.startsWith("account")
      ) {
        // Find the .name field in the fetched fields
        const nameField = Object.values(targetFields).find(
          (f) =>
            f.label?.toLowerCase() === "name" ||
            f.label?.toLowerCase() === "value",
        );
        if (nameField) {
          setSelectedFieldData({
            label: nameField.label,
            value: getNarrowedDownFieldValue(path[0], nameField),
            type: nameField.type,
            supportedOperators: Array.isArray(nameField.supportedOperators)
              ? nameField.supportedOperators
              : [],
            constraints: nameField.constraints,
          });
        }
      }
    } catch (err) {
      console.error("Error loading related fields:", err);
    }
  };

  const renderFieldItems = (
    currentFields: { [key: string]: TransformedField },
    currentPath: string[] = [],
  ) => {
    // Sort keys: standard fields (except account/customerContact), then account, then customerContact, then others
    const keys = Object.keys(currentFields) as string[];
    const standard = keys.filter(
      (k) =>
        StandardFields.includes(k) &&
        k !== "account" &&
        k !== "customerContact",
    );
    const account = keys.includes("account") ? ["account"] : [];
    const customerContact = keys.includes("customerContact")
      ? ["customerContact"]
      : [];
    const others = keys.filter(
      (k) =>
        !standard.includes(k) &&
        !account.includes(k) &&
        !customerContact.includes(k),
    );
    const orderedKeys = [
      ...standard,
      ...others,
      ...account,
      ...customerContact,
    ];

    return orderedKeys.map((key) => {
      const field = currentFields[key];
      const newPath = [...currentPath, key];
      const hasPresetChoices =
        field.constraints?.options?.length > 0 ||
        field.constraints?.dynamicChoices?.length > 0;

      // Special handling for narrowed down fields
      if (
        NarrowedDownFields.includes(key) ||
        NarrowedDownFieldsWithAccount.includes(key)
      ) {
        return (
          <DropdownMenuItem
            key={`${key}.${
              NarrowedDownFieldsWithAccount.includes(key) ? "value" : "name"
            }`}
            onClick={() => {
              handleFieldSelect(
                [...newPath, getNarrowedDownFieldValue(key, field)],
                field,
              );
              loadRelatedFields(newPath);
            }}
            className="flex items-center gap-2 px-2 py-1.5 text-sm outline-none cursor-default
                     focus:bg-accent hover:bg-accent"
          >
            <span className="flex-grow truncate">
              {getFieldLabel(key, field)}
            </span>
          </DropdownMenuItem>
        );
      }

      if (
        (field.type === "lookup" || field.type === "group") &&
        !hasPresetChoices
      ) {
        return (
          <DropdownMenuSub key={key}>
            <DropdownMenuSubTrigger
              className="flex items-center gap-2 px-2 py-1.5 text-sm outline-none cursor-default
                       focus:bg-accent data-[state=open]:bg-accent"
              onClick={() => {
                if (!field.fields) {
                  loadRelatedFields(newPath);
                }
              }}
            >
              <span className="flex-grow truncate">
                {getFieldLabel(key, field)}
              </span>
              {field.isLoading ? (
                <span className="ml-auto animate-spin">⌛</span>
              ) : null}
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent className="min-w-[8rem] max-h-[300px] overflow-y-auto">
              {field.fields ? (
                renderFieldItems(field.fields, newPath)
              ) : (
                <DropdownMenuItem disabled className="text-muted-foreground">
                  Load data
                </DropdownMenuItem>
              )}
            </DropdownMenuSubContent>
          </DropdownMenuSub>
        );
      }

      return (
        <DropdownMenuItem
          key={key}
          onClick={() => handleFieldSelect(newPath, field)}
          className="flex items-center gap-2 px-2 py-1.5 text-sm outline-none cursor-default
                   focus:bg-accent hover:bg-accent"
        >
          <span className="flex-grow truncate">
            {getFieldLabel(key, field)}
          </span>
        </DropdownMenuItem>
      );
    });
  };

  const handleFieldSelect = (path: string[], field: TransformedField) => {
    if (path[0] === "ticket") {
      setSelectedPath(path.slice(1));
      setSelectedFieldData(field);
      setSelectedOperator("");
      setSelectedValue("");
    } else {
      setSelectedPath(path);
      setSelectedFieldData(field);
      setSelectedOperator("");
      setSelectedValue("");
    }

    onChange({
      field: path[0] === "ticket" ? path.slice(1).join(".") : path.join("."),
      operator: "",
      value: "",
      label: field.label,
    });
  };

  const handleOperatorChange = (operatorValue: string) => {
    setSelectedOperator(operatorValue);
    onChange({
      field: selectedPath.join("."),
      operator: operatorValue,
      value: selectedValue,
      label: selectedFieldData?.label,
    });
  };

  const handleValueChange = (value: string) => {
    setSelectedValue(value);
    onChange({
      field: selectedPath.join("."),
      operator: selectedOperator,
      value,
      label: selectedFieldData?.label,
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-10 text-sm text-muted-foreground">
        <span className="animate-spin mr-2">⌛</span>
        Loading fields...
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-10 text-sm text-destructive">
        <span className="mr-2">⚠️</span>
        {error}
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col space-y-4", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-between font-normal"
          >
            <span className="truncate">
              {selectedPath.length > 0
                ? selectedPath[0] === "ticket"
                  ? selectedPath.slice(1).join(".")
                  : selectedPath.join(".")
                : "Select field"}
            </span>
            <ChevronDown className="ml-2 h-4 w-4 opacity-50 shrink-0" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-72 max-h-[300px] overflow-y-auto"
          align="start"
        >
          <DropdownMenuLabel className="font-normal text-muted-foreground">
            Select Field
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div className="overflow-y-auto">{renderFieldItems(fields)}</div>
        </DropdownMenuContent>
      </DropdownMenu>

      {selectedFieldData && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between font-normal"
            >
              <span className="truncate">
                {selectedOperator || "Select operator"}
              </span>
              <ChevronDown className="ml-2 h-4 w-4 opacity-50 shrink-0" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="start">
            <DropdownMenuLabel className="font-normal text-muted-foreground">
              Select Operator
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {selectedFieldData.supportedOperators?.map((operator, index) => (
              <DropdownMenuItem
                key={`${operator.value}-${index}`}
                onClick={() => handleOperatorChange(operator.value)}
                className="flex items-center gap-2 px-2 py-1.5 text-sm"
              >
                {operator.name}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {selectedOperator && (
        <div className="relative">
          <FieldInput
            type={selectedFieldData?.type || "string"}
            operator={selectedOperator}
            value={selectedValue}
            onChange={handleValueChange}
            choices={
              selectedFieldData?.constraints?.dynamicChoices?.length > 0
                ? selectedFieldData?.constraints?.dynamicChoices
                : selectedFieldData?.constraints?.options
            }
            className="w-full h-10 px-3 py-2 text-sm rounded-md border border-input bg-background 
               focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring 
               disabled:cursor-not-allowed disabled:opacity-50"
            insideModal={insideModal}
          />
        </div>
      )}
    </div>
  );
};

export default FieldSelector;
