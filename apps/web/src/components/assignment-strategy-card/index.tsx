"use client";

import { RoutingConfig } from "@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/routing/types";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { ArrowRight, Info } from "lucide-react";
import { useRouter } from "next/navigation";

interface AssignmentStrategyCardProps {
  routingConfig: RoutingConfig | null;
  isSaving?: boolean;
  onUpdateConfig: (updates: Partial<RoutingConfig>) => void;
  showGroupsInfo?: boolean;
  teamId: string;
  title?: string;
  description?: string;
  readOnly?: boolean;
  showInfo?: boolean;
  routingRoute?: string;
}

export const AssignmentStrategyCard = ({
  routingConfig,
  isSaving = false,
  onUpdateConfig,
  showGroupsInfo = false,
  teamId,
  title = "Assignment logic",
  description = "Configure basic routing behavior",
  readOnly = false,
  showInfo = true,
  routingRoute,
}: AssignmentStrategyCardProps) => {
  const router = useRouter();

  const handleStrategyChange = (value: string) => {
    onUpdateConfig({
      userRoutingStrategy: value,
    });
  };
  return (
    <Card className="border-none shadow-none outline-none pt-0">
      <CardHeader className="!px-0">
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
        <Select
          value={routingConfig?.userRoutingStrategy || ""}
          disabled={isSaving}
          onValueChange={handleStrategyChange}
        >
          <SelectTrigger className="w-[180px]" disabled={readOnly}>
            <SelectValue placeholder="Select strategy">
              {routingConfig?.userRoutingStrategy === "manual"
                ? "Manual"
                : routingConfig?.userRoutingStrategy === "round_robin"
                ? "Round robin"
                : <span className="text-muted-foreground">Select strategy</span>}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="manual">Manual</SelectItem>
            <SelectItem value="round_robin">Round robin</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="space-y-6 !px-0">
        {routingConfig &&
          routingConfig?.userRoutingStrategy &&
          routingConfig?.userRoutingStrategy !== "manual" && (
            <>
              <div>
                <h3 className="font-medium">Assignment factors</h3>
                <p className="text-sm text-muted-foreground">
                  Select the different user preferences to factor in during
                  assignment
                </p>
              </div>
              <div className="space-y-4">
                <div className="flex items-center">
                  <Checkbox
                    id="timezone"
                    checked={true} // TODO: keeping it default selected for now
                    // onCheckedChange={(checked) =>
                    //   onUpdateConfig({
                    //     routingRespectsUserTimezone: checked as boolean,
                    //   })
                    // }
                    onCheckedChange={() => {}}
                    disabled
                    className="mr-2"
                  />
                  <label
                    htmlFor="timezone"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Timezone
                  </label>
                </div>

                <div className="flex items-center">
                  <Checkbox
                    id="availability"
                    checked={true} // TODO: keeping it default selected for now
                    // onCheckedChange={(checked) =>
                    //   onUpdateConfig({
                    //     routingRespectsUserAvailability: checked as boolean,
                    //   })
                    // }
                    onCheckedChange={() => {}}
                    disabled
                    className="mr-2"
                  />
                  <label
                    htmlFor="availability"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Availability
                  </label>
                </div>

                {/* Working hours checkbox - disabled until BE changes are implemented */}
                <div className="flex items-center">
                  <Checkbox
                    id="working-hours"
                    checked={true} // TODO: keeping it default selected for now
                    onCheckedChange={() => {}}
                    disabled
                    className="mr-2"
                  />
                  <label
                    htmlFor="working-hours"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Working hours
                  </label>

                </div>

                {/* Capacity checkbox - disabled until BE changes are implemented */}
                <div className="flex items-center">
                  <Checkbox
                    id="capacity"
                    checked={true} // TODO: keeping it default selected for now
                    onCheckedChange={() => {}}
                    disabled
                    className="mr-2"
                  />
                  <label
                    htmlFor="capacity"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Capacity
                  </label>

                </div>
              </div>
              {showInfo && <Separator />}
            </>
          )}
        {showInfo ? (
          showGroupsInfo ? (
            <div className="text-sm bg-[var(--color-bg-info-muted)] p-4 rounded-[6px]">
              <div className="flex flex-col">
                <div className="text-color-text-info font-medium">
                  Want more control over ticket routing?
                </div>
                <p className="text-color-text-info font-normal mt-1">
                  Create groups to enable specific routing rules based on
                  ticket properties, skills, or regions. Groups help you
                  ensure the right tickets reach the right team members.
                </p>
                <div className="flex justify-end mt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex items-center"
                    onClick={() =>
                      router.push(`/dashboard/${teamId}/settings/groups`)
                    }
                  >
                    Create groups <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-sm bg-accent p-4 rounded-md">
              <div className="flex items-start gap-2 text-foreground">
                <Info size={20} className="text-primary" />
                <div>
                  Configure how tickets are distributed to this group&apos;s
                  agents. To set up rules for routing tickets between groups,
                  visit 
                  <span
                    onClick={() =>
                      router.push(`/dashboard/${routingRoute}/settings/routing`)
                    }
                    role="button"
                    className="underline hover:bg-transparent p-0 cursor-pointer"
                  >
                    Team Settings → Routing
                  </span>
                </div>
              </div>
            </div>
          )
        ) : null}
      </CardContent>
    </Card>
  );
};
