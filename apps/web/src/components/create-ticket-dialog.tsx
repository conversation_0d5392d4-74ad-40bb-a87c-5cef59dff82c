"use client";

import {
  Condition,
  TargetField,
  TargetFieldActionType,
  TargetFieldConditionsType,
} from "@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/forms/components/conditions-section";
import { PreviewPanel } from "@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/forms/components/preview-panel";
import { useFormBuilder } from "@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/forms/hooks/use-form-builder";
import { HeroIcon } from "@/components/hero-icon";
import ThenaLoader from "@/components/thena-loader";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useFormsStore } from "@/store/forms-store";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { FormType } from "@/types/forms";
import { ThenaTicket } from "@/types/kanban";
import { ChevronRight, Loader2, Plus } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import * as React from "react";
import { toast } from "sonner";

interface CreateTicketDialogProps {
  open?: boolean;
  setOpen?: (open: boolean) => void;
  onTicketCreated?: (ticket: ThenaTicket) => void;
  accountId?: string;
}

export function CreateTicketDialog({
  open: externalOpen,
  setOpen: setExternalOpen,
  onTicketCreated,
  accountId: _accountId,
}: CreateTicketDialogProps) {
  const router = useRouter();
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const [internalOpen, setInternalOpen] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);
  const [selectedForm, setSelectedForm] = React.useState<FormType | null>(null);
  const [selectedTeamId, setSelectedTeamId] = React.useState<string | null>(
    null,
  );
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const {
    setConditions,
    setFieldValue,
    evaluateConditions,
    setConditionOrder,
    setSelectedFields,
    selectedFields,
    fieldValues,
    initialFieldStates,
  } = useFormBuilder();

  // Use external state if provided, otherwise use internal state
  const open = externalOpen !== undefined ? externalOpen : internalOpen;
  const setOpen = setExternalOpen || setInternalOpen;

  const params = useParams<{ teamId: string }>();
  const teams = useTicketMetaStore((state) => state.teams);
  const { getTeamForms, setTeamForms } = useFormsStore();

  // Set initial team ID from params or first available team
  React.useEffect(() => {
    if (!selectedTeamId && teams.length > 0) {
      const initialTeamId = params.teamId || teams[0].uid;
      setSelectedTeamId(initialTeamId);
    }
  }, [teams, params.teamId, selectedTeamId]);

  // Transform form fields to match PreviewPanel's expected format
  const transformFormFields = (form: FormType) => {
    return form.fields
      .filter((field) => field.meta.accessibleInTicketCreationForm !== false)
      .map((field) => {
        // Get the field state from selectedFields
        const fieldState = selectedFields.find((f) => f.id === field.field);

        return {
          id: field.field,
          name: field.meta.name,
          description: field.meta.description || "",
          fieldType: field.meta.type || field.fieldType,
          mandatoryOnCreation: fieldState
            ? fieldState.mandatoryOnCreation
            : field.mandatoryOnCreation,
          mandatoryOnClose: fieldState
            ? fieldState.mandatoryOnClose
            : field.mandatoryOnClose,
          visibleToCustomer: fieldState
            ? fieldState.visibleToCustomer
            : field.visibleToCustomer,
          editableByCustomer: fieldState
            ? fieldState.editableByCustomer
            : field.editableByCustomer,
          options: field.meta.options || undefined,
          isStandard: false as const,
          source: "form",
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
      });
  };

  // Transform conditions to match PreviewPanel's expected format
  const transformConditions = (form: FormType) => {
    const conditionsMap = new Map<string, Condition>();
    const conditionIds: string[] = [];

    // Safely handle undefined conditions
    if (!form.conditions || !Array.isArray(form.conditions)) {
      console.log("No conditions found for form:", form.id);
      return { conditionsMap, conditionIds };
    }

    form.conditions.forEach((condition, index) => {
      const conditionId = `condition-${index + 1}`;
      conditionIds.push(conditionId);

      // Find the field and its options
      const field = form.fields.find(
        (f) => f.field === condition.triggerFieldId,
      );
      console.log("Found trigger field:", {
        fieldId: condition.triggerFieldId,
        field,
        options: field?.meta.options,
        triggerValue: condition.triggerFieldValue?.[0],
      });

      condition.targetFields.forEach((targetField) => {
        const transformedTargetField: TargetField = {
          id: targetField.id,
          fieldId: targetField.id,
          type: targetField.type as TargetFieldActionType,
          value: targetField.value,
        };

        // Find the option value that corresponds to the condition's trigger value
        const optionValue = field?.meta.options?.find(
          (opt) => opt.id === condition.triggerFieldValue?.[0],
        )?.value;

        console.log("Option matching:", {
          triggerFieldId: condition.triggerFieldId,
          triggerValue: condition.triggerFieldValue?.[0],
          matchingOption: field?.meta.options?.find(
            (opt) => opt.id === condition.triggerFieldValue?.[0],
          ),
          allOptions: field?.meta.options,
          optionValue,
        });

        const transformedCondition: Condition = {
          id: conditionId,
          triggerFieldId: condition.triggerFieldId,
          conditionType: condition.conditionType as TargetFieldConditionsType,
          // Use the option's value instead of its ID to match PreviewPanel's behavior
          value: optionValue || condition.triggerFieldValue?.[0],
          targetFields: [transformedTargetField],
          isExpanded: true,
        };

        console.log("Final condition:", {
          original: {
            triggerFieldId: condition.triggerFieldId,
            triggerValue: condition.triggerFieldValue?.[0],
            conditionType: condition.conditionType,
          },
          transformed: {
            triggerFieldId: transformedCondition.triggerFieldId,
            value: transformedCondition.value,
            conditionType: transformedCondition.conditionType,
          },
        });

        conditionsMap.set(conditionId, transformedCondition);
      });
    });

    return { conditionsMap, conditionIds };
  };

  // Set initial field values for condition evaluation
  const setInitialFieldValues = (form: FormType) => {
    form.fields.forEach((field) => {
      // Set empty default values based on field type
      let defaultValue: string | number | boolean | string[] | null = null;

      switch (field.meta.type || field.fieldType) {
        case "single_choice":
          defaultValue = null;
          break;
        case "multi_choice":
          defaultValue = [];
          break;
        case "boolean":
        case "toggle":
          defaultValue = false;
          break;
        default:
          defaultValue = "";
      }

      console.log("Setting initial value for field:", {
        fieldId: field.field,
        fieldType: field.meta.type || field.fieldType,
        defaultValue,
        options: field.meta.options,
      });

      setFieldValue(field.field, defaultValue);
    });
  };

  // Fetch forms when team changes
  React.useEffect(() => {
    if (open && selectedTeamId) {
      const fetchForms = async () => {
        setIsLoading(true);
        setError(null);
        try {
          // Check if we already have forms for this team
          const existingForms = getTeamForms(selectedTeamId);
          if (existingForms.length > 0) {
            console.log("Using cached forms for team:", selectedTeamId);
            const defaultForm =
              existingForms.find((form) => form.default) || existingForms[0];
            if (defaultForm) {
              setSelectedForm(defaultForm);
              const transformedFields = transformFormFields(defaultForm);
              setSelectedFields(transformedFields);
              const { conditionsMap, conditionIds } =
                transformConditions(defaultForm);
              setConditions(conditionsMap);
              setConditionOrder(conditionIds);
              setInitialFieldValues(defaultForm);
              evaluateConditions();
            }
            setIsLoading(false);
            return;
          }

          console.log("Fetching forms for team:", selectedTeamId);
          const response = await fetch(
            `/api/forms?teamId=${selectedTeamId}&onlyTeamForms=true&page=0&limit=50`,
          );
          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.message || "Failed to fetch forms");
          }

          const activeForms = data.data.results.filter(
            (form: FormType) => form.isActive,
          );

          // Store forms in the forms store
          setTeamForms(selectedTeamId, activeForms);
          console.log("Stored forms for team:", selectedTeamId, activeForms);

          // Find default form or use first active form
          const defaultForm =
            activeForms.find((form) => form.default) || activeForms[0];

          if (defaultForm) {
            console.log("Setting form:", defaultForm);
            setSelectedForm(defaultForm);

            // Transform and set fields
            const transformedFields = transformFormFields(defaultForm);
            console.log("Setting fields:", transformedFields);
            setSelectedFields(transformedFields);

            // Transform and set conditions
            console.log("Original conditions:", defaultForm.conditions);
            const { conditionsMap, conditionIds } =
              transformConditions(defaultForm);
            console.log("Transformed conditions:", conditionsMap);
            console.log("Condition IDs:", conditionIds);

            setConditions(conditionsMap);
            setConditionOrder(conditionIds);

            // Set initial field values and evaluate conditions
            setInitialFieldValues(defaultForm);
            evaluateConditions();
          } else {
            setSelectedForm(null);
          }
        } catch (error) {
          console.error("Error fetching forms:", error);
          setError(
            error instanceof Error ? error.message : "Failed to fetch forms",
          );
        } finally {
          setIsLoading(false);
        }
      };

      fetchForms();
    }
  }, [
    open,
    selectedTeamId,
    setTeamForms,
    getTeamForms,
    setConditions,
    setFieldValue,
    evaluateConditions,
    setConditionOrder,
    setSelectedFields,
  ]);

  // Reset fields to their initial states
  const resetFieldsToInitialState = React.useCallback(() => {
    if (!selectedForm) return;

    // Update fields with their initial states
    const updatedFields = selectedFields.map((field) => {
      const initialState = initialFieldStates.get(field.id);
      if (initialState) {
        return {
          ...field,
          ...initialState,
        };
      }
      return field;
    });

    setSelectedFields(updatedFields);
  }, [selectedForm, selectedFields, initialFieldStates, setSelectedFields]);

  // Reset state when dialog closes
  React.useEffect(() => {
    if (!open) {
      setSelectedForm(null);
      setError(null);
      resetFieldsToInitialState();
    }
  }, [open, resetFieldsToInitialState]);

  const handleFormSubmit = async (formData: Record<string, unknown>) => {
    if (!selectedForm || !selectedTeamId) return;

    // Find the requester field ID from the form configuration
    const requesterField = selectedForm.fields.find(
      (field) => field.meta.name.toLowerCase() === "requester",
    );

    const requesterFieldId = requesterField?.field;
    const requesterEmail = requesterFieldId
      ? (formData[requesterFieldId] as string)
      : "";
    // Get accessible fields first
    const accessibleFields = selectedForm.fields.filter(
      (field) => field.meta.accessibleInTicketCreationForm !== false,
    );

    // Validation errors array
    const validationErrors: string[] = [];

    // Validate required fields and field types
    for (const field of selectedFields) {
      // Find the original field to check if it's accessible
      const originalField = accessibleFields.find((f) => f.field === field.id);
      if (!originalField) continue;

      const value = formData[field.id];

      // Required field validation
      if (field.mandatoryOnCreation) {
        if (value === undefined || value === null || value === "") {
          validationErrors.push(`${field.name} is required`);
          continue;
        }
        if (Array.isArray(value) && value.length === 0) {
          validationErrors.push(`${field.name} is required`);
          continue;
        }
      }

      // Skip type validation if field is empty and not required
      if (value === undefined || value === null || value === "") continue;

      // Type validation based on field type
      const fieldType = originalField.meta.type || originalField.fieldType;
      switch (fieldType) {
        case "email":
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (typeof value === "string" && !emailRegex.test(value)) {
            validationErrors.push(
              `${field.name} must be a valid email address`,
            );
          }
          break;
        case "number":
          if (typeof value === "string" && isNaN(Number(value))) {
            validationErrors.push(`${field.name} must be a valid number`);
          }
          break;
        case "url":
          try {
            if (typeof value === "string") {
              new URL(value);
            }
          } catch {
            validationErrors.push(`${field.name} must be a valid URL`);
          }
          break;
        case "date":
          if (typeof value === "string" && isNaN(Date.parse(value))) {
            validationErrors.push(`${field.name} must be a valid date`);
          }
          break;
      }
    }

    // Show validation errors if any
    if (validationErrors.length > 0) {
      toast.error(validationErrors.join("\n"));
      return;
    }

    setIsSubmitting(true);
    try {
      // Transform form data to match API requirements
      const transformedFormData = Object.entries(formData).reduce(
        (acc, [key, value]) => {
          const field = accessibleFields.find((f) => f.field === key);
          if (!field) return acc;

          // Skip fields that are not accessible in ticket creation form or not editable by customer
          if (
            field.meta.accessibleInTicketCreationForm === false ||
            !field.editableByCustomer
          ) {
            return acc;
          }

          // For custom fields (non thena_restricted), use the field ID and format according to API requirements
          if (field.fieldType !== "thena_restricted") {
            // If field has options, we need to send the option ID
            if (field.meta.options) {
              // For multi-select fields
              if (Array.isArray(value)) {
                acc[`custom_${field.field}`] = value
                  .map((v) => {
                    const option = field.meta.options?.find(
                      (opt) => opt.value === v,
                    );
                    return option?.id || v;
                  })
                  .filter(Boolean);
              } else {
                // For single-select fields
                const option = field.meta.options?.find(
                  (opt) => opt.value === value,
                );
                acc[`custom_${field.field}`] = option?.id || value;
              }
            } else {
              // For fields without options, use the value as is
              acc[`custom_${field.field}`] = value;
            }
            return acc;
          }

          // For standard fields (thena_restricted), use the field name
          // If field has options, send the option ID instead of value
          if (field.meta.options) {
            if (Array.isArray(value)) {
              acc[field.meta.name.toLowerCase()] = value
                .map((v) => {
                  const option = field.meta.options?.find(
                    (opt) => opt.value === v,
                  );
                  return option?.id || v;
                })
                .filter(Boolean);
            } else {
              const option = field.meta.options?.find(
                (opt) => opt.value === value,
              );
              acc[field.meta.name.toLowerCase()] = option?.id || value;
            }
          } else {
            acc[field.meta.name.toLowerCase()] = value;
          }
          return acc;
        },
        {} as Record<string, unknown>,
      );

      // Prepare the request body
      const requestBody = {
        formId: selectedForm.id,
        teamId: selectedTeamId,
        requestorEmail: requesterEmail || currentUser?.email || "",
        submitterEmail: currentUser?.email || "", // The person creating the ticket (logged in user)
        formData: transformedFormData,
      };
      // Make API call to create ticket using the new route
      const response = await fetch(`/api/tickets/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        credentials: "include",
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create ticket");
      }

      // Get ticket identifier using teamIdentifier and ticketId from response
      const ticketIdentifier = `${data.ticket.teamIdentifier}-${data.ticket.ticketId}`;
      const ticketUrl = `${window.location.origin}/dashboard/${data.ticket.teamId}?ticketId=${data.ticket.id}`;

      // Reset fields to initial state before showing success message
      resetFieldsToInitialState();

      toast.success(
        <div>
          Ticket created successfully:{" "}
          <a
            href={ticketUrl}
            className="text-primary hover:underline font-medium"
            target="_blank"
            rel="noopener noreferrer"
          >
            {ticketIdentifier}
          </a>
        </div>,
      );

      if (onTicketCreated) {
        onTicketCreated(data.ticket);
      }

      setOpen(false);
    } catch (error) {
      console.error("Error creating ticket:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create ticket",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get forms for the selected team
  const teamForms = selectedTeamId ? getTeamForms(selectedTeamId) : [];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[600px] p-0 gap-0">
        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <ThenaLoader loaderText="Fetching forms" />
            </div>
          ) : error ? (
            <div className="text-sm text-destructive">{error}</div>
          ) : (
            <div className="space-y-4">
              <div className="p-4 pb-0">
                <div className="flex items-center gap-2">
                  {/* Team Selector */}
                  <div className="max-w-[25%] min-w-fit">
                    <Select
                      value={selectedTeamId || ""}
                      onValueChange={(value) => {
                        // Reset fields to initial state before changing team
                        resetFieldsToInitialState();
                        setSelectedTeamId(value);
                      }}
                    >
                      <SelectTrigger className="w-full h-6 border-0 bg-muted/50 focus:ring-0 pr-7">
                        <SelectValue>
                          {selectedTeamId ? (
                            <div className="flex items-center gap-2">
                              <HeroIcon
                                name={
                                  teams.find((t) => t.uid === selectedTeamId)
                                    ?.icon || "RocketLaunchIcon"
                                }
                                className="h-3 w-3"
                                color={
                                  teams.find((t) => t.uid === selectedTeamId)
                                    ?.color
                                }
                              />
                              <span className="font-medium truncate">
                                {teams.find((t) => t.uid === selectedTeamId)
                                  ?.identifier || "Select Team"}
                              </span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">
                              Select Team
                            </span>
                          )}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {teams.map((team) => (
                          <SelectItem key={team.uid} value={team.uid}>
                            <div className="flex items-center gap-2">
                              <HeroIcon
                                name={team.icon || "RocketLaunchIcon"}
                                className="h-3 w-3"
                                color={team.color}
                              />
                              <span className="truncate">
                                {team.identifier}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Separator */}
                  <ChevronRight size={16} />

                  {/* Form Selector */}
                  {selectedTeamId && teamForms.length > 0 && (
                    <div className="max-w-[33.333333%] min-w-fit">
                      <Select
                        value={selectedForm?.id || ""}
                        onValueChange={(value) => {
                          const form = teamForms.find((f) => f.id === value);
                          if (form) {
                            // Reset fields to initial state before changing form
                            resetFieldsToInitialState();
                            setSelectedForm(form);
                            const transformedFields = transformFormFields(form);
                            setSelectedFields(transformedFields);
                            const { conditionsMap, conditionIds } =
                              transformConditions(form);
                            setConditions(conditionsMap);
                            setConditionOrder(conditionIds);
                            setInitialFieldValues(form);
                            evaluateConditions();
                          }
                        }}
                      >
                        <SelectTrigger className="w-full h-6 border-0 bg-muted/50 focus:ring-0 pr-7">
                          <SelectValue>
                            {selectedForm ? (
                              <span className="font-medium truncate">
                                {selectedForm.name}
                              </span>
                            ) : (
                              <span className="text-muted-foreground">
                                Select Form
                              </span>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {teamForms.map((form) => (
                            <SelectItem key={form.id} value={form.id}>
                              <div className="flex items-center gap-2">
                                <span className="truncate">{form.name}</span>
                                {form.default && (
                                  <span className="text-xs text-muted-foreground whitespace-nowrap">
                                    (Default)
                                  </span>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>

              {!selectedTeamId ? (
                <div className="text-sm text-muted-foreground text-center py-8">
                  Please select a team to continue.
                </div>
              ) : teamForms.length === 0 ? (
                <div className="flex flex-col items-center justify-center gap-4 py-8">
                  <div className="text-sm text-muted-foreground text-center">
                    No active forms found for this team.
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2"
                    onClick={() => {
                      router.push(
                        `/dashboard/${selectedTeamId}/settings/forms/create`,
                      );
                      setOpen(false);
                    }}
                  >
                    <Plus className="h-4 w-4" />
                    Create Form
                  </Button>
                </div>
              ) : selectedForm ? (
                <div className="!border-0">
                  <PreviewPanel
                    formName={selectedForm.name}
                    description={selectedForm.description}
                    fields={transformFormFields(selectedForm)}
                    mode="create"
                    showButtons={true}
                    customButtons={
                      <div className="flex items-center justify-end gap-3">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            resetFieldsToInitialState();
                            setOpen(false);
                          }}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          onClick={() => handleFormSubmit(fieldValues)}
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                              Creating...
                            </>
                          ) : (
                            "Create ticket"
                          )}
                        </Button>
                      </div>
                    }
                  />
                </div>
              ) : null}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
