"use client";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@radix-ui/react-popover";
import { useRef, useState } from "react";

import { LucideIcon } from "@/components/lucide-icon";
import TooltipWrapper from "@/components/tooltip-wrapper";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

type Props = {
  onSetLink: (link: string) => void;
  initialUrl?: string;
};

export const EditLink = ({ onSetLink, initialUrl }: Props) => {
  const [url, setUrl] = useState(initialUrl || "");
  const inputRef = useRef<HTMLInputElement>(null);

  // useEffect(() => {
  //   // Focus input when component mounts
  //   if (inputRef.current) {
  //     inputRef.current.focus();
  //     inputRef.current.select();
  //   }
  // }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url.trim()) {
      let finalUrl = url.trim();
      if (!finalUrl.startsWith('http://') && !finalUrl.startsWith('https://')) {
        finalUrl = `https://${finalUrl}`;
      }
      onSetLink(finalUrl);
    }
  };

  const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(event.target.value);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="flex items-center gap-2 relative z-30 border rounded-sm bg-popover shadow-md p-2"
    >
      <label className="flex items-center gap-2 p-2 rounded-sm cursor-text">
        <LucideIcon
          name="Link"
          className="flex-none text-black dark:text-white"
        />
        <Input
          ref={inputRef}
          type="url"
          className="flex-1 bg-transparent outline-none min-w-[12rem] text-black text-sm dark:text-white h-7"
          placeholder="Enter URL"
          value={url}
          onChange={onChange}
          autoFocus
        />
      </label>
      <Button size="sm" type="submit">
        Set link
      </Button>
    </form>
  );
};

const EditLinkPopover = ({ onSetLink, initialUrl }: Props) => {
  const [open, setOpen] = useState(false);

  const handleSetLink = (url: string) => {
    onSetLink(url);
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      // Reset the URL when closing
      onSetLink("");
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <TooltipWrapper tooltipContent="Add link" asChild>
        <PopoverTrigger asChild>
          <Button variant="ghost" size ="sm" className="p-0 px-2 h-7">
            <LucideIcon name="Link" />
          </Button>
        </PopoverTrigger>
      </TooltipWrapper>

      <PopoverContent className="w-max p-0 border-none" sideOffset={6}>
        <EditLink onSetLink={handleSetLink} initialUrl={initialUrl} />
      </PopoverContent>
    </Popover>
  );
};

export default EditLinkPopover;
