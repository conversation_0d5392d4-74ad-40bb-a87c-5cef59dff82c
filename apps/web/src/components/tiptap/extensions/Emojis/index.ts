import { CLOUNDFRONT_TIPTAP_EMOJIS } from '@/services/emoji'
import { Node } from '@tiptap/core'
import { ReactRenderer } from '@tiptap/react'
import Suggestion from '@tiptap/suggestion'
import tippy from 'tippy.js'
import { EmojiList } from './EmojiList'

interface EmojiItem {
  name: string
  emoji: string
  shortcodes: string[]
  tags: string[]
  group: string
  fallbackImage?: string
}

// Global cache for emojis data
let cachedEmojis: EmojiItem[] | null = null

// Function to fetch and cache emojis
const fetchAndCacheEmojis = async (): Promise<EmojiItem[]> => {
  if (cachedEmojis) {
    return cachedEmojis
  }

  try {
    const response = await fetch(CLOUNDFRONT_TIPTAP_EMOJIS)
    cachedEmojis = await response.json()
    return cachedEmojis
  } catch (error) {
    console.error('Error fetching emojis:', error)
    return []
  }
}

export const EmojiSuggestion = Node.create({
  name: 'emojiSuggestion',

  addOptions() {
    return {
      suggestion: {
        char: ':',
        allowSpaces: false,
        allowedPrefixes: [' ', '\n'],
        startOfLine: false,
        decorationTag: 'span',
        decorationClass: 'emoji-suggestion',
        minChars: 0,

        command: ({ editor, range, props }) => {
          editor
            .chain()
            .focus()
            .insertContentAt(range, props.emoji + ' ')
            .run()
        },

        items: async ({ query }) => {
          // For single colon with no text (:), don't show anything
          if (query === '') {
            return []
          }

          const emojis = await fetchAndCacheEmojis()

          // For single colon with text (:text), filter emojis
          const searchQuery = query.toLowerCase()
          return emojis
            .filter(item => {
              // Search in name
              if (item.name.toLowerCase().includes(searchQuery)) {
                return true
              }

              // Search in shortcodes
              if (item.shortcodes?.some(code => code.toLowerCase().includes(searchQuery))) {
                return true
              }

              // Search in tags
              if (item.tags?.some(tag => tag.toLowerCase().includes(searchQuery))) {
                return true
              }

              return false
            })
            .slice(0, 10)
        },

        render: () => {
          let component
          let popup

          const createPopup = (props) => {
            if (component) {
              component.destroy()
            }
            if (popup && popup[0]) {
              popup[0].destroy()
            }

            component = new ReactRenderer(EmojiList, {
              props,
              editor: props.editor,
            })

            if (props.clientRect) {
              popup = tippy('body', {
                getReferenceClientRect: props.clientRect,
                appendTo: () => document.body,
                content: component.element,
                showOnCreate: true,
                interactive: true,
                trigger: 'manual',
                placement: 'bottom-start',
                zIndex: 9999,
              })
            }
          }

          return {
            onStart: (props) => {
              if (props.query === '' || props.items.length === 0) {
                return
              }
              createPopup(props)
            },

            onUpdate(props) {
              if (props.query === '' || props.items.length === 0) {
                if (popup && popup[0]) {
                  popup[0].hide()
                }
                return
              }

              if (!popup || !component) {
                createPopup(props)
                return
              }

              if (component) {
                component.updateProps(props)
              }

              if (popup && popup[0] && props.clientRect) {
                popup[0].setProps({
                  getReferenceClientRect: props.clientRect,
                })
                popup[0].show()
              }
            },

            onKeyDown(props) {
              if (props.event.key === 'Escape') {
                if (popup && popup[0]) {
                  popup[0].hide()
                }
                return true
              }
              return component?.ref?.onKeyDown(props)
            },

            onExit() {
              if (popup && popup[0]) {
                popup[0].destroy()
                popup = null
              }
              if (component) {
                component.destroy()
                component = null
              }
            },
          }
        },
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        ...this.options.suggestion,
      }),
    ]
  },
})