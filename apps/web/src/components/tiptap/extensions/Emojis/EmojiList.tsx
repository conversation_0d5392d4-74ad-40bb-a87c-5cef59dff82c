"use client"

import { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react'

interface EmojiItem {
  name: string
  emoji: string
  shortcodes: string[]
  tags: string[]
  group: string
  fallbackImage?: string
}

interface EmojiListProps {
  items: EmojiItem[]
  command: (item: EmojiItem) => void
}

export const EmojiList = forwardRef(function EmojiList(props: EmojiListProps, ref) {
  const [selectedIndex, setSelectedIndex] = useState(0)


  const selectItem = useCallback(
    (index: number) => {
      const item = props.items[index]
      if (item) {
        props.command(item)
      }
    },
    [props],
  )

  const upHandler = useCallback(() => {
    if (props.items.length === 0) return
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length)
  }, [props.items.length, selectedIndex])

  const downHandler = useCallback(() => {
    if (props.items.length === 0) return
    setSelectedIndex((selectedIndex + 1) % props.items.length)
  }, [props.items.length, selectedIndex])

  const enterHandler = useCallback(() => {
    selectItem(selectedIndex)
  }, [selectItem, selectedIndex])

  useEffect(() => setSelectedIndex(0), [props.items])

  useImperativeHandle(
    ref,
    () => ({
      onKeyDown: (x: { event: KeyboardEvent }) => {
        if (x.event.key === 'ArrowUp') {
          upHandler()
          return true
        }

        if (x.event.key === 'ArrowDown') {
          downHandler()
          return true
        }

        if (x.event.key === 'Enter') {
          enterHandler()
          return true
        }

        return false
      },
    }),
    [upHandler, downHandler, enterHandler],
  )

  return (
    <div className="bg-background/95 backdrop-blur-xl border border-border rounded-xl shadow-lg p-1 max-h-80 overflow-y-auto min-w-64 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
      {props.items.length ? (
        props.items.map((item, index) => (
          <div
            role="option"
            tabIndex={0}
            onKeyDown={(e) => e.key === 'Enter' && selectItem(index)}
            className={`
              flex items-center gap-3 px-3 py-1.5 mx-1 cursor-pointer rounded-lg transition-all duration-150 ease-out
              ${index === selectedIndex
                ? 'bg-accent ring-accent-foreground/20 shadow-sm scale-[1.02] transform'
                : 'hover:bg-accent/50 hover:scale-[1.01] transform'
              }
            `}
            key={item.name}
            onClick={() => selectItem(index)}
          >
            <span className="text-xl leading-none flex-shrink-0 w-6 text-center">
              {item.fallbackImage ? (
                <img
                  src={item.fallbackImage}
                  alt={item.name}
                  className="w-6 h-6 object-contain"
                />
              ) : (
                item.emoji
              )}
            </span>
            <div className="flex flex-col">
              <span className="text-sm font-medium text-foreground/90 font-mono tracking-tight">
                :{item.name}:
              </span>
            </div>
          </div>
        ))
      ) : (
        <div className="flex items-center justify-center py-1.5 px-4">
          <span className="text-sm text-muted-foreground font-medium">No emojis found</span>
        </div>
      )}
    </div>
  )
}) 