import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef } from "@tanstack/react-table";
import { MoveDiagonal } from "lucide-react";
import { ColumnRenderer } from "../components/column-renderer";
import { ColumnConfig, ColumnDisplayType } from "../types/column-types";

interface GenerateColumnsOptions<T> {
  configs: Record<string, ColumnConfig>;
  onEdit?: (entity: T) => void;
  onDelete?: (row: T) => void;
  selectable?: boolean;
}

export function generateColumns<T>({
  configs,
  onEdit,
  onDelete, // selectable = true,
}: GenerateColumnsOptions<T>): ColumnDef<T>[] {
  const columns: ColumnDef<T>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <div className="w-[20px]">
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className="w-[20px]">
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "expand",
      header: () => <div className="w-[20px]" />,
      cell: ({ row }) => {
        return (
          <div className="w-[20px]">
            <Button
              variant="ghost"
              className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => onEdit?.(row.original)}
            >
              <span className="sr-only">Expand row</span>
              <MoveDiagonal className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableHiding: false,
    },
  ];

  // Filter out hidden columns and sort by preferred order
  const visibleConfigs = Object.entries(configs)
     
    .filter(([_, config]) => !config.hidden)
    .sort((a, b) => {
      const preferredOrder = [
        "name",
        "stage",
        "amount",
        "dealSize",
        "close_date",
      ];
      const aIndex = preferredOrder.indexOf(a[0]);
      const bIndex = preferredOrder.indexOf(b[0]);
      if (aIndex === -1 && bIndex === -1) return 0;
      if (aIndex === -1) return 1;
      if (bIndex === -1) return -1;
      return aIndex - bIndex;
    });

  // Add configured columns
  columns.push(
    ...visibleConfigs.map(([key, config]) => ({
      id: key,
      accessorKey:
        config.type === ColumnDisplayType.CALCULATED ? undefined : key,
      accessorFn:
        config.type === ColumnDisplayType.CALCULATED
          ? // @ts-expect-error fix types
            (row: T) => config.calculateValue(row)
          : undefined,
      header: config.label,
      cell: ({ row }) => (
        <ColumnRenderer
          config={config}
          value={
            config.type === ColumnDisplayType.CALCULATED
              ? // @ts-expect-error fix types
                config.calculateValue(row.original)
              : row.getValue(key)
          }
          row={row.original}
        />
      ),
      enableSorting: config.sortable,
      enableColumnFilter: config.filterable,
    })),
  );

  // Add action column if edit or delete handlers are provided
  if (onEdit || onDelete) {
    columns.push({
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {onEdit && (
            <button
              onClick={() => onEdit(row.original)}
              className="text-primary hover:text-primary-dark"
            >
              Edit
            </button>
          )}
          {onDelete && (
            <button
              onClick={() => onDelete(row.original)}
              className="text-destructive hover:text-destructive-dark"
            >
              Delete
            </button>
          )}
        </div>
      ),
    });
  }

  return columns;
}
