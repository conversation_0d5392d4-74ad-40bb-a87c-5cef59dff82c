import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
} from "../ui/dialog";

type Tprops = {
  title: string;
  description?: string;
  okText: string;
  onOk: () => void;
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  loading?: boolean;
  variant?:
    | "destructive"
    | "outline"
    | "ghost"
    | "link"
    | "secondary"
    | "default";
};

const WarningModal = ({
  title,
  description,
  okText,
  onOk,
  isOpen,
  setIsOpen,
  loading = false,
  variant = "destructive",
}: Tprops) => {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent>
        <DialogTitle>{title}</DialogTitle>
        <DialogDescription className="test-sm text-muted-text">
          {description}
        </DialogDescription>
        <DialogFooter>
          <Button
            variant="ghost"
            onClick={(e) => {
              setIsOpen(false);
              e.stopPropagation();
            }}
          >
            Cancel
          </Button>
          <Button
            loading={loading}
            variant={variant}
            className="bg-[hsl(var(--destructive))]"
            onClick={(e) => {
              onOk();
              e.stopPropagation();
            }}
          >
            {okText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WarningModal;
