import { cn } from "@/lib/utils";
import { getInitials } from "@/utils/kanban";
import Image, { StaticImageData } from "next/image";

// Helper function to proxy external images
const getProxiedImageUrl = (url: string) => {
  if (!url) return url;

  // Check if it's an external URL that might have CORS issues (like Slack)
  if (
    url.includes("slack-edge.com") ||
    url.includes("avatars.slack-edge.com")
  ) {
    return `/api/proxy/image?url=${encodeURIComponent(url)}`;
  }

  return url;
};

type AvatarProps = {
  src?: string;
  alt?: string;
  fallbackText?: string;
  imgClassnames?: string;
  fallbackTextClassnames?: string;
  fallbackImage?: string | StaticImageData;
};

const Avatar = ({
  src,
  alt,
  fallbackText,
  imgClassnames,
  fallbackTextClassnames,
  fallbackImage = "http://res.cloudinary.com/dqjkjie4a/image/upload/v1737537155/ma-editor/pb91kghgfnvfgamfxaem.png",
}: AvatarProps) => {
  const handleError = (e) => {
    e.currentTarget.src =
      typeof fallbackImage === "string"
        ? getProxiedImageUrl(fallbackImage)
        : fallbackImage;
  };
  return (
    <div className={cn("flex justify-center items-center rounded-md")}>
      {src ? (
        <img
          height={5}
          width={5}
          src={getProxiedImageUrl(src)}
          alt={alt}
          className={cn("object-cover w-9 h-9 rounded-md", imgClassnames)}
          onError={handleError}
          crossOrigin="anonymous"
          // quality={100}
          // unoptimized
        />
      ) : fallbackImage &&
        typeof fallbackImage === "string" &&
        fallbackImage !==
          "http://res.cloudinary.com/dqjkjie4a/image/upload/v1737537155/ma-editor/pb91kghgfnvfgamfxaem.png" ? (
        <img
          height={5}
          width={5}
          src={getProxiedImageUrl(fallbackImage)}
          alt={alt || "avatar"}
          className={cn("object-cover w-9 h-9 rounded-md", imgClassnames)}
          onError={(e) => {
            const target = e.currentTarget as HTMLImageElement;
            target.style.display = "none";
            // If image fails to load, show initials as fallback
            const nextElement = target.parentElement?.querySelector("div");
            if (nextElement) {
              (nextElement as HTMLDivElement).style.display = "flex";
            }
          }}
          crossOrigin="anonymous"
        />
      ) : !fallbackText ? (
        <Image
          height={5}
          width={5}
          src={fallbackImage}
          alt={alt || "default"}
          className={cn("object-cover w-9 h-9 rounded-md", imgClassnames)}
          quality={100}
          // unoptimized
        />
      ) : (
        <div
          className={cn(
            "w-9 h-9 rounded-md border border-border bg-muted text-foreground flex items-center justify-center text-xs",
            imgClassnames,
            fallbackTextClassnames,
          )}
          style={{
            display:
              fallbackImage &&
              typeof fallbackImage === "string" &&
              fallbackImage !==
                "http://res.cloudinary.com/dqjkjie4a/image/upload/v1737537155/ma-editor/pb91kghgfnvfgamfxaem.png"
                ? "none"
                : "flex",
          }}
        >
          {getInitials(fallbackText)}
        </div>
      )}
    </div>
  );
};

export default Avatar;
