export const TYPESENSE_CONFIG = {
  SEARCH_ONLY_API_KEY_TICKETS_AND_COMMENTS:
    process.env.TYPESENSE_SEARCH_KEY_TICKETS_AND_COMMENTS!,
  SEARCH_ONLY_API_KEY_ACCOUNTS: process.env.TYPESENSE_SEARCH_KEY_ACCOUNTS!,
  HOST: process.env.NEXT_PUBLIC_TYPESENSE_HOST!,
  PORT: process.env.NEXT_PUBLIC_TYPESENSE_PORT || "443",
  PROTOCOL: process.env.NEXT_PUBLIC_TYPESENSE_PROTOCOL || "https",
  MULTI_SEARCH_ENDPOINT: "/multi_search",
} as const;

// Helper function to create Typesense node config
export const getTypesenseNodeConfig = () => ({
  host: TYPESENSE_CONFIG.HOST,
  port: parseInt(TYPESENSE_CONFIG.PORT),
  protocol: TYPESENSE_CONFIG.PROTOCOL,
});

// Helper function to get Typesense multi-search URL
export const getTypesenseMultiSearchUrl = () => {
  return `${TYPESENSE_CONFIG.PROTOCOL}://${TYPESENSE_CONFIG.HOST}:${TYPESENSE_CONFIG.PORT}${TYPESENSE_CONFIG.MULTI_SEARCH_ENDPOINT}`;
};

// Helper function to get Typesense search URL for a specific collection
export const getTypesenseSearchUrl = (collection: string) => {
  return `${process.env.NEXT_PUBLIC_BASE_URL}/v1/search/${collection}`;
};