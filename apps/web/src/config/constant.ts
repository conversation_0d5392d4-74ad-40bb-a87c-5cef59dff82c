export const BASE_URL =
  process.env.NEXT_PUBLIC_BASE_URL || "https://platform.thena.tools";

export const BASE_URL_APP = process.env.NEXT_PUBLIC_BASE_URL_APP || "";

export const AUTH_URL = process.env.NEXT_PUBLIC_AUTH_URL || "";

export const SLA_URL = process.env.NEXT_PUBLIC_SLA_URL || "";

export const APPS_PLATFORM_URL =
  process.env.NEXT_PUBLIC_APPS_PLATFORM_URL || "";

export const TIPTAP_AI_APP_ID = process.env.NEXT_PUBLIC_TIPTAP_AI_APP_ID || "";

export const WORKFLOWS_URL = process.env.NEXT_PUBLIC_WORKFLOWS_URL || "";

export const HUBSPOT_GET_SETTINGS_ACTIVITY =
  process.env.NEXT_PUBLIC_HUBSPOT_GET_SETTINGS_ACTIVITY || "";

export const HUBSPOT_UPDATE_SETTINGS_ACTIVITY =
  process.env.NEXT_PUBLIC_HUBSPOT_UPDATE_SETTINGS_ACTIVITY || "";

export const HUBSPOT_SEARCH_CUSTOM_FIELDS_ACTIVITY =
  process.env.NEXT_PUBLIC_HUBSPOT_SEARCH_CUSTOM_FIELDS_ACTIVITY || "";

export const HUBSPOT_GET_SYNC_STATUS_ACTIVITY =
  process.env.NEXT_PUBLIC_HUBSPOT_GET_SYNC_STATUS_ACTIVITY || "";

export const HUBSPOT_TRIGGER_SYNC_ACTIVITY =
  process.env.NEXT_PUBLIC_HUBSPOT_TRIGGER_SYNC_ACTIVITY || "";

export const APP_STUDIO_URL = process.env.NEXT_PUBLIC_APP_STUDIO_URL || "";

export const THENA_SLACK_APP_URL =
  process.env.NEXT_PUBLIC_THENA_SLACK_APP_URL || "";

export const THENA_MS_TEAMS_APP_URL =
  process.env.NEXT_PUBLIC_THENA_MS_TEAMS_APP_URL || "";

export const KB_URL = process.env.NEXT_PUBLIC_KB_URL || "";

export const TIPTAP_APP_ID = process.env.NEXT_PUBLIC_TIPTAP_APP_ID || "";

export const KB_DEFAULT_DOMAIN =
  process.env.NEXT_PUBLIC_KB_DEFAULT_DOMAIN || "";

export const KB_PUBLIC_URL = process.env.NEXT_PUBLIC_KB_PUBLIC_URL || "";

export const CLOUDINARY_CLOUDNAME =
  process.env.NEXT_PUBLIC_CLOUDINARY_CLOUDNAME || "";

export const THENA_ANNOTATOR_URL =
  process.env.NEXT_PUBLIC_THENA_ANNOTATOR_URL || "";

export const KNOCK_PUBLIC_API_KEY = process.env.NEXT_PUBLIC_KNOCK_API_KEY || "";

export const KNOCK_FEED_CHANNEL_ID =
  process.env.NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID || "";

export const EMAIL_SERVICE_URL =
  process.env.NEXT_PUBLIC_EMAIL_SERVICE_URL || "";

export const KNOCK_TOAST_CHANNEL_ID =
  process.env.NEXT_PUBLIC_KNOCK_TOAST_CHANNEL_ID || "";

export const LC_CLIENT_KEY = process.env.NEXT_PUBLIC_LC_CLIENT_KEY || "";

export const JIRA_API_URL = process.env.NEXT_PUBLIC_JIRA_API_URL || "";
