"use client";

import { AuthLayout } from "@/components/auth-layout";
import { But<PERSON> } from "@/components/ui/button";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { Eye, EyeOff, Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { setOrgDetails } from "../../utils/browserUtils";
import { login, signInWithGoogleIdToken } from "../actions/auth";

// Move handleGoogleOAuth declaration before component and make it global
if (typeof window !== "undefined") {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (window as any).handleGoogleOAuth = async (response: {
    credential: string;
    isAuthCode?: boolean;
  }) => {
    // If it's an auth code, we need to handle it differently
    if (response.isAuthCode) {
      try {
        // Create a server-side API endpoint to handle the token exchange
        const tokenResponse = await fetch("/api/google-token", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ code: response.credential }),
        });

        if (!tokenResponse.ok) {
          const errorData = await tokenResponse.json();
          throw new Error(
            errorData.message || "Failed to exchange code for token",
          );
        }

        const tokenData = await tokenResponse.json();
        // Extract email from the token payload
        let email = null;
        if (tokenData.id_token) {
          try {
            // Decode the JWT to get the email
            const payload = JSON.parse(atob(tokenData.id_token.split(".")[1]));
            email = payload.email;
          } catch (e) {
            console.error("Error decoding token:", e);
          }
        }

        // Now use the ID token from the response to authenticate
        const result = await signInWithGoogleIdToken(tokenData.id_token, email);
        if (!result.success) {
          if (
            result.error.message === "domain_check_failed" ||
            result.error.message === "unauthorized_domain"
          ) {
            window.location.href = `/signup-error?error=${result.error.message}`;
            return;
          }
        }
        const error = result.error;
        const data = result.data.response;
        if (error) {
          console.error("Error during sign-in:", error.message);
          toast.error("Authentication failed: " + error.message);
          return;
        }
        if (data) {
          const userData = data.user;
          if (userData) {
            useGlobalConfigPersistStore.getState().dispatch({
              type: "SET_CURRENT_USER",
              payload: {
                currentUser: {
                  id: userData.id,
                  email: userData.email,
                  name: userData.name,
                  metadata: userData.userMetadata,
                  uid: userData.currentUserUid || null,
                  avatarUrl: userData.avatarUrl || null,
                  allUserIds: userData.allUserIds || [],
                },
              },
            });
            useGlobalConfigPersistStore.getState().dispatch({
              type: "SET_ORGS",
              payload: {
                orgs: userData.allOrgs || [],
                currentOrgId: userData.currentOrgId || null,
              },
            });
            useGlobalConfigPersistStore.getState().dispatch({
              type: "SET_CURRENT_USER_UID",
              payload: {
                uid: userData.currentUserUid || null,
              },
            });
            useGlobalConfigPersistStore.getState().dispatch({
              type: "SET_CURRENT_ORG",
              payload: {
                currentOrgId: userData.currentOrgId || null,
              },
            });
            const orgUid = userData.allOrgs.find(
              (org) => org.id === userData.currentOrgId,
            )?.orgId;
            setOrgDetails(userData.currentOrgId, orgUid);
          }
          setOrgDetails(data.user.currentOrgId, data.user.orgId);
          window.location.href = "/dashboard";
        }
      } catch (error) {
        console.error("Authentication error:", error);
        toast.error("Authentication failed. Please try again.");
      }
    } else {
      let email = null;
      try {
        // Decode the JWT to get the email
        const payload = JSON.parse(atob(response.credential.split(".")[1]));
        email = payload.email;
      } catch (e) {
        console.error("Error decoding token:", e);
      }

      // Handle regular ID token flow
      const result = await signInWithGoogleIdToken(response.credential, email);
      if (!result.success) {
        if (
          result.error.message === "domain_check_failed" ||
          result.error.message === "unauthorized_domain"
        ) {
          window.location.href = `/signup-error?error=${result.error.message}`;
          return;
        }
      }
      const error = result.error;
      const data = result.data.response;
      if (error) console.error("Error during sign-in:", error.message);
      if (data) {
        const userData = data.user;
        if (userData) {
          useGlobalConfigPersistStore.getState().dispatch({
            type: "SET_CURRENT_USER",
            payload: {
              currentUser: {
                id: userData.id,
                email: userData.email,
                name: userData.name,
                metadata: userData.userMetadata,
                uid: userData.currentUserUid || null,
                avatarUrl: userData.avatarUrl || null,
                allUserIds: userData.allUserIds || [],
              },
            },
          });
          useGlobalConfigPersistStore.getState().dispatch({
            type: "SET_ORGS",
            payload: {
              orgs: userData.allOrgs || [],
              currentOrgId: userData.currentOrgId || null,
            },
          });
          useGlobalConfigPersistStore.getState().dispatch({
            type: "SET_CURRENT_USER_UID",
            payload: {
              uid: userData.currentUserUid || null,
            },
          });
          useGlobalConfigPersistStore.getState().dispatch({
            type: "SET_CURRENT_ORG",
            payload: {
              currentOrgId: userData.currentOrgId || null,
            },
          });
          const orgUid = userData.allOrgs.find(
            (org) => org.id === userData.currentOrgId,
          )?.orgId;
          setOrgDetails(userData.currentOrgId, orgUid);
        }
        setOrgDetails(data.user.currentOrgId, data.user.orgId);
        window.location.href = "/dashboard";
      }
    }
  };
}

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);
  const [showPassword, setShowPassword] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const result = await login(email, password);
      if (
        result.error === "Authentication failed" &&
        result.message === "Email not confirmed"
      ) {
        setOrgDetails(null, null);
        router.push("/signup/verify");
        return;
      }
      if (!result.success || !result.data?.user) {
        throw new Error(result.message || "Invalid response from server");
      }
      const userData = result.data.user;
      if (userData) {
        dispatch({
          type: "SET_CURRENT_USER",
          payload: {
            currentUser: {
              id: userData.id,
              email: userData.email,
              name: userData.name,
              metadata: userData.userMetadata,
              uid: userData.currentUserUid || null,
              avatarUrl: userData.avatarUrl || null,
              allUserIds: userData.allUserIds || [],
            },
          },
        });
        dispatch({
          type: "SET_ORGS",
          payload: {
            orgs: userData.allOrgs || [],
            currentOrgId: userData.currentOrgId || null,
          },
        });
        dispatch({
          type: "SET_CURRENT_USER_UID",
          payload: {
            uid: userData.currentUserUid || null,
          },
        });
        dispatch({
          type: "SET_CURRENT_ORG",
          payload: {
            currentOrgId: userData.currentOrgId || null,
          },
        });
        const orgUid = userData.allOrgs.find(
          (org) => org.id === userData.currentOrgId,
        )?.orgId;
        setOrgDetails(userData.currentOrgId, orgUid);

        // Get search settings and store them
        try {
          const searchSettingsResponse = await fetch(
            `/api/search/settings?orgId=${userData.currentOrgId}`,
          );
          if (searchSettingsResponse.ok) {
            const searchSettings = await searchSettingsResponse.json();
            dispatch({
              type: "SET_SEARCH_KEYS",
              payload: {
                ticketsAndComments: searchSettings.ticketsAndComments,
                accounts: searchSettings.accounts,
                teams: searchSettings.teams,
              },
            });
          }
        } catch (error) {
          console.error("Error fetching search settings:", error);
        }
      }
      router.push("/dashboard");
    } catch (error: unknown) {
      console.error("Login error:", error);
      toast.error(
        (error as Error)?.message || "Failed to login. Please try again.",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = () => {
    setGoogleLoading(true);
    try {
      // Check if Google Sign-In SDK is loaded
      if (!window.google?.accounts) {
        throw new Error(
          "Google Sign-In SDK not loaded. Please refresh the page and try again.",
        );
      }

      // Check if client ID is configured
      if (!process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID) {
        throw new Error("Google client ID is not configured.");
      }

      // Helper function to handle OAuth flow
      const initiateOAuthFlow = () => {
        if (window.google?.accounts?.oauth2) {
          const client = window.google.accounts.oauth2.initCodeClient({
            client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
            scope: "email profile",
            ux_mode: "popup",
            callback: (response) => {
              if (response.code) {
                // Pass the code with a flag indicating it's an auth code
                window.handleGoogleOAuth?.({
                  credential: response.code,
                  isAuthCode: true,
                });
                setGoogleLoading(false);
              } else {
                setGoogleLoading(false);
              }
            },
            error_callback: (error) => {
              console.error("OAuth error:", error);
              if (
                error.type !== "popup_closed" &&
                error.type !== "popup_blocked"
              ) {
                toast.error("Google Sign-In failed. Please try again.");
              }
              setGoogleLoading(false);
            },
          });

          // Launch the popup
          client.requestCode();
        } else {
          toast.error(
            "Google Sign-In is not available. Please try another method.",
          );
          setGoogleLoading(false);
        }
      };

      // First try One Tap sign-in
      window.google.accounts.id.initialize({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
        callback: (response) => {
          if (response.credential) {
            window.handleGoogleOAuth?.({
              credential: response.credential,
            });
            setGoogleLoading(false);
          }
        },
        cancel_on_tap_outside: false,
        error_callback: (error) => {
          console.log("One Tap error:", error);
          initiateOAuthFlow();
        },
      });

      // Try to display One Tap UI
      window.google.accounts.id.prompt((notification) => {
        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
          console.log(
            "One Tap not displayed or skipped:",
            notification.isNotDisplayed()
              ? notification.getNotDisplayedReason()
              : notification.getSkippedReason(),
          );

          initiateOAuthFlow();
        } else {
          // If One Tap is displayed but user doesn't interact, reset loading state after timeout
          setTimeout(() => {
            if (googleLoading) {
              setGoogleLoading(false);
            }
          }, 5000);
        }
      });
    } catch (error) {
      console.error("Google Sign-In error:", error);
      toast.error(
        error instanceof Error ? error.message : "An unexpected error occurred",
      );
      setGoogleLoading(false);
    }
  };

  return (
    <AuthLayout>
      {/* Login Container with Sign Up Link */}
      <div className="relative w-[496px] px-4">
        <div className="w-[496px] p-8 bg-background border border-border rounded-sm">
          <div>
            {/* Enhanced Logo */}
            <div className="flex justify-start mb-8">
              <div className="w-[40px] h-[39px] relative">
                <div className="absolute inset-0 rounded-[9px] bg-gradient-to-r from-[var(--brand-gradient-start)]/10 via-[var(--brand-gradient-end)]/10 to-[var(--brand-gradient-start)]/10 p-[1px]">
                  <div className="w-full h-full rounded-[8px] bg-gradient-to-b from-[var(--brand-gradient-start)] to-[var(--brand-gradient-end)]">
                    {/* Logo wrapper with enhanced positioning */}
                    <div className="absolute left-[7px] top-[8px]">
                      <svg
                        width="26"
                        height="24"
                        viewBox="0 0 574 520"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M251.779 233.508C389.466 263.756 495.892 378.605 496.336 518.91L251.779 518.91L251.779 233.508Z"
                          fill="white"
                          stroke="white"
                          strokeWidth="1.76189"
                        />
                        <path
                          d="M519.61 205.796L332.393 205.796L332.393 1.67702C430.265 1.82365 490.193 26.4181 525.687 64.1288C561.099 101.753 572.349 152.603 572.471 205.796L519.61 205.796Z"
                          fill="white"
                          stroke="white"
                          strokeWidth="1.76189"
                        />
                        <path
                          d="M58.3207 1.67684L245.538 1.67684L245.538 205.796C145.423 205.653 84.3733 181.055 48.3209 143.339C12.3565 105.716 1.10401 54.8695 0.981568 1.67684L58.3207 1.67684Z"
                          fill="white"
                          stroke="white"
                          strokeWidth="1.76189"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Title and Subtitle */}
            <div className="mb-8">
              <h1 className="text-2xl font-semibold text-foreground mb-3">
                Sign in to Thena
              </h1>
              <p className="text-muted-foreground text-sm">
                Don&apos;t have an account?{" "}
                <Link
                  href="/signup"
                  className="text-[var(--brand-gradient-start)] hover:text-[var(--brand-gradient-hover-start)] font-medium transition-colors"
                >
                  Sign up
                </Link>
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-4">
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Email address"
                  className="w-full px-4 py-3 bg-background border border-border rounded-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-[var(--brand-gradient-start)]/50 focus:border-[var(--brand-gradient-start)] transition-all"
                />

                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Password"
                    className="w-full px-4 py-3 bg-background border border-border rounded-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-[var(--brand-gradient-start)]/50 focus:border-[var(--brand-gradient-start)] transition-all"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 flex items-center pr-4 text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                disabled={loading}
                loading={loading}
                className="w-full py-3 bg-gradient-to-b from-[var(--brand-gradient-start)] to-[var(--brand-gradient-end)] hover:from-[var(--brand-gradient-hover-start)] hover:to-[var(--brand-gradient-hover-end)] text-white transition-all rounded-sm font-medium focus:outline-none focus:ring-2 focus:ring-[var(--brand-gradient-start)]/50"
              >
                {loading ? "Signing in..." : "Sign in"}
              </Button>
            </form>
            {/* Or Divider */}
            <div className="relative mt-8">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-border"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-background text-muted-foreground">
                  or
                </span>
              </div>
            </div>
            {/* Google Sign In Button */}
            <div className="mt-6">
              <button
                onClick={handleGoogleSignIn}
                disabled={googleLoading}
                className="w-full h-9 flex items-center justify-center gap-3 px-4 bg-background border border-border rounded-sm hover:bg-muted transition-colors text-foreground text-sm focus:outline-none focus:ring-2 focus:ring-[var(--brand-gradient-start)]/20"
              >
                {googleLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M17.64 9.20419C17.64 8.56601 17.5827 7.95237 17.4764 7.36328H9V10.8446H13.8436C13.635 11.9696 13.0009 12.9228 12.0477 13.5614V15.8196H14.9564C16.6582 14.2524 17.64 11.9451 17.64 9.20419Z"
                      fill="#4285F4"
                    />
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M8.99976 18C11.4298 18 13.467 17.1941 14.9561 15.8195L12.0475 13.5614C11.2416 14.1014 10.2107 14.4204 8.99976 14.4204C6.65567 14.4204 4.67158 12.8372 3.96385 10.71H0.957031V13.0418C2.43794 15.9831 5.48158 18 8.99976 18Z"
                      fill="#34A853"
                    />
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M3.96409 10.7101C3.78409 10.1701 3.68182 9.59325 3.68182 9.00007C3.68182 8.40689 3.78409 7.83007 3.96409 7.29007V4.95825H0.957273C0.347727 6.17325 0 7.54689 0 9.00007C0 10.4533 0.347727 11.8269 0.957273 13.0419L3.96409 10.7101Z"
                      fill="#FBBC05"
                    />
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M8.99976 3.57955C10.3211 3.57955 11.5075 4.03364 12.4402 4.92545L15.0216 2.34409C13.4629 0.891818 11.4257 0 8.99976 0C5.48158 0 2.43794 2.01682 0.957031 4.95818L3.96385 7.29C4.67158 5.16273 6.65567 3.57955 8.99976 3.57955Z"
                      fill="#EA4335"
                    />
                  </svg>
                )}
                Continue with Google
              </button>
            </div>

            {/* Forget Password */}
            <div className="mt-6 flex items-center justify-center text-sm">
              <span className="text-muted-foreground">Forgot password?</span>
              <Button
                variant="link"
                onClick={() => router.push("/password-recovery")}
                className="w-fit h-9 flex items-center justify-center gap-3 rounded-sm transition-colors  p-0 ml-[6px]"
              >
                Reset
              </Button>
            </div>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}

declare global {
  interface Window {
    google?: {
      accounts?: {
        id?: {
          initialize: (config: {
            client_id: string;
            callback: (response: { credential?: string }) => void;
            error_callback?: (error: {
              type: string;
              message?: string;
            }) => void;
            cancel_on_tap_outside?: boolean;
          }) => void;
          prompt: (
            callback: (notification: {
              isNotDisplayed: () => boolean;
              isSkippedMoment: () => boolean;
              getNotDisplayedReason: () => string;
              getSkippedReason: () => string;
            }) => void,
          ) => void;
        };
        oauth2?: {
          initCodeClient: (config: {
            client_id: string;
            scope: string;
            ux_mode: string;
            callback: (response: { code?: string }) => void;
            error_callback?: (error: {
              type: string;
              message?: string;
            }) => void;
          }) => {
            requestCode: () => void;
          };
          initTokenClient: (config: {
            client_id: string;
            callback: (response: {
              error?: string;
              credential?: string;
            }) => void;
            scope: string;
            error_callback: (error: { type: string; message?: string }) => void;
          }) => {
            requestAccessToken: () => void;
          };
        };
      };
    };
    handleGoogleOAuth?: (response: {
      credential: string;
      isAuthCode?: boolean;
    }) => Promise<void>;
  }
}
