"use client";

import { AccountsGrowthTrend } from "@/components/Insights/accounts-insights/AccountsGrowthTrend";
import { ClassificationDistribution } from "@/components/Insights/accounts-insights/ClassificationDistribution";
import { HealthCountCards } from "@/components/Insights/accounts-insights/HealthCountCards";
import { HealthDistribution } from "@/components/Insights/accounts-insights/HealthDistribution";
import { IndustryDistribution } from "@/components/Insights/accounts-insights/IndustryDistribution";
import { SourceDistribution } from "@/components/Insights/accounts-insights/SourceDistribution";
import { StatusDistribution } from "@/components/Insights/accounts-insights/StatusDistribution";
import { TotalAccountsCard } from "@/components/Insights/accounts-insights/TotalAccountsCard";
import { TotalContactsCard } from "@/components/Insights/accounts-insights/TotalContactsCard";
import { TotalRevenueCard } from "@/components/Insights/accounts-insights/TotalRevenueCard";
import { InsightsDatePicker } from "@/components/Insights/shared/InsightsDatePicker";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { TimeRangeProvider, useTimeRange } from "@/contexts/TimeRangeContext";
import * as React from "react";

function AccountsInsightsContent() {
  // We need useTimeRange context for the chart components
  useTimeRange();



  return (
    <div className="h-[calc(100vh-75px)] overflow-y-auto bg-background/50">
      <div className="p-4">
        <div className="flex justify-between items-center pb-4">
          <>
            <InsightsDatePicker
              onFilterClick={() => {
                // Handle filter click - can be implemented later
              }}
            />
            <div /> {/* Empty div to maintain flex layout */}
          </>
        </div>

        <div className="grid gap-4">
          {/* Overview Cards with horizontal scroll */}
          <ScrollArea className="w-full whitespace-nowrap">
            <div className="flex space-x-4">
              <div className="min-w-[280px] w-[280px]">
                <TotalAccountsCard />
              </div>
              <div className="min-w-[280px] w-[280px]">
                <TotalContactsCard />
              </div>
              <div className="min-w-[280px] w-[280px]">
                <TotalRevenueCard />
              </div>
              <HealthCountCards />
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          {/* Timeline Charts */}
          <div className="grid gap-4">
            <AccountsGrowthTrend />
          </div>

          {/* Distribution Charts */}
          <div className="grid gap-4 md:grid-cols-2">
            <HealthDistribution />
            <StatusDistribution />
          </div>

          {/* Additional Distribution Charts */}
          <div className="grid gap-4 md:grid-cols-2">
            <IndustryDistribution />
            <ClassificationDistribution />
          </div>

          {/* Health Distribution Chart */}
          <div className="grid gap-4 md:grid-cols-1">
            <SourceDistribution />
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AccountsInsightsPage() {
  return (
    <TimeRangeProvider>
      <AccountsInsightsContent />
    </TimeRangeProvider>
  );
}
