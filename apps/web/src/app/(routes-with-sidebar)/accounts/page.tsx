"use client";

import AccountsPageDrawer from "@/components/Account/AccountsPage/AccountsPageDrawer";
import Then<PERSON><PERSON>oader from "@/components/thena-loader";
import { AccountOwnerSelect } from "@/components/ui/account-owner-select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/accountstable";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import type { AttributeOption } from "@/stores/use-account-field-options";
import { useAccountFieldOptions } from "@/stores/use-account-field-options";
import { useAccountsDisplayOptions } from "@/stores/use-accounts-display-options";
import { useAccountsRefetchStore } from "@/stores/use-accounts-refetch-store";
import { AccountField, CustomField, StandardField } from "@/types/accounts";
import { getOrgDetails } from "@/utils/browserUtils";
import * as HeroIcons from "@heroicons/react/24/outline";
import { useInfiniteQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { useVirtualizer } from "@tanstack/react-virtual";
import {
  Activity,
  Check,
  ChevronDown,
  ChevronsUpDown,
  ChevronUp,
  Copy,
  DollarSign,
  Download,
  ExternalLink,
  Eye,
  EyeOff,
  FileText,
  Link as LinkIcon,
  ListFilter,
  MapPin,
  MoreVertical,
  Pen,
  Search,
  Star,
  StickyNote,
  Trash2,
  UserPlus,
  X,
} from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import {
  addActivity,
  addContact,
  addNote,
  addTask,
  copyAccountId,
  copyAccountLink,
  createTicket,
  deleteAccount,
  viewAccount,
} from "./account-actions";
import {
  CustomBooleanDropdown,
  CustomCheckboxDropdown,
  CustomMultiSelectDropdown,
  CustomRadioButtonDropdown,
  CustomSingleSelectDropdown,
  CustomToggleDropdown,
} from "./customFieldsDropdownOptions";
import { useCustomFieldsEdit } from "./useCustomFieldsEdit";
import {
  isStandardField,
  useStandardFieldsEdit,
} from "./useStandardFieldsEdit";

interface CustomerContact {
  id: string;
  name: string;
  email: string;
  avatarUrl?: string | null;
}

interface Account {
  id: string;
  name: string;
  description: string;
  source: string;
  logo?: string;
  statusId: string;
  status: string;
  primaryDomain: string;
  secondaryDomain?: string | null;
  billingAddress: string;
  shippingAddress: string;
  customerContacts: CustomerContact[];
  customFields: Record<string, string | string[]>;
  accountOwner: string;
  accountOwnerId: string;
  accountOwnerAvatarUrl?: string;
  accountOwnerEmail: string;
  annualRevenue?: number;
  employees?: number;
  website?: string;
  createdAt: string;
  updatedAt: string;
  customFieldValues: {
    customFieldId: string;
    data: { value: string }[];
  }[];
}

interface EditableCellProps {
  value: string | number | boolean | string[] | null;
  field: AccountField;
  isReadOnly?: boolean;
  accountId: string;
  onSave?: (
    accountId: string,
    field: AccountField,
    value: string | number | boolean | string[] | null,
  ) => Promise<void>;
  setAccounts: React.Dispatch<React.SetStateAction<Account[]>>;
  refetch?: () => Promise<void>;
  onClick?: () => void;
}

// Helper function to get source icon based on source name
const getSourceIcon = (source: string) => {
  const lowerSource = source.toLowerCase();
  if (lowerSource.includes("thena")) {
    return "/thena_purple_logo.svg";
  }
  if (lowerSource.includes("hubspot")) {
    return "/hubspot.svg";
  }
  if (lowerSource.includes("salesforce")) {
    return "/salesforce.svg";
  }
  if (lowerSource.includes("slack")) {
    return "/slack.svg";
  } else {
    return "/custom.svg";
  }
};

// Add this helper function near the top (after imports or inside EditableCell)
const isValidAccountName = (name: string) => {
  // Allows letters, numbers, spaces, hyphens
  return /^[a-zA-Z0-9\s\-'.]+$/.test(name);
};

const EditableCell = ({
  value,
  field,
  isReadOnly = false,
  accountId,
  onSave,
  setAccounts,
  onClick,
}: EditableCellProps) => {
  // Make password fields always read-only
  const isPasswordField = field.fieldType === "password";
  const effectiveReadOnly = isReadOnly || isPasswordField;

  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value?.toString() || "");
  const initialValueRef = useRef(value?.toString() || "");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { options } = useAccountFieldOptions();
  // const [error, setError] = useState<string | null>(null);

  // Update initialValue ref when value prop changes
  useEffect(() => {
    initialValueRef.current = value?.toString() || "";
    setEditValue(value?.toString() || "");
  }, [value]);

  const handleSave = async (
    newValue?: string | number | boolean | string[] | null,
  ) => {
    if (!onSave) return;

    const valueToSave = newValue ?? editValue;
    const currentValue = valueToSave?.toString() || "";

    // Input validation for account name field
    const isNameField = isStandardField(field) && field.systemName === "name";
    if (
      isNameField &&
      (typeof valueToSave === "string" && !isValidAccountName(valueToSave))
    ) {
      toast.error(
        "Invalid account name: only letters, numbers, spaces and hyphens are allowed."
      );
      return;
    }

    // Don't save if value hasn't changed
    if (currentValue === initialValueRef.current) {
      setIsEditing(false);
      return;
    }

    // For custom field dropdowns, saving is handled directly by the CustomSingleSelectDropdown component
    // which makes its own API calls, so we exit early here
    if (field.isCustomField && field.fieldType === "dropdown") {
      // Note: CustomSingleSelectDropdown will call its own onSave internally
      setIsEditing(false);
      return;
    }

    // For standard fields, continue with existing logic
    if (field.fieldType === "dropdown" && typeof valueToSave === "string") {
      // No longer transform dropdown values to uppercase with underscores
      // valueToSave = valueToSave.toUpperCase().replace(/ /g, "_");
    }

    const valueToSend = valueToSave;

    // Store original value in case we need to revert
    const originalValue = value?.toString() || "";

    // Exit edit mode immediately for better user experience
    // This allows users to quickly edit multiple fields
    setIsEditing(false);

    // Optimistically update the UI with the new value
    // This makes the UI feel more responsive without waiting for API
    if (setAccounts) {
      if (!field.isCustomField && "systemName" in field) {
        // For standard fields like status, classification, etc.
        const systemName = field.systemName as string;
        setAccounts((prevAccounts) =>
          prevAccounts.map((account) =>
            account.id === accountId
              ? {
                ...account,
                [systemName]: valueToSend,
              }
              : account,
          ),
        );
      } else if (field.isCustomField && "uid" in field) {
        // For custom fields, update the customFieldValues
        setAccounts((prevAccounts) =>
          prevAccounts.map((account) => {
            if (account.id !== accountId) return account;

            // Create a new array to avoid mutating the original
            const customFieldValues = [...(account.customFieldValues || [])];
            const existingIndex = customFieldValues.findIndex(
              (cfv) => cfv.customFieldId === field.uid,
            );

            if (existingIndex >= 0) {
              // Update existing custom field value
              customFieldValues[existingIndex] = {
                ...customFieldValues[existingIndex],
                data: Array.isArray(valueToSend)
                  ? valueToSend.map((v: string | number | boolean) => ({
                    value: v.toString(),
                  }))
                  : [{ value: valueToSend?.toString() || "" }],
              };
            } else {
              // Add new custom field value
              customFieldValues.push({
                customFieldId: field.uid as string,
                data: Array.isArray(valueToSend)
                  ? valueToSend.map((v: string | number | boolean) => ({
                    value: v.toString(),
                  }))
                  : [{ value: valueToSend?.toString() || "" }],
                // Remove metadata property as it's not in the expected type
              });
            }

            return {
              ...account,
              customFieldValues,
            };
          }),
        );
      }
    }

    // Call onSave without awaiting — let it run in the background
    onSave(accountId, field, valueToSend).catch((error) => {
      // If the API call fails, show error toast and revert the UI
      console.error(`Failed to update ${field.name.toLowerCase()}:`, error);
      toast.error(
        `Couldn't update ${field.name.toLowerCase()}. Please check the format or try again.`,
      );
      // Revert the edit value
      setEditValue(originalValue);

      // Revert the optimistic UI update
      if (setAccounts) {
        if (!field.isCustomField && "systemName" in field) {
          // For standard fields
          const systemName = field.systemName as string;
          setAccounts((prevAccounts) =>
            prevAccounts.map((account) =>
              account.id === accountId
                ? {
                  ...account,
                  [systemName]: value, // Revert to original value
                }
                : account,
            ),
          );
        } else if (field.isCustomField && "uid" in field) {
          // For custom fields
          setAccounts((prevAccounts) =>
            prevAccounts.map((account) => {
              if (account.id !== accountId) return account;

              const customFieldValues = [...(account.customFieldValues || [])];
              const existingIndex = customFieldValues.findIndex(
                (cfv) => cfv.customFieldId === field.uid,
              );

              if (existingIndex >= 0) {
                // Revert to original value
                customFieldValues[existingIndex] = {
                  ...customFieldValues[existingIndex],
                  data: Array.isArray(value)
                    ? value.map((v: string | number | boolean) => ({
                      value: v.toString(),
                    }))
                    : [{ value: value?.toString() || "" }],
                };
              }

              return {
                ...account,
                customFieldValues,
              };
            }),
          );
        }
      }
    });
  };

  const isDropdownField =
    field.fieldType === "dropdown" ||
    field.fieldType === "multiselect" ||
    field.fieldType === "checkbox" ||
    field.fieldType === "radio_button" ||
    field.fieldType === "toggle" ||
    field.fieldType === "boolean";

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsEditing(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const getDropdownOptions = (field: AccountField): string[] => {
    if (field.isCustomField && field.options) {
      return field.options.map((opt) => {
        if (typeof opt === "object" && opt !== null && "value" in opt) {
          return (opt as { value: string }).value;
        }
        return String(opt);
      });
    }

    if (!field.isCustomField && "systemName" in field) {
      const standardField = field as StandardField;
      switch (standardField.systemName) {
        case "account_status":
          return options?.status
            ? options.status.map((opt: { value: string }) => opt.value)
            : [];
        case "account_classification":
          return options?.classification
            ? options.classification.map((opt: { value: string }) => opt.value)
            : [];
        case "account_health":
          return options?.health
            ? options.health.map((opt: { value: string }) => opt.value)
            : [];
        case "account_industry":
          return options?.industry
            ? options.industry.map((opt: { value: string }) => opt.value)
            : [];
        default:
          return [];
      }
    }
    return [];
  };

  // Convert value to array for multiselect/checkbox fields
  const normalizeValue = (val: string | number | boolean | string[] | null) => {
    if (val === null || val === undefined) return "";
    if (Array.isArray(val)) {
      return val.join(", ");
    }
    return val.toString();
  };

  const getTagColorScheme = (field: AccountField, value: string) => {
    // Try to get the color from the API configuration for the attribute value
    if (!field.isCustomField && "systemName" in field) {
      // Use the cached options from useMemo instead of calling getState() directly
      let attributeOptions: AttributeOption[] = [];
      switch (field.systemName) {
        case "account_status":
          attributeOptions = options?.status ?? [];
          break;
        case "account_classification":
          attributeOptions = options?.classification ?? [];
          break;
        case "account_health":
          attributeOptions = options?.health ?? [];
          break;
        case "account_industry":
          attributeOptions = options?.industry ?? [];
          break;
        default:
          attributeOptions = [];
      }
      const found = attributeOptions.find((opt) => opt.value === value);
      // If a color is present in configuration, use it
      const color = found?.configuration?.color as string | undefined;
      if (color && typeof color === "string" && color.trim() !== "") {
        return color;
      }
    }
    // Default fallback color
    return "#64748b";
  };

  // Helper function to get icon from field option configuration
  const getFieldIcon = (field: AccountField, value: string) => {
    if (!field.isCustomField && "systemName" in field) {
      // Use the cached options from useMemo instead of calling getState() directly
      let attributeOptions: AttributeOption[] = [];

      switch (field.systemName) {
        case "account_status":
          attributeOptions = options?.status ?? [];
          break;
        case "account_classification":
          attributeOptions = options?.classification ?? [];
          break;
        case "account_health":
          attributeOptions = options?.health ?? [];
          break;
        case "account_industry":
          attributeOptions = options?.industry ?? [];
          break;
        default:
          attributeOptions = [];
      }
      const found = attributeOptions.find((opt) => opt.value === value);
      // If an icon is present in configuration, use it
      const iconName = found?.configuration?.icon as string | undefined;
      return iconName;
    }
    return null;
  };

  const renderEditor = () => {
    // For custom field dropdowns, use the new component
    if (field.isCustomField && field.fieldType === "dropdown") {
      // We need to cast field to CustomField to access the uid property
      const customField = field as CustomField;

      return (
        <CustomSingleSelectDropdown
          field={customField}
          value={value}
          accountId={accountId}
          isReadOnly={effectiveReadOnly}
          setAccounts={setAccounts}
        />
      );
    }

    // For custom field radio buttons, use the radio button component
    if (field.isCustomField && field.fieldType === "radio_button") {
      // We need to cast field to CustomField to access the uid property
      const customField = field as CustomField;

      return (
        <CustomRadioButtonDropdown
          field={customField}
          value={value}
          accountId={accountId}
          isReadOnly={effectiveReadOnly}
          setAccounts={setAccounts}
        />
      );
    }

    // For custom field multiselect, use the multiselect component
    if (field.isCustomField && field.fieldType === "multiselect") {
      // We need to cast field to CustomField to access the uid property
      const customField = field as CustomField;

      return (
        <CustomMultiSelectDropdown
          field={customField}
          value={value}
          accountId={accountId}
          isReadOnly={effectiveReadOnly}
          setAccounts={setAccounts}
        />
      );
    }

    // For custom field checkbox, use the checkbox component
    if (field.isCustomField && field.fieldType === "checkbox") {
      // We need to cast field to CustomField to access the uid property
      const customField = field as CustomField;

      return (
        <CustomCheckboxDropdown
          field={customField}
          value={value}
          accountId={accountId}
          isReadOnly={effectiveReadOnly}
          setAccounts={setAccounts}
        />
      );
    }

    // For custom field toggle, use the toggle component
    if (field.isCustomField && field.fieldType === "toggle") {
      // We need to cast field to CustomField to access the uid property
      const customField = field as CustomField;

      return (
        <CustomToggleDropdown
          field={customField}
          value={value}
          accountId={accountId}
          isReadOnly={effectiveReadOnly}
          setAccounts={setAccounts}
        />
      );
    }

    // For custom field boolean, use the boolean component
    if (field.isCustomField && field.fieldType === "boolean") {
      // We need to cast field to CustomField to access the uid property
      const customField = field as CustomField;

      return (
        <CustomBooleanDropdown
          field={customField}
          value={value}
          accountId={accountId}
          isReadOnly={effectiveReadOnly}
          setAccounts={setAccounts}
        />
      );
    }

    if (isDropdownField && !field.isCustomField) {
      const currentValue =
        field.fieldType === "dropdown"
          ? field.isCustomField
            ? value
            : value?.toString() || ""
          : normalizeValue(value);

      const dropdownOptions = getDropdownOptions(field);
      const colorValue = getTagColorScheme(
        field,
        currentValue?.toString() || "",
      );
      const iconName = getFieldIcon(field, currentValue?.toString() || "");
      const isSourceField =
        isStandardField(field) && field.systemName === "source";
      const sourceIcon = isSourceField
        ? getSourceIcon(currentValue?.toString() || "")
        : null;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "flex w-full items-center text-left text-sm outline-none",
                !isEditing && "cursor-pointer",
              )}
            >
              <div className="flex items-center">
                <span
                  className={cn(
                    "inline-flex items-center gap-2",
                    value
                      ? "rounded px-2 py-1 text-xs font-medium"
                      : "text-muted-foreground",
                  )}
                  style={
                    value
                      ? {
                        backgroundColor: `${colorValue}1A`, // 1A is for 10% opacity
                        color: colorValue,
                        border: `1px solid ${colorValue}40`, // 40 is for 25% opacity
                        width: "fit-content",
                      }
                      : {}
                  }
                >
                  {sourceIcon && (
                    <Image
                      src={sourceIcon}
                      alt={`${currentValue} icon`}
                      width={16}
                      height={16}
                      className="h-4 w-4 object-contain"
                    />
                  )}
                  {iconName &&
                    HeroIcons[iconName as keyof typeof HeroIcons] && (
                      <span className="mr-[0.08rem]">
                        {React.createElement(
                          HeroIcons[iconName as keyof typeof HeroIcons],
                          {
                            className: "h-3 w-3",
                            color: colorValue,
                            strokeWidth: 1.5,
                          },
                        )}
                      </span>
                    )}
                  <span
                    className="truncate whitespace-nowrap overflow-hidden block max-w-full"
                    title={
                      Array.isArray(currentValue)
                        ? currentValue.join(", ")
                        : currentValue?.toString()
                    }
                  >
                    {Array.isArray(currentValue)
                      ? currentValue.join(", ")
                      : currentValue || ""}
                  </span>
                </span>
              </div>
              <ChevronDown className="h-4 w-4 ml-auto opacity-0 group-hover:opacity-100 shrink-0" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-[167px]">
            {dropdownOptions.map((option) => {
              const optionColorValue = getTagColorScheme(field, option);
              const optionIconName = getFieldIcon(field, option);
              const isSourceFieldOption = isSourceField;
              const optionSourceIcon = isSourceFieldOption
                ? getSourceIcon(option)
                : null;

              return (
                <DropdownMenuItem
                  key={option}
                  className="flex items-center gap-2 px-2 py-1.5"
                  onClick={() => handleSave(option)}
                >
                  <span
                    className={cn(
                      "flex size-5 items-center justify-center rounded text-xs font-medium shrink-0",
                    )}
                    style={{
                      backgroundColor: `${optionColorValue}1A`, // 10% opacity
                      color: optionColorValue,
                      border: `1px solid ${optionColorValue}40`, // 25% opacity
                    }}
                    aria-hidden="true"
                  >
                    {optionSourceIcon && (
                      <Image
                        src={optionSourceIcon}
                        alt={`${option} icon`}
                        width={12}
                        height={12}
                        className="h-3 w-3 object-contain"
                      />
                    )}
                    {optionIconName &&
                      HeroIcons[optionIconName as keyof typeof HeroIcons] &&
                      React.createElement(
                        HeroIcons[optionIconName as keyof typeof HeroIcons],
                        {
                          className: "h-3 w-3",
                          color: optionColorValue,
                          strokeWidth: 1.5,
                        },
                      )}
                  </span>
                  <span className="truncate flex-1">{option}</span>
                  <span className="ml-auto">
                    {option === currentValue ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      ""
                    )}
                  </span>
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    // Special handling for rating fields in edit mode
    if (field.fieldType === "rating" && isEditing) {
      return (
        <div className="flex items-center justify-between h-7 text-sm px-2">
          <RatingFieldDisplay
            value={editValue}
            isEditing={true}
            onRatingChange={(rating) => {
              setEditValue(rating.toString());
              handleSave(rating.toString());
            }}
            disabled={effectiveReadOnly}
          />
        </div>
      );
    }

    // Special handling for coordinates fields in edit mode
    if (field.fieldType === "coordinates" && isEditing) {
      return (
        <div className="flex items-center justify-between h-7 text-sm px-2">
          <CoordinatesFieldDisplay
            value={editValue?.toString() || null}
            isEditing={true}
            onCoordinatesChange={(coordinates) => {
              setEditValue(coordinates);
              handleSave(coordinates);
            }}
            disabled={effectiveReadOnly}
          />
        </div>
      );
    }

    // Special handling for currency fields in edit mode
    if (field.fieldType === "currency" && isEditing) {
      return (
        <div className="flex items-center justify-between h-7 text-sm px-2">
          <CurrencyFieldDisplay
            value={editValue}
            isEditing={true}
            onCurrencyChange={(value) => {
              // Convert number to string for state consistency
              setEditValue(value.toString());
              handleSave(value);
            }}
            disabled={effectiveReadOnly}
          />
        </div>
      );
    }

    // Special handling for URL fields in edit mode
    if (field.fieldType === "url" && isEditing) {
      return (
        <div className="flex items-center justify-between h-7 text-sm px-2">
          <URLFieldDisplay
            value={editValue}
            isEditing={true}
            onURLChange={(url) => {
              setEditValue(url);
              handleSave(url);
            }}
            disabled={effectiveReadOnly}
          />
        </div>
      );
    }

    if (isEditing && !isDropdownField) {
      // Determine input type based on field type
      let inputType = "text";
      if (
        ["number", "integer", "decimal", "currency"].includes(field.fieldType)
      ) {
        inputType = "number";
      } else if (["date", "date_time"].includes(field.fieldType)) {
        inputType = "date";
      } else if (field.fieldType === "time") {
        inputType = "time";
      } else if (field.fieldType === "email") {
        inputType = "email";
      } else if (field.fieldType === "url") {
        inputType = "url";
      } else if (field.fieldType === "password") {
        inputType = "password";
      } else if (
        field.fieldType === "phone" ||
        field.fieldType === "phone_number"
      ) {
        inputType = "tel";
      }

      // Format the initial value for date fields
      let initialValue = editValue;
      if (["date", "date_time"].includes(field.fieldType) && value) {
        // Ensure we have a valid date string
        const date = new Date(value.toString());
        if (!isNaN(date.getTime())) {
          initialValue = date.toISOString().split("T")[0];
        }
      }

      return (
        <div className="flex items-center justify-between h-7 text-sm px-2">
          <input
            ref={inputRef}
            type={inputType}
            className={cn(
              "w-full bg-transparent outline-none border-none focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:outline-none appearance-none shadow-none text-sm",
              value ? "text-foreground" : "text-muted-foreground",
            )}
            style={{
              caretColor: "currentColor",
              fontSize: "inherit",
              lineHeight: "inherit",
              fontFamily: "inherit",
              fontWeight: "inherit",
            }}
            value={initialValue}
            onChange={(e) => {
              const newValue = e.target.value;
              setEditValue(newValue);
            }}
            onBlur={() => {
              const currentValue = editValue?.toString() || "";
              if (currentValue !== initialValueRef.current) {
                handleSave();
              } else {
                setIsEditing(false);
              }
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                const currentValue = editValue?.toString() || "";
                if (currentValue !== initialValueRef.current) {
                  handleSave();
                } else {
                  setIsEditing(false);
                }
              }
              if (e.key === "Escape") setIsEditing(false);
            }}
          />
          {!effectiveReadOnly && (
            <button className="ml-2 opacity-0">
              <Pen className="h-4 w-4 shrink-0" />
            </button>
          )}
          {field.fieldType === "password" && (
            <button
              className="ml-2 opacity-0"
              onClick={() => {
                inputRef.current?.setAttribute("type", "text");
              }}
            >
              <Eye className="h-4 w-4 shrink-0" />
            </button>
          )}
          {field.fieldType === "password" && (
            <button
              className="ml-2 opacity-0"
              onClick={() => {
                inputRef.current?.setAttribute("type", "password");
              }}
            >
              <EyeOff className="h-4 w-4 shrink-0" />
            </button>
          )}
        </div>
      );
    }

    // Format date for display
    const formatDateForDisplay = (dateStr: string) => {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr; // Return original if invalid date

      return date.toLocaleDateString("en-US", {
        day: "2-digit",
        month: "long",
        year: "numeric",
      });
    };

    // Format time for display with AM/PM
    const formatTimeForDisplay = (timeStr: string) => {
      try {
        // If it's a time string like "14:30" or "03:03"
        if (timeStr.includes(":")) {
          const [hours, minutes] = timeStr.split(":").map(Number);
          const date = new Date();
          date.setHours(hours, minutes);
          return date.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          });
        }

        // If it's a full date string
        const date = new Date(timeStr);
        if (!isNaN(date.getTime())) {
          return date.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          });
        }
      } catch (_e) {
        // Fall back to original string if parsing fails
      }
      return timeStr;
    };

    // Format datetime for display
    const formatDateTimeForDisplay = (dateTimeStr: string) => {
      try {
        const date = new Date(dateTimeStr);
        if (isNaN(date.getTime())) return dateTimeStr; // Return original if invalid date

        return `${date.toLocaleDateString("en-US", {
          day: "numeric",
          month: "long",
          year: "numeric",
        })} ${date.toLocaleTimeString("en-US", {
          hour: "numeric",
          minute: "2-digit",
          hour12: true,
        })}`;
      } catch (_e) {
        // Fall back to original string if parsing fails
      }
      return dateTimeStr;
    };

    // Format display value based on field type
    const formatDisplayValue = (
      val: string | number | boolean | string[] | null,
    ) => {
      if (val === null || val === undefined) {
        return "";
      }

      // Password fields are handled by the PasswordField component
      if (field.fieldType === "password") {
        return "••••••"; // This won't actually be used, just a fallback
      }

      // Rating fields are handled by the RatingFieldDisplay component
      if (field.fieldType === "rating") {
        const ratingValue =
          typeof value === "boolean" || Array.isArray(value) ? null : value;
        return <RatingFieldDisplay value={ratingValue} />;
      }

      // Coordinates fields are handled by the CoordinatesFieldDisplay component
      if (field.fieldType === "coordinates") {
        const coordsValue = typeof val === "string" ? val : null;
        return <CoordinatesFieldDisplay value={coordsValue} />;
      }

      // Currency fields are handled by the CurrencyFieldDisplay component
      if (field.fieldType === "currency" || field.id === "annualRevenue") {
        return <CurrencyFieldDisplay value={val} />;
      }

      // URL fields are handled by the URLFieldDisplay component
      if (field.fieldType === "url") {
        // Special case for primary and secondary domain fields - display as plain text
        if (field.id === "primaryDomain" || field.id === "secondaryDomain") {
          const stringValue =
            typeof val === "string" ? val : val?.toString() || "";
          return stringValue;
        }
        return <URLFieldDisplay value={val} />;
      }

      if (field.fieldType === "date") {
        return formatDateForDisplay(val.toString());
      }

      if (field.fieldType === "time") {
        return formatTimeForDisplay(val.toString());
      }

      if (field.fieldType === "date_time") {
        return formatDateTimeForDisplay(val.toString());
      }

      if (
        typeof val === "string" &&
        (field.fieldType === "multiselect" || field.fieldType === "checkbox")
      ) {
        try {
          const parsed = JSON.parse(val);
          // If successfully parsed as array, use the parsed array
          if (Array.isArray(parsed)) {
            return parsed.join(", ");
          }
        } catch (_e) {
          // Failed to parse as JSON, continue with normal processing
        }
      }

      if (Array.isArray(val)) {
        return val.join(", ");
      }

      return val.toString();
    };

    // Special handling for password fields
    if (field.fieldType === "password") {
      return <PasswordField value={value?.toString() || null} />;
    }

    return (
      <div
        className={cn(
          "flex items-center justify-between h-7 text-sm px-2 max-w-full",
          !effectiveReadOnly && "group cursor-pointer",
          onClick && "cursor-pointer",
        )}
        onClick={(e) => {
          // Only trigger onClick if we're not clicking the edit button
          if (
            onClick &&
            !isEditing &&
            e.target !== e.currentTarget.querySelector("button")
          ) {
            onClick();
          }
        }}
      >
        <span
          className={cn(
            "inline-flex items-center gap-2 truncate max-w-full overflow-hidden",
            value
              ? !field.isCustomField &&
                isStandardField(field) &&
                (field.systemName === "account_status" ||
                  field.systemName === "account_classification" ||
                  field.systemName === "account_health" ||
                  field.systemName === "account_industry")
                ? "rounded px-2 py-1 text-xs font-medium"
                : "text-foreground"
              : "text-muted-foreground",
          )}
          style={
            value &&
              !field.isCustomField &&
              isStandardField(field) &&
              (field.systemName === "account_status" ||
                field.systemName === "account_classification" ||
                field.systemName === "account_health" ||
                field.systemName === "account_industry")
              ? {
                backgroundColor: `${getTagColorScheme(
                  field,
                  value?.toString() || "",
                )}1A`, // 1A is for 10% opacity
                color: getTagColorScheme(field, value?.toString() || ""),
                border: `1px solid ${getTagColorScheme(
                  field,
                  value?.toString() || "",
                )}40`, // 40 is for 25% opacity
                width: "fit-content",
              }
              : {}
          }
        >
          <span
            className="block w-full overflow-hidden text-ellipsis whitespace-nowrap"
            title={value?.toString()}
          >
            {formatDisplayValue(value)}
          </span>
        </span>
        {!effectiveReadOnly && (
          <button
            onClick={(e) => {
              e.stopPropagation(); // Prevent the parent onClick from firing
              setIsEditing(true);
            }}
            className="ml-2 opacity-0 group-hover:opacity-100"
          >
            <Pen className="h-4 w-4 shrink-0" />
          </button>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        "group relative w-full",
        !effectiveReadOnly && "cursor-pointer",
      )}
    >
      {renderEditor()}
    </div>
  );
};

// Password field component that shows eye icon on hover and reveals password on click
const PasswordField = ({ value }: { value: string | null }) => {
  const [showPassword, setShowPassword] = useState(false);

  if (!value) {
    return (
      <div className="flex items-center justify-between h-7 text-sm px-2 max-w-full">
        <span className="truncate text-muted-foreground italic w-full" />
      </div>
    );
  }

  return (
    <div className="group flex items-center justify-between h-7 text-sm px-2 max-w-full">
      <span className="truncate text-foreground w-full">
        {showPassword ? value : "••••••"}
      </span>
      <button
        onClick={() => setShowPassword(!showPassword)}
        className="ml-2 opacity-0 group-hover:opacity-100"
      >
        {showPassword ? (
          <EyeOff className="h-4 w-4 shrink-0" />
        ) : (
          <Eye className="h-4 w-4 shrink-0" />
        )}
      </button>
    </div>
  );
};

// Rating field component for displaying star ratings
const RatingFieldDisplay = ({
  value,
  maxRating = 5,
  isEditing = false,
  onRatingChange,
  disabled = false,
}: {
  value: number | string | null;
  maxRating?: number;
  isEditing?: boolean;
  onRatingChange?: (rating: number) => void;
  disabled?: boolean;
}) => {
  const rating =
    typeof value === "string"
      ? parseInt(value, 10)
      : typeof value === "number"
        ? value
        : 0;
  const stars = Array.from({ length: maxRating }, (_, i) => i + 1);

  const handleRatingClick = (starValue: number) => {
    if (isEditing && !disabled && onRatingChange) {
      onRatingChange(starValue);
    }
  };

  return (
    <div className="flex items-center space-x-1">
      {stars.map((star) => (
        <Star
          key={star}
          size={16}
          className={`
            ${star <= rating
              ? "fill-amber-500 text-amber-500"
              : "text-muted-foreground"
            }
            ${isEditing && !disabled
              ? "cursor-pointer hover:scale-110 transition-transform"
              : ""
            }
          `}
          onClick={() => handleRatingClick(star)}
        />
      ))}
    </div>
  );
};

// Coordinates field component for displaying latitude and longitude
const CoordinatesFieldDisplay = ({
  value,
  isEditing = false,
  onCoordinatesChange,
  disabled = false,
}: {
  value: string | null;
  isEditing?: boolean;
  onCoordinatesChange?: (coordinates: string) => void;
  disabled?: boolean;
}) => {
  // Parse coordinates from string format "latitude,longitude"
  const parseCoordinates = (coordsString: string | null) => {
    if (!coordsString) return { latitude: "", longitude: "" };

    const parts = coordsString.split(",");
    if (parts.length !== 2) return { latitude: "", longitude: "" };

    return {
      latitude: parts[0].trim(),
      longitude: parts[1].trim(),
    };
  };

  const [coordinates, setCoordinates] = useState(parseCoordinates(value));

  // Update local state when prop value changes
  useEffect(() => {
    const parsed = parseCoordinates(value);
    setCoordinates(parsed);
  }, [value]);

  const handleChange = (
    field: "latitude" | "longitude",
    fieldValue: string,
  ) => {
    if (!isEditing || disabled) return;

    const updatedCoordinates = {
      ...coordinates,
      [field]: fieldValue,
    };

    setCoordinates(updatedCoordinates);
  };

  const handleSave = () => {
    if (!isEditing || disabled) return;

    // Only trigger onChange if both values are valid numbers
    const lat = parseFloat(coordinates.latitude);
    const lng = parseFloat(coordinates.longitude);

    if (!isNaN(lat) && !isNaN(lng) && onCoordinatesChange) {
      onCoordinatesChange(`${lat},${lng}`);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    }
  };

  if (isEditing) {
    return (
      <div className="grid grid-cols-2 gap-2 w-full">
        <div>
          <input
            type="number"
            placeholder="Latitude"
            value={coordinates.latitude}
            onChange={(e) => handleChange("latitude", e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyDown}
            disabled={disabled}
            className="w-full bg-transparent outline-none border-none focus:ring-0 text-xs"
            step="any"
            min="-90"
            max="90"
          />
        </div>
        <div>
          <input
            type="number"
            placeholder="Longitude"
            value={coordinates.longitude}
            onChange={(e) => handleChange("longitude", e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyDown}
            disabled={disabled}
            className="w-full bg-transparent outline-none border-none focus:ring-0 text-xs"
            step="any"
            min="-180"
            max="180"
          />
        </div>
      </div>
    );
  }

  // View mode - compact display with icon
  if (coordinates.latitude && coordinates.longitude) {
    return (
      <div className="flex items-center space-x-2">
        <MapPin size={14} className="text-muted-foreground" />
        <span className="text-sm truncate">
          {coordinates.latitude}, {coordinates.longitude}
        </span>
      </div>
    );
  }

  // Empty state
  return (
    <div className="flex items-center text-muted-foreground italic text-sm">
      <MapPin size={14} className="mr-2" />
      <span>No coordinates</span>
    </div>
  );
};

// URL field component for displaying hyperlinked URLs
const URLFieldDisplay = ({
  value,
  isEditing = false,
  onURLChange,
  disabled = false,
}: {
  value: string | number | boolean | string[] | null;
  isEditing?: boolean;
  onURLChange?: (url: string) => void;
  disabled?: boolean;
}) => {
  // Parse URL from various value types
  const parseURL = (
    val: string | number | boolean | string[] | null,
  ): string => {
    if (val === null || val === undefined) return "";

    // Handle different value types
    if (typeof val === "boolean") return "";
    if (Array.isArray(val)) return val.length > 0 ? val[0].toString() : "";

    return val.toString();
  };

  const [url, setUrl] = useState(parseURL(value));

  // Update local state when prop value changes
  useEffect(() => {
    const parsed = parseURL(value);
    setUrl(parsed);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditing || disabled) return;
    setUrl(e.target.value);
  };

  const handleSave = () => {
    if (!isEditing || disabled) return;

    if (url && onURLChange) {
      onURLChange(url);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    }
  };

  // Ensure URL has proper format for href
  const getFormattedUrl = (urlString: string): string => {
    if (!urlString) return "";
    if (/^https?:\/\//i.test(urlString)) return urlString;
    return `http://${urlString}`;
  };

  if (isEditing) {
    return (
      <div className="flex items-center w-full">
        <LinkIcon
          size={14}
          className="text-muted-foreground mr-1 flex-shrink-0"
        />
        <input
          type="text"
          value={url}
          onChange={handleChange}
          onBlur={handleSave}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className="w-full bg-transparent outline-none border-none focus:ring-0 text-xs"
          placeholder="https://example.com"
        />
      </div>
    );
  }

  // View mode - display as hyperlink
  if (url) {
    const formattedUrl = getFormattedUrl(url);
    const displayUrl = url.replace(/^https?:\/\//i, "");

    return (
      <div className="flex items-center space-x-1">
        <LinkIcon size={14} className="text-muted-foreground flex-shrink-0" />
        <a
          href={formattedUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="text-sm truncate text-primary hover:underline flex items-center"
        >
          <span className="truncate max-w-[150px]">{displayUrl}</span>
        </a>
      </div>
    );
  }

  // Empty state
  return (
    <div className="flex items-center text-muted-foreground italic text-sm">
      <LinkIcon size={14} className="mr-1 flex-shrink-0" />
      <span>No URL</span>
    </div>
  );
};

// Currency field component for displaying monetary values with dollar sign
const CurrencyFieldDisplay = ({
  value,
  isEditing = false,
  onCurrencyChange,
  disabled = false,
}: {
  value: string | number | boolean | string[] | null;
  isEditing?: boolean;
  onCurrencyChange?: (value: number) => void;
  disabled?: boolean;
}) => {
  // Parse value to number, handling different input types
  const parseValue = (
    val: string | number | boolean | string[] | null,
  ): number => {
    if (val === null || val === undefined) return 0;

    // Handle different value types
    if (typeof val === "boolean") return 0;
    if (Array.isArray(val))
      return val.length > 0
        ? Number(val[0].toString().replace(/[^\d.]/g, "")) || 0
        : 0;

    // If already a number, return it directly
    if (typeof val === "number") return val;

    // Otherwise convert string to number
    const stringVal = val.toString();
    const numericValue = Number(stringVal.replace(/[^\d.]/g, ""));
    return isNaN(numericValue) ? 0 : numericValue;
  };

  // Parse the initial value to number
  const parsedValue = parseValue(value);

  // Store as number in state
  const [amount, setAmount] = useState<number>(parsedValue);

  // Update local state when prop value changes
  useEffect(() => {
    const parsed = parseValue(value);
    setAmount(parsed);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isEditing || disabled) return;

    // Only allow numbers and decimal point
    const inputValue = e.target.value.replace(/[^\d.]/g, "");
    const numValue = Number(inputValue);

    // Store as number, defaulting to 0 if invalid
    setAmount(isNaN(numValue) ? 0 : numValue);
  };

  const handleSave = () => {
    if (!isEditing || disabled) return;

    // Pass the numeric value directly
    if (onCurrencyChange) {
      onCurrencyChange(amount);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center w-full">
        <DollarSign
          size={14}
          className="text-muted-foreground mr-1 flex-shrink-0"
        />
        <input
          type="text"
          value={amount === 0 ? "" : amount.toString()}
          onChange={handleChange}
          onBlur={handleSave}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className="w-full bg-transparent outline-none border-none focus:ring-0 text-xs"
          placeholder="0.00"
        />
      </div>
    );
  }

  // View mode - display with dollar sign
  if (amount > 0) {
    // Format as currency
    const formattedValue = amount.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    return (
      <div className="flex items-center space-x-1">
        <DollarSign size={14} className="text-muted-foreground flex-shrink-0" />
        <span className="text-sm truncate">{formattedValue}</span>
      </div>
    );
  }

  // Empty state
  return (
    <div className="flex items-center text-muted-foreground italic text-sm">
      <DollarSign size={14} className="mr-1 flex-shrink-0" />
      <span>No amount</span>
    </div>
  );
};

// First, let's extract the search and filter components
const AccountFiltersBar = ({
  searchQuery,
  setSearchQuery,
  isSearchExpanded,
  setIsSearchExpanded,
  totalAccounts,
}: {
  searchQuery: string;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
  isSearchExpanded: boolean;
  setIsSearchExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  totalAccounts: number;
}) => {
  return (
    <div className="flex items-center justify-between px-4 py-2 border-b">
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          className="p-0 h-7 text-color-icon-muted hover:text-color-icon-muted flex items-center gap-2 px-2"
        >
          <ListFilter size={16} />
          <span>Filter</span>
        </Button>
      </div>

      <div className="flex items-center gap-4">
        {/* Total accounts count display */}
        <div className="text-sm text-muted-foreground mr-2">
          {totalAccounts ? `${totalAccounts} accounts` : ""}
        </div>
        <div className="relative">
          {isSearchExpanded ? (
            <div className="relative">
              <Input
                type="text"
                placeholder="Search accounts"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                }}
                className="w-[200px] h-[28px] pe-7"
                autoFocus
                onBlur={() => {
                  if (searchQuery === "") {
                    setIsSearchExpanded(false);
                  }
                }}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  className="absolute h-[26px] w-[26px] p-0 top-[1px] right-[1px] z-1 bg-background hover:bg-background"
                  onClick={() => setSearchQuery("")}
                >
                  <X size={14} />
                </Button>
              )}
            </div>
          ) : (
            <Button
              variant="outline"
              className="p-0 w-7 h-7"
              onClick={() => setIsSearchExpanded(true)}
              type="button"
            >
              <Search size={16} />
            </Button>
          )}
        </div>
        <Button variant="outline" className="p-0 w-7 h-7">
          <Download size={16} />
        </Button>
      </div>
    </div>
  );
};

export default function AccountsPage() {
  // UI state (this doesn't affect data fetching)
  const [rowSelection, setRowSelection] = useState({});
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const params = useSearchParams();
  const router = useRouter();
  const accountId = params.get("accountId") || "";

  // Get organization details
  const { orgUid } = getOrgDetails();

  // These states manage data and will cause re-renders of the data portions only
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [totalAccounts, setTotalAccounts] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const {
    fields,
    isLoading: isFieldsLoading,
    fetchFields,
  } = useAccountsDisplayOptions();

  const options = useAccountFieldOptions((state) => state.options);

  // Debounce search query changes
  useEffect(() => {
    if (searchQuery.trim().length > 0 && searchQuery.trim().length < 3) {
      return;
    }

    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [searchQuery]);

  // Refetch function for data updates
  const refetch = async () => {
    await refetchAccounts();
  };

  // Store the refetch function in the global store
  const setRefetchAccounts = useAccountsRefetchStore(
    (state) => state.setRefetchAccounts,
  );

  useEffect(() => {
    // Register the refetch function in the global store for use by other components
    setRefetchAccounts(refetch);
  }, [refetch, setRefetchAccounts]);

  // Initialize field editing hooks
  const standardFieldHandlers = useStandardFieldsEdit({
    accounts,
    setAccounts,
    refetch,
  });

  const customFieldHandlers = useCustomFieldsEdit({
    accounts,
    setAccounts,
    refetch,
  });

  // Fetch fields and options
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // Always fetch field options to ensure they're up-to-date
        const fieldOptionsStore = useAccountFieldOptions.getState();
        await fieldOptionsStore.fetchStandardFieldOptions();

        // Check for new fields
        const response = await fetch(`/api/accounts/fields?limit=100&offset=0`);
        if (!response.ok) {
          throw new Error(`Failed to fetch fields: ${response.statusText}`);
        }

        const data = await response.json();
        const apiFields = data.fields || [];

        // If we have no fields or there are new fields in the API, update our store
        const storeFieldIds = new Set(fields.map((f) => f.id));

        const hasNewFields = apiFields.some(
          (field) => !storeFieldIds.has(field.id),
        );
        const hasMissingFields = fields.length === 0;

        if (hasMissingFields || hasNewFields) {
          await fetchFields();
        }
      } catch (error) {
        console.error("Error loading fields:", error);
        setIsLoading(false);
      }
      // Remove the setIsLoading(false) from finally block to let the data effect handle it
    };
    loadData();
  }, [fetchFields, fields]);

  // Define a more specific response type for our API
  interface AccountsApiResponse {
    accounts: Account[];
    pagination: {
      limit: number;
      page: number;
      hasMore: boolean;
      total: number;
    };
  }

  // Fetch accounts data with infinite query
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: _isLoadingAccounts,
    refetch: refetchAccounts,
  } = useInfiniteQuery<AccountsApiResponse>({
    queryKey: ["accounts", debouncedSearchQuery, orgUid], // Add orgUid to query key
    queryFn: async ({ pageParam = 1 }) => {
      const url = new URL("/api/accounts", window.location.origin);
      url.searchParams.append("limit", "1000");
      url.searchParams.append("page", pageParam.toString());

      if (debouncedSearchQuery.trim()) {
        url.searchParams.append("search", debouncedSearchQuery.trim());
      }

      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch accounts");
      }

      return response.json();
    },
    getNextPageParam: (lastPage) => {
      // Return the next page number if there are more results
      return lastPage.pagination?.hasMore
        ? lastPage.pagination.page + 1
        : undefined;
    },
    initialPageParam: 1,
    refetchOnWindowFocus: false,
    // Add these options to ensure proper initialization
    enabled: typeof window !== "undefined" && !!orgUid, // Only run when we have orgUid
    staleTime: 60 * 1000, // Consider data fresh for 1 minute
    retry: 3, // Retry failed requests 3 times
  });

  // Update accounts and total count when API data changes
  useEffect(() => {
    if (data?.pages) {
      setAccounts(data.pages.flatMap((page) => page.accounts) || []);

      // Also update the total count for display in the filters bar
      if (data.pages[0]?.pagination?.total) {
        setTotalAccounts(data.pages[0].pagination.total);
      }
      
      // Ensure loading state is turned off only after data is processed
      setIsLoading(false);
    }
  }, [data]);

  const columns = useMemo(() => {
    // Create a set to track which fields we've already added
    const addedFields = new Set<string>();

    const baseColumns = [];

    // Add visible standard fields from the fields array
    const standardColumns = fields
      .filter((field) => {
        const shouldInclude = !field.isCustomField && field.visible;
        return shouldInclude;
      })
      .map((field) => {
        const isStandardField = (field: AccountField): field is StandardField =>
          !field.isCustomField;

        // Type guard to ensure field is StandardField
        if (!isStandardField(field)) return null;

        // Special handling for specific fields
        switch (field.systemName) {
          case "name":
            return {
              id: "name",
              accessorKey: "name",
              header: "Account",
              size: 350,
              cell: ({ row }) => {
                const account = row.original;

                return (
                  <div className="flex items-center gap-3 group w-full">
                    {/* <Checkbox
                      checked={row.getIsSelected()}
                      onCheckedChange={(value) => row.toggleSelected(!!value)}
                      aria-label="Select row"
                      className="translate-y-[2px] flex-shrink-0"
                    /> */}
                    <div
                      onClick={() =>
                        router.push(`/accounts?accountId=${account.id}`)
                      }
                      className="flex-shrink-0 w-7 h-7 rounded overflow-hidden flex items-center justify-center bg-primary/10 border border-secondary"
                    >
                      {account.logo ? (
                        <Image
                          src={account.logo}
                          alt={account.name}
                          width={28}
                          height={28}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-sm font-medium text-primary">
                          {account.name[0].toUpperCase()}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center justify-between w-full min-w-0 gap-2">
                      <div className="overflow-hidden min-w-0 flex-1">
                        <EditableCell
                          value={account.name}
                          field={field}
                          accountId={account.id}
                          setAccounts={setAccounts}
                          // refetch prop removed - using optimistic updates instead
                          onSave={standardFieldHandlers.handleNameUpdate}
                          onClick={() =>
                            router.push(`/accounts?accountId=${account.id}`)
                          }
                        />
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-transparent flex-shrink-0 ml-2"
                          >
                            <MoreVertical className="h-4 w-4" />
                            <span className="sr-only">Open actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="start"
                          className="w-[240px]"
                        >
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() => createTicket(account)}
                          >
                            <FileText className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">Create a ticket</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() => addContact(account)}
                          >
                            <UserPlus className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">Add a contact</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() => addNote(account)}
                          >
                            <StickyNote className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">Add a note</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() => addTask(account)}
                          >
                            <Check className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">Add a task</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() => addActivity(account)}
                          >
                            <Activity className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">Add an activity</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() => copyAccountId(account)}
                          >
                            <Copy className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">Copy account ID</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() => copyAccountLink(account)}
                          >
                            <ExternalLink className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">Copy account link</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() =>
                              deleteAccount(account, () => {
                                // Optimistically remove the account from the accounts list
                                setAccounts(
                                  accounts.filter((a) => a.id !== account.id),
                                );
                              })
                            }
                          >
                            <Trash2 className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">Delete</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="flex items-center gap-2 py-2 px-3"
                            onClick={() => viewAccount(account)}
                          >
                            <ExternalLink className="h-4 w-4 flex-shrink-0" />
                            <span className="flex-grow">View account</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                );
              },
            };
          case "id":
            return {
              id: "id",
              accessorKey: "id",
              header: "ID",
              size: 350,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.id}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    isReadOnly={true}
                    onSave={
                      standardFieldHandlers.handleGenericStandardFieldUpdate
                    }
                  />
                );
              },
            };
          case "account_status":
            return {
              id: "status",
              accessorKey: "status",
              header: "Status",
              size: 200,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.status}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={standardFieldHandlers.handleStatusUpdate}
                  />
                );
              },
            };
          case "account_classification":
            return {
              id: "classification",
              accessorKey: "classification",
              header: "Classification",
              size: 200,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.classification}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={standardFieldHandlers.handleClassificationUpdate}
                  />
                );
              },
            };
          case "account_health":
            return {
              id: "health",
              accessorKey: "health",
              header: "Health",
              size: 200,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.health}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={standardFieldHandlers.handleHealthUpdate}
                  />
                );
              },
            };
          case "account_industry":
            return {
              id: "industry",
              accessorKey: "industry",
              header: "Industry",
              size: 200,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.industry} // This will remain as value, since API returns string, but dropdown options now use .value from AttributeOption
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={standardFieldHandlers.handleIndustryUpdate}
                  />
                );
              },
            };
          case "primaryDomain":
            return {
              id: "primaryDomain",
              accessorKey: "primaryDomain",
              header: "Primary domain",
              size: 250,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.primaryDomain}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={standardFieldHandlers.handlePrimaryDomainUpdate}
                  />
                );
              },
            };
          case "accountOwner":
            return {
              id: "accountOwner",
              accessorKey: "accountOwner",
              header: "Account owner",
              size: 225,
              cell: ({ row }) => {
                const account = row.original;

                const selectedOwner = account.accountOwner
                  ? {
                    id: account.accountOwnerId,
                    name: account.accountOwner,
                    avatar: account.accountOwnerAvatarUrl,
                  }
                  : undefined;

                // Map users to the Owner format expected by AccountOwnerSelect
                const owners = options.users.map((user) => ({
                  id: user.id,
                  name: user.name,
                  avatar: user.avatarUrl,
                }));

                return (
                  <AccountOwnerSelect
                    owners={owners}
                    selectedOwner={selectedOwner}
                    onSelect={(owner) =>
                      standardFieldHandlers.handleAccountOwnerUpdate(
                        account.id,
                        owner,
                      )
                    }
                  />
                );
              },
            };
          case "customerContacts":
            return {
              id: "customerContacts",
              accessorKey: "customerContacts",
              header: "Contacts",
              size: 200,
              cell: ({ row }) => {
                const account = row.original;
                const contacts = account.customerContacts || [];
                const contactsToShow = contacts.slice(0, 4);
                const contactsToHide = contacts.slice(4);

                if (contacts.length === 0) {
                  return <div className="px-2 truncate" />;
                }

                return (
                  <TooltipProvider>
                    <div className="px-4 flex text-sm max-w-full overflow-hidden">
                      <div className="flex">
                        {contactsToShow.map((contact) => (
                          <span key={contact.id}>
                            <Tooltip>
                              <TooltipTrigger>
                                <div className="h-7 min-w-7 max-w-7 rounded overflow-hidden flex items-center justify-center bg-primary/10 border border-secondary ml-[-10px]">
                                  {contact.avatarUrl ? (
                                    <Image
                                      src={contact.avatarUrl}
                                      alt={contact.name}
                                      width={28}
                                      height={28}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <span className="text-xs font-medium text-primary">
                                      {contact.name[0].toUpperCase()}
                                    </span>
                                  )}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>{contact.name}</TooltipContent>
                            </Tooltip>
                          </span>
                        ))}
                      </div>
                      {contactsToHide.length > 0 && (
                        <HoverCard>
                          <HoverCardTrigger>
                            <div className="flex cursor-pointer">
                              <div className="h-[28px] w-[28px] rounded text-xs border border-secondary ml-[-10px] bg-secondary flex items-center justify-center">
                                <span>+{contactsToHide.length}</span>
                              </div>
                            </div>
                          </HoverCardTrigger>
                          <HoverCardContent
                            align="start"
                            className="max-w-fit py-2 max-h-52 overflow-auto"
                          >
                            <div>
                              <div className="text-sm text-muted-text py-1">
                                Contacts ({contacts.length})
                              </div>
                              <div className="flex flex-col gap-2 p-2">
                                {contacts.map((contact) => (
                                  <div
                                    key={contact.id}
                                    className="flex gap-2 items-center"
                                  >
                                    <div className="h-7 w-7 rounded overflow-hidden flex items-center justify-center bg-primary/10 border border-secondary">
                                      {contact.avatarUrl ? (
                                        <Image
                                          src={contact.avatarUrl}
                                          alt={contact.name}
                                          width={28}
                                          height={28}
                                          className="w-full h-full object-cover"
                                        />
                                      ) : (
                                        <span className="text-xs font-medium text-primary">
                                          {contact.name[0].toUpperCase()}
                                        </span>
                                      )}
                                    </div>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <span className="max-w-32 truncate">
                                          {contact.name}
                                        </span>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        {contact.name}
                                      </TooltipContent>
                                    </Tooltip>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </HoverCardContent>
                        </HoverCard>
                      )}
                    </div>
                  </TooltipProvider>
                );
              },
            };
          case "createdAt":
            return {
              id: "createdAt",
              accessorKey: "createdAt",
              header: "Created at",
              size: 200,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.createdAt}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    isReadOnly={field.readOnly}
                    onSave={standardFieldHandlers.handleCreatedAtUpdate}
                  />
                );
              },
            };
          case "secondaryDomain":
            return {
              id: "secondaryDomain",
              accessorKey: "secondaryDomain",
              header: "Secondary domain",
              size: 250,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.secondaryDomain}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={standardFieldHandlers.handleSecondaryDomainUpdate}
                  />
                );
              },
            };
          case "source":
            return {
              id: "source",
              accessorKey: "source",
              header: "Source",
              size: 150,
              cell: ({ row }) => {
                const account = row.original;
                const sourceValue = account.source || "";
                const sourceIcon = getSourceIcon(sourceValue);

                return (
                  <div className="flex items-center gap-2 px-2">
                    <Image
                      src={sourceIcon}
                      alt={`${sourceValue} icon`}
                      width={16}
                      height={16}
                      className="h-4 w-4 object-contain flex-shrink-0"
                    />
                    <span>{sourceValue}</span>
                  </div>
                );
              },
            };
          case "website":
            return {
              id: "website",
              accessorKey: "website",
              header: "Website",
              size: 200,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.website}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={standardFieldHandlers.handleWebsiteUpdate}
                  />
                );
              },
            };
          case "description":
            return {
              id: "description",
              accessorKey: "description",
              header: "Description",
              size: 250,
              cell: ({ row }) => {
                const account = row.original;
                return (
                  <EditableCell
                    value={account.description}
                    field={field}
                    accountId={account.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={
                      standardFieldHandlers.handleGenericStandardFieldUpdate
                    }
                  />
                );
              },
            };
          default:
            return {
              id: field.systemName,
              accessorKey: field.systemName,
              header: field.name,
              size: 200,
              cell: ({ row }) => {
                const value = row.getValue(field.systemName);
                let displayValue = value ? String(value) : "";

                // Format date fields
                if (field.fieldType === "date" && value) {
                  const date = new Date(value.toString());
                  if (!isNaN(date.getTime())) {
                    displayValue = date.toLocaleDateString("en-US", {
                      day: "2-digit",
                      month: "long",
                      year: "numeric",
                    });
                  }
                }

                return field.readOnly ? (
                  <div className="px-2 py-1 max-w-full">
                    <span
                      className="block w-full overflow-hidden text-ellipsis whitespace-nowrap"
                      title={displayValue}
                    >
                      {displayValue}
                    </span>
                  </div>
                ) : (
                  <EditableCell
                    value={value}
                    field={field}
                    accountId={row.original.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={
                      standardFieldHandlers.handleGenericStandardFieldUpdate
                    }
                  />
                );
              },
            };
        }
      })
      .filter(Boolean);

    // Add visible custom fields
    const customColumns = fields
      .filter((field) => {
        if (!field.visible || !field.isCustomField) return false;
        // if (!("uid" in field)) return false;
        if (!field.isCustomField) return false;

        const customField = field as CustomField;
        return !addedFields.has(customField.uid);
      })
      .map((field) => {
        // We know it's a CustomField because of the filter
        const customField = field as CustomField;
        addedFields.add(customField.uid);

        return {
          id: customField.uid,
          accessorFn: (row: Account) => {
            // Use the helper from customFieldHandlers
            return customFieldHandlers.getCustomFieldValue(row, customField);
          },
          header: field.name,
          size: customField.fieldType === "coordinates" ? 300 : 200,
          cell: ({ row }) => {
            // For custom dropdown and radio button fields, we need to get the raw ID value
            if (
              customField.fieldType === "dropdown" ||
              customField.fieldType === "radio_button" ||
              customField.fieldType === "multiselect" ||
              customField.fieldType === "checkbox"
            ) {
              // Get the raw ID value directly from customFieldValues
              const customFieldValue = row.original.customFieldValues?.find(
                (cfv) => cfv.customFieldId === customField.uid,
              );

              // For multiselect and checkbox fields, we need an array of values
              if (
                customField.fieldType === "multiselect" ||
                customField.fieldType === "checkbox"
              ) {
                const rawValues =
                  customFieldValue?.data?.map((d) => d.value) || [];

                // Only handle readonly fields with static content
                if (field.readOnly) {
                  // For readonly fields, display the mapped values
                  const displayValue = customFieldHandlers.getCustomFieldValue(
                    row.original,
                    customField,
                  );

                  return (
                    <div className="px-2 py-1">
                      {Array.isArray(displayValue)
                        ? displayValue.join(", ")
                        : displayValue?.toString() || ""}
                    </div>
                  );
                }

                // For editable multiselect/checkbox fields, pass the raw ID values
                return (
                  <EditableCell
                    value={rawValues}
                    field={field}
                    accountId={row.original.id}
                    setAccounts={setAccounts}
                    // refetch prop removed - using optimistic updates instead
                    onSave={customFieldHandlers.handleCustomFieldUpdate}
                  />
                );
              }

              // For dropdown and radio button fields
              const rawValue = customFieldValue?.data?.[0]?.value || null;

              // Only handle readonly fields with static content
              if (field.readOnly) {
                // For readonly fields, display the mapped value
                const displayValue = customFieldHandlers.getCustomFieldValue(
                  row.original,
                  customField,
                );

                return (
                  <div className="px-2 py-1 max-w-full">
                    <span
                      className="block w-full overflow-hidden text-ellipsis whitespace-nowrap"
                      title={
                        Array.isArray(displayValue)
                          ? displayValue.join(", ")
                          : displayValue?.toString() || ""
                      }
                    >
                      {Array.isArray(displayValue)
                        ? displayValue.join(", ")
                        : displayValue?.toString() || ""}
                    </span>
                  </div>
                );
              }

              // For editable dropdown/radio fields, pass the raw ID value
              return (
                <EditableCell
                  value={rawValue}
                  field={field}
                  accountId={row.original.id}
                  setAccounts={setAccounts}
                  // refetch prop removed - using optimistic updates instead
                  onSave={customFieldHandlers.handleCustomFieldUpdate}
                />
              );
            }

            // Special handling for password fields
            if (customField.fieldType === "password") {
              const value = customFieldHandlers.getCustomFieldValue(
                row.original,
                customField,
              );

              return (
                <EditableCell
                  value={value}
                  field={{
                    ...field,
                    fieldType: "password", // Explicitly set fieldType to password
                  }}
                  accountId={row.original.id}
                  setAccounts={setAccounts}
                  // refetch prop removed - using optimistic updates instead
                  onSave={customFieldHandlers.handleCustomFieldUpdate}
                />
              );
            }

            // For other field types, continue with the existing logic
            const value = customFieldHandlers.getCustomFieldValue(
              row.original,
              customField,
            );

            // Only handle readonly fields with static content
            if (field.readOnly) {
              return (
                <div className="px-2 py-1 max-w-full">
                  <span
                    className="block w-full overflow-hidden text-ellipsis whitespace-nowrap"
                    title={
                      Array.isArray(value)
                        ? value.join(", ")
                        : value?.toString() || ""
                    }
                  >
                    {Array.isArray(value)
                      ? value.join(", ")
                      : value?.toString() || ""}
                  </span>
                </div>
              );
            }

            // For all editable fields (including empty ones), use EditableCell
            return (
              <EditableCell
                value={value}
                field={field}
                accountId={row.original.id}
                setAccounts={setAccounts}
                refetch={refetch}
                onSave={customFieldHandlers.handleCustomFieldUpdate}
              />
            );
          },
        };
      });

    return [...baseColumns, ...standardColumns, ...customColumns];
  }, [
    fields,
    standardFieldHandlers,
    customFieldHandlers,
    options.users,
    refetch,
    router,
    accounts,
  ]);

  // Update accounts state only when API response changes
  useEffect(() => {
    if (data?.pages) {
      setAccounts(data.pages.flatMap((page) => page.accounts) || []);
    }
  }, [data]);

  const table = useReactTable({
    data: accounts,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
      rowSelection,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    debugTable: false,
  });

  const tableContainerRef = useRef<HTMLDivElement>(null);
  const { rows } = table.getRowModel();

  // Set up virtualization
  const rowVirtualizer = useVirtualizer({
    count: rows.length + (hasNextPage ? 1 : 0), // Add one for the loading row if there are more pages
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 45, // Approximate row height
    overscan: 10,
  });

  // Get the virtualized rows
  const virtualRows = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();
  const paddingTop = virtualRows.length > 0 ? virtualRows[0].start : 0;
  const paddingBottom =
    virtualRows.length > 0
      ? totalSize - virtualRows[virtualRows.length - 1].end
      : 0;

  // Detect when we're near the bottom and fetch more data
  useEffect(() => {
    const scrollElement = tableContainerRef.current;
    if (!scrollElement) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollElement;

      // When user has scrolled to 60% of the available content, fetch more
      if (
        scrollTop + clientHeight >= scrollHeight * 0.6 &&
        hasNextPage &&
        !isFetchingNextPage
      ) {
        fetchNextPage();
      }
    };

    scrollElement.addEventListener("scroll", handleScroll);

    return () => scrollElement.removeEventListener("scroll", handleScroll);
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  // Show loading state only for initial page load
  if (isLoading || isFieldsLoading) {
    return <ThenaLoader loaderText="Loading..." />;
  }

  return (
    <div className="flex flex-col h-[calc(100vh-3.5rem)]">
      {/* Extracted filter bar component */}
      <AccountFiltersBar
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        isSearchExpanded={isSearchExpanded}
        setIsSearchExpanded={setIsSearchExpanded}
        totalAccounts={totalAccounts}
      />

      <div className="relative flex-1">
        <div ref={tableContainerRef} className="absolute inset-0 overflow-auto">
          <div className="inline-block min-w-full align-middle pb-16">
            <div className="relative">
              <Table className="table-fixed w-full">
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          key={header.id}
                          style={{
                            width: header.column.columnDef.size
                              ? `${header.column.columnDef.size}px`
                              : "150px",
                            maxWidth: header.column.columnDef.size
                              ? `${header.column.columnDef.size}px`
                              : "150px",
                          }}
                        >
                          {header.isPlaceholder ? null : (
                            <div
                              className={cn(
                                "flex items-center space-x-2",
                                header.column.getCanSort() &&
                                "cursor-pointer select-none",
                              )}
                              onClick={header.column.getToggleSortingHandler()}
                            >
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                              {header.column.getCanSort() && (
                                <div className="ml-2 h-4 w-4">
                                  {header.column.getIsSorted() === "desc" ? (
                                    <ChevronDown className="h-4 w-4" />
                                  ) : header.column.getIsSorted() === "asc" ? (
                                    <ChevronUp className="h-4 w-4" />
                                  ) : (
                                    <ChevronsUpDown className="h-4 w-4 text-gray-400" />
                                  )}
                                </div>
                              )}
                            </div>
                          )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-60 text-center border-b border-r border-border"
                      >
                        <div className="flex flex-col items-center justify-center py-8 text-center">
                          <ThenaLoader />
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : rows.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-60 text-center border-b border-r border-border"
                      >
                        <div className="flex flex-col items-center justify-center py-8 text-center">
                          <div className="rounded-full bg-primary/10 p-4 mb-4">
                            <FileText className="h-6 w-6 text-primary" />
                          </div>
                          <h3 className="text-lg font-semibold mb-2">
                            No accounts found
                          </h3>
                          <p className="text-sm text-muted-foreground max-w-[400px]">
                            There are no accounts to display at this time.
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    <>
                      {paddingTop > 0 && (
                        <tr>
                          <td style={{ height: `${paddingTop}px` }} />
                        </tr>
                      )}
                      {virtualRows.map((virtualRow) => {
                        // Check if this is the loading row
                        const isLoadingRow = virtualRow.index >= rows.length;

                        if (isLoadingRow) {
                          return (
                            <TableRow
                              key="loading-row"
                              className="hover:bg-secondary/30"
                            >
                              <TableCell
                                colSpan={columns.length}
                                className="px-4 py-2 border-b border-r border-border whitespace-nowrap text-center"
                              >
                                {isFetchingNextPage ? (
                                  <div className="flex items-center justify-center gap-2">
                                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                                    <span>Loading more accounts...</span>
                                  </div>
                                ) : hasNextPage ? (
                                  <span>Scroll to load more</span>
                                ) : (
                                  <span>No more accounts to load</span>
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        }

                        // Regular row rendering
                        const row = rows[virtualRow.index];
                        return (
                          <TableRow
                            key={row.id}
                            data-state={row.getIsSelected() && "selected"}
                            className="hover:bg-secondary/30"
                          >
                            {row.getVisibleCells().map((cell) => (
                              <TableCell
                                key={cell.id}
                                style={{
                                  width: cell.column.columnDef.size
                                    ? `${cell.column.columnDef.size}px`
                                    : "150px",
                                  maxWidth: cell.column.columnDef.size
                                    ? `${cell.column.columnDef.size}px`
                                    : "150px",
                                  overflow: "hidden",
                                }}
                              >
                                {flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext(),
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        );
                      })}
                      {paddingBottom > 0 && (
                        <tr>
                          <td style={{ height: `${paddingBottom}px` }} />
                        </tr>
                      )}
                    </>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </div>

      {accountId && <AccountsPageDrawer accountId={accountId} />}
    </div>
  );
}
