"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { useApi } from "@/hooks/use-api";
import { GET_ACCOUNT_BY_ID } from "@/services/accounts";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import ActivitiesPage from "./activities/page";
import NotesPage from "./notes/page";
import TasksPage from "./tasks/page";
import ThenaLoader from "@/components/thena-loader";

interface Account {
  id: string;
  name: string;
  industry: string;
  website: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export default function AccountDetailsPage() {
  const params = useParams();
  const [account, setAccount] = useState<Account | null>(null);
  const accountId = params.accountId as string;
  const { data: accountData, loading: isLoading } = useApi<Account>(
    GET_ACCOUNT_BY_ID(accountId),
    {},
    {
      isNextApi: true,
      enabled: true,
    },
  );
  useEffect(() => {
    if (accountData) {
      setAccount(accountData);
    }
  }, [accountData]);

  if (isLoading) {
    return <ThenaLoader loaderText="Loading account details..." />;
  }

  return (
    <div className="flex h-full w-full">
      {/* Main content with tabs (60%) */}
      <div className="w-[70%] p-8 h-full">
        <Tabs defaultValue="tasks" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="tickets">Tickets</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="notes">Notes</TabsTrigger>
            <TabsTrigger value="activities">Activities</TabsTrigger>
          </TabsList>
          <TabsContent value="tickets">
            <div />
          </TabsContent>
          <TabsContent value="tasks">
            <TasksPage />
          </TabsContent>
          <TabsContent value="notes">
            <NotesPage />
          </TabsContent>
          <TabsContent value="activities">
            <ActivitiesPage />
          </TabsContent>
        </Tabs>
      </div>

      {/* Account details sidebar (40%) */}
      <div className="w-[30%] border-l p-8 space-y-6 h-full">
        {account ? (
          <>
            <div>
              <h1 className="text-2xl font-bold">{account.name}</h1>
              <p className="text-gray-500">Account Details</p>
            </div>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Industry
                </label>
                <p>{account.industry}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">
                  Website
                </label>
                <p>{account.website}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">
                  Description
                </label>
                <p>{account.description}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">
                  Created At
                </label>
                <p>{new Date(account.createdAt).toLocaleDateString()}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">
                  Last Updated
                </label>
                <p>{new Date(account.updatedAt).toLocaleDateString()}</p>
              </div>
            </div>
          </>
        ) : (
          <div>No account details found</div>
        )}
      </div>
    </div>
  );
}
