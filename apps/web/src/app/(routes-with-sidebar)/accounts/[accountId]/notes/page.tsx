"use client";

import { DataTable } from "@/components/data-table";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogD<PERSON>cription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useApi } from "@/hooks/use-api";
import { GET_NOTES_BY_ID } from "@/services/accounts";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Plus } from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

interface Note {
  id: string;
  accountId: string;
  content: string;
  createdAt: string;
  createdBy: string;
}

const noteSchema = z.object({
  content: z.string().min(1, { message: "Content is required" }),
});

type NoteFormValues = z.infer<typeof noteSchema>;

function NoteForm({
  accountId,
  note,
  onSuccess,
}: {
  accountId: string;
  note?: Note;
  onSuccess: () => void;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<NoteFormValues>({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    resolver: zodResolver(noteSchema as any),
    defaultValues: {
      content: note?.content || "",
    },
  });

  const onSubmit = async (data: NoteFormValues) => {
    try {
      setIsLoading(true);

      let payload;
      if (!note) {
        // For POST, send all fields
        payload = {
          ...data,
          accountId,
        };
      } else {
        // For PUT, only send changed fields and ignore empty values
        payload = {};

        // Only add content if it has changed and is not empty
        if (data.content !== note.content && data.content) {
          payload.content = data.content;
        }
      }

      const response = await fetch(
        !note
          ? `/api/accounts/${accountId}/notes`
          : `/api/accounts/notes/${note.id}`,
        {
          method: note ? "PUT" : "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        throw new Error(
          note ? "Failed to update note" : "Failed to create note",
        );
      }

      toast.success(
        note ? "Note updated successfully" : "Note created successfully",
      );
      onSuccess();
      form.reset();
    } catch (error) {
      console.error(
        note ? "Error updating note:" : "Error creating note:",
        error,
      );
      toast.error(note ? "Failed to update note" : "Failed to create note");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="content">Content</Label>
        <Textarea id="content" {...form.register("content")} rows={5} />
        {form.formState.errors.content && (
          <p className="text-sm text-red-500">
            {form.formState.errors.content.message}
          </p>
        )}
      </div>
      <Button type="submit" disabled={isLoading}>
        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {note ? "Update" : "Create"} Note
      </Button>
    </form>
  );
}

export default function NotesPage() {
  const params = useParams();
  const [notes, setNotes] = useState<Note[]>([]);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const accountId = params.accountId as string;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { data: notesData, loading: isNotesLoading } = useApi<any>(
    GET_NOTES_BY_ID(accountId),
    {},
    {
      isNextApi: true,
      enabled: true,
    },
  );

  useEffect(() => {
    if (notesData) {
      setNotes(notesData?.data);
    }
  }, [notesData]);

  const handleEdit = (note: Note) => {
    setSelectedNote(note);
    setIsEditOpen(true);
  };

  const handleDelete = (note: Note) => {
    setSelectedNote(note);
    setIsDeleteOpen(true);
  };

  const handleDeleteNote = async () => {
    if (!selectedNote) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/accounts/notes/${selectedNote.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete note");
      }

      setIsDeleteOpen(false);
      window.location.reload();
    } catch (error) {
      console.error("Error deleting note:", error);
      toast.error("Failed to delete note");
    } finally {
      setIsLoading(false);
    }
  };

  const columns = [
    {
      accessorKey: "content",
      header: "Content",
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ row }: { row }) =>
        new Date(row.original.createdAt).toLocaleString(),
    },
    {
      accessorKey: "createdBy",
      header: "Created By",
    },
  ];

  return (
    <div>
      <Dialog>
        <DialogTrigger asChild>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Note
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Note</DialogTitle>
          </DialogHeader>
          <NoteForm
            accountId={accountId}
            onSuccess={() => {
              window.location.reload();
            }}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Note</DialogTitle>
          </DialogHeader>
          <NoteForm
            accountId={accountId}
            note={selectedNote}
            onSuccess={() => {
              setIsEditOpen(false);
              window.location.reload();
            }}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure?</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the
              note.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose>Cancel</DialogClose>
            <Button onClick={handleDeleteNote} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <DataTable
        columns={columns}
        data={notes}
        isLoading={isNotesLoading}
        tableName="Notes"
        showActions={true}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    </div>
  );
}
