"use client";

import InputWithIcon from "@/components/input-with-icon";
import <PERSON><PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import TooltipWrapper from "@/components/tooltip-wrapper";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
// Import the custom Avatar component that works correctly
import Avatar from "@/components/common/Avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useApi } from "@/hooks/use-api";
import { Team } from "@/types/kanban";
import { UserGroupIcon } from "@heroicons/react/24/outline";
import {
  BadgeCheck,
  Loader2,
  MoreHorizontal,
  Pencil,
  Plus,
  Search,
  UserMinus2,
} from "lucide-react";
import { useP<PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { RoutingConfig } from "../routing/types";

interface TeamGroup {
  id: string;
  name: string;
  description?: string;
  members: {
    id: string;
    name: string;
    avatarUrl?: string;
  }[];
  isDefault?: boolean;
}

export default function GroupsSettings() {
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [groups, setGroups] = useState<TeamGroup[]>([]);
  const [deleteGroupAlert, setDeleteGroupAlert] = useState(false);
  const [deletingGroupId, setDeletingGroupId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const { teamId } = useParams();

  const router = useRouter();

  const { data, loading: isLoading } = useApi<TeamGroup[]>(
    `/v1/teams/${teamId}/sub-teams`,
    {},
    { enabled: !!teamId, isNextApi: false, method: "GET" },
  );

  const { data: currentTeam } = useApi<Team>(
    `/v1/teams/${teamId}`,
    {},
    { enabled: !!teamId, isNextApi: false, method: "GET" },
  );

  useEffect(() => {
    if (data) {
      setGroups(data);
    }
  }, [data]);

  useEffect(() => {
    const getMembers = async (teamIds: string[]) => {
      const promises = teamIds.map((id) =>
        fetch(`/api/teams/${id}/members`).then((res) => res.json()),
      );
      const results = await Promise.all(promises);

      setGroups((prev) =>
        prev.map((group) => {
          // Find the matching result for this group
          const groupResult = results.find(
            (result) =>
              result?.data?.some?.((member) => member.teamId === group.id),
          );

          // Map the members data if found
          const members = groupResult?.data || [];

          return {
            ...group,
            members: members.map((member) => ({
              id: member.id,
              name: member.name,
              avatarUrl: member.avatarUrl,
            })),
          };
        }),
      );
    };

    if (data) {
      getMembers(data.map((group) => group.id));
    }
  }, [data]);

  const handleDeleteGroup = async (groupId: string) => {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/teams/${groupId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || "Failed to delete group");
      }

      setGroups((prev) => prev.filter((group) => group.id !== groupId));
      setError(null);
      setDeleteGroupAlert(false);
      setDeletingGroupId(null);
    } catch (e) {
      console.error("Error deleting group:", e);
      setError(
        e instanceof Error
          ? e.message
          : "Failed to delete group. Please try again later.",
      );
    } finally {
      setIsDeleting(false);
    }
  };

  const filteredGroups = groups?.filter((group) =>
    group.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleUpdateConfig = async (
    groupId: string,
    updates: Partial<RoutingConfig>,
  ) => {
    try {
      const response = await fetch(`/api/teams/${groupId}/configurations`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error("Failed to update routing settings");
      }

      const data = await response.json();
      console.log({ data });
    } catch (error) {
      toast.error("Failed to mark as default");
      console.error("Error updating group as default:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="w-full h-full min-h-[calc(100vh-150px)] overflow-y-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  if (!groups?.length) {
    return <EmptyState teamId={teamId as string} />;
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <main className="flex-1 overflow-y-auto">
      <div className="container mx-auto py-6">
        <div className="max-w-[640px] mx-auto">
          <AlertDialog
            open={deleteGroupAlert}
            onOpenChange={setDeleteGroupAlert}
          >
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure you want to delete the group?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete
                  your group and remove any routing rules set for the group.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel disabled={isDeleting}>
                  Cancel
                </AlertDialogCancel>
                <AlertDialogAction
                  onClick={(event) => {
                    event.preventDefault();
                    if (deletingGroupId) {
                      handleDeleteGroup(deletingGroupId);
                    }
                  }}
                  disabled={isDeleting}
                  className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Deleting...
                    </>
                  ) : (
                    "Delete"
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <div className="flex flex-col gap-6">
            <div className="space-y-2">
              <h1 className="text-2xl font-medium tracking-tight">Groups</h1>
              <p className="text-sm text-muted-foreground">
                Manage groups of your team.
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <InputWithIcon
                  type="text"
                  Icon={Search}
                  value={searchQuery}
                  placeholder="Search groups"
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-[300px] ring-0 rounded-sm focus:ring-0 focus-visible:ring-0"
                />
              </div>
              <Button
                onClick={() => {
                  router.push(`/dashboard/${teamId}/settings/groups/create`);
                }}
                variant="default"
                size="sm"
                className="flex items-center gap-2 text-[14px] bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                <Plus className="h-4 w-4" />
                <span>Create group</span>
              </Button>
            </div>

            <div className="flex flex-col gap-4">
              {groups.length === 0 ? (
                <EmptyState teamId={teamId as string} />
              ) : filteredGroups.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-sm text-muted-foreground">
                    No groups found matching your search.
                  </p>
                </div>
              ) : (
                filteredGroups.map((group) => (
                  <div
                    key={group.id}
                    className="border rounded-sm px-4 py-3 bg-card flex items-center justify-between hover:bg-muted/50 transition-colors cursor-pointer"
                    onClick={() =>
                      router.push(
                        `/dashboard/${teamId}/settings/groups/${group.id}`,
                      )
                    }
                  >
                    <div className="flex items-center gap-4">
                      <span className="text-sm font-medium">{group.name}</span>
                      {currentTeam?.fallbackSubTeam === group.id && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-500/10 text-green-500">
                          <BadgeCheck className="h-3 w-3" />
                          Default
                        </span>
                      )}
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="flex -space-x-2">
                        {group.members === undefined ? null : group.members
                            .length === 0 ? (
                          <p className="text-xs text-muted-foreground">
                            No members
                          </p>
                        ) : (
                          <>
                            {group.members.slice(0, 4).map((member) => (
                              <TooltipWrapper
                                key={member.id}
                                tooltipContent={member.name}
                              >
                                <Avatar
                                  src={member.avatarUrl}
                                  alt={member.name}
                                  fallbackText={member.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")
                                    .toUpperCase()}
                                  imgClassnames="h-6 w-6 border-2 border-background"
                                  fallbackTextClassnames="bg-muted text-foreground"
                                />
                              </TooltipWrapper>
                            ))}
                            {group.members.length > 4 && (
                              <TooltipWrapper
                                tooltipContentClassname="bg-background text-color-text border"
                                tooltipContent={
                                  <div className="flex flex-col gap-2 p-1">
                                    {group.members.slice(4).map((member) => (
                                      <div
                                        key={member.id}
                                        className="flex items-center gap-2"
                                      >
                                        <Avatar
                                          src={member.avatarUrl}
                                          alt={member.name}
                                          fallbackText={member.name
                                            .split(" ")
                                            .map((n) => n[0])
                                            .join("")
                                            .toUpperCase()}
                                          imgClassnames="h-6 w-6"
                                          fallbackTextClassnames="bg-muted text-foreground"
                                        />
                                        <span className="text-sm">
                                          {member.name}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                }
                              >
                                <div className="h-6 w-6 rounded-full bg-gray-100 flex items-center justify-center border-2 border-background">
                                  <span className="text-xs text-gray-600 font-medium">
                                    +{group.members.length - 4}
                                  </span>
                                </div>
                              </TooltipWrapper>
                            )}
                          </>
                        )}
                      </div>
                      <DropdownMenu modal={false}>
                        <DropdownMenuTrigger
                          asChild
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 p-0"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(
                                `/dashboard/${teamId}/settings/groups/${group.id}`,
                              );
                            }}
                          >
                            <Pencil className="h-4 w-4 mr-2" />
                            Edit group
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleUpdateConfig(teamId as string, {
                                fallbackSubTeam: group.id || null,
                              });
                            }}
                          >
                            <BadgeCheck className="h-4 w-4 mr-2" />
                            Make as default
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              setDeleteGroupAlert(true);
                              setDeletingGroupId(group.id);
                            }}
                            className="text-[var(--color-text-error)]"
                          >
                            <UserMinus2 className="h-4 w-4 mr-2" />
                            Delete group
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}

function EmptyState({ teamId }: { teamId: string }) {
  const router = useRouter();
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="max-w-md w-full px-6 py-12 text-center">
        <div className="space-y-6">
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-primary/5 animate-pulse rounded-full blur-xl" />
            <div className="relative mx-auto w-20 h-20 flex items-center justify-center bg-primary/10 rounded-full">
              <UserGroupIcon className="h-10 w-10 text-primary/80 transition-transform duration-200 hover:scale-110" />
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-medium tracking-tight">
              Let&apos;s create your group
            </h3>
            <p className="text-muted-foreground text-sm">
              Organize your team by creating a group to streamline collaboration
              and workflows.
            </p>
          </div>
          <div className="pt-2">
            <Button
              size="lg"
              variant="default"
              className="mx-auto flex items-center gap-2"
              onClick={() =>
                router.push(`/dashboard/${teamId}/settings/groups/create`)
              }
            >
              <Plus className="h-4 w-4" />
              <span>Create your first group</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
