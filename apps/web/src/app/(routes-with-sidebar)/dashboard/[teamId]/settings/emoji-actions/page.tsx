"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import data from "@emoji-mart/data";
import Picker from "@emoji-mart/react";
import { init, SearchIndex } from "emoji-mart";
import { ArrowRight, Pencil, Plus, Search, Smile, Trash2 } from "lucide-react";
import { useParams } from "next/navigation";
import { createElement, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

init({ data });

/*
  This is the solution to the following error:
  Property 'em-emoji' does not exist on type 'JSX.IntrinsicElements'.ts
*/

// Define em-emoji web component inside React
// https://github.com/missive/emoji-mart#-emoji-component
interface EmEmojiProps {
  id?: string;
  shortcodes?: string;
  native?: string;
  size?: string;
  fallback?: string;
  set?: "native" | "apple" | "facebook" | "google" | "twitter";
  skin?: 1 | 2 | 3 | 4 | 5 | 6;
}
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      "em-emoji": EmEmojiProps;
    }
  }
}

interface EmojiConfig {
  id: string;
  uid: string;
  emoji: { native: string; id: string };
  type: "OUTBOUND" | "INBOUND";
  actionType: "status" | "escalation";
  actionValue: string;
}

export default function EmojiActionsPage() {
  const [isDeleting, setIsDeleting] = useState(false);
  const [emojiConfigs, setEmojiConfigs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isTypeDialogOpen, setIsTypeDialogOpen] = useState(false);
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [isReactionDialogOpen, setIsReactionDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingConfig, setEditingConfig] = useState<EmojiConfig | null>(null);
  const [actions, setActions] = useState<string[]>([]);
  const [actionsLoading, setActionsLoading] = useState(true);
  const params = useParams();
  const teamId = params.teamId;

  const statusDisplayNames: Record<string, string> = {
    "ticket-to-closed": "Closed",
    "ticket-to-in-progress": "In-progress",
    "ticket-to-on-hold": "On-hold",
  };

  const statusList = useMemo(() => {
    return actions.map((action) => ({
      id: action,
      name:
        statusDisplayNames[action] ||
        action.replace("ticket-to-", "").replace(/-/g, " "),
    }));
  }, [actions]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setActionsLoading(true);
        const response = await fetch(`/api/v1/emojis/actions`);

        if (!response.ok) {
          throw new Error("Failed to fetch emoji configurations");
        }

        const data = await response.json();
        setActions(data.data?.data ?? []);
      } catch (err) {
        console.error("Error fetching emoji configurations:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch emoji configurations",
        );
        setActions([]); // Ensure actions is always an array even on error
      } finally {
        setActionsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Function to handle edit button click
  const handleEditClick = async (config) => {
    const getEmojiList = await SearchIndex.search(config.emoji);
    const getEmoji = getEmojiList.find((emoji) => emoji.id === config.emoji);
    setEditingConfig({
      ...config,
      emoji: {
        native: getEmoji ? getEmoji?.skins[0]?.native : config.emoji,
        id: getEmoji ? getEmoji?.id : config.emoji,
      },
    });
    if (config.type === "OUTBOUND") {
      setIsActionDialogOpen(true);
    } else {
      setIsReactionDialogOpen(true);
    }
  };

  // Function to close dialogs and reset editing state
  const handleCloseDialogs = () => {
    setIsActionDialogOpen(false);
    setIsReactionDialogOpen(false);
    setEditingConfig(null);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/v1/emojis/actions/${teamId}`);

        if (!response.ok) {
          throw new Error("Failed to fetch emoji configurations");
        }

        const data = await response.json();
        // Map the API response to match our component's structure
        const mappedConfigs = (data.data?.data || []).map((config) => ({
          ...config,
          actionType: config.action === "escalation" ? "escalation" : "status",
          actionValue:
            config.action === "escalation" ? "escalation" : config.action,
          type: config.flow,
        }));
        setEmojiConfigs(mappedConfigs);
      } catch (err) {
        console.error("Error fetching emoji configurations:", err);
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch emoji configurations",
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [teamId]);

  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="rounded-full bg-gray-100 p-4 dark:bg-gray-800">
        <Smile className="h-8 w-8 text-gray-400" />
      </div>
      <h3 className="mt-4 text-lg font-semibold">
        No emoji configurations created
      </h3>
      <p className="mt-2 max-w-sm text-sm text-gray-500">
        Emoji configurations let you automate actions with emoji reactions.
      </p>
      <Button className="mt-4" onClick={() => setIsTypeDialogOpen(true)}>
        Create emoji configuration
      </Button>
    </div>
  );

  const TypeSelectionDialog = () => (
    <Dialog open={isTypeDialogOpen} onOpenChange={setIsTypeDialogOpen}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Choose configuration type</DialogTitle>
          <DialogDescription>
            Would you like to set up automatic emoji responses or actions
            triggered by emoji reactions?
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-4 py-4">
          <div
            className="flex flex-col items-center p-6 border rounded-md cursor-pointer hover:bg-accent/10 transition-colors"
            onClick={() => {
              setIsTypeDialogOpen(false);
              setIsActionDialogOpen(true);
            }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="flex items-center justify-center px-3 py-1 rounded-md bg-green-100 text-green-700 font-medium text-sm">
                Closed
              </div>
              <div className="mx-2 text-gray-500">→</div>
              <div className="text-2xl">✅</div>
            </div>
            <h3 className="font-medium mb-1">Auto emoji</h3>
            <p className="text-xs text-center text-muted-foreground">
              When a ticket status changes, system automatically adds an emoji
              reaction.
            </p>
          </div>

          <div
            className="flex flex-col items-center p-6 border rounded-md cursor-pointer hover:bg-accent/10 transition-colors"
            onClick={() => {
              setIsTypeDialogOpen(false);
              setIsReactionDialogOpen(true);
            }}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="text-2xl">🚨</div>
              <div className="mx-2 text-gray-500">→</div>
              <div className="flex items-center justify-center px-3 py-1 rounded-md bg-orange-100 text-orange-700 font-medium text-sm">
                Escalate
              </div>
            </div>
            <h3 className="font-medium mb-1">Emoji reaction</h3>
            <p className="text-xs text-center text-muted-foreground">
              When someone adds an emoji reaction, system changes the ticket
              status.
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsTypeDialogOpen(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  const ActionDialog = () => {
    const [actionType, setActionType] = useState<"status" | "escalation">(
      editingConfig?.actionType || "status",
    );
    const [selectedEmoji, setSelectedEmoji] = useState({
      native: editingConfig?.emoji || "😀",
      id: editingConfig?.emoji || '"grinning"',
    });
    const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState(
      editingConfig?.actionValue || "",
    );

    useEffect(() => {
      if (editingConfig) {
        setActionType(editingConfig.actionType);
        setSelectedEmoji({
          native: editingConfig.emoji.native,
          id: editingConfig.emoji.id,
        });
        setSelectedStatus(editingConfig.actionValue);
      }
    }, [editingConfig]);

    const handleEmojiSelect = (emoji: { native: string; id: string }) => {
      setSelectedEmoji({
        native: emoji.native,
        id: emoji.id,
      });
      setIsEmojiPickerOpen(false);
    };

    const handleCreate = async () => {
      try {
        setIsSubmitting(true);
        const endpoint = editingConfig
          ? `/api/v1/emojis/actions/${teamId}/${editingConfig.uid}`
          : `/api/v1/emojis/actions/${teamId}`;

        const response = await fetch(endpoint, {
          method: editingConfig ? "PATCH" : "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            emojis: [selectedEmoji.id],
            flow: "OUTBOUND",
            action: actionType === "status" ? selectedStatus : "escalation",
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);

          if (response.status === 400) {
            toast.error("This emoji configuration already exists");
            return;
          }

          if (response.status === 500) {
            toast.error("Failed to update emoji action.");
            return;
          }

          const defaultErrorMessage = editingConfig
            ? "Failed to update emoji action"
            : "Failed to create emoji action";
          throw new Error(errorData?.message || defaultErrorMessage);
        }

        // Refresh the list
        const fetchData = async () => {
          const response = await fetch(`/api/v1/emojis/actions/${teamId}`);
          if (response.ok) {
            const data = await response.json();
            const mappedConfigs = (data.data?.data || []).map((config) => ({
              ...config,
              actionType:
                config.action === "escalation" ? "escalation" : "status",
              actionValue:
                config.action === "escalation" ? "escalation" : config.action,
              type: config.flow,
            }));
            setEmojiConfigs(mappedConfigs);
          }
        };
        await fetchData();

        handleCloseDialogs();
      } catch (error) {
        console.error("Error handling emoji action:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Failed to handle emoji action",
        );
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <Dialog
        open={isActionDialogOpen}
        onOpenChange={(open) => {
          if (!open) setEditingConfig(null);
          setIsActionDialogOpen(open);
        }}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingConfig ? "Edit auto emoji" : "Create an auto emoji"}
            </DialogTitle>
            <DialogDescription>
              Configure an emoji that will be automatically added when a
              specific event occurs.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="emoji">Emoji</Label>
              <Popover
                open={isEmojiPickerOpen}
                onOpenChange={setIsEmojiPickerOpen}
              >
                <PopoverTrigger asChild>
                  <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm items-center cursor-pointer">
                    <span className="text-xl mr-2">
                      {" "}
                      {createElement("em-emoji", {
                        id: selectedEmoji.native,
                      } as React.HTMLAttributes<HTMLElement>)}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Click to select emoji
                    </span>
                  </div>
                </PopoverTrigger>
                <PopoverContent className="p-0 w-max">
                  <Picker
                    onEmojiSelect={handleEmojiSelect}
                    theme="light"
                    data={data}
                  />
                </PopoverContent>
              </Popover>
              <p className="text-xs text-muted-foreground">
                This emoji will be added when the event occurs.
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="action-type">Action type</Label>
              <Select
                defaultValue="status"
                onValueChange={(value) =>
                  setActionType(value as "status" | "escalation")
                }
                value={actionType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select action type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="status">Status change</SelectItem>
                  {/* <SelectItem value="escalation">Escalation</SelectItem> */}
                </SelectContent>
              </Select>
            </div>

            {actionType === "status" ? (
              <div className="grid gap-2">
                <Label htmlFor="action-value">Status</Label>
                <Select
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusList.map((status) => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  The emoji will be added when a ticket is changed to this
                  status.
                </p>
              </div>
            ) : (
              <div className="grid gap-2">
                <div className="rounded-md bg-blue-50 p-3">
                  <p className="text-sm text-blue-700">
                    Escalation is a state in itself. The emoji will be added
                    when a ticket is escalated.
                  </p>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCloseDialogs}>
              Cancel
            </Button>
            <Button onClick={handleCreate} disabled={isSubmitting}>
              {isSubmitting
                ? "Creating..."
                : editingConfig
                ? "Save changes"
                : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const ReactionDialog = () => {
    const [reactionType, setReactionType] = useState<"status" | "escalation">(
      editingConfig?.actionType || "status",
    );
    const [selectedEmoji, setSelectedEmoji] = useState({
      native: editingConfig?.emoji || "😀",
      id: editingConfig?.emoji || '"grinning"',
    });
    const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState(
      editingConfig?.actionValue || "",
    );

    useEffect(() => {
      if (editingConfig) {
        setReactionType(editingConfig.actionType);
        setSelectedEmoji({
          native: editingConfig.emoji.native,
          id: editingConfig.emoji.id,
        });
        setSelectedStatus(editingConfig.actionValue);
      }
    }, [editingConfig]);

    const handleEmojiSelect = (emoji: { native: string; id: string }) => {
      setSelectedEmoji({
        native: emoji.native,
        id: emoji.id,
      });
      setIsEmojiPickerOpen(false);
    };

    const handleCreate = async () => {
      try {
        setIsSubmitting(true);
        const endpoint = editingConfig
          ? `/api/v1/emojis/actions/${teamId}/${editingConfig.uid}`
          : `/api/v1/emojis/actions/${teamId}`;

        const response = await fetch(endpoint, {
          method: editingConfig ? "PATCH" : "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            emojis: [selectedEmoji.id],
            flow: "INBOUND",
            action: reactionType === "status" ? selectedStatus : "escalation",
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);

          if (response.status === 400) {
            toast.error("This emoji configuration already exists");
            return;
          }

          if (response.status === 500) {
            toast.error("Failed to update emoji action.");
            return;
          }

          const defaultErrorMessage = editingConfig
            ? "Failed to update emoji action"
            : "Failed to create emoji action";
          throw new Error(errorData?.message || defaultErrorMessage);
        }

        // Refresh the list
        const fetchData = async () => {
          const response = await fetch(`/api/v1/emojis/actions/${teamId}`);
          if (response.ok) {
            const data = await response.json();
            const mappedConfigs = (data.data?.data || []).map((config) => ({
              ...config,
              actionType:
                config.action === "escalation" ? "escalation" : "status",
              actionValue:
                config.action === "escalation" ? "escalation" : config.action,
              type: config.flow,
            }));
            setEmojiConfigs(mappedConfigs);
          }
        };
        await fetchData();

        handleCloseDialogs();
      } catch (error) {
        console.error("Error handling emoji reaction:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Failed to handle emoji reaction",
        );
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <Dialog
        open={isReactionDialogOpen}
        onOpenChange={(open) => {
          if (!open) setEditingConfig(null);
          setIsReactionDialogOpen(open);
        }}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingConfig
                ? "Edit emoji reaction"
                : "Create an emoji reaction"}
            </DialogTitle>
            <DialogDescription>
              Configure an action that will be triggered when someone reacts
              with a specific emoji.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="emoji">Emoji</Label>
              <Popover
                open={isEmojiPickerOpen}
                onOpenChange={setIsEmojiPickerOpen}
              >
                <PopoverTrigger asChild>
                  <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm items-center cursor-pointer">
                    <span className="text-xl mr-2">
                      {createElement("em-emoji", {
                        id: selectedEmoji.native,
                      } as React.HTMLAttributes<HTMLElement>)}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Click to select emoji
                    </span>
                  </div>
                </PopoverTrigger>
                <PopoverContent className="p-0 w-max">
                  <Picker
                    onEmojiSelect={handleEmojiSelect}
                    theme="light"
                    data={data}
                  />
                </PopoverContent>
              </Popover>
              <p className="text-xs text-muted-foreground">
                When someone reacts with this emoji, the configured action will
                be triggered.
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="reaction-type">Reaction type</Label>
              <Select
                defaultValue="status"
                onValueChange={(value) =>
                  setReactionType(value as "status" | "escalation")
                }
                value={reactionType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select reaction type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="status">Change status</SelectItem>
                  {/* <SelectItem value="escalation">Escalate</SelectItem> */}
                </SelectContent>
              </Select>
            </div>

            {reactionType === "status" ? (
              <div className="grid gap-2">
                <Label htmlFor="reaction-value">Change to status</Label>
                <Select
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusList.map((status) => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  The ticket status will be changed to this when someone reacts
                  with the selected emoji.
                </p>
              </div>
            ) : (
              <div className="grid gap-2">
                <div className="rounded-md bg-orange-50 p-3">
                  <p className="text-sm text-orange-700">
                    Escalation is a state in itself. The ticket will be
                    escalated when someone reacts with the selected emoji.
                  </p>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCloseDialogs}>
              Cancel
            </Button>
            <Button onClick={handleCreate} disabled={isSubmitting}>
              {isSubmitting
                ? "Creating..."
                : editingConfig
                ? "Save changes"
                : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const handleDelete = async (emojiActionId: string) => {
    try {
      setIsDeleting(true);

      const response = await fetch(
        `/api/v1/emojis/actions/${teamId}/${emojiActionId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        throw new Error("Failed to delete emoji action");
      }

      // Refresh the list after successful deletion
      const fetchData = async () => {
        const response = await fetch(`/api/v1/emojis/actions/${teamId}`);
        if (response.ok) {
          const data = await response.json();
          const mappedConfigs = (data.data?.data || []).map((config) => ({
            ...config,
            actionType:
              config.action === "escalation" ? "escalation" : "status",
            actionValue:
              config.action === "escalation" ? "escalation" : config.action,
            type: config.flow,
          }));
          setEmojiConfigs(mappedConfigs);
        }
      };
      await fetchData();

      toast.success("Emoji action deleted successfully");
    } catch (error) {
      console.error("Error deleting emoji action:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to delete emoji action";
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading || actionsLoading) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <p className="text-red-500">Error: {error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Try again
        </Button>
      </div>
    );
  }

  const filteredConfigs = emojiConfigs.filter((config, index, self) => {
    const matchesSearch =
      config.emoji.toLowerCase().includes(searchQuery.toLocaleLowerCase()) ||
      config.actionType.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.actionValue.toLowerCase().includes(searchQuery.toLowerCase());

    if (!matchesSearch) return false;

    const isDuplicate =
      self.findIndex(
        (item) =>
          item.emoji === config.emoji &&
          item.actionValue === config.actionValue &&
          item.type === config.type,
      ) !== index;

    return !isDuplicate;
  });

  return (
    <div
      className="space-y-6 p-6 max-w-[640px] mx-auto"
      style={{ fontSize: "14px" }}
    >
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col space-y-1.5">
          <h2 className="text-2xl font-medium">Emoji actions</h2>
          <p className="text-muted-foreground">
            Configure automatic emoji reactions and actions triggered by emoji
            reactions.
          </p>
        </div>
        <div className="flex items-center justify-between mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground pointer-events-none" />
            <Input
              placeholder="Search..."
              className="pl-10"
              style={{ width: "280px" }}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={() => setIsTypeDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add
          </Button>
        </div>

        {filteredConfigs.length === 0 ? (
          searchQuery ? (
            <div className="text-center py-8 text-muted-foreground">
              No emoji configurations match your search.
            </div>
          ) : (
            <EmptyState />
          )
        ) : (
          <div className="space-y-4">
            {filteredConfigs.map((config) => (
              <div
                key={config.id}
                className="flex items-center justify-between p-4 bg-background border rounded-md"
              >
                <div className="flex items-center gap-4">
                  <div className="text-2xl">
                    {createElement("em-emoji", {
                      id: config.emoji,
                    } as React.HTMLAttributes<HTMLElement>)}
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Badge
                        variant={
                          config.type === "OUTBOUND" ? "default" : "secondary"
                        }
                        className={cn(
                          "text-xs font-normal px-2 py-1 shadow-none",
                          config.type === "OUTBOUND"
                            ? "bg-blue-100 text-blue-700 hover:bg-blue-100 border border-blue-200"
                            : "bg-green-100 text-green-700 hover:bg-green-100 border border-green-200",
                        )}
                      >
                        {config.type === "OUTBOUND"
                          ? "Auto emoji"
                          : "Emoji reaction"}
                      </Badge>
                      {config.type === "OUTBOUND" ? (
                        <>
                          <span className="font-medium">When</span>
                          {config.actionType === "status" ? (
                            <>
                              <span className="px-2 py-1 text-xs rounded-md bg-gray-100">
                                Status changes to
                              </span>
                              <span className="font-medium">
                                {config.actionValue
                                  .replace("ticket-to-", "")
                                  .replace(/-/g, " ")}
                              </span>
                            </>
                          ) : (
                            <span className="px-2 py-1 text-xs rounded-md bg-gray-100">
                              Escalated
                            </span>
                          )}
                        </>
                      ) : (
                        <>
                          <span className="font-medium">On reaction</span>
                          <ArrowRight className="h-4 w-4" />
                          {config.actionType === "status" ? (
                            <>
                              <span className="px-2 py-1 text-xs rounded-md bg-gray-100">
                                Change status to
                              </span>
                              <span className="font-medium">
                                {config.actionValue
                                  .replace("ticket-to-", "")
                                  .replace(/-/g, " ")}
                              </span>
                            </>
                          ) : (
                            <span className="px-2 py-1 text-xs rounded-md bg-gray-100">
                              Escalate
                            </span>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEditClick(config)}
                    disabled={isDeleting}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDelete(config.uid)}
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <TypeSelectionDialog />
      <ActionDialog />
      <ReactionDialog />
    </div>
  );
}
