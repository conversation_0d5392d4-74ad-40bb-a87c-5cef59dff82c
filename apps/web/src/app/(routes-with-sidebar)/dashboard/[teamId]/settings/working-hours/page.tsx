"use client";

import { BusinessHours } from "@/components/business-hours";
import Then<PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import { DEFAULT_CONFIG } from "@/constants/working-hours";
import { DayConfig, WorkingHoursConfig } from "@/types/team";
import { formatHolidayDate, validateWorkingHours } from "@/utils/settingsUtils";
import { format } from "date-fns";
import { use, useEffect, useState } from "react";
import { toast } from "sonner";

export default function WorkingHoursSettings({
  params,
}: {
  params: Promise<{ teamId: string }>;
}) {
  const resolvedParams = use(params);
  const teamId = resolvedParams.teamId;
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [config, setConfig] = useState<WorkingHoursConfig>(DEFAULT_CONFIG);
  const [hasChanges, setHasChanges] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch(`/api/teams/${teamId}/configurations`);

        if (!response.ok) {
          throw new Error("Failed to fetch working hours configuration");
        }

        const responseData = await response.json();

        const data = responseData.data;

        if (data?.dailyConfig) {
          const transformedConfig: WorkingHoursConfig = {
            dayWiseHoursEnabled: true,
            workingDays: Object.entries(data.dailyConfig)
              .filter(([_, dayConfig]) => (dayConfig as DayConfig).isActive)
              .map(([day]) => day.charAt(0).toUpperCase() + day.slice(1)),
            daySpecificHours: Object.entries(data.dailyConfig).reduce(
              (acc, [day, dayConfig]) => {
                const slots = Array.isArray((dayConfig as DayConfig).slots)
                  ? [...(dayConfig as DayConfig).slots]
                  : [{ start: "00:00", end: "23:00" }];

                return {
                  ...acc,
                  [day.charAt(0).toUpperCase() + day.slice(1)]: {
                    isActive: (dayConfig as DayConfig).isActive,
                    slots,
                  },
                };
              },
              {},
            ),
            timezone: data.timezone,
            holidays: data.holidays || [],
            defaultHours: data.commonDailyConfig
              ? []
              : (Object.values(data.dailyConfig)[0] as DayConfig)?.slots || [
                  { start: "00:00", end: "23:00" },
                ],
            commonDailyConfig: false,
            commonSlots: [],
          };
          setConfig(transformedConfig);
          setHasChanges(false);
        } else {
          setConfig({
            ...DEFAULT_CONFIG,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          });
          setHasChanges(false);
        }
      } catch (error) {
        console.error("Error in fetchConfig:", error);
        toast.error(
          error instanceof Error
            ? error.message
            : "Failed to fetch configuration",
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfig();
  }, [teamId]);

  const handleSave = async () => {
    try {
      // Validate before saving
      const validationError = validateWorkingHours(config);
      if (validationError) {
        toast.error(validationError);
        return;
      }

      setIsSaving(true);
      const ALL_DAYS = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
      ];
      const dailyConfig = ALL_DAYS.reduce((acc, day) => {
        const dayLower = day.toLowerCase();
        const isActive = config.workingDays.includes(day);
        const dayConfig = {
          isActive,
          slots: isActive
            ? config.dayWiseHoursEnabled
              ? config.daySpecificHours[day]?.slots || []
              : config.defaultHours
            : [],
        };

        return {
          ...acc,
          [dayLower]: dayConfig,
        };
      }, {});

      const payload = {
        timezone: config.timezone === "" ? "UNSET" : config.timezone,
        holidays: config.holidays.map((item) =>
          format(formatHolidayDate(item), "dd-MM-yyyy"),
        ),
        ...(config.dayWiseHoursEnabled
          ? { dailyConfig }
          : {
              commonDailyConfig: true,
              commonSlots: config.defaultHours,
            }),
      };

      const response = await fetch(`/api/teams/${teamId}/configurations`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error("Failed to save working hours configuration");
      }

      toast.success("Working hours updated successfully");
      setHasChanges(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to save configuration",
      );
    } finally {
      setIsSaving(false);
    }
  };

  const dayOrder = {
    Monday: 0,
    Tuesday: 1,
    Wednesday: 2,
    Thursday: 3,
    Friday: 4,
    Saturday: 5,
    Sunday: 6,
  };

  const sortDays = (days: string[]) => {
    return [...days].sort((a, b) => dayOrder[a] - dayOrder[b]);
  };

  const handleToggleDay = (day: string) => {
    setConfig((prev) => {
      const isAdding = !prev.workingDays.includes(day);
      const newWorkingDays = sortDays(
        isAdding
          ? [...prev.workingDays, day]
          : prev.workingDays.filter((d) => d !== day),
      );

      // Update day-specific hours
      const updatedDaySpecificHours = { ...prev.daySpecificHours };

      if (isAdding) {
        // Initialize slots for newly selected day
        updatedDaySpecificHours[day] = {
          isActive: true,
          slots: [{ start: "00:00", end: "23:59" }],
        };
      } else {
        // Remove slots for deselected day
        delete updatedDaySpecificHours[day];
      }

      return {
        ...prev,
        workingDays: newWorkingDays,
        daySpecificHours: updatedDaySpecificHours,
      };
    });
    setHasChanges(true);
  };
  const handleTimeChange = (
    day: string | null,
    slotIndex: number,
    type: "start" | "end",
    value: string,
  ) => {
    if (config.daySpecificHours && day) {
      setConfig((prev) => ({
        ...prev,
        daySpecificHours: {
          ...prev.daySpecificHours,
          [day]: {
            ...prev.daySpecificHours[day],
            slots:
              prev.daySpecificHours[day]?.slots?.map((slot, idx) =>
                idx === slotIndex ? { ...slot, [type]: value } : slot,
              ) || [],
          },
        },
      }));
    } else {
      setConfig((prev) => ({
        ...prev,
        defaultHours: prev.defaultHours.map((slot, idx) =>
          idx === slotIndex ? { ...slot, [type]: value } : slot,
        ),
      }));
    }
    setHasChanges(true);
  };

  const handleAddTimeSlot = (day: string | null) => {
    if (config.daySpecificHours && day) {
      setConfig((prev) => ({
        ...prev,
        daySpecificHours: {
          ...prev.daySpecificHours,
          [day]: {
            ...prev.daySpecificHours[day],
            slots: [
              ...(prev.daySpecificHours[day]?.slots || []),
              { start: "00:00", end: "23:00" },
            ],
          },
        },
      }));
    } else {
      setConfig((prev) => ({
        ...prev,
        defaultHours: [...prev.defaultHours, { start: "00:00", end: "23:00" }],
      }));
    }
    setHasChanges(true);
  };

  const handleRemoveTimeSlot = (day: string | null, slotIndex: number) => {
    if (day) {
      setConfig((prev) => ({
        ...prev,
        daySpecificHours: {
          ...prev.daySpecificHours,
          [day]: {
            ...prev.daySpecificHours[day],
            slots:
              prev.daySpecificHours[day]?.slots?.filter(
                (_, idx) => idx !== slotIndex,
              ) || [],
          },
        },
      }));
    } else {
      setConfig((prev) => ({
        ...prev,
        defaultHours: prev.defaultHours.filter((_, idx) => idx !== slotIndex),
      }));
    }
    setHasChanges(true);
  };

  const handleAddHoliday = (date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd");
    if (config.holidays.includes(dateStr)) return;

    setConfig((prev) => ({
      ...prev,
      holidays: [...prev.holidays, dateStr].sort(),
    }));
    setHasChanges(true);
  };

  const handleRemoveHoliday = (date: string) => {
    setConfig((prev) => ({
      ...prev,
      holidays: prev.holidays.filter((h) => h !== date),
    }));
    setHasChanges(true);
  };

  if (isLoading) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-y-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="flex-1 bg-background py-8 flex items-center justify-center w-[640px]">
        <div className="w-full">
          <div className="mb-8">
            <h2 className="text-2xl font-medium">Working hours</h2>
            <p className="text-sm text-muted-foreground mt-0.5">
              Configure your team&apos;s working hours to align with SLA
              requirements.
            </p>
          </div>
          <BusinessHours
            config={config}
            setConfig={setConfig}
            handleToggleDay={handleToggleDay}
            handleTimeChange={handleTimeChange}
            handleAddTimeSlot={handleAddTimeSlot}
            handleRemoveTimeSlot={handleRemoveTimeSlot}
            handleAddHoliday={handleAddHoliday}
            handleRemoveHoliday={handleRemoveHoliday}
            hasChanges={hasChanges}
            setHasChanges={setHasChanges}
            isLoading={isLoading}
            isSaving={isSaving}
            handleSave={handleSave}
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
            isFormEnabled={true} //Change this to false when the form is not enabled
            accordionClassName="rounded-sm border bg-card text-card-foreground -mt-1"
            triggerClassName="flex w-full items-center justify-between px-4 py-3 bg-[var(--color-bg-subtle)] rounded-t-[4px] data-[state=open]:border-b"
            contentClassName="p-6 rounded-[4px] bg-[var(--color-bg)]"
            iconClassName="h-4 w-4 text-muted-text shrink-0 transition-transform duration-200"
          />
        </div>
      </div>
    </div>
  );
}
