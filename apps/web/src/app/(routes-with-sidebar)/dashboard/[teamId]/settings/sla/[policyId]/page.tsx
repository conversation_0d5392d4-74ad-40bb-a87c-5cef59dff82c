"use client";
import Then<PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import ClientForm from "./client-form";

export default function EditSLAPolicy() {
  const params = useParams();
  const teamId = params.teamId as string;
  const policyId = params.policyId as string;

  const [policyData, setPolicyData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchPolicy = async () => {
      try {
        setIsLoading(true);
        const res = await fetch(`/api/sla/${teamId}/policy/${policyId}`);

        if (!res.ok) {
          throw new Error(`Failed to fetch SLA policy: ${res.status}`);
        }

        const data = await res.json();
        setPolicyData(data);
      } catch (err) {
        setError(
          err instanceof Error
            ? err
            : new Error("An unexpected error occurred"),
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchPolicy();
  }, [teamId, policyId]);

  if (isLoading) {
    return (
      <ThenaLoader className="h-screen w-full flex items-center justify-center" />
    );
  }

  if (error) {
    return (
      <div className="h-full overflow-auto">
        <div className="container mx-auto p-6 max-w-3xl flex flex-col items-center justify-center">
          <h1 className="text-2xl font-bold text-red-500">
            Error Loading SLA Policy
          </h1>
          <p className="text-gray-600 mt-2">{error.message}</p>
          <p className="mt-4">Policy ID: {policyId}</p>
        </div>
      </div>
    );
  }

  if (!policyData) {
    return null;
  }

  return (
    <div className="h-full overflow-auto">
      <div className="container mx-auto p-6 max-w-3xl">
        <ClientForm
          initialData={policyData}
          policyId={policyId}
          teamId={teamId}
        />
      </div>
    </div>
  );
}
