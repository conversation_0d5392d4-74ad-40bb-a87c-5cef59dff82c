// "use client";
"use client";

import ThenaLoader from "@/components/thena-loader";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  CheckCircle,
  ChevronDown,
  ClipboardList,
  Clock,
  Copy,
  Loader2,
  Pen,
  Trash2,
} from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

export default function EmailSourcePage() {
  const params = useParams<{ teamId: string }>();
  const teamId = params.teamId as string;

  // State for default email
  const [defaultEmail, setDefaultEmail] = useState("");

  const [domains, setDomains] = useState<string[]>([]);
  const [selectedDomain, setSelectedDomain] = useState("");
  const [emailPrefix, setEmailPrefix] = useState("");
  const [customEmails, setCustomEmails] = useState<
    Array<{
      id: string;
      uid: string;
      configId: string;
      email: string;
      domain: string;
      verified: boolean;
      pending: boolean;
      isEmailForwardingVerified: boolean;
      verificationCode: string;
      senderPreference: string;
      customCommonName: string;
    }>
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [_isEditing, _setIsEditing] = useState(false);
  const [_commonName, _setCommonName] = useState("");
  const [_isSaving, _setIsSaving] = useState(false);
  const [expandedAccordions, setExpandedAccordions] = useState<string[]>([]);
  const [newEmailAccordionOpen, setNewEmailAccordionOpen] = useState(false);
  const [emailSenderPreference, setEmailSenderPreference] =
    useState("original");
  const [customCommonName, setCustomCommonName] = useState("");
  const [editingSenderPreference, setEditingSenderPreference] = useState<
    string | null
  >(null);
  const [updatedSenderPreference, setUpdatedSenderPreference] =
    useState("original");
  const [updatedCustomCommonName, setUpdatedCustomCommonName] = useState("");
  const [isUpdatingSenderPreference, setIsUpdatingSenderPreference] =
    useState(false);
  const [emailToConfirmDelete, setEmailToConfirmDelete] = useState<
    string | null
  >(null);
  const [deletingEmails, setDeletingEmails] = useState<Set<string>>(new Set());
  const [verificationCodeSending, setVerificationCodeSending] = useState<
    Set<string>
  >(new Set());
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-focus input when editing starts
  useEffect(() => {
    if (_isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [_isEditing]);

  // Toggle accordion expansion
  const toggleAccordion = (accordionId: string) => {
    setExpandedAccordions((prev) =>
      prev.includes(accordionId)
        ? prev.filter((id) => id !== accordionId)
        : [...prev, accordionId],
    );
  };

  // Toggle new email accordion
  const toggleNewEmailAccordion = () => {
    setNewEmailAccordionOpen((prev) => !prev);
    if (!newEmailAccordionOpen) {
      // Reset form when opening
      setEmailPrefix("");
      setEmailSenderPreference("original");
      setCustomCommonName("");
    }
  };

  // Fetch verified domains from the API
  const fetchVerifiedDomains = async () => {
    try {
      const response = await fetch("/api/organization/domains/fetch");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch domains");
      }

      const _data = await response.json();

      // Filter domains that have isDnsVerified as true and format them with @ prefix
      const verifiedDomains = _data
        .filter((domain: { isDnsVerified: boolean }) => domain.isDnsVerified)
        .map((domain: { domain: string }) => `@${domain.domain}`);

      setDomains(verifiedDomains);

      // Set the first domain as selected if available
      if (verifiedDomains.length > 0) {
        setSelectedDomain(verifiedDomains[0]);
      }
    } catch (error) {
      console.error("Error fetching domains:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to fetch domains",
      );
    }
  };

  // Fetch domains and existing email config when component mounts
  useEffect(() => {
    fetchVerifiedDomains();
    fetchDefaultEmail();
    fetchCustomEmailConfig();
  }, []);

  // Function to fetch default email
  const fetchDefaultEmail = async () => {
    try {
      const response = await fetch(
        `/api/organization/teams/email-config/default-email?teamId=${teamId}`,
        {
          method: "POST",
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch default email");
      }

      const data = await response.json();
      setDefaultEmail(data.email);
    } catch (error) {
      console.error("Error fetching default email:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to fetch default email",
      );
    }
  };

  // Function to fetch custom email configurations
  const fetchCustomEmailConfig = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/organization/teams/email-config/custom-email-config?teamId=${teamId}`,
      );

      if (!response.ok) {
        throw new Error("Failed to fetch custom email configurations");
      }

      const _data = await response.json();

      // Map the API sender preference to UI values
      const senderPreferenceMap: Record<string, string> = {
        original_name: "original",
        use_common_name: "common",
        both: "combined",
      };

      // Define the type for the API response item
      interface EmailConfigApiItem {
        id: string;
        uid: string;
        customEmail: string;
        domain: string;
        isEmailForwardingVerified: boolean;
        forwardingVerificationCode?: string;
        sendersPreferredChoice: string;
        customCommonName?: string;
      }

      // Transform the data to match our state structure
      const formattedEmails = _data.map((item: EmailConfigApiItem) => ({
        id: item.id,
        uid: item.uid,
        configId: item.uid, // Add the configId for delete operations
        email: item.customEmail,
        domain: item.domain,
        verified: item.isEmailForwardingVerified,
        pending: !item.isEmailForwardingVerified,
        isEmailForwardingVerified: item.isEmailForwardingVerified,
        verificationCode: item.forwardingVerificationCode || "",
        senderPreference:
          senderPreferenceMap[item.sendersPreferredChoice] || "original",
        customCommonName: item.customCommonName || _commonName,
      }));

      setCustomEmails(formattedEmails);

      // All accordions should be closed by default
      // Keep the expandedAccordions state empty
    } catch (error) {
      console.error("Error fetching custom email config:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to fetch custom email configurations",
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyEmail = () => {
    navigator.clipboard.writeText(defaultEmail);
    toast.success("Email copied to clipboard");
  };

  const handleAddCustomEmail = async () => {
    if (!emailPrefix) {
      toast.error("Please enter an email prefix");
      return;
    }

    const newEmail = `${emailPrefix}${selectedDomain}`;

    try {
      // Map the UI sender preference to the API enum values
      const senderPreferenceMap = {
        original: "original_name",
        common: "use_common_name",
        both: "both",
      };

      const response = await fetch(
        "/api/organization/teams/email-config/add-custom-email",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            teamId,
            customEmail: newEmail,
            sendersPreferredChoice:
              senderPreferenceMap[
                emailSenderPreference as keyof typeof senderPreferenceMap
              ],
            customCommonName:
              emailSenderPreference !== "original"
                ? customCommonName
                : undefined,
            ...(emailSenderPreference !== "original" && {
              sendersPreferredChoiceValue: customCommonName,
            }),
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add custom email");
      }

      const _data = await response.json();

      // Extract the real configId from the API response
      const newEmailConfig = _data.data || _data;
      const realConfigId =
        newEmailConfig.uid || newEmailConfig.configId || `temp-${Date.now()}`;

      // Add the email to the list but mark as unverified and pending
      setCustomEmails([
        ...customEmails,
        {
          id: realConfigId, // Use the real ID from the API response
          uid: realConfigId, // Use the real UID from the API response
          configId: realConfigId, // Use the real configId from the API response
          email: newEmail,
          domain: selectedDomain.replace("@", ""),
          verified: false,
          pending: true,
          isEmailForwardingVerified: false,
          verificationCode: "",
          senderPreference: emailSenderPreference,
          customCommonName: customCommonName,
        },
      ]);

      // Expand the accordion for the new email
      setExpandedAccordions((prev) => [...prev, newEmail]);

      // Close the new email accordion
      setNewEmailAccordionOpen(false);

      toast.success(
        "Custom email added. Please complete the forwarding setup.",
      );
    } catch (error) {
      console.error("Error adding custom email:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to add custom email",
      );
    }
  };

  const handleRemoveCustomEmail = async (email: string) => {
    try {
      setDeletingEmails((prev) => new Set([...prev, email]));

      // Find the email configuration to get its ID
      const emailConfig = customEmails.find((item) => item.email === email);
      if (!emailConfig) {
        throw new Error("Email configuration not found");
      }

      // Make API call to delete the custom email configuration
      const response = await fetch(
        `/api/organization/teams/email-config/delete-custom-email?teamId=${params.teamId}&configId=${emailConfig.configId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        throw new Error("Failed to delete custom email");
      }

      // Update the state to remove the deleted email
      setCustomEmails((prev) => prev.filter((item) => item.email !== email));

      // Remove from expanded accordions if present
      setExpandedAccordions((prev) => prev.filter((id) => id !== email));

      toast.success(`Email ${email} deleted successfully`);
    } catch (error) {
      console.error("Error deleting custom email:", error);
      toast.error("Failed to delete custom email. Please try again.");
    } finally {
      setDeletingEmails((prev) => {
        const newSet = new Set(prev);
        newSet.delete(email);
        return newSet;
      });
      setEmailToConfirmDelete(null);
    }
  };

  const handleVerifyEmail = async (email: string) => {
    try {
      // Add email to the set of emails with pending verification code requests
      setVerificationCodeSending((prev) => new Set([...prev, email]));

      const response = await fetch(
        "/api/organization/teams/email-config/verify-custom-email-forwarding",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            teamId,
            customEmail: email,
          }),
        },
      );

      // Set a timeout to remove the email from the set after 30 seconds
      setTimeout(() => {
        setVerificationCodeSending((prev) => {
          const newSet = new Set([...prev]);
          newSet.delete(email);
          return newSet;
        });

        if (response.ok) {
          // Show success message for sending verification email
          toast.success(
            "Verification message sent successfully. Please click on Verify button to verify the email.",
          );
        }
      }, 30000); // 30 seconds

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send verification email");
      }
    } catch (error) {
      console.error("Error sending verification email:", error);

      // Remove the email from the set immediately on error
      setVerificationCodeSending((prev) => {
        const newSet = new Set([...prev]);
        newSet.delete(email);
        return newSet;
      });

      toast.error(
        error instanceof Error
          ? error.message
          : "Unable to send verification email. Please click on Send verification code button again",
      );
    }
  };

  const handleCheckVerificationStatus = async (email: string) => {
    try {
      // Get the current verification status
      const response = await fetch(
        `/api/organization/teams/email-config/custom-email-config?teamId=${teamId}`,
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to check verification status",
        );
      }

      const data = await response.json();

      // Find the specific email in the response
      const emailConfig = data.find(
        (item: { customEmail: string; isEmailForwardingVerified: boolean }) =>
          item.customEmail === email,
      );

      if (!emailConfig) {
        throw new Error("Email configuration not found");
      }

      // Update the UI based on the verification status
      setCustomEmails(
        customEmails.map((item) =>
          item.email === email
            ? {
                ...item,
                verified: emailConfig.isEmailForwardingVerified,
                pending: !emailConfig.isEmailForwardingVerified,
                isEmailForwardingVerified:
                  emailConfig.isEmailForwardingVerified,
              }
            : item,
        ),
      );

      // Show appropriate toast message
      if (emailConfig.isEmailForwardingVerified) {
        toast.success("Email forwarding is verified");

        // Remove from expanded accordions when verified to show the verified status
        setExpandedAccordions((prev) => prev.filter((id) => id !== email));
      } else {
        toast.error(
          "Email forwarding is not verified. Please check your email forwarding settings and try again.",
        );
      }
    } catch (error) {
      console.error("Error checking verification status:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to check verification status",
      );
    }
  };

  const handleEditSenderPreference = (email: string) => {
    const emailConfig = customEmails.find((item) => item.email === email);
    if (!emailConfig) return;

    setEditingSenderPreference(email);
    setUpdatedSenderPreference(emailConfig.senderPreference);
    setUpdatedCustomCommonName(emailConfig.customCommonName);
  };

  const handleCancelEditSenderPreference = () => {
    setEditingSenderPreference(null);
  };

  const handleUpdateSenderPreference = async (email: string) => {
    try {
      setIsUpdatingSenderPreference(true);
      const emailConfig = customEmails.find((item) => item.email === email);
      if (!emailConfig) throw new Error("Email configuration not found");
      if (!emailConfig.uid) throw new Error("Email configuration ID not found");

      // Map the UI sender preference to the API enum values
      const senderPreferenceMap = {
        original: "original_name",
        common: "use_common_name",
        both: "both",
      };

      // Make API call to update sender preference
      const response = await fetch(
        `/api/organization/teams/email-config/update-sender-preference/${emailConfig.uid}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            teamId,
            sendersPreferredChoice:
              senderPreferenceMap[
                updatedSenderPreference as keyof typeof senderPreferenceMap
              ],
            ...(updatedSenderPreference !== "original" && {
              sendersPreferredChoiceValue: updatedCustomCommonName,
            }),
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to update sender preference",
        );
      }

      await response.json();

      // Update the UI after successful update
      setCustomEmails(
        customEmails.map((item) =>
          item.email === email
            ? {
                ...item,
                senderPreference: updatedSenderPreference,
                customCommonName: updatedCustomCommonName,
              }
            : item,
        ),
      );

      setEditingSenderPreference(null);
      toast.success("Sender preference updated successfully");
    } catch (error) {
      console.error("Error updating sender preference:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update sender preference",
      );
    } finally {
      setIsUpdatingSenderPreference(false);
    }
  };

  if (isLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center min-h-[640px]">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="max-w-[640px] mx-auto space-y-4 pt-[56px]">
      <div>
        <h1 className="text-2xl font-medium tracking-tight">Email</h1>
        <p className="text-sm text-muted-foreground">
          Configure your team&apos;s email settings and custom email addresses.
        </p>
      </div>

      <div className="space-y-6">
        {/* Team Email Section */}
        <div>
          <h3 className="text-lg font-medium mb-[3px]">Team email</h3>
          <p className="text-sm text-[var(--color-text-muted)]">
            Your team&apos;s default email address for receiving messages.
          </p>

          <div className="mt-4">
            <div className="flex items-center justify-between border border-[var(--color-border)] rounded-[4px] bg-[var(--color-bg-subtle)] max-w-md overflow-hidden group">
              <div className="flex-1 px-3 py-2">
                <span className="text-sm text-[var(--color-text)]">
                  {defaultEmail}
                </span>
              </div>
              <div>
                <button
                  onClick={handleCopyEmail}
                  className="h-full px-3 py-2 flex items-center justify-center text-[var(--color-icon-muted)] hover:text-[var(--color-text-body)] hover:bg-[var(--color-bg-hover)] transition-all"
                  aria-label="Copy email address"
                >
                  <Copy className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="h-px bg-[var(--color-border)]" aria-hidden="true"></div>

        {/* Custom Email Addresses Section */}
        <div>
          <h3 className="text-lg font-medium mb-[3px]">Custom email address</h3>
          <p className="text-sm text-[var(--color-text-muted)]">
            Add custom email addresses to use with your verified domains. This
            will disable the team email for ticket creation.{" "}
          </p>

          {/* Existing Custom Emails */}
          <div className="mt-4 space-y-4">
            {/* Add New Custom Email Accordion */}
            <div className="border rounded-[4px] overflow-hidden bg-[var(--color-bg-card)]">
              <div
                className="flex items-center justify-between px-4 py-3 bg-[var(--color-bg-subtle)] cursor-pointer"
                onClick={toggleNewEmailAccordion}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    toggleNewEmailAccordion();
                  }
                }}
                role="button"
                tabIndex={0}
              >
                <div className="text-[var(--color-text-body)]">
                  {customEmails.length > 0
                    ? "Add another custom email"
                    : "Add custom email address"}
                </div>
                <div className="flex items-center">
                  <div className="flex items-center mr-2 cursor-pointer text-[var(--color-icon-muted)] hover:text-[var(--color-text-body)]">
                    {newEmailAccordionOpen ? (
                      <ChevronDown className="h-4 w-4 rotate-180 transition-transform duration-200" />
                    ) : (
                      <ChevronDown className="h-4 w-4 transition-transform duration-200" />
                    )}
                  </div>
                </div>
              </div>

              {/* New Email Form */}
              {newEmailAccordionOpen && (
                <div className="p-4 border-t border-[var(--color-border)] animate-slideIn">
                  <div className="space-y-4">
                    {/* Email Address Configuration */}
                    <div>
                      <h4 className="text-sm text-[var(--color-text)] mb-3">
                        Email address
                      </h4>
                      <div className="grid grid-cols-1 sm:grid-cols-12 gap-2">
                        <div className="sm:col-span-7">
                          <Input
                            placeholder="Enter email prefix"
                            className="border-[var(--color-border)] focus:border-[var(--color-border-strong)] focus:ring-0 h-10 w-full rounded-[4px]"
                            value={emailPrefix}
                            onChange={(e) => setEmailPrefix(e.target.value)}
                          />
                        </div>
                        <div className="sm:col-span-5">
                          <div className="relative">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="outline"
                                  className="w-full justify-between border border-[var(--color-border)] bg-[var(--color-bg-card)] text-[var(--color-text-body)] hover:bg-[var(--color-bg-subtle)] h-10 rounded-[4px]"
                                >
                                  <span>
                                    {selectedDomain || "Select domain"}
                                  </span>
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="text-[var(--color-icon-muted)]"
                                    aria-hidden="true"
                                  >
                                    <title>Dropdown arrow</title>
                                    <polyline points="6 9 12 15 18 9" />
                                  </svg>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent
                                className="w-[247.84px]"
                                sideOffset={5}
                                align="start"
                              >
                                {domains.length > 0 ? (
                                  domains.map((domain) => (
                                    <DropdownMenuItem
                                      key={domain}
                                      className={`py-2.5 cursor-pointer ${
                                        domain === selectedDomain
                                          ? "bg-[var(--color-bg-subtle)]"
                                          : ""
                                      }`}
                                      onClick={() => setSelectedDomain(domain)}
                                    >
                                      {domain}
                                    </DropdownMenuItem>
                                  ))
                                ) : (
                                  <>
                                    <DropdownMenuItem
                                      disabled
                                      className="cursor-not-allowed"
                                    >
                                      No verified domains available
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      className="py-2.5"
                                      onClick={() =>
                                        window.open(
                                          `/organization/settings/sources/email`,
                                          "_blank",
                                        )
                                      }
                                    >
                                      Set up domain
                                    </DropdownMenuItem>
                                  </>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                      <p className="text-xs text-[var(--color-text-muted)] mt-0.5 pt-2">
                        This will be the address where customers can send emails
                        to your team.
                      </p>
                    </div>

                    {/* Sender preference */}
                    <div>
                      <h4 className="text-sm text-[var(--color-text)] mb-3">
                        Sender preference
                      </h4>
                      <RadioGroup
                        value={emailSenderPreference}
                        onValueChange={setEmailSenderPreference}
                        className="space-y-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value="original"
                            id="email-original"
                            className="border-[var(--color-border-strong)] text-[var(--color-text)]"
                          />
                          <label
                            htmlFor="email-original"
                            className="text-sm text-[var(--color-text)] cursor-pointer"
                          >
                            Sender&apos;s name
                          </label>
                          <span className="text-xs px-1.5 py-0.5 border border-green-500 text-green-600 bg-green-50 rounded-[4px]">
                            Default
                          </span>
                        </div>

                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value="common"
                            id="email-common"
                            className="border-[var(--color-border-strong)] text-[var(--color-text)]"
                          />
                          <label
                            htmlFor="email-common"
                            className="text-sm text-[var(--color-text)] cursor-pointer"
                          >
                            Common name
                          </label>
                        </div>
                        {emailSenderPreference === "common" && (
                          <div className="ml-6">
                            <Input
                              placeholder="Enter custom common name"
                              className="w-full max-w-xs rounded-[4px]"
                              value={customCommonName}
                              onChange={(e) =>
                                setCustomCommonName(e.target.value)
                              }
                            />
                          </div>
                        )}

                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value="both"
                            id="email-combined"
                            className="border-[var(--color-border-strong)] text-[var(--color-text)]"
                          />
                          <label
                            htmlFor="email-combined"
                            className="text-sm text-[var(--color-text)] cursor-pointer"
                          >
                            Sender&apos;s name + Common name
                          </label>
                        </div>
                        {emailSenderPreference === "both" && (
                          <div className="ml-6">
                            <Input
                              placeholder="Enter custom common name"
                              className="w-full max-w-xs rounded-[4px]"
                              value={customCommonName}
                              onChange={(e) =>
                                setCustomCommonName(e.target.value)
                              }
                            />
                          </div>
                        )}
                      </RadioGroup>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end gap-2 mt-3">
                      <Button
                        variant="outline"
                        className="border-[var(--color-border)] text-[var(--color-text-body)] rounded-[4px]"
                        onClick={toggleNewEmailAccordion}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="default"
                        onClick={handleAddCustomEmail}
                        disabled={
                          !emailPrefix.trim() ||
                          !selectedDomain ||
                          (emailSenderPreference !== "original" &&
                            !customCommonName.trim())
                        }
                      >
                        Add
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {customEmails.map((item) => (
              <div
                key={item.email}
                className="border rounded-[4px] overflow-hidden bg-[var(--color-bg-card)]"
              >
                {/* Accordion Header */}
                <div
                  className="flex items-center justify-between px-4 py-3 bg-[var(--color-bg-subtle)] cursor-pointer"
                  onClick={() => toggleAccordion(item.email)}
                >
                  <div className="flex items-center gap-3">
                    <div className="text-[var(--color-text-body)]">
                      {item.email}
                    </div>
                    <div className="flex items-center gap-1">
                      {item.isEmailForwardingVerified ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-xs font-medium text-green-500">
                            Verified
                          </span>
                        </>
                      ) : (
                        <>
                          <Clock className="h-4 w-4 text-amber-500" />
                          <span className="text-xs font-medium text-amber-500">
                            Pending
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="flex items-center mr-2 cursor-pointer text-[var(--color-icon-muted)] hover:text-[var(--color-text-body)]">
                      {expandedAccordions.includes(item.email) ? (
                        <ChevronDown className="h-4 w-4 rotate-180 transition-transform duration-200" />
                      ) : (
                        <ChevronDown className="h-4 w-4 transition-transform duration-200" />
                      )}
                    </div>
                  </div>
                </div>

                {/* Accordion Content */}
                {expandedAccordions.includes(item.email) && (
                  <div className="p-4 border-t border-[var(--color-border)] animate-slideIn">
                    {item.verified ? (
                      <div className="space-y-3">
                        <div className="text-sm space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="text-[var(--color-text-muted)]">
                              Status:
                            </span>
                            <span className="font-medium text-[var(--color-text-success)]">
                              Verified
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-[var(--color-text-muted)]">
                              Sender preference:
                            </span>
                            {editingSenderPreference === item.email ? (
                              <div className="flex flex-col space-y-3 w-full">
                                <RadioGroup
                                  value={updatedSenderPreference}
                                  onValueChange={setUpdatedSenderPreference}
                                  className="space-y-2"
                                >
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                      value="original"
                                      id={`original-${item.email}`}
                                    />
                                    <label
                                      htmlFor={`original-${item.email}`}
                                      className="text-sm text-[var(--color-text)] cursor-pointer"
                                    >
                                      Sender&apos;s name
                                    </label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                      value="common"
                                      id={`common-${item.email}`}
                                    />
                                    <label
                                      htmlFor={`common-${item.email}`}
                                      className="text-sm text-[var(--color-text)] cursor-pointer"
                                    >
                                      Common name
                                    </label>
                                  </div>
                                  {updatedSenderPreference === "common" && (
                                    <div className="ml-6">
                                      <Input
                                        type="text"
                                        placeholder="Enter common name"
                                        value={updatedCustomCommonName}
                                        onChange={(e) =>
                                          setUpdatedCustomCommonName(
                                            e.target.value,
                                          )
                                        }
                                        className="w-full max-w-xs rounded-[4px]"
                                      />
                                    </div>
                                  )}
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                      value="both"
                                      id={`both-${item.email}`}
                                    />
                                    <label
                                      htmlFor={`both-${item.email}`}
                                      className="text-sm text-[var(--color-text)] cursor-pointer"
                                    >
                                      Sender&apos;s name + Common name
                                    </label>
                                  </div>
                                  {updatedSenderPreference === "both" && (
                                    <div className="ml-6">
                                      <Input
                                        type="text"
                                        placeholder="Enter common name"
                                        value={updatedCustomCommonName}
                                        onChange={(e) =>
                                          setUpdatedCustomCommonName(
                                            e.target.value,
                                          )
                                        }
                                        className="w-full max-w-xs rounded-[4px]"
                                      />
                                    </div>
                                  )}
                                </RadioGroup>
                                <div className="flex items-center gap-2 mt-2">
                                  <Button
                                    onClick={() =>
                                      handleUpdateSenderPreference(item.email)
                                    }
                                    disabled={isUpdatingSenderPreference}
                                    className="h-8 px-3 py-1 rounded-[4px]"
                                  >
                                    {isUpdatingSenderPreference
                                      ? "Updating..."
                                      : "Update"}
                                  </Button>
                                  <Button
                                    variant="outline"
                                    onClick={handleCancelEditSenderPreference}
                                    className="h-8 px-3 py-1 rounded-[4px]"
                                  >
                                    Cancel
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-center gap-2">
                                <span className="font-medium">
                                  {item.senderPreference === "original"
                                    ? "Use original sender's name"
                                    : item.senderPreference === "common"
                                    ? `Use common name (${item.customCommonName})`
                                    : `Sender's name + Common name (${item.customCommonName})`}
                                </span>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditSenderPreference(item.email);
                                  }}
                                  className="text-[var(--color-icon-muted)] hover:text-[var(--color-text-body)]"
                                  aria-label="Edit sender preference"
                                >
                                  <Pen className="h-3.5 w-3.5" />
                                </button>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex justify-end mt-4">
                          <Button
                            variant="outline"
                            className="text-red-400 hover:text-red-500 hover:bg-red-50 rounded-[4px] text-xs font-medium"
                            onClick={() => setEmailToConfirmDelete(item.email)}
                            disabled={deletingEmails.has(item.email)}
                          >
                            {deletingEmails.has(item.email) ? (
                              <Loader2 className="h-3 w-3 animate-spin mr-1" />
                            ) : (
                              <Trash2 className="h-3 w-3 mr-1" />
                            )}
                            Delete
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="bg-[var(--color-bg-error-muted)] p-4 rounded-[4px] shadow-sm">
                          <div className="flex items-center gap-2 mb-3">
                            <ClipboardList className="h-5 w-5 text-amber-600" />
                            <h4 className="text-sm text-amber-700">
                              Set up email forwarding
                            </h4>
                          </div>
                          <div className="pl-7 space-y-3 text-sm">
                            <div className="relative before:absolute before:content-['1'] before:-left-7 before:text-amber-600 before:font-medium">
                              Log in to your email provider&apos;s account
                              settings
                            </div>
                            <div className="relative before:absolute before:content-['2'] before:-left-7 before:text-amber-600 before:font-medium">
                              Find the Forwarding or Auto-forward settings
                            </div>

                            <div className="relative before:absolute before:content-['4'] before:-left-7 before:text-amber-600 before:font-medium">
                              Configure your email to forward to{" "}
                              <span className="font-medium bg-amber-100 px-1.5 py-0.5 rounded-[4px] text-amber-800">
                                {defaultEmail}
                              </span>
                            </div>
                            <div className="relative before:absolute before:content-['5'] before:-left-7 before:text-amber-600 before:font-medium">
                              Save your settings in your email provider, send
                              verification code and click on Verify
                            </div>
                          </div>
                        </div>

                        <div className="flex justify-between items-center mt-4">
                          <div className="flex space-x-2">
                            <Button
                              variant="default"
                              className="w-fit text-xs rounded-[4px]"
                              onClick={() => handleVerifyEmail(item.email)}
                              disabled={verificationCodeSending.has(item.email)}
                            >
                              {verificationCodeSending.has(item.email) ? (
                                <>
                                  <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                  Sending...
                                </>
                              ) : (
                                "Send verification code"
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              className="w-fit text-xs rounded-[4px]"
                              onClick={() =>
                                handleCheckVerificationStatus(item.email)
                              }
                            >
                              Verify
                            </Button>
                          </div>

                          <Button
                            variant="outline"
                            className="text-red-400 hover:text-red-500 hover:bg-red-50 rounded-[4px] text-xs font-medium"
                            onClick={() => setEmailToConfirmDelete(item.email)}
                            disabled={deletingEmails.has(item.email)}
                          >
                            {deletingEmails.has(item.email) ? (
                              <Loader2 className="h-3 w-3 animate-spin mr-1" />
                            ) : (
                              <Trash2 className="h-3 w-3 mr-1" />
                            )}
                            Delete
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Delete Email Confirmation Dialog */}
      <Dialog
        open={!!emailToConfirmDelete}
        onOpenChange={(open) => !open && setEmailToConfirmDelete(null)}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm email deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the email{" "}
              <span className="font-medium">{emailToConfirmDelete}</span>? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-2 sm:justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => setEmailToConfirmDelete(null)}
              className="rounded-[4px] text-xs font-medium"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              className="rounded-[4px] text-xs font-medium bg-red-500 hover:bg-red-600"
              onClick={() => {
                if (emailToConfirmDelete) {
                  handleRemoveCustomEmail(emailToConfirmDelete);
                }
              }}
              disabled={
                !!emailToConfirmDelete &&
                deletingEmails.has(emailToConfirmDelete)
              }
            >
              {emailToConfirmDelete &&
              deletingEmails.has(emailToConfirmDelete) ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : (
                <Trash2 className="h-3 w-3 mr-1" />
              )}
              Delete email
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
