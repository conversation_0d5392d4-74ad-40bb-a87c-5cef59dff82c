"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON>, Loader2, Plus, X } from "lucide-react";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import { WebChatAgentBanner } from "../../../../../../../components/agents/web-chat-agent-banner";
import CommonSelectWrapper from "../../../../../../../components/common-select-wrapper";
import { Button } from "../../../../../../../components/ui/button";
import { Checkbox } from "../../../../../../../components/ui/checkbox";
import { Input } from "../../../../../../../components/ui/input";
import { Label } from "../../../../../../../components/ui/label";
import { AGENT_NAME } from "../../../../../../../constants/web-chat";
import { getAgents } from "../../../../../../../lib/api/agents";
import { Agent } from "../../../../../../../types/agent";
import { ColorPicker } from "../../../../../helpcenter/[helpcenterId]/settings/_components/color-picker";
import WebChatEmptyConfig from "./empty-config";
import WebSettingsEmptyState from "./empty-state";
// Get Base URL and WS Endpoint from environment variables
const baseUrl = process.env.NEXT_PUBLIC_AGENT_STUDIO_URL;
const wsEndpoint = process.env.NEXT_PUBLIC_AGENT_STUDIO_WS_URL;
const widgetUrl = process.env.NEXT_PUBLIC_WIDGET_CDN_URL;

interface WidgetSettings {
  targetElementId?: string;
  useCustomLauncher?: boolean;
  initialPosition?: {
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  };
  themeColorStart?: string;
  themeColorEnd?: string;
  gradientDirection?: string;
  brandLogoUrl?: string;
  userContextExample?: {
    email?: string;
    name?: string;
  };
  darkMode?: boolean;
  autoclose?: boolean;
}

interface WidgetConfig {
  baseUrl: string;
  apiKey: string;
  agentId: string;
  wsEndpoint: string;
  useCustomLauncher: boolean;
  user?: {
    email?: string;
    name?: string;
    hash?: string;
  };
  initialPosition?: {
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  };
  targetElementId?: string;
  themeColorStart?: string;
  themeColorEnd?: string;
  gradientDirection?: string;
  brandLogoUrl?: string;
  darkMode?: boolean;
  autoclose?: boolean;
  [key: string]: unknown;
}

export default function WebChatSettings() {
  const searchParams = useSearchParams();
  const params = useParams();
  const teamId = params.teamId as string;
  const agentId = searchParams.get("agentId");
  const [isLoadingDeployments, setIsLoadingDeployments] = useState(false);
  const [allowedDomains, setAllowedDomains] = useState<string[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);
  const [deploymentData, setDeploymentData] = useState(null);
  const [agentConfigurationPrompt, setAgentConfigurationPrompt] =
    useState<string>("");
  const [isSavingChatWidgetInstructions, setIsSavingChatWidgetInstructions] =
    useState(false);
  const queryClient = useQueryClient();

  // Add initial state tracking
  const [initialState, setInitialState] = useState<{
    targetElementId: string;
    useCustomLauncher: boolean;
    initialPositionTop?: string;
    initialPositionLeft?: string;
    initialPositionBottom?: string;
    initialPositionRight?: string;
    selectedPositionType: string;
    themeColorStart: string;
    themeColorEnd: string;
    brandLogoUrl: string;
    gradientDirection: string;
    deploymentMode: "floating" | "embedded";
    userEmail: string;
    userName: string;
    allowedDomains: string[];
    agentConfigurationPrompt: string;
  } | null>(null);

  // Widget specific settings states
  const [targetElementId, setTargetElementId] = useState<string>("");
  const [useCustomLauncher, setUseCustomLauncher] = useState<boolean>(false);
  const [initialPositionTop, setInitialPositionTop] = useState<
    string | undefined
  >(undefined);
  const [initialPositionLeft, setInitialPositionLeft] = useState<
    string | undefined
  >(undefined);
  const [initialPositionBottom, setInitialPositionBottom] = useState<
    string | undefined
  >(undefined);
  const [initialPositionRight, setInitialPositionRight] = useState<
    string | undefined
  >(undefined);
  const [selectedPositionType, setSelectedPositionType] =
    useState<string>("bottom-right");
  const [themeColorStart, setThemeColorStart] = useState<string>("");
  const [themeColorEnd, setThemeColorEnd] = useState<string>("");
  const [gradientDirection, setGradientDirection] = useState<string>("");
  const [brandLogoUrl, setBrandLogoUrl] = useState<string>("");
  const [deploymentMode, setDeploymentMode] = useState<"floating" | "embedded">(
    "floating",
  );
  const [userEmail, setUserEmail] = useState<string>("<EMAIL>");
  const [userName, setUserName] = useState<string>("Test User (HMAC)");
  const [darkMode, setDarkMode] = useState<boolean>(false);
  const [autoclose, setAutoclose] = useState<boolean>(false);
  const { data: agents } = useQuery({
    queryKey: ["agents"],
    queryFn: () => getAgents(),
    refetchInterval: 10000, // Add polling every 10 seconds
    staleTime: 5000, // Consider data stale after 5 seconds
  });

  const agentDetails = useMemo(() => {
    return agents?.find(
      (agent) => agent.name === AGENT_NAME && agent.team_id.includes(teamId),
    );
  }, [agents]);

  const { data: agent, isLoading } = useQuery<Agent>({
    queryKey: ["agent", agentId],
    queryFn: async () => {
      const response = agentId
        ? await fetch(`/api/agents/${agentId}`)
        : await fetch(`/api/agents/${agentDetails?.id}`);
      if (!response.ok) throw new Error("Failed to fetch agent");
      return response.json();
    },
    enabled: !!agentId || !!agentDetails?.id,
  });

  // Add a ref to track if we've set initial state
  const hasSetInitialState = useRef(false);

  const [originalPositionConfig, setOriginalPositionConfig] = useState<{
    type: string;
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  } | null>(null);

  const isBackToOriginalPosition = () => {
    if (!originalPositionConfig) return false;

    // If position type is different, it's definitely changed
    if (selectedPositionType !== originalPositionConfig.type) {
      return false;
    }

    // If we're in custom mode, check coordinates
    if (selectedPositionType === "custom") {
      return (
        initialPositionTop === originalPositionConfig.top &&
        initialPositionLeft === originalPositionConfig.left &&
        initialPositionBottom === originalPositionConfig.bottom &&
        initialPositionRight === originalPositionConfig.right
      );
    }

    // For preset positions, if the type matches, consider it as back to original
    // unless the original was also a preset with different coordinates
    if (originalPositionConfig.type !== "custom") {
      // Both are preset positions and types match
      return true;
    }

    return false;
  };

  // Updated customization changes function
  const hasCustomizationChanges = () => {
    if (!initialState || !originalPositionConfig) return false;

    // Simple field comparisons
    const simpleFieldChanges =
      themeColorStart !== initialState.themeColorStart ||
      themeColorEnd !== initialState.themeColorEnd ||
      gradientDirection !== initialState.gradientDirection ||
      brandLogoUrl !== initialState.brandLogoUrl ||
      deploymentMode !== initialState.deploymentMode ||
      targetElementId !== initialState.targetElementId ||
      useCustomLauncher !== initialState.useCustomLauncher;

    // Position change check
    const positionChanged = !isBackToOriginalPosition();

    return simpleFieldChanges || positionChanged;
  };

  // Updated deployment changes function (this one should be fine as is)
  const hasDeploymentChanges = () => {
    if (!initialState) return false;

    return (
      JSON.stringify(allowedDomains) !==
        JSON.stringify(initialState.allowedDomains) ||
      userEmail !== initialState.userEmail ||
      userName !== initialState.userName
    );
  };

  // Updated agent config changes function
  const hasAgentConfigChanges = () => {
    if (!initialState) return false;

    // Get the current agent's instructions or empty string if not available
    const originalInstructions =
      agent?.configuration?.chat_widget_instructions || "";

    // Compare with current value
    return agentConfigurationPrompt !== originalInstructions;
  };

  useEffect(() => {
    async function fetchDeployments() {
      setIsLoadingDeployments(true);
      try {
        const response = agentId
          ? await fetch(`/api/agents/${agentId}/deployments`)
          : await fetch(`/api/agents/${agentDetails?.id}/deployments`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to fetch deployments", errorData);
          setIsLoadingDeployments(false);
          return;
        }

        const deployments = await response.json();
        setDeploymentData(deployments[0]);
        // If we have deployments for this agent, use the first one
        if (deployments && deployments.length > 0) {
          const latestDeployment = deployments[0];
          // If the deployment has allowed origins, update state
          if (
            latestDeployment.allowed_origins &&
            latestDeployment.allowed_origins.length > 0
          ) {
            setAllowedDomains(latestDeployment.allowed_origins);
          }

          // Set selected team based on team_id if available
          if (latestDeployment.team_id) {
            setSelectedTeam(latestDeployment.team_id);
          }

          // Populate widget settings states from latestDeployment.widget_settings
          if (latestDeployment.widget_settings) {
            const settings = latestDeployment.widget_settings;
            setTargetElementId(settings.targetElementId || "my-thena-widget");
            setUseCustomLauncher(
              settings.useCustomLauncher === undefined
                ? false
                : settings.useCustomLauncher,
            );
            if (settings.initialPosition) {
              setInitialPositionTop(settings.initialPosition.top);
              setInitialPositionLeft(settings.initialPosition.left);
              setInitialPositionBottom(settings.initialPosition.bottom);
              setInitialPositionRight(settings.initialPosition.right);
            }
            if (settings.themeColorStart && settings.themeColorEnd) {
              setThemeColorStart(settings.themeColorStart);
              setThemeColorEnd(settings.themeColorEnd);
            }
            setBrandLogoUrl(settings.brandLogoUrl || "");
            if (settings.userContextExample) {
              setUserEmail(
                settings.userContextExample.email || "<EMAIL>",
              );
              setUserName(
                settings.userContextExample.name || "Test User (HMAC)",
              );
            }
            // Determine deploymentMode based on targetElementId presence in fetched settings
            if (settings.targetElementId) {
              setDeploymentMode("embedded");
            } else {
              setDeploymentMode("floating");
            }
            if (settings.gradientDirection) {
              setGradientDirection(settings.gradientDirection);
            }
            if (settings.darkMode !== undefined) {
              setDarkMode(settings.darkMode);
            }
            if (settings.autoclose !== undefined) {
              setAutoclose(settings.autoclose);
            }
          }
        }
      } catch (err) {
        console.error("Error fetching deployments:", err);
      } finally {
        setIsLoadingDeployments(false);
      }
    }

    if (agentId || agentDetails?.id) {
      fetchDeployments();
    }
  }, [agentId, agentDetails]);

  useEffect(() => {
    if (
      !isLoadingDeployments &&
      deploymentData &&
      agent &&
      !hasSetInitialState.current
    ) {
      // Capture the original position configuration as it was loaded
      setOriginalPositionConfig({
        type: selectedPositionType,
        top: initialPositionTop,
        left: initialPositionLeft,
        bottom: initialPositionBottom,
        right: initialPositionRight,
      });

      setInitialState({
        targetElementId,
        useCustomLauncher,
        initialPositionTop,
        initialPositionLeft,
        initialPositionBottom,
        initialPositionRight,
        selectedPositionType,
        themeColorEnd,
        themeColorStart,
        gradientDirection,
        brandLogoUrl,
        deploymentMode,
        userEmail,
        userName,
        allowedDomains,
        agentConfigurationPrompt:
          agent?.configuration?.chat_widget_instructions || "",
      });
      hasSetInitialState.current = true;
    }
  }, [
    isLoadingDeployments,
    deploymentData,
    agent,
    targetElementId,
    useCustomLauncher,
    initialPositionTop,
    initialPositionLeft,
    initialPositionBottom,
    initialPositionRight,
    selectedPositionType,
    themeColorStart,
    themeColorEnd,
    gradientDirection,
    brandLogoUrl,
    deploymentMode,
    userEmail,
    userName,
    allowedDomains,
    agentConfigurationPrompt,
  ]);

  // Reset initial state when agent changes
  useEffect(() => {
    hasSetInitialState.current = false;
    setInitialState(null);
  }, [agentId, agentDetails?.id]);

  useEffect(() => {
    if (agent) {
      setAgentConfigurationPrompt(
        agent?.configuration?.chat_widget_instructions || "",
      );

      // Reset initial state flag so it gets recalculated with new agent data
      if (hasSetInitialState.current) {
        hasSetInitialState.current = false;
        setInitialState(null);
      }
    }
  }, [agent]);

  const generateWidgetCode = (apiKey: string, agentId: string) => {
    const thenaWidgetConfig: WidgetConfig = {
      baseUrl: baseUrl || "http://localhost:8008",
      apiKey: apiKey,
      agentId: agentId,
      wsEndpoint: wsEndpoint || "ws://localhost:8008",
      useCustomLauncher: useCustomLauncher,
    };

    const user =
      userEmail || userName
        ? {
            ...(userEmail ? { email: userEmail } : {}),
            ...(userName ? { name: userName } : {}),
            hash: "YOUR_SERVER_SIDE_GENERATED_HMAC_HASH",
          }
        : undefined;

    if (user) {
      thenaWidgetConfig.user = user;
    }

    if (deploymentMode === "floating") {
      const cleanedInitialPosition = Object.fromEntries(
        Object.entries({
          top: initialPositionTop,
          left: initialPositionLeft,
          bottom: initialPositionBottom,
          right: initialPositionRight,
        }).filter(([_, v]) => v != null),
      );

      if (Object.keys(cleanedInitialPosition).length > 0) {
        thenaWidgetConfig.initialPosition = cleanedInitialPosition;
      }
    } else if (deploymentMode === "embedded") {
      if (targetElementId) {
        thenaWidgetConfig.targetElementId = targetElementId;
      }
    }

    if (themeColorStart && themeColorEnd) {
      thenaWidgetConfig.themeColorStart = themeColorStart;
      thenaWidgetConfig.themeColorEnd = themeColorEnd;
    }
    if (gradientDirection) {
      thenaWidgetConfig.gradientDirection = gradientDirection;
    }
    if (brandLogoUrl) {
      thenaWidgetConfig.brandLogoUrl = brandLogoUrl;
    }
    if (darkMode !== undefined) {
      thenaWidgetConfig.darkMode = darkMode;
    }
    if (autoclose !== undefined) {
      thenaWidgetConfig.autoclose = autoclose;
    }

    // Function to convert the object to a nicely formatted string for the script tag
    const configToString = (obj: Record<string, unknown>, indent = "  ") => {
      let result = "{\n";
      const keys = Object.keys(obj);
      keys.forEach((key, index) => {
        const value = obj[key];
        result += `${indent}  ${key}: `;
        if (typeof value === "string") {
          result += `\'${value.replace(/'/g, "\\\\'")}\'`;
        } else if (typeof value === "boolean" || typeof value === "number") {
          result += value;
        } else if (typeof value === "object" && value !== null) {
          result += configToString(
            value as Record<string, unknown>,
            `${indent}  `,
          ); // Recursive call for nested objects
        } else if (value === undefined) {
          // Skip undefined values
          result = result.substring(
            0,
            result.lastIndexOf(`${indent}  ${key}: `),
          ); // Remove the key line
          return; // and skip adding comma
        } else {
          result += "null";
        }
        if (
          index < keys.length - 1 &&
          !(
            value === undefined &&
            keys.slice(index + 1).every((k) => obj[k] === undefined)
          )
        ) {
          // Add comma if not the last real key
          const nextRealKeyIndex = keys.findIndex(
            (k, i) => i > index && obj[k] !== undefined,
          );
          if (nextRealKeyIndex !== -1) {
            result += ",\n";
          }
        } else if (value !== undefined) {
          result += "\n";
        }
      });
      result += `${indent.substring(0, indent.length - 2)}}`;
      return result;
    };

    const configString = configToString(thenaWidgetConfig);

    return `<script>\n  window.thenaWidget = ${configString};\n</script>\n<script src="${(
      widgetUrl || "https://widget.thena.tools/shim.js"
    ).replace(/'/g, "\\\\'")}"></script>`;
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        console.log(`${type} copied to clipboard.`);
      },
      (err) => {
        console.error(`Could not copy ${type}: ${err}`);
      },
    );
  };

  const handleDeploy = async () => {
    try {
      const widgetSettingsForApi: WidgetSettings = {
        useCustomLauncher: useCustomLauncher,
        themeColorStart: themeColorStart || undefined,
        themeColorEnd: themeColorEnd || undefined,
        gradientDirection: gradientDirection || undefined,
        brandLogoUrl: brandLogoUrl || undefined,
        userContextExample: {
          email: userEmail || undefined,
          name: userName || undefined,
        },
        darkMode: darkMode || undefined,
        autoclose: autoclose || undefined,
      };

      if (deploymentMode === "floating") {
        const cleanedInitialPosition = Object.fromEntries(
          Object.entries({
            top: initialPositionTop,
            left: initialPositionLeft,
            bottom: initialPositionBottom,
            right: initialPositionRight,
          }).filter(([_, v]) => v != null),
        );
        if (Object.keys(cleanedInitialPosition).length > 0) {
          widgetSettingsForApi.initialPosition = cleanedInitialPosition;
        }
      } else if (deploymentMode === "embedded") {
        widgetSettingsForApi.targetElementId = targetElementId;
      }
      // Remove userContextExample if both fields are empty
      if (
        !widgetSettingsForApi.userContextExample.email &&
        !widgetSettingsForApi.userContextExample.name
      ) {
        delete widgetSettingsForApi.userContextExample;
      }

      const response = await fetch(`/api/agents/${agent.id}/deployments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          team_id: selectedTeam,
          allowed_origins: allowedDomains,
          deployment_type: "widget",
          widget_settings: widgetSettingsForApi,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Deployment failed");
      }

      toast.success(
        "Deployment successful! Your widget configuration is ready.",
      );
      setDeploymentData(result);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred.";
      toast.error(errorMessage);
    }
  };
  const handleDomainChange = (index: number, value: string) => {
    const newDomains = [...allowedDomains];
    newDomains[index] = value;
    setAllowedDomains(newDomains);
  };

  const addDomain = () => {
    setAllowedDomains([...allowedDomains, ""]);
  };

  const removeDomain = (index: number) => {
    setAllowedDomains(allowedDomains.filter((_, i) => i !== index));
  };

  const updateAgentConfigMutation = useMutation({
    mutationFn: async (newInstructions: string) => {
      if (!agent?.id) throw new Error("Agent data not available for update.");
      setIsSavingChatWidgetInstructions(true);
      const updatedConfiguration = {
        ...agent.configuration,
        chat_widget_instructions: newInstructions,
      };

      const response = await fetch(`/api/agents/${agent.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: agent.name,
          status: agent.status,
          configuration: updatedConfiguration,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to update agent configuration",
        );
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success("Chat widget instructions saved successfully.");
      queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
      setIsSavingChatWidgetInstructions(false);
    },
    onError: (error: Error) => {
      toast.error(`Error saving instructions: ${error.message}`);
      setAgentConfigurationPrompt(
        agent.configuration?.chat_widget_instructions || "",
      );
      setIsSavingChatWidgetInstructions(false);
    },
  });

  if (isLoadingDeployments) {
    return;
  }

  if (!deploymentData) {
    return <WebChatEmptyConfig />;
  }

  if (teamId !== deploymentData?.team_id) {
    return <WebSettingsEmptyState />;
  }

  return (
    <div className="min-h-[calc(100vh-14rem)] flex flex-col">
      <div className="px-6 pt-14 pb-20 h-full overflow-y-auto overflow-x-hidden">
        <div className="grid gap-4 mx-auto max-w-[640px] pb-16 w-full">
          <div>
            <h2 className="text-2xl font-medium">Web chat</h2>
            <p className="text-sm text-[var(--color-text-muted)]">
              Embed our AI-native widget in your product for instant support.
            </p>
          </div>
          <div className="border-border border border-solid rounded-sm w-full p-4">
            <h3 className="text-lg font-medium mb-6">Set customization</h3>

            <label className="text-sm text-color-text">Logo</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Add your company logo using a public URL (SVG, PNG, JPG, JPEG,
              WebP supported).
            </p>
            <Input
              id="brand-logo-url"
              type="url"
              value={brandLogoUrl}
              className="flex-grow overflow-hidden text-ellipsis"
              onChange={(e) => setBrandLogoUrl(e.target.value)}
              placeholder="https://example.com/logo.svg"
              disabled={isLoading || isLoadingDeployments}
            />

            <label className="text-sm text-color-text">Theme color</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Pick a color to match your brand. This will be used across the
              chat widget and interface.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="text-xs text-color-text mb-1 block">
                  Theme Color Start (hex)
                </label>
                <div className="flex gap-2 items-center border rounded-sm px-2 py-1">
                  <ColorPicker
                    color={themeColorStart}
                    setColor={(color) => setThemeColorStart(color)}
                  />
                  <span className="w-18 inline-block grow-0 shrink-0 ml-2">
                    {themeColorStart || "#RRGGBB"}
                  </span>
                </div>
              </div>
              <div>
                <label className="text-xs text-color-text mb-1 block">
                  Theme Color End (hex)
                </label>
                <div className="flex gap-2 items-center border rounded-sm px-2 py-1">
                  <ColorPicker
                    color={themeColorEnd}
                    setColor={(color) => setThemeColorEnd(color)}
                  />
                  <span className="w-18 inline-block grow-0 shrink-0 ml-2">
                    {themeColorEnd || "#RRGGBB"}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <label
                className="text-sm text-color-text mb-1 block"
                htmlFor="gradient-direction"
              >
                Gradient Direction
              </label>
              <Input
                id="gradient-direction"
                value={gradientDirection}
                onChange={(e) => setGradientDirection(e.target.value)}
                placeholder="e.g., to bottom right, 45deg"
                disabled={isLoading || isLoadingDeployments}
                className="mb-1 overflow-hidden text-ellipsis"
              />
              <p className="text-xs text-[var(--color-text-muted)] mb-4">
                Examples: <code>to right</code>, <code>to bottom left</code>,{" "}
                <code>45deg</code>, <code>135deg</code>. Default is{" "}
                <code>135deg</code>. If Theme Color End is empty or same as
                Start, a solid color (Start) will be used.
              </p>
            </div>

            <label className="text-sm text-color-text">Position</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Choose where the chat widget should appear on your site.
            </p>

            <div className="mb-4">
              <CommonSelectWrapper
                placeholder="Select position"
                hideClearIndicator
                isClearable={false}
                options={[
                  { value: "bottom-right", label: "Bottom right" },
                  { value: "bottom-left", label: "Bottom left" },
                  { value: "top-right", label: "Top right" },
                  { value: "top-left", label: "Top left" },
                  { value: "custom", label: "Custom position" },
                ]}
                name="position"
                value={{
                  value: selectedPositionType,
                  label:
                    selectedPositionType === "bottom-right"
                      ? "Bottom right"
                      : selectedPositionType === "bottom-left"
                      ? "Bottom left"
                      : selectedPositionType === "top-right"
                      ? "Top right"
                      : selectedPositionType === "top-left"
                      ? "Top left"
                      : "Custom position",
                }}
                onChange={(value) => {
                  const position = value as { value: string; label: string };
                  setSelectedPositionType(position.value);
                  switch (position.value) {
                    case "bottom-right":
                      setInitialPositionBottom("30px");
                      setInitialPositionRight("35px");
                      setInitialPositionLeft(undefined);
                      setInitialPositionTop(undefined);
                      break;
                    case "bottom-left":
                      setInitialPositionBottom("30px");
                      setInitialPositionLeft("35px");
                      setInitialPositionRight(undefined);
                      setInitialPositionTop(undefined);
                      break;
                    case "top-right":
                      setInitialPositionTop("30px");
                      setInitialPositionRight("35px");
                      setInitialPositionLeft(undefined);
                      setInitialPositionBottom(undefined);
                      break;
                    case "top-left":
                      setInitialPositionTop("30px");
                      setInitialPositionLeft("35px");
                      setInitialPositionRight(undefined);
                      setInitialPositionBottom(undefined);
                      break;
                    case "custom":
                      // Do not change values, allow user to edit
                      break;
                  }
                }}
                isVirtualized={false}
                isOptionsMemoized
                triggerClassname="w-full h-6"
                wrapperClassname="!max-w-[80%] w-full"
              />
            </div>

            {selectedPositionType === "custom" && (
              <div className="grid grid-cols-4 gap-4 mb-4">
                <div>
                  <label className="text-sm text-color-text mb-2 block">
                    Left
                  </label>
                  <Input
                    type="text"
                    value={initialPositionLeft}
                    onChange={(e) => setInitialPositionLeft(e.target.value)}
                    placeholder="px"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-sm text-color-text mb-2 block">
                    Right
                  </label>
                  <Input
                    type="text"
                    value={initialPositionRight}
                    onChange={(e) => setInitialPositionRight(e.target.value)}
                    placeholder="px"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-sm text-color-text mb-2 block">
                    Top
                  </label>
                  <Input
                    type="text"
                    value={initialPositionTop}
                    onChange={(e) => setInitialPositionTop(e.target.value)}
                    placeholder="px"
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-sm text-color-text mb-2 block">
                    Bottom
                  </label>
                  <Input
                    type="text"
                    value={initialPositionBottom}
                    onChange={(e) => setInitialPositionBottom(e.target.value)}
                    placeholder="px"
                    className="w-full"
                  />
                </div>
              </div>
            )}

            <label className="text-sm text-color-text">Widget type</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Select the default chat experience. Use Thena&apos;s standard
              widget or link it to a custom element on your site.
            </p>
            <div className="mb-4">
              <CommonSelectWrapper
                placeholder="Select widget type"
                hideClearIndicator
                isClearable={false}
                options={[
                  { value: "floating", label: "Thena's default widget" },
                  { value: "embedded", label: "Custom embed" },
                ]}
                name="widget-type"
                value={{
                  value: deploymentMode,
                  label:
                    deploymentMode === "floating"
                      ? "Thena's default widget"
                      : "Custom embed",
                }}
                onChange={(value) => {
                  const option = value as { value: string; label: string };
                  setDeploymentMode(option.value as "floating" | "embedded");
                }}
                isVirtualized={false}
                isOptionsMemoized
                triggerClassname="w-full h-6"
                wrapperClassname="!max-w-[80%] w-full"
              />
            </div>
            {deploymentMode === "embedded" && (
              <div className="mb-4">
                <label className="text-sm text-color-text flex items-center gap-1">
                  Target element ID
                  <span
                    className="ml-1 text-xs text-[var(--color-text-muted)] cursor-pointer"
                    tabIndex={0}
                    aria-label="Specify the ID element where the chat widget should be embedded."
                  >
                    &#9432;
                  </span>
                </label>
                <p className="text-sm text-[var(--color-text-muted)] mb-2">
                  Specify the ID element where the chat widget should be
                  embedded.
                </p>
                <Input
                  type="text"
                  value={targetElementId}
                  onChange={(e) => setTargetElementId(e.target.value)}
                  placeholder="e.g., my-thena-widget"
                  className="w-full"
                  aria-label="Target element ID"
                />
              </div>
            )}

            <div className="flex items-center space-x-2 mt-4">
              <Checkbox
                id="use-custom-launcher"
                checked={useCustomLauncher}
                onCheckedChange={(checkedState) =>
                  setUseCustomLauncher(
                    checkedState === true || checkedState === "indeterminate"
                      ? true
                      : false,
                  )
                }
                disabled={isLoading || isLoadingDeployments}
              />
              <Label
                htmlFor="use-custom-launcher"
                className="text-sm font-normal cursor-pointer text-color-text"
              >
                Use Custom Launcher Button
              </Label>
            </div>

            <div className="flex items-center space-x-2 mt-4">
              <Checkbox
                id="dark-mode"
                checked={darkMode}
                onCheckedChange={(checkedState) =>
                  setDarkMode(checkedState === true)
                }
                disabled={isLoading || isLoadingDeployments}
              />
              <Label
                htmlFor="dark-mode"
                className="text-sm font-normal cursor-pointer text-color-text"
              >
                Initial Dark Mode
              </Label>
            </div>

            {deploymentMode === "floating" && (
              <div className="flex items-center space-x-2 mt-4">
                <Checkbox
                  id="autoclose-widget"
                  checked={autoclose}
                  onCheckedChange={(checkedState) =>
                    setAutoclose(checkedState === true)
                  }
                  disabled={isLoading || isLoadingDeployments}
                />
                <Label
                  htmlFor="autoclose-widget"
                  className="text-sm font-normal cursor-pointer text-color-text"
                >
                  Auto-close widget on outside click
                </Label>
              </div>
            )}

            {/* API information - ADDED */}
            <div className="mt-6 p-4 border rounded-sm bg-muted/50 text-sm text-[var(--color-text-muted)] space-y-2">
              <p className="font-medium text-color-text mb-1">
                Using the Widget JavaScript API:
              </p>
              <p>
                You can control the widget programmatically using the following
                JavaScript functions on the <code>window.thena</code> object:
              </p>
              <ul className="list-disc pl-5 my-2 space-y-1">
                <li>
                  <code>window.thena.open()</code> - Opens the widget.
                </li>
                <li>
                  <code>window.thena.close()</code> - Closes the widget.
                </li>
                <li>
                  <code>window.thena.toggle()</code> - Toggles the widget
                  visibility.
                </li>
                <li>
                  <code>window.thena.toggleDarkMode()</code> - Toggles between
                  light and dark themes.
                </li>
                <li>
                  <code>window.thena.toggleVisibility()</code> - Shows/hides the
                  widget and its launcher (if default launcher is used).
                </li>
              </ul>
              <p>
                Example (for a custom button with ID &apos;myChatButton&apos; to
                open the chat):
              </p>
              <pre className="mt-1 p-2 rounded-sm bg-background font-mono text-xs overflow-x-auto">
                {`document.getElementById('myChatButton').addEventListener('click', function() {\n  if (window.thena && window.thena.open) {\n    window.thena.open();\n  }\n});`}
              </pre>
            </div>

            <div className="w-full flex justify-end">
              <Button
                onClick={handleDeploy}
                disabled={
                  isLoading ||
                  isLoadingDeployments ||
                  !hasCustomizationChanges()
                }
                size="sm"
                className="mt-3"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save
              </Button>
            </div>
          </div>

          <div className="border-border border border-solid rounded-sm w-full p-4">
            <h3 className="text-lg font-medium mb-6">Deploy web chat</h3>
            <label className="text-sm text-color-text">Code snippet</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Add this code to your website&apos;s HTML, just before the closing{" "}
              {`</body>`} tag. Customize the appearance and behavior using the
              configuration options.
            </p>
            <div className="py-4">
              <div className="space-y-4">
                <div className="relative">
                  <pre className="p-4 rounded-sm bg-muted font-mono text-sm overflow-x-auto whitespace-pre-wrap break-all">
                    {deploymentData &&
                      deploymentData.agent_key &&
                      deploymentData.agent_id &&
                      generateWidgetCode(
                        deploymentData.agent_key,
                        deploymentData.agent_id,
                      )}
                  </pre>
                  {deploymentData &&
                    deploymentData.agent_key &&
                    deploymentData.agent_id && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="absolute top-2 right-2"
                        onClick={() =>
                          copyToClipboard(
                            generateWidgetCode(
                              deploymentData.agent_key,
                              deploymentData.agent_id,
                            ),
                            "Widget Code",
                          )
                        }
                      >
                        <Copy className="h-4 w-4" />
                        <span className="sr-only">Copy code</span>
                      </Button>
                    )}
                </div>
              </div>
            </div>
            <label className="text-sm text-color-text">HMAC secret key</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              This key is shown ONLY ONCE. Store it securely on your server and
              never expose it client-side.
            </p>

            <div className="relative flex items-center mb-6">
              <Input
                id="hmac-key-display"
                readOnly
                value={deploymentData?.hmac_secret_key || ""}
                className="font-mono text-xs pr-10 bg-background overflow-hidden text-ellipsis"
              />
              {deploymentData?.hmac_secret_key !==
                "Secret key only shown during creation" && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute right-1 h-7 w-7 p-0"
                  onClick={() =>
                    copyToClipboard(
                      deploymentData?.hmac_secret_key,
                      "HMAC Secret Key",
                    )
                  }
                >
                  <Copy className="h-3.5 w-3.5" />
                  <span className="sr-only">Copy HMAC Secret Key</span>
                </Button>
              )}
            </div>

            <label className="text-sm text-color-text">Allowed domains</label>
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Enter the full domains (including http/https) where your widget
              will be hosted. This helps secure your deployment..
            </p>

            <div className="space-y-3">
              {allowedDomains.map((domain, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    type="text"
                    value={domain}
                    onChange={(e) => handleDomainChange(index, e.target.value)}
                    placeholder="https://www.example.com"
                    className="flex-grow overflow-hidden text-ellipsis"
                    disabled={isLoading || isLoadingDeployments}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeDomain(index)}
                    aria-label="Remove domain"
                    disabled={isLoading || isLoadingDeployments}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={addDomain}
                className="mt-3"
                disabled={isLoading || isLoadingDeployments}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add domain
              </Button>
            </div>
            <div className="w-full flex justify-end">
              <Button
                onClick={handleDeploy}
                disabled={
                  isLoading || isLoadingDeployments || !hasDeploymentChanges()
                }
                size="sm"
                className="mt-3"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save
              </Button>
            </div>
          </div>

          <div className="border-border border border-solid rounded-sm w-full p-4">
            <div className="flex-1 min-w-0 text-left">
              <div className="flex flex-col gap-4">
                <div className="flex items-center">
                  <h3 className="text-base font-medium truncate flex items-center">
                    Agent configuration prompt
                  </h3>
                </div>

                <textarea
                  id="agent-configuration-prompt"
                  className="w-full p-3 text-sm border border-border rounded-sm resize-y min-h-[100px] bg-background focus:ring-1 focus:ring-primary/30 focus:border-primary/30 outline-none transition-colors overflow-x-auto"
                  value={agentConfigurationPrompt}
                  placeholder={`Set ${agent?.name}'s support style and handoff rules. Define tone, response format, and when to escalate to your team.`}
                  onChange={(e) => {
                    setAgentConfigurationPrompt(e.target.value);
                  }}
                />
                <div className="flex justify-end">
                  <Button
                    onClick={() =>
                      updateAgentConfigMutation.mutate(agentConfigurationPrompt)
                    }
                    disabled={
                      isSavingChatWidgetInstructions || !hasAgentConfigChanges()
                    }
                    size="sm"
                  >
                    {isSavingChatWidgetInstructions && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save
                  </Button>
                </div>
              </div>
            </div>
          </div>
          {agent && (
            <WebChatAgentBanner AGENT_NAME={AGENT_NAME} agentDetails={agent} />
          )}
        </div>
      </div>
    </div>
  );
}
