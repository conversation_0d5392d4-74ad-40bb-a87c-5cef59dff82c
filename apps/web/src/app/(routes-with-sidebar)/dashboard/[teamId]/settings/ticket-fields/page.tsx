"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getFieldTypeConfig } from "@/lib/field-types";
import { formatDistanceToNow } from "date-fns";
import { Pencil, Plus } from "lucide-react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import * as React from "react";
import { toast } from "sonner";
import EditPriorities from "./components/EditableStandardFields/EditPriorities";
import EditSentiment from "./components/EditableStandardFields/EditSentiment";
import EditTypes from "./components/EditableStandardFields/EditTypes";
import StandardFieldItem from "./components/standard-field-item";
import { useStandardFields } from "./hooks/use-standard-fields";
import { CustomField, StandardField } from "./types";

// Add CSS for pulse animation
const pulseAnimation = `
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}
`;

const EmptyState = () => (
  <div className="flex flex-col items-center justify-center py-24 text-center">
    <div className="rounded-full bg-secondary p-6 mb-6 border border-border">
      <Plus className="h-8 w-8 text-muted-foreground" />
    </div>
    <h3 className="text-xl font-semibold text-foreground mb-2">
      No ticket fields yet
    </h3>
    <p className="text-sm text-muted-foreground max-w-[400px]">
      Click the Create field button in the top-right to start customizing your
      tickets.
    </p>
  </div>
);

const formatTimeAgo = (timestamp: string | undefined) => {
  if (!timestamp) return "Never";

  try {
    const date = new Date(timestamp);

    // Validate the date
    if (isNaN(date.getTime())) {
      return "Never";
    }

    return formatDistanceToNow(date, { addSuffix: true });
  } catch (error) {
    console.error("Error formatting time:", error);
    return "Never";
  }
};

const CustomFieldsSettings = () => {
  const resolvedParams = useParams();
  const router = useRouter();
  const [customFields, setCustomFields] = React.useState<CustomField[]>([]);
  const {
    standardFields,
    isLoading: isLoadingStandardFields,
    error: standardFieldsError,
  } = useStandardFields();
  const [searchQuery, setSearchQuery] = React.useState("");
  const [isLoadingFields, setIsLoadingFields] = React.useState(true);
  const [currentPage, setCurrentPage] = React.useState(0);
  const [hasMore, setHasMore] = React.useState(true);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);
  const mainContainerRef = React.useRef<HTMLElement | null>(null);
  const [highlightedFieldId, setHighlightedFieldId] = React.useState<
    string | null
  >(null);
  const [_error, _setError] = React.useState<string | null>(null);
  const [_errorDetails, _setErrorDetails] = React.useState<string | null>(null);

  const filteredFields = React.useMemo(() => {
    return customFields
      .sort((a, b) => {
        // Get the most recent timestamp between creation and update for each field
        const aTime = Math.max(
          a.updatedAt ? new Date(a.updatedAt).getTime() : 0,
          a.createdAt ? new Date(a.createdAt).getTime() : 0,
        );
        const bTime = Math.max(
          b.updatedAt ? new Date(b.updatedAt).getTime() : 0,
          b.createdAt ? new Date(b.createdAt).getTime() : 0,
        );

        if (aTime !== bTime) {
          return bTime - aTime; // Most recent first
        }

        // If timestamps are equal or not present, sort by ID in reverse order
        return (b.id || "").localeCompare(a.id || "");
      })
      .filter((field) => {
        const matchesSearch = field.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase());
        return matchesSearch;
      });
  }, [customFields, searchQuery]);

  // Filter standard fields based on search
  const filteredStandardFields = React.useMemo(() => {
    if (!searchQuery.trim()) return standardFields;

    return Object.entries(standardFields).reduce(
      (filtered, [key, field]) => {
        if (
          field.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          field.description.toLowerCase().includes(searchQuery.toLowerCase())
        ) {
          filtered[key] = field;
        }
        return filtered;
      },
      {} as Record<string, StandardField>,
    );
  }, [standardFields, searchQuery]);

  const handleAddFieldClick = React.useCallback(() => {
    router.push(
      `/dashboard/${resolvedParams.teamId}/settings/ticket-fields/new`,
    );
  }, [router, resolvedParams.teamId]);

  const fetchCustomFields = React.useCallback(
    async (page: number = 0) => {
      try {
        if (page === 0) {
          setIsLoadingFields(true);
        } else {
          setIsLoadingMore(true);
        }

        const response = await fetch(
          `/api/custom-fields?page=${page}&limit=100&teamId=${resolvedParams.teamId}&onlyTeamFields=true&source=ticket`,
          {
            cache: "no-store",
            headers: {
              "Cache-Control": "no-cache",
            },
          },
        );

        const data = await response.json();

        if (!response.ok) {
          throw new Error("Failed to fetch ticket fields");
        }

        // Transform the data to preserve existing timestamps
        const transformedFields = (data.data?.results || []).map(
          (field: CustomField) => ({
            ...field,
          }),
        );

        if (page === 0) {
          setCustomFields(transformedFields);
        } else {
          setCustomFields((prev) => [...prev, ...transformedFields]);
        }

        setHasMore(data.data?.hasMore);
        setCurrentPage(page);
      } catch (error) {
        console.error("Error fetching ticket fields:", error);
        toast.error("Failed to fetch ticket fields.");
      } finally {
        setIsLoadingFields(false);
        setIsLoadingMore(false);
      }
    },
    [resolvedParams.teamId],
  );

  const handleScroll = React.useCallback(() => {
    if (!mainContainerRef.current || isLoadingMore || !hasMore) return;

    const container = mainContainerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // Load more when user scrolls to bottom (with 100px threshold)
    if (scrollHeight - scrollTop - clientHeight < 100) {
      fetchCustomFields(currentPage + 1);
    }
  }, [
    fetchCustomFields,
    currentPage,
    isLoadingMore,
    hasMore,
    mainContainerRef,
  ]);

  const handleSearch = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    [],
  );

  React.useEffect(() => {
    fetchCustomFields(0);
    // Standard fields are now fetched via the useStandardFields hook automatically
  }, [fetchCustomFields, resolvedParams.teamId]);

  // Effect to check for highlighted field ID in localStorage
  React.useEffect(() => {
    if (typeof window !== "undefined") {
      const fieldIdToHighlight = localStorage.getItem("highlightFieldId");
      if (fieldIdToHighlight) {
        setHighlightedFieldId(fieldIdToHighlight);

        // Clear the localStorage item after 3 seconds
        const timer = setTimeout(() => {
          setHighlightedFieldId(null);
          localStorage.removeItem("highlightFieldId");
        }, 3000);

        return () => clearTimeout(timer);
      }
    }
  }, [customFields]);

  const setMainContainerRef = React.useCallback((node: HTMLElement | null) => {
    if (node) {
      mainContainerRef.current = node;
    }
  }, []);

  React.useEffect(() => {
    const container = mainContainerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
      return () => {
        if (container) {
          container.removeEventListener("scroll", handleScroll);
        }
      };
    }
  }, [handleScroll]);

  // Show loader when any data is loading - exactly like Account fields page
  if (isLoadingFields || isLoadingStandardFields) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-y-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  // Display an error if standard fields fetch failed
  if (standardFieldsError) {
    // Log error but don't block the UI - user can still work with custom fields
    console.error("Error loading standard fields:", standardFieldsError);
    toast.error(
      "Failed to load standard fields. Some information may be missing.",
    );
  }

  return (
    <main ref={setMainContainerRef} className="flex-1 overflow-y-auto">
      {/* Add style tag for pulse animation */}
      <style jsx global>
        {pulseAnimation}
      </style>
      <div className="container mx-auto py-6">
        <div className="max-w-[640px] mx-auto">
          <div className="flex flex-col gap-6">
            <div>
              <h1 className="text-2xl font-medium">Ticket fields</h1>
              <p className="text-sm text-muted-foreground mt-1">
                Manage custom fields for your tickets.
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Input
                  placeholder="Search fields"
                  value={searchQuery}
                  onChange={handleSearch}
                  className="w-[300px]"
                />
              </div>
              <Button
                onClick={handleAddFieldClick}
                variant="default"
                size="sm"
                className="text-[14px]"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create field
              </Button>
            </div>

            <div className="flex flex-col gap-4">
              {/* Combined Fields List */}
              <div>
                {filteredFields.length === 0 &&
                Object.keys(filteredStandardFields).length === 0 ? (
                  <div className="text-sm text-muted-foreground border rounded-sm p-4 bg-card">
                    {searchQuery ? (
                      <p>No fields match your search.</p>
                    ) : (
                      <EmptyState />
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col gap-4">
                    {/* Custom Fields */}
                    {filteredFields.map((field) => {
                      const fieldType = field.fieldType;
                      const fieldConfig = getFieldTypeConfig(fieldType);
                      const Icon = fieldConfig?.icon;
                      const colorClass =
                        fieldConfig?.color ||
                        "bg-gray-100 text-gray-700 hover:bg-gray-100";
                      const typeLabel = fieldConfig?.label || fieldType;

                      return (
                        <div
                          key={field.id}
                          className={`border rounded-sm px-4 py-3 bg-card flex items-center justify-between transition-all duration-500 ${
                            highlightedFieldId === field.id
                              ? "border-primary bg-primary/10"
                              : ""
                          }`}
                          style={
                            highlightedFieldId === field.id
                              ? { animation: "pulse 2s infinite" }
                              : undefined
                          }
                        >
                          <div className="flex items-center gap-4">
                            <span className="font-medium">{field.name}</span>
                            <span
                              className={`inline-flex items-center px-2 py-1 rounded-[4px] text-xs ${colorClass}`}
                            >
                              {Icon && <Icon className="h-3 w-3 mr-1" />}
                              {typeLabel}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {field.updatedAt !== field.createdAt ? (
                                <>
                                  • Last modified{" "}
                                  {formatTimeAgo(field.updatedAt)}
                                </>
                              ) : (
                                <>• Created {formatTimeAgo(field.createdAt)}</>
                              )}
                            </span>
                          </div>
                          <button
                            onClick={() => {
                              router.push(
                                `/dashboard/${resolvedParams.teamId}/settings/ticket-fields/${field.id}`,
                              );
                            }}
                          >
                            <Pencil className="h-4 w-4" />
                          </button>
                        </div>
                      );
                    })}

                    <EditSentiment />

                    <EditPriorities />

                    <EditTypes />

                    {/* Standard Fields */}
                    {Object.entries(filteredStandardFields)
                      .filter(([_fieldId, field]) => {
                        // Hide specific standard fields that should not be shown in the UI
                        const fieldLabel = field.label.toLowerCase();
                        const hiddenFields = [
                          "status",
                          "tags",
                          "id",
                          "ticket id",
                          "created at",
                          "updated at",
                          "private",
                          "is draft",
                          "customer contact",
                          "priority",
                          "sentiment",
                          "type",
                        ];
                        return !hiddenFields.includes(fieldLabel);
                      })
                      .map(([fieldId, field]) => (
                        <StandardFieldItem
                          key={fieldId}
                          fieldId={fieldId}
                          field={field}
                        />
                      ))}
                  </div>
                )}
                {isLoadingMore && (
                  <div className="flex justify-center py-4">
                    <ThenaLoader />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default CustomFieldsSettings;
