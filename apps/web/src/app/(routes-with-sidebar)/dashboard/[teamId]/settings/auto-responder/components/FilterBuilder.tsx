"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusIcon, X } from "lucide-react";
import { useEffect, useState } from "react";
import FieldSelector from "../../../../../../../components/field-selector";

interface FilterBuilderProps {
  onAddFilter: (filter: Record<string, unknown>) => void;
  filters: Record<string, unknown>[];
  onRemoveFilter: (index: number) => void;
  teamId: string;
}

interface Filter {
  field: string;
  operator: string;
  value: string | number | boolean;
  label?: string;
}

export function FilterBuilder({
  onAddFilter,
  teamId,
  filters = [],
  onRemoveFilter,
}: FilterBuilderProps) {
  // State to manage field selectors in the UI - can be completely empty
  const [fieldSelectors, setFieldSelectors] = useState<Filter[]>([]);

  // Track if this is just initialization
  const [initialized, setInitialized] = useState(false);

  // Effect to initialize field selectors when filters change
  useEffect(() => {
    if (filters && Array.isArray(filters)) {
      // Only replace fields on initialization or if filters array changes size
      if (!initialized || fieldSelectors.length !== filters.length) {
        // Convert existing filters to UI format
        const convertedFilters = filters.map((filter) => {
          const field = Object.keys(filter)[0] || "";
          const operatorObj = filter[field] || {};
          const operator = Object.keys(operatorObj)[0] || "";
          const value = operatorObj[operator];

          return {
            field,
            operator: convertOperatorForUI(operator),
            value,
          };
        });

        // Set only the active filters - no empty placeholder
        setFieldSelectors(convertedFilters);

        // Mark as initialized
        if (!initialized) {
          setInitialized(true);
        }
      } else {
        // Just update the field selectors based on current filters
        // This is important when removing a filter
        const updatedSelectors = [...fieldSelectors];

        // Find field selectors that should be cleared because their filter was removed
        const activeFieldNames = filters.map((f) => Object.keys(f)[0]);

        // Remove any field selectors that don't have a matching filter
        const newSelectors = updatedSelectors.filter((selector) => {
          // If it's an empty selector or its field is in active filters, keep it
          return !selector.field || activeFieldNames.includes(selector.field);
        });

        setFieldSelectors(newSelectors);
      }
    } else {
      // If no filters, have empty field selectors array
      setFieldSelectors([]);
    }
  }, [filters, initialized]);

  // Convert operator formats between UI and API
  const convertOperatorForUI = (operator) => {
    switch (operator) {
      case "~eq":
      case "=":
        return "equals";
      case "~neq":
      case "!=":
        return "not equals";
      case "~in":
        return "in";
      case "~nin":
        return "not in";
      case "~contains":
        return "contains";
      case "~ncontains":
        return "not contains";
      case "~gt":
      case ">":
        return "greater than";
      case "~gte":
      case ">=":
        return "greater than or equal";
      case "~lt":
      case "<":
        return "less than";
      case "~lte":
      case "<=":
        return "less than or equal";
      case "~isNull":
        return "is empty";
      case "~isNotNull":
        return "is not empty";
      case "~isTrue":
        return "is true";
      case "~isFalse":
        return "is false";
      default:
        return operator;
    }
  };

  // When a field selector changes
  const handleFieldChange = (index, value) => {
    setFieldSelectors((prev) => {
      const updated = [...prev];
      updated[index] = value;
      return updated;
    });

    // If it's a complete filter, add it to active filters
    if (value.field && value.operator && value.value !== "") {
      onAddFilter({
        [value.field]: {
          [value.operator]: value.value,
        },
      });
    }
  };

  // Handle removing a field selector
  const handleRemoveField = (index) => {
    const fieldToRemove = fieldSelectors[index];

    // If this field has a value and corresponds to an active filter
    if (fieldToRemove.field && fieldToRemove.value !== "") {
      // Find corresponding filter in the active filters
      const filterIndex = filters.findIndex((filter) => {
        const field = Object.keys(filter)[0];
        return field === fieldToRemove.field;
      });

      // If found, remove it from active filters
      if (filterIndex !== -1) {
        onRemoveFilter(filterIndex);
      }
    }

    // Always remove the field from UI state - no minimum requirement
    setFieldSelectors((prev) => prev.filter((_, i) => i !== index));
  };

  // Add a new empty field selector
  const handleAddField = () => {
    setFieldSelectors((prev) => [
      ...prev,
      { field: "", operator: "equals", value: "" },
    ]);
  };

  return (
    <div className="space-y-4">
      {/* Field Selectors */}
      {fieldSelectors.map((fieldSelector, idx) => (
        <div
          className="flex gap-2 items-center"
          key={`field-${idx}-${fieldSelector.field}`}
        >
          <FieldSelector
            entityType="Ticket"
            onChange={(v) => handleFieldChange(idx, v)}
            teamId={teamId}
            className="w-full"
            initialValue={fieldSelector}
          />
          <Button
            variant="ghost"
            size="icon"
            className="h-4 w-4 p-0 hover:bg-transparent ml-1"
            onClick={() => handleRemoveField(idx)}
            aria-label="Remove field"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      ))}

      {/* Add Field Button */}
      <div className="flex justify-start mt-2">
        <Button
          type="button"
          size="sm"
          onClick={handleAddField}
          className="flex items-center gap-1 rounded"
          style={{ borderRadius: "4px" }}
          variant="outline"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add filter</span>
        </Button>
      </div>
    </div>
  );
}
