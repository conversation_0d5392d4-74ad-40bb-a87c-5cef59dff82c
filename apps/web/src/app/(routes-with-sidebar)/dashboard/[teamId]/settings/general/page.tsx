"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import TooltipWrapper from "@/components/tooltip-wrapper";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { GET_ALL_TEAMS } from "@/services/kanban";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import * as HeroIcons from "@heroicons/react/24/solid";
import { kebabCase } from "lodash";
import { Globe2, Loader2, Lock } from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

interface Team {
  id: string;
  name: string;
  identifier: string;
  visibility: string;
  icon?: string;
  iconColor?: IconColor;
}

interface ApiResponse<T> {
  data?: T;
  error?: string;
}

type IconName = keyof typeof HeroIcons;

const ICON_COLORS = {
  purple: "rgb(155, 135, 245)",
  red: "rgb(248, 113, 113)",
  green: "rgb(52, 211, 153)",
  blue: "rgb(96, 165, 250)",
  indigo: "rgb(129, 140, 248)",
  violet: "rgb(192, 132, 252)",
  pink: "rgb(232, 121, 249)",
  orange: "rgb(251, 146, 60)",
  yellow: "rgb(251, 191, 36)",
  lime: "rgb(163, 230, 53)",
} as const;

type IconColor = keyof typeof ICON_COLORS;

export default function GeneralSettings() {
  const resolvedParams = useParams();
  const teamId = resolvedParams.teamId;
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    identifier: "",
    visibility: "public",
    icon: "RocketLaunchIcon",
    iconColor: ICON_COLORS.purple,
  });
  const [originalData, setOriginalData] = useState({
    name: "",
    identifier: "",
    visibility: "public",
    icon: "RocketLaunchIcon",
    iconColor: ICON_COLORS.purple,
  });
  const [isUpdating, setIsUpdating] = useState<{
    name: boolean;
    visibility: boolean;
    identifier: boolean;
  }>({
    name: false,
    visibility: false,
    identifier: false,
  });
  const [hasChanges, setHasChanges] = useState<{
    name: boolean;
    identifier: boolean;
  }>({
    name: false,
    identifier: false,
  });
  const [errors, setErrors] = useState({
    name: "",
    identifier: "",
  });
  const [showVisibilityModal, setShowVisibilityModal] = useState(false);
  const [pendingVisibility, setPendingVisibility] = useState<
    "public" | "private"
  >("public");
  const [iconSearch, setIconSearch] = useState("");

  const filteredIcons = Object.keys(HeroIcons).filter((iconName) =>
    iconName.toLowerCase().includes(iconSearch.toLowerCase()),
  );

  useEffect(() => {
    const loadTeam = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/teams/${teamId}`);
        const result = await response.json();

        if (!response.ok || result.error) {
          throw new Error(result.error || "Failed to fetch team");
        }
        if (result.data?.data) {
          const initialData = {
            name: result.data.data.name,
            identifier: result.data.data.identifier,
            visibility: result.data.data.isPrivate ? "private" : "public",
            icon: result.data.data.icon || "RocketLaunchIcon",
            iconColor: result.data.data.color || ("purple" as IconColor),
          };
          setFormData(initialData);
          setOriginalData(initialData);
          setHasChanges({
            name: false,
            identifier: false,
          });
        }
        setError(null);
      } catch (e) {
        console.error("Error fetching team:", e);
        setError(
          e instanceof Error
            ? e.message
            : "Failed to load team. Please try again later.",
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadTeam();
  }, [teamId]);
  const handleDelete = async () => {
    if (deleteConfirmation !== formData?.name) {
      return;
    }

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/teams/${teamId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || "Failed to delete team");
      }

      // Redirect to teams list or dashboard
      window.location.href = "/dashboard";
    } catch (e) {
      console.error("Error deleting team:", e);
      setError(
        e instanceof Error
          ? e.message
          : "Failed to delete team. Please try again later.",
      );
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const validateField = (field: string, value: string) => {
    if (field === "name") {
      if (!value.trim()) {
        return "Team name is required";
      } else if (value.length < 1) {
        return "Team name must be at least 1 character";
      } else if (
        !/^[\p{L}\p{N}\p{Emoji}][\p{L}\p{N}\p{Emoji}\s\-_]*[\p{L}\p{N}\p{Emoji}]$/u.test(
          formData.name.trim(),
        )
      ) {
        return "Team name must start and end with a letter, and contain only letters, numbers, emojis, single spaces, hyphens, or underscores.";
      }
    }

    if (field === "identifier") {
      if (!value.trim()) {
        return "Team identifier is required";
      } else if (value.length < 1) {
        return "Identifier must be at least 1 character";
      } else if (value.length > 7) {
        return "Identifier must not exceed 7 characters";
      } else if (!/^[A-Z]+$/.test(value)) {
        return "Identifier must contain only capital letters";
      }
    }

    return "";
  };

  const handleUpdateField = async (field: string, value: string) => {
    if (field === "visibility") {
      setPendingVisibility(value as "public" | "private");
      setShowVisibilityModal(true);
      return;
    }

    // Trim the value before validation and update
    const trimmedValue = value.trim();

    if (field === "name" || field === "identifier") {
      const error = validateField(field, trimmedValue);
      if (error) {
        setErrors((prev) => ({ ...prev, [field]: error }));
        return;
      }
    }

    try {
      setIsUpdating((prev) => ({ ...prev, [field]: true }));

      const payload =
        field === "visibility"
          ? { isPrivate: trimmedValue === "private" }
          : field === "name"
          ? {
              name: trimmedValue,
              icon: formData.icon,
              color: formData.iconColor,
            }
          : { [field]: trimmedValue };

      const response = await fetch(`/api/teams/${teamId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result: ApiResponse<Team> = await response.json();

      if (!response.ok || result.error) {
        throw new Error(result.error || `Failed to update ${field}`);
      }

      setOriginalData((prev) => ({
        ...prev,
        [field]: value,
      }));

      setHasChanges((prev) => ({
        ...prev,
        [field]: false,
      }));

      const userId = useGlobalConfigPersistStore.getState().currentUser.uid;

      const teamsList = await fetch(GET_ALL_TEAMS(userId), {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const teamsData = await teamsList.json();
      useTicketMetaStore.getState().setAllTeams([...teamsData]);
      setFormData((prev) => ({
        ...prev,
        [field]:
          field === "visibility" ? value : result.data?.[field] || prev[field],
      }));
      setError(null);
    } catch (e) {
      console.error(`Error updating ${field}:`, e);
      setError(
        e instanceof Error
          ? e.message
          : `Failed to update ${field}. Please try again later.`,
      );
    } finally {
      setIsUpdating((prev) => ({ ...prev, [field]: false }));
    }
  };

  const handleVisibilityConfirm = async () => {
    try {
      setIsUpdating((prev) => ({ ...prev, visibility: true }));

      const response = await fetch(`/api/teams/${teamId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isPrivate: pendingVisibility === "private" }),
      });

      const result: ApiResponse<Team> = await response.json();

      if (!response.ok || result.error) {
        throw new Error(result.error || `Failed to update visibility`);
      }
      const userId = useGlobalConfigPersistStore.getState().currentUser.uid;
      const teamsList = await fetch(GET_ALL_TEAMS(userId), {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const teamsData = await teamsList.json();
      useTicketMetaStore.getState().setAllTeams([...teamsData]);
      setFormData((prev) => ({
        ...prev,
        visibility: pendingVisibility,
      }));
      setError(null);
    } catch (e) {
      console.error(`Error updating visibility:`, e);
      setError(
        e instanceof Error
          ? e.message
          : `Failed to update visibility. Please try again later.`,
      );
    } finally {
      setIsUpdating((prev) => ({ ...prev, visibility: false }));
      setShowVisibilityModal(false);
    }
  };

  const hasIconChanges = () => {
    return (
      formData.icon !== originalData.icon ||
      formData.iconColor !== originalData.iconColor
    );
  };

  const IconComponent = ({
    name,
    showColor = true,
  }: {
    name: IconName;
    showColor?: boolean;
  }) => {
    const iconName = name || "RocketLaunchIcon";
    const Icon = HeroIcons[iconName];
    return (
      <Icon
        className={cn("!w-5 !h-5")}
        color={showColor ? formData.iconColor : "currentColor"}
      />
    );
  };

  if (error) {
    return (
      <div className="p-6">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-y-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="flex-1 bg-background py-8 flex items-center justify-center w-[640px]">
        <div className="w-full">
          <div className="mb-6">
            <h2 className="text-2xl font-medium">General</h2>
            <p className="text-sm text-gray-500 mt-0.5">
              Manage your team&apos;s general settings and preferences.
            </p>
          </div>

          <form className="space-y-8 mt-4">
            <div className="space-y-4">
              <div className="w-full">
                <Label
                  htmlFor="team-name"
                  className="text-sm font-medium flex items-center gap-1"
                >
                  Team icon and name
                  <span className="text-red-500">*</span>
                </Label>
                <div className="flex flex-col gap-2 mt-2">
                  <div className="flex gap-2 w-full">
                    <div className="flex gap-2 flex-1">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            className="w-[36px] h-[36px] p-0 flex-shrink-0 hover:bg-color-bg-subtle"
                            aria-label="Select icon"
                            style={{
                              borderRadius: "4px",
                              backgroundColor: formData.iconColor
                                ? formData.iconColor
                                    .replace("rgb", "rgba")
                                    .replace(")", ", 0.2)")
                                : ICON_COLORS.purple
                                    .replace("rgb", "rgba")
                                    .replace(")", ", 0.2)"),
                            }}
                          >
                            <IconComponent name={formData.icon as IconName} />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[320px] p-3 h-[400px] overflow-y-auto">
                          <div className="space-y-3 h-full">
                            <div>
                              <div className="flex flex-wrap gap-2 mb-3">
                                {Object.keys(ICON_COLORS).map((color) => (
                                  <Button
                                    key={color}
                                    style={{
                                      backgroundColor:
                                        ICON_COLORS[color as IconColor],
                                    }}
                                    className={cn(
                                      "h-[20px] w-[20px] p-0",
                                      formData.iconColor === color &&
                                        "border-primary",
                                    )}
                                    onClick={() => {
                                      // @ts-expect-error fix types
                                      setFormData((prev) => ({
                                        ...prev,
                                        iconColor:
                                          ICON_COLORS[color as IconColor],
                                      }));
                                      setHasChanges((prev) => ({
                                        ...prev,
                                        name: true,
                                      }));
                                    }}
                                  >
                                    <div
                                      className={cn(
                                        "w-4 h-4 rounded-full",
                                        ICON_COLORS[color as IconColor],
                                      )}
                                    />
                                  </Button>
                                ))}
                              </div>
                              <Separator className="my-1" />
                              <Input
                                placeholder="Search icons..."
                                value={iconSearch}
                                onChange={(e) => setIconSearch(e.target.value)}
                                className="h-7 my-3"
                              />
                              <div className="grid grid-cols-8 gap-2 mt-3">
                                {filteredIcons.map((iconName) => (
                                  <TooltipWrapper
                                    key={iconName}
                                    tooltipContent={kebabCase(iconName)}
                                    asChild
                                  >
                                    <Button
                                      key={iconName}
                                      variant="ghost"
                                      className="h-[36px] w-[36px] p-0"
                                      onClick={() => {
                                        setFormData((prev) => ({
                                          ...prev,
                                          icon: iconName,
                                        }));
                                        setHasChanges((prev) => ({
                                          ...prev,
                                          name: true,
                                        }));
                                      }}
                                    >
                                      <IconComponent
                                        name={iconName as IconName}
                                        showColor={false}
                                      />
                                    </Button>
                                  </TooltipWrapper>
                                ))}
                              </div>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                      <Input
                        id="team-name"
                        placeholder="E.g - Customer support"
                        value={formData.name}
                        onChange={(e) => {
                          const newName = e.target.value;
                          setFormData((prev) => ({
                            ...prev,
                            name: newName,
                          }));
                          setHasChanges((prev) => ({
                            ...prev,
                            name: true,
                          }));
                          setErrors((prev) => ({ ...prev, name: "" }));
                        }}
                        className={errors.name ? "border-red-500" : ""}
                        disabled={isUpdating.name}
                        required
                        aria-invalid={!!errors.name}
                        aria-describedby={
                          errors.name ? "name-error" : undefined
                        }
                      />
                    </div>
                    {(formData.name !== originalData.name ||
                      hasIconChanges()) && (
                      <Button
                        onClick={() => {
                          handleUpdateField("name", formData.name);
                          setHasChanges((prev) => ({ ...prev, name: false }));
                        }}
                        disabled={isUpdating.name}
                      >
                        {isUpdating.name && (
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        )}
                        {isUpdating.name ? "Updating..." : "Update"}
                      </Button>
                    )}
                  </div>
                  {errors.name && (
                    <p id="name-error" className="text-sm text-red-500">
                      {errors.name}.
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="identifier" className="text-sm font-medium">
                  Identifier <span className="text-red-500">*</span>
                </Label>
                <p className="text-sm text-gray-500 mb-2">
                  Create a unique ID to reference tickets for this team.
                </p>
                <div
                  className={cn(
                    "flex gap-2",
                    errors.identifier ? "flex-col" : "",
                  )}
                >
                  <Input
                    id="identifier"
                    maxLength={7}
                    placeholder="E.g - SUPPORT"
                    value={formData.identifier}
                    onChange={(e) => {
                      const upperValue = e.target.value.toUpperCase();
                      setFormData((prev) => ({
                        ...prev,
                        identifier: upperValue,
                      }));
                      setHasChanges((prev) => ({
                        ...prev,
                        identifier: upperValue !== originalData.identifier,
                      }));
                      setErrors((prev) => ({ ...prev, identifier: "" }));
                    }}
                    className={errors.identifier ? "border-red-500" : ""}
                    aria-invalid={!!errors.identifier}
                    aria-describedby={
                      errors.identifier ? "identifier-error" : undefined
                    }
                  />
                  {errors.identifier && (
                    <p
                      id="identifier-error"
                      className="text-sm text-red-500 mt-1"
                    >
                      {errors.identifier}.
                    </p>
                  )}
                  {hasChanges.identifier && (
                    <Button
                      onClick={() => {
                        handleUpdateField("identifier", formData.identifier);
                        setHasChanges((prev) => ({
                          ...prev,
                          identifier: false,
                        }));
                      }}
                      disabled={isUpdating.identifier}
                    >
                      {isUpdating.identifier && (
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      )}
                      {isUpdating.identifier ? "Updating..." : "Update"}
                    </Button>
                  )}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">
                  Set team visibility
                </Label>
                <div className="text-sm text-gray-500 mb-4">
                  <ul className="list-disc ml-4">
                    <li>
                      Public: Accessible to everyone in your organization.
                    </li>
                    <li>Private: Only accessible to invited members.</li>
                  </ul>
                </div>
                <RadioGroup
                  value={formData.visibility}
                  className="grid grid-cols-2 gap-4"
                  onValueChange={(value) => {
                    setPendingVisibility(value as "public" | "private");
                    setShowVisibilityModal(true);
                  }}
                  disabled={isUpdating.visibility}
                >
                  <Label
                    htmlFor="public"
                    className={cn(
                      "cursor-pointer flex items-center border rounded-sm px-4 py-3 flex-row-reverse justify-between hover:bg-accent transition-colors",
                      formData.visibility === "public" &&
                        "border border-primary",
                    )}
                  >
                    <RadioGroupItem
                      value="public"
                      id="public"
                      className="data-[state=checked]:border-primary"
                    />
                    <span className="font-normal flex items-center gap-2">
                      <Globe2 size={20} className="text-muted-foreground" />
                      Public
                    </span>
                  </Label>
                  <Label
                    htmlFor="private"
                    className={cn(
                      "cursor-pointer flex items-center border rounded-sm px-4 py-3 flex-row-reverse justify-between hover:bg-accent transition-colors",
                      formData.visibility === "private" &&
                        "border border-primary",
                    )}
                  >
                    <RadioGroupItem
                      value="private"
                      id="private"
                      className="data-[state=checked]:border-primary"
                    />
                    <span className="font-normal flex items-center gap-2">
                      <Lock size={20} className="text-muted-foreground" />
                      Private
                    </span>
                  </Label>
                </RadioGroup>
              </div>
            </div>

            <Separator />
            <div className="flex items-start justify-between">
              <div>
                <div className="text-sm font-medium">Delete team</div>
                <div className="text-sm text-color-text-muted">
                  Permanently delete this team and all related data.
                </div>
              </div>

              <div>
                <Dialog
                  open={showDeleteDialog}
                  onOpenChange={setShowDeleteDialog}
                >
                  <DialogTrigger asChild>
                    <Button
                      className="h-8"
                      style={{
                        backgroundColor: "var(--color-text-error)",
                      }}
                    >
                      Delete team
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Delete team</DialogTitle>
                      <DialogDescription>
                        You can undo this action for the next 30 days from the
                        &quot;Teams&quot; page
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <p className="text-sm text-muted-foreground mb-2">
                        Please type{" "}
                        <span className="font-semibold">{formData?.name}</span>{" "}
                        to confirm.
                      </p>
                      <Input
                        value={deleteConfirmation}
                        onChange={(e) => setDeleteConfirmation(e.target.value)}
                        placeholder="Enter team name"
                      />
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setShowDeleteDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        style={{
                          backgroundColor: "var(--color-text-error)",
                        }}
                        onClick={handleDelete}
                        disabled={
                          deleteConfirmation !== formData?.name || isDeleting
                        }
                      >
                        {isDeleting ? "Deleting..." : "Delete team"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </form>
          <Dialog
            open={showVisibilityModal}
            onOpenChange={(open) => {
              if (!open) {
                // Reset the radio group selection to the current formData value
                setFormData((prev) => ({
                  ...prev,
                  visibility: prev.visibility,
                }));
                setPendingVisibility(
                  formData.visibility as "public" | "private",
                );
                setShowVisibilityModal(false);
              }
            }}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Make this team {pendingVisibility}?</DialogTitle>
                <DialogDescription>
                  {pendingVisibility === "private"
                    ? "The team and its tickets will only be visible to members added to the team. These tickets will no longer be accessible to the rest of the organization."
                    : "The team and its tickets will be visible to everyone in the organization."}
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowVisibilityModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleVisibilityConfirm}
                  disabled={isUpdating.visibility}
                >
                  {isUpdating.visibility ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Updating...
                    </>
                  ) : (
                    `Make ${pendingVisibility}`
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
