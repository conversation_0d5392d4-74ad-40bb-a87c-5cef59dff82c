"use client";

import { MessageCircle } from "lucide-react";

export default function WebSettingsEmptyState() {
  return (
    <div className="space-y-6 p-6 max-w-[640px] mx-auto">
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="max-w-md w-full p-8">
          <div className="space-y-6 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-primary/5 animate-pulse rounded-[var(--radius-sm)] blur-xl" />
              <div className="relative mx-auto w-20 h-20 flex items-center justify-center bg-primary/10 rounded-[var(--radius-sm)]">
                <MessageCircle className="h-10 w-10 text-primary/80 transition-transform duration-200 hover:scale-110" />
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-semibold tracking-tight">
                Web chat is active on a different team
              </h3>
              <p className="text-muted-foreground">
                Web chat is currently enabled for one team. We&apos;re working
                on an upgrade that will let you activate it across multiple
                teams soon.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
