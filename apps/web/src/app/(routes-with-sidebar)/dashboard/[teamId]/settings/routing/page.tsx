"use client";

import { GroupLevelStrategyCard } from "@/components/group-level-strategy-card";
import { RoutingRules } from "@/components/routing-rules";
import Then<PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useApi } from "@/hooks/use-api";
import { ArrowRight, Info, MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import { toast } from "sonner";
import { RoutingConfig, RoutingRule, TeamGroup, TeamOption } from "./types";

type DialogMode = "create" | "edit";

export default function RoutingSettings({
  params,
}: {
  params: Promise<{ teamId: string }>;
}) {
  const resolvedParams = React.use(params);
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(true);
  const [isSaving, setIsSaving] = React.useState(false);
  const [routingConfig, setRoutingConfig] =
    React.useState<RoutingConfig | null>(null);
  const [routingRules, setRoutingRules] = React.useState<RoutingRule[]>([]);
  const [teams, setTeams] = React.useState<TeamOption[]>([]);
  const [selectedRule, setSelectedRule] = React.useState<RoutingRule | null>(
    null,
  );
  const [isRuleDialogOpen, setIsRuleDialogOpen] = React.useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [ruleToDelete, setRuleToDelete] = React.useState<RoutingRule | null>(
    null,
  );
  const [groups, setGroups] = React.useState<TeamGroup[]>([]);
  const [dialogMode, setDialogMode] = React.useState<DialogMode>("create");
  const { data, loading: isGroupsLoading } = useApi<TeamGroup[]>(
    `/v1/teams/${resolvedParams.teamId}/sub-teams`,
    {},
    { enabled: !!resolvedParams.teamId, isNextApi: false, method: "GET" },
  );

  React.useEffect(() => {
    if (data) {
      setGroups(data);
    }
  }, [data]);
  React.useEffect(() => {
    const fetchData = async () => {
      try {
        const [configResponse, rulesResponse, teamsResponse] =
          await Promise.all([
            fetch(`/api/teams/${resolvedParams.teamId}/configurations`),
            fetch(`/api/teams/${resolvedParams.teamId}/routing`),
            fetch(`/api/teams/${resolvedParams.teamId}/sub-teams`),
          ]);

        if (!configResponse.ok || !rulesResponse.ok || !teamsResponse.ok) {
          throw new Error("Failed to fetch routing data");
        }

        const configData = await configResponse.json();
        const rulesData = await rulesResponse.json();
        const teamsData = await teamsResponse.json();

        setRoutingConfig(configData.data);
        setRoutingRules(rulesData.data);
        setTeams(teamsData.data);
      } catch (error) {
        toast.error("Failed to load routing settings");
        console.error("Error loading routing settings:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [resolvedParams.teamId]);

  const handleUpdateConfig = async (updates: Partial<RoutingConfig>) => {
    setIsSaving(true);
    try {
      const response = await fetch(
        `/api/teams/${resolvedParams.teamId}/configurations`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updates),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to update routing settings");
      }

      const data = await response.json();
      setRoutingConfig(data.data);
      toast.success("Routing settings updated successfully");
    } catch (error) {
      toast.error("Failed to update routing settings");
      console.error("Error updating routing settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCreateRule = async (rule: Omit<RoutingRule, "id" | "teamId">) => {
    try {
      const response = await fetch(
        `/api/teams/${resolvedParams.teamId}/routing`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(rule),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to create routing rule");
      }

      const data = await response.json();
      setRoutingRules([...routingRules, data.data]);
      setIsRuleDialogOpen(false);
      toast.success("Routing rule created successfully");
    } catch (error) {
      toast.error("Failed to create routing rule");
      console.error("Error creating routing rule:", error);
    }
  };

  const handleUpdateRule = async (rule: Omit<RoutingRule, "id" | "teamId">) => {
    if (!selectedRule) return;

    try {
      const response = await fetch(
        `/api/teams/${resolvedParams.teamId}/routing/${selectedRule.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(rule),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to update routing rule");
      }

      const data = await response.json();
      setRoutingRules(
        routingRules.map((r) => (r.id === selectedRule.id ? data.data : r)),
      );
      setIsRuleDialogOpen(false);
      setSelectedRule(null);
      toast.success("Routing rule updated successfully");
    } catch (error) {
      toast.error("Failed to update routing rule");
      console.error("Error updating routing rule:", error);
    }
  };

  const handleDeleteRule = async () => {
    if (!ruleToDelete) return;

    try {
      const response = await fetch(
        `/api/teams/${resolvedParams.teamId}/routing/${ruleToDelete.id}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        throw new Error("Failed to delete routing rule");
      }

      setRoutingRules(routingRules.filter((r) => r.id !== ruleToDelete.id));
      setIsDeleteDialogOpen(false);
      setRuleToDelete(null);
      toast.success("Routing rule deleted successfully");
    } catch (error) {
      toast.error("Failed to delete routing rule");
      console.error("Error deleting routing rule:", error);
    }
  };

  if (isLoading || isGroupsLoading) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  if (!groups?.length) {
    return (
      <div className="space-y-6 pt-6 max-w-[640px] mx-auto">
        <div>
          <h2 className="text-2xl font-medium">Routing</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Control how tickets are automatically distributed within your team.
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="font-medium">Assignment logic</h3>
            <p className="text-sm text-muted-foreground mt-1 mb-2">
              Choose the strategy of assignment of tickets within your team.
            </p>
            <Select
              value={routingConfig?.userRoutingStrategy || "manual"}
              disabled={isSaving}
              onValueChange={(value) =>
                handleUpdateConfig({
                  userRoutingStrategy: value,
                })
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select strategy">
                  {routingConfig?.userRoutingStrategy === "manual"
                    ? "Manual"
                    : routingConfig?.userRoutingStrategy === "round_robin"
                    ? "Round robin"
                    : "Select strategy"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manual">Manual</SelectItem>
                <SelectItem value="round_robin">Round robin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {routingConfig &&
            routingConfig?.userRoutingStrategy &&
            routingConfig?.userRoutingStrategy !== "manual" && (
              <div>
                <h3 className="font-medium">Assignment factors</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Select the different user preferences to factor in during
                  assignment
                </p>
                <div className="space-y-2 mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="working-hours"
                      checked={routingConfig?.routingRespectsUserTimezone}
                      onCheckedChange={(checked) =>
                        handleUpdateConfig({
                          routingRespectsUserTimezone: checked as boolean,
                        })
                      }
                      disabled={isSaving}
                    />
                    <label
                      htmlFor="working-hours"
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Working hours
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="timezone"
                      checked={routingConfig?.routingRespectsTimezone}
                      onCheckedChange={(checked) =>
                        handleUpdateConfig({
                          routingRespectsTimezone: checked as boolean,
                        })
                      }
                      disabled={isSaving}
                    />
                    <label
                      htmlFor="timezone"
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Timezone
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="availability"
                      checked={routingConfig?.routingRespectsUserAvailability}
                      onCheckedChange={(checked) =>
                        handleUpdateConfig({
                          routingRespectsUserAvailability: checked as boolean,
                        })
                      }
                      disabled={isSaving}
                    />
                    <label
                      htmlFor="availability"
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Availability
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="capacity"
                      checked={routingConfig?.routingRespectsUserCapacity}
                      onCheckedChange={(checked) =>
                        handleUpdateConfig({
                          routingRespectsUserCapacity: checked as boolean,
                        })
                      }
                      disabled={isSaving}
                    />
                    <label
                      htmlFor="capacity"
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Capacity
                    </label>
                  </div>
                </div>
              </div>
            )}

          <div className="mt-4 border-t pt-4"></div>

          <div className="bg-blue-50 dark:bg-blue-950/30 p-[18px] rounded-md flex justify-between items-center">
            <div>
              <div className="text-blue-600 dark:text-blue-400 font-medium text-sm">
                Want more control over ticket routing?
              </div>
              <p className="text-blue-600 dark:text-blue-400 mt-1 text-xs">
                Create groups to enable specific routing rules based on ticket
                properties, skills, or regions. Groups help you ensure the right
                tickets reach the right team members.
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center ml-4 whitespace-nowrap text-sm font-normal text-primary hover:text-primary hover:bg-primary/10"
              onClick={() =>
                router.push(
                  `/dashboard/${resolvedParams.teamId}/settings/groups`,
                )
              }
            >
              Create groups <ArrowRight className="ml-1 h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 flex flex-col items-center justify-center h-full pt-6">
      <div className="space-t-6 w-[640px]">
        <h2 className="text-2xl font-medium">Routing</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Control how tickets are automatically distributed within your team.
        </p>
      </div>

      <div className="space-y-6 w-[640px]">
        <Card className="border-none shadow-none outline-none p-0">
          <div className="flex flex-row items-center justify-between !px-0">
            <div>
              <CardTitle>Routing rules</CardTitle>
              <CardDescription className="w-[430px] mt-1">
                Set conditions for tickets to be automatically assigned to
                groups. Tickets not matching any rules will require manual
                assignment.{" "}
              </CardDescription>
            </div>
            <Button
              onClick={() => {
                setDialogMode("create");
                setSelectedRule(null);
                setIsRuleDialogOpen(true);
              }}
            >
              Create rule
            </Button>
          </div>
          {isRuleDialogOpen && (
            <div className="mb-4">
              <RoutingRules
                key={selectedRule?.id || "create"}
                open={isRuleDialogOpen}
                onSave={dialogMode === "edit" ? handleUpdateRule : handleCreateRule}
                onClose={() => {
                  setIsRuleDialogOpen(false);
                  setSelectedRule(null);
                  setDialogMode("create");
                }}
                teams={teams}
                initialRule={selectedRule ? {
                  name: selectedRule.name,
                  resultTeamId: selectedRule.resultTeamId,
                  andRules: selectedRule.andRules,
                  orRules: selectedRule.orRules,
                  id: selectedRule.id,
                  teamId: selectedRule.teamId,
                } : undefined}
                mode={dialogMode}
                teamId={resolvedParams.teamId}
              />
            </div>
          )}
          <CardContent className="!px-0 py-4">
            <div className="space-y-4">
              {routingRules.map((rule) => (
                <div
                  key={rule.id}
                  className="flex items-center justify-between px-4 py-3 rounded-sm border bg-[var(--color-bg-subtle)]"
                >
                  <div>
                    <p className="font-medium text-sm ">{rule.name}</p>
                    <p className="text-muted-foreground text-xs">
                      Group:{" "}
                      {teams.find((t) => t.id === rule.resultTeamId)?.name}
                    </p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="hover:bg-transparent"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">More</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedRule(rule);
                          setDialogMode("edit");
                          setIsRuleDialogOpen(true);
                        }}
                      >
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          setRuleToDelete(rule);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          </CardContent>
          <div className="flex flex-row items-center justify-between !px-0">
            <div>
              <CardTitle>Default group</CardTitle>
              <CardDescription className="w-[430px] mt-1">
                Select a default group for tickets that don&apos;t match routing
                rules. This ensures automatic assignment.
              </CardDescription>
            </div>
            <Select
              value={routingConfig?.fallbackSubTeam}
              onValueChange={(value) =>
                handleUpdateConfig({
                  fallbackSubTeam: value || null,
                })
              }
            >
              <SelectTrigger id="defaultTeam">
                <SelectValue placeholder="No group" />
              </SelectTrigger>
              <SelectContent>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Separator className="mt-8" />
          <div className="mt-8">
            <CardTitle>Group-level strategy</CardTitle>
            <div className="flex flex-col gap-3 mt-4">
              {groups?.map((group) => (
                <GroupLevelStrategyCard
                  key={group.id}
                  group={group}
                  teamId={resolvedParams.teamId}
                />
              ))}
            </div>
          </div>
          <Separator className="my-8" />
          <div className="text-sm text-[var(--color-text-body)] bg-[var(--color-bg-warning-muted)] p-3 rounded-[6px]">
            <div className="flex items-start">
              <Info className="mr-2 text-color-warning" />
              <div className="flex flex-col">
                <div className="text-color-warning font-medium">
                  Manual assignment for non-group members
                </div>
                <div className="flex items-start justify-between">
                  <span className="text-color-warning font-normal text-xs">
                    Automatic assignment only works for group members. Create
                    more groups to include everyone.
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Routing Rule</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this routing rule? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <div className="flex justify-end gap-4">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeleteRule}>
                Delete
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
