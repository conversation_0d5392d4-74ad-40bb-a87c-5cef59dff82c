"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import {
  DragDropContext,
  Draggable,
  Droppable,
  type DropResult,
} from "@hello-pangea/dnd";
import {
  ChevronDown,
  ChevronUp,
  GripVertical,
  Info,
  Plus,
  Trash2,
} from "lucide-react";
import { CustomField, useFormBuilder } from "../hooks/use-form-builder";

export enum TargetFieldConditionsType {
  EQUALS = "equals",
  NOT_EQUALS = "not_equals",
  CONTAINS = "contains",
  NOT_CONTAINS = "not_contains",
  GREATER_THAN = "greater_than",
  GREATER_THAN_EQUAL = "greater_than_equal",
  LESS_THAN = "less_than",
  LESS_THAN_EQUAL = "less_than_equal",
  IS_EMPTY = "is_empty",
  IS_NOT_EMPTY = "is_not_empty",
}

export enum TargetFieldActionType {
  ADD_FIELD = "add_field",
  REMOVE_FIELD = "remove_field",
  MARK_MANDATORY = "mark_mandatory",
  MARK_NON_MANDATORY = "mark_non_mandatory",
  FILL_VALUE = "fill_value",
}

export interface TargetField {
  id: string;
  type: TargetFieldActionType;
  value: string | number | boolean | string[] | null;
  fieldId?: string;
}

export interface Condition {
  id: string;
  triggerFieldId: string;
  conditionType: TargetFieldConditionsType | "";
  value: string | number | boolean | string[] | null;
  targetFields: TargetField[];
  isExpanded: boolean;
}

interface ConditionCardProps {
  condition: Condition;
  onDelete: (id: string) => void;
  onUpdate: (id: string, updates: Partial<Omit<Condition, "id">>) => void;
}

interface SortableConditionCardProps extends ConditionCardProps {
  id: string;
  onToggleExpand: () => void;
  index: number;
}

function SortableConditionCard(props: SortableConditionCardProps) {
  const { selectedFields } = useFormBuilder();

  const getAvailableOperators = (
    fieldType: string,
  ): TargetFieldConditionsType[] => {
    const type = fieldType.toLowerCase();

    // Base operators available for all fields
    const baseOperators = [
      TargetFieldConditionsType.EQUALS,
      TargetFieldConditionsType.NOT_EQUALS,
      TargetFieldConditionsType.IS_EMPTY,
      TargetFieldConditionsType.IS_NOT_EMPTY,
    ];

    // Text-based fields
    if (
      [
        "single_line",
        "multi_line",
        "rich_text",
        "email",
        "phone_number",
        "url",
        "ip_address",
        "regex",
        "password",
        "address",
      ].includes(type)
    ) {
      return [
        ...baseOperators,
        TargetFieldConditionsType.CONTAINS,
        TargetFieldConditionsType.NOT_CONTAINS,
      ];
    }

    // Numeric fields
    if (
      ["integer", "decimal", "currency", "rating", "calculated"].includes(type)
    ) {
      return [
        ...baseOperators,
        TargetFieldConditionsType.GREATER_THAN,
        TargetFieldConditionsType.GREATER_THAN_EQUAL,
        TargetFieldConditionsType.LESS_THAN,
        TargetFieldConditionsType.LESS_THAN_EQUAL,
      ];
    }

    // Date/Time fields
    if (["date", "date_time", "time"].includes(type)) {
      return [
        ...baseOperators,
        TargetFieldConditionsType.GREATER_THAN,
        TargetFieldConditionsType.GREATER_THAN_EQUAL,
        TargetFieldConditionsType.LESS_THAN,
        TargetFieldConditionsType.LESS_THAN_EQUAL,
      ];
    }

    // Boolean fields
    if (["boolean", "toggle"].includes(type)) {
      return baseOperators;
    }

    // Choice fields
    if (
      ["single_choice", "multi_choice", "radio_button", "checkbox"].includes(
        type,
      )
    ) {
      return baseOperators;
    }

    // File fields
    if (["file_upload"].includes(type)) {
      return baseOperators;
    }

    // Special fields
    if (["lookup", "coordinates"].includes(type)) {
      return baseOperators;
    }

    // Default to base operators for any unhandled types
    return baseOperators;
  };

  const getConditionSummary = () => {
    if (!props.condition.triggerFieldId) return null;

    const field = selectedFields.find(
      (f) => f.id === props.condition.triggerFieldId,
    );
    if (!field) return null;

    const whenPart = `When "${field.name}"`;

    if (!props.condition.conditionType) return whenPart;

    const isPart = props.condition.conditionType.replace(/_/g, " ");

    if (props.condition.value === null) return `${whenPart} ${isPart}`;

    const valuePart = Array.isArray(props.condition.value)
      ? props.condition.value.join(", ")
      : props.condition.value.toString();

    const thenPart =
      props.condition.targetFields.length > 0
        ? `, then ${props.condition.targetFields
            .map((tf) => {
              const targetField = selectedFields.find(
                (f) => f.id === tf.fieldId,
              );
              return targetField
                ? `${getActionLabel(tf.type).toLowerCase()} "${
                    targetField.name
                  }"`
                : getActionLabel(tf.type).toLowerCase();
            })
            .join(" and ")}`
        : "";

    return `${whenPart} ${isPart} ${valuePart}${thenPart}`;
  };

  const getActionLabel = (actionType: TargetFieldActionType): string => {
    switch (actionType) {
      case TargetFieldActionType.ADD_FIELD:
        return "Add field";
      case TargetFieldActionType.REMOVE_FIELD:
        return "Remove field";
      case TargetFieldActionType.MARK_MANDATORY:
        return "Make mandatory";
      case TargetFieldActionType.MARK_NON_MANDATORY:
        return "Make optional";
      case TargetFieldActionType.FILL_VALUE:
        return "Fill value";
      default:
        return actionType;
    }
  };

  const renderValueInput = (condition: Condition) => {
    const field = selectedFields.find((f) => f.id === condition.triggerFieldId);
    if (!field) return null;

    const fieldWithOptions = field as CustomField;
    const hasOptions = "options" in fieldWithOptions;

    switch (field.fieldType.toLowerCase()) {
      case "single_choice":
        if (!hasOptions) return null;
        return (
          <Select
            value={condition.value?.toString() || ""}
            onValueChange={(value) => props.onUpdate(condition.id, { value })}
          >
            <SelectTrigger className="rounded-[4px]">
              <SelectValue placeholder="Select value" />
            </SelectTrigger>
            <SelectContent>
              {fieldWithOptions.options?.map((option) => (
                <SelectItem key={option.id} value={option.value}>
                  {option.value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "boolean":
      case "toggle":
        return (
          <div className="flex items-center space-x-2">
            <Switch
              checked={!!condition.value}
              onCheckedChange={(checked) =>
                props.onUpdate(condition.id, { value: checked })
              }
              className="rounded-[4px]"
            />
            <Label>{!!condition.value ? "Yes" : "No"}</Label>
          </div>
        );

      case "checkbox":
      case "multi_choice":
        if (!hasOptions) return null;
        return (
          <div className="space-y-2">
            {fieldWithOptions.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  checked={(condition.value as string[])?.includes(
                    option.value,
                  )}
                  onCheckedChange={(checked) => {
                    const currentValues = (condition.value as string[]) || [];
                    const newValues = checked
                      ? [...currentValues, option.value]
                      : currentValues.filter((v) => v !== option.value);
                    props.onUpdate(condition.id, { value: newValues });
                  }}
                  className="rounded-[4px]"
                />
                <Label>{option.value}</Label>
              </div>
            ))}
          </div>
        );

      default:
        return (
          <Input
            type={
              field.fieldType === "number" ||
              field.fieldType === "integer" ||
              field.fieldType === "decimal"
                ? "number"
                : "text"
            }
            value={condition.value?.toString() || ""}
            onChange={(e) =>
              props.onUpdate(condition.id, { value: e.target.value })
            }
            placeholder={`Enter value for ${field.name.toLowerCase()}`}
            className="rounded-[4px]"
          />
        );
    }
  };

  const renderTargetFieldInput = (targetField: TargetField) => {
    const renderFieldSelector = (placeholder: string) => (
      <Select
        value={targetField.fieldId || ""}
        onValueChange={(fieldId) => {
          props.onUpdate(props.condition.id, {
            targetFields: props.condition.targetFields.map((f) =>
              f.id === targetField.id ? { ...f, fieldId } : f,
            ),
          });
        }}
      >
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent position="popper" className="max-h-[300px] overflow-y-auto">
          {selectedFields.map((field) => (
            <SelectItem key={field.id} value={field.id}>
              {field.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );

    switch (targetField.type) {
      case TargetFieldActionType.ADD_FIELD:
      case TargetFieldActionType.REMOVE_FIELD:
      case TargetFieldActionType.MARK_MANDATORY:
      case TargetFieldActionType.MARK_NON_MANDATORY:
        return renderFieldSelector(
          `Select field to ${getActionLabel(targetField.type).toLowerCase()}`,
        );
      default:
        return null;
    }
  };

  return (
    <Draggable draggableId={props.id} index={props.index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={cn(
            "transition-all",
            snapshot.isDragging && "shadow-lg opacity-60",
          )}
        >
          <Card className={cn("p-4", snapshot.isDragging && "border-primary")}>
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div
                  {...provided.dragHandleProps}
                  className="h-8 w-8 flex items-center justify-center cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground"
                >
                  <GripVertical className="h-4 w-4" />
                </div>
                <div className="space-y-1">
                  <span className="text-sm font-medium">
                    #{props.condition.id}
                  </span>
                  {!props.condition.isExpanded && (
                    <p className="text-sm text-muted-foreground">
                      {getConditionSummary() || "Incomplete condition"}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => props.onToggleExpand()}
                  className="h-8 w-8 text-muted-foreground hover:text-foreground"
                >
                  {props.condition.isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => props.onDelete(props.condition.id)}
                  className="h-8 w-8 text-destructive hover:text-destructive/90 hover:bg-destructive/10 rounded-[4px]"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {props.condition.isExpanded && (
              <div className="space-y-6 pl-10 mt-4">
                <div className="grid gap-4">
                  <div className="flex items-center gap-4">
                    <Label className="text-muted-foreground text-xs uppercase min-w-[60px]">
                      When
                    </Label>
                    <Select
                      value={props.condition.triggerFieldId}
                      onValueChange={(fieldId) => {
                        props.onUpdate(props.condition.id, {
                          triggerFieldId: fieldId,
                          // Reset dependent fields when field changes
                          conditionType: "",
                          value: null,
                        });
                      }}
                    >
                      <SelectTrigger className="rounded-[4px]">
                        <SelectValue placeholder="Select a field" />
                      </SelectTrigger>
                      <SelectContent position="popper" className="max-h-[300px] overflow-y-auto">
                        {selectedFields.map((field) => (
                          <SelectItem key={field.id} value={field.id}>
                            {field.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {props.condition.triggerFieldId !== "" && (
                    <div className="flex items-center gap-4">
                      <Label className="text-muted-foreground text-xs uppercase min-w-[60px]">
                        Is
                      </Label>
                      <Select
                        value={props.condition.conditionType}
                        onValueChange={(type) => {
                          props.onUpdate(props.condition.id, {
                            conditionType: type as TargetFieldConditionsType,
                          });
                        }}
                      >
                        <SelectTrigger className="rounded-[4px]">
                          <SelectValue placeholder="Select condition type" />
                        </SelectTrigger>
                        <SelectContent position="popper" className="max-h-[300px] overflow-y-auto">
                          {(() => {
                            const field = selectedFields.find(
                              (f) => f.id === props.condition.triggerFieldId,
                            );
                            if (!field) return null;

                            const availableOperators = getAvailableOperators(
                              field.fieldType,
                            );

                            return Object.entries(TargetFieldConditionsType)
                              .filter(([_, value]) =>
                                availableOperators.includes(
                                  value as TargetFieldConditionsType,
                                ),
                              )
                              .map(([key, value]) => (
                                <SelectItem key={key} value={value}>
                                  {value.replace(/_/g, " ")}
                                </SelectItem>
                              ));
                          })()}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {props.condition.conditionType !== "" && (
                    <div className="flex items-center gap-4">
                      <Label className="text-muted-foreground text-xs uppercase min-w-[60px]">
                        Value
                      </Label>
                      <div className="flex-1">
                        {renderValueInput(props.condition)}
                      </div>
                    </div>
                  )}
                </div>

                {props.condition.conditionType !== "" &&
                  props.condition.value !== null && (
                    <div className="space-y-4 border-t pt-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-muted-foreground text-xs uppercase">
                          Then
                        </Label>
                        <Select
                          value=""
                          onValueChange={(value) => {
                            props.onUpdate(props.condition.id, {
                              targetFields: [
                                ...props.condition.targetFields,
                                {
                                  id: `${value}-${Date.now()}-${Math.random()
                                    .toString(36)
                                    .substring(2, 7)}`,
                                  type: value as TargetFieldActionType,
                                  value: null,
                                  fieldId: "",
                                },
                              ],
                            });
                          }}
                        >
                          <SelectTrigger className="w-[180px] rounded-[4px]">
                            <SelectValue placeholder="Select action" />
                          </SelectTrigger>
                          <SelectContent position="popper" className="max-h-[300px] overflow-y-auto">
                            {Object.values(TargetFieldActionType).map(
                              (actionType) => (
                                <SelectItem key={actionType} value={actionType}>
                                  {getActionLabel(actionType)}
                                </SelectItem>
                              ),
                            )}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-3">
                        {props.condition.targetFields.map((targetField) => (
                          <div
                            key={targetField.id}
                            className="flex items-start gap-3 bg-muted/30 p-3 rounded-[4px] border border-border/50"
                          >
                            <div className="flex-1 space-y-2">
                              <div className="text-xs font-medium text-muted-foreground uppercase">
                                {getActionLabel(targetField.type)}
                              </div>
                              {renderTargetFieldInput(targetField)}
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                props.onUpdate(props.condition.id, {
                                  targetFields:
                                    props.condition.targetFields.filter(
                                      (f) => f.id !== targetField.id,
                                    ),
                                });
                              }}
                              className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10 rounded-[4px]"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}

                        {props.condition.targetFields.length === 0 && (
                          <p className="text-sm text-muted-foreground text-center py-4 bg-muted/30 rounded-[4px] border border-border/50">
                            Select an action above to define what happens when
                            this condition is met
                          </p>
                        )}
                      </div>
                    </div>
                  )}
              </div>
            )}
          </Card>
        </div>
      )}
    </Draggable>
  );
}

export interface ConditionsSectionProps {
  className?: string;
  readOnly?: boolean;
}

export function ConditionsSection({
  className,
  readOnly,
}: ConditionsSectionProps) {
  const {
    conditions,
    conditionOrder,
    addCondition,
    deleteCondition,
    updateCondition,
    reorderConditions,
  } = useFormBuilder();

  const handleDragEnd = (result: DropResult) => {
    const { destination, source } = result;

    if (!destination) return;
    if (destination.index === source.index) return;

    reorderConditions(source.index, destination.index);
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="space-y-4">
        <div className="bg-muted/30 p-4 rounded-[4px] border border-border/50">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0" />
            <p className="text-sm text-muted-foreground">
              These conditions override the field settings and are evaluated in
              ascending order.
            </p>
          </div>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => addCondition()}
          disabled={readOnly}
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add condition
        </Button>
      </div>

      <div className="space-y-4">
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="conditions">
            {(provided, snapshot) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className={cn(
                  "space-y-2",
                  snapshot.isDraggingOver && "bg-muted/20 rounded-md p-2",
                )}
              >
                {conditionOrder.map((conditionId, index) => {
                  const condition = conditions.get(conditionId);
                  if (!condition) return null;
                  return (
                    <SortableConditionCard
                      key={conditionId}
                      id={conditionId}
                      index={index}
                      condition={condition}
                      onDelete={deleteCondition}
                      onUpdate={updateCondition}
                      onToggleExpand={() =>
                        updateCondition(conditionId, {
                          isExpanded: !condition.isExpanded,
                        })
                      }
                    />
                  );
                })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>
    </div>
  );
}
