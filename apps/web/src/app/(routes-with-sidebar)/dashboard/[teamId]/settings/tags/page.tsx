"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Check,
  MoreHorizontal,
  Pencil,
  Search,
  Tag as TagIcon,
  Trash,
  Loader2,
} from "lucide-react";
import React from "react";
import { useParams } from "next/navigation";
import { toast } from "sonner";
import Thena<PERSON>oader from "@/components/thena-loader";

interface Tag {
  id: string;
  uid: string;
  name: string;
  color: string;
}

interface ApiError {
  message: string;
}

interface TagFormProps {
  initialName?: string;
  initialColor?: string;
  onSubmit: (name: string, color: string) => Promise<void>;
  onCancel?: () => void;
  submitLabel?: string;
}

const tagColors = [
  "#FF6B6B", // Red
  "#4ECDC4", // Teal
  "#45B7D1", // Blue
  "#96CEB4", // Green
  "#FFEEAD", // Yellow
  "#D4A5A5", // Pink
  "#9933FF", // Purple
];

const TagForm = ({
  initialName = "",
  initialColor = tagColors[0],
  onSubmit,
  onCancel,
  submitLabel = "Add",
}: TagFormProps) => {
  const [name, setName] = React.useState(initialName);
  const [color, setColor] = React.useState(initialColor);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) {
      setError("Tag name cannot be empty");
      return;
    }
    setIsLoading(true);
    try {
      await onSubmit(name, color);
      setName("");
      setColor(tagColors[0]);
      setError(null);
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex items-center gap-4 w-full">
      <div className="flex items-center gap-2 flex-1">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              type="button"
              variant="outline"
              size="icon"
              className="h-9 w-9 flex-shrink-0"
              disabled={isLoading}
            >
              <div
                className="h-4 w-4 rounded-full"
                style={{ backgroundColor: color }}
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="p-3">
            <div className="flex gap-2">
              {tagColors.map((c) => (
                <button
                  key={c}
                  type="button"
                  className="h-[22px] w-[22px] rounded-full relative flex items-center justify-center hover:ring-2 hover:ring-offset-2 hover:ring-black/10 transition-all"
                  style={{ backgroundColor: c }}
                  onClick={() => setColor(c)}
                >
                  {color === c && (
                    <Check className="h-3.5 w-3.5 text-white stroke-[3]" />
                  )}
                </button>
              ))}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
        <div className="flex-1 min-w-0">
          <Label htmlFor="name" className="sr-only">
            Name
          </Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter tag name"
            className={error ? "border-red-500" : ""}
            disabled={isLoading}
          />
          {error && <span className="text-sm text-red-500 mt-1">{error}</span>}
        </div>
      </div>
      <div className="flex items-center gap-2 flex-shrink-0">
        {onCancel && (
          <Button
            type="button"
            variant="ghost"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            submitLabel
          )}
        </Button>
      </div>
    </form>
  );
};

const EmptyState = ({ onCreateClick }: { onCreateClick: () => void }) => (
  <div className="flex flex-col items-center justify-center py-12 text-center">
    <div className="rounded-full bg-gray-100 p-4 dark:bg-gray-800">
      <TagIcon className="h-8 w-8 text-gray-400" />
    </div>
    <h3 className="mt-4 text-lg font-semibold">No tags created</h3>
    <p className="mt-2 max-w-sm text-sm text-gray-500">
      Tags help you organize and categorize items. Create your first tag to get
      started.
    </p>
    <Button onClick={onCreateClick} className="mt-4">
      Create first tag
    </Button>
  </div>
);

export default function TagsSettings() {
  const params = useParams<{ teamId: string }>();
  const teamId = params.teamId;
  const [tags, setTags] = React.useState<Tag[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [showCreateForm, setShowCreateForm] = React.useState(false);
  const [editingTagId, setEditingTagId] = React.useState<string | null>(null);
  const [deletingTag, setDeletingTag] = React.useState<Tag | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);

  const fetchTags = React.useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/teams/${teamId}/tags`
      );
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to fetch tags");
      }
      const data = await response.json();
      const tagsArray = Array.isArray(data) ? data : Array.isArray(data.data) ? data.data : [];
      setTags(tagsArray);
      setError(null);
    } catch (error) {
      console.error("Error fetching tags:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch tags");
      setTags([]);
    } finally {
      setIsLoading(false);
    }
  }, [teamId]);

  const validateTagName = (name: string, currentTagId?: string): boolean => {
    const existingTag = tags.find(
      (tag) =>
        tag.name.toLowerCase() === name.toLowerCase() &&
        tag.uid !== currentTagId
    );
    return !existingTag;
  };

  const handleCreateTag = async (name: string, color: string) => {
    if (!validateTagName(name)) {
      toast.error("A tag with this name already exists");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/teams/${teamId}/tags`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ name, color }),
        }
      );

      if (!response.ok) {
        const error = (await response.json()) as ApiError;
        throw new Error(error.message || "Failed to create tag");
      }

      await fetchTags();
      toast.success("Tag created successfully");
      setShowCreateForm(false);
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to create tag");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditTag = async (tagId: string, name: string, color: string) => {
    if (!validateTagName(name, tagId)) {
      toast.error("A tag with this name already exists");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/teams/${teamId}/tags/${tagId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ name, color }),
        }
      );

      if (!response.ok) {
        const error = (await response.json()) as ApiError;
        throw new Error(error.message || "Failed to update tag");
      }

      await fetchTags();
      toast.success("Tag updated successfully");
      setEditingTagId(null);
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Failed to update tag");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClick = (tag: Tag) => {
    setDeletingTag(tag);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteCancel = () => {
    setDeletingTag(null);
    setIsDeleteDialogOpen(false);
  };

  const filteredTags = tags.filter((tag) =>
    tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleDeleteConfirm = async () => {
    if (!deletingTag) return;

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/teams/${teamId}/tags/${deletingTag.uid}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        const error = (await response.json()) as ApiError;
        throw new Error(error.message || "Failed to delete tag");
      }

      await fetchTags();
      toast.success("Tag deleted successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to delete tag");
    } finally {
      setIsLoading(false);
      setDeletingTag(null);
      setIsDeleteDialogOpen(false);
    }
  };

  React.useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  return (
    <div className="space-y-6 p-6 max-w-[688px] mx-auto">
      {isLoading && tags.length === 0 ? (
        <div className="h-full min-h-[calc(100vh-150px)] overflow-auto flex items-center justify-center">
          <ThenaLoader />
        </div>
      ) : (
        <>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-[24px] font-medium">Tags</h2>
              <p className="text-sm text-gray-500 mt-0.5">
                Organize and categorize tickets to simplify your workflow.
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1 max-w-[240px]">
              <Input
                type="text"
                placeholder="Search tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
              <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
            </div>
            {!isLoading && tags.length === 0 && !searchQuery ? null : (
              <Button onClick={() => setShowCreateForm(true)}>
                Add tag
              </Button>
            )}
          </div>

          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Delete Tag</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete the tag &quot;{deletingTag?.name}
                  &quot;? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  onClick={handleDeleteCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteConfirm}
                  disabled={isLoading}
                >
                  Delete
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <div className="space-y-6">
            {isLoading && tags.length > 0 ? (
              <div className="flex items-center justify-center py-8">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-primary"></div>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <p className="text-sm text-red-500">{error}</p>
                <Button onClick={fetchTags} variant="outline" className="mt-4">
                  Try Again
                </Button>
              </div>
            ) : filteredTags.length === 0 && !searchQuery ? (
              showCreateForm ? (
                <div className="rounded-sm border py-2 px-4 animate-in slide-in-from-top-2 fade-in duration-200">
                  <TagForm
                    onSubmit={handleCreateTag}
                    onCancel={() => setShowCreateForm(false)}
                  />
                </div>
              ) : (
                <EmptyState onCreateClick={() => setShowCreateForm(true)} />
              )
            ) : filteredTags.length === 0 && searchQuery ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <p className="text-sm text-gray-500">
                  No tags found matching &quot;{searchQuery}&quot;
                </p>
                <Button
                  variant="link"
                  onClick={() => setSearchQuery("")}
                  className="mt-2"
                >
                  Clear Search
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {showCreateForm && (
                  <div className="rounded-sm border py-2 px-4 animate-in slide-in-from-top-2 fade-in duration-200">
                    <TagForm
                      onSubmit={handleCreateTag}
                      onCancel={() => setShowCreateForm(false)}
                    />
                  </div>
                )}
                {filteredTags.map((tag) => (
                  <div
                    key={tag.id}
                    className="flex items-center justify-between rounded-sm border py-2 px-4 transition-all duration-200 hover:border-gray-400"
                  >
                    {editingTagId === tag.uid ? (
                      <div className="w-full animate-in fade-in zoom-in-95 duration-200">
                        <TagForm
                          initialName={tag.name}
                          initialColor={tag.color}
                          onSubmit={(name, color) => handleEditTag(tag.uid, name, color)}
                          onCancel={() => setEditingTagId(null)}
                          submitLabel="Save"
                        />
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center space-x-4">
                          <div
                            className="h-3.5 w-3.5 rounded-full transition-transform duration-200 hover:scale-110"
                            style={{ backgroundColor: tag.color }}
                          />
                          <span className="text-sm">{tag.name}</span>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setEditingTagId(tag.uid)} className="flex items-center gap-2">
                              <Pencil className="h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600 flex items-center gap-2"
                              onClick={() => handleDeleteClick(tag)}
                            >
                              <Trash className="h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
