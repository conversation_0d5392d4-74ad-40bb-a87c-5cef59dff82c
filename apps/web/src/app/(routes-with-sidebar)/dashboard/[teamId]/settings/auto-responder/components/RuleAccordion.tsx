"use client";

import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { MoreHorizontal, Pencil, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Badge } from "../../../../../../../components/ui/badge";

interface MessageContent {
  content: string;
  contentHtml: string;
}

interface RuleAccordionProps {
  rule: {
    id: string;
    name: string;
    trigger: string;
    considerations: string[];
    filters: {
      "~and": Record<string, unknown>[];
    };
    message: MessageContent;
    enabled: boolean;
    uniqueIdentifier: string;
  };
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onToggle: (id: string, enabled: boolean) => void;
}

export function RuleAccordion({
  rule,
  onEdit,
  onDelete,
  onToggle,
}: RuleAccordionProps) {
  const [isEnabled, setIsEnabled] = useState(rule.enabled);

  useEffect(() => {
    setIsEnabled(rule.enabled);
  }, [rule.enabled]);

  const handleToggle = (checked: boolean) => {
    setIsEnabled(checked);
    onToggle(rule.id, checked);
  };

  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem value={rule.id} className="border rounded-sm">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between px-4 py-2">
          <AccordionTrigger
            className="hover:no-underline flex-1 text-left pr-2"
            hideIcon
          >
            <div className="flex-1 w-full">
              <h3 className="text-base font-medium">{rule.name}</h3>
              <div className="flex flex-wrap gap-1 mt-2 w-full">
                <Badge
                  key={rule.trigger}
                  variant="outline"
                  className="bg-blue-100 border-transparent"
                >
                  {rule.trigger === "ticket_created" ? "Ticket created" : "Message received"}
                </Badge>
                {(rule.considerations as string[]).map((consideration) => (
                  <Badge
                    key={consideration}
                    variant="outline"
                    className="bg-slate-100 border-transparent"
                  >
                    {consideration === "nonWorkingHours" &&
                      "Team non-working hours"}
                    {consideration === "holidays" && "Team holidays"}
                    {consideration === "memberUnavailableTimeOff" &&
                      "Member time off"}
                    {consideration === "memberUnavailableWorkingHours" &&
                      "Member non-working hours"}
                    {consideration === "groupUnavailableWorkingHours" &&
                      "Group non-working hours"}
                    {consideration === "groupUnavailableHolidays" &&
                      "Group holidays"}
                  </Badge>
                ))}
              </div>
            </div>
          </AccordionTrigger>
          <div className="flex items-center gap-3 mt-2 sm:mt-0 pl-6 sm:pl-0">
            <Switch
              checked={isEnabled}
              onCheckedChange={handleToggle}
              onClick={(e) => e.stopPropagation()}
            />
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger
                asChild
                onClick={(e) => e.stopPropagation()}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(rule.id);
                  }}
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit rule
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(rule.id);
                  }}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete rule
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </AccordionItem>
    </Accordion>
  );
}
