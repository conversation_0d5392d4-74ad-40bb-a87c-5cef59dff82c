"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format } from "date-fns";
import { Activity, Eye, Search, Trash2, Workflow } from "lucide-react";
import Link from "next/link";
import { useParams, useSearchParams } from "next/navigation";
import React from "react";
import { toast } from "sonner";

interface TriggerEventSchema {
  type: string;
  properties: Record<
    string,
    {
      type: string;
      description?: string;
      format?: string;
      enum?: string[];
    }
  >;
  required?: string[];
}

interface TriggerEvent {
  uid: string;
  description: string;
  eventName: string;
  source: string;
  schema: TriggerEventSchema;
  metadata: Record<string, unknown>;
}

interface WorkflowFilter {
  field: string;
  operator:
    | "equals"
    | "not_equals"
    | "contains"
    | "not_contains"
    | "in"
    | "not_in";
  value: string | number | boolean | Array<string | number | boolean>;
}

interface WorkflowStep {
  id: string;
  type: "trigger" | "activity";
  position: { x: number; y: number };
  data: Record<string, unknown>;
}

interface Workflow {
  uid: string;
  uniqueIdentifier: string;
  name: string;
  version: number;
  triggerEvent: TriggerEvent;
  filters: WorkflowFilter[];
  workflowDefinition: WorkflowStep[];
  executingAgent: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  metadata: Record<string, unknown>;
}

const EmptyState = () => (
  <div className="text-center py-12">
    <h3 className="mt-2 text-lg font-semibold">No workflows found</h3>
    <p className="mt-1 text-sm text-gray-500">
      Get started by creating a new workflow.
    </p>
  </div>
);

const formatDate = (date: string) => {
  return format(new Date(date), "MMM d, yyyy h:mm a");
};

export default function WorkflowsSettings() {
  const params = useParams();
  const teamId = params.teamId as string;
  const searchParams = useSearchParams();
  const [workflows, setWorkflows] = React.useState<Workflow[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [workflowToDelete, setWorkflowToDelete] =
    React.useState<Workflow | null>(null);

  const fetchWorkflows = React.useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/teams/${teamId}/workflows`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to fetch workflows");
      }

      setWorkflows(result.data || []);
    } catch (error) {
      console.error("Error fetching workflows:", error);
      toast.error("Failed to fetch workflows");
    } finally {
      setLoading(false);
    }
  }, [teamId]);

  const toggleWorkflowStatus = React.useCallback(
    async (workflow: Workflow, newStatus: boolean) => {
      try {
        const response = await fetch(
          `/api/teams/${teamId}/workflows/${workflow.uniqueIdentifier}/toggle`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ isActive: newStatus }),
          },
        );

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || "Failed to toggle workflow status");
        }

        // Update local state
        setWorkflows((prevWorkflows) =>
          prevWorkflows.map((w) =>
            w.uniqueIdentifier === workflow.uniqueIdentifier
              ? { ...w, isActive: newStatus }
              : w,
          ),
        );

        toast.success(`Workflow ${newStatus ? "activated" : "deactivated"}`);
      } catch (error) {
        console.error("Error toggling workflow status:", error);
        toast.error("Failed to toggle workflow status");
      }
    },
    [teamId],
  );

  const deleteWorkflow = React.useCallback(
    async (workflow: Workflow) => {
      try {
        const response = await fetch(
          `/api/teams/${teamId}/workflows/${workflow.uniqueIdentifier}/delete`,
          {
            method: "DELETE",
          },
        );

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || "Failed to delete workflow");
        }

        // Update local state
        setWorkflows((prevWorkflows) =>
          prevWorkflows.filter(
            (w) => w.uniqueIdentifier !== workflow.uniqueIdentifier,
          ),
        );

        toast.success("Workflow deleted successfully");
        setWorkflowToDelete(null);
      } catch (error) {
        console.error("Error deleting workflow:", error);
        toast.error("Failed to delete workflow");
      }
    },
    [teamId],
  );

  // Extract the refresh param to a separate variable for dependency tracking
  const refreshParam = searchParams.get("refresh");
  
  React.useEffect(() => {
    fetchWorkflows();
  }, [fetchWorkflows, refreshParam]);

  const filteredWorkflows = React.useMemo(() => {
    if (!searchQuery) return workflows;
    const query = searchQuery.toLowerCase();
    return workflows.filter(
      (workflow) =>
        workflow.name.toLowerCase().includes(query) ||
        workflow.triggerEvent.eventName.toLowerCase().includes(query),
    );
  }, [workflows, searchQuery]);

  if (loading) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-65px)]">
      {/* Workflow list */}
      <div className="flex-1">
        {workflows.length === 0 ? (
          <EmptyState />
        ) : (
          <>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
              <Input
                placeholder="Search workflows..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 rounded-none border-none bg-transparent h-12"
              />
            </div>

            <Card className="rounded-none border-b-0 border-l-0 border-r-0 border-t">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Trigger event</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created at</TableHead>
                    <TableHead>Last updated</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredWorkflows.map((workflow) => (
                    <TableRow key={workflow.uid}>
                      <TableCell className="font-medium">
                        {workflow.name}
                      </TableCell>
                      <TableCell>
                        {workflow.triggerEvent.eventName || "No trigger"}
                      </TableCell>
                      <TableCell>
                        <Switch
                          checked={workflow.isActive}
                          onCheckedChange={(checked) =>
                            toggleWorkflowStatus(workflow, checked)
                          }
                          aria-label="Toggle workflow status"
                        />
                      </TableCell>
                      <TableCell>{formatDate(workflow.createdAt)}</TableCell>
                      <TableCell>{formatDate(workflow.updatedAt)}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Link
                            href={{
                              pathname: `/dashboard/${teamId}/settings/workflows/${workflow.uniqueIdentifier}`,
                              query: { workflowName: workflow.name },
                            }}
                          >
                            <Button
                              variant="ghost"
                              size="icon"
                              title="Edit workflow"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link
                            href={{
                              pathname: `/dashboard/${teamId}/settings/workflows/${workflow.uniqueIdentifier}/executions`,
                              query: { workflowName: workflow.name },
                            }}
                          >
                            <Button
                              variant="ghost"
                              size="icon"
                              title="View executions"
                            >
                              <Activity className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="icon"
                            title="Delete workflow"
                            onClick={(e) => {
                              e.preventDefault();
                              setWorkflowToDelete(workflow);
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          </>
        )}

        {/* Delete Confirmation Modal */}
        <Dialog
          open={!!workflowToDelete}
          onOpenChange={(open) => !open && setWorkflowToDelete(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Workflow</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete the workflow &quot;
                {workflowToDelete?.name}&quot;? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setWorkflowToDelete(null)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() =>
                  workflowToDelete && deleteWorkflow(workflowToDelete)
                }
              >
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
