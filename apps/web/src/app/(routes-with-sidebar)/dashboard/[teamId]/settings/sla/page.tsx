"use client";

import Then<PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from "@hello-pangea/dnd";
import {
  ChevronDown,
  ChevronUp,
  GripVertical,
  MoreHorizontal,
  Pen,
  Trash2,
} from "lucide-react";
import { Route } from "next";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";
import {
  useDeleteSLAPolicy,
  useFetchSLAPolicies,
  useUpdatePriorities,
} from "./services/use-sla-policies";

export default function SLAListingPage() {
  const { teamId } = useParams();
  const {
    data: policies,
    loading,
    refetch,
  } = useFetchSLAPolicies(teamId as string, true);
  const [searchQuery, setSearchQuery] = useState("");
  const [isReordering, setIsReordering] = useState(false);
  const [orderedPolicies, setOrderedPolicies] = useState<typeof policies>([]);
  const updatePriorities = useUpdatePriorities();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletePolicyId, setDeletePolicyId] = useState<string | null>(null);
  const deleteSLAPolicy = useDeleteSLAPolicy();
  const [isDeleting, setIsDeleting] = useState(false);
  const [sortConfig, setSortConfig] = useState<{
    key: "name" | "isActive";
    direction: "ascending" | "descending";
  } | null>(null);

  // Sort the policies based on the sort configuration
  const sortedPolicies = useMemo(() => {
    let sortableItems = Array.isArray(policies) ? [...policies] : [];

    // First sort by priority (default sort)
    sortableItems = sortableItems.sort((a, b) => a.priority - b.priority);

    // Then apply user-selected sort if any
    if (sortConfig !== null) {
      sortableItems.sort((a, b) => {
        if (sortConfig.key === "name") {
          if (a.name < b.name) {
            return sortConfig.direction === "ascending" ? -1 : 1;
          }
          if (a.name > b.name) {
            return sortConfig.direction === "ascending" ? 1 : -1;
          }
          return 0;
        }

        if (sortConfig.key === "isActive") {
          if (a.isActive === b.isActive) return 0;
          if (sortConfig.direction === "ascending") {
            return a.isActive ? -1 : 1;
          } else {
            return a.isActive ? 1 : -1;
          }
        }

        return 0;
      });
    }

    return sortableItems;
  }, [policies, sortConfig]);

  const requestSort = (key: "name" | "isActive") => {
    if (isReordering) return; // Don't allow sorting during reordering

    let direction: "ascending" | "descending" = "ascending";

    if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === "ascending"
    ) {
      direction = "descending";
    } else if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      // If already descending, clear the sort
      setSortConfig(null);
      return;
    }

    setSortConfig({ key, direction });
  };

  const getSortIndicator = (key: "name" | "isActive") => {
    if (!sortConfig || sortConfig.key !== key) {
      return null;
    }

    return sortConfig.direction === "ascending" ? (
      <ChevronUp className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDown className="h-4 w-4 ml-1" />
    );
  };

  const filteredPolicies = sortedPolicies.filter((policy) =>
    policy.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleStartReordering = useCallback(() => {
    // Create a deep copy to avoid reference issues
    setOrderedPolicies([...filteredPolicies]);
    setIsReordering(true);
  }, [filteredPolicies]);

  const handleDragEnd = useCallback(
    (result: DropResult) => {
      if (!result.destination) return;

      const items = Array.from(orderedPolicies || []);
      const [reorderedItem] = items.splice(result.source.index, 1);
      items.splice(result.destination.index, 0, reorderedItem);

      // Update priorities based on new order
      const updatedItems = items.map((item, index) => ({
        ...item,
        priority: index + 1,
      }));

      setOrderedPolicies(updatedItems);
    },
    [orderedPolicies],
  );

  const handleCancelReordering = useCallback(() => {
    setIsReordering(false);
    setOrderedPolicies([]);
  }, []);

  const handleSaveOrder = useCallback(async () => {
    try {
      if (!orderedPolicies?.length) return;

      const priorityUpdates = orderedPolicies.map((policy) => ({
        id: policy.uid,
        priority: policy.priority,
      }));

      await updatePriorities.mutate({
        teamId: teamId as string,
        entityType: "ticket",
        priorityUpdates,
      });

      // Refetch the policies
      await refetch();

      toast.success("Priority order updated successfully");
      handleCancelReordering();
    } catch (error) {
      console.error("Failed to update priorities:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update priorities",
      );
    }
  }, [
    orderedPolicies,
    teamId,
    updatePriorities,
    refetch,
    handleCancelReordering,
  ]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
    [],
  );

  const handleDeletePolicy = useCallback(async () => {
    if (!deletePolicyId) return;

    try {
      setIsDeleting(true);
      await deleteSLAPolicy.deleteSLAPolicy(deletePolicyId);

      // After successful deletion, close the dialog and refresh the policies
      setShowDeleteDialog(false);
      setDeletePolicyId(null);

      // Show success toast
      toast.success("SLA policy deleted successfully");

      // Refetch the policies to update the list
      await refetch();

      // Also update the local state for immediate UI feedback
      const updatedOrderedPolicies = orderedPolicies.filter(
        (policy) => policy.uid !== deletePolicyId,
      );
      setOrderedPolicies(updatedOrderedPolicies);
    } catch (error) {
      console.error("Error deleting SLA policy:", error);
      // Show error toast
      toast.error("Failed to delete SLA policy. Please try again.");
    } finally {
      setIsDeleting(false);
    }
  }, [deletePolicyId, deleteSLAPolicy, orderedPolicies, refetch]);

  if (loading) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="max-w-[640px] mx-auto pt-6">
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-medium">SLA policies</h1>
          <p className="text-sm text-muted-foreground">
            Configure your team&apos;s SLA policies.
          </p>
        </div>

        <div className="flex items-center justify-between gap-4">
          <div className="relative w-[280px]">
            <Input
              type="text"
              placeholder="Search policies"
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-8"
              disabled={isReordering}
            />
            <svg
              width="15"
              height="15"
              viewBox="0 0 15 15"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="absolute left-2.5 top-1/2 -translate-y-1/2 text-muted-foreground"
            >
              <path
                d="M10 6.5C10 8.433 8.433 10 6.5 10C4.567 10 3 8.433 3 6.5C3 4.567 4.567 3 6.5 3C8.433 3 10 4.567 10 6.5ZM9.30884 10.0159C8.53901 10.6318 7.56251 11 6.5 11C4.01472 11 2 8.98528 2 6.5C2 4.01472 4.01472 2 6.5 2C8.98528 2 11 4.01472 11 6.5C11 7.56251 10.6318 8.53901 10.0159 9.30884L12.8536 12.1464C13.0488 12.3417 13.0488 12.6583 12.8536 12.8536C12.6583 13.0488 12.3417 13.0488 12.1464 12.8536L9.30884 10.0159ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.26618 11.9026 7.38064 11.95 7.49999 11.95C7.61933 11.95 7.73379 11.9026 7.81819 11.8182L10.0682 9.56819ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.26618 11.9026 7.38064 11.95 7.49999 11.95C7.61933 11.95 7.73379 11.9026 7.81819 11.8182L10.0682 9.56819Z"
                fill="currentColor"
                fillRule="evenodd"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <Link href={`/dashboard/${teamId}/settings/sla/create`}>
            <Button
              className="bg-[var(--color-border-primary)]"
              disabled={isReordering}
            >
              Create new policy
            </Button>
          </Link>
        </div>

        <div>
          <div className="flex items-center justify-between mb-4">
            <div className="flex flex-col gap-1">
              <h2 className="font-medium">Organize your SLA policies</h2>
              <p className="text-sm text-muted-foreground">
                The order of your SLA policies determines the priority in which
                they are applied.
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleStartReordering}
              disabled={isReordering}
            >
              Re-order
            </Button>
          </div>

          <div className="rounded-sm border">
            <div className="grid grid-cols-[1fr,120px,48px] px-6 py-3 border-b">
              <div
                className="text-sm font-medium text-muted-foreground flex items-center gap-1 cursor-pointer"
                onClick={() => requestSort("name")}
              >
                Name
                <span className="ml-1">
                  {getSortIndicator("name") || (
                    <svg
                      width="15"
                      height="15"
                      viewBox="0 0 15 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-1"
                    >
                      <path
                        d="M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.26618 11.9026 7.38064 11.95 7.49999 11.95C7.61933 11.95 7.73379 11.9026 7.81819 11.8182L10.0682 9.56819Z"
                        fill="currentColor"
                        fillRule="evenodd"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </span>
              </div>
              <div
                className="text-sm font-medium text-muted-foreground flex items-center gap-1 cursor-pointer"
                onClick={() => requestSort("isActive")}
              >
                Status
                <span className="ml-1">
                  {getSortIndicator("isActive") || (
                    <svg
                      width="15"
                      height="15"
                      viewBox="0 0 15 15"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-1"
                    >
                      <path
                        d="M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.26618 11.9026 7.38064 11.95 7.49999 11.95C7.61933 11.95 7.73379 11.9026 7.81819 11.8182L10.0682 9.56819Z"
                        fill="currentColor"
                        fillRule="evenodd"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </span>
              </div>
              <div /> {/* Spacer for more actions */}
            </div>

            {!filteredPolicies?.length ? (
              <div className="px-6 py-4 text-center text-sm text-muted-foreground">
                No SLA policies found
              </div>
            ) : isReordering ? (
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="sla-policies">
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="divide-y"
                    >
                      {orderedPolicies?.map((policy, index) => (
                        <Draggable
                          key={policy.id}
                          draggableId={policy.id}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={`grid grid-cols-[1fr,120px,48px] items-center px-6 py-3 ${
                                snapshot.isDragging
                                  ? "bg-muted"
                                  : "hover:bg-muted/50"
                              }`}
                            >
                              <div className="flex items-center gap-2">
                                <div {...provided.dragHandleProps}>
                                  <GripVertical className="h-4 w-4 text-muted-foreground cursor-move -ml-2 mr-2" />
                                </div>
                                {policy.name}
                              </div>
                              <div>
                                <span
                                  className={`text-xs px-2 py-1 rounded-full ${
                                    policy.isActive
                                      ? "text-[var(--color-text-success)] bg-[var(--color-bg-success-muted)]"
                                      : "text-[var(--color-text-error)] bg-[var(--color-bg-error-muted)]"
                                  }`}
                                >
                                  {policy.isActive ? "Active" : "Inactive"}
                                </span>
                              </div>
                              <div className="flex justify-end">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-8 w-8"
                                    >
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem asChild>
                                      <Link
                                        href={
                                          `/dashboard/${teamId}/settings/sla/${policy.uid}` as Route
                                        }
                                        className="flex items-center"
                                      >
                                        <Pen className="h-4 w-4 mr-2" />
                                        Edit
                                      </Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      className="text-destructive focus:text-destructive"
                                      onClick={() => {
                                        setDeletePolicyId(policy.uid);
                                        setShowDeleteDialog(true);
                                      }}
                                    >
                                      <Trash2 className="h-4 w-4 mr-2" />
                                      Delete
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            ) : (
              <div className="divide-y">
                {filteredPolicies.map((policy) => (
                  <div
                    key={policy.id}
                    className="grid grid-cols-[1fr,120px,48px] items-center px-6 py-3 hover:bg-muted/50"
                  >
                    <div className="flex items-center gap-2">{policy.name}</div>
                    <div>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          policy.isActive
                            ? "text-[var(--color-text-success)] bg-[var(--color-bg-success-muted)]"
                            : "text-[var(--color-text-error)] bg-[var(--color-bg-error-muted)]"
                        }`}
                      >
                        {policy.isActive ? "Active" : "Inactive"}
                      </span>
                    </div>
                    <div className="flex justify-end">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link
                              href={
                                `/dashboard/${teamId}/settings/sla/${policy.uid}` as Route
                              }
                              className="flex items-center"
                            >
                              <Pen className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => {
                              setDeletePolicyId(policy.uid);
                              setShowDeleteDialog(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {isReordering && (
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={handleCancelReordering}>
                Cancel
              </Button>
              <Button
                className="bg-[var(--color-border-primary)] hover:bg-[var(--color-bg-dark-secondary)]"
                onClick={handleSaveOrder}
              >
                Save
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this SLA policy? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeletePolicy}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
