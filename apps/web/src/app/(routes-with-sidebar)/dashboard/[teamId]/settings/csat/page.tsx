"use client";

import * as React from "react";
import { Star } from "lucide-react";
import { useRouter } from "next/navigation";

export default function CSATSettings({
  params,
}: {
  params: Promise<{ teamId: string }>;
}) {
  const resolvedParams = React.use(params);
  const _router = useRouter();

  React.useEffect(() => {
    const fetchCSATSettings = async () => {
      try {
        const response = await fetch(
          `/api/teams/${resolvedParams.teamId}/csat-settings`,
        );
        await response.json();
        // setCSATSettings(data || null);
      } catch (error) {
        console.error("Error fetching CSAT settings:", error);
      }
    };

    fetchCSATSettings();
  }, [resolvedParams.teamId]);

  return (
    <div className="space-y-6 p-6 max-w-[640px] mx-auto">
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="max-w-md w-full p-8">
          <div className="space-y-6 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-primary/5 animate-pulse rounded-[var(--radius-sm)] blur-xl" />
              <div className="relative mx-auto w-20 h-20 flex items-center justify-center bg-primary/10 rounded-[var(--radius-sm)]">
                <Star className="h-10 w-10 text-primary/80 transition-transform duration-200 hover:scale-110" />
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-semibold tracking-tight">
                CSAT coming soon
              </h3>
              <p className="text-muted-foreground text-sm">
                We&apos;re working on customer satisfaction surveys to help you gather feedback and improve your service quality.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
