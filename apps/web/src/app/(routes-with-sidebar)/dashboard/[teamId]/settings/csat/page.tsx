"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { fetchCSATSettings, reorderCSATRules, updateCSATSettings } from "@/services/csat-service";
import { CSATRule } from "@/types/csat";
import { DragDropContext, Draggable, Droppable, DropResult } from "@hello-pangea/dnd";
import { useFlags } from "launchdarkly-react-client-sdk";
import { AlertCircle, GripVertical, Info, Plus, Star } from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import { toast } from "sonner";
import { CSATRuleCard } from "../../../../../../components/csat/csat-rule-card";

type CSATSettingsType = {
  rules: CSATRule[];
  cooldownPeriodDays?: number;
  emailConfigId?: string;
};

type EmailDataType = {
  id: string;
  email: string;
  domain?: string;
};

type DefaultEmailType = {
  id: string;
  email: string;
};

type EmailResponseItemType = {
  id?: string;
  uid?: string;
  domain?: string;
  customEmail?: string;
  isEmailForwardingVerified: boolean;
};

type DomainType = {
  isDnsVerified: boolean;
  domain: string;
};
function ComingSoon() {
  return (
    <div className="space-y-6 p-6 max-w-[640px] mx-auto">
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="max-w-md w-full p-8">
          <div className="space-y-6 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-primary/5 animate-pulse rounded-[var(--radius-sm)] blur-xl" />
              <div className="relative mx-auto w-20 h-20 flex items-center justify-center bg-primary/10 rounded-[var(--radius-sm)]">
                <Star className="h-10 w-10 text-primary/80 transition-transform duration-200 hover:scale-110" />
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-semibold tracking-tight">
                CSAT coming soon
              </h3>
              <p className="text-muted-foreground text-sm">
                We&apos;re working on customer satisfaction surveys to help you gather feedback and improve your service quality.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CSATSettings({
  params,
}: {
  params: Promise<{ teamId: string }>;
}) {
  const resolvedParams = React.use(params);
  const teamId = resolvedParams.teamId;
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(true);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [dataFetched, setDataFetched] = React.useState(false);

  const [settings, setSettings] = React.useState<CSATSettingsType>({
    rules: [],
    cooldownPeriodDays: undefined,
    emailConfigId: undefined
  });

  const [isReordering, setIsReordering] = React.useState(false);
  const [orderedRules, setOrderedRules] = React.useState<CSATRule[]>([]);

  const [defaultEmail, setDefaultEmail] = React.useState<DefaultEmailType | null>(null);

  const [isEditingSettings, setIsEditingSettings] = React.useState(false);
  const [cooldownPeriodDays, setCooldownPeriodDays] = React.useState<number | undefined>();

  const [domains, setDomains] = React.useState<string[]>([]);
  const [verifiedEmails, setVerifiedEmails] = React.useState<EmailDataType[]>([]);
  const [selectedDomain, setSelectedDomain] = React.useState("");
  const [selectedEmailId, setSelectedEmailId] = React.useState("");
  const [selectedEmailAddress, setSelectedEmailAddress] = React.useState("");
  const [allEmails, setAllEmails] = React.useState<EmailDataType[]>([]);
  const [useDefaultEmail, setUseDefaultEmail] = React.useState(true);

  const ldFlags = useFlags();
  const isCSATEnabled = ldFlags.csat || false;

  const fetchCSATSettingsData = React.useCallback(async (): Promise<CSATSettingsType | null> => {
    try {
      const fetchedSettings: CSATSettingsType = await fetchCSATSettings(teamId);

      setSettings(fetchedSettings);
      setCooldownPeriodDays(fetchedSettings.cooldownPeriodDays);

      return fetchedSettings;
    } catch {
      toast.error("Failed to load CSAT settings");
      return null;
    }
  }, [teamId]);

  const fetchDefaultEmail = React.useCallback(async (): Promise<DefaultEmailType | null> => {
    try {
      const response = await fetch(
        `/api/organization/teams/email-config/default-email?teamId=${teamId}`,
        {
          method: "POST",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch default email");
      }

      const data = await response.json() as DefaultEmailType;
      setDefaultEmail(data);
      return data;
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to fetch default email"
      );
      return null;
    }
  }, [teamId]);

  const fetchVerifiedDomains = React.useCallback(async (): Promise<string[]> => {
    try {
      const response = await fetch("/api/organization/domains/fetch");

      if (!response.ok) throw new Error("Failed to fetch domains");
      const data = await response.json() as DomainType[];

      const verifiedDomains = data
        .filter((domain) => domain.isDnsVerified)
        .map((domain) => domain.domain);

      setDomains(verifiedDomains);
      return verifiedDomains;
    } catch {
      toast.error("Failed to load verified domains");
      return [];
    }
  }, []);

  const fetchAllVerifiedEmails = React.useCallback(async (
    defaultEmailData: DefaultEmailType | null = null,
    fetchedSettings: CSATSettingsType | null = null
  ): Promise<EmailDataType[]> => {
    try {
      const response = await fetch(
        `/api/organization/teams/email-config/custom-email-config?teamId=${teamId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch custom email configurations");
      }

      const responseData = await response.json();
      const emailsArray: EmailResponseItemType[] = Array.isArray(responseData)
        ? responseData
        : responseData.data || [];

      const allVerifiedEmails: EmailDataType[] = emailsArray
        .filter((item) => item.isEmailForwardingVerified)
        .map((item) => {
          const domain = item.domain ||
            (item.customEmail ? item.customEmail.split('@')[1] : "");

          return {
            id: item.id || item.uid || "",
            email: item.customEmail || "",
            domain
          };
        });

      setAllEmails(allVerifiedEmails);

      const settingsToUse = fetchedSettings || settings;
      if (settingsToUse.emailConfigId) {
        const foundCustomEmail = allVerifiedEmails.find(email => email.id === settingsToUse.emailConfigId);

        if (foundCustomEmail) {
          setUseDefaultEmail(false);
          setSelectedEmailId(foundCustomEmail.id);
          setSelectedEmailAddress(foundCustomEmail.email);
          setSelectedDomain(foundCustomEmail.domain || "");
        } else if (defaultEmailData && settingsToUse.emailConfigId === defaultEmailData.id) {
          setUseDefaultEmail(true);
          setSelectedEmailId(defaultEmailData.id);
          setSelectedEmailAddress(defaultEmailData.email);
          setSelectedDomain("");
        }
      } else if (defaultEmailData) {
        setUseDefaultEmail(true);
        setSelectedEmailId(defaultEmailData.id);
        setSelectedEmailAddress(defaultEmailData.email);
        setSelectedDomain("");
      }

      return allVerifiedEmails;
    } catch {
      toast.error("Failed to load verified emails");
      return [];
    }
  }, [teamId, settings]);

  const handleStartEditingSettings = () => {
    setIsEditingSettings(true);
    setCooldownPeriodDays(settings.cooldownPeriodDays);

    if (settings.emailConfigId) {
      const foundCustomEmail = allEmails.find(email => email.id === settings.emailConfigId);

      if (foundCustomEmail) {
        setUseDefaultEmail(false);
        setSelectedEmailId(foundCustomEmail.id);
        setSelectedEmailAddress(foundCustomEmail.email);
        setSelectedDomain(foundCustomEmail.domain || "");
      } else if (defaultEmail && settings.emailConfigId === defaultEmail.id) {
        setUseDefaultEmail(true);
        setSelectedEmailId(defaultEmail.id);
        setSelectedEmailAddress(defaultEmail.email);
        setSelectedDomain("");
      }
    } else if (defaultEmail) {
      setUseDefaultEmail(true);
      setSelectedEmailId(defaultEmail.id);
      setSelectedEmailAddress(defaultEmail.email);
      setSelectedDomain("");
    }
  };

  const handleCancelEditingSettings = () => {
    setIsEditingSettings(false);
    setCooldownPeriodDays(settings.cooldownPeriodDays);

    if (settings.emailConfigId) {
      const foundCustomEmail = allEmails.find(email => email.id === settings.emailConfigId);

      if (foundCustomEmail) {
        setUseDefaultEmail(false);
        setSelectedEmailId(foundCustomEmail.id);
        setSelectedEmailAddress(foundCustomEmail.email);
        setSelectedDomain(foundCustomEmail.domain || "");
      } else if (defaultEmail && settings.emailConfigId === defaultEmail.id) {
        setUseDefaultEmail(true);
        setSelectedEmailId(defaultEmail.id);
        setSelectedEmailAddress(defaultEmail.email);
        setSelectedDomain("");
      }
    } else if (defaultEmail) {
      setUseDefaultEmail(true);
      setSelectedEmailId(defaultEmail.id);
      setSelectedEmailAddress(defaultEmail.email);
      setSelectedDomain("");
    }
  };

  const getEmailDisplayName = (): string => {
    if (settings.emailConfigId) {
      const foundCustomEmail = allEmails.find(email => email.id === settings.emailConfigId);
      if (foundCustomEmail) {
        return foundCustomEmail.email;
      }

      if (defaultEmail && settings.emailConfigId === defaultEmail.id) {
        return defaultEmail.email;
      }

      return "Unknown email";
    }

    return defaultEmail?.email || "No email selected";
  };

  const isDefaultEmailSelected = useDefaultEmail ||
    (!settings.emailConfigId && defaultEmail !== null) ||
    (defaultEmail && settings.emailConfigId === defaultEmail.id &&
      !allEmails.find(email => email.id === settings.emailConfigId));

  React.useEffect(() => {
    if (dataFetched) return;

    const initializeData = async () => {
      setIsLoading(true);
      try {
        const [fetchedSettings, defaultEmailData, _verifiedDomains] = await Promise.all([
          fetchCSATSettingsData(),
          fetchDefaultEmail(),
          fetchVerifiedDomains()
        ]);

        await fetchAllVerifiedEmails(defaultEmailData, fetchedSettings);

        setDataFetched(true);
      } catch {
        toast.error("Error initializing data");
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, [dataFetched, fetchCSATSettingsData, fetchDefaultEmail, fetchVerifiedDomains, fetchAllVerifiedEmails]);

  React.useEffect(() => {
    if (selectedDomain && allEmails.length > 0) {
      const filteredEmails = allEmails.filter(email => email.domain === selectedDomain);
      setVerifiedEmails(filteredEmails);

      if (isEditingSettings && selectedEmailId) {
        const emailFromThisDomain = filteredEmails.some(email => email.id === selectedEmailId);
        if (!emailFromThisDomain) {
          if (filteredEmails.length > 0) {
            setSelectedEmailId(filteredEmails[0].id);
            setSelectedEmailAddress(filteredEmails[0].email);
          } else {
            setSelectedEmailId("");
            setSelectedEmailAddress("");
          }
        }
      }
    } else {
      setVerifiedEmails([]);
    }
  }, [selectedDomain, allEmails, isEditingSettings, selectedEmailId]);

  React.useEffect(() => {
    if (!isReordering && settings.rules.length > 0) {
      setOrderedRules([...settings.rules]);
    }
  }, [settings.rules, isReordering]);

  const handleEditRule = (rule: CSATRule) => {
    const url = `/dashboard/${teamId}/settings/csat/rules/${rule.id}`;
    //@ts-expect-error - Next js router typig issue in this project
    router.push(url, { state: { rule } });
  };

  const handleCreateRule = () => {
    const url = `/dashboard/${teamId}/settings/csat/rules/new`;
    //@ts-expect-error - Next js router typig issue in this project
    router.push(url);
  };

  const handleStartReordering = React.useCallback(() => {
    setOrderedRules([...settings.rules]);
    setIsReordering(true);
  }, [settings.rules]);

  const handleDragEnd = React.useCallback(
    (result: DropResult) => {
      if (!result.destination) return;

      const items = Array.from(orderedRules);
      const [reorderedItem] = items.splice(result.source.index, 1);
      items.splice(result.destination.index, 0, reorderedItem);

      const updatedItems = items.map((item, index) => ({
        ...item,
        priority: items.length - index,
      }));

      setOrderedRules(updatedItems);
    },
    [orderedRules],
  );

  const handleCancelReordering = React.useCallback(() => {
    setOrderedRules([...settings.rules]);
    setIsReordering(false);
  }, [settings.rules]);

  const handleSaveOrder = React.useCallback(async () => {
    if (!orderedRules.length) return;

    setIsSubmitting(true);
    try {
      const priorityUpdates = orderedRules.map(rule => ({
        ruleId: rule.id,
        priority: rule.priority,
      }));

      await reorderCSATRules(teamId, priorityUpdates);

      setSettings(prev => ({
        ...prev,
        rules: orderedRules,
      }));

      toast.success("CSAT rules reordered successfully");
      setIsReordering(false);
    } catch {
      toast.error("Failed to reorder CSAT rules");
      setOrderedRules([...settings.rules]);
    } finally {
      setIsSubmitting(false);
    }
  }, [orderedRules, teamId, settings.rules]);

  const handleRuleUpdate = React.useCallback((updatedRule: CSATRule) => {
    setSettings(prev => ({
      ...prev,
      rules: prev.rules.map(rule =>
        rule.id === updatedRule.id ? updatedRule : rule
      ),
    }));
    toast.success("CSAT rule updated successfully");
  }, []);

  const handleRuleDelete = React.useCallback((deletedRuleId: string) => {
    setSettings(prev => ({
      ...prev,
      rules: prev.rules.filter(rule => rule.id !== deletedRuleId),
    }));
    toast.success("CSAT rule deleted successfully");
  }, []);

  const handleToggleDefaultEmail = (useDefault: boolean) => {
    setUseDefaultEmail(useDefault);

    if (useDefault && defaultEmail) {
      setSelectedDomain("");
      setSelectedEmailId(defaultEmail.id);
      setSelectedEmailAddress(defaultEmail.email);
    } else if (domains.length > 0) {
      const firstDomain = domains[0];
      setSelectedDomain(firstDomain);

      const domainEmails = allEmails.filter(email => email.domain === firstDomain);
      if (domainEmails.length > 0) {
        setSelectedEmailId(domainEmails[0].id);
        setSelectedEmailAddress(domainEmails[0].email);
      } else {
        setSelectedEmailId("");
        setSelectedEmailAddress("");
      }
    }
  };

  const handleSaveSettings = async () => {
    setIsSubmitting(true);
    try {
      let emailIdToSave: string | undefined;

      if (useDefaultEmail && defaultEmail) {
        emailIdToSave = defaultEmail.id;
      } else if (!useDefaultEmail && selectedEmailId) {
        emailIdToSave = selectedEmailId;
      } else {
        emailIdToSave = undefined;
      }

      await updateCSATSettings(teamId, cooldownPeriodDays, emailIdToSave);

      setSettings(prev => ({
        ...prev,
        cooldownPeriodDays,
        emailConfigId: emailIdToSave
      }));

      toast.success("CSAT settings updated successfully");
      setIsEditingSettings(false);
    } catch {
      toast.error("Failed to update CSAT settings");
      setCooldownPeriodDays(settings.cooldownPeriodDays);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDomainSelect = (domain: string) => {
    setSelectedDomain(domain);
    setUseDefaultEmail(false);

    const domainEmails = allEmails.filter(email => email.domain === domain);

    if (domainEmails.length > 0) {
      setSelectedEmailId(domainEmails[0].id);
      setSelectedEmailAddress(domainEmails[0].email);
    } else {
      setSelectedEmailId("");
      setSelectedEmailAddress("");
    }
  };

  const handleEmailSelect = (id: string, email: string) => {
    setSelectedEmailId(id);
    setSelectedEmailAddress(email);
    setUseDefaultEmail(false);
  };


  const isLoadingContent = (
    <div className="py-8 flex justify-center">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
    </div>
  );

  const noRulesContent = (
    <div className="flex flex-col items-center justify-center p-12 text-center border rounded-lg">
      <div className="rounded-full bg-muted p-3 mb-4">
        <Info className="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium mb-2">No CSAT rules found</h3>
      <p className="text-muted-foreground mb-6">
        Create your first rule to start collecting customer satisfaction feedback.
      </p>
      <Button
        onClick={handleCreateRule}
        className="flex items-center"
      >
        <Plus className="h-4 w-4 mr-2" />
        Create your first rule
      </Button>
    </div>
  );

  if (!isCSATEnabled) {
    return <ComingSoon />
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 max-w-[664px] mx-auto">
        <div>
          <h2 className="text-2xl font-semibold tracking-tight">CSAT</h2>
          <p className="text-muted-foreground">
            Configure customer satisfaction surveys for your team.
          </p>
        </div>
        <div className="flex gap-2">
          {settings.rules.length > 1 && !isReordering && (
            <Button variant="outline" onClick={handleStartReordering}>
              Reorder rules
            </Button>
          )}
          <Button onClick={handleCreateRule} disabled={isReordering}>
            <Plus className="mr-2 h-4 w-4" />
            Create new rule
          </Button>
        </div>
      </div>

      <div className="max-w-[664px] mx-auto mb-6 border rounded-lg p-4 bg-card">
        <div className="flex flex-col gap-4">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium">CSAT Settings</h3>
              <p className="text-sm text-muted-foreground">
                Configure cooldown period and notification email
              </p>
            </div>

            {isEditingSettings ? (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelEditingSettings}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveSettings}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Saving..." : "Save"}
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={handleStartEditingSettings}
                disabled={isReordering || isLoading}
              >
                Edit
              </Button>
            )}
          </div>

          {isLoading ? isLoadingContent : (
            <div className="space-y-4 mt-2">
              <div className="border-b pb-4">
                <h4 className="text-sm font-medium mb-2">Survey Cooldown Period</h4>
                {isEditingSettings ? (
                  <div className="flex items-center">
                    <input
                      type="number"
                      min="1"
                      max="365"
                      value={cooldownPeriodDays === undefined ? '' : cooldownPeriodDays}
                      onChange={(e) => setCooldownPeriodDays(e.target.value === '' ? undefined : parseInt(e.target.value))}
                      className="w-24 h-10 px-3 rounded-md border border-input bg-background text-sm"
                    />
                    <span className="ml-2 text-sm">days</span>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <span className="font-medium">{settings.cooldownPeriodDays !== undefined ? settings.cooldownPeriodDays : 'Not set'}</span>
                    {settings.cooldownPeriodDays !== undefined && <span className="ml-1 text-sm text-muted-foreground">days</span>}
                  </div>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Minimum days between surveys shown to the same customer
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Notification Email</h4>
                {isEditingSettings ? (
                  <div className="space-y-4">
                    {defaultEmail && (
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="use-default-email"
                          checked={useDefaultEmail}
                          onChange={(e) => handleToggleDefaultEmail(e.target.checked)}
                          className="mr-2 h-4 w-4"
                        />
                        <label htmlFor="use-default-email" className="text-sm">
                          Use default email ({defaultEmail.email})
                        </label>
                      </div>
                    )}

                    {!useDefaultEmail && (
                      <>
                        <div>
                          <label className="text-xs text-muted-foreground block mb-1">Domain</label>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" className="w-full md:w-auto">
                                {selectedDomain || "Select Domain"}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              {domains.length > 0 ? (
                                domains.map((domain) => (
                                  <DropdownMenuItem
                                    key={domain}
                                    onClick={() => handleDomainSelect(domain)}
                                  >
                                    {domain}
                                  </DropdownMenuItem>
                                ))
                              ) : (
                                <DropdownMenuItem disabled>
                                  No verified domains available
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        <div>
                          <label className="text-xs text-muted-foreground block mb-1">Email Address</label>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="outline"
                                disabled={!selectedDomain}
                                className="w-full md:w-auto"
                              >
                                {selectedEmailAddress || "Select Email"}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              {verifiedEmails.length > 0 ? (
                                verifiedEmails.map((item) => (
                                  <DropdownMenuItem
                                    key={item.id}
                                    onClick={() => handleEmailSelect(item.id, item.email)}
                                  >
                                    {item.email}
                                  </DropdownMenuItem>
                                ))
                              ) : (
                                <DropdownMenuItem disabled>
                                  {selectedDomain ? "No verified emails for this domain" : "Select a domain first"}
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {selectedDomain && verifiedEmails.length === 0 && (
                          <div className="flex items-start gap-2 text-amber-600">
                            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                            <p className="text-xs">
                              No verified emails found for {selectedDomain}. Please verify an email for this domain.
                            </p>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                ) : (
                  <div>
                    {isDefaultEmailSelected ? (
                      <div className="flex items-center">
                        <span className="font-medium">Using default email: </span>
                        <span className="ml-1">{defaultEmail?.email || "Loading..."}</span>
                      </div>
                    ) : (
                      <div className="font-medium">{getEmailDisplayName()}</div>
                    )}
                  </div>
                )}
                <p className="text-xs text-muted-foreground mt-2">
                  Email used to send CSAT surveys. If no custom email is set, the default email will be used.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="max-w-[664px] mx-auto">
        {isLoading ? (
          <div className="flex justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : settings.rules.length === 0 ? (
          noRulesContent
        ) : isReordering ? (
          <div className="space-y-4">
            <div className="flex flex-col gap-1 mb-4">
              <h3 className="font-medium">Reorder CSAT rules</h3>
              <p className="text-sm text-muted-foreground">
                Drag and drop to change the priority of your CSAT rules. Rules with higher priority will be applied first.
              </p>
            </div>

            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="csat-rules">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="space-y-2"
                  >
                    {orderedRules.map((rule, index) => (
                      <Draggable key={rule.id} draggableId={rule.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`border rounded-lg p-4 ${snapshot.isDragging ? "bg-muted shadow-md" : "bg-card"}`}
                          >
                            <div className="flex items-center gap-2">
                              <div {...provided.dragHandleProps} className="cursor-move">
                                <GripVertical className="h-5 w-5 text-muted-foreground" />
                              </div>
                              <div className="flex-1">
                                <h4 className="font-medium">{rule.name}</h4>
                                <p className="text-sm text-muted-foreground line-clamp-1">
                                  {rule.description || "No description"}
                                </p>
                              </div>
                              <div>
                                <span
                                  className={`text-xs px-2 py-1 rounded-full ${rule.isActive
                                    ? "text-[var(--color-text-success)] bg-[var(--color-bg-success-muted)]"
                                    : "text-[var(--color-text-error)] bg-[var(--color-bg-error-muted)]"}`}
                                >
                                  {rule.isActive ? "Active" : "Inactive"}
                                </span>
                              </div>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={handleCancelReordering}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveOrder}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Saving..." : "Save order"}
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {settings.rules.map((rule) => (
              <CSATRuleCard
                key={rule.id}
                rule={rule}
                teamId={teamId}
                onUpdate={handleRuleUpdate}
                onDelete={handleRuleDelete}
                onEdit={handleEditRule}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}