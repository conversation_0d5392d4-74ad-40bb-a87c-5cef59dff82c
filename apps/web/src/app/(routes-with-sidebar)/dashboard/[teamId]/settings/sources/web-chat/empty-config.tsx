import { ExternalLink, MessageCircle } from "lucide-react";
import { But<PERSON> } from "../../../../../../../components/ui/button";

export default function WebChatEmptyConfig() {
  return (
    <div className="space-y-6 p-6 max-w-[640px] mx-auto">
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="max-w-md w-full p-8">
          <div className="space-y-6 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-primary/5 animate-pulse rounded-[var(--radius-sm)] blur-xl" />
              <div className="relative mx-auto w-20 h-20 flex items-center justify-center bg-primary/10 rounded-[var(--radius-sm)]">
                <MessageCircle className="h-10 w-10 text-primary/80 transition-transform duration-200 hover:scale-110" />
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-semibold tracking-tight">
                Team is not configured for Web chat
              </h3>
              <p className="text-muted-foreground text-sm">
                Add this team to Web chat at the organization level to access
                team-specific settings.
              </p>
              <div className="pt-4 flex justify-center">
                <Button
                  variant="outline"
                  onClick={() =>
                    window.open(`/organization/settings/sources/web-chat`)
                  }
                  className="flex items-center gap-2"
                >
                  Go to organization settings
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
