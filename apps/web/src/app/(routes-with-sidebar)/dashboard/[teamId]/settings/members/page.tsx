"use client";

import { inviteUserByEmail } from "@/app/actions/auth";
import Then<PERSON><PERSON>oader from "@/components/thena-loader";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { getOrgDetails } from "../../../../../../utils/browserUtils";
import { MemberList } from "./_components/member-list";

interface Member {
  id: string;
  name: string;
  email: string;
  role: string;
  avatarUrl?: string;
  userType: string;
}

interface ApiError {
  message: string;
}

export default function TeamMembersSettings() {
  const params = useParams();
  const teamId = params.teamId as string;

  const [members, setMembers] = useState<Member[]>([]);
  const [orgMembers, setOrgMembers] = useState<Member[]>([]);
  const [inviteMember, setInviteMember] = useState("");
  const [loading, setLoading] = useState(false);
  const [isLoadingMembers, setIsLoadingMembers] = useState(true);
  const [isLoadingOrgMembers, setIsLoadingOrgMembers] = useState(true);
  const { orgUid, orgId } = getOrgDetails();
  const handleError = useCallback((error: Error | ApiError) => {
    toast.error(error.message);
  }, []);

  const fetchMembers = useCallback(async () => {
    try {
      setIsLoadingMembers(true);
      const response = await fetch(`/api/teams/${teamId}/members`);
      if (!response.ok) {
        throw new Error("Failed to fetch members");
      }
      const data = await response.json();
      setMembers(data.data || []);
    } catch (error) {
      handleError(error as Error);
      setMembers([]); // Set empty array on error
    } finally {
      setIsLoadingMembers(false);
    }
  }, [teamId, handleError]);

  const fetchOrgMembers = useCallback(async () => {
    try {
      setIsLoadingOrgMembers(true);
      const response = await fetch(`/api/users/list`);
      if (!response.ok) {
        throw new Error("Failed to fetch organization members");
      }
      const data = await response.json();

      setOrgMembers(data.data || []);
    } catch (error) {
      handleError(error as Error);
      setOrgMembers([]); // Set empty array on error
    } finally {
      setIsLoadingOrgMembers(false);
    }
  }, [handleError]);

  const handleInviteToOrg = useCallback(
    async (email: string) => {
      if (!email) return;
      setLoading(true);

      try {
        // First send the organization invite
        const response = await fetch(`/api/organizations/invite`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email,
          }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(
            error.message || "Failed to send organization invite",
          );
        }
        // Then invite the user using the server action
        const { error } = await inviteUserByEmail(
          email,
          "user",
          orgUid,
          orgId,
          teamId,
        );
        if (error) {
          throw new Error(
            typeof error === "string"
              ? error
              : error.message || "Failed to send invite",
          );
        }

        toast.success(`Successfully invited ${email} to the organization`);

        // Don't refresh member lists since they haven't joined yet
      } catch (error) {
        handleError(error as Error);
      } finally {
        setLoading(false);
      }
    },
    [teamId, handleError],
  );

  const handleAddToTeam = useCallback(
    async (memberId: string) => {
      try {
        const response = await fetch(`/api/teams/${teamId}/members`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            userId: memberId,
          }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || "Failed to add member to team");
        }

        toast.success("Successfully added member to team");

        // Refresh the team members list
        fetchMembers();
      } catch (error) {
        handleError(error as Error);
      }
    },
    [teamId, fetchMembers, handleError],
  );

  const handleRemoveMember = useCallback(
    async (memberId: string) => {
      try {
        const response = await fetch(
          `/api/teams/${teamId}/members/${memberId}`,
          {
            method: "DELETE",
          },
        );

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || "Failed to remove member");
        }

        toast.success("Member removed successfully");

        // Refresh the members list
        fetchMembers();
      } catch (error) {
        handleError(error as Error);
      }
    },
    [teamId, handleError, fetchMembers],
  );

  useEffect(() => {
    fetchMembers();
    fetchOrgMembers();
  }, [fetchMembers, fetchOrgMembers]);

  if (isLoadingMembers || isLoadingOrgMembers) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-y-auto overflow-x-hidden flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="h-full overflow-hidden">
      <div className="h-full px-6 pt-6 overflow-y-auto">
        <div className="max-w-[640px] mx-auto flex flex-col gap-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-medium">Team members</h2>
              <p className="text-sm text-gray-500">
                Manage your team members and their roles.
              </p>
            </div>
          </div>
          <div>
            <MemberList
              members={members}
              orgMembers={orgMembers}
              inviteMember={inviteMember}
              setInviteMember={setInviteMember}
              handleInviteToOrg={handleInviteToOrg}
              handleAddToTeam={handleAddToTeam}
              handleRemoveMember={handleRemoveMember}
              loading={loading}
              isLoadingOrgMembers={isLoadingOrgMembers}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
