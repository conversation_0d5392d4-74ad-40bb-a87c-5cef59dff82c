import { useEffect, useMemo, useState } from "react";

export interface AutoResponderRule {
  id: string;
  name: string;
  uniqueIdentifier: string;
  enabled: boolean;
}

interface ApiRule {
  uid: string;
  name: string;
  isActive: boolean;
  uniqueIdentifier: string;
  subType: string;
}

export const useFetchAutoResponderRules = (teamId: string, isAutoResponderPage: boolean) => {
  const [data, setData] = useState<AutoResponderRule[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!isAutoResponderPage || !teamId) {
      setData([]);
      return;
    }

    const controller = new AbortController();

    const fetchRules = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/teams/${teamId}/workflows`,
          { signal: controller.signal },
        );
        
        if (controller.signal.aborted) return;

        if (!response.ok) {
          throw new Error("Failed to fetch rules");
        }

        const responseData = await response.json();
        
        if (controller.signal.aborted) return;

        if (responseData.data && Array.isArray(responseData.data)) {
          // Filter and transform auto-responder rules
          const autoResponderRules = responseData.data
            .filter((rule: ApiRule) => rule.subType === "AUTO_RESPONDER")
            .map((rule: ApiRule) => ({
              id: rule.uid,
              name: rule.name,
              uniqueIdentifier: rule.uniqueIdentifier,
              enabled: rule.isActive,
            }));
          
          setData(autoResponderRules);
        }
      } catch (error) {
        if (controller.signal.aborted) return;
        
        console.error("Error fetching auto-responder rules:", error);
        setData([]);
      } finally {
        if (!controller.signal.aborted) {
          setLoading(false);
        }
      }
    };

    fetchRules();

    return () => controller.abort();
  }, [teamId, isAutoResponderPage]);

  return useMemo(() => ({ data, loading }), [data, loading]);
}; 