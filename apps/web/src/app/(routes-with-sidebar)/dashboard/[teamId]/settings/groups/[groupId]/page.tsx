"use client";

import { AssignmentStrategyCard } from "@/components/assignment-strategy-card";
import { BusinessHours } from "@/components/business-hours";
import CommonSelectWrapper from "@/components/common-select-wrapper";
import Avatar from "@/components/common/Avatar";
import Then<PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DEFAULT_CONFIG } from "@/constants/working-hours";
import { useApi } from "@/hooks/use-api";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { User } from "@/types/global";
import { DayConfig, WorkingHoursConfig } from "@/types/team";
import { getInitials } from "@/utils/kanban";
import { formatHolidayDate, validateWorkingHours } from "@/utils/settingsUtils";
import { format } from "date-fns";
import {
  ChevronLeft,
  Loader2,
  MoreHorizontal,
  Plus,
  Replace,
  Trash2,
  Users2,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { RoutingConfig } from "../../routing/types";

export default function CreateNewGroup() {
  const [groupName, setGroupName] = useState("");
  const [originalGroupName, setOriginalGroupName] = useState("");
  const [isUpdatingName, setIsUpdatingName] = useState(false);
  const _currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteGroupAlert, setDeleteGroupAlert] = useState(false);
  const [routingConfig, setRoutingConfig] = useState<RoutingConfig | null>(
    null,
  );
  const [unsavedRoutingConfig, setUnsavedRoutingConfig] = useState<RoutingConfig | null>(null);
  const [_hasRoutingChanges, setHasRoutingChanges] = useState(false);
  const [config, setConfig] = useState<WorkingHoursConfig>(DEFAULT_CONFIG);
  const [hasChanges, setHasChanges] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [isConfigLoading, setIsConfigLoading] = useState(true);

  const router = useRouter();

  const [members, setMembers] = useState<User[]>([]);

  const teamId = useParams().teamId as string;
  const groupId = useParams().groupId as string;
  const { data: group, loading: isLoading } = useApi<{
    createdAt: string;
    id: string;
    identifier: string;
    isActive: boolean;
    isPrivate: boolean;
    name: string;
    parentTeamId: string;
    parentTeamName: string;
    teamId: string;
    teamOwner: string;
    teamOwnerId: string;
    updatedAt: string;
  }>(
    `/v1/teams/${groupId}`,
    {},
    { enabled: !!groupId, isNextApi: false, method: "GET" },
  );
  const { data: membersData } = useApi<User[]>(
    `/v1/teams/${groupId}/members`,
    {},
    { enabled: !!groupId, isNextApi: false, method: "GET" },
  );

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const response = await fetch(`/api/teams/${groupId}/configurations`);

        if (!response.ok) {
          throw new Error("Failed to fetch working hours configuration");
        }

        const responseData = await response.json();

        const data = responseData.data;
        
        // Set routing configuration with default strategy if not present
        setRoutingConfig({
          teamId: groupId, // Add required teamId property
          userRoutingStrategy: data?.userRoutingStrategy || 'manual',
          routingRespectsUserTimezone: data?.routingRespectsUserTimezone || false,
          routingRespectsTimezone: data?.routingRespectsTimezone || false,
          routingRespectsUserAvailability: data?.routingRespectsUserAvailability || false,
          routingRespectsUserCapacity: data?.routingRespectsUserCapacity || false
        });

        if (data?.dailyConfig) {
          const transformedConfig: WorkingHoursConfig = {
            dayWiseHoursEnabled: true,
            workingDays: Object.entries(data.dailyConfig)
              .filter(([_, dayConfig]) => (dayConfig as DayConfig).isActive)
              .map(([day]) => day.charAt(0).toUpperCase() + day.slice(1)),
            daySpecificHours: Object.entries(data.dailyConfig).reduce(
              (acc, [day, dayConfig]) => {
                const slots = Array.isArray((dayConfig as DayConfig).slots)
                  ? [...(dayConfig as DayConfig).slots]
                  : [{ start: "00:00", end: "23:00" }];

                return {
                  ...acc,
                  [day.charAt(0).toUpperCase() + day.slice(1)]: {
                    isActive: (dayConfig as DayConfig).isActive,
                    slots,
                  },
                };
              },
              {},
            ),
            timezone: data.timezone,
            holidays: data.holidays || [],
            defaultHours: data.commonDailyConfig
              ? [...data.commonSlots]
              : (Object.values(data.dailyConfig)[0] as DayConfig)?.slots || [
                  { start: "00:00", end: "23:00" },
                ],
            commonDailyConfig: false,
            commonSlots: data.commonSlots,
          };
          setConfig(transformedConfig);
          setHasChanges(false);
        } else {
          setConfig({
            ...DEFAULT_CONFIG,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          });
          setHasChanges(false);
        }
      } catch (error) {
        console.error("Error in fetchConfig:", error);
        toast.error(
          error instanceof Error
            ? error.message
            : "Failed to fetch configuration",
        );
      } finally {
        setIsConfigLoading(false);
      }
    };

    fetchConfig();
  }, [groupId]);

  useEffect(() => {
    if (group) {
      setGroupName(group.name);
      setOriginalGroupName(group.name);
    }
  }, [group]);

  // Set members from the membersData API response
  useEffect(() => {
    if (membersData) {
      setMembers(membersData);
    }
  }, [membersData]);
  
  // We don't need this second fetch as we're already getting members data from the useApi hook above

  const _updateGroupMutation = useApiMutation(
    `/teams/${teamId}/groups/${groupId}`,
    {},
    "PATCH",
  );

  const handleDeleteMember = async (memberId: string) => {
    try {
      const response = await fetch(
        `/api/teams/${groupId}/members/${memberId}`,
        {
          method: "DELETE",
        },
      );

      if (!response.ok) {
        throw new Error("Failed to delete member");
      }

      // Update the members list by filtering out the deleted member
      setMembers((prev) => prev.filter((member) => member.id !== memberId));
      toast.success("Member removed successfully");
    } catch (error) {
      console.error("Error deleting member:", error);
      toast.error("Failed to remove member");
    }
  };

  const handleUpdateConfig = (updates: Partial<RoutingConfig>) => {
    // Only update local unsaved state, don't make API calls
    // Always ensure team timezone is respected (not configurable by user)
    const updatesWithTeamTimezone = {
      ...updates,
      routingRespectsTimezone: true,
    };

    // Update unsaved routing config state
    setUnsavedRoutingConfig((prev) => {
      if (!prev && routingConfig) {
        // Initialize with current routing config if it exists
        return {
          ...routingConfig,
          ...updatesWithTeamTimezone,
        };
      } else if (!prev && !routingConfig) {
        // Initialize with defaults if no current routing config
        return {
          teamId: groupId || '',
          userRoutingStrategy: updates.userRoutingStrategy || '',
          routingRespectsTimezone: true,
          routingRespectsUserTimezone: updates.routingRespectsUserTimezone || false,
          routingRespectsUserAvailability: updates.routingRespectsUserAvailability || false,
          routingRespectsUserCapacity: false, // Required by RoutingConfig type
        };
      } else {
        // Update existing unsaved state
        return {
          ...prev,
          ...updatesWithTeamTimezone,
        };
      }
    });

    // Mark that we have changes to save
    setHasRoutingChanges(true);
    setHasChanges(true); // Also update the overall hasChanges state
  };

  // Routing config is saved as part of handleSave

  // Create filtered options for the dropdown using the parent team members
  // We fetch parent team members (teamId) to show available users who can be added to this group
  const { data: teamMembers } = useApi<User[]>(
    `/v1/teams/${teamId}/members`,
    {},
    { enabled: !!teamId, isNextApi: false, method: "GET" }
  );
  
  const filteredTeamMembers = useMemo(() => {
    return (teamMembers || [])
      .filter((user) => !members.some((member) => member.id === user.id))
      .map((user) => ({
        label: user.name,
        value: user.id,
      }));
  }, [teamMembers, members]);

  const { mutate: addMemberToGroup } = useApiMutation(`/v1/teams`, {}, "POST");

  const handleSave = async () => {
    try {
      // Validate before saving
      const validationError = validateWorkingHours(config);
      if (validationError) {
        toast.error(validationError);
        return;
      }

      setIsSaving(true);
      
      // We'll include routing configuration in the same payload as working hours
      // This matches the approach in the create group page
      const ALL_DAYS = [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
      ];
      const dailyConfig = ALL_DAYS.reduce((acc, day) => {
        const dayLower = day.toLowerCase();
        const isActive = config.workingDays.includes(day);
        const dayConfig = {
          isActive,
          slots: isActive
            ? config.dayWiseHoursEnabled
              ? config.daySpecificHours[day]?.slots || []
              : config.defaultHours
            : [],
        };

        return {
          ...acc,
          [dayLower]: dayConfig,
        };
      }, {});

      // Determine which routing config to use (unsaved changes or current config)
      const routingConfigToSave = unsavedRoutingConfig || routingConfig;

      const payload = {
        timezone: config.timezone === "" ? "UTC" : config.timezone,
        holidays: config.holidays.map((item) =>
          format(formatHolidayDate(item), "dd-MM-yyyy"),
        ),
        ...(config.dayWiseHoursEnabled
          ? { dailyConfig }
          : {
              commonDailyConfig: true,
              commonSlots: config.defaultHours,
            }),
        // Only include routing strategy in the payload if it has been selected
        ...(routingConfigToSave?.userRoutingStrategy ? {
          userRoutingStrategy: routingConfigToSave.userRoutingStrategy,
          // Always ensure team timezone is respected (not configurable by user)
          routingRespectsTimezone: true,
          routingRespectsUserTimezone: routingConfigToSave.routingRespectsUserTimezone || false,
          routingRespectsUserAvailability: routingConfigToSave.routingRespectsUserAvailability || false,
          // Remove capacity for now as it's not supported
          // routingRespectsUserCapacity: routingConfigToSave.routingRespectsUserCapacity || false
        } : {
          // Even if no routing strategy is selected, ensure team timezone is respected
          routingRespectsTimezone: true
        })
      };

      const response = await fetch(`/api/teams/${groupId}/configurations`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      // Log the response headers for debugging
      const responseHeaders = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      if (!response.ok) {
        let errorMessage = `Failed to save group configuration: ${response.status} ${response.statusText}`;
        try {
          const errorData = await response.json();
          console.error("Error response data:", errorData);
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          console.error("Error parsing error response:", parseError);
        }
        throw new Error(errorMessage);
      }

      // If we get here, the request was successful
      try {
        const data = await response.json();
        console.log("Success response data:", data);
        toast.success("Group configuration updated successfully");
        setHasChanges(false);
        setHasRoutingChanges(false);
        
        // Update the actual routing config with the saved values
        if (unsavedRoutingConfig) {
          setRoutingConfig(unsavedRoutingConfig);
          setUnsavedRoutingConfig(null); // clear local draft
        }
      } catch (parseError) {
        console.error("Error parsing success response:", parseError);
        toast.success("Group configuration saved successfully");
      }

      setHasChanges(false);
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to save group configuration",
      );
    } finally {
      setIsSaving(false);
    }
  };

  const dayOrder = {
    Monday: 0,
    Tuesday: 1,
    Wednesday: 2,
    Thursday: 3,
    Friday: 4,
    Saturday: 5,
    Sunday: 6,
  };

  const sortDays = (days: string[]) => {
    return [...days].sort((a, b) => dayOrder[a] - dayOrder[b]);
  };

  const handleToggleDay = (day: string) => {
    setConfig((prev) => {
      const isAdding = !prev.workingDays.includes(day);
      const newWorkingDays = sortDays(
        isAdding
          ? [...prev.workingDays, day]
          : prev.workingDays.filter((d) => d !== day),
      );

      // Update day-specific hours
      const updatedDaySpecificHours = { ...prev.daySpecificHours };

      if (isAdding) {
        // Initialize slots for newly selected day
        updatedDaySpecificHours[day] = {
          isActive: true,
          slots: [{ start: "00:00", end: "23:59" }],
        };
      } else {
        // Remove slots for deselected day
        delete updatedDaySpecificHours[day];
      }

      return {
        ...prev,
        workingDays: newWorkingDays,
        daySpecificHours: updatedDaySpecificHours,
      };
    });
    setHasChanges(true);
  };

  const handleTimeChange = (
    day: string | null,
    slotIndex: number,
    type: "start" | "end",
    value: string,
  ) => {
    if (config.daySpecificHours && day) {
      setConfig((prev) => ({
        ...prev,
        daySpecificHours: {
          ...prev.daySpecificHours,
          [day]: {
            ...prev.daySpecificHours[day],
            slots:
              prev.daySpecificHours[day]?.slots?.map((slot, idx) =>
                idx === slotIndex ? { ...slot, [type]: value } : slot,
              ) || [],
          },
        },
      }));
    } else {
      setConfig((prev) => ({
        ...prev,
        defaultHours: prev.defaultHours.map((slot, idx) =>
          idx === slotIndex ? { ...slot, [type]: value } : slot,
        ),
      }));
    }
    setHasChanges(true);
  };

  const handleAddTimeSlot = (day: string | null) => {
    if (config.daySpecificHours && day) {
      setConfig((prev) => ({
        ...prev,
        daySpecificHours: {
          ...prev.daySpecificHours,
          [day]: {
            ...prev.daySpecificHours[day],
            slots: [
              ...(prev.daySpecificHours[day]?.slots || []),
              { start: "00:00", end: "23:00" },
            ],
          },
        },
      }));
    } else {
      setConfig((prev) => ({
        ...prev,
        defaultHours: [...prev.defaultHours, { start: "00:00", end: "23:00" }],
      }));
    }
    setHasChanges(true);
  };

  const handleRemoveTimeSlot = (day: string | null, slotIndex: number) => {
    if (day) {
      setConfig((prev) => ({
        ...prev,
        daySpecificHours: {
          ...prev.daySpecificHours,
          [day]: {
            ...prev.daySpecificHours[day],
            slots:
              prev.daySpecificHours[day]?.slots?.filter(
                (_, idx) => idx !== slotIndex,
              ) || [],
          },
        },
      }));
    } else {
      setConfig((prev) => ({
        ...prev,
        defaultHours: prev.defaultHours.filter((_, idx) => idx !== slotIndex),
      }));
    }
    setHasChanges(true);
  };

  const handleAddHoliday = (date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd");
    if (config.holidays.includes(dateStr)) return;

    setConfig((prev) => ({
      ...prev,
      holidays: [...prev.holidays, dateStr].sort(),
    }));
    setHasChanges(true);
  };

  const handleRemoveHoliday = (date: string) => {
    setConfig((prev) => ({
      ...prev,
      holidays: prev.holidays.filter((h) => h !== date),
    }));
    setHasChanges(true);
  };

  const handleUpdateGroupName = async () => {
    if (!groupId || groupName.trim() === "") {
      return;
    }

    try {
      setIsUpdatingName(true);

      const response = await fetch(`/api/teams/${groupId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: groupName.trim() }),
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || "Failed to update group name");
      }

      setOriginalGroupName(groupName);
      toast.success("Group name updated successfully");
    } catch (error) {
      console.error("Error updating group name:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update group name. Please try again later.",
      );
    } finally {
      setIsUpdatingName(false);
    }
  };

  const handleDeleteGroup = async () => {
    setIsDeleting(true);
    try {
      // Using the same API endpoint as the group list page
      const response = await fetch(`/api/teams/${groupId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.error || "Failed to delete group");
      }

      toast.success("Group deleted successfully");
      router.push(`/dashboard/${teamId}/settings/groups`);
    } catch (e) {
      console.error("Error deleting group:", e);
      toast.error(
        e instanceof Error
          ? e.message
          : "Failed to delete group. Please try again later.",
      );
    } finally {
      setIsDeleting(false);
      setDeleteGroupAlert(false);
    }
  };

  if (isLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center min-h-[640px]">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="h-full w-full bg-background">
      <div className="mx-auto max-w-[640px] space-y-8 py-8">
        <div className="space-y-2">
          <h1 className="text-2xl font-medium tracking-tight">Edit group</h1>
          <p className="text-sm text-muted-foreground">
            Manage group details and configurations.
          </p>
        </div>
        <form className="space-y-6">
          <div className="space-y-3">
            <Label className="text-sm font-medium">Group name</Label>
            <div className="flex items-center gap-3">
              <Input
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                placeholder="Enter group name"
                disabled={isUpdatingName}
                className="flex-1"
              />
              {groupName !== originalGroupName && groupName.trim() !== "" && (
                <Button
                  onClick={handleUpdateGroupName}
                  disabled={isUpdatingName}
                >
                  {isUpdatingName && (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  )}
                  {isUpdatingName ? "Updating..." : "Update"}
                </Button>
              )}
            </div>
          </div>
        </form>

        <div className="space-y-4">
          <Accordion type="single" collapsible className="rounded-sm border text-card-foreground">
            <AccordionItem value="members" className="border-0">
              <AccordionTrigger 
                className="flex w-full items-center px-4 h-12 hover:bg-muted/50 transition-colors rounded-t-sm data-[state=open]:border-b data-[state=open]:rounded-b-none"
              >
                <div className="flex items-center gap-3">
                  <Users2 className="h-4 w-4 text-muted-foreground" />
                  <span className="text-base font-medium">Members</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
              <div className="px-4">
                {members.map((member) => (
                  <div
                    key={member.id}
                    className="flex justify-between items-center py-3"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar
                        src={member.avatarUrl}
                        alt={member.name}
                        fallbackText={getInitials(member.name)}
                        imgClassnames="w-8 h-8"
                      />
                      <div>
                        <p className="font-medium flex items-center gap-2">
                          {member.name}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {member.email}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            onClick={() => handleDeleteMember(member.id)}
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
                
                <div className="mt-4">
                  <CommonSelectWrapper
                    options={filteredTeamMembers}
                    value={undefined}
                    onChange={(value) => {
                      if (Array.isArray(value)) {
                        return;
                      }
                      const user = (teamMembers || []).find(
                        (user) => user.id === value.value,
                      );

                      addMemberToGroup(
                        {
                          email: user.email,
                          userId: user.id,
                        },
                        {},
                        `/${groupId}/members`,
                      )
                        .then((res) => {
                          if (res && typeof res === "object" && "data" in res) {
                            toast.success("Member added");
                            setMembers((prev) => [...prev, res.data as User]);
                          }
                        })
                        .catch(() => {
                          toast.error("Failed to add member");
                        });
                    }}
                    placeholder="Add member"
                    customTrigger={<div className="flex items-center gap-1 text-foreground border border-input rounded-sm px-3 py-1 hover:bg-accent"><Plus className="h-4 w-4" /> Add member</div>}
                    hideClearIndicator
                    hideDropdownIndicator
                    isVirtualized={(teamMembers || []).length > 20}
                    triggerClassname="text-foreground text-sm h-7 px-3"
                    wrapperClassname="bg-background hover:bg-accent self-start"
                    labelClass="border border-input rounded-sm"
                    hideBorder={false}
                  />
                </div>
              </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <BusinessHours
            config={config}
            setConfig={setConfig}
            handleToggleDay={handleToggleDay}
            handleTimeChange={handleTimeChange}
            handleAddTimeSlot={handleAddTimeSlot}
            handleRemoveTimeSlot={handleRemoveTimeSlot}
            handleAddHoliday={handleAddHoliday}
            handleRemoveHoliday={handleRemoveHoliday}
            hasChanges={hasChanges}
            setHasChanges={setHasChanges}
            isLoading={isConfigLoading}
            isSaving={isSaving}
            handleSave={handleSave}
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
            showSaveButton={false}
            isCollapsibleDisabled={false}
            isFormEnabled={true}
            accordionClassName="rounded-sm border text-card-foreground"
            triggerClassName="flex w-full items-center px-4 h-12 hover:bg-muted/50 transition-colors rounded-t-sm data-[state=open]:border-b data-[state=open]:rounded-b-none"
            contentClassName="p-4 rounded-b-sm"
            iconClassName="h-4 w-4 text-muted-foreground shrink-0 transition-transform duration-200"
          />

          <Accordion type="single" collapsible className="rounded-sm border text-card-foreground">
            <AccordionItem value="assignment" className="border-0">
              <AccordionTrigger 
                className="flex w-full items-center px-4 h-12 hover:bg-muted/50 transition-colors rounded-t-sm data-[state=open]:border-b data-[state=open]:rounded-b-none"
              >
                <div className="flex items-center gap-3">
                  <Replace className="h-4 w-4 text-muted-foreground" />
                  <span className="text-base font-medium">Assignment logic</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="p-4 rounded-b-sm">
                <AssignmentStrategyCard
                  routingConfig={unsavedRoutingConfig || routingConfig}
                  isSaving={isSaving}
                  onUpdateConfig={handleUpdateConfig}
                  showGroupsInfo={false}
                  teamId={groupId}
                  routingRoute={teamId}
                />
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="" style={{ marginTop: "32px" }}>
            <div className="flex w-full justify-between items-center">
              <Button
                variant="outline"
                onClick={() =>
                  router.push(`/dashboard/${teamId}/settings/groups`)
                }
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                Back
              </Button>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setDeleteGroupAlert(true)}
                  disabled={isDeleting}
                  className="text-destructive hover:text-destructive border-destructive hover:bg-destructive/10 min-w-[120px] flex items-center gap-2"
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Deleting...</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      <span>Delete group</span>
                    </>
                  )}
                </Button>
                <Button 
                  onClick={handleSave} 
                  disabled={isSaving || !hasChanges}
                  className="min-w-[120px] flex items-center gap-2"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <span>Save changes</span>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AlertDialog open={deleteGroupAlert} onOpenChange={setDeleteGroupAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete the group?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your
              group and remove any routing rules set for the group.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(event) => {
                event.preventDefault();
                handleDeleteGroup();
              }}
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
