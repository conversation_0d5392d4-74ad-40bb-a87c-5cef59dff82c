"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from "@hello-pangea/dnd";
import {
  AlertCircle,
  Check,
  FileText,
  GripVertical,
  MoreHorizontal,
  Pencil,
  Power,
  Search,
  Trash,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";
import { FormActionDialog } from "./components/form-action-dialog";

interface Form {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  default?: boolean;
  createdAt: string;
  updatedAt: string;
  version?: number;
  order?: number;
}

interface PaginatedResponse {
  results: Form[];
  pageTotal: number;
  total: number;
}

interface ApiResponse {
  data: PaginatedResponse;
  status: boolean;
  message: string;
  timestamp: string;
}

const PAGE_SIZE = 50; // Configurable page size

const EmptyState = () => {
  const router = useRouter();
  const params = useParams<{ teamId: string }>();

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="rounded-full bg-muted p-4">
        <FileText className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="mt-4 text-lg font-semibold">No forms created</h3>
      <p className="mt-2 max-w-sm text-sm text-muted-foreground">
        Forms help you collect structured data from your users. Create your
        first form to get started.
      </p>
      <Button
        onClick={() =>
          router.push(`/dashboard/${params.teamId}/settings/forms/create`)
        }
        className="mt-4"
      >
        Create first form
      </Button>
    </div>
  );
};

export default function FormsSettings() {
  const params = useParams<{ teamId: string }>();
  const teamId = params.teamId;
  const [forms, setForms] = React.useState<Form[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isInitialLoading, setIsInitialLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [showInactiveForms, setShowInactiveForms] = React.useState(false);
  const [deletingForm, setDeletingForm] = React.useState<Form | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [totalForms, setTotalForms] = React.useState(0);
  const [currentPage, setCurrentPage] = React.useState(0);
  const [hasMore, setHasMore] = React.useState(true);
  const observerTarget = React.useRef<HTMLDivElement>(null);
  const loadedFormIds = React.useRef(new Set<string>());
  const isFetchingRef = React.useRef(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [allFormsLoaded, setAllFormsLoaded] = React.useState(false);
  const router = useRouter();
  const [actionForm, setActionForm] = React.useState<{
    form: Form | null;
    action: "deactivate" | "default" | "activate" | null;
  }>({ form: null, action: null });
  const [isActionLoading, setIsActionLoading] = React.useState(false);

  const fetchForms = React.useCallback(async () => {
    // Return if already fetching or no more data or all forms are loaded
    if (isFetchingRef.current || !hasMore || allFormsLoaded) return;

    isFetchingRef.current = true;
    setIsLoading(true);

    try {
      const response = await fetch(
        `/api/forms?teamId=${teamId}&onlyTeamForms=true&page=${currentPage}&limit=${PAGE_SIZE}`,
      );
      const data = (await response.json()) as ApiResponse;

      if (!response.ok) {
        throw new Error(data.message || "Failed to fetch forms");
      }

      const formsArray = data?.data?.results || [];

      // Filter out any forms we've already loaded
      const newForms = formsArray.filter(
        (form) => !loadedFormIds.current.has(form.id),
      );

      // Update our set of loaded form IDs
      newForms.forEach((form) => loadedFormIds.current.add(form.id));

      if (newForms.length > 0) {
        setForms((prevForms) => [...prevForms, ...newForms]);
      }

      setTotalForms(data?.data?.total || 0);

      // Continue fetching if we received a full page of new forms
      const shouldContinue = formsArray.length === PAGE_SIZE;
      setHasMore(shouldContinue);
      setAllFormsLoaded(!shouldContinue);

      if (shouldContinue) {
        // Automatically increment page for next fetch
        setCurrentPage((prev) => prev + 1);
      }
    } catch (error) {
      console.error("Error fetching forms:", error);
      setError(
        error instanceof Error ? error.message : "Failed to fetch forms",
      );
      setHasMore(false);
    } finally {
      setIsLoading(false);
      setIsInitialLoading(false);
      isFetchingRef.current = false;
    }
  }, [teamId, currentPage, hasMore, allFormsLoaded]);

  // Initialize intersection observer for infinite scroll
  React.useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        // If the target is visible and we're not currently fetching, trigger a fetch
        if (entries[0].isIntersecting && !isFetchingRef.current && hasMore) {
          fetchForms();
        }
      },
      {
        threshold: 0.1,
        rootMargin: "200px", // Start loading earlier before reaching the bottom
      },
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [fetchForms, hasMore]);

  // Initial fetch
  React.useEffect(() => {
    fetchForms();
  }, [fetchForms]);

  // Remove the search-triggered API calls effect
  // The filtering is now handled entirely client-side in the filteredForms memo

  const filteredForms = React.useMemo(() => {
    return forms.filter((form) => {
      const matchesSearch = form.name
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
      const matchesStatus = showInactiveForms ? !form.isActive : form.isActive;
      return matchesSearch && matchesStatus;
    });
  }, [forms, searchQuery, showInactiveForms]);

  const handleDeleteClick = (form: Form) => {
    setDeletingForm(form);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteCancel = () => {
    setDeletingForm(null);
    setIsDeleteDialogOpen(false);
  };

  // Handle successful form deletion
  const handleDeleteSuccess = React.useCallback((deletedFormId: string) => {
    setForms((prevForms) =>
      prevForms.filter((form) => form.id !== deletedFormId),
    );
    loadedFormIds.current.delete(deletedFormId);
    setTotalForms((prev) => Math.max(0, prev - 1));
  }, []);

  const handleDeleteConfirm = async () => {
    if (!deletingForm) return;

    setIsDeleting(true);
    try {
      const version = deletingForm.version || 1;
      const response = await fetch(
        `/api/forms/${deletingForm.id}?version=${version}`,
        {
          method: "DELETE",
        },
      );
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || data.message || "Failed to delete form");
      }

      handleDeleteSuccess(deletingForm.id);
      toast.success("Form deleted successfully");
    } catch (error) {
      console.error("Error deleting form:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to delete form",
      );
    } finally {
      setIsDeleting(false);
      setDeletingForm(null);
      setIsDeleteDialogOpen(false);
    }
  };

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination } = result;

    // If dropped in the same position, do nothing
    if (source.index === destination.index) return;

    // Keep a backup of the current forms state
    const originalForms = [...forms];

    // Create a new array of forms with updated order
    const updatedForms = Array.from(forms);
    const [removed] = updatedForms.splice(source.index, 1);
    updatedForms.splice(destination.index, 0, removed);

    // Update the local state immediately for a smoother UX
    setForms(updatedForms);

    try {
      const response = await fetch(`/api/forms/order`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          forms: updatedForms.map((form, index) => ({
            formId: form.id,
            order: index,
            version: form.version || 1,
          })),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.error || data.message || "Failed to update form order",
        );
      }

      toast.success("Form order updated successfully");
    } catch (error) {
      console.error("Error updating form order:", error);
      // Revert to the original order
      setForms(originalForms);
      toast.error(
        error instanceof Error ? error.message : "Failed to update form order",
      );
    }
  };

  const handleDeactivateForm = async () => {
    if (!actionForm.form) return;

    setIsActionLoading(true);
    try {
      const response = await fetch(`/api/forms/${actionForm.form.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          version: actionForm.form.version,
          updates: {
            isActive: false,
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to deactivate form");
      }

      // Update local state
      setForms((prevForms) =>
        prevForms.map((form) =>
          form.id === actionForm.form.id
            ? {
                ...form,
                isActive: false,
                version: (actionForm.form.version || 1) + 1,
              }
            : form,
        ),
      );

      toast.success("Form deactivated successfully");
    } catch (error) {
      console.error("Error deactivating form:", error);
      toast.error("Failed to deactivate form");
    } finally {
      setIsActionLoading(false);
      setActionForm({ form: null, action: null });
    }
  };

  const handleActivateForm = async () => {
    if (!actionForm.form) return;

    setIsActionLoading(true);
    try {
      const response = await fetch(`/api/forms/${actionForm.form.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          version: actionForm.form.version,
          updates: {
            isActive: true,
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to activate form");
      }

      // Update local state
      setForms((prevForms) =>
        prevForms.map((form) =>
          form.id === actionForm.form.id
            ? {
                ...form,
                isActive: true,
                version: (actionForm.form.version || 1) + 1,
              }
            : form,
        ),
      );

      toast.success("Form activated successfully");
    } catch (error) {
      console.error("Error activating form:", error);
      toast.error("Failed to activate form");
    } finally {
      setIsActionLoading(false);
      setActionForm({ form: null, action: null });
    }
  };

  const handleSetDefaultForm = async () => {
    if (!actionForm.form) return;

    setIsActionLoading(true);
    try {
      const response = await fetch(`/api/forms/${actionForm.form.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          version: actionForm.form.version,
          updates: {
            default: true,
          },
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to set form as default");
      }

      // Update local state - set current form as default and remove default from others
      setForms((prevForms) =>
        prevForms.map((form) => ({
          ...form,
          default: form.id === actionForm.form.id,
          version:
            form.id === actionForm.form.id
              ? (actionForm.form.version || 1) + 1
              : form.version,
        })),
      );

      toast.success("Form set as default successfully");
    } catch (error) {
      console.error("Error setting default form:", error);
      toast.error("Failed to set form as default");
    } finally {
      setIsActionLoading(false);
      setActionForm({ form: null, action: null });
    }
  };

  if (isInitialLoading) {
    return (
      <div className="h-full min-h-[calc(100vh-150px)] overflow-y-auto flex items-center justify-center">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 max-w-[688px] mx-auto">
      <div>
        <h1 className="text-2xl font-medium">Forms</h1>
        <p className="text-sm text-muted-foreground mt-1">
          Create and manage forms to collect structured data from your users.
        </p>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative w-64">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search forms..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 rounded-md"
          />
        </div>
        <div className="flex items-center gap-2">
          <Switch
            id="show-inactive"
            checked={showInactiveForms}
            onCheckedChange={setShowInactiveForms}
          />
          <Label htmlFor="show-inactive">Show inactive</Label>
        </div>
        <div className="flex-1 flex justify-end">
          <Button
            onClick={() =>
              router.push(`/dashboard/${params.teamId}/settings/forms/create`)
            }
          >
            Create form
          </Button>
        </div>
      </div>

      {error ? (
        <div className="rounded-mg border border-destructive/20 bg-destructive/10 p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 mt-0.5 text-destructive" />
            <div>
              <h3 className="font-medium text-destructive-foreground">
                Failed to load forms
              </h3>
              <p className="text-sm text-destructive mt-1">
                {error}. Please try again or contact support if the issue
                persists.
              </p>
              <Button
                variant="ghost"
                className="mt-2 h-8 text-destructive hover:bg-destructive/10 hover:text-destructive"
                onClick={() => {
                  setError(null);
                  setCurrentPage(0);
                  setHasMore(true);
                  loadedFormIds.current.clear();
                  isFetchingRef.current = false;
                  fetchForms();
                }}
              >
                Try again
              </Button>
            </div>
          </div>
        </div>
      ) : forms.length === 0 && totalForms === 0 ? (
        <EmptyState />
      ) : (
        <>
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="forms-list">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-2"
                >
                  {filteredForms.map((form, index) => (
                    <Draggable
                      key={form.id}
                      draggableId={form.id}
                      index={index}
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={cn(
                            "flex items-center justify-between p-3 bg-background border rounded-md min-h-[68px]",
                            snapshot.isDragging && "shadow-lg",
                          )}
                        >
                          <div className="flex items-center gap-3 min-w-0">
                            <div
                              {...provided.dragHandleProps}
                              className="cursor-grab touch-none"
                            >
                              <GripVertical className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <div className="truncate">
                              <h3 className="font-medium truncate leading-5">
                                {form.name}
                              </h3>
                              <p className="text-sm text-muted-foreground truncate h-5">
                                {form.description || "No description provided"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {!form.isActive && (
                              <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                                Inactive
                              </span>
                            )}
                            {form.default && (
                              <span className="text-xs text-primary bg-primary/10 px-2 py-1 rounded">
                                Default
                              </span>
                            )}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() =>
                                    router.push(
                                      `/dashboard/${params.teamId}/settings/forms/${form.id}/edit`,
                                    )
                                  }
                                >
                                  <Pencil className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                {!form.isActive && !form.default && (
                                  <DropdownMenuItem
                                    onClick={() =>
                                      setActionForm({
                                        form,
                                        action: "activate",
                                      })
                                    }
                                  >
                                    <Power className="mr-2 h-4 w-4" />
                                    Activate
                                  </DropdownMenuItem>
                                )}
                                {form.isActive && !form.default && (
                                  <DropdownMenuItem
                                    onClick={() =>
                                      setActionForm({
                                        form,
                                        action: "deactivate",
                                      })
                                    }
                                  >
                                    <Power className="mr-2 h-4 w-4" />
                                    Deactivate
                                  </DropdownMenuItem>
                                )}
                                {!form.default && form.isActive && (
                                  <DropdownMenuItem
                                    onClick={() =>
                                      setActionForm({ form, action: "default" })
                                    }
                                  >
                                    <Check className="mr-2 h-4 w-4" />
                                    Set as default
                                  </DropdownMenuItem>
                                )}
                                {!form.default && (
                                  <DropdownMenuItem
                                    className="text-destructive focus:text-destructive"
                                    onClick={() => handleDeleteClick(form)}
                                  >
                                    <Trash className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}

                  {/* Loading indicator at bottom - only show for pagination loads */}
                  {isLoading && !isInitialLoading && (
                    <div className="flex justify-center py-4">
                      <ThenaLoader />
                    </div>
                  )}
                </div>
              )}
            </Droppable>
          </DragDropContext>

          {/* Infinite scroll observer */}
          <div ref={observerTarget} className="h-4 w-full" />
        </>
      )}

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete form</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete &quot;{deletingForm?.name}&quot;?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-3">
            <Button
              variant="ghost"
              onClick={handleDeleteCancel}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <FormActionDialog
        open={!!actionForm.action}
        onClose={() => setActionForm({ form: null, action: null })}
        onConfirm={
          actionForm.action === "deactivate"
            ? handleDeactivateForm
            : actionForm.action === "activate"
            ? handleActivateForm
            : handleSetDefaultForm
        }
        title={
          actionForm.action === "deactivate"
            ? "Deactivate form"
            : actionForm.action === "activate"
            ? "Activate form"
            : "Set as default form"
        }
        description={
          actionForm.action === "deactivate"
            ? "Are you sure you want to deactivate this form? This will prevent any new tickets from being created with this form."
            : actionForm.action === "activate"
            ? "Are you sure you want to activate this form? This will allow new tickets to be created with this form."
            : "Are you sure you want to set this form as the default? This will make it the default form for new tickets."
        }
        isLoading={isActionLoading}
      />
    </div>
  );
}
