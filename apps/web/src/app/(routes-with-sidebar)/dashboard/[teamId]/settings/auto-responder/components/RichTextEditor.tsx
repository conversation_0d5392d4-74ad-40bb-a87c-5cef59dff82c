"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import TextAlign from "@tiptap/extension-text-align";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Bold, Italic, List, ListOrdered, Type, Variable } from "lucide-react";
import { useEffect } from "react";

interface RichTextEditorProps {
  value: string; // HTML string
  onChange: (value: { content: string; contentHtml: string }) => void;
  error?: boolean;
  onBlur?: () => void;
}

export function RichTextEditor({
  value,
  onChange,
  error = false,
  onBlur,
}: RichTextEditorProps) {
  const getExtensions = () => {
    return [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
      }),
      TextAlign.configure({ types: ["heading", "paragraph", "callout"] }),
      Placeholder.configure({
        emptyEditorClass: "is-editor-empty",
        placeholder: "Enter your automatic response message here...",
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          target: "_blank",
          rel: "noopener noreferrer nofollow",
          class: "text-blue-300",
        },
      }).extend({
        inclusive: false,
      }),
    ].filter(Boolean);
  };

  const editor = useEditor({
    extensions: getExtensions(),
    content: value, // value is HTML string
    onUpdate: ({ editor }) => {
      onChange({
        content: editor.getText(),
        contentHtml: editor.getHTML(),
      });
    },
    onBlur: () => {
      onBlur?.();
    },
    editorProps: {
      attributes: {
        class: `prose prose-sm focus:outline-none min-h-[100px] whitespace-pre-wrap ${
          error ? "border-destructive" : ""
        }`,
      },
    },
  });

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value);
    }
  }, [value, editor]);

  // Available variables for insertion
  const variables = [
    { id: "customer_name", label: "Customer name", value: "{customer_name}" },
    { id: "ticket_id", label: "Ticket ID", value: "{ticket_id}" },
    { id: "team_name", label: "Team name", value: "{team_name}" },
    { id: "agent_name", label: "Agent name", value: "{agent_name}" },
    { id: "company_name", label: "Company name", value: "{company_name}" },
  ];

  const insertVariable = (variable: string) => {
    if (editor) {
      editor.chain().focus().insertContent(variable).run();
    }
  };

  const applyFormat = (format: string) => {
    if (!editor) return;

    switch (format) {
      case "bold":
        editor.chain().focus().toggleBold().run();
        break;
      case "italic":
        editor.chain().focus().toggleItalic().run();
        break;
      case "list":
        editor.chain().focus().toggleBulletList().run();
        break;
      case "ordered-list":
        editor.chain().focus().toggleOrderedList().run();
        break;
      case "heading":
        editor.chain().focus().toggleHeading({ level: 2 }).run();
        break;
      default:
        return;
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-1 bg-muted p-1 rounded-md">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => applyFormat("bold")}
          className="h-8 w-8 p-0"
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => applyFormat("italic")}
          className="h-8 w-8 p-0"
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => applyFormat("heading")}
          className="h-8 w-8 p-0"
        >
          <Type className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => applyFormat("list")}
          className="h-8 w-8 p-0"
        >
          <List className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => applyFormat("ordered-list")}
          className="h-8 w-8 p-0"
        >
          <ListOrdered className="h-4 w-4" />
        </Button>
        <div className="flex-1" />
        <Popover>
          <PopoverTrigger asChild>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 px-2"
            >
              <Variable className="h-4 w-4" />
              <span className="text-xs">Variables</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-56 p-2" align="end">
            <div className="space-y-1">
              {variables.map((variable) => (
                <Button
                  key={variable.id}
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-sm h-8"
                  onClick={() => insertVariable(variable.value)}
                >
                  {variable.label}
                </Button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div className={`border rounded-md ${error ? "border-destructive" : ""}`}>
        <style jsx global>{`
          .ProseMirror {
            padding: 0.5rem;
            min-height: 100px;
            outline: none;
          }
          .ProseMirror p.is-editor-empty:first-child::before {
            content: attr(data-placeholder);
            float: left;
            color: #adb5bd;
            pointer-events: none;
            height: 0;
          }
          .ProseMirror p {
            margin: 0;
            white-space: pre-wrap;
          }
        `}</style>
        <EditorContent editor={editor} />
      </div>
    </div>
  );
}
