"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import TextAlign from "@tiptap/extension-text-align";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import {
  Bold,
  Code,
  Italic,
  List,
  ListOrdered,
  Quote,
  Strikethrough,
  Type,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";

interface RichTextEditorProps {
  value: string; // HTML string
  onChange: (value: {
    content: string;
    contentHtml: string;
    contentJson: string;
  }) => void;
  error?: boolean;
  onBlur?: () => void;
}

export function RichTextEditor({
  value,
  onChange,
  error = false,
  onBlur,
}: RichTextEditorProps) {
  const [isFocused, setIsFocused] = useState(false);

  const getExtensions = useCallback(() => {
    return [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
        code: {
          HTMLAttributes: {
            class: "inline-code",
          },
        },
        blockquote: {
          HTMLAttributes: {
            class: "blockquote-element",
          },
        },
      }),
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Placeholder.configure({
        emptyEditorClass: "is-editor-empty",
        placeholder: "Write your automatic response message...",
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          target: "_blank",
          rel: "noopener noreferrer nofollow",
          class: "text-blue-500 hover:underline",
        },
      }).extend({
        inclusive: false,
      }),
    ].filter(Boolean);
  }, []);

  const editor = useEditor({
    extensions: getExtensions(),
    content: value,
    onUpdate: ({ editor }) => {
      onChange({
        content: editor.getText(),
        contentHtml: editor.getHTML(),
        contentJson: JSON.stringify(editor.getJSON()),
      });
    },
    onFocus: () => {
      setIsFocused(true);
    },
    onBlur: () => {
      setIsFocused(false);
      onBlur?.();
    },
    editorProps: {
      attributes: {
        class: "focus:outline-none min-h-[120px] max-w-none",
      },
    },
  });

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value);
    }
  }, [value, editor]);

  // Inject ProseMirror styles to avoid styled-jsx quotation mark issues
  useEffect(() => {
    const styleId = "prosemirror-styles";

    // Check if styles are already injected
    if (document.getElementById(styleId)) {
      return;
    }

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      .ProseMirror {
        padding: 12px;
        min-height: 120px;
        outline: none;
        font-size: 14px;
        line-height: 1.5;
        color: hsl(var(--foreground));
      }
      
      .ProseMirror * {
        quotes: none !important;
      }
      
      .ProseMirror *::before,
      .ProseMirror *::after {
        quotes: none !important;
      }
      
      .ProseMirror p.is-editor-empty:first-child::before {
        content: attr(data-placeholder);
        float: left;
        color: hsl(var(--muted-foreground));
        pointer-events: none;
        height: 0;
      }
      
      .ProseMirror p {
        margin: 0 0 8px 0;
      }
      
      .ProseMirror p:last-child {
        margin-bottom: 0;
      }
      
      .ProseMirror h2 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 16px 0 8px 0;
        line-height: 1.3;
        color: hsl(var(--foreground));
      }
      
      .ProseMirror h2:first-child {
        margin-top: 0;
      }
      
      .ProseMirror ul, .ProseMirror ol {
        padding-left: 24px;
        margin: 8px 0;
      }
      
      .ProseMirror li {
        margin: 4px 0;
      }
      
      .ProseMirror blockquote,
      .ProseMirror .blockquote-element {
        border-left: 4px solid hsl(var(--border));
        padding-left: 8px;
        margin: 12px 0;
        color: hsl(var(--foreground));
        quotes: none !important;
      }
      
      .ProseMirror blockquote::before,
      .ProseMirror blockquote::after,
      .ProseMirror .blockquote-element::before,
      .ProseMirror .blockquote-element::after {
        content: none !important;
        quotes: none !important;
      }
      
      .ProseMirror code,
      .ProseMirror .inline-code {
        background-color: hsl(var(--muted));
        color: hsl(var(--foreground));
        padding: 2px 4px;
        border-radius: 3px;
        font-family: Monaco, Menlo, Ubuntu Mono, monospace;
        font-size: 0.875em;
        quotes: none !important;
      }
      
      .ProseMirror code::before,
      .ProseMirror code::after,
      .ProseMirror .inline-code::before,
      .ProseMirror .inline-code::after {
        content: none !important;
        quotes: none !important;
      }
      
      .ProseMirror a {
        color: hsl(var(--primary));
        text-decoration: underline;
      }
      
      .ProseMirror a:hover {
        color: hsl(var(--primary));
        opacity: 0.8;
      }
    `;

    document.head.appendChild(style);

    // Keep the style as a singleton – do not remove it to prevent FOUC
    // when multiple RichTextEditor instances exist
    return () => {
      /* noop */
    };
  }, []);

  const isActive = (format: string) => {
    if (!editor) return false;

    switch (format) {
      case "bold":
        return editor.isActive("bold");
      case "italic":
        return editor.isActive("italic");
      case "strike":
        return editor.isActive("strike");
      case "code":
        return editor.isActive("code");
      case "blockquote":
        return editor.isActive("blockquote");
      case "bulletList":
        return editor.isActive("bulletList");
      case "orderedList":
        return editor.isActive("orderedList");
      case "heading":
        return editor.isActive("heading", { level: 2 });
      default:
        return false;
    }
  };

  const applyFormat = (format: string) => {
    if (!editor) return;

    switch (format) {
      case "bold":
        editor.chain().focus().toggleBold().run();
        break;
      case "italic":
        editor.chain().focus().toggleItalic().run();
        break;
      case "strike":
        editor.chain().focus().toggleStrike().run();
        break;
      case "code":
        editor.chain().focus().toggleCode().run();
        break;
      case "blockquote":
        editor.chain().focus().toggleBlockquote().run();
        break;
      case "bulletList":
        editor.chain().focus().toggleBulletList().run();
        break;
      case "orderedList":
        editor.chain().focus().toggleOrderedList().run();
        break;
      case "heading":
        editor.chain().focus().toggleHeading({ level: 2 }).run();
        break;
      default:
        return;
    }
  };

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium">
        Auto-response message
        <span className="text-red-500 ml-1">*</span>
      </Label>

      <div
        className={`border rounded-sm transition-all duration-200 ${
          error
            ? "border-red-500 shadow-sm"
            : isFocused
            ? "border-blue-500 shadow-sm ring-1 ring-blue-500/20"
            : "border-border hover:border-border/80"
        }`}
      >
        {/* Toolbar */}
        <div className="flex items-center gap-1 p-2 border-b bg-muted/30">
          <div className="flex items-center gap-0.5">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => applyFormat("bold")}
              className={`h-8 w-8 p-0 ${isActive("bold") ? "bg-muted" : ""}`}
              title="Bold"
            >
              <Bold className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => applyFormat("italic")}
              className={`h-8 w-8 p-0 ${isActive("italic") ? "bg-muted" : ""}`}
              title="Italic"
            >
              <Italic className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => applyFormat("strike")}
              className={`h-8 w-8 p-0 ${isActive("strike") ? "bg-muted" : ""}`}
              title="Strikethrough"
            >
              <Strikethrough className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => applyFormat("code")}
              className={`h-8 w-8 p-0 ${isActive("code") ? "bg-muted" : ""}`}
              title="Code"
            >
              <Code className="h-4 w-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          <div className="flex items-center gap-0.5">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => applyFormat("heading")}
              className={`h-8 w-8 p-0 ${isActive("heading") ? "bg-muted" : ""}`}
              title="Heading"
            >
              <Type className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => applyFormat("bulletList")}
              className={`h-8 w-8 p-0 ${
                isActive("bulletList") ? "bg-muted" : ""
              }`}
              title="Bullet List"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => applyFormat("orderedList")}
              className={`h-8 w-8 p-0 ${
                isActive("orderedList") ? "bg-muted" : ""
              }`}
              title="Numbered List"
            >
              <ListOrdered className="h-4 w-4" />
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => applyFormat("blockquote")}
              className={`h-8 w-8 p-0 ${
                isActive("blockquote") ? "bg-muted" : ""
              }`}
              title="Quote"
            >
              <Quote className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex-1" />
        </div>

        {/* Editor */}
        <div className="relative">
          <EditorContent editor={editor} />
        </div>
      </div>

      <p className="text-xs text-muted-foreground">
        Rich formatting is supported including <strong>bold</strong>, <em>italic</em>, lists, and more.
      </p>
    </div>
  );
}
