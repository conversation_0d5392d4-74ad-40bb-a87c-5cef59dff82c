import ThenaLoader from "@/components/thena-loader";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { THENA_SLACK_APP_URL } from "@/config/constant";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Edit, Eye, Sparkles } from "lucide-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { SourceDetails } from "../page";

interface Prompt {
  id: string;
  name: string;
  prompts: {
    ticket_detection: string;
    sentiment_analysis: string;
    urgency_detection: string;
    // custom_fields: string;
    title_generation: string;
    description_generation: string;
  };
  isEnabled: boolean;
}

interface AIPromptsProps {
  sourceDetails: SourceDetails;
  workspaceId: string;
}

type PromptKey = keyof Prompt["prompts"];

export function AIPrompts({ sourceDetails, workspaceId }: AIPromptsProps) {
  const params = useParams();
  const teamId = params?.teamId as string;
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [originalPrompt, setOriginalPrompt] = useState<Prompt | null>(null);
  const [editingPrompt, setEditingPrompt] = useState<PromptKey | null>(null);
  const [viewingPrompt, setViewingPrompt] = useState<PromptKey | null>(null);

  // Fetch prompts
  const { isLoading, refetch, error } = useQuery({
    queryKey: ["ai-prompts", teamId, workspaceId],
    queryFn: async () => {
      try {
        const response = await fetch(`${THENA_SLACK_APP_URL}/v1/prompts/teams/${teamId}`, {
          headers: {
            "x-slack-id": workspaceId,
            "x-auth-token": sourceDetails.key,
          },
       });

        if (!response.ok) {
          console.error(
            "Failed to fetch AI prompts:",
            response.status,
            response.statusText,
          );
          throw new Error(`Failed to fetch AI prompts: ${response.status}`);
        }

        const data = await response.json();
        console.log("Prompts API response:", data);

        if (data.ok && data.data.length > 0) {
          setPrompt(data.data[0]);
          setOriginalPrompt(data.data[0]);
          return data.data[0];
        } else {
          console.error("No prompts found in response:", data);
          throw new Error("No prompts found");
        }
      } catch (error) {
        console.error("Error fetching prompts:", error);
        toast.error("Failed to load AI prompts");
        return null;
      }
    },
    enabled: !!teamId && !!workspaceId && !!sourceDetails?.key,
  });

  // Update prompts
  const { mutate: updatePrompt, isPending: isUpdating } = useMutation({
    mutationFn: async (updatedPrompt: Prompt) => {
      try {
        const response = await fetch(
          `${THENA_SLACK_APP_URL}/v1/prompts/${updatedPrompt.id}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              "x-slack-id": workspaceId,
              "x-auth-token": sourceDetails.key,
            },
            body: JSON.stringify({
              name: updatedPrompt.name,
              prompts: updatedPrompt.prompts,
              isEnabled: updatedPrompt.isEnabled,
            }),
          },
        );

        if (!response.ok) {
          throw new Error("Failed to update AI prompts");
        }

        const data = await response.json();

        if (data.ok) {
          setPrompt(data.data);
          setOriginalPrompt(data.data);
          toast.success("AI prompts updated successfully");
          return data.data;
        } else {
          throw new Error("Failed to update prompts");
        }
      } catch (error) {
        console.error("Error updating prompts:", error);
        toast.error("Failed to update AI prompts");
        throw error;
      }
    },
    onSuccess: () => {
      refetch();
      setEditingPrompt(null);
    },
  });

  const handlePromptChange = (key: PromptKey, value: string) => {
    if (!prompt) return;

    const updatedPrompt = {
      ...prompt,
      prompts: {
        ...prompt.prompts,
        [key]: value,
      },
    };

    setPrompt(updatedPrompt);
  };

  const handleSavePrompt = () => {
    if (!prompt) return;
    updatePrompt(prompt);
  };

  const handleCancelEdit = () => {
    if (originalPrompt) {
      setPrompt(originalPrompt);
    }
    setEditingPrompt(null);
  };

  const handleCancelView = () => {
    setViewingPrompt(null);
  };

  const hasChanges = () => {
    if (!prompt || !originalPrompt || !editingPrompt) return false;

    return (
      prompt.prompts[editingPrompt] !== originalPrompt.prompts[editingPrompt]
    );
  };

  const getPromptDisplayName = (key: PromptKey): string => {
    const displayNames: Record<PromptKey, string> = {
      ticket_detection: "Ticket detection",
      title_generation: "Title generation",
      description_generation: "Description generation",
      sentiment_analysis: "Sentiment auto-fill",
      urgency_detection: "Urgency auto-fill",
      // custom_fields: "Ticket custom fields auto-fill",
    };

    return displayNames[key];
  };

  if (isLoading) {
    return <ThenaLoader />;
  }

  if (!prompt) {
    return (
      <div className="space-y-4">
        <div className="p-4 text-center border rounded-[4px]">
          <p className="text-muted-foreground">
            No AI prompts configuration found.
          </p>
          <Button
            className="mt-4"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            {isLoading ? "Loading..." : "Retry loading prompts"}
          </Button>
          {error && (
            <p className="mt-2 text-sm text-destructive">
              Error: {error instanceof Error ? error.message : "Unknown error"}
            </p>
          )}
        </div>
      </div>
    );
  }

  const promptKeys: PromptKey[] = [
    "ticket_detection",
    "title_generation",
    "description_generation",
    "sentiment_analysis",
    "urgency_detection",
    // "custom_fields",
  ];

  return (
    <div className="space-y-6">
      {editingPrompt ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Label className="text-base">
              {getPromptDisplayName(editingPrompt)}
            </Label>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleCancelEdit}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSavePrompt}
                disabled={isUpdating || !hasChanges()}
              >
                {isUpdating ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>

          <Textarea
            value={prompt.prompts[editingPrompt]}
            onChange={(e) => handlePromptChange(editingPrompt, e.target.value)}
            className="min-h-[200px]"
            placeholder={`Enter prompt for ${getPromptDisplayName(
              editingPrompt,
            ).toLowerCase()}`}
          />
        </div>
      ) : viewingPrompt ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Label className="text-base">
              {getPromptDisplayName(viewingPrompt)}
            </Label>
            <Button variant="outline" onClick={handleCancelView}>
              Close
            </Button>
          </div>

          <div className="p-4 border rounded-[4px] bg-muted/30 whitespace-pre-wrap min-h-[200px]">
            {prompt.prompts[viewingPrompt]}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <Label className="text-base">AI features</Label>
          <div className="space-y-2">
            {promptKeys.map((key) => (
              <div
                key={key}
                className="flex items-center justify-between p-3 border rounded-[4px] hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-muted-foreground" />
                  <span>{getPromptDisplayName(key)}</span>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setViewingPrompt(key)}
                    title="View prompt"
                    className="h-8 w-8"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setEditingPrompt(key)}
                    title="Edit prompt"
                    className="h-8 w-8"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
