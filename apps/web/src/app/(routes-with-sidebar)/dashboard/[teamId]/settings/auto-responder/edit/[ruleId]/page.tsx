"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Activity, Event } from "@/types/workflow";
import {
  ChevronLeft,
  Clock,
  FileText,
  Filter,
  MessageSquare,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { FilterBuilder } from "../../components/FilterBuilder";
import { RichTextEditor } from "../../components/RichTextEditor";

interface MessageContent {
  content: string;
  contentHtml: string;
  contentJson: string;
}

interface Rule {
  name: string;
  trigger: string;
  considerations: string[];
  filters: { "~and": FilterCondition[] };
  message: MessageContent;
  enabled: boolean;
  uniqueIdentifier: string;
}

interface ApiRule {
  uid: string;
  name: string;
  isActive: boolean;
  triggerEvent: {
    eventType: string;
  };
  metadata: {
    considerations: string[];
    filters: {
      "~and": Record<string, unknown>[];
    };
    message: MessageContent;
    trigger: string;
  };
  uniqueIdentifier: string;
  version: number;
  subType: string;
}

interface WorkflowPayload {
  name: string;
  teamId: string;
  type: string;
  subType: string;
  triggerEvent: string;
  workflowDefinition: WorkflowStep[];
  filters: {
    "~and": FilterCondition[];
  };
  metadata: {
    considerations: string[];
    filters: Record<string, unknown>[];
    message: MessageContent;
    trigger: string;
  };
}

interface WorkflowStep {
  stepIdentifier: number;
  activity: {
    uniqueIdentifier: string;
    autoUpgradeToLatestVersion: boolean;
  };
  input: Record<string, unknown>;
  dependencies?: number[];
  filters?: Record<string, unknown>;
  onFailure?: string;
}

interface FilterCondition {
  [key: string]: {
    [operator: string]: unknown;
  };
}

interface FilterOrCondition {
  "~and": Array<{
    [key: string]: {
      [operator: string]: unknown;
    };
  }>;
}

const DEFAULT_RULE: Rule = {
  name: "",
  trigger: "ticket_created",
  considerations: [],
  filters: { "~and": [] },
  message: {
    content: "",
    contentHtml: "",
    contentJson: "",
  },
  enabled: true,
  uniqueIdentifier: "",
};

export default function EditAutoResponderRule() {
  const router = useRouter();
  const params = useParams();
  const teamId = params.teamId as string;
  const ruleId = params.ruleId as string;

  const [rule, setRule] = useState<Rule>(DEFAULT_RULE);
  const [events, setEvents] = useState<Event[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isLoadingRule, setIsLoadingRule] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  const [errors, setErrors] = useState<{
    name?: string;
    trigger?: string;
    message?: string;
  }>({});

  const [touched, setTouched] = useState<{
    name: boolean;
    message: boolean;
  }>({
    name: false,
    message: false,
  });

  const backendToLogical: Record<string, string> = {
    "=": "~eq",
    "!=": "~neq",
    in: "~in",
    "not in": "~nin",
    contains: "~contains",
    "not contains": "~ncontains",
    ">": "~gt",
    ">=": "~gte",
    "<": "~lt",
    "<=": "~lte",
    "is null": "~isNull",
    "is not null": "~isNotNull",
    "is true": "~isTrue",
    "is false": "~isFalse",
  };

  const [teamHolidays, setTeamHolidays] = useState<
    { date: string; label: string }[]
  >([]);
  const [selectedHolidays, setSelectedHolidays] = useState<string[]>([]);

  const handleChange = (field: string, value: unknown) => {
    if (field === "message") {
      setRule((prev: Rule) => ({
        ...prev,
        message: value as MessageContent,
      }));
    } else if (field === "trigger") {
      setRule((prev: Rule) => ({
        ...prev,
        trigger: value as string,
        considerations: [],
      }));
    } else {
      setRule((prev: Rule) => ({
        ...prev,
        [field]: value,
      }));
    }

    // Clear error when field is changed
    if (errors[field as keyof typeof errors]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }

    // Mark field as touched
    if (field === "name" || field === "message") {
      setTouched((prev) => ({
        ...prev,
        [field]: true,
      }));
    }
  };

  const handleConsiderationChange = (value: string[]) => {
    setRule((prev: Rule) => ({
      ...prev,
      considerations: value,
    }));
  };

  const validateForm = () => {
    const newErrors: typeof errors = {};

    if (!rule.name.trim()) {
      newErrors.name = "Rule name is required.";
    }

    if (!rule.trigger) {
      newErrors.trigger = "Trigger is required.";
    }

    if (!rule.message.content.trim() && !rule.message.contentHtml.trim()) {
      newErrors.message = "Message is required.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateWorkflow = async (payload: WorkflowPayload) => {
    const response = await fetch(
      `/api/teams/${teamId}/workflows/${rule.uniqueIdentifier}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      },
    );

    if (!response.ok) {
      let message = "Failed to update workflow";
      try {
        const errorPayload = await response.json();
        if (errorPayload?.error) message = errorPayload.error;
      } catch (_) {
        /* non-JSON response – fall back to default message */
      }
      throw new Error(message);
    }

    return response.json();
  };

  const getWorkflowDefinition = (
    pathToAnnotate: string,
    filtersOr: FilterOrCondition[],
  ): WorkflowStep[] => {
    if (rule.trigger === "ticket_created") {
      return [
        ...(rule.considerations as string[]).map((consideration, index) => {
          const activity = activities.find((act) => {
            switch (consideration) {
              case "nonWorkingHours":
              case "groupUnavailableWorkingHours":
              case "holidays":
              case "groupUnavailableHolidays":
                return (
                  act.uniqueIdentifier ===
                  "teams:check-team-availability-platform"
                );
              case "memberUnavailableTimeOff":
              case "memberUnavailableWorkingHours":
                return (
                  act.uniqueIdentifier ===
                  "users:check-user-availability-platform"
                );
              default:
                return false;
            }
          });
          return {
            stepIdentifier: index + 1,
            activity: {
              uniqueIdentifier: activity?.uniqueIdentifier || "",
              autoUpgradeToLatestVersion: true,
            },
            input:
              consideration === "groupUnavailableWorkingHours" ||
              consideration === "groupUnavailableHolidays"
                ? {
                    teamId: `{{${pathToAnnotate}.subTeam.id}}`,
                    specificHolidays: [],
                  }
                : consideration === "memberUnavailableWorkingHours" ||
                  consideration === "memberUnavailableTimeOff"
                ? {
                    userId: `{{${pathToAnnotate}.assignedAgent.id}}`,
                  }
                : {
                    teamId: `{{${pathToAnnotate}.team.id}}`,
                    specificHolidays: selectedHolidays,
                  },
            onFailure: "CONTINUE",
          };
        }),
        {
          stepIdentifier: (rule.considerations as string[]).length + 1,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            ...rule.message,
            commentVisibility: "public",
            commentType: "comment",
            entityType: "ticket",
            entityId: `{{${pathToAnnotate}.id}}`,
            parentCommentId: "{{context.event.message.payload.comment.id}}",
            shouldSendEmail: true,
          },
          dependencies: (rule.considerations as string[]).map(
            (_, index) => index + 1,
          ),
          ...(filtersOr.length
            ? {
                filters: {
                  "~or": filtersOr,
                },
              }
            : {}),
        },
      ];
    } else {
      return [
        ...(rule.considerations as string[]).map((consideration, index) => {
          const activity = activities.find((act) => {
            switch (consideration) {
              case "nonWorkingHours":
              case "groupUnavailableWorkingHours":
              case "holidays":
              case "groupUnavailableHolidays":
                return (
                  act.uniqueIdentifier ===
                  "teams:check-team-availability-platform"
                );
              case "memberUnavailableTimeOff":
              case "memberUnavailableWorkingHours":
                return (
                  act.uniqueIdentifier ===
                  "users:check-user-availability-platform"
                );
              default:
                return false;
            }
          });
          return {
            stepIdentifier: index + 1,
            activity: {
              uniqueIdentifier: activity?.uniqueIdentifier || "",
              autoUpgradeToLatestVersion: true,
            },
            input:
              consideration === "groupUnavailableWorkingHours" ||
              consideration === "groupUnavailableHolidays"
                ? {
                    teamId: `{{${pathToAnnotate}.subTeam.id}}`,
                    specificHolidays: [],
                  }
                : consideration === "memberUnavailableWorkingHours" ||
                  consideration === "memberUnavailableTimeOff"
                ? {
                    userId: `{{${pathToAnnotate}.assignedAgent.id}}`,
                  }
                : {
                    teamId: `{{${pathToAnnotate}.team.id}}`,
                    specificHolidays: selectedHolidays,
                  },
            onFailure: "CONTINUE",
          };
        }),
        {
          stepIdentifier: (rule.considerations as string[]).length + 1,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            ...rule.message,
            commentVisibility: "public",
            commentType: "comment",
            entityType: "ticket",
            entityId: `{{${pathToAnnotate}.id}}`,
            parentCommentId: "{{context.event.message.payload.comment.id}}",
            shouldSendEmail: true,
          },
          dependencies: (rule.considerations as string[]).map(
            (_, index) => index + 1,
          ),
          filters: {
            "~and": [
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isempty": true,
                },
              },
              {
                "{{context.event.message.payload.comment.createdWithTicket}}": {
                  "~eq": false,
                },
              },
              ...(filtersOr.length ? [{ "~or": filtersOr }] : []),
            ],
          },
        },
        {
          stepIdentifier: (rule.considerations as string[]).length + 2,
          activity: {
            uniqueIdentifier: "communications:create-comment-platform",
            autoUpgradeToLatestVersion: true,
          },
          input: {
            ...rule.message,
            commentVisibility: "public",
            commentType: "comment",
            entityType: "ticket",
            entityId: `{{${pathToAnnotate}.id}}`,
            parentCommentId:
              "{{context.event.message.payload.comment.parentCommentId}}",
            shouldSendEmail: true,
          },
          dependencies: (rule.considerations as string[]).map(
            (_, index) => index + 1,
          ),
          filters: {
            "~and": [
              {
                "{{context.event.message.payload.comment.parentCommentId}}": {
                  "~isempty": false,
                },
              },
              {
                "{{context.event.message.payload.comment.createdWithTicket}}": {
                  "~eq": false,
                },
              },
              ...(filtersOr.length ? [{ "~or": filtersOr }] : []),
            ],
          },
        },
      ];
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      // Mark all fields as touched to show errors
      setTouched({
        name: true,
        message: true,
      });

      // Show toast error for missing fields
      const missingFields = [];
      if (!rule.name.trim()) missingFields.push("Rule name");
      if (!rule.trigger) missingFields.push("Trigger");
      if (!rule.message.content.trim() && !rule.message.contentHtml.trim())
        missingFields.push("Auto-response message");

      toast.error(
        `Please fill in the required fields: ${missingFields.join(", ")}`,
      );
      return;
    }

    try {
      setIsSaving(true);

      // Get pathToAnnotate from event metadata like original
      const pathToAnnotate =
        rule.trigger === "ticket_created"
          ? events.find((item) => item.eventType === "ticket:created")?.metadata
              ?.pathToAnnotate
          : events.find((item) => item.eventType === "ticket:comment:added")
              ?.metadata?.pathToAnnotate;

      if (!pathToAnnotate) {
        toast.error(
          "Failed to resolve event metadata. Please try again or contact support.",
        );
        setIsSaving(false);
        return;
      }

      const formattedFilters = rule.filters?.["~and"] ?? [];

      // Build filtersOr dynamically based on considerations like original
      const filtersOr: FilterOrCondition[] = [];
      (rule.considerations as string[]).forEach((consideration, idx) => {
        if (
          consideration === "nonWorkingHours" ||
          consideration === "groupUnavailableWorkingHours"
        ) {
          filtersOr.push({
            "~and": [
              {
                [`{{context.outputs.${idx + 1}.isAvailable}}`]: {
                  "~eq": false,
                },
              },
              {
                [`{{context.outputs.${idx + 1}.reason}}`]: {
                  "~eq": "OUTSIDE_BUSINESS_HOURS",
                },
              },
            ],
          });
        }
        if (
          consideration === "holidays" ||
          consideration === "groupUnavailableHolidays"
        ) {
          filtersOr.push({
            "~and": [
              {
                [`{{context.outputs.${idx + 1}.isAvailable}}`]: {
                  "~eq": false,
                },
              },
              {
                [`{{context.outputs.${idx + 1}.reason}}`]: { "~eq": "HOLIDAY" },
              },
            ],
          });
        }
        if (consideration === "memberUnavailableWorkingHours") {
          filtersOr.push({
            "~and": [
              {
                [`{{context.outputs.${idx + 1}.isAvailable}}`]: {
                  "~eq": false,
                },
              },
              {
                [`{{context.outputs.${idx + 1}.reason}}`]: {
                  "~eq": "OUTSIDE_BUSINESS_HOURS",
                },
              },
            ],
          });
        }
        if (consideration === "memberUnavailableTimeOff") {
          filtersOr.push({
            "~and": [
              {
                [`{{context.outputs.${idx + 1}.isAvailable}}`]: {
                  "~eq": false,
                },
              },
              {
                [`{{context.outputs.${idx + 1}.reason}}`]: {
                  "~eq": "TIME_OFF",
                },
              },
            ],
          });
        }
      });

      // Use original payload structure exactly
      const payload = {
        name: rule.name,
        teamId: teamId,
        type: "MANUAL",
        subType: "AUTO_RESPONDER",
        triggerEvent:
          rule.trigger === "ticket_created"
            ? events.find((item) => item.eventType === "ticket:created")?.uid
            : events.find((item) => item.eventType === "ticket:comment:added")
                ?.uid,
        workflowDefinition: getWorkflowDefinition(pathToAnnotate, filtersOr),
        filters:
          rule.trigger === "ticket_created"
            ? {
                "~and": formattedFilters.map((f) => {
                  const field = Object.keys(f)[0];
                  const operator = Object.keys(f[field])[0];
                  const value = f[field][operator];
                  const operatorKey =
                    backendToLogical[operator] ||
                    (operator.startsWith("~") ? operator : `~${operator}`);
                  return {
                    [`{{${pathToAnnotate}.${field}}}`]: {
                      [operatorKey]: value,
                    },
                  };
                }),
              }
            : {
                "~and": [
                  {
                    "{{context.event.message.payload.comment.customerContact}}":
                      {
                        "~isempty": false,
                      },
                  },
                  ...formattedFilters.map((f) => {
                    const field = Object.keys(f)[0];
                    const operator = Object.keys(f[field])[0];
                    const value = f[field][operator];
                    const operatorKey =
                      backendToLogical[operator] ||
                      (operator.startsWith("~") ? operator : `~${operator}`);
                    return {
                      [`{{${pathToAnnotate}.${field}}}`]: {
                        [operatorKey]: value,
                      },
                    };
                  }),
                ],
              },
        metadata: {
          considerations: rule.considerations,
          filters: rule.filters["~and"] || [],
          message: rule.message,
          trigger: rule.trigger,
          holidays: rule.considerations.includes("holidays")
            ? selectedHolidays
            : [],
        },
      };

      await handleUpdateWorkflow(payload);

      toast.success("Auto-responder rule updated successfully");
    } catch (error) {
      console.error("Error updating rule:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update rule",
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleBlur = (field: string) => {
    if (field === "name" || field === "message") {
      setTouched((prev) => ({
        ...prev,
        [field]: true,
      }));

      // Validate on blur
      if (field === "name" && !String(rule.name).trim()) {
        setErrors((prev) => ({
          ...prev,
          name: "Rule name is required.",
        }));
      }

      if (
        field === "message" &&
        !rule.message.content.trim() &&
        !rule.message.contentHtml.trim()
      ) {
        setErrors((prev) => ({
          ...prev,
          message: "Message is required.",
        }));
      }
    }
  };

  // Fetch existing rule data
  useEffect(() => {
    if (!teamId || !ruleId) {
      toast.error("Team ID and Rule ID are required");
      return;
    }

    let isMounted = true;

    async function fetchRule() {
      try {
        setIsLoadingRule(true);
        const response = await fetch(`/api/teams/${teamId}/workflows`);

        if (!isMounted) return;

        if (!response.ok) {
          throw new Error("Failed to fetch rules");
        }

        const data = await response.json();

        if (!isMounted) return;

        if (data.data && Array.isArray(data.data)) {
          const apiRule = data.data.find(
            (r: ApiRule) => r.uniqueIdentifier === ruleId,
          );

          if (apiRule) {
            // Ensure we have a properly formatted filters structure
            let filterArray: Record<string, unknown>[] = [];

            // Safely extract filters from the metadata
            if (apiRule.metadata && apiRule.metadata.filters) {
              // Make sure filters is an array
              if (Array.isArray(apiRule.metadata.filters)) {
                filterArray = apiRule.metadata.filters.filter(
                  (filter) => filter !== null && typeof filter === "object",
                );
              }
            }

            // Transform API rule to our rule format
            const transformedRule: Rule = {
              name: apiRule.name,
              trigger:
                apiRule.metadata?.trigger ||
                (apiRule.triggerEvent?.eventType === "ticket:created"
                  ? "ticket_created"
                  : "message_received"),
              considerations: apiRule.metadata?.considerations || [],
              filters: { "~and": filterArray as FilterCondition[] },
              message: apiRule.metadata?.message || {
                content: "",
                contentHtml: "",
                contentJson: "",
              },
              enabled: apiRule.isActive,
              uniqueIdentifier: apiRule.uniqueIdentifier,
            };

            setRule(transformedRule);
          } else {
            throw new Error("Rule not found");
          }
        }
      } catch (error) {
        console.error("Error fetching rule:", error);
        if (isMounted) {
          toast.error("Failed to load rule data");
          router.push(`/dashboard/${teamId}/settings/auto-responder`);
        }
      } finally {
        if (isMounted) {
          setIsLoadingRule(false);
        }
      }
    }

    fetchRule();

    return () => {
      isMounted = false;
    };
  }, [teamId, ruleId, router]);

  // Fetch workflow data
  useEffect(() => {
    if (!teamId) {
      toast.error("Team ID is required");
      return;
    }

    let isMounted = true;

    async function fetchData() {
      try {
        setIsLoadingData(true);
        const [eventsResponse, activitiesResponse] = await Promise.all([
          fetch(`/api/teams/${teamId}/workflows/registry/events`),
          fetch(`/api/teams/${teamId}/workflows/registry/activities`),
        ]);

        if (!isMounted) return;

        if (!eventsResponse.ok || !activitiesResponse.ok) {
          throw new Error("Failed to fetch workflow data");
        }

        const [eventsData, activitiesData] = await Promise.all([
          eventsResponse.json(),
          activitiesResponse.json(),
        ]);

        if (!isMounted) return;

        if (Array.isArray(eventsData.results)) {
          setEvents(eventsData.results);
        } else {
          console.error("Events data is not an array:", eventsData);
        }

        if (Array.isArray(activitiesData.results)) {
          setActivities(activitiesData.results);
        } else {
          console.error("Activities data is not an array:", activitiesData);
        }
      } catch (error) {
        console.error("Error fetching workflow data:", error);
        if (isMounted) {
          toast.error("Failed to load workflow data");
        }
      } finally {
        if (isMounted) {
          setIsLoadingData(false);
        }
      }
    }

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [teamId]);

  useEffect(() => {
    if (!teamId) return;
    async function fetchHolidays() {
      try {
        const res = await fetch(`/api/teams/${teamId}/configurations`);
        if (!res.ok) throw new Error("Failed to fetch holidays");
        const data = await res.json();
        // Extract holidays from data.data.holidays and map to { date, label }
        const holidaysArr = data?.data?.holidays || [];
        const holidays = holidaysArr.map((date: string) => {
          const [day, month, year] = date.split("-");
          const jsDate = new Date(Number(year), Number(month) - 1, Number(day));
          return {
            date,
            label: jsDate.toLocaleDateString("en-GB", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            }), // e.g. 02 Jun 2025
          };
        });
        setTeamHolidays(holidays);
        setSelectedHolidays(holidays.map((h) => h.date));
      } catch (e) {
        console.log(e);
      }
    }
    fetchHolidays();
  }, [teamId]);

  const handleAddHoliday = (date: string) => {
    if (!selectedHolidays.includes(date)) {
      setSelectedHolidays((prev) => [...prev, date]);
    }
  };

  const handleRemoveHoliday = (date: string) => {
    setSelectedHolidays((prev) => prev.filter((d) => d !== date));
  };

  const handleAddMoreClick = () => {
    router.push(`/dashboard/${teamId}/settings/working-hours`);
  };

  const hasNoConsiderations = rule.considerations.length === 0;

  useEffect(() => {
    if (rule.considerations.includes("holidays")) {
      setRule((prev) => ({ ...prev, selectedHolidays }));
    }
    // Only depend on selectedHolidays and rule.considerations
  }, [selectedHolidays, rule.considerations]);

  if (isLoadingRule || isLoadingData) {
    return (
      <main className="flex-1 overflow-y-auto bg-background">
        <div className="container mx-auto py-8">
          <div className="max-w-[640px] mx-auto">
            <div className="flex items-center justify-center min-h-[640px]">
              <ThenaLoader />
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="flex-1 overflow-y-auto bg-background">
      <div className="container mx-auto py-8">
        <div className="max-w-[640px] mx-auto">
          <div className="flex flex-col space-y-8">
            <div className="space-y-2">
              <h1 className="text-2xl font-medium tracking-tight">
                Edit auto-responder rule
              </h1>
              <p className="text-sm text-muted-foreground">
                Update when and how automatic responses should be sent.
              </p>
            </div>

            <div className="space-y-6">
              <Accordion type="single" collapsible className="w-full space-y-4">
                <AccordionItem
                  value="overview"
                  className="rounded-sm border overflow-hidden"
                >
                  <AccordionTrigger className="px-4 py-3 bg-background hover:bg-muted/30 text-base font-medium">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-blue-500" />
                      Overview
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="border-t">
                    <div className="p-4">
                      <div className="space-y-2">
                        <Label
                          htmlFor="rule-name"
                          className="flex items-center gap-1 text-sm font-medium"
                        >
                          Rule name
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="rule-name"
                          placeholder="Give your rule a descriptive name"
                          className="max-w-full"
                          value={rule.name}
                          onChange={(e) => handleChange("name", e.target.value)}
                          onBlur={() => handleBlur("name")}
                          required
                        />
                        {errors.name && touched.name && (
                          <p className="text-sm text-red-500">
                            Rule name is required.
                          </p>
                        )}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="trigger-conditions"
                  className="rounded-sm border overflow-hidden"
                >
                  <AccordionTrigger className="px-4 py-3 bg-background hover:bg-muted/30 text-base font-medium">
                    <div className="flex flex-col items-start gap-2 w-full">
                      <div className="flex items-center gap-3">
                        <Clock className="h-5 w-5 text-green-500" />
                        Trigger and conditions
                      </div>
                      {!hasNoConsiderations && (
                        <div className="flex flex-wrap gap-1 ml-8">
                          {rule.considerations.map((consideration) => (
                            <Badge
                              key={consideration}
                              variant="outline"
                              className="bg-slate-100"
                            >
                              {consideration === "nonWorkingHours" &&
                                "Outside business hours"}
                              {consideration === "holidays" &&
                                "During holidays"}
                              {consideration === "memberUnavailableTimeOff" &&
                                "Member on leave"}
                              {consideration ===
                                "memberUnavailableWorkingHours" &&
                                "Member unavailable"}
                              {consideration ===
                                "groupUnavailableWorkingHours" &&
                                "Group unavailable"}
                              {consideration === "groupUnavailableHolidays" &&
                                "Group holidays"}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="border-t">
                    <div className="p-4 space-y-6">
                      <div className="space-y-2">
                        <Label
                          htmlFor="trigger"
                          className="flex items-center gap-1 text-sm font-medium"
                        >
                          When should this auto-responder activate?
                          <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          value={rule.trigger}
                          onValueChange={(value) =>
                            handleChange("trigger", value)
                          }
                        >
                          <SelectTrigger className="max-w-full h-auto min-h-[60px] py-3">
                            <SelectValue placeholder="Choose when to send automatic responses">
                              {rule.trigger === "ticket_created" && (
                                <div className="flex flex-col items-start text-left">
                                  <span className="font-medium text-foreground">
                                    When a ticket is created
                                  </span>
                                  <span className="text-sm text-muted-foreground mt-0.5">
                                    Send an automatic response when customers
                                    create new tickets.
                                  </span>
                                </div>
                              )}
                              {rule.trigger === "message_received" && (
                                <div className="flex flex-col items-start text-left">
                                  <span className="font-medium text-foreground">
                                    When a message is received
                                  </span>
                                  <span className="text-sm text-muted-foreground mt-0.5">
                                    Send an automatic response when customers
                                    message on assigned tickets.
                                  </span>
                                </div>
                              )}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem
                              value="ticket_created"
                              className="py-3 h-auto"
                            >
                              <div className="flex flex-col items-start gap-1">
                                <span className="font-medium">
                                  When a ticket is created
                                </span>
                                <span className="text-sm text-muted-foreground">
                                  Send an automatic response when customers
                                  create new tickets.
                                </span>
                              </div>
                            </SelectItem>
                            <SelectItem
                              value="message_received"
                              className="py-3 h-auto"
                            >
                              <div className="flex flex-col items-start gap-1">
                                <span className="font-medium">
                                  When a message is received
                                </span>
                                <span className="text-sm text-muted-foreground">
                                  Send an automatic response when customers
                                  message on assigned tickets.
                                </span>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.trigger && (
                          <p className="text-sm text-red-500">
                            Please select when this rule should trigger.
                          </p>
                        )}
                      </div>

                      <div className="space-y-4">
                        <div className="space-y-1">
                          <Label className="text-sm font-medium">
                            Application conditions (optional)
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            If no conditions are selected, responses will be
                            sent at all times.
                          </p>
                        </div>

                        <div className="space-y-4">
                          <div className="flex items-start space-x-3">
                            <Checkbox
                              id="nonWorkingHours"
                              checked={rule.considerations.includes(
                                "nonWorkingHours",
                              )}
                              onCheckedChange={(checked) => {
                                const current = rule.considerations;
                                const updated = checked
                                  ? [...current, "nonWorkingHours"]
                                  : current.filter(
                                      (c) => c !== "nonWorkingHours",
                                    );
                                handleConsiderationChange(updated);
                              }}
                              className="mt-0.5"
                            />
                            <div className="grid gap-1.5 leading-none">
                              <Label
                                htmlFor="nonWorkingHours"
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                              >
                                Outside business hours
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                Only respond when team is not working.
                              </p>
                            </div>
                          </div>

                          <div className="flex items-start space-x-3">
                            <Checkbox
                              id="holidays"
                              checked={rule.considerations.includes("holidays")}
                              onCheckedChange={(checked) => {
                                const current = rule.considerations;
                                const updated = checked
                                  ? [...current, "holidays"]
                                  : current.filter((c) => c !== "holidays");
                                handleConsiderationChange(updated);
                                if (checked) {
                                  setSelectedHolidays(
                                    (teamHolidays || []).map((h) => h.date),
                                  );
                                } else {
                                  setSelectedHolidays([]);
                                }
                              }}
                              className="mt-0.5"
                            />
                            <div className="grid gap-1.5 leading-none">
                              <Label
                                htmlFor="holidays"
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                              >
                                During holidays
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                Only respond during team holidays.
                              </p>
                            </div>
                          </div>
                          {rule.considerations.includes("holidays") && (
                            <div className="!mt-2">
                              <Label className="text-sm font-medium ml-7 block">
                                Configure specific holidays
                              </Label>
                              <div className="flex flex-wrap mb-4 ml-7 mt-3">
                                {selectedHolidays.map((date) => {
                                  const holiday = (teamHolidays || []).find(
                                    (h) => h.date === date,
                                  );
                                  return (
                                    <div
                                      key={date}
                                      className="flex items-center bg-slate-100 rounded-sm px-3 py-1 text-xs font-medium mr-2 mb-2 shadow-sm"
                                      tabIndex={0}
                                      aria-label={`Holiday: ${
                                        holiday?.label || date
                                      }`}
                                    >
                                      <span>{holiday?.label || date}</span>
                                      <button
                                        type="button"
                                        onClick={() =>
                                          handleRemoveHoliday(date)
                                        }
                                        className="ml-2 text-gray-500 hover:text-red-500 focus:outline-none"
                                        aria-label={`Remove ${
                                          holiday?.label || date
                                        }`}
                                        tabIndex={0}
                                        onKeyDown={(e) => {
                                          if (
                                            e.key === "Enter" ||
                                            e.key === " "
                                          )
                                            handleRemoveHoliday(date);
                                        }}
                                      >
                                        ×
                                      </button>
                                    </div>
                                  );
                                })}
                                {/* Select dropdown for adding holidays, now with Add more inside */}
                                <Select
                                  value=""
                                  onValueChange={handleAddHoliday}
                                >
                                  <SelectTrigger className="w-[140px] border border-border rounded-sm px-2 py-1 text-sm h-6">
                                    <SelectValue placeholder="Select" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {(teamHolidays || [])
                                      .filter(
                                        (h) =>
                                          !selectedHolidays.includes(h.date),
                                      )
                                      .map((h) => (
                                        <SelectItem
                                          key={h.date}
                                          value={h.date}
                                          className="py-1 px-2 text-sm"
                                        >
                                          {h.label}
                                        </SelectItem>
                                      ))}
                                    {/* Add more button inside dropdown */}
                                    <div
                                      className="py-1 px-2 text-sm rounded-sm cursor-pointer hover:bg-gray-100 border-gray-200"
                                      tabIndex={0}
                                      role="button"
                                      aria-label="Add more holidays"
                                      onClick={handleAddMoreClick}
                                      onKeyDown={(e) => {
                                        if (e.key === "Enter" || e.key === " ")
                                          handleAddMoreClick();
                                      }}
                                    >
                                      + Add more
                                    </div>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          )}
                          {rule.trigger !== "ticket_created" && (
                            <div className="flex items-start space-x-3">
                              <Checkbox
                                id="memberUnavailableWorkingHours"
                                checked={rule.considerations.includes(
                                  "memberUnavailableWorkingHours",
                                )}
                                onCheckedChange={(checked) => {
                                  const current = rule.considerations;
                                  const updated = checked
                                    ? [
                                        ...current,
                                        "memberUnavailableWorkingHours",
                                      ]
                                    : current.filter(
                                        (c) =>
                                          c !== "memberUnavailableWorkingHours",
                                      );
                                  handleConsiderationChange(updated);
                                }}
                                className="mt-0.5"
                              />
                              <div className="grid gap-1.5 leading-none">
                                <Label
                                  htmlFor="memberUnavailableWorkingHours"
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                >
                                  When assigned member is unavailable
                                </Label>
                                <p className="text-sm text-muted-foreground">
                                  Only respond when the assigned team member is
                                  not working.
                                </p>
                              </div>
                            </div>
                          )}

                          {rule.trigger !== "ticket_created" && (
                            <div className="flex items-start space-x-3">
                              <Checkbox
                                id="memberUnavailableTimeOff"
                                checked={rule.considerations.includes(
                                  "memberUnavailableTimeOff",
                                )}
                                onCheckedChange={(checked) => {
                                  const current = rule.considerations;
                                  const updated = checked
                                    ? [...current, "memberUnavailableTimeOff"]
                                    : current.filter(
                                        (c) => c !== "memberUnavailableTimeOff",
                                      );
                                  handleConsiderationChange(updated);
                                }}
                                className="mt-0.5"
                              />
                              <div className="grid gap-1.5 leading-none">
                                <Label
                                  htmlFor="memberUnavailableTimeOff"
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                >
                                  When assigned member is on leave
                                </Label>
                                <p className="text-sm text-muted-foreground">
                                  Only respond when the assigned team member is
                                  on time off.
                                </p>
                              </div>
                            </div>
                          )}

                          {rule.trigger !== "ticket_created" && (
                            <div className="flex items-start space-x-3">
                              <Checkbox
                                id="groupUnavailableWorkingHours"
                                checked={rule.considerations.includes(
                                  "groupUnavailableWorkingHours",
                                )}
                                onCheckedChange={(checked) => {
                                  const current = rule.considerations;
                                  const updated = checked
                                    ? [
                                        ...current,
                                        "groupUnavailableWorkingHours",
                                      ]
                                    : current.filter(
                                        (c) =>
                                          c !== "groupUnavailableWorkingHours",
                                      );
                                  handleConsiderationChange(updated);
                                }}
                                className="mt-0.5"
                              />
                              <div className="grid gap-1.5 leading-none">
                                <Label
                                  htmlFor="groupUnavailableWorkingHours"
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                >
                                  When assigned group is unavailable
                                </Label>
                                <p className="text-sm text-muted-foreground">
                                  Only respond when the assigned group is not
                                  working.
                                </p>
                              </div>
                            </div>
                          )}

                          {rule.trigger !== "ticket_created" && (
                            <div className="flex items-start space-x-3">
                              <Checkbox
                                id="groupUnavailableHolidays"
                                checked={rule.considerations.includes(
                                  "groupUnavailableHolidays",
                                )}
                                onCheckedChange={(checked) => {
                                  const current = rule.considerations;
                                  const updated = checked
                                    ? [...current, "groupUnavailableHolidays"]
                                    : current.filter(
                                        (c) => c !== "groupUnavailableHolidays",
                                      );
                                  handleConsiderationChange(updated);
                                }}
                                className="mt-0.5"
                              />
                              <div className="grid gap-1.5 leading-none">
                                <Label
                                  htmlFor="groupUnavailableHolidays"
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                >
                                  During assigned group holidays
                                </Label>
                                <p className="text-sm text-muted-foreground">
                                  Only respond during the assigned group&apos;s
                                  holidays.
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="filters"
                  className="rounded-sm border overflow-hidden"
                >
                  <AccordionTrigger className="px-4 py-3 bg-background hover:bg-muted/30 text-base font-medium">
                    <div className="flex items-center gap-3">
                      <Filter className="h-5 w-5 text-purple-500" />
                      Advanced filters
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="border-t">
                    <div className="p-4 space-y-6">
                      <p className="text-sm text-muted-foreground">
                        Add advanced filtering rules to control when this
                        auto-responder should trigger.
                      </p>
                      <div className="space-y-6">
                        <div>
                          <div className="text-sm text-muted-foreground mb-4">
                            Match{" "}
                            <span className="font-medium text-foreground">
                              ALL
                            </span>{" "}
                            of the below conditions
                          </div>
                          <FilterBuilder
                            teamId={teamId}
                            filters={rule.filters["~and"] || []}
                            onAddFilter={(filter) => {
                              setRule((prev) => ({
                                ...prev,
                                filters: {
                                  "~and": [
                                    // Remove any filter with the same field (so you update, not duplicate)
                                    ...(prev.filters["~and"] || []).filter(
                                      (f) =>
                                        Object.keys(f)[0] !==
                                        Object.keys(filter)[0],
                                    ),
                                    filter as FilterCondition,
                                  ],
                                },
                              }));
                            }}
                            onRemoveFilter={(index) => {
                              setRule((prev) => {
                                // Get the current filters
                                const currentFilters =
                                  prev.filters["~and"] || [];

                                // Create a new array without the filter at the specified index
                                const updatedFilters = [
                                  ...currentFilters.slice(0, index),
                                  ...currentFilters.slice(index + 1),
                                ];

                                // Return the updated rule state
                                return {
                                  ...prev,
                                  filters: {
                                    "~and": updatedFilters,
                                  },
                                };
                              });
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="message"
                  className="rounded-sm border overflow-hidden"
                >
                  <AccordionTrigger className="px-4 py-3 bg-background hover:bg-muted/30 text-base font-medium">
                    <div className="flex items-center gap-3">
                      <MessageSquare className="h-5 w-5 text-orange-500" />
                      Auto-response message
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="border-t">
                    <div className="p-4 space-y-2">
                      <RichTextEditor
                        value={rule.message.contentHtml}
                        onChange={(value) => handleChange("message", value)}
                        onBlur={() => handleBlur("message")}
                        error={errors.message && touched.message}
                      />
                      {errors.message && touched.message && (
                        <p className="text-sm text-red-500">
                          Message is required.
                        </p>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={() =>
                  router.push(`/dashboard/${teamId}/settings/auto-responder`)
                }
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                Back
              </Button>
              <Button
                onClick={handleSave}
                disabled={isSaving || isLoadingData}
                className="min-w-[120px] flex items-center gap-2"
              >
                {isSaving ? (
                  <>
                    <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    <span>Saving...</span>
                  </>
                ) : (
                  <span>Save changes</span>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
