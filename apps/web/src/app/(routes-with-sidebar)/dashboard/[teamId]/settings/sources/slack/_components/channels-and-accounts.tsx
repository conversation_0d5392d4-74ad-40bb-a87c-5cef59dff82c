"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import {
  AlertTriangle,
  Filter,
  Hash,
  Headphones,
  Link2,
  Lock,
  Search,
  Users,
} from "lucide-react";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { HeroIcon } from "../../../../../../../../components/hero-icon";
import { THENA_SLACK_APP_URL } from "../../../../../../../../config/constant";
import { useTicketMetaStore } from "../../../../../../../../store/ticket-meta-store";
import { SourceDetails } from "../page";

interface Team {
  id: string;
  uid: string;
  installedBy: string;
  relationshipType: "secondary" | "primary";
  installations: {
    id: string;
    teamId: string;
    teamName: string;
  }[];
}

export enum ChannelType {
  CUSTOMER_CHANNEL = "customer_channel",
  INTERNAL_HELPDESK = "internal_helpdesk",
  TRIAGE_CHANNEL = "triage_channel",
}

export enum ChannelState {
  AVAILABLE = "available",
  PRIMARY = "primary",
  SECONDARY = "secondary",
}

interface Channel {
  id: string;
  name: string;
  channelId: string;
  slackCreatedAt: string;
  isBotActive: boolean;
  isBotJoined: boolean;
  channelType: ChannelType;
  isArchived: boolean;
  isPrivate: boolean;
  isShared: boolean;
  guestAreCustomers: boolean;
  createdAt: string;
  updatedAt: string;
  slackDeletedAt: string | null;
  lastBotLeftAt: string | null;
  deletedAt: string | null;
  teams: Team[];
}

const channelTypeLabels: Record<
  ChannelType,
  { label: string; className: string; icon: React.ReactNode }
> = {
  [ChannelType.CUSTOMER_CHANNEL]: {
    label: "Customer Channel",
    className:
      "bg-primary/10 text-primary border-primary/15 hover:bg-primary/15",
    icon: <Users className="h-3.5 w-3.5" />,
  },
  [ChannelType.INTERNAL_HELPDESK]: {
    label: "Internal Helpdesk",
    className:
      "bg-primary/10 text-primary border-primary/15 hover:bg-primary/15",
    icon: <Headphones className="h-3.5 w-3.5" />,
  },
  [ChannelType.TRIAGE_CHANNEL]: {
    label: "Triage Channel",
    className:
      "bg-primary/10 text-primary border-primary/15 hover:bg-primary/15",
    icon: <Filter className="h-3.5 w-3.5" />,
  },
};

const badgesToRender = [
  ChannelType.CUSTOMER_CHANNEL,
  ChannelType.INTERNAL_HELPDESK,
  ChannelType.TRIAGE_CHANNEL,
];

interface TabSelectedChannels {
  available: string[];
  primary: string[];
  secondary: string[];
}

export function ChannelsAndAccounts({
  sourceDetails,
  workspaceId,
}: {
  sourceDetails: SourceDetails;
  workspaceId: string;
}) {
  const params = useParams();
  const teamId = params?.teamId as string;

  const [channels, setChannels] = useState<Channel[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [showPrivate, setShowPrivate] = useState(true);
  const [selectedChannels, setSelectedChannels] = useState<TabSelectedChannels>(
    {
      available: [],
      primary: [],
      secondary: [],
    },
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingThena, setIsAddingThena] = useState(false);
  const [showTypeDialog, setShowTypeDialog] = useState(false);
  const [selectedType, setSelectedType] = useState<ChannelType | null>(null);
  const [selectedSecondaryTeam, setSelectedSecondaryTeam] = useState<
    string | null
  >(null);
  const [secondaryTeams, setSecondaryTeams] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [activeTab, setActiveTab] = useState<ChannelState>(
    ChannelState.AVAILABLE,
  );
  const loaderRef = useRef(null);
  const isFirstLoad = useRef(true);

  const { teamsMap, teams } = useTicketMetaStore();
  const [showDelayedLoader, setShowDelayedLoader] = useState(false);
  const loaderTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const getAvailabilityType = (tabValue: ChannelState) => {
    if (
      tabValue == ChannelState.PRIMARY ||
      tabValue == ChannelState.SECONDARY
    ) {
      return "configured";
    } else {
      return "available";
    }
  };

  const fetchSlackTeams = useCallback(async () => {
    if (
      activeTab != ChannelState.PRIMARY ||
      !teamId ||
      teams.length == 0 ||
      !showTypeDialog
    ) {
      return;
    }

    setIsLoading(true);

    try {
      const url = `${THENA_SLACK_APP_URL}/v1/slack/teams`;
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "x-auth-token": sourceDetails.key,
          "x-slack-id": workspaceId,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch slack teams");
      }

      const data = await response.json();
      const slackConfiguredTeams = data.data;

      if (!slackConfiguredTeams || slackConfiguredTeams?.length == 0) {
        return;
      }

      const filteredTeams = [];
      const slackConfiguredTeamIds = slackConfiguredTeams.map(
        (slackConfiguredTeam) => slackConfiguredTeam.uid,
      );

      teams.forEach((team) => {
        if (slackConfiguredTeamIds.includes(team.uid)) {
          filteredTeams.push(team);
        }
      });

      setSecondaryTeams(filteredTeams);
    } catch (error) {
      console.error("Error fetching slack teams:", error);
      toast.error("Failed to fetch slack teams");
    }
  }, [teamId, activeTab, teams, showTypeDialog]);

  const fetchChannels = useCallback(
    async (isLoadMore = false) => {
      if (!teamId || !workspaceId || (!isLoadMore && isLoading)) return;

      setIsLoading(true);
      loaderTimeoutRef.current = setTimeout(() => {
        setShowDelayedLoader(true);
      }, 900);
      try {
        let url = `${THENA_SLACK_APP_URL}/v1/slack/channel/${teamId}?page=${page}&limit=20&availabilityType=${getAvailabilityType(
          activeTab,
        )}&showExternalChannels=true&showPrivateChannels=true`;
        if (searchQuery) {
          url += `&searchQuery=${searchQuery}`;
        }

        const response = await fetch(url, {
          method: "GET",
          headers: {
            "x-auth-token": sourceDetails.key,
            "x-slack-id": workspaceId,
          },
        });

        const data = await response.json();
        const channels = data.data;
        if (!response.ok) {
          throw new Error("Failed to fetch channels");
        }

        if (activeTab === ChannelState.PRIMARY) {
          const primaryChannels = channels.filter(
            (channel) => channel.relationshipType == ChannelState.PRIMARY,
          );
          setChannels((prev) =>
            isLoadMore ? [...prev, ...primaryChannels] : primaryChannels,
          );
        } else if (activeTab === ChannelState.SECONDARY) {
          const secondaryChannels = channels.filter(
            (channel) =>
              channel.relationshipType == ChannelState.SECONDARY &&
              channel.isPrivate == false,
          );

          setChannels((prev) =>
            isLoadMore ? [...prev, ...secondaryChannels] : secondaryChannels,
          );
        } else {
          const availableChannels = channels.filter(
            (channel) => channel.isArchived === false,
          );
          setChannels((prev) =>
            isLoadMore ? [...prev, ...availableChannels] : availableChannels,
          );
        }
        setHasMore(channels.length === 20 && page < data.meta.totalPages);
      } catch (error) {
        console.error("Error fetching channels:", error);
        toast.error("Failed to fetch channels");
      } finally {
        setIsLoading(false);
        if (loaderTimeoutRef.current) {
          clearTimeout(loaderTimeoutRef.current);
          loaderTimeoutRef.current = null;
        }
        setShowDelayedLoader(false);
      }
    },
    [
      teamId,
      workspaceId,
      page,
      activeTab,
      isLoading,
      searchQuery,
      sourceDetails.key,
    ],
  );

  useEffect(() => {
    setPage(1);
    setChannels([]);
    fetchChannels();
  }, [searchQuery, showPrivate, activeTab, showTypeDialog]);

  useEffect(() => {
    if (showTypeDialog) {
      setSecondaryTeams([]);
      fetchSlackTeams();
    }
  }, [showTypeDialog]);

  useEffect(() => {
    if (isFirstLoad.current) {
      isFirstLoad.current = false;
      return;
    }
    fetchChannels(true);
  }, [page]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const first = entries[0];
        if (first.isIntersecting && hasMore && !isLoading) {
          setPage((p) => p + 1);
        }
      },
      { threshold: 1 },
    );

    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [hasMore, isLoading, fetchChannels, channels]);

  useEffect(() => {
    if (activeTab == ChannelState.PRIMARY) {
      if (!selectedChannels[activeTab].length) {
        setSelectedType(null);
        return;
      }

      const types = selectedChannels[activeTab]
        .map((id) => channels.find((c) => c.channelId === id)?.channelType)
        .filter(Boolean);

      const uniqueTypes = [...new Set(types)];

      if (uniqueTypes.length === 1) {
        setSelectedType(uniqueTypes[0] as ChannelType);
      } else {
        setSelectedType(null);
      }
    }
  }, [selectedChannels, activeTab, channels]);

  const handleChannelSelect = (channelId: string) => {
    setSelectedChannels((prev) => {
      const currentTabChannels = prev[activeTab];
      const newChannels = currentTabChannels.includes(channelId)
        ? currentTabChannels.filter((id) => id !== channelId)
        : [...currentTabChannels, channelId];

      return {
        ...prev,
        [activeTab]: newChannels,
      };
    });
  };

  const handleAddChannels = async () => {
    if (!selectedChannels[activeTab].length) {
      toast.error("Please select at least one channel");
      return;
    }

    const hasNonExternalChannels = selectedChannels[activeTab].some(
      (id) => !channels.find((c) => c.channelId === id)?.isShared,
    );

    if (hasNonExternalChannels) {
      setShowTypeDialog(true);
    } else {
      setSelectedType(ChannelType.CUSTOMER_CHANNEL);
      setTimeout(() => {
        handleAddThena();
      }, 0);
    }
  };

  const handleAddThena = async () => {
    if (!selectedType) return;

    setIsAddingThena(true);
    try {
      const response = await fetch(
        `${THENA_SLACK_APP_URL}/v1/slack/teams/map-channels`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-auth-token": sourceDetails.key,
            "x-slack-id": workspaceId,
          },
          body: JSON.stringify({
            teamId,
            channelIds: selectedChannels[activeTab],
            channelType: selectedType,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to add Thena");
      }

      const _data = await response.json();
      toast.success("Successfully added Thena");

      setActiveTab(ChannelState.PRIMARY);

      setTimeout(() => {
        fetchChannels();
      }, 1500);

      fetchChannels();

      setSelectedChannels((prev) => ({
        ...prev,
        [activeTab]: [],
      }));
      setSelectedType(null);
      setShowTypeDialog(false);
      setActiveTab(ChannelState.PRIMARY);
      setPage(1);
      await fetchChannels(); // optional: force fresh fetch
      setShowTypeDialog(false); // set dialog last to avoid triggering stale logic
      setIsAddingThena(false);
      setIsLoading(false);
    } catch (error) {
      console.error("Error adding Thena:", error);
      toast.error("Failed to add Thena");
    } finally {
      setIsAddingThena(false);
    }
  };

  const handleChangeTeamThena = async () => {
    if (!selectedSecondaryTeam) return;

    setIsAddingThena(true);

    try {
      const response = await fetch(
        `${THENA_SLACK_APP_URL}/v1/slack/teams/map-secondary-channels`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-auth-token": sourceDetails.key,
            "x-slack-id": workspaceId,
          },
          body: JSON.stringify({
            teamId,
            newTeamId: selectedSecondaryTeam,
            channelIds: selectedChannels[activeTab],
            channelType: selectedType,
          }),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to change team");
      }

      const _data = await response.json();
      toast.success("Successfully changed team");
      setSelectedChannels((prev) => ({
        ...prev,
        [activeTab]: [],
      }));
      setActiveTab(ChannelState.SECONDARY);
      await fetchChannels(); // optional: force fresh fetch
      setSelectedType(null);
      setShowTypeDialog(false); // set dialog last to avoid triggering stale logic
      setIsAddingThena(false);
      setSelectedSecondaryTeam(null);
      setIsLoading(false);
    } catch (error) {
      console.error("Error changing team:", error);
      toast.error("Failed to change team");
    } finally {
      setIsAddingThena(false);
    }
  };

  const handleRemoveThena = async () => {
    setIsAddingThena(true);
    try {
      const response = await fetch(
        `${THENA_SLACK_APP_URL}/v1/slack/teams/disconnect-channels`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            "x-auth-token": sourceDetails.key,
            "x-slack-id": workspaceId,
          },
          body: JSON.stringify({
            teamId,
            channelIds: selectedChannels[activeTab],
          }),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      toast.success(data.message || "Successfully removed Thena");

      setChannels((prev) =>
        prev.filter(
          (channel) => !selectedChannels[activeTab].includes(channel.id),
        ),
      );
      setSelectedChannels((prev) => ({
        ...prev,
        [activeTab]: [],
      }));

      await fetchChannels();
    } catch (error) {
      console.error("Error removing Thena:", error);
      toast.error("Failed to remove Thena from channels");
    } finally {
      setIsAddingThena(false);
    }
  };

  const _handleSetPrimaryTeam = async (channelId: string) => {
    try {
      const response = await fetch(
        `/api/teams/${teamId}/sources/slack/workspace-channels/${channelId}/primary-team`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ teamId: "" }),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      setChannels(
        (prev) =>
          prev?.map((channel) => {
            if (channel.id === channelId) {
              return {
                ...channel,
                teams: channel.teams?.map((team) => ({
                  ...team,
                  isPrimary: team.id === "",
                })),
              };
            }
            return channel;
          }),
      );

      toast.success("Primary team updated successfully");
    } catch (error) {
      console.error("Error updating primary team:", error);
      toast.error("Failed to update primary team");
    }
  };

  const handleTabChange = (value: ChannelState) => {
    setActiveTab(value);
    setPage(1);
    setChannels([]);

    if (value === ChannelState.PRIMARY) {
      setShowPrivate(true);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs
        value={activeTab}
        onValueChange={(value) => {
          handleTabChange(value as ChannelState);
        }}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value={ChannelState.AVAILABLE}>
            Available channels
          </TabsTrigger>
          <TabsTrigger value={ChannelState.PRIMARY}>
            Primary channels
          </TabsTrigger>
          <TabsTrigger value={ChannelState.SECONDARY}>
            Secondary channels
          </TabsTrigger>
        </TabsList>

        <Card className="rounded-[4px] mt-4 shadow-none">
          <CardContent className="p-4">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="Search channels..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9"
                  />
                </div>
              </div>
            </div>

            {activeTab === ChannelState.AVAILABLE && (
              <div className="flex items-center gap-2 bg-yellow-500/20 border border-yellow-500/20 rounded py-2.5 px-2.5 mb-4">
                <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                <span className="text-sm text-foreground">
                  For private channels, please add Thena via Slack.
                </span>
              </div>
            )}

            {activeTab === ChannelState.PRIMARY && (
              <div className="flex items-center gap-2 bg-yellow-500/20 border border-yellow-500/20 rounded py-2.5 px-2.5 mb-4">
                <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0 self-start mt-1" />
                <span className="text-sm text-foreground">
                  Tickets will originate from primary channels. You can edit
                  primary channels to change their teams.
                </span>
              </div>
            )}

            {activeTab === ChannelState.SECONDARY && (
              <div className="flex items-center gap-2 bg-yellow-500/20 border border-yellow-500/20 rounded py-2.5 px-2.5 mb-4">
                <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0 self-start mt-1" />
                <span className="text-sm text-foreground">
                  Functioning as primary channels in other teams, they
                  can&apos;t be set as primary here.
                </span>
              </div>
            )}

            {selectedChannels[activeTab].length > 0 && (
              <div className="flex items-center justify-between mb-4 border-t border-b py-4">
                <div className="text-sm text-muted-foreground">
                  {selectedChannels[activeTab].length} channels selected
                </div>
                <div>
                  {activeTab == ChannelState.PRIMARY && (
                    <Button
                      variant="outline"
                      onClick={
                        activeTab === "primary"
                          ? () => setShowTypeDialog(true)
                          : null
                      }
                      disabled={isAddingThena}
                      className={cn("rounded-[4px] mr-[12px]")}
                    >
                      {isAddingThena ? "Processing..." : "Edit"}
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    onClick={
                      activeTab === "available"
                        ? handleAddChannels
                        : handleRemoveThena
                    }
                    disabled={isAddingThena}
                    className={cn(
                      "rounded-[4px]",
                      activeTab === "primary" &&
                        "text-destructive hover:text-destructive",
                    )}
                  >
                    {isAddingThena
                      ? "Processing..."
                      : activeTab === "available"
                      ? "Add Thena"
                      : "Remove Thena"}
                  </Button>
                </div>
              </div>
            )}

            <ScrollArea className="h-[500px]">
              <div className="space-y-2">
                {isLoading && showDelayedLoader && channels.length === 0 ? (
                  <div className="text-center text-sm text-muted-foreground py-8">
                    Loading channels...
                  </div>
                ) : channels.length === 0 && showDelayedLoader ? (
                  <div className="text-center text-sm text-muted-foreground py-8">
                    No channels found
                  </div>
                ) : (
                  <div className="space-y-2">
                    {channels?.map((channel) => {
                      return (
                        <div
                          key={channel.id}
                          className={cn(
                            "flex items-center gap-4 p-3 rounded-[4px] hover:bg-muted group",
                            selectedChannels[activeTab].includes(
                              channel.channelId,
                            ) && "bg-muted",
                          )}
                        >
                          <Checkbox
                            id={channel.id}
                            checked={selectedChannels[activeTab].includes(
                              channel.channelId,
                            )}
                            disabled={activeTab == ChannelState.SECONDARY}
                            onCheckedChange={() =>
                              handleChannelSelect(channel.channelId)
                            }
                          />
                          <div className="flex items-center justify-between flex-1 min-w-0">
                            <div className="flex items-center gap-2 min-w-0">
                              {channel.isPrivate ? (
                                <Lock
                                  className="w-4 h-4 text-muted-foreground flex-shrink-0"
                                  aria-label="Private channel"
                                />
                              ) : (
                                <Hash
                                  className="w-4 h-4 text-muted-foreground flex-shrink-0"
                                  aria-label="Public channel"
                                />
                              )}
                              <div className="flex items-center gap-2 group">
                                <span className="font-medium truncate">
                                  {channel.name}{" "}
                                </span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {channel.teams && channel.teams.length > 0 && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Badge
                                        variant="outline"
                                        className="bg-primary/5 border-primary/15 hover:bg-primary/10 transition-colors flex items-center justify-center h-6 w-6 p-0 cursor-help"
                                      >
                                        <HeroIcon
                                          name={
                                            teamsMap[channel?.teams[0]?.uid]
                                              ?.icon ?? "RocketLaunchIcon"
                                          }
                                          color={
                                            teamsMap[channel?.teams[0]?.uid]
                                              ?.color
                                          }
                                          className={cn(
                                            `w-3.5 h-3.5 rounded-sm`,
                                          )}
                                        />
                                      </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>
                                        {teamsMap[channel.teams[0].uid]?.name}
                                        {channel.teams[0].relationshipType ===
                                          "primary" && (
                                          <span className="text-primary ml-1">
                                            (Primary)
                                          </span>
                                        )}
                                        {channel.teams.length > 1 && (
                                          <span className="text-muted-foreground ml-1">
                                            +{channel.teams.length - 1} more
                                          </span>
                                        )}
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                              {channel.isShared && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Badge
                                        variant="outline"
                                        className="bg-primary/10 text-primary border-primary/15 hover:bg-primary/15 flex items-center justify-center h-6 w-6 p-0 cursor-help"
                                      >
                                        <Link2 className="h-3.5 w-3.5" />
                                      </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Slack connect</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                              {channel.channelType &&
                                badgesToRender.includes(
                                  channel.channelType,
                                ) && (
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Badge
                                          variant="outline"
                                          className={cn(
                                            channelTypeLabels[
                                              channel.channelType
                                            ].className,
                                            "flex items-center justify-center h-6 w-6 p-0 cursor-help",
                                          )}
                                        >
                                          {
                                            channelTypeLabels[
                                              channel.channelType
                                            ].icon
                                          }
                                        </Badge>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>
                                          {
                                            channelTypeLabels[
                                              channel.channelType
                                            ].label
                                          }
                                        </p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                    {hasMore && (
                      <div
                        ref={loaderRef}
                        className="h-8 flex items-center justify-center"
                      >
                        {isLoading && showDelayedLoader && (
                          <div className="text-sm text-muted-foreground">
                            Loading more...
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </Tabs>

      <Dialog open={showTypeDialog} onOpenChange={setShowTypeDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {activeTab == ChannelState.PRIMARY
                ? "Edit channel setting"
                : "Select channel type"}
            </DialogTitle>
            <DialogDescription>
              Choose how you want to set up this channel with Thena
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            {activeTab == ChannelState.PRIMARY && (
              <DialogDescription className="font-medium">
                Channel type
              </DialogDescription>
            )}

            <Select
              value={selectedType || ""}
              onValueChange={(value: ChannelType) => setSelectedType(value)}
            >
              <SelectTrigger className="w-full rounded-[4px]">
                <SelectValue placeholder="Channel type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="customer_channel">
                  Customer channel
                </SelectItem>
                <SelectItem value="internal_helpdesk">
                  Internal helpdesk
                </SelectItem>
                <SelectItem value="triage_channel">Triage channel</SelectItem>
              </SelectContent>
            </Select>

            {activeTab == ChannelState.PRIMARY && (
              <DialogDescription className="font-medium">
                Primary team
              </DialogDescription>
            )}
            {activeTab == ChannelState.PRIMARY && (
              <Select
                value={selectedSecondaryTeam || teamId}
                onValueChange={(value: string) => {
                  setSelectedSecondaryTeam(value);
                }}
              >
                <SelectTrigger className="w-full rounded-[4px]">
                  <SelectValue
                    defaultChecked={true}
                    placeholder={
                      secondaryTeams.find((team) => team.uid == teamId)?.name
                    }
                    defaultValue={teamId}
                  />
                </SelectTrigger>
                <SelectContent>
                  {secondaryTeams?.map((team) => (
                    <SelectItem value={team.uid.toString()} key={team.uid}>
                      {team.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowTypeDialog(false)}
                className="rounded-[4px]"
              >
                Cancel
              </Button>
              <Button
                onClick={
                  selectedSecondaryTeam &&
                  selectedSecondaryTeam != teamId &&
                  activeTab == ChannelState.PRIMARY
                    ? handleChangeTeamThena
                    : handleAddThena
                }
                disabled={!selectedType || isAddingThena}
                className="rounded-[4px]"
              >
                {isAddingThena ? "Adding..." : "Continue"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
