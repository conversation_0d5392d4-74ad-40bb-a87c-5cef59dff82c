"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { CalendarIcon, Contact, UserCog2 } from "lucide-react";
import React from "react";
import { ControllerRenderProps, useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";
import { useFormBuilder } from "../hooks/use-form-builder";

interface PreviewPanelProps {
  className?: string;
  formName: string;
  description: string;
  fields: Array<{
    id: string;
    name: string;
    description?: string;
    fieldType: string;
    mandatoryOnCreation: boolean;
    mandatoryOnClose: boolean;
    visibleToCustomer: boolean;
    editableByCustomer: boolean;
    options?: Array<{
      id: string;
      value: string;
    }>;
  }>;
  mode?: "preview" | "create";
  showButtons?: boolean;
  customButtons?: React.ReactNode;
  _submitButtonText?: string;
  _resetButtonText?: string;
}

export function PreviewPanel({
  className,
  formName,
  description,
  fields,
  mode = "preview",
  showButtons = true,
  customButtons,
  _submitButtonText = "Test form",
  _resetButtonText = "Reset values",
}: PreviewPanelProps) {
  const [viewMode, setViewMode] = React.useState<"agent" | "requestor">(
    "agent",
  );
  const {
    setFieldValue,
    evaluateConditions,
    fieldValues,
    initialFieldStates,
    setSelectedFields,
    conditions,
  } = useFormBuilder();

  // Filter fields based on view mode
  const visibleFields = fields.filter((field) =>
    viewMode === "agent" ? true : field.editableByCustomer,
  );

  // Determine if a field is mandatory based on view mode
  const isFieldMandatory = (field: PreviewPanelProps["fields"][0]) =>
    viewMode === "agent" ? field.mandatoryOnClose : field.mandatoryOnCreation;

  // Type for store field values
  type StoreFieldValue = string | number | boolean | string[] | Date | null;

  // Determine if buttons should be shown based on view mode and conditions
  const shouldShowButtons =
    (showButtons && viewMode === "requestor" && conditions.size > 0) ||
    customButtons;

  // Update field value and evaluate conditions
  const handleFieldValueChange = (fieldId: string, value: StoreFieldValue) => {
    setFieldValue(fieldId, value);
    evaluateConditions();
  };

  // Dynamically generate the form schema based on fields
  const generateFormSchema = () => {
    const schemaFields: Record<string, z.ZodTypeAny> = {};

    fields.forEach((field) => {
      // Add validation based on field type
      switch (field.fieldType.toLowerCase()) {
        case "single_choice":
        case "radio_button":
          if (field.options && field.options.length > 0) {
            schemaFields[field.id] = field.mandatoryOnCreation
              ? z.string().min(1)
              : z.string().optional();
          }
          break;
        case "multi_choice":
        case "checkbox":
          schemaFields[field.id] = field.mandatoryOnCreation
            ? z.array(z.string()).min(1)
            : z.array(z.string()).optional();
          break;
        case "multi_line":
        case "rich_text":
          schemaFields[field.id] = field.mandatoryOnCreation
            ? z.string().min(1)
            : z.string().optional();
          break;
        case "date":
        case "date_time":
          schemaFields[field.id] = field.mandatoryOnCreation
            ? z.date()
            : z.date().optional();
          break;
        case "boolean":
        case "toggle":
          schemaFields[field.id] = z.boolean().optional();
          break;
        case "integer":
        case "decimal":
          schemaFields[field.id] = field.mandatoryOnCreation
            ? z.string().regex(/^\d*\.?\d*$/)
            : z
                .string()
                .regex(/^\d*\.?\d*$/)
                .optional();
          break;
        case "email":
          schemaFields[field.id] = field.mandatoryOnCreation
            ? z.string().email()
            : z.string().email().optional();
          break;
        case "url":
          schemaFields[field.id] = field.mandatoryOnCreation
            ? z.string().url()
            : z.string().url().optional();
          break;
        default:
          schemaFields[field.id] = field.mandatoryOnCreation
            ? z.string().min(1)
            : z.string().optional();
          break;
      }
    });

    return z.object(schemaFields);
  };

  const formSchema = generateFormSchema();
  type FormSchema = z.infer<typeof formSchema>;

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: React.useMemo(() => {
      // Use values from the store instead of generating defaults
      return fieldValues;
    }, [fieldValues]),
  });

  // Keep form values in sync with store values
  React.useEffect(() => {
    form.reset(fieldValues);
  }, [fieldValues, form]);

  function onSubmit(data: FormSchema) {
    toast.success("Form submitted successfully", {
      description:
        "This is a preview. When created, this would submit the form data.",
    });
    console.log("Form submitted:", data);
  }

  const handleReset = () => {
    // Reset fields to their initial states
    const fieldsWithInitialState = fields.map((field) => {
      // Add required properties for Field type
      const baseField = {
        ...field,
        isStandard: false as const,
        source: "form",
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const initialState = initialFieldStates.get(field.id);
      if (initialState) {
        return {
          ...baseField,
          ...initialState,
        };
      }
      return baseField;
    });
    setSelectedFields(fieldsWithInitialState);

    // Reset all form fields to empty values based on their type
    const emptyValues = fields.reduce(
      (acc, field) => {
        switch (field.fieldType.toLowerCase()) {
          case "multi_choice":
          case "checkbox":
            acc[field.id] = [];
            break;
          case "boolean":
          case "toggle":
            acc[field.id] = false;
            break;
          case "date":
          case "date_time":
            acc[field.id] = null;
            break;
          default:
            acc[field.id] = "";
        }
        return acc;
      },
      {} as Record<string, string | boolean | Date | null | string[]>,
    );

    form.reset(emptyValues);
    toast.success("Form values cleared", {
      description: "All field values have been reset.",
    });
  };

  const renderFormControl = (
    field: PreviewPanelProps["fields"][0],
    formField: ControllerRenderProps<FormSchema, string>,
  ) => {
    switch (field.fieldType.toLowerCase()) {
      case "radio_button":
        return (
          <RadioGroup
            value={formField.value}
            onValueChange={(value) => {
              formField.onChange(value);
              handleFieldValueChange(field.id, value);
            }}
          >
            {field.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={option.id} />
                <Label htmlFor={option.id}>{option.value}</Label>
              </div>
            ))}
          </RadioGroup>
        );

      case "single_choice":
        return (
          <Select
            value={formField.value}
            onValueChange={(value) => {
              formField.onChange(value);
              handleFieldValueChange(field.id, value);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.id} value={option.value}>
                  {option.value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "checkbox":
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={option.id}
                  checked={((formField.value as string[]) || []).includes(
                    option.value,
                  )}
                  onCheckedChange={(checked) => {
                    const currentValues = (formField.value as string[]) || [];
                    const newValues = checked
                      ? [...currentValues, option.value]
                      : currentValues.filter((v) => v !== option.value);
                    formField.onChange(newValues);
                    handleFieldValueChange(field.id, newValues);
                  }}
                />
                <Label htmlFor={option.id}>{option.value}</Label>
              </div>
            ))}
          </div>
        );

      case "multi_choice":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formField.value?.length && "text-muted-foreground",
                )}
              >
                {formField.value?.length > 0 ? (
                  <div className="flex gap-1 flex-wrap">
                    {(formField.value as string[]).map((value) => (
                      <Badge
                        variant="secondary"
                        key={value}
                        className="mr-1 mb-1"
                      >
                        {value}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  "Select options..."
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="start">
              <Command>
                <CommandInput placeholder="Search options..." />
                <CommandList>
                  <CommandEmpty>No options found.</CommandEmpty>
                  <CommandGroup>
                    {field.options?.map((option) => {
                      const isSelected = (
                        (formField.value as string[]) || []
                      ).includes(option.value);
                      return (
                        <CommandItem
                          key={option.id}
                          onSelect={() => {
                            const currentValues =
                              (formField.value as string[]) || [];
                            const newValues = isSelected
                              ? currentValues.filter((v) => v !== option.value)
                              : [...currentValues, option.value];
                            formField.onChange(newValues);
                            handleFieldValueChange(field.id, newValues);
                          }}
                        >
                          <Checkbox checked={isSelected} className="mr-2" />
                          {option.value}
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        );

      case "boolean":
      case "toggle":
        return (
          <Switch
            checked={formField.value === "true"}
            onCheckedChange={(checked) => {
              formField.onChange(checked.toString());
              handleFieldValueChange(field.id, checked);
            }}
          />
        );

      case "date":
      case "date_time":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formField.value && "text-muted-foreground",
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formField.value ? (
                  format(new Date(formField.value), "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={
                  formField.value ? new Date(formField.value) : undefined
                }
                onSelect={(date) => {
                  if (date) {
                    formField.onChange(date.toISOString());
                    handleFieldValueChange(field.id, date);
                  }
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case "multi_line":
      case "rich_text":
        return (
          <Textarea
            {...formField}
            onChange={(e) => {
              formField.onChange(e.target.value);
              handleFieldValueChange(field.id, e.target.value);
            }}
          />
        );

      default:
        return (
          <Input
            {...formField}
            type={
              field.fieldType === "number" ||
              field.fieldType === "integer" ||
              field.fieldType === "decimal"
                ? "number"
                : field.fieldType === "email"
                ? "email"
                : field.fieldType === "url"
                ? "url"
                : "text"
            }
            onChange={(e) => {
              const value = e.target.value;
              formField.onChange(value);
              handleFieldValueChange(
                field.id,
                field.fieldType === "number" ||
                  field.fieldType === "integer" ||
                  field.fieldType === "decimal"
                  ? Number(value)
                  : value,
              );
            }}
          />
        );
    }
  };

  return (
    <div className={className}>
      <div className="h-full flex flex-col border">
        <div className="flex-none space-y-6 p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="text-lg font-medium">
                {formName || "Form preview"}
              </h3>
              {description && (
                <p className="text-sm text-muted-foreground">{description}</p>
              )}
            </div>
            {mode === "preview" && visibleFields?.length > 0 && (
              <div className="flex items-center gap-2">
                <Select
                  value={viewMode}
                  onValueChange={(value: "agent" | "requestor") =>
                    setViewMode(value)
                  }
                >
                  <SelectTrigger className="w-[180px] focus:ring-0 focus-visible:ring-0">
                    <SelectValue placeholder="Select view" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="agent">
                      <div className="flex items-center gap-2">
                        <UserCog2 className="h-4 w-4" />
                        <span>Agent view</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="requestor">
                      <div className="flex items-center gap-2">
                        <Contact className="h-4 w-4" />
                        <span>Requestor view</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto max-h-[calc(100vh-300px)]">
          <div className="p-6 space-y-6">
            {!visibleFields.length && (
              <div className="flex items-center justify-center text-sm text-muted-foreground h-[200px] border-2 border-dashed rounded-lg">
                Form fields will appear here as you add them
              </div>
            )}

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                {visibleFields.map((field) => (
                  <FormField
                    key={field.id}
                    control={form.control}
                    name={field.id}
                    render={({ field: formField }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-0.5">
                          {field.name}
                          {isFieldMandatory(field) && (
                            <span className="text-destructive">*</span>
                          )}
                        </FormLabel>
                        <FormControl>
                          {renderFormControl(field, formField)}
                        </FormControl>
                        {field.description && (
                          <FormDescription>{field.description}</FormDescription>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                ))}
              </form>
            </Form>
          </div>
        </div>

        {shouldShowButtons && (
          <div className="flex-none">
            <div className="p-6">
              <div className="flex items-center justify-end gap-3">
                {customButtons || (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleReset}
                    >
                      Clear
                    </Button>
                    <Button type="button" onClick={form.handleSubmit(onSubmit)}>
                      Submit preview
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
