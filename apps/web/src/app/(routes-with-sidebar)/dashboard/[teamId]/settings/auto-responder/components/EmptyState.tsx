"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusIcon, MessageSquare } from "lucide-react";

interface EmptyStateProps {
  onCreateRule: () => void;
}

export function EmptyState({ onCreateRule }: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center w-full">
      <div className="w-full max-w-2xl mx-auto bg-white rounded-md border p-0">
        <div className="flex flex-col items-center justify-center py-12">
          <MessageSquare className="h-12 w-12 text-slate-300 mb-6" />
          
          <h3 className="text-xl font-medium mb-2">No auto-responder rules yet</h3>
          
          <p className="text-sm text-muted-foreground mb-6">
            Create rules to automatically respond to tickets based on specific conditions.
          </p>
          
          <div className="w-full max-w-md px-6">
            <div className="border-t border-b py-6">
              <div className="space-y-5">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-slate-100 flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-medium">1</span>
                  </div>
                  <p className="text-sm">
                    Set up responses for when your team is unavailable during non-working hours.
                  </p>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-slate-100 flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-medium">2</span>
                  </div>
                  <p className="text-sm">
                    Create automated acknowledgments for new tickets to improve customer experience.
                  </p>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-slate-100 flex items-center justify-center mr-3 mt-0.5">
                    <span className="text-xs font-medium">3</span>
                  </div>
                  <p className="text-sm">
                    Configure different messages based on ticket properties and customer information.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-6">
            <Button 
              className="flex items-center gap-2"
              variant="default"
              onClick={onCreateRule}
            >
              <PlusIcon className="h-4 w-4" />
              Create your first rule
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
