"use client";

import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, PlusIcon } from "lucide-react";

interface EmptyStateProps {
  onCreateRule: () => void;
  isCreating?: boolean;
}

export function EmptyState({ onCreateRule, isCreating = false }: EmptyStateProps) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="max-w-md w-full px-6 py-12 text-center">
        <div className="space-y-6">
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-primary/5 animate-pulse rounded-full blur-xl" />
            <div className="relative mx-auto w-20 h-20 flex items-center justify-center bg-primary/10 rounded-full">
              <MessageSquare className="h-10 w-10 text-primary/80 transition-transform duration-200 hover:scale-110" />
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-medium tracking-tight">
              Let&apos;s create your auto-responder
            </h3>
            <p className="text-sm text-muted-foreground max-w-md text-center">
              Set up automatic responses to tickets based on custom rules and conditions.
            </p>
          </div>
          <div className="pt-2">
            <Button
              size="lg"
              variant="default"
              disabled={isCreating}
              className="mx-auto flex items-center gap-2 disabled:opacity-50"
              onClick={onCreateRule}
            >
              {isCreating ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground border-t-transparent" />
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <PlusIcon className="h-4 w-4" />
                  <span>Create your first rule</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
