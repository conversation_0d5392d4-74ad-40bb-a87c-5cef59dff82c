"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusIcon, Search } from "lucide-react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
//import { FeatureFlagGate } from "../../../../../../components/feature-flag-gate";
import { DeleteConfirmationModal } from "./components/DeleteConfirmationModal";
import { EmptyState } from "./components/EmptyState";
import { RuleAccordion } from "./components/RuleAccordion";

// Define the Rule interface to match the expected structure

interface MessageContent {
  content: string;
  contentHtml: string;
  contentJson: string;
}

interface ApiRule {
  uid: string;
  name: string;
  isActive: boolean;
  triggerEvent: {
    eventType: string;
  };
  metadata: {
    considerations: string[];
    filters: {
      "~and": Record<string, unknown>[];
    };
    message: MessageContent;
  };
  uniqueIdentifier: string;
  version: number;
  subType: string;
}

interface Rule extends Record<string, unknown> {
  id: string;
  name: string;
  trigger: string;
  considerations: string[];
  filters: {
    "~and": Record<string, unknown>[];
  };
  message: MessageContent;
  enabled: boolean;
  uniqueIdentifier: string;
  version: number;
}
// Improved transformApiResponseToRules function for AutoResponderSettings.js
const transformApiResponseToRules = (apiData: ApiRule[]): Rule[] => {
  // First group by uniqueIdentifier
  const groupedRules = apiData.reduce(
    (acc, rule) => {
      if (rule.subType !== "AUTO_RESPONDER") return acc;

      const key = rule.uniqueIdentifier;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(rule);
      return acc;
    },
    {} as Record<string, ApiRule[]>,
  );

  // Then map each group to get the latest version
  return Object.values(groupedRules).map((rules) => {
    // Sort by version in descending order and take the first one
    const latestRule = rules.sort((a, b) => b.version - a.version)[0];

    // Ensure we have a properly formatted filters structure
    let filterArray: Record<string, unknown>[] = [];

    // Safely extract filters from the metadata
    if (latestRule.metadata && latestRule.metadata.filters) {
      // Make sure filters is an array
      if (Array.isArray(latestRule.metadata.filters)) {
        filterArray = latestRule.metadata.filters.filter(
          (filter) => filter !== null && typeof filter === "object",
        );
      }
    }

    return {
      id: latestRule.uid,
      name: latestRule.name,
      enabled: latestRule.isActive,
      trigger:
        latestRule.triggerEvent.eventType === "ticket:created"
          ? "ticket_created"
          : "message_received",
      considerations: latestRule.metadata.considerations
        ? Array.isArray(latestRule.metadata.considerations)
          ? latestRule.metadata.considerations
          : []
        : [],
      // Ensure filters is ALWAYS a properly structured object with ~and property
      filters: { "~and": filterArray },
      message: latestRule.metadata.message || { content: "", contentHtml: "", contentJson: "" },
      uniqueIdentifier: latestRule.uniqueIdentifier,
      version: latestRule.version,
    };
  });
};

export default function AutoResponderSettings() {
  const params = useParams();
  const router = useRouter();
  const teamId = params.teamId as string;
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const initialRef = useRef<boolean>(false);
  const [rules, setRules] = useState<Rule[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState<Rule | null>(null);
  //const flag = useFlags();

  // Check URL parameters for creating state
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const creating = urlParams.get('creating');
    const ruleName = urlParams.get('ruleName');
    const error = urlParams.get('error');
    
    if (creating === 'true' && ruleName) {
      setIsCreating(true);
      
      // Clean up URL parameters
      router.replace(`/dashboard/${teamId}/settings/auto-responder`, undefined);
      
      // Set up polling to check when the rule is created
      const pollForRule = setInterval(async () => {
        try {
          const response = await fetch(`/api/teams/${teamId}/workflows`);
          if (response.ok) {
            const data = await response.json();
            if (data.data) {
              const transformedRules = transformApiResponseToRules(data.data);
              const newRule = transformedRules.find(rule => rule.name === decodeURIComponent(ruleName));
              
              if (newRule) {
                setRules(transformedRules);
                setIsCreating(false);
                toast.success("Auto-responder rule created successfully");
                clearInterval(pollForRule);
              }
            }
          }
        } catch (error) {
          console.error('Error polling for new rule:', error);
        }
      }, 3000); // Poll every 3 seconds
      
      // Stop polling after 30 seconds
      const timeout = setTimeout(() => {
        clearInterval(pollForRule);
        setIsCreating(false);
        toast.error("Rule creation is taking longer than expected. Please refresh the page.");
      }, 30000);
      
      // Cleanup function
      return () => {
        clearInterval(pollForRule);
        clearTimeout(timeout);
      };
      
    } else if (error === 'true') {
      toast.error("Failed to create rule");
      router.replace(`/dashboard/${teamId}/settings/auto-responder`, undefined);
    }
  }, [teamId, router]);

  const fetchRules = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/teams/${teamId}/workflows`);
      if (!response.ok) {
        throw new Error(`Request failed with ${response.status}`);
      }
      const data = await response.json();
      if (data.data) {
        const transformedRules = transformApiResponseToRules(data.data);
        setRules(transformedRules);
      }
    } catch (error) {
      console.error("[fetchRules] Error:", error);
      toast.error("Failed to fetch rules");
    } finally {
      setIsLoading(false);
    }
  }, [teamId]);

  const handleCreateRule = () => {
    router.push(`/dashboard/${teamId}/settings/auto-responder/create`);
  };

  const handleEditRule = (id: string) => {
    const ruleToEdit = rules.find((rule) => rule.id === id);
    if (ruleToEdit) {
      router.push(`/dashboard/${teamId}/settings/auto-responder/edit/${ruleToEdit.uniqueIdentifier}`);
    }
  };

  const handleDeleteRule = (id: string) => {
    const rule = rules.find((rule) => rule.id === id);
    if (rule) {
      setRuleToDelete(rule);
      setIsDeleteModalOpen(true);
    }
  };

  const confirmDeleteRule = async () => {
    if (ruleToDelete) {
      try {
        const response = await fetch(
          `/api/teams/${teamId}/workflows/${ruleToDelete.uniqueIdentifier}/delete`,
          {
            method: "DELETE",
          },
        );

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || "Failed to delete workflow");
        }

        toast.success("Rule deleted successfully.");
        await fetchRules(); // Refresh the rules after deletion
        setRuleToDelete(null);
        setIsDeleteModalOpen(false);
      } catch (error) {
        console.error("[confirmDeleteRule] Error:", error);
        toast.error(
          error instanceof Error ? error.message : "Failed to delete rule",
        );
      }
    }
  };

  const handleToggleRule = async (id: string, enabled: boolean) => {
    try {
      const rule = rules.find((rule) => rule.id === id);
      if (!rule) return;

      const response = await fetch(
        `/api/teams/${teamId}/workflows/${rule.uniqueIdentifier}/toggle`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            isActive: enabled,
          }),
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update rule");
      }

      await fetchRules(); // Refresh the rules after toggle
      toast.success("Rule updated successfully.");
    } catch (error) {
      console.error("[handleToggleRule] Error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update rule",
      );
    }
  };

  useEffect(() => {
    const fetchAndSetInitial = async () => {
      await fetchRules();
      initialRef.current = true;
    };
    fetchAndSetInitial();
  }, [teamId, fetchRules]);

  // Filter rules based on search query
  const filteredRules = rules.filter((rule) =>
    rule.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const showSpinner = isLoading && !initialRef.current;

  if (showSpinner) {
    return (
      <div className="h-full w-full flex items-center justify-center min-h-[640px]">
        <div className="flex flex-col items-center gap-4">
          <ThenaLoader />
        </div>
      </div>
    );
  }

  return (
    //<FeatureFlagGate flag={flag.autoResponder} shouldRedirect={false}>
      <>
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto py-6">
            <div className="max-w-[640px] mx-auto">
              {rules.length === 0 ? (
                <EmptyState onCreateRule={handleCreateRule} isCreating={isCreating} />
              ) : (
                <div className="flex flex-col gap-6">
                  <div className="space-y-2">
                    <h1 className="text-2xl font-medium tracking-tight">Auto-responder</h1>
                    <p className="text-sm text-muted-foreground">
                      Set up automatic responses to tickets based on custom rules and conditions.
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search rules..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-9 w-[300px]"
                        />
                      </div>
                    </div>
                    <Button
                      onClick={handleCreateRule}
                      variant="default"
                      size="sm"
                      disabled={isCreating}
                      className="flex items-center gap-2 text-[14px] bg-primary hover:bg-primary/90 text-primary-foreground disabled:opacity-50"
                    >
                      {isCreating ? (
                        <>
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground border-t-transparent" />
                          <span>Creating...</span>
                        </>
                      ) : (
                        <>
                          <PlusIcon className="h-4 w-4" />
                          <span>Create rule</span>
                        </>
                      )}
                    </Button>
                  </div>

                  <div className="flex flex-col gap-4">
                    <div className="space-y-4">
                      {filteredRules.length === 0 ? (
                        <div className="text-center py-8">
                          <p className="text-sm text-muted-foreground">
                            {searchQuery ? "No rules found matching your search." : "No rules found."}
                          </p>
                        </div>
                      ) : (
                        filteredRules.map((rule) => (
                          <RuleAccordion
                            key={rule.id}
                            rule={rule}
                            onEdit={handleEditRule}
                            onDelete={handleDeleteRule}
                            onToggle={handleToggleRule}
                          />
                        ))
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>

        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onOpenChange={setIsDeleteModalOpen}
          onConfirm={confirmDeleteRule}
          ruleName={ruleToDelete?.name || ""}
        />
      </>
    //</FeatureFlagGate>
  );
}
