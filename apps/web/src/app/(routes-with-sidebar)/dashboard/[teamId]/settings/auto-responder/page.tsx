"use client";

import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import { But<PERSON> } from "@/components/ui/button";
import { useFlags } from "launchdarkly-react-client-sdk";
import { PlusIcon } from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { FeatureFlagGate } from "../../../../../../components/feature-flag-gate";
import { DeleteConfirmationModal } from "./components/DeleteConfirmationModal";
import { EmptyState } from "./components/EmptyState";
import { RuleAccordion } from "./components/RuleAccordion";
import { RuleModal } from "./components/RuleModal";

// Define the Rule interface to match the expected structure

interface MessageContent {
  content: string;
  contentHtml: string;
}

interface ApiRule {
  uid: string;
  name: string;
  isActive: boolean;
  triggerEvent: {
    eventType: string;
  };
  metadata: {
    considerations: string[];
    filters: {
      "~and": Record<string, unknown>[];
    };
    message: MessageContent;
  };
  uniqueIdentifier: string;
  version: number;
  subType: string;
}

interface Rule extends Record<string, unknown> {
  id: string;
  name: string;
  trigger: string;
  considerations: string[];
  filters: {
    "~and": Record<string, unknown>[];
  };
  message: MessageContent;
  enabled: boolean;
  uniqueIdentifier: string;
  version: number;
}
// Improved transformApiResponseToRules function for AutoResponderSettings.js
const transformApiResponseToRules = (apiData: ApiRule[]): Rule[] => {
  // First group by uniqueIdentifier
  const groupedRules = apiData.reduce(
    (acc, rule) => {
      if (rule.subType !== "AUTO_RESPONDER") return acc;

      const key = rule.uniqueIdentifier;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(rule);
      return acc;
    },
    {} as Record<string, ApiRule[]>,
  );

  // Then map each group to get the latest version
  return Object.values(groupedRules).map((rules) => {
    // Sort by version in descending order and take the first one
    const latestRule = rules.sort((a, b) => b.version - a.version)[0];

    // Ensure we have a properly formatted filters structure
    let filterArray: Record<string, unknown>[] = [];

    // Safely extract filters from the metadata
    if (latestRule.metadata && latestRule.metadata.filters) {
      // Make sure filters is an array
      if (Array.isArray(latestRule.metadata.filters)) {
        filterArray = latestRule.metadata.filters.filter(
          (filter) => filter !== null && typeof filter === "object",
        );
      }
    }

    return {
      id: latestRule.uid,
      name: latestRule.name,
      enabled: latestRule.isActive,
      trigger:
        latestRule.triggerEvent.eventType === "ticket:created"
          ? "ticket_created"
          : "message_received",
      considerations: latestRule.metadata.considerations
        ? Array.isArray(latestRule.metadata.considerations)
          ? latestRule.metadata.considerations
          : []
        : [],
      // Ensure filters is ALWAYS a properly structured object with ~and property
      filters: { "~and": filterArray },
      message: latestRule.metadata.message || { content: "", contentHtml: "" },
      uniqueIdentifier: latestRule.uniqueIdentifier,
      version: latestRule.version,
    };
  });
};

export default function AutoResponderSettings() {
  const params = useParams();
  const teamId = params.teamId as string;
  const [isLoading, setIsLoading] = useState(false);
  const initialRef = useRef<boolean>(false);
  const [rules, setRules] = useState<Rule[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentRule, setCurrentRule] = useState<Rule | undefined>(undefined);
  const [isEdit, setIsEdit] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState<Rule | null>(null);
  const flag = useFlags();
  const fetchRules = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/teams/${teamId}/workflows`);
      if (!response.ok) {
        throw new Error(`Request failed with ${response.status}`);
      }
      const data = await response.json();
      if (data.data) {
        const transformedRules = transformApiResponseToRules(data.data);
        setRules(transformedRules);
      }
    } catch (error) {
      console.error("[fetchRules] Error:", error);
      toast.error("Failed to fetch rules");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateRule = () => {
    setCurrentRule(undefined);
    setIsEdit(false);
    setIsModalOpen(true);
  };

  const handleEditRule = (id: string) => {
    const ruleToEdit = rules.find((rule) => rule.id === id);
    if (ruleToEdit) {
      // Ensure rule has proper filter structure before passing to modal
      const editableRule = {
        ...ruleToEdit,
        // Ensure filters is always properly formatted with ~and
        filters:
          ruleToEdit.filters && ruleToEdit.filters["~and"]
            ? ruleToEdit.filters
            : { "~and": [] },
      };

      setCurrentRule(editableRule);
      setIsEdit(true);
      setIsModalOpen(true);
    }
  };

  const handleDeleteRule = (id: string) => {
    const rule = rules.find((rule) => rule.id === id);
    if (rule) {
      setRuleToDelete(rule);
      setIsDeleteModalOpen(true);
    }
  };

  const confirmDeleteRule = async () => {
    if (ruleToDelete) {
      try {
        const response = await fetch(
          `/api/teams/${teamId}/workflows/${ruleToDelete.uniqueIdentifier}/delete`,
          {
            method: "DELETE",
          },
        );

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || "Failed to delete workflow");
        }

        toast.success("Rule deleted successfully.");
        await fetchRules(); // Refresh the rules after deletion
        setRuleToDelete(null);
        setIsDeleteModalOpen(false);
      } catch (error) {
        console.error("[confirmDeleteRule] Error:", error);
        toast.error(
          error instanceof Error ? error.message : "Failed to delete rule",
        );
      }
    }
  };

  const handleToggleRule = async (id: string, enabled: boolean) => {
    try {
      const rule = rules.find((rule) => rule.id === id);
      if (!rule) return;

      const response = await fetch(
        `/api/teams/${teamId}/workflows/${rule.uniqueIdentifier}/toggle`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            isActive: enabled,
          }),
        },
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update rule");
      }

      await fetchRules(); // Refresh the rules after toggle
      toast.success("Rule updated successfully.");
    } catch (error) {
      console.error("[handleToggleRule] Error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update rule",
      );
    }
  };

  useEffect(() => {
    const fetchAndSetInitial = async () => {
      await fetchRules();
      initialRef.current = true;
    };
    fetchAndSetInitial();
  }, [teamId]);

  if (isLoading && !initialRef.current) {
    return (
      <div className="h-full w-full flex items-center justify-center min-h-[640px]">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <FeatureFlagGate flag={flag.autoResponder} shouldRedirect={false}>
      <div className="flex flex-col justify-center items-center w-full">
        <div className="flex-1 bg-background py-8 flex items-center justify-center w-full max-w-[640px] px-4 sm:px-0 mx-auto">
          <div className="w-full">
            <div className="mb-8">
              <h2 className="text-2xl font-medium">Auto-responder</h2>
              <p className="text-sm text-muted-foreground mt-0.5">
                Set up automatic responses to tickets based on custom rules and
                conditions.
              </p>
            </div>

            {rules.length > 0 && (
              <div className="flex justify-between items-center mb-6">
                <Button
                  className="flex items-center gap-2"
                  variant="default"
                  onClick={handleCreateRule}
                >
                  <PlusIcon className="h-4 w-4" />
                  Create new rule
                </Button>
              </div>
            )}

            {rules.length === 0 ? (
              <EmptyState onCreateRule={handleCreateRule} />
            ) : (
              <div className="space-y-4">
                {rules.map((rule) => (
                  <RuleAccordion
                    key={rule.id}
                    rule={rule}
                    onEdit={handleEditRule}
                    onDelete={handleDeleteRule}
                    onToggle={handleToggleRule}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        <RuleModal
          isOpen={isModalOpen}
          onOpenChange={setIsModalOpen}
          initialRule={currentRule}
          fetchRules={fetchRules}
          isEdit={isEdit}
        />

        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onOpenChange={setIsDeleteModalOpen}
          onConfirm={confirmDeleteRule}
          ruleName={ruleToDelete?.name || ""}
        />
      </div>
    </FeatureFlagGate>
  );
}
