"use client";

import { getRealtimeToken } from "@/app/actions/auth";
import Then<PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import { useApi } from "@/hooks/use-api";
import { createClient } from "@/lib/supabase-client";
import {
  GET_BOOTSTRAP,
  GET_FORM_BY_TEAM_ID,
  GET_TICKET_TYPES_BY_TEAM_ID,
  GET_TICKETS,
} from "@/services/kanban";
import { useKanbanStore } from "@/store/kanbanStore";
import { useKanbanStorePersist } from "@/store/kanbanStorePersist";
import { useTicketDetailsDisplayOptions } from "@/store/ticket-details-display-options";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { useKanbanDisplayOptions } from "@/stores/use-kanban-display-options";
import {
  Account,
  FormType,
  Priority,
  Requestor,
  SavedViewConfig,
  SentimentType,
  Status,
  SubTeam,
  Tag,
  TeamMembers,
  ThenaTicket,
  TicketTypes,
  UserConfig,
} from "@/types/kanban";
import { REALTIME_SUBSCRIBE_STATES } from "@supabase/supabase-js";
import { useQuery } from "@tanstack/react-query";
import { subDays } from "date-fns";
import cloneDeep from "lodash/cloneDeep";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { sendErrorToSentry } from "../../../../components/Kanban/TicketDetails/utils";
import { useAppsSourcesStore } from "../../../../store/apps-sources-store";
import { useGlobalConfigPersistStore } from "../../../../store/globalConfigPersistStore";
import { useInternalUsersStore } from "../../../../store/internal-users-store";
import { fetchInternalUsers } from "../../../../utils/internal-user";
import { TeamGroup } from "./settings/routing/types";

type BootstrapResponse = {
  statuses: Status[];
  priorities: Priority[];
  teamMembers: TeamMembers[];
  statusOrder: UserConfig["status_order"];
  userFilterConfig: UserConfig["userFilterConfig"];
  savedViews: SavedViewConfig[];
  hiddenColumns: string[];
};

type formResponse = {
  results: FormType[];
  pageTotal: number;
  total: number;
  offset: number;
};

type TicketsResponse = {
  tickets: ThenaTicket[];
  accounts: Account[];
  requestors: Requestor[];
  sources: string[];
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const teamId = useParams().teamId as string;
  const supabase = createClient();

  const viewId = useSearchParams().get("viewId");

  const [isStatusOrderMapReady, setIsStatusOrderMapReady] = useState(false);

  const tickets = useTicketMetaStore((state) => state.tickets);
  const statuses = useTicketMetaStore((state) => state.statuses);
  const allTeams = useTicketMetaStore((state) => state.teams);
  const savedViews = useTicketMetaStore((state) => state.savedViews);
  const sources = useAppsSourcesStore((state) => state.sources);
  const { currentOrgId, orgs } = useGlobalConfigPersistStore((state) => state);

  const currentTeam = allTeams.find((team) => team.uid === teamId);

  const {
    data: bootstrapData,
    loading: bootstrapLoading,
    error,
  } = useApi<BootstrapResponse>(
    GET_BOOTSTRAP(teamId),
    {},
    {
      isNextApi: true,
      enabled: !!teamId,
    },
  );

  useEffect(() => {
    if (error) {
      toast.error("Error fetching data", {
        description: error.message,
      });
    }
  }, [error]);

  useEffect(() => {
    const orgId = orgs.find((org) => org.id === currentOrgId)?.orgId;
    if (sources.length > 0 && orgId) {
      fetchInternalUsers(sources, orgId)
        .then((users) => {
          useInternalUsersStore.getState().setInternalUsers(users);
        })
        .catch((error) => {
          console.error("Failed to fetch internal users:", error);
          toast.error("Failed to load internal users");
        });
    }
  }, [sources, currentOrgId, orgs]);

  useEffect(() => {
    if (bootstrapData) {
      const {
        statuses,
        priorities,
        teamMembers,
        statusOrder = {} as UserConfig["status_order"],
        userFilterConfig = {},
        savedViews,
        hiddenColumns,
      } = bootstrapData;
      useTicketMetaStore.getState().setAllStatuses(statuses);
      useTicketMetaStore.getState().setAllPriorities(priorities);
      useTicketMetaStore.getState().setAllTeamMembers(teamMembers);

      const localView = useTicketMetaStore.getState().savedViews;
      if (savedViews.length > localView.length) {
        useTicketMetaStore.getState().setSavedViews(savedViews);
      }

      const { metadata = {}, filterUid } =
        userFilterConfig as UserConfig["userFilterConfig"];

      const {
        queryValuesMap = {},
        selectedFiltersOrder = [],
        dateFilters = {
          dateRange: { from: subDays(new Date(), 30), to: new Date() },
          preselectedRange: "",
        },
        drawerDisplayOptions,
        kanbanDisplayOptions,
      } = metadata as UserConfig["userFilterConfig"]["metadata"];

      if (drawerDisplayOptions) {
        useTicketDetailsDisplayOptions
          .getState()
          .setStateOverwrite(drawerDisplayOptions);
      }

      if (kanbanDisplayOptions) {
        useKanbanDisplayOptions
          .getState()
          .setStateOverwrite(kanbanDisplayOptions);
      }

      useKanbanStorePersist.dispatch({
        type: "SET_DEFAULT_FILTERS",
        payload: {
          queryValuesMap,
          selectedFiltersOrder,
          dateFilters,
          filterUid,
        },
      });
      // Fallback if the data was added via API
      const newStatusOrder = cloneDeep(statusOrder.statusOrder || {});

      statuses.forEach((status) => {
        const isParent = !status.parent_status;

        if (isParent) {
          if (newStatusOrder[status.uid]) {
            return;
          }
          newStatusOrder[status.uid] = {
            order: Object.keys(newStatusOrder).length,
            sub_statuses: statuses
              .filter((s) => s.parent_status?.uid === status.uid)
              .map((s) => s.uid),
          };

          return;
        }

        const isPresentInMap = newStatusOrder[
          status.parent_status.uid
        ].sub_statuses.find((s) => s === status.uid);

        if (isPresentInMap) {
          return;
        }

        newStatusOrder[status.parent_status.uid] = {
          order: newStatusOrder[status.parent_status.uid].order,
          sub_statuses: [
            ...newStatusOrder[status.parent_status.uid].sub_statuses,
            status.uid,
          ],
        };
      });

      useKanbanStore.getState().setStatusOrder(newStatusOrder);
      useKanbanStore
        .getState()
        .setStatusOrderUid(statusOrder.statusOrderUid ?? "");

      useKanbanStore.getState().setHiddenColumns(
        hiddenColumns.map((uid) => {
          const column = statuses.find((status) => status.uid === uid);
          return {
            uid,
            count: 0, // This to be updated from tickets
            name: column?.display_name ?? "",
          };
        }),
      );

      if (statuses.length) {
        const statusMap = statuses
          .filter((status) => status.child_statuses.length === 0)
          .reduce(
            (acc, status) => {
              acc[status.uid] = [];
              return acc;
            },
            {} as Record<string, ThenaTicket[]>,
          );

        useKanbanStore.getState().setStatusOrderMap(statusMap);
        setIsStatusOrderMapReady(true);
      }
    }
  }, [bootstrapData, viewId]);

  useEffect(() => {
    const view = savedViews.find((view) => view.uid === viewId);

    if (view) {
      const { metadata = {} } = view;

      const {
        queryValuesMap = {},
        selectedFiltersOrder = [],
        dateFilters = {
          dateRange: { from: subDays(new Date(), 30), to: new Date() },
          preselectedRange: "",
        },
        drawerDisplayOptions,
        kanbanDisplayOptions,
      } = metadata as UserConfig["userFilterConfig"]["metadata"];

      if (drawerDisplayOptions) {
        useTicketDetailsDisplayOptions
          .getState()
          .setStateOverwrite(drawerDisplayOptions);
      }

      if (kanbanDisplayOptions) {
        useKanbanDisplayOptions
          .getState()
          .setStateOverwrite(kanbanDisplayOptions);
      }

      // Check if this view has ticket IDs from chat
      const ticketIds =
        (metadata as SavedViewConfig["metadata"]).ticketMap?.ticket_ids || [];
      const isChatCreatedView =
        (metadata as SavedViewConfig["metadata"]).created_by_chat === true;

      useKanbanStorePersist.dispatch({
        type: "SET_DEFAULT_FILTERS",
        payload: {
          queryValuesMap,
          selectedFiltersOrder,
          dateFilters,
          filterUid: viewId,
          isChatCreatedView,
          ticketIds,
        },
      });
    }
  }, [savedViews, viewId]);

  const teamPk = useTicketMetaStore((state) => state.teams).find(
    (team) => team.uid === teamId,
  )?.id;

  const queryValuesMap = useKanbanStorePersist((state) => state.queryValuesMap);
  const sortBy = useKanbanStorePersist((state) => state.sortby);
  const filteringType = useKanbanStorePersist((state) => state.filteringType);
  const dateRange = useKanbanStorePersist(
    (state) => state.dateFilters.dateRange,
  );
  const preselectedRange = useKanbanStorePersist(
    (state) => state.dateFilters.preselectedRange,
  );
  const chatViewTicketIds = useKanbanStorePersist(
    (state) => state.chatViewTicketIds,
  );

  const {
    data: ticketsData,
    error: ticketsError,
    refetch,
  } = useQuery<TicketsResponse>({
    queryKey: [
      "tickets",
      teamPk,
      queryValuesMap,
      filteringType,
      dateRange,
      preselectedRange,
      sortBy,
      viewId,
      chatViewTicketIds, // Add ticket IDs to ensure query refreshes when they change
    ],
    queryFn: async () => {
      if (!teamPk) {
        return;
      }
      const response = await fetch(GET_TICKETS, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          teamId: teamPk,
          queryValuesMap: JSON.stringify(queryValuesMap),
          filteringType,
          dateRange,
          preselectedRange,
          sortBy,
          ticketIds: chatViewTicketIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch tickets");
      }

      return response.json();
    },
    enabled: !!teamPk && !bootstrapLoading,
  });

  const { data: groups } = useApi<TeamGroup[]>(
    `/v1/teams/${teamId}/sub-teams`,
    {},
    { enabled: !!teamId, isNextApi: false, method: "GET" },
  );

  const { data: ticketTypes } = useApi<TicketTypes[]>(
    GET_TICKET_TYPES_BY_TEAM_ID(teamId),
    {},
    { isNextApi: false, enabled: !!teamId },
  );

  const { data: tags } = useApi<{
    status: boolean;
    timestamp: string;
    teamUuid: string;
    data: {
      count: number;
      items: Tag[];
    };
    message: string;
  }>(`/v1/teams/${teamId}/tags`, {}, { isNextApi: false, enabled: !!teamId });

  useEffect(() => {
    if (ticketTypes) {
      useTicketMetaStore.getState().setAllTicketType(ticketTypes);
    }

    if (tags) {
      useTicketMetaStore.getState().setAllTags(tags.data?.items || []);
    }
  }, [ticketTypes, tags]);

  useEffect(() => {
    if (!currentTeam?.id) return;

    const teamId = currentTeam.uid;

    let subscription: ReturnType<typeof supabase.channel> | undefined;

    const setupSubscription = async () => {
      // Set authentication for Supabase Realtime before creating the subscription
      const access_token = await getRealtimeToken();
      await supabase.realtime.setAuth(access_token);

      subscription = supabase
        .channel(`multitable`)
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "ticket_tags",
          },
          async (payload) => {
            console.log("Ticket tags change received!", payload);
            if (
              payload.eventType === "INSERT" ||
              payload.eventType === "UPDATE" ||
              payload.eventType === "DELETE"
            ) {
              const key = payload.eventType === "DELETE" ? "old" : "new";

              const currentFilters = {
                queryValuesMap: useKanbanStorePersist.getState().queryValuesMap,
                filteringType: useKanbanStorePersist.getState().filteringType,
                dateRange:
                  useKanbanStorePersist.getState().dateFilters.dateRange,
                preselectedRange:
                  useKanbanStorePersist.getState().dateFilters.preselectedRange,
                sortBy: useKanbanStorePersist.getState().sortby,
                teamId: currentTeam.id,
              };
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const payloadData: any = payload[key];
              const ticketIds = Array.isArray(payloadData)
                ? payloadData?.map((p) => p.ticket_id)
                : [payloadData?.ticket_id];

              // Fetch updated tickets in batch with filters
              const response = await fetch("/api/tickets/batch", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  ticketIds,
                  filters: currentFilters,
                }),
              });

              if (!response.ok) {
                console.error("Failed to fetch updated tickets");
                return;
              }

              const updatedData = await response.json();

              useTicketMetaStore.setState((state) => {
                const newTickets = [...state.tickets];

                updatedData.tickets.forEach((updatedTicket) => {
                  const ticketIndex = newTickets.findIndex(
                    (t) => t.id === updatedTicket.id,
                  );
                  if (ticketIndex !== -1) {
                    newTickets[ticketIndex] = updatedTicket;
                  } else {
                    newTickets.push(updatedTicket);
                  }
                });

                const filteredTickets = newTickets.filter((t) => !t.deleted_at);
                return {
                  ...state,
                  tickets: filteredTickets,
                  accounts: [
                    ...state.accounts,
                    ...updatedData.accounts.filter(
                      (newAcc) =>
                        !state.accounts.some((acc) => acc.id === newAcc.id),
                    ),
                  ],
                  sources: [
                    ...new Set([...state.sources, ...updatedData.sources]),
                  ],
                };
              });
            }
          },
        )
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "ticket",
            filter: `team_id=eq.${currentTeam.id}`,
          },
          async (payload) => {
            console.log("Change received!", payload);

            if (
              payload.eventType === "INSERT" ||
              payload.eventType === "UPDATE"
            ) {
              // Get all ticket IDs that need to be updated
              const ticketIds = Array.isArray(payload.new)
                ? payload.new.map((p) => p.id)
                : [payload.new.id];

              // Get current filter state (may have changed since subscription setup)
              const currentFilters = {
                queryValuesMap: useKanbanStorePersist.getState().queryValuesMap,
                filteringType: useKanbanStorePersist.getState().filteringType,
                dateRange:
                  useKanbanStorePersist.getState().dateFilters.dateRange,
                preselectedRange:
                  useKanbanStorePersist.getState().dateFilters.preselectedRange,
                sortBy: useKanbanStorePersist.getState().sortby,
                teamId: currentTeam.id,
              };

              // Fetch updated tickets in batch with filters
              const response = await fetch("/api/tickets/batch", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  ticketIds,
                  filters: currentFilters,
                }),
              });

              if (!response.ok) {
                console.error("Failed to fetch updated tickets");
                return;
              }

              const updatedData = await response.json();

              useTicketMetaStore.setState((state) => {
                const newTickets = [...state.tickets];

                updatedData.tickets.forEach((updatedTicket) => {
                  const ticketIndex = newTickets.findIndex(
                    (t) => t.id === updatedTicket.id,
                  );
                  if (ticketIndex !== -1) {
                    newTickets[ticketIndex] = updatedTicket;
                  } else {
                    newTickets.push(updatedTicket);
                  }
                });

                const filteredTickets = newTickets.filter((t) => !t.deleted_at);

                return {
                  ...state,
                  tickets: filteredTickets,
                  accounts: [
                    ...state.accounts,
                    ...updatedData.accounts.filter(
                      (newAcc) =>
                        !state.accounts.some((acc) => acc.id === newAcc.id),
                    ),
                  ],
                  sources: [
                    ...new Set([...state.sources, ...updatedData.sources]),
                  ],
                };
              });
            }
          },
        )
        .subscribe((s) => {
          console.log("Subscription status:", s);
          if (
            s === REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR ||
            s === REALTIME_SUBSCRIBE_STATES.TIMED_OUT
          ) {
            sendErrorToSentry({
              message: `SUPABASE LIVE BROKE AND STATUS IS ${s} FOR teamId ${teamId}`,
              error: new Error("Supabase error"),
              level: "error",
              tag: {
                key: "teamId",
                value: teamId,
              },
            });
          }
        });
    };

    // Execute the async function
    setupSubscription();

    return () => {
      if (subscription) subscription.unsubscribe();
    };
  }, [currentTeam]);

  const { data: formDataFromApi } = useApi<formResponse>(
    GET_FORM_BY_TEAM_ID(teamId),
    {},
    { enabled: !!teamId, isNextApi: false },
  );

  useEffect(() => {
    if (formDataFromApi?.results) {
      useTicketMetaStore.getState().setForms(formDataFromApi.results);
    }
    if (groups) {
      useTicketMetaStore.getState().setGroups(groups);
    }
  }, [formDataFromApi, groups]);

  useEffect(() => {
    if (teamId) {
      refetch();
    }
  }, [
    queryValuesMap,
    filteringType,
    dateRange,
    preselectedRange,
    sortBy,
    teamId,
    refetch,
  ]);

  useEffect(() => {
    if (ticketsError) {
      toast.error("Error fetching tickets", {
        description: ticketsError.message,
      });
    }
  }, [ticketsError]);

  useEffect(() => {
    if (
      Array.isArray(ticketsData?.accounts) &&
      (useTicketMetaStore.getState().accounts?.length === 0 ||
        ticketsData.accounts?.length >
          useTicketMetaStore.getState().accounts?.length)
    ) {
      useTicketMetaStore.getState().setAccounts(ticketsData.accounts);
    }

    if (
      Array.isArray(ticketsData?.sources) &&
      useTicketMetaStore.getState().sources?.length === 0
    ) {
      useTicketMetaStore.getState().setSources(ticketsData.sources);
    }
  }, [ticketsData?.accounts, ticketsData?.sources]);

  useEffect(() => {
    if (Array.isArray(ticketsData?.tickets)) {
      useTicketMetaStore.getState().setAllTickets(ticketsData?.tickets);
      const uniquesRequestors = new Set();

      ticketsData?.tickets.forEach((ticket) => {
        if (ticket.requestor_email) {
          uniquesRequestors.add(ticket.requestor_email);
        }
      });

      const requesterArr = Array.from(uniquesRequestors).map((requestor) => ({
        email: requestor,
        id: requestor,
      }));

      if (
        !useTicketMetaStore.getState().requestors ||
        useTicketMetaStore.getState().requestors?.length === 0
      ) {
        useTicketMetaStore
          .getState()
          .setRequestors(requesterArr as Requestor[]);
      }
    }
  }, [ticketsData?.tickets]);

  useEffect(() => {
    if (Array.isArray(tickets)) {
      const hiddenColumns = useKanbanStore.getState().hiddenColumns;
      const sortBy = useKanbanStorePersist.getState().sortby;

      // Create a sorted copy of tickets if sort settings exist
      const sortedTickets = [...tickets];
      if (sortBy?.field) {
        const fieldMap: Record<string, string> = {
          created_at_date: "created_at",
        };
        const field = fieldMap[sortBy.field] || sortBy.field;
        const isAscending = sortBy.order === "ASC";

        sortedTickets.sort((a, b) => {
          if (field === "created_at") {
            const dateA = new Date(a[field]).getTime();
            const dateB = new Date(b[field]).getTime();
            return isAscending ? dateA - dateB : dateB - dateA;
          }
          if (a[field] < b[field]) return isAscending ? -1 : 1;
          if (a[field] > b[field]) return isAscending ? 1 : -1;
          return 0;
        });
      }

      // Distribute sorted tickets to columns
      const statusMap = statuses
        .filter((status) => status.child_statuses.length === 0)
        .filter(
          (status) =>
            !hiddenColumns.find((column) => column.uid === status.uid),
        )
        .reduce(
          (acc, status) => {
            acc[status.uid] = sortedTickets.filter(
              (ticket) => ticket.status_id === status.id,
            );

            return acc;
          },
          {} as Record<string, ThenaTicket[]>,
        );
      useKanbanStore.getState().setStatusOrderMap(statusMap);
      useKanbanStore.getState().setHiddenColumns(
        hiddenColumns.map((column) => {
          return {
            ...column,
            count: statusMap[column.uid]?.length ?? 0,
          };
        }),
      );
    }
  }, [tickets, statuses]);

  const { data: allPublicTeams } = useApi<SubTeam[]>(
    `/v1/teams/public`,
    {},
    { isNextApi: false, enabled: true },
  );

  useEffect(() => {
    if (allPublicTeams && allPublicTeams?.length > 0) {
      const subTeams = allPublicTeams.filter(
        (team) => team.parentTeamId === teamId,
      );
      useTicketMetaStore.getState().setAllSubTeams(subTeams);
    }
  }, [allPublicTeams, teamId]);

  const { data: allSentiments } = useApi<SentimentType[]>(
    `/v1/tickets/sentiment?teamId=${teamId}`,
    {},
    { isNextApi: false, enabled: true },
  );

  useEffect(() => {
    if (allSentiments) {
      useTicketMetaStore.getState().setAllSentiments(allSentiments);
    }
  }, [allSentiments]);

  if (bootstrapLoading || !isStatusOrderMapReady) {
    return (
      <ThenaLoader
        loaderText="Loading your board..."
        className="h-screen w-full flex items-center justify-center"
      />
    );
  }

  return children;
}
