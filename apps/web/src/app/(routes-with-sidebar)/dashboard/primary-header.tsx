"use client";

import { Breadcrumb } from "@/components/breadcrumb";
import { ChatDialog } from "@/components/chat/chat-dialog";
import DisplayOptions from "@/components/header/DisplayOptions";
import ViewSwitcher from "@/components/header/ViewSwitcher";
import TooltipWrapper from "@/components/tooltip-wrapper";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useSidebar } from "@/components/ui/sidebar";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { WorkflowHeader } from "@/components/workflows/workflow-header";
import { useTicketField } from "@/contexts/ticket-field-context";
import { useApi } from "@/hooks/use-api";
import { useHelpcenterStore } from "@/store/helpcenter-store";
import { useKanbanStorePersist } from "@/store/kanbanStorePersist";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { HelpcenterList, KBArt<PERSON> } from "@/types/helpcenter.types";
import { Team } from "@/types/kanban";

import { LayoutGrid, List, PanelLeft, Plus } from "lucide-react";
import Link from "next/link";
import { useParams, usePathname, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useAppStudioStore } from "../../../store/app-studio-store";
import {
  App,
  useInstallationFormStore,
} from "../../../store/installation-form-store";
import { InstalledAppDto } from "../../../types/app-studio";
import { SourceTypes } from "../../../types/sources";

import {
  SLAPolicy,
  useFetchSLAPolicies,
} from "./[teamId]/settings/sla/services/use-sla-policies";

interface SourceDetails {
  id: string;
  name: string;
  type: SourceTypes;
  isConnected: boolean;
  key: string;
}

interface AutoResponderRule {
  id: string;
  name: string;
  uniqueIdentifier: string;
  enabled: boolean;
}

// Add interface for API rule data to replace 'any' types
interface ApiRule {
  uid: string;
  name: string;
  isActive: boolean;
  uniqueIdentifier: string;
  subType: string;
}

const PrimaryHeader = ({
  renderInboxHeader,
}: {
  renderInboxHeader?: React.ReactNode;
}) => {
  const pathname = usePathname();
  const { teamId, fieldId, formId, sourceId: paramsSourceId } = useParams();
  const teams = useTicketMetaStore((state) => state.teams);
  const helpcenters = useHelpcenterStore((state) => state.helpcenters);
  const articles = useHelpcenterStore((state) => state.articles);
  const { selectedApp: app } = useAppStudioStore();
  const selectedApp = useInstallationFormStore((state) => state.app);
  const groupId = pathname.match(/\/settings\/groups\/([^\/]+)$/)?.[1];
  const ruleId = pathname.match(
    /\/settings\/auto-responder\/edit\/([^\/]+)$/,
  )?.[1];
  const { field: ticketField } = useTicketField();
  const searchParams = useSearchParams();
  const workflowName = searchParams.get("workflowName");
  const [currentSource, setCurrentSource] = useState<SourceDetails | null>(
    null,
  );
  const sourceId = paramsSourceId as string;
  const botUserId = searchParams.get("botUserId");
  const type = searchParams.get("type") as SourceTypes;
  const [slaPolicies, setSLAPolicies] = useState<SLAPolicy[]>([]);
  const [currentAutoResponderRule, setCurrentAutoResponderRule] =
    useState<AutoResponderRule | null>(null);
  // Get form data if we're on a form page
  const { data: formData } = useApi<{ data: Array<{ name: string }> }>(
    `/api/forms/${formId}`,
    {},
    { enabled: !!formId, isNextApi: true },
  );
  const formName = formData?.data?.[0]?.name;
  const isSLAPage = pathname.includes("/settings/sla");
  const isAutoResponderPage = pathname.includes("/settings/auto-responder");
  // Remove workflow API call
  const { data: currentGroup } = useApi<Team>(
    `/v1/teams/${groupId}`,
    {},
    { enabled: !!groupId, isNextApi: false, method: "GET" },
  );

  const { data: currentRule } = useApi<{ data: ApiRule[] }>(
    `/api/teams/${teamId}/workflows`,
    {},
    {
      enabled: !!(teamId && (ruleId || isAutoResponderPage)),
      isNextApi: true,
      method: "GET",
    },
  );

  const { data } = useFetchSLAPolicies(teamId as string, isSLAPage);

  const fetchAndSetSourceDetails = async () => {
    try {
      const response = await fetch(
        `/api/workspace/sources/${sourceId}?botUserId=${botUserId}&type=${type}`,
      );
      if (!response.ok) throw new Error("Failed to fetch source details");
      const data = await response.json();
      setCurrentSource(data);
      return data;
    } catch (_error) {}
  };

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchAndSetSourceDetails();
      setCurrentSource(data);
    };
    if (sourceId && botUserId && type) {
      fetchData();
    }
  }, [sourceId, botUserId, type]);

  useEffect(() => {
    const fetchData = async () => {
      setSLAPolicies(data);
    };
    if (teamId && isSLAPage) {
      fetchData();
    }
  }, [teamId, isSLAPage, data]);

  // Derive Auto-responder rules from the single API call
  useEffect(() => {
    if (currentRule?.data && Array.isArray(currentRule.data)) {
      // Find specific rule for breadcrumb
      if (ruleId) {
        const rule = currentRule.data.find(
          (r: ApiRule) =>
            r.uniqueIdentifier === ruleId && r.subType === "AUTO_RESPONDER",
        );

        if (rule) {
          setCurrentAutoResponderRule({
            id: rule.uid,
            name: rule.name,
            uniqueIdentifier: rule.uniqueIdentifier,
            enabled: rule.isActive,
          });
        }
      }
    }
  }, [currentRule, ruleId]);

  const formatBreadcrumb = useCallback(
    (
      path: string,
      teams: Team[],
      helpcenters: HelpcenterList[],
      articles: KBArticles[],
      appStudio: App,
      currentSource: SourceDetails | null,
      botUserId: string | null,
      type: SourceTypes | null,
      app: InstalledAppDto,
    ) => {
      const segments = path.split("/").filter(Boolean);
      const isWorkflowsPath = path.includes("/settings/workflows");
      const isOrgPath = segments.includes("organization");
      return segments
        .filter((segment) => {
          // Remove "dashboard" from workflows pages
          if (segment === "dashboard") return false;
          // Remove "settings" from organization paths
          if (isOrgPath && segment === "settings") return false;
          return true;
        })
        .map((segment, index, segments) => {
          const team = teams?.find((t) => t.uid === segment);
          const helpcenter = helpcenters?.find((t) => t._id === segment);
          const article = articles?.find((t) => t._id === segment);
          const selectedApp = appStudio?.uid === segment;
          const installedApp = app?.appId === segment;
          const selectedSource = currentSource?.id === segment;
          const isLastSegment = index === segments.length - 1;

          // Handle form edit page
          if (segment === "forms") {
            return {
              label: "Forms",
              href: !isLastSegment
                ? `/dashboard/${teamId}/settings/forms`
                : undefined,
            };
          }
          if (segments[index - 1] === "forms" && formId === segment) {
            // Skip if we don't have the form name yet
            if (!formName) {
              return {
                label: "Loading...",
                href: undefined,
              };
            }
            return {
              label: formName,
              href: undefined,
            };
          }
          // Skip the edit segment entirely
          if (segments[index - 1] === formId && segment === "edit") {
            return undefined;
          }

          // Special handling for workflows section
          if (
            segments[index - 1] === "workflows" &&
            (segments[index + 1] === "executions" || !segments[index + 1])
          ) {
            const isOrgWorkflow = segments.includes("organization");
            const basePath = isOrgWorkflow
              ? `/organization/settings/workflows/${segment}`
              : `/dashboard/${teamId}/settings/workflows/${segment}`;

            return {
              label: (
                <WorkflowHeader
                  isExecutionsPage={segments[index + 1] === "executions"}
                />
              ),
              href: workflowName
                ? `${basePath}?workflowName=${encodeURIComponent(workflowName)}`
                : basePath,
            };
          }

          if (isOrgPath && selectedSource) {
            return {
              label:
                currentSource?.name === "web-chat"
                  ? "Web chat"
                  : currentSource?.name,
              href: !isLastSegment
                ? `/organization/settings/sources/${currentSource?.id}?botUserId=${botUserId}&type=${type}`
                : undefined,
            };
          }

          // Special handling for organization settings sources
          if (isOrgPath && segments.includes("sources")) {
            // Handle 'sources' segment
            if (segment === "sources") {
              return {
                label: "Sources",
                href: "/organization/settings/sources",
              };
            }
            // Handle specific source types (like 'email')
            if (segments[index - 1] === "sources") {
              if (segment === "web-chat") {
                return {
                  label: "Web chat",
                  href: "/organization/settings/sources/web-chat",
                };
              }
              return {
                label: segment.charAt(0).toUpperCase() + segment.slice(1),
                href: isLastSegment
                  ? undefined
                  : `/organization/settings/sources/${segment}`,
              };
            }
          }

          if (isOrgPath && selectedApp) {
            return {
              label: appStudio.name,
              href: !isLastSegment
                ? `/organization/settings/apps-studio/${appStudio.uid}`
                : undefined,
            };
          }

          if (isOrgPath && installedApp) {
            return {
              label: app.name,
              href: !isLastSegment
                ? `/organization/settings/apps-studio/${app.appId}`
                : undefined,
            };
          }

          if (team) {
            return {
              label: team.name,
              href: !isLastSegment ? `/dashboard/${segment}` : undefined,
              icon: team.icon || "RocketLaunchIcon",
              color: team.color,
              showIcon: true,
            };
          }
          if (helpcenter) {
            return {
              label: helpcenter.name,
              href: !isLastSegment ? `/helpcenter/${segment}` : undefined,
            };
          }
          if (article) {
            return {
              label: article.title,
              href: !isLastSegment
                ? `/helpcenter/articles/${segment}`
                : undefined,
            };
          }
          if (segment === "settings") {
            const isOrgPath = segments.includes("organization");
            return {
              label: "Settings",
              href: !isLastSegment
                ? isOrgPath
                  ? "/organization/settings"
                  : `/dashboard/${teamId}/settings`
                : undefined,
            };
          }
          if (segment === "workflows") {
            const isOrgWorkflow = segments.includes("organization");
            return {
              label: "Workflows",
              href: !isLastSegment
                ? isOrgWorkflow
                  ? `/organization/settings/workflows`
                  : `/dashboard/${teamId}/settings/workflows`
                : undefined,
            };
          }
          if (segment === "groups") {
            return {
              label: "Groups",
              href: !isLastSegment
                ? `/dashboard/${teamId}/settings/groups`
                : undefined,
            };
          }

          // Handle group ID in breadcrumb
          if (segments[index - 1] === "groups" && currentGroup) {
            return {
              label: currentGroup.name,
              href: !isLastSegment
                ? `/dashboard/${teamId}/settings/groups/${segment}`
                : undefined,
            };
          }
          if (segment === "routing") {
            return {
              label: "Routing",
              href: !isLastSegment
                ? `/dashboard/${teamId}/settings/routing`
                : undefined,
            };
          }
          if (segment === "ticket-fields") {
            return {
              label: "Ticket fields",
              href: !isLastSegment
                ? `/dashboard/${team?.uid}/settings/ticket-fields`
                : undefined,
            };
          }
          if (segment === "sla") {
            return {
              label: "SLAs",
              href: !isLastSegment
                ? `/dashboard/${teamId}/settings/sla`
                : undefined,
            };
          }
          // Handle SLA policy ID in breadcrumb
          if (segments[index - 1] === "sla" && slaPolicies) {
            const slaPolicy = slaPolicies.find(
              (policy) => policy.uid === segment || policy.id === segment,
            );
            if (slaPolicy) {
              return {
                label: slaPolicy.name,
                href: !isLastSegment
                  ? `/dashboard/${teamId}/settings/sla/${segment}`
                  : undefined,
              };
            }
          }
          if (segment === "auto-responder") {
            return {
              label: "Auto-responder",
              href: teamId
                ? `/dashboard/${teamId}/settings/auto-responder`
                : "#",
            };
          }
          // Handle Auto-responder rule ID in breadcrumb
          if (
            segments[index - 2] === "auto-responder" &&
            currentAutoResponderRule
          ) {
            if (
              currentAutoResponderRule.uniqueIdentifier === segment ||
              currentAutoResponderRule.id === segment
            ) {
              return {
                label: currentAutoResponderRule.name,
                href: !isLastSegment
                  ? `/dashboard/${teamId}/settings/auto-responder/edit/${segment}`
                  : "#",
              };
            }
          }
          // Skip the edit/create segment entirely for Auto-responder
          if (
            segments[index - 1] === "auto-responder" &&
            (segment === "edit" || segment === "create")
          ) {
            return undefined;
          }
          if (segment === "csat") {
            return {
              label: "CSAT",
              href: !isLastSegment
                ? `/dashboard/${teamId}/settings/csat`
                : undefined,
            };
          }
          if (fieldId && segment === fieldId && ticketField) {
            return {
              label: ticketField.name,
              href: !isLastSegment
                ? `/dashboard/${team?.uid}/settings/ticket-fields/${fieldId}`
                : undefined,
            };
          }
          if (segment === "apps-studio") {
            return {
              label: "Apps studio",
              href: !isLastSegment
                ? "/organization/settings/apps-studio"
                : undefined,
            };
          }
          if (segment === "articles") {
            return {
              label: "Articles",
              href: !isLastSegment ? `/helpcenter/articles` : undefined,
            };
          }
          if (
            segments[index - 1] === "apps-studio" &&
            segments[index + 1] === "install"
          ) {
            return {
              label: segment,
              href: !isLastSegment
                ? `/organization/settings/apps-studio/${segment}`
                : undefined,
            };
          }
          if (segment === "ms-teams") {
            return {
              label: "MS Teams",
              href: !isLastSegment ? `/${segment}` : undefined,
            };
          }
          if (segment === "organization") {
            return {
              label: "Organization",
              href: undefined,
            };
          }
          if (segment === "accounts") {
            return {
              label: "Accounts",
              href: !isLastSegment ? "/accounts" : undefined,
            };
          }
          if (segment === "account-fields") {
            return {
              label: "Account fields",
              href: !isLastSegment ? "/accounts/account-fields" : undefined,
            };
          }
          // Fallback to original formatting
          return {
            label: segment
              .split("-")
              .map((word, i) =>
                i === 0 ? word.charAt(0).toUpperCase() + word.slice(1) : word,
              )
              .join(" "),
            href: !isLastSegment
              ? isWorkflowsPath
                ? `/dashboard/${teamId}/settings/${segment}`
                : `/${segment}`
              : undefined,
          };
        })
        .filter(Boolean); // Filter out any undefined/null items
    },
    [
      teamId,
      currentGroup,
      groupId,
      ticketField,
      fieldId,
      workflowName,
      formId,
      formName,
      slaPolicies,
      currentAutoResponderRule,
    ],
  );
  const breadcrumbs = formatBreadcrumb(
    pathname,
    teams,
    helpcenters,
    articles,
    selectedApp,
    currentSource,
    botUserId,
    type,
    app,
  );
  const dispatch = useKanbanStorePersist((state) => state.dispatch);
  const view = useKanbanStorePersist((state) => state.view);

  const { toggleSidebar } = useSidebar();

  const setCurrentToggle = (value: typeof view) => {
    dispatch({
      type: "SET_VIEW",
      payload: { view: value },
    });
  };
  const isKanbanPage = pathname === `/dashboard/${teamId}`;
  const isDashboardPage = pathname === `/dashboard`;
  const isInboxPage = pathname.startsWith("/inbox");
  const isWorkflowsPage = pathname.endsWith("/settings/workflows");
  const isOrgWorkflowsPage = pathname.startsWith(
    "/organization/settings/workflows",
  );
  const _isTicketsPage = pathname.includes(`/dashboard/${teamId}/tickets`);
  return !isDashboardPage ? (
    <div className="px-4 py-2 h-[60px] flex justify-between items-center bg-[var(--color-bg-subtle)]">
      <div className="flex items-center gap-2 w-full">
        <PanelLeft
          size={16}
          onClick={toggleSidebar}
          className="cursor-pointer"
          role="button"
        />
        <Separator orientation="vertical" className="h-[14px]" />
        <Breadcrumb items={breadcrumbs} className="!border-none" />
        {isKanbanPage && (
          <>
            <div className="w-1" />
            <ViewSwitcher />
          </>
        )}
        {isInboxPage && renderInboxHeader}
      </div>
      <div className="flex items-center gap-2">
        {isOrgWorkflowsPage ? (
          <Link href={`/organization/settings/workflows/new`}>
            <Button variant="themed" size="xs" className="gap-2">
              <Plus className="h-4 w-4" />
              Create workflow
            </Button>
          </Link>
        ) : isWorkflowsPage ? (
          <Link href={`/dashboard/${teamId}/settings/workflows/new `}>
            <Button variant="themed" size="xs" className="gap-2">
              <Plus className="h-4 w-4" />
              Create workflow
            </Button>
          </Link>
        ) : null}
        {isKanbanPage && (
          <>
            <DisplayOptions />
            <div className="border-[1px] rounded-sm flex items-center h-7 bg-[var(--color-bg-subtle)] min-w-[176px]">
              <ToggleGroup
                className="gap-[2px] p-0.5 w-full h-7"
                type="single"
                value={view}
                onValueChange={(value: typeof view) => {
                  if (value) setCurrentToggle(value);
                }}
              >
                <TooltipWrapper
                  delayDuration={500}
                  tooltipContent="Kanban board view"
                  triggerClassname="h-[22px] flex-1 flex items-center justify-center"
                  asChild
                >
                  <ToggleGroupItem
                    value="BOARD"
                    aria-label="Toggle kanban view"
                    className="h-[22px] rounded-sm p-0 !min-w-4 w-full data-[state=on]:!bg-strong data-[state=on]:!text-strong-foreground flex items-center justify-center gap-2"
                    style={{
                      backgroundColor:
                        view === "BOARD"
                          ? "var(--color-strong)"
                          : "transparent",
                      color:
                        view === "BOARD"
                          ? "var(--color-strong-foreground)"
                          : "inherit",
                    }}
                  >
                    <LayoutGrid size={12} />
                    <span className="text-xs font-medium">Kanban</span>
                  </ToggleGroupItem>
                </TooltipWrapper>
                <TooltipWrapper
                  delayDuration={500}
                  tooltipContent="List view"
                  triggerClassname="h-[22px] flex-1 flex items-center justify-center"
                  asChild
                >
                  <ToggleGroupItem
                    value="LIST"
                    aria-label="Toggle list view"
                    className="h-[22px] rounded-sm p-0 !min-w-4 w-full data-[state=on]:!bg-strong data-[state=on]:!text-strong-foreground flex items-center justify-center gap-2"
                    style={{
                      backgroundColor:
                        view === "LIST" ? "var(--color-strong)" : "transparent",
                      color:
                        view === "LIST"
                          ? "var(--color-strong-foreground)"
                          : "inherit",
                    }}
                  >
                    <List size={12} />
                    <span className="text-xs font-medium">List</span>
                  </ToggleGroupItem>
                </TooltipWrapper>
              </ToggleGroup>
            </div>
          </>
        )}
        {!isDashboardPage && <ChatDialog />}
      </div>
    </div>
  ) : null;
};

export default PrimaryHeader;
