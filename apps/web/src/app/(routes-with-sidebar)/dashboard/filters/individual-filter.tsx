"use client";
/* eslint-disable @typescript-eslint/no-explicit-any */

import CommonSelectWrapper from "@/components/common-select-wrapper";
import TooltipWrapper from "@/components/tooltip-wrapper";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useKanbanStorePersist } from "@/store/kanbanStorePersist";
import { isEmpty } from "lodash";
import { ListFilterPlus } from "lucide-react";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { FilterType } from "./types";
import { getFilterIcon, getFilterName } from "./util";

const filtersToNotVirtualize = ["Urgency", "Sentiment", "Source"];

type Props = {
  item: FilterType;
  queryValuesMap: Record<
    string,
    {
      values: string[];
      filterKey: string;
      disabled?: boolean;
    }
  >;
};

const IndividualFilter = memo(function Filter({ item, queryValuesMap }: Props) {
  const [selectedValues, setSelectedValues] = useState<any[]>([]);
  const [selectedOperator, setSelectedOperator] = useState(item.operator);
  const [operatorDropdownOpen, setOperatorDropdownOpen] = useState(false);

  const [containsQuery, setContainsQuery] = useState(() => {
    const selected = queryValuesMap[item.filterKey]?.values || [];
    const containsText = selected.find((i) => i?.startsWith("$contains:"));
    if (!containsText || typeof containsText !== "string") {
      return "";
    }
    const value = containsText.split(":")[1];
    return value ?? "";
  });

  const stableContainsQuery = useRef(containsQuery);
  useEffect(() => {
    stableContainsQuery.current = containsQuery;
  }, [containsQuery]);

  useEffect(() => {
    const selected = queryValuesMap[item.filterKey]?.values || [];
    const newSelectedValues: any[] = [];

    if (!isEmpty(selected)) {
      if (selected.includes("")) {
        newSelectedValues.push({ Icon: null, label: "Not set", value: "" });
      }
      if (selected.includes("$set")) {
        newSelectedValues.push({ Icon: null, label: "Set", value: "$set" });
      }
      const containsText = selected.find((i) => i?.startsWith("$contains:"));
      if (containsText) {
        const value = containsText.split(":")[1];
        newSelectedValues.push({
          Icon: null,
          label: `"${value}"`,
          value: containsText,
        });
      }
    }

    newSelectedValues.push(
      ...item.values.filter((i) => {
        return selected.includes(i.value);
      }),
    );

    setSelectedValues(newSelectedValues);
  }, [item.filterKey, item.values, queryValuesMap]);

  useEffect(() => {
    if (item.operator) {
      setSelectedOperator(item.operator);
    }
  }, [item.operator]);

  const Icon = useMemo(() => {
    return getFilterIcon(item.label, item.filterKey?.startsWith("custom_"));
  }, [item.filterKey, item.label]);

  const removeFilter = useCallback((value) => {
    useKanbanStorePersist.dispatch({
      type: "REMOVE_FILTER",
      payload: {
        query: value,
      },
    });
  }, []);

  //   const isCustomField = item.filterKey.startsWith("custom_");

  const options = useMemo(() => {
    const updatedOptions = item.values.map((option) => {
      return {
        ...option,
        Icon:
          option.label === "Thena" && option.value !== "API"
            ? null
            : option?.avatar
            ? null
            : option.Icon || null,
      };
    });
    // if (isCustomField) {
    //   updatedOptions.unshift({
    //     label: "Not set",
    //     value: "",
    //     Icon: null,
    //   });
    //   if (item.isTextOrLink) {
    //     updatedOptions.unshift({
    //       label: "Set",
    //       value: "$set",
    //       Icon: null,
    //     });
    //   }
    // }

    return updatedOptions;
  }, [item]);

  const handleContainsApply = useCallback(
    (value: string) => {
      setContainsQuery(value);
      if (value.trim().length) {
        setSelectedValues((prev) => [
          ...prev.filter((i) => !i.value.includes("$contains")),
          { Icon: null, label: value, value: `$contains:${value}` },
        ]);
        // useKanbanStorePersist.dispatch({
        //   type: "SET_QUERY_VALUES_MAP",
        //   payload: {
        //     query: item.name,
        //     values: [
        //       ...selectedValues
        //         .map((i) => i.value)
        //         .filter((i) => !i.includes("$contains")),
        //       `$contains:${value}`,
        //     ],
        //     indexed_key: item.indexed_key,
        //   },
        // });
      } else {
        setSelectedValues((prev) =>
          prev.filter((i) => !i.value.includes("$contains")),
        );
        const remainingValues = selectedValues
          .map((i) => i.value)
          .filter((i) => !i.includes("$contains"));

        useKanbanStorePersist.dispatch({
          type: "SET_QUERY_VALUES_MAP",
          payload: {
            query: item.filterKey,
            values: remainingValues,
            indexed_key: item.filterKey,
            operator: selectedOperator,
          },
        });
      }
    },
     
    [item.filterKey, selectedValues, selectedOperator],
  );

  const onOptionChange = useCallback(
    (e) => {
      if (Array.isArray(e)) {
        useKanbanStorePersist.dispatch({
          type: "SET_QUERY_VALUES_MAP",
          payload: {
            query: item.filterKey,
            values: e.map((i) => i.value),
            indexed_key: item.filterKey,
            operator: selectedOperator,
          },
        });
      }
    },
     
    [item.filterKey, selectedOperator],
  );

  return (
    <div className="flex items-center border rounded text-sm px-2 cursor-pointer h-7">
      <div className="flex items-center gap-1">
        <Icon className="h-[16px] w-[16px]" />
        {getFilterName(item.label)}
        {item.type && (
          <TooltipWrapper 
            tooltipContent={item.type === 'accounts' ? 'Account field' : 'Ticket field'}
            delayDuration={300}
          >
            <Badge 
              variant="outline" 
              className={`ml-1 px-1 py-0 text-[10px] h-4 ${item.type === 'accounts' ? 'text-[var(--color-text-info)] border-[var(--color-border-info-muted)]' : 'text-[var(--color-text-success)] border-[var(--color-border-success-muted)]'}`}
            >
              {item.type === 'accounts' ? 'A' : 'T'}
            </Badge>
          </TooltipWrapper>
        )}
      </div>
      <Select
        defaultValue={selectedOperator}
        onValueChange={(val) => {
          setSelectedOperator(val);
          useKanbanStorePersist.dispatch({
            type: "UPDATE_OPERATOR",
            payload: {
              query: item.filterKey,
              operator: val,
            },
          });
        }}
        open={operatorDropdownOpen}
        onOpenChange={setOperatorDropdownOpen}
      >
        <div className="border-x ml-2 ">
          <SelectTrigger
            hideDropdownIndicator
            className="hover:bg-[hsl(var(--accent))] p-0 border-none h-6 px-2 outline-none rounded-none"
          >
            {selectedOperator === "not" ? <div>is not</div> : <div>is</div>}
          </SelectTrigger>
        </div>
        <SelectContent align="start" className="w-min">
          <SelectItem value="in">
            <span>is</span>
          </SelectItem>
          <SelectItem value="not">
            <span>is not</span>
          </SelectItem>
        </SelectContent>
      </Select>
      <CommonSelectWrapper
        isMulti
        key={item.filterKey}
        placeholder=""
        selectedCountLabel="Selected"
        hideDropdownIndicator
        options={options}
        name={item.filterKey}
        onCancelClick={removeFilter}
        value={selectedValues}
        isVirtualized={!filtersToNotVirtualize.includes(item.filterKey)}
        // fixedLabel={getFilterName(item.label)}
        Icon={ListFilterPlus}
        labelClass="border-none hover:bg-transparent"
        isOptionsMemoized
        onChange={onOptionChange}
        onContainsApply={item.isTextOrLink ? handleContainsApply : undefined}
        containsQuery={stableContainsQuery.current}
      />
    </div>
  );
});

export default IndividualFilter;
