"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { VariableFontHoverByRandomLetter } from "@/components/ui/variable-font-hover-by-random-letter";
import { useDebounce } from "@/hooks/use-debounce";
import {
  searchAll,
  SearchResponse,
  SearchResult,
} from "@/services/client-search-service";
import { Search, X } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";

// Define filter types
type TimeFilter = "24h" | "7d" | "30d" | "365d" | "all";
type FilterState = {
  time: TimeFilter;
  priority: string[];
  status: string[];
  team: string[];
};

export default function SearchPage() {
  const searchParams = useSearchParams();
  const initialQuery = searchParams.get("q") || "";
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const debouncedSearchQuery = useDebounce(searchQuery, 230);
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, _] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter states
  const [filters, setFilters] = useState<FilterState>({
    time: "all",
    priority: [],
    status: [],
    team: [],
  });

  // Available filter options (would normally be populated from API data)
  const [availableFilters, setAvailableFilters] = useState({
    priority: ["High", "Medium", "Low"],
    status: [
      "Waiting on us",
      "Waiting on customer",
      "Triage",
      "Resolved",
      "Closed",
    ],
    team: ["Customer support", "Engineering", "Sales", "Product"],
  });

  // Get search keys from the store
  // const search = useGlobalConfigPersistStore((state) => state.search);
  // const ticketsAndCommentsKey = search?.ticketsAndComments?.key;
  // const accountsKey = search?.accounts?.key;

  // Focus search input on mount
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Handle search errors
  const handleSearchError = (error: unknown) => {
    console.error("Search error:", error);
    setIsLoading(false);
    // You could add error state handling here if needed
  };

  // Extract available filter options from search results
  const extractFilterOptions = useCallback(
    (results: SearchResponse) => {
      if (!results?.results) return;

      const priorities = new Set<string>();
      const statuses = new Set<string>();
      const teams = new Set<string>();

      results.results.forEach((collection) => {
        collection.hits.forEach((hit) => {
          const doc = hit.document;
          if (doc.priorityName) priorities.add(doc.priorityName as string);
          if (doc.statusName) statuses.add(doc.statusName as string);
          if (doc.teamName) teams.add(doc.teamName as string);
        });
      });

      setAvailableFilters({
        priority:
          Array.from(priorities).length > 0
            ? Array.from(priorities)
            : availableFilters.priority,
        status:
          Array.from(statuses).length > 0
            ? Array.from(statuses)
            : availableFilters.status,
        team:
          Array.from(teams).length > 0
            ? Array.from(teams)
            : availableFilters.team,
      });
    },
    [availableFilters],
  );

  // Perform search when debounced query changes
  useEffect(() => {
    if (debouncedSearchQuery) {
      setIsLoading(true);

      searchAll(debouncedSearchQuery)
        .then((results) => {
          setSearchResults(results);
          extractFilterOptions(results);

          // Update URL with search query without using router.replace
          const url = new URL(window.location.href);
          url.searchParams.set("q", debouncedSearchQuery);
          window.history.pushState({}, "", url.toString());
        })
        .catch(handleSearchError)
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [debouncedSearchQuery]);

  // Initial search on mount if query exists
  // useEffect(() => {
  //   if (initialQuery) {
  //     setIsLoading(true);

  //     searchAll(initialQuery)
  //       .then((results) => {
  //         setSearchResults(results);
  //         extractFilterOptions(results);
  //       })
  //       .catch(handleSearchError)
  //       .finally(() => {
  //         setIsLoading(false);
  //       });
  //   }
  // }, [initialQuery]);

  // Toggle a filter value
  const toggleFilter = (type: keyof FilterState, value: string) => {
    setFilters((prev) => {
      if (type === "time") {
        return { ...prev, time: value as TimeFilter };
      } else {
        const currentValues = prev[type] as string[];
        return {
          ...prev,
          [type]: currentValues.includes(value)
            ? currentValues.filter((v) => v !== value)
            : [...currentValues, value],
        };
      }
    });
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      time: "all",
      priority: [],
      status: [],
      team: [],
    });
  };

  // Count total results
  const totalResults =
    searchResults?.results.reduce(
      (total, collection) => total + collection.found,
      0,
    ) || 0;

  // Filter results by collection and applied filters
  const getFilteredResults = () => {
    if (!searchResults) return [];

    let results: (SearchResult & { collection: string })[] = [];
    // Get all results from all collections
    results = searchResults.results.flatMap((collectionResult) => {
      return collectionResult.hits.map((hit) => ({
        ...hit,
        collection: collectionResult.request_params.collection_name,
        document: {
          ...hit.document,
          collection: collectionResult.request_params.collection_name,
        },
      }));
    });

    // Apply time filter
    if (filters.time !== "all") {
      const now = new Date();
      const cutoffDate = new Date();

      switch (filters.time) {
        case "24h":
          cutoffDate.setDate(now.getDate() - 1);
          break;
        case "7d":
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case "30d":
          cutoffDate.setDate(now.getDate() - 30);
          break;
        case "365d":
          cutoffDate.setDate(now.getDate() - 365);
          break;
      }

      results = results.filter((result) => {
        const createdAt = result.document.createdAt as string;
        if (!createdAt) return true; // If no date, include it

        const itemDate = new Date(createdAt);
        return itemDate >= cutoffDate;
      });
    }

    // Apply other filters
    if (filters.priority.length > 0) {
      results = results.filter((result) =>
        filters.priority.includes(result.document.priorityName as string),
      );
    }

    if (filters.status.length > 0) {
      results = results.filter((result) =>
        filters.status.includes(result.document.statusName as string),
      );
    }

    if (filters.team.length > 0) {
      results = results.filter((result) =>
        filters.team.includes(result.document.teamName as string),
      );
    }

    return results;
  };

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.time !== "all") count++;
    count += filters.priority.length;
    count += filters.status.length;
    count += filters.team.length;
    return count;
  };

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <div
        className={`flex-1 overflow-y-auto ${!searchResults && !isLoading ? "flex items-center justify-center" : ""
          }`}
      >
        <div
          className={`mx-auto w-full max-w-[740px] p-4 ${!searchResults && !isLoading ? "" : "h-full"
            }`}
        >
          {/* Heading and search input container */}
          <div className={`w-full transition-all duration-300 ease-in-out`}>
            {/* Heading - only show when no search results */}
            {!searchResults && !isLoading && (
              <div className="mb-6 text-center">
                <VariableFontHoverByRandomLetter
                  label="What are you looking for today?"
                  className="text-2xl font-medium text-foreground md:text-3xl"
                  fromFontVariationSettings="'wght' 400"
                  toFontVariationSettings="'wght' 600"
                  staggerDuration={0.02}
                />
              </div>
            )}

            {/* Search Input */}
            <div className="mb-6 w-full">
              <div className="relative w-full">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <Search className="h-5 w-5 text-muted-foreground" />
                </div>
                <Input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search in Thena"
                  className="h-12 w-full rounded-[4px] border border-input bg-background pl-10 pr-12 text-foreground transition-shadow duration-200 focus:border-primary/30 focus:outline-none focus:ring-0 focus:shadow-[0_0_0_2px_rgba(0,0,0,0.05),0_4px_12px_rgba(0,0,0,0.1)]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  autoFocus
                />
              </div>
            </div>
          </div>

          {/* Results Section */}
          {isLoading && !searchResults ? (
            <div className="w-full py-8 text-center">
              <p className="text-muted-foreground">Searching...</p>
            </div>
          ) : searchResults && (searchResults?.results.length || totalResults > 0) && searchQuery.trim() ? (
            <div className="flex flex-col">
              {/* Results count and filter button */}
              <div className="mb-4 flex items-center justify-between text-sm text-muted-foreground">
                <span>
                  Found {totalResults} results for &ldquo;{searchQuery}&rdquo;
                </span>
                {/* <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="h-4 w-4" />
                  <span>Filters</span>
                  <Badge
                    variant="secondary"
                    className="ml-1 min-w-[20px] text-center"
                  >
                    {getActiveFilterCount() > 0 ? getActiveFilterCount() : "0"}
                  </Badge>
                </Button> */}
              </div>

              {/* Filters Section */}
              {showFilters && (
                <div className="mb-6 rounded-[4px] border border-border bg-muted/20 p-4">
                  <div className="mb-3 flex items-center justify-between">
                    <h3 className="font-medium">Filters</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="h-8 px-2 text-sm"
                    >
                      Clear all
                    </Button>
                  </div>

                  {/* Time Filter */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">Time period</h4>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant={filters.time === "all" ? "default" : "outline"}
                        size="sm"
                        onClick={() => toggleFilter("time", "all")}
                        className="h-8 text-xs"
                      >
                        All time
                      </Button>
                      <Button
                        variant={filters.time === "24h" ? "default" : "outline"}
                        size="sm"
                        onClick={() => toggleFilter("time", "24h")}
                        className="h-8 text-xs"
                      >
                        Last 24 hours
                      </Button>
                      <Button
                        variant={filters.time === "7d" ? "default" : "outline"}
                        size="sm"
                        onClick={() => toggleFilter("time", "7d")}
                        className="h-8 text-xs"
                      >
                        Last 7 days
                      </Button>
                      <Button
                        variant={filters.time === "30d" ? "default" : "outline"}
                        size="sm"
                        onClick={() => toggleFilter("time", "30d")}
                        className="h-8 text-xs"
                      >
                        Last 30 days
                      </Button>
                      <Button
                        variant={
                          filters.time === "365d" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => toggleFilter("time", "365d")}
                        className="h-8 text-xs"
                      >
                        Last 365 days
                      </Button>
                    </div>
                  </div>

                  {/* Priority Filter */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">Priority</h4>
                    <div className="flex flex-wrap gap-2">
                      {availableFilters.priority.map((priority) => (
                        <Button
                          key={priority}
                          variant={
                            filters.priority.includes(priority)
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() => toggleFilter("priority", priority)}
                          className="h-8 text-xs"
                        >
                          {priority}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Status Filter */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">Status</h4>
                    <div className="flex flex-wrap gap-2">
                      {availableFilters.status.map((status) => (
                        <Button
                          key={status}
                          variant={
                            filters.status.includes(status)
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() => toggleFilter("status", status)}
                          className="h-8 text-xs"
                        >
                          {status}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Team Filter */}
                  <div>
                    <h4 className="text-sm font-medium mb-2">Team</h4>
                    <div className="flex flex-wrap gap-2">
                      {availableFilters.team.map((team) => (
                        <Button
                          key={team}
                          variant={
                            filters.team.includes(team) ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => toggleFilter("team", team)}
                          className="h-8 text-xs"
                        >
                          {team}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Active filters display */}
              {getActiveFilterCount() > 0 && (
                <div className="mb-4 flex flex-wrap gap-2">
                  {filters.time !== "all" && (
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <span>
                        {filters.time === "24h"
                          ? "Last 24 hours"
                          : filters.time === "7d"
                            ? "Last 7 days"
                            : filters.time === "30d"
                              ? "Last 30 days"
                              : "Last 365 days"}
                      </span>
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => toggleFilter("time", "all")}
                      />
                    </Badge>
                  )}

                  {filters.priority.map((priority) => (
                    <Badge
                      key={priority}
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <span>Priority: {priority}</span>
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => toggleFilter("priority", priority)}
                      />
                    </Badge>
                  ))}

                  {filters.status.map((status) => (
                    <Badge
                      key={status}
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <span>Status: {status}</span>
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => toggleFilter("status", status)}
                      />
                    </Badge>
                  ))}

                  {filters.team.map((team) => (
                    <Badge
                      key={team}
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <span>Team: {team}</span>
                      <X
                        className="h-3 w-3 ml-1 cursor-pointer"
                        onClick={() => toggleFilter("team", team)}
                      />
                    </Badge>
                  ))}
                </div>
              )}

              {/* Search Results */}
              <div className="space-y-4 pb-4">
                {getFilteredResults().map((result, index) => (
                  <SearchResultCard key={index} result={result} />
                ))}
              </div>
            </div>
          ) : searchQuery.trim() ? (
            <div className="w-full py-8 text-center">
              <p className="text-muted-foreground">
                No results found for &ldquo;{searchQuery}&rdquo;
              </p>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}

// Define interface for document properties
interface CommentDocument {
  authorFirstName?: string;
  authorLastName?: string;
  authorName?: string;
  createdBy?: string;
  created_by?: string;
  userFirstName?: string;
  userLastName?: string;
  userName?: string;

  authorAvatarUrl?: string;
  createdByAvatarUrl?: string;
  avatarUrl?: string;
  avatar?: string;
  profilePicture?: string;
  profilePictureUrl?: string;
  userAvatarUrl?: string;

  ticketTitle?: string;
  ticketId?: string;
  ticket_id?: string;
  teamId?: string;
  teamIdentifier?: string;

  customerContactFirstName?: string;
  customerContactLastName?: string;
  customerContactAvatarUrl?: string;

  content?: string;
  description?: string;
  title?: string;
  subject?: string;
  name?: string;

  createdAt?: string;

  // Other properties
  id?: string;
  collection?: string;
}

// Helper function to render comment author
const renderCommentAuthor = (document: CommentDocument) => {
  if (!(document.authorFirstName || document.authorLastName || document.authorName ||
    document.createdBy || document.created_by || document.userFirstName ||
    document.userLastName || document.userName)) {
    return null;
  }

  const authorName = document.authorName
    ? document.authorName
    : document.createdBy
      ? document.createdBy
      : document.created_by
        ? document.created_by
        : document.userName
          ? document.userName
          : document.userFirstName || document.userLastName
            ? `${document.userFirstName || ""} ${document.userLastName || ""}`.trim()
            : `${document.authorFirstName || ""} ${document.authorLastName || ""}`.trim();

  const avatarSrc = document.authorAvatarUrl || document.createdByAvatarUrl ||
    document.avatarUrl || document.avatar || document.profilePicture ||
    document.profilePictureUrl || document.userAvatarUrl || "";

  const getInitials = () => {
    if (document.authorName) return (document.authorName.charAt(0) || "").toUpperCase();
    if (document.createdBy) return (document.createdBy.charAt(0) || "").toUpperCase();
    if (document.created_by) return (document.created_by.charAt(0) || "").toUpperCase();
    if (document.userName) return (document.userName.charAt(0) || "").toUpperCase();
    if (document.userFirstName) return (document.userFirstName.charAt(0) || "").toUpperCase();
    if (document.userLastName) return (document.userLastName.charAt(0) || "").toUpperCase();
    return ((document.authorFirstName?.charAt(0) || "") +
      (document.authorLastName?.charAt(0) || ""));
  };

  return (
    <div className="flex items-center gap-1.5 ml-2">
      <span className="text-xs text-primary/80 whitespace-nowrap">{authorName}</span>
      <Avatar className="h-5 w-5 flex-shrink-0">
        <AvatarImage src={avatarSrc} alt={authorName} />
        <AvatarFallback className="text-[10px] bg-primary/10 text-primary">
          {getInitials()}
        </AvatarFallback>
      </Avatar>
    </div>
  );
};

// Helper function to render comment date
const renderCommentDate = (dateString?: unknown) => {
  if (!dateString) return null;

  try {
    const date = new Date(String(dateString));
    if (isNaN(date.getTime())) return null;

    return (
      <div className="inline-flex items-center px-2 py-1 rounded-[4px] bg-muted text-muted-foreground">
        <span className="font-medium mr-1">Posted:</span>{" "}
        {date.toLocaleDateString(undefined, {
          year: "numeric",
          month: "short",
          day: "numeric",
        })}
      </div>
    );
  } catch (_e) {
    return null;
  }
};

function SearchResultCard({
  result,
}: {
  result: SearchResult & { collection: string };
}) {
  const router = useRouter();
  const document = result.document || {};
  // Get the URL for the search result
  // const getResultUrl = (result: SearchResult & { collection: string }) => {
  //   try {
  //     // Use collection from both result and document
  //     const collection = result.collection || document.collection;
  //     console.log("_collection", collection);
  //     if (!collection || !document.id) {
  //       return null;
  //     }

  //     let url;
  //     switch (collection) {
  //       case "tickets":
  //         const teamId = document.teamId || document.teamIdentifier;
  //         console.log("_teamId", teamId);
  //         url = teamId ? `/dashboard/${teamId}?ticketId=${document.uid || document.id}` : null;
  //         console.log("_uid", document.uid);
  //         console.log("_id", document.id);
  //         console.log("_url_1", url);
  //         break;

  //       case "comments":
  //         const commentTeamId = document.teamId || document.teamIdentifier;
  //         const ticketId = document.ticketId || document.ticket_id;
  //         url =
  //           commentTeamId && ticketId
  //             ? `/dashboard/${commentTeamId}?ticketId=${ticketId}&cid=${document.id}`
  //             : null;
  //         break;

  //       case "accounts":
  //         url = `/accounts?accountId=${document.id}`;
  //         break;

  //       default:
  //         url = null;
  //     }
  //     console.log("_url", url);
  //     return url;
  //   } catch (error) {
  //     console.log("_error", error);
  //     return null;
  //   }
  // };

  // Helper functions to safely access document properties
  const getTitle = (): string => {
    // For comments, try to use the ticket title or a more descriptive fallback
    if (result.collection === "comments") {
      return (
        (document.ticketTitle as string) ||
        `Comment on ticket ${document.ticketId || document.ticket_id || ""}`
      );
    }

    // For other result types, use the existing logic
    return (
      (document.title as string) ||
      (document.subject as string) ||
      (document.name as string) ||
      "Untitled"
    );
  };

  const getContent = (): string => {
    if (result.collection === "comments" && document.content) {
      return document.content as string;
    }

    if (result.collection === "tickets") {
      if (document.description) return document.description as string;
      if (document.content) return document.content as string;
    }

    return (document.content as string) || "No content available";
  };

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    const url = document.uid ? `/dashboard/${document.teamId || document.teamIdentifier}?ticketId=${document.uid || document.id}` : null;//getResultUrl(result);
    if (url) {
      router.push(`/dashboard/${document.teamUid || document.teamIdentifier}?ticketId=${document.uid || document.id}`);
    }
  };

  return (
    <div
      onClick={handleClick}
      className="group w-full rounded-[4px] border border-border/40 hover:border-border bg-background hover:shadow-sm transition-all duration-200 cursor-pointer overflow-hidden block no-underline text-foreground"
    >
      <div className="px-4 py-3">
        {/* Header with title and collection type */}
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-base font-medium text-foreground line-clamp-1">
            {getTitle()}
          </h3>
          <div className="flex items-center gap-2">
            {/* Collection type */}
            <div className="text-xs font-medium text-muted-foreground bg-muted/50 px-2 py-1 rounded-[4px]">
              {result.collection === "tickets"
                ? "Ticket"
                : result.collection === "comments"
                  ? "Comment"
                  : result.collection === "accounts"
                    ? "Account"
                    : result.collection
                      ? result.collection.charAt(0).toUpperCase() +
                      result.collection.slice(1)
                      : ""}
            </div>

            {/* Comment author - show when result is a comment */}
            {result.collection === "comments" && renderCommentAuthor(document)}

            {/* Customer contact avatar and name - only show if customer contact info exists */}
            {(document.customerContactFirstName ||
              document.customerContactLastName) && (
                <div className="flex items-center gap-1.5 ml-2">
                  {/* Only show name if it's not already in the title */}
                  {!getTitle().includes(
                    (document.customerContactFirstName as string) || "",
                  ) &&
                    !getTitle().includes(
                      (document.customerContactLastName as string) || "",
                    ) && (
                      <span className="text-xs text-primary/80 whitespace-nowrap">
                        {(document.customerContactFirstName as string) || ""}{" "}
                        {(document.customerContactLastName as string) || ""}
                      </span>
                    )}
                  <Avatar className="h-5 w-5 flex-shrink-0">
                    <AvatarImage
                      src={(document.customerContactAvatarUrl as string) || ""}
                      alt={`${(document.customerContactFirstName as string) || ""
                        } ${(document.customerContactLastName as string) || ""
                        }`.trim()}
                    />
                    <AvatarFallback className="text-[10px] bg-primary/10 text-primary">
                      {(document.customerContactFirstName as string)
                        ? (document.customerContactFirstName as string)
                          .charAt(0)
                          .toUpperCase()
                        : ""}
                      {(document.customerContactLastName as string)
                        ? (document.customerContactLastName as string)
                          .charAt(0)
                          .toUpperCase()
                        : ""}
                    </AvatarFallback>
                  </Avatar>
                </div>
              )}
          </div>
        </div>

        {/* Matched highlight section */}
        {result.highlights && result.highlights.length > 0 && (
          <div className="mb-3 text-sm text-foreground/80">
            <div
              className="line-clamp-2"
              dangerouslySetInnerHTML={{ __html: result.highlights[0].snippet }}
            />
          </div>
        )}

        {/* Content preview if no highlights */}
        {(!result.highlights || result.highlights.length === 0) && (
          <div
            className="mb-3 text-sm text-foreground/80 line-clamp-2 max-h-[3rem] overflow-hidden"
            dangerouslySetInnerHTML={{ __html: getContent() }}
          />
        )}

        {/* Metadata tags in a clean layout */}
        <div className="flex flex-wrap gap-1.5 mt-2 text-xs">
          {/* For comments, show the creation date */}
          {result.collection === "comments" && renderCommentDate(document.createdAt)}

          {/* ID - display in the format teamIdentifier-ticketId */}
          {(document.teamIdentifier || document.TeamIdentifier) &&
            (document.ticketId || document.TicketId) && (
              <div className="inline-flex items-center px-2 py-1 rounded-[4px] bg-muted text-muted-foreground">
                <span className="font-medium mr-1">ID:</span>
                {`${document.teamIdentifier || document.TeamIdentifier}-${document.ticketId || document.TicketId
                  }`}
              </div>
            )}

          {/* Team */}
          {document.teamName && (
            <div className="inline-flex items-center px-2 py-1 rounded-[4px] bg-muted text-muted-foreground">
              <span className="font-medium mr-1">Team:</span>{" "}
              {document.teamName as string}
            </div>
          )}

          {/* Status */}
          {document.statusName && (
            <div className="inline-flex items-center px-2 py-1 rounded-[4px] bg-muted text-muted-foreground">
              <span className="font-medium mr-1">Status:</span>{" "}
              {document.statusName as string}
            </div>
          )}

          {/* Priority */}
          {document.priorityName && (
            <div className="inline-flex items-center px-2 py-1 rounded-[4px] bg-muted text-muted-foreground">
              <span className="font-medium mr-1">Priority:</span>{" "}
              {document.priorityName as string}
            </div>
          )}

          {/* accountName */}
          {document.accountName && (
            <div className="inline-flex items-center px-2 py-1 rounded-[4px] bg-muted text-muted-foreground">
              <span className="font-medium mr-1">Account:</span>{" "}
              {document.accountName as string}
            </div>
          )}

          {/* requestorEmail */}
          {/* To be replaced with customerContactName */}
          {document.customerContactName && (
            <div className="inline-flex items-center px-2 py-1 rounded-[4px] bg-muted text-muted-foreground">
              <span className="font-medium mr-1">Requester:</span>{" "}
              {document.customerContactName as string}
            </div>
          )}

          {/* Region */}
          {document.region && (
            <div className="inline-flex items-center px-2 py-1 rounded-[4px] bg-muted text-muted-foreground">
              <span className="font-medium mr-1">Region:</span>{" "}
              {document.region as string}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
