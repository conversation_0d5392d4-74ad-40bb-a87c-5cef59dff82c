"use client";

import { getUserSessionDetails } from "@/app/actions/auth";
import Providers from "@/components/providers";
import { DashboardSidebar } from "@/components/sidebar/sidebar";
import Thena<PERSON>oader from "@/components/thena-loader";
import { SidebarProvider } from "@/components/ui/sidebar";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useApi } from "@/hooks/use-api";
import { WebChatWidget } from "@/hooks/use-web-chat-init";
import { getAllSidebarPreferences } from "@/lib/user-preferences";
import { GET_ALL_TEAMS } from "@/services/kanban";
import { useChatStore } from "@/store/chat-store";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { Team } from "@/types/kanban";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { GET_ALL_SOURCES } from "../../services/ticket-sources";
import { Source, useAppsSourcesStore } from "../../store/apps-sources-store";
import { getOrgDetails } from "../../utils/browserUtils";
import AccountPrimaryHeader from "./dashboard/account-primary-header";
import ContactPrimaryHeader from "./dashboard/contact-primary-header";
import InsightsPrimaryHeader from "./dashboard/insights-primary-header";
import PrimaryHeader from "./dashboard/primary-header";

interface ThenaWidgetWindow extends Window {
  thenaWidget?: WebChatWidget;
  thena?: {
    open: () => void;
    close: () => void;
    toggle: () => void;
    toggleDarkMode: () => void;
    toggleVisibility: () => void;
  };
}

interface TimeSlot {
  start: string;
  end: string;
}

interface DailyConfig {
  isActive: boolean;
  slots: TimeSlot[];
}

interface UserDetailsResponse {
  dailyConfig?: {
    [key: string]: DailyConfig;
  };
  id: string;
  name: string;
  email: string;
  userType: "ORG_ADMIN" | string;
  status: "ACTIVE" | "INACTIVE" | string;
  isActive: boolean;
  avatarUrl: string;
  timezone: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

function DashboardLayoutContent({ children }: { children: React.ReactNode }) {
  const userId = useGlobalConfigPersistStore((state) => state)?.currentUser
    ?.uid;
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const searchKeys = useGlobalConfigPersistStore(
    (state) => state?.search?.ticketsAndComments?.key,
  );
  const loader = useGlobalConfigPersistStore((state) => state.loader);
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);
  const [isHydrated, setIsHydrated] = useState(false);
  const pathname = usePathname();
  const isAccountsPage =
    pathname?.includes("/accounts") &&
    !pathname?.includes("/organization/settings/accounts") &&
    !pathname?.includes("/accounts/contacts");
  const isContactsPage = pathname?.includes("/accounts/contacts");
  const isInsightsPage = pathname?.includes("/insights");
  const isOrgAccountSettingsPage = pathname?.includes(
    "/organization/settings/accounts",
  );
  const isDashboardPage = pathname === `/dashboard`;
  const isChooseOrgPage = pathname === `/getting-started/choose-organization`;
  const router = useRouter();
  const { orgUid, orgId } = getOrgDetails();
  const [hasOrgs, setHasOrgs] = useState(false);
  const hasRedirected = useRef(false);
  const shimLoadedRef = useRef(false); // To ensure shim loads only once

  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      setHasOrgs(!!searchParams.get("fetchOrgs"));
    }
  }, []);

  const { data: userData } = useApi<UserDetailsResponse>(
    "/v1/users/details",
    {},
    { enabled: !!userId },
  );

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    const fetchUserSession = async () => {
      const result = await getUserSessionDetails(orgUid, orgId);
      const userData = result?.data?.user;
      if (userData) {
        dispatch({
          type: "SET_CURRENT_USER",
          payload: {
            currentUser: {
              id: userData.id,
              email: userData.email,
              name: userData.name,
              metadata: userData.metadata,
              uid: userData.currentUserUid || null,
              avatarUrl: userData?.avatarUrl || null,
              allUserIds: userData.allUserIds || [],
            },
          },
        });
        dispatch({
          type: "SET_ORGS",
          payload: {
            orgs: userData.allOrgs || [],
            currentOrgId: userData.currentOrgId || null,
          },
        });
        dispatch({
          type: "SET_CURRENT_USER_UID",
          payload: {
            uid: userData.currentUserUid || null,
          },
        });
        dispatch({
          type: "SET_CURRENT_ORG",
          payload: {
            currentOrgId: userData.currentOrgId || null,
          },
        });
        // Remove fetchOrgs from URL after fetching
        if (hasOrgs) {
          const url = new URL(window.location.href);
          url.searchParams.delete("fetchOrgs");
          // Use history.replaceState instead of router.replace
          window.history.replaceState({}, "", url.toString());
          // Update the hasOrgs state after URL change
          setHasOrgs(false);
        }
      }
    };
    if ((!currentUser || !!hasOrgs || !currentUser?.uid) && isHydrated) {
      fetchUserSession();
    }
  }, [currentUser, dispatch, isHydrated, hasOrgs]);

  useEffect(() => {
    const fetchSearchSettings = async () => {
      if (!searchKeys) {
        try {
          const searchSettingsResponse = await fetch(
            `/api/search/settings?orgId=${orgId}`,
          );
          if (searchSettingsResponse.ok) {
            const searchSettings = await searchSettingsResponse.json();
            dispatch({
              type: "SET_SEARCH_KEYS",
              payload: {
                ticketsAndComments: searchSettings.ticketsAndComments,
                accounts: searchSettings.accounts,
                teams: searchSettings.teams,
              },
            });
          }
        } catch (error) {
          console.error("Error fetching search settings:", error);
        }
      }
    };
    if (!!orgId && orgId !== "null" && orgId !== "undefined") {
      fetchSearchSettings();
    }
  }, [searchKeys, orgId]);

  const { data: teams, loading } = useApi<Team[]>(
    GET_ALL_TEAMS(userId),
    {},
    {
      isNextApi: true,
      enabled: !!userId,
    },
  );
  const isOpen = useChatStore((state) => state.isOpen);
  // Fetch Sources
  const { data: sources } = useApi<{ sources: Source[] }>(
    GET_ALL_SOURCES,
    {},
    { isNextApi: true, enabled: !!orgUid },
  );

  useEffect(() => {
    //TODO: Remove this once we have a way to update params when switching workspace
    if (teams?.length === 0) {
      router.push("/dashboard");
    }
    if (teams?.length > 0) {
      const nonDeletedTeams = teams.filter((team) => !team.deleted_at);

      useTicketMetaStore.getState().setAllTeams(nonDeletedTeams);
    }

    if (sources?.sources?.length > 0) {
      useAppsSourcesStore.getState().dispatch({
        type: "SET_SOURCES",
        payload: { sources: sources.sources },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [teams, sources]);

  // Update user avatar from API data
  const isFirstRun = useRef(true);

  useEffect(() => {
    if (isFirstRun.current) {
      isFirstRun.current = false;
      return;
    }

    if (
      userData &&
      currentUser &&
      (userData?.avatarUrl !== currentUser?.avatarUrl ||
        userData?.name !== currentUser?.name)
    ) {
      const updatedAllUserIds =
        currentUser?.allUserIds?.map((user) =>
          user.uid === currentUser?.uid
            ? { ...user, avatarUrl: userData?.avatarUrl, name: userData?.name }
            : user,
        ) || [];

      dispatch({
        type: "UPDATE_CURRENT_USER",
        payload: {
          name: userData?.name,
          avatarUrl: userData?.avatarUrl,
          allUserIds: updatedAllUserIds,
        },
      });
    }
  }, [userData]);

  // Handle redirection to last saved team when on dashboard root
  useEffect(() => {
    if (hasRedirected.current) return; // Prevent redundant redirects

    if (isDashboardPage && currentUser?.id && orgId) {
      const redirectToLastTeam = async () => {
        try {
          const { lastTeam } = await getAllSidebarPreferences(
            currentUser.id,
            orgId,
          );

          if (lastTeam) {
            // Redirect to the last saved team
            router.replace(`/dashboard/${lastTeam}`);
            hasRedirected.current = true;
          } else if (teams && teams.length > 0) {
            // Fallback to the first team if no last team is saved
            router.replace(`/dashboard/${teams[0].uid}`);
            hasRedirected.current = true;
          }
        } catch (error) {
          console.error("Error loading last clicked team in layout:", error);
        }
      };

      redirectToLastTeam();
    }
  }, [isDashboardPage, currentUser?.id, orgId, teams, router]);

  const showWidget = useGlobalConfigPersistStore((state) => state.showWidget);
  useEffect(() => {
    // Initialize Thena Widget
    const globalWindow = window as ThenaWidgetWindow;
    if (!showWidget) {
      const element = document.getElementById("thena-widget-launcher");
      if (element) {
        element.style.display = "none";
      }
      // Close the widget if it's open
      if (globalWindow.thena?.close) {
        globalWindow.thena.close();
      }
    } else {
      const element = document.getElementById("thena-widget-launcher");
      if (element) {
        element.style.display = "flex";
      }
    }
    if (
      !shimLoadedRef.current &&
      typeof window !== "undefined" &&
      currentUser?.email &&
      currentUser?.name
    ) {
      shimLoadedRef.current = true;

      globalWindow.thenaWidget = {
        baseUrl: process.env.NEXT_PUBLIC_AGENT_STUDIO_URL,
        apiKey: process.env.NEXT_PUBLIC_CHAT_WIDGET_API_KEY,
        agentId: process.env.NEXT_PUBLIC_CHAT_WIDGET_AGENT_ID,
        wsEndpoint: process.env.NEXT_PUBLIC_AGENT_STUDIO_WS_URL,
        user: {
          email: currentUser.email,
          name: currentUser.name,
        },
        useCustomLauncher: false, // Keep existing or make env var if needed
        themeColorStart: "#6A00FF",
        themeColorEnd: "#3B01B7",
        gradientDirection: "to right bottom",
      };

      const thenaShimScript = document.createElement("script");
      thenaShimScript.src = process.env.NEXT_PUBLIC_WIDGET_CDN_URL;
      thenaShimScript.async = true;
      thenaShimScript.onload = () => {
        console.log("Thena shim script loaded successfully.");
      };
      thenaShimScript.onerror = () => {
        console.error("Failed to load Thena shim script.");
        shimLoadedRef.current = false;
      };

      if (showWidget) {
        document.body.appendChild(thenaShimScript);
      }
    }
  }, [currentUser, showWidget]);

  if (
    loading ||
    !isHydrated ||
    !currentUser ||
    !currentUser?.uid ||
    (isChooseOrgPage && loader)
  ) {
    return (
      <ThenaLoader className="h-screen w-full flex items-center justify-center" />
    );
  }

  return (
    <Providers>
      <SidebarProvider>
        <TooltipProvider delayDuration={700}>
          <div className="flex min-h-screen w-screen bg-color-bg-subtle grow">
            <DashboardSidebar />
            <main
              className={`flex-1 overflow-auto transition-all duration-300 ${
                isOpen ? "mr-[432px]" : ""
              }`}
            >
              {isOrgAccountSettingsPage ? (
                <PrimaryHeader />
              ) : isContactsPage ? (
                <ContactPrimaryHeader />
              ) : isAccountsPage ? (
                <AccountPrimaryHeader />
              ) : isInsightsPage ? (
                <InsightsPrimaryHeader />
              ) : (
                <PrimaryHeader />
              )}
              <div className="px-4">
                <div
                  className={`bg-background overflow-hidden ${
                    isDashboardPage
                      ? "h-full"
                      : "h-[calc(100vh-75px)] border rounded-sm"
                  }`}
                >
                  {children}
                </div>
              </div>
            </main>
          </div>
        </TooltipProvider>
      </SidebarProvider>
    </Providers>
  );
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <DashboardLayoutContent>{children}</DashboardLayoutContent>;
}
