"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Activity,
  Archive,
  BarChart3,
  Bell,
  BookOpen,
  Building2,
  CheckSquare,
  Clock,
  Code2,
  Columns,
  Database,
  ExternalLink,
  Eye,
  FileText,
  Filter,
  FormInput,
  Globe,
  Hash,
  HelpCircle,
  Inbox,
  Info,
  Layers,
  LayoutGrid,
  Lock,
  Mail,
  MessageCircle,
  MessageSquare,
  Phone,
  Play,
  Puzzle,
  Search,
  Smile,
  Tag,
  Tags,
  Ticket,
  Timer,
  UserCircle2,
  Users,
  UsersRound,
  Workflow,
  Zap,
} from "lucide-react";
import Image from "next/image";
import { useEffect, useMemo, useState } from "react";
import styles from "./guide.module.css";

// Feature illustration components
const TicketsIllustration = () => (
  <div className={styles.iconContainer}>
    <Ticket className={styles.ticketIcon} strokeWidth={1.5} />
  </div>
);

const GroupsIllustration = () => (
  <div className={styles.iconContainer}>
    <Users className={styles.groupsIcon} strokeWidth={1.5} />
  </div>
);

const RoutingIllustration = () => (
  <div className={styles.iconContainer}>
    <Layers className={styles.routingIcon} strokeWidth={1.5} />
  </div>
);

const StatusesIllustration = () => (
  <div className={styles.iconContainer}>
    <BarChart3 className={styles.statusesIcon} strokeWidth={1.5} />
  </div>
);

const SLAIllustration = () => (
  <div className={styles.iconContainer}>
    <Clock className={styles.slaIcon} strokeWidth={1.5} />
  </div>
);

const _NotificationsIllustration = () => (
  <div className={styles.iconContainer}>
    <Bell className={styles.notificationsIcon} strokeWidth={1.5} />
  </div>
);

const EmailNotificationsIllustration = () => (
  <div className={styles.iconContainer}>
    <Mail className={styles.emailNotificationsIcon} strokeWidth={1.5} />
  </div>
);

const SlackNotificationsIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageSquare
      className={styles.slackNotificationsIcon}
      strokeWidth={1.5}
    />
  </div>
);

const ToastNotificationsIllustration = () => (
  <div className={styles.iconContainer}>
    <Bell className={styles.toastNotificationsIcon} strokeWidth={1.5} />
  </div>
);

const CustomerPortalIllustration = () => (
  <div className={styles.iconContainer}>
    <Globe className={styles.customerPortalIcon} strokeWidth={1.5} />
  </div>
);

const TagsIllustration = () => (
  <div className={styles.iconContainer}>
    <Tags className={styles.tagsIcon} strokeWidth={1.5} />
  </div>
);

const FormsIllustration = () => (
  <div className={styles.iconContainer}>
    <FileText className={styles.formsIcon} strokeWidth={1.5} />
  </div>
);

const AccountsIllustration = () => (
  <div className={styles.iconContainer}>
    <Building2 className={styles.accountsIcon} strokeWidth={1.5} />
  </div>
);

const ContactsIllustration = () => (
  <div className={styles.iconContainer}>
    <UserCircle2 className={styles.contactsIcon} strokeWidth={1.5} />
  </div>
);

const NotesIllustration = () => (
  <div className={styles.iconContainer}>
    <FileText className={styles.notesIcon} strokeWidth={1.5} />
  </div>
);

const TasksIllustration = () => (
  <div className={styles.iconContainer}>
    <CheckSquare className={styles.tasksIcon} strokeWidth={1.5} />
  </div>
);

const InternalThreadsIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageSquare className={styles.internalThreadsIcon} strokeWidth={1.5} />
  </div>
);

const HelpCentersIllustration = () => (
  <div className={styles.iconContainer}>
    <HelpCircle className={styles.helpCentersIcon} strokeWidth={1.5} />
  </div>
);

const AIAgentsIllustration = () => (
  <div className={styles.iconContainer}>
    <Zap className={styles.aiAgentsIcon} strokeWidth={1.5} />
  </div>
);

const AccountFieldsIllustration = () => (
  <div className={styles.iconContainer}>
    <Database className={styles.accountFieldsIcon} strokeWidth={1.5} />
  </div>
);

const ContactFieldsIllustration = () => (
  <div className={styles.iconContainer}>
    <Database className={styles.contactFieldsIcon} strokeWidth={1.5} />
  </div>
);

const FlowsIllustration = () => (
  <div className={styles.iconContainer}>
    <Workflow className={styles.workflowsIcon} strokeWidth={1.5} />
  </div>
);

const _AppsIllustration = () => (
  <div className={styles.iconContainer}>
    <LayoutGrid className={styles.appsIcon} strokeWidth={1.5} />
  </div>
);

const _SlackIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageSquare className={styles.slackIcon} strokeWidth={1.5} />
  </div>
);

const _EmailIllustration = () => (
  <div className={styles.iconContainer}>
    <Mail className={styles.emailIcon} strokeWidth={1.5} />
  </div>
);

const _WebChatIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageCircle className={styles.webChatIcon} strokeWidth={1.5} />
  </div>
);

const _MSTeamsIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageSquare className={styles.msTeamsIcon} strokeWidth={1.5} />
  </div>
);

const _TicketingCategoryIllustration = () => (
  <Ticket className={styles.categoryIcon} />
);

const _AccountsCategoryIllustration = () => (
  <Building2 className={styles.categoryIcon} />
);

const _KnowledgeBaseCategoryIllustration = () => (
  <BookOpen className={styles.categoryIcon} />
);

const _AIAgentsCategoryIllustration = () => (
  <Zap className={styles.categoryIcon} />
);

const CSATIllustration = () => (
  <div className={styles.iconContainer}>
    <Globe className={styles.slaIcon} strokeWidth={1.5} />
  </div>
);

const WorkingHoursIllustration = () => (
  <div className={styles.iconContainer}>
    <Timer className={styles.slaIcon} strokeWidth={1.5} />
  </div>
);

const ActivityIllustration = () => (
  <div className={styles.iconContainer}>
    <Activity className={styles.activityIcon} strokeWidth={1.5} />
  </div>
);

const AttributesIllustration = () => (
  <div className={styles.iconContainer}>
    <Eye className={styles.attributesIcon} strokeWidth={1.5} />
  </div>
);

const ArticlesIllustration = () => (
  <div className={styles.iconContainer}>
    <Hash className={styles.articlesIcon} strokeWidth={1.5} />
  </div>
);

const WYSIWYGEditorIllustration = () => (
  <div className={styles.iconContainer}>
    <Lock className={styles.wysiwygEditorIcon} strokeWidth={1.5} />
  </div>
);

const LivePresenceIllustration = () => (
  <div className={styles.iconContainer}>
    <Play className={styles.livePresenceIcon} strokeWidth={1.5} />
  </div>
);

const CommentsIllustration = () => (
  <div className={styles.iconContainer}>
    <Filter className={styles.commentsIcon} strokeWidth={1.5} />
  </div>
);

const CustomDomainsIllustration = () => (
  <div className={styles.iconContainer}>
    <LayoutGrid className={styles.customDomainsIcon} strokeWidth={1.5} />
  </div>
);

const KnowledgeIllustration = () => (
  <div className={styles.iconContainer}>
    <Database className={styles.knowledgeIcon} strokeWidth={1.5} />
  </div>
);

const AuthorizationIllustration = () => (
  <div className={styles.iconContainer}>
    <Lock className={styles.authorizationIcon} strokeWidth={1.5} />
  </div>
);

const ExecutionsIllustration = () => (
  <div className={styles.iconContainer}>
    <Play className={styles.executionsIcon} strokeWidth={1.5} />
  </div>
);

const _WorkflowsCategoryIllustration = () => (
  <Workflow className={styles.categoryIcon} />
);

const _OthersCategoryIllustration = () => (
  <Puzzle className={styles.categoryIcon} />
);

const TicketingWorkflowsIllustration = () => (
  <div className={styles.iconContainer}>
    <Ticket className={styles.ticketingWorkflowsIcon} strokeWidth={1.5} />
  </div>
);

const AccountWorkflowsIllustration = () => (
  <div className={styles.iconContainer}>
    <Building2 className={styles.accountWorkflowsIcon} strokeWidth={1.5} />
  </div>
);

const SearchIllustration = () => (
  <div className={styles.iconContainer}>
    <Search className={styles.searchFeatureIcon} strokeWidth={1.5} />
  </div>
);

const ViewsIllustration = () => (
  <div className={styles.iconContainer}>
    <LayoutGrid className={styles.routingIcon} strokeWidth={1.5} />
  </div>
);

const FiltersIllustration = () => (
  <div className={styles.iconContainer}>
    <Filter className={styles.notesIcon} strokeWidth={1.5} />
  </div>
);

const DisplayIllustration = () => (
  <div className={styles.iconContainer}>
    <Eye className={styles.tagsIcon} strokeWidth={1.5} />
  </div>
);

const SlackTriageIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageSquare className={styles.slackIcon} strokeWidth={1.5} />
  </div>
);

const EmojiActionsIllustration = () => (
  <div className={styles.iconContainer}>
    <Smile className={styles.tagsIcon} strokeWidth={1.5} />
  </div>
);

const ArchiveIllustration = () => (
  <div className={styles.iconContainer}>
    <Archive className={styles.routingIcon} strokeWidth={1.5} />
  </div>
);

const InsightsIllustration = () => (
  <div className={styles.iconContainer}>
    <BarChart3 className={styles.routingIcon} strokeWidth={1.5} />
  </div>
);

const MultiTeamBoardsIllustration = () => (
  <div className={styles.iconContainer}>
    <Columns className={styles.multiTeamBoardsIcon} strokeWidth={1.5} />
  </div>
);

const TicketFieldsIllustration = () => (
  <div className={styles.iconContainer}>
    <FormInput className={styles.ticketFieldsIcon} strokeWidth={1.5} />
  </div>
);

const InboxIllustration = () => (
  <div className={styles.iconContainer}>
    <Inbox className={styles.inboxIcon} strokeWidth={1.5} />
  </div>
);

const CustomInsightDashboardsIllustration = () => (
  <div className={styles.iconContainer}>
    <BarChart3
      className={styles.customInsightDashboardsIcon}
      strokeWidth={1.5}
    />
  </div>
);

const AITextEditorIllustration = () => (
  <div className={styles.iconContainer}>
    <FileText className={styles.aiTextEditorIcon} strokeWidth={1.5} />
  </div>
);

const SlackIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageSquare className={styles.slackIcon} strokeWidth={1.5} />
  </div>
);

const EmailIllustration = () => (
  <div className={styles.iconContainer}>
    <Mail className={styles.emailIcon} strokeWidth={1.5} />
  </div>
);

const WebChatIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageCircle className={styles.webChatIcon} strokeWidth={1.5} />
  </div>
);

const MSTeamsIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageSquare className={styles.msTeamsIcon} strokeWidth={1.5} />
  </div>
);

const SlackGroupsIllustration = () => (
  <div className={styles.iconContainer}>
    <UsersRound className={styles.slackGroupsIcon} strokeWidth={1.5} />
  </div>
);

const AutoResponderIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageCircle className={styles.autoResponderIcon} strokeWidth={1.5} />
  </div>
);

const SlackBroadcastsIllustration = () => (
  <div className={styles.iconContainer}>
    <MessageSquare className={styles.slackBroadcastsIcon} strokeWidth={1.5} />
  </div>
);

const EmailBroadcastsIllustration = () => (
  <div className={styles.iconContainer}>
    <Mail className={styles.emailBroadcastsIcon} strokeWidth={1.5} />
  </div>
);

const BroadcastsWYSWIGEditorIllustration = () => (
  <div className={styles.iconContainer}>
    <FileText className={styles.broadcastsWYSWIGEditorIcon} strokeWidth={1.5} />
  </div>
);

const StaticAudienceListIllustration = () => (
  <div className={styles.iconContainer}>
    <Users className={styles.staticAudienceListIcon} strokeWidth={1.5} />
  </div>
);

const DynamicAudienceListIllustration = () => (
  <div className={styles.iconContainer}>
    <UsersRound className={styles.dynamicAudienceListIcon} strokeWidth={1.5} />
  </div>
);

const _BroadcastsCategoryIllustration = () => (
  <MessageCircle className={styles.categoryIcon} />
);

// Helper function to get category label
const _getCategoryLabel = (category) => {
  switch (category) {
    case "ticketing":
      return (
        <span className={`${styles.categoryLabel} ${styles.ticketingLabel}`}>
          Ticketing
        </span>
      );
    case "accounts":
      return (
        <span className={`${styles.categoryLabel} ${styles.accountsLabel}`}>
          Accounts
        </span>
      );
    case "knowledge-base":
      return (
        <span
          className={`${styles.categoryLabel} ${styles.knowledgeBaseLabel}`}
        >
          Knowledge Base
        </span>
      );
    case "ai-agents":
      return (
        <span className={`${styles.categoryLabel} ${styles.aiAgentsLabel}`}>
          AI agents
        </span>
      );
    case "workflows":
      return (
        <span className={`${styles.categoryLabel} ${styles.workflowsLabel}`}>
          Workflows
        </span>
      );
    case "broadcasts":
      return (
        <span className={`${styles.categoryLabel} ${styles.broadcastsLabel}`}>
          Broadcasts
        </span>
      );
    default:
      return (
        <span className={`${styles.categoryLabel} ${styles.othersLabel}`}>
          Others
        </span>
      );
  }
};

const FeatureCard = ({ feature, onClick }) => {
  // Get the appropriate icon component based on the feature id
  const getIconForFeature = (featureId) => {
    const iconMap = {
      // Ticketing
      tickets: (
        <Ticket className={`${styles.ticketIcon} ${styles.featureTitleIcon}`} />
      ),
      groups: (
        <Users className={`${styles.groupsIcon} ${styles.featureTitleIcon}`} />
      ),
      routing: (
        <Layers
          className={`${styles.routingIcon} ${styles.featureTitleIcon}`}
        />
      ),
      statuses: (
        <BarChart3
          className={`${styles.statusesIcon} ${styles.featureTitleIcon}`}
        />
      ),
      tags: <Tag className={`${styles.tagsIcon} ${styles.featureTitleIcon}`} />,
      forms: (
        <FormInput
          className={`${styles.formsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "email-notifications": (
        <Mail
          className={`${styles.emailNotificationsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "slack-notifications": (
        <MessageSquare
          className={`${styles.slackNotificationsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "toast-notifications": (
        <Bell
          className={`${styles.toastNotificationsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "customer-portal": (
        <Globe
          className={`${styles.customerPortalIcon} ${styles.featureTitleIcon}`}
        />
      ),
      sla: <Clock className={`${styles.slaIcon} ${styles.featureTitleIcon}`} />,
      csat: (
        <Globe className={`${styles.csatIcon} ${styles.featureTitleIcon}`} />
      ),
      "working-hours": (
        <Timer
          className={`${styles.workingHoursIcon} ${styles.featureTitleIcon}`}
        />
      ),
      notes: (
        <FileText
          className={`${styles.notesIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "internal-threads": (
        <MessageSquare
          className={`${styles.internalThreadsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "slack-triage": (
        <MessageSquare
          className={`${styles.slackTriageIcon} ${styles.featureTitleIcon}`}
        />
      ),
      archive: (
        <Archive
          className={`${styles.archiveIcon} ${styles.featureTitleIcon}`}
        />
      ),
      insights: (
        <BarChart3
          className={`${styles.insightsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "multi-team-boards": (
        <Columns
          className={`${styles.multiTeamBoardsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "ticket-fields": (
        <FormInput
          className={`${styles.ticketFieldsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      inbox: (
        <Inbox className={`${styles.inboxIcon} ${styles.featureTitleIcon}`} />
      ),
      slack: (
        <MessageSquare
          className={`${styles.slackIcon} ${styles.featureTitleIcon}`}
        />
      ),
      email: (
        <Mail className={`${styles.emailIcon} ${styles.featureTitleIcon}`} />
      ),
      "web-chat": (
        <MessageCircle
          className={`${styles.webChatIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "ms-teams": (
        <MessageSquare
          className={`${styles.msTeamsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "slack-groups": (
        <UsersRound
          className={`${styles.slackGroupsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "Auto-responder": (
        <MessageCircle
          className={`${styles.autoResponderIcon} ${styles.featureTitleIcon}`}
        />
      ),

      // Accounts
      accounts: (
        <Building2
          className={`${styles.accountsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      contacts: (
        <UserCircle2
          className={`${styles.contactsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "account-fields": (
        <Database
          className={`${styles.accountFieldsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "contact-fields": (
        <Database
          className={`${styles.contactFieldsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "account-notes": (
        <FileText
          className={`${styles.accountNotesIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "account-tasks": (
        <CheckSquare
          className={`${styles.accountTasksIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "account-activity": (
        <Activity
          className={`${styles.accountActivityIcon} ${styles.featureTitleIcon}`}
        />
      ),
      attributes: (
        <Eye
          className={`${styles.attributesIcon} ${styles.featureTitleIcon}`}
        />
      ),

      // Knowledge Base
      "help-centers": (
        <HelpCircle
          className={`${styles.helpCentersIcon} ${styles.featureTitleIcon}`}
        />
      ),
      articles: (
        <Hash className={`${styles.articlesIcon} ${styles.featureTitleIcon}`} />
      ),
      "wysiwyg-editor": (
        <Lock
          className={`${styles.wysiwygEditorIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "live-presence": (
        <Play
          className={`${styles.livePresenceIcon} ${styles.featureTitleIcon}`}
        />
      ),
      comments: (
        <Filter
          className={`${styles.commentsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "custom-domains": (
        <LayoutGrid
          className={`${styles.customDomainsIcon} ${styles.featureTitleIcon}`}
        />
      ),

      // AI Agents
      "ai-agents": (
        <Zap className={`${styles.aiAgentsIcon} ${styles.featureTitleIcon}`} />
      ),
      flows: (
        <Workflow
          className={`${styles.workflowsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      authorization: (
        <Lock
          className={`${styles.authorizationIcon} ${styles.featureTitleIcon}`}
        />
      ),
      executions: (
        <Play
          className={`${styles.executionsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      knowledge: (
        <Database
          className={`${styles.knowledgeIcon} ${styles.featureTitleIcon}`}
        />
      ),

      // Workflows
      "ticketing-workflows": (
        <Ticket
          className={`${styles.ticketingWorkflowsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "account-workflows": (
        <Building2
          className={`${styles.accountWorkflowsIcon} ${styles.featureTitleIcon}`}
        />
      ),

      // Broadcasts
      "slack-broadcasts": (
        <MessageSquare
          className={`${styles.slackBroadcastsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "email-broadcasts": (
        <Mail
          className={`${styles.emailBroadcastsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "broadcasts-wysiwyg-editor": (
        <FileText
          className={`${styles.broadcastsWYSWIGEditorIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "static-audience-list": (
        <Users
          className={`${styles.staticAudienceListIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "dynamic-audience-list": (
        <UsersRound
          className={`${styles.dynamicAudienceListIcon} ${styles.featureTitleIcon}`}
        />
      ),

      // Others
      apps: (
        <LayoutGrid
          className={`${styles.appsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "custom-insight-dashboards": (
        <BarChart3
          className={`${styles.customInsightDashboardsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      "ai-text-editor": (
        <FileText
          className={`${styles.aiTextEditorIcon} ${styles.featureTitleIcon}`}
        />
      ),
      search: (
        <Search
          className={`${styles.searchFeatureIcon} ${styles.featureTitleIcon}`}
        />
      ),
      views: (
        <LayoutGrid
          className={`${styles.viewsIcon} ${styles.featureTitleIcon}`}
        />
      ),
      filters: (
        <Filter
          className={`${styles.filtersIcon} ${styles.featureTitleIcon}`}
        />
      ),
      // Default benefits for any other features
      default: [
        "Streamline your workflow and save time.",
        "Improve team collaboration and communication.",
        "Enhance customer experience with faster responses.",
      ],
    };

    return (
      iconMap[featureId] || (
        <Info className={`${styles.othersIcon} ${styles.featureTitleIcon}`} />
      )
    );
  };

  return (
    <div className={styles.featureCard} onClick={() => onClick(feature)}>
      <div className={styles.featureContent}>
        <div className={styles.featureIconContainer}>
          {getIconForFeature(feature.id)}
        </div>
        <h3 className={styles.featureTitle}>{feature.title}</h3>
        <div style={{ height: "8px" }}></div>
        <p className={styles.featureDescription}>
          {feature.description.endsWith(".")
            ? feature.description
            : `${feature.description}.`}
        </p>
        {feature.comingSoon && (
          <div className={styles.comingSoonLabel}>Coming soon</div>
        )}
      </div>
    </div>
  );
};

export default function HelpGuide() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [activeFilter, setActiveFilter] = useState("all");
  const [_preloadedImages, setPreloadedImages] = useState({});
  const [imagesLoaded, setImagesLoaded] = useState({});
  const [imageLoadingStatus, setImageLoadingStatus] = useState({});

  // Helper function to get the correct image path for each feature
  const getImagePathForFeature = (featureId) => {
    const imagePathMap = {
      sla: "/platform-docs/images/docs/SLAs.webp",
      "working-hours": "/platform-docs/images/docs/Working hours.webp",
      "ai-agents": "/platform-docs/images/docs/AI-agent.webp",
      knowledge: "/platform-docs/images/docs/Jamie-Knowledge.webp",
      accounts: "/platform-docs/images/docs/Accounts-view.webp",
      "multi-team-boards": "/platform-docs/images/docs/Create-team.webp",
      "account-fields": "/platform-docs/images/docs/Account fields.webp",
      views: "/platform-docs/images/docs/Views.webp",
      search: "/platform-docs/images/docs/Search.webp",
      filters: "/platform-docs/images/docs/Filters.webp",
      "emoji-actions": "/platform-docs/images/docs/Emoji-actions.webp",
      "ai-text-editor": "/platform-docs/images/docs/Editor.webp",
      "contact-fields": "/platform-docs/images/docs/Contact-fields.webp",
      "email-notifications": "/platform-docs/images/docs/Notifications.webp",
      "slack-notifications": "/platform-docs/images/docs/Notifications.webp",
      inbox: "/platform-docs/images/docs/Notifications.webp",
      "ticket-fields": "/platform-docs/images/docs/Ticket-fields.webp",
      insights: "/platform-docs/images/docs/Insights.webp",
      groups: "/platform-docs/images/docs/Groups.webp",
    };

    // Return the mapped path if it exists, otherwise construct a path based on the feature ID
    return (
      imagePathMap[featureId] ||
      `/platform-docs/images/docs/${
        featureId.charAt(0).toUpperCase() +
        featureId.slice(1).replace(/-([a-z])/g, (g) => g[1].toUpperCase())
      }.webp`
    );
  };

  // Helper function to check if an image exists for a feature
  const hasImageForFeature = (featureId) => {
    const availableImages = [
      "sla",
      "working-hours",
      "ai-agents",
      "knowledge",
      "accounts",
      "multi-team-boards",
      "account-fields",
      "views",
      "search",
      "filters",
      "emoji-actions",
      "ai-text-editor",
      "contact-fields",
      "email-notifications",
      "slack-notifications",
      "ticket-fields",
      "insights",
      "groups",
      "routing",
      "statuses",
      "tags",
      "flows",
    ];

    return availableImages.includes(featureId);
  };

  // Function to preload images
  const preloadImages = () => {
    // Only run in browser environment
    if (typeof window === "undefined") return;

    // Get all features with images - use the same array we already have in hasImageForFeature
    const featuresToPreload = [
      "sla",
      "working-hours",
      "ai-agents",
      "knowledge",
      "accounts",
      "multi-team-boards",
      "account-fields",
      "views",
      "search",
      "filters",
      "emoji-actions",
      "ai-text-editor",
      "contact-fields",
      "email-notifications",
      "slack-notifications",
      "ticket-fields",
      "insights",
      "groups",
      "routing",
      "statuses",
      "tags",
      "flows",
    ];

    // Create a new object to store preloaded images
    const newPreloadedImages = {};
    const newImageLoadingStatus = {};
    const newImagesLoaded = {};

    // Initialize all images as not loaded
    featuresToPreload.forEach((featureId) => {
      newImageLoadingStatus[featureId] = "loading";
      newImagesLoaded[featureId] = false;
    });

    // Update loading status
    setImageLoadingStatus(newImageLoadingStatus);
    setImagesLoaded(newImagesLoaded);

    // Preload each image with both techniques
    featuresToPreload.forEach((featureId) => {
      const imagePath = getImagePathForFeature(featureId);

      // TECHNIQUE 1: Use the Image API directly
      const preloadLink = document.createElement("link");
      preloadLink.rel = "preload";
      preloadLink.as = "image";
      preloadLink.href = imagePath;
      document.head.appendChild(preloadLink);

      // TECHNIQUE 2: Create an actual image element
      const img = document.createElement("img");

      // Set up onload handler before setting src
      img.onload = () => {
        // Update loading status for this specific image
        setImageLoadingStatus((prevStatus) => ({
          ...prevStatus,
          [featureId]: "loaded",
        }));

        setImagesLoaded((prevLoaded) => ({
          ...prevLoaded,
          [featureId]: true,
        }));
      };

      img.onerror = () => {
        console.error(`Failed to preload image for ${featureId}`);
        setImageLoadingStatus((prevStatus) => ({
          ...prevStatus,
          [featureId]: "error",
        }));

        // Even on error, mark as loaded to avoid blocking
        setImagesLoaded((prevLoaded) => ({
          ...prevLoaded,
          [featureId]: true,
        }));
      };

      // Set crossOrigin to anonymous to avoid CORS issues with image caching
      img.crossOrigin = "anonymous";
      img.style.position = "absolute";
      img.style.opacity = "0";
      img.style.width = "1px";
      img.style.height = "1px";
      img.style.top = "-9999px";
      img.style.left = "-9999px";
      document.body.appendChild(img);

      // Set src after setting up handlers
      img.src = imagePath;

      // Store the path in our preloaded images object
      newPreloadedImages[featureId] = imagePath;
    });

    // Update state with preloaded images
    setPreloadedImages(newPreloadedImages);
  };

  // Use effect to preload images when component mounts
  useEffect(() => {
    preloadImages();
    return () => {
      // Clean up appended <img> elements
      const appendedImages = document.querySelectorAll(
        'img[style*="position: absolute"]',
      );
      appendedImages.forEach((img) => img.remove());

      // Clean up <link rel="preload"> elements
      const preloadLinks = document.querySelectorAll(
        'link[rel="preload"][as="image"]',
      );
      preloadLinks.forEach((link) => link.remove());
    };
  }, []);

  // Function to handle feature selection with preloading check
  const handleFeatureSelect = (feature) => {
    setSelectedFeature(feature);

    // If the feature has an image but it's not loaded yet, trigger loading
    if (hasImageForFeature(feature.id) && !imagesLoaded[feature.id]) {
      // Force load the image
      const imagePath = getImagePathForFeature(feature.id);

      // Create an image element instead of using the Image constructor
      const img = document.createElement("img");
      img.src = imagePath;

      // Set up handlers to update state when image loads
      img.onload = () => {
        setImagesLoaded((prevLoaded) => ({
          ...prevLoaded,
          [feature.id]: true,
        }));
        setImageLoadingStatus((prevStatus) => ({
          ...prevStatus,
          [feature.id]: "loaded",
        }));
      };
    }
  };

  // Filter features based on search query
  const filterFeatures = (features) => {
    if (!searchQuery) return features;
    return features.filter(
      (feature) =>
        feature.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        feature.description.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  };

  // Filter features based on active filter
  const filterFeaturesByCategory = (features) => {
    if (activeFilter === "all") return features;
    if (activeFilter === "coming-soon")
      return features.filter((feature) => feature.comingSoon === true);
    return features.filter((feature) => feature.category === activeFilter);
  };

  // Generate feature-specific benefits
  const getFeatureBenefits = (featureId) => {
    const benefitsMap = {
      // Ticketing benefits
      tickets: [
        "Centralize customer inquiries from multiple channels.",
        "Track and measure response and resolution times.",
        "Maintain a complete history of customer communications.",
      ],
      groups: [
        "Organize team members by department, skill, or function.",
        "Distribute workload evenly across your support team.",
        "Ensure the right people handle the right tickets.",
      ],
      routing: [
        "Automatically assign tickets to the right team or agent.",
        "Reduce response times with intelligent ticket distribution.",
        "Ensure tickets are handled by the most qualified agents.",
      ],
      statuses: [
        "Track ticket progress through your support workflow.",
        "Customize statuses to match your team's processes.",
        "Gain visibility into ticket bottlenecks and backlogs.",
      ],
      tags: [
        "Categorize tickets for better organization and reporting.",
        "Filter and search tickets based on specific criteria.",
        "Identify trends and common issues across tickets.",
      ],
      forms: [
        "Collect all necessary information upfront.",
        "Reduce back-and-forth communication for missing details.",
        "Create custom forms for different types of requests.",
      ],
      "email-notifications": [
        "Stay informed about important ticket events via email.",
        "Customize email alerts based on ticket properties and events.",
        "Receive notifications via email for ticket updates.",
      ],
      "slack-notifications": [
        "Stay informed about important ticket events in Slack.",
        "Customize Slack alerts based on ticket properties and events.",
        "Receive notifications in Slack for ticket updates.",
      ],
      "toast-notifications": [
        "Stay informed about important ticket events with toast notifications.",
        "Customize toast alerts based on ticket properties and events.",
        "Receive notifications via toast for ticket updates.",
      ],
      "customer-portal": [
        "Provide customers with a self-service portal.",
        "Allow customers to view their tickets and account information.",
        "Reduce the number of incoming support requests.",
      ],
      sla: [
        "Set clear expectations for response and resolution times.",
        "Monitor compliance with service level agreements.",
        "Prioritize tickets based on urgency and SLA status.",
      ],
      csat: [
        "Measure customer satisfaction with your support.",
        "Identify areas for improvement in your customer service.",
        "Track CSAT scores over time to monitor progress.",
      ],
      "working-hours": [
        "Define when your team is available to handle tickets.",
        "Set expectations for response times during off-hours.",
        "Automatically adjust SLA calculations based on working hours.",
      ],
      notes: [
        "Add private notes to tickets for internal communication.",
        "Document important context and decisions.",
        "Keep a record of internal discussions about tickets.",
      ],
      "internal-threads": [
        "Collaborate with team members on complex tickets.",
        "Keep internal discussions separate from customer communications.",
        "Share knowledge and insights without exposing to customers.",
      ],
      "slack-triage": [
        "Triage and respond to tickets directly from Slack.",
        "Collaborate with team members in familiar channels.",
        "Reduce context switching between tools.",
      ],
      archive: [
        "Keep your ticket list clean and focused.",
        "Store resolved tickets for future reference.",
        "Maintain a complete history while reducing clutter.",
      ],
      insights: [
        "Analyze support metrics to identify trends.",
        "Make data-driven decisions to improve processes.",
        "Track team performance and customer satisfaction.",
      ],
      "multi-team-boards": [
        "View tickets across multiple teams in a single board.",
        "Coordinate cross-team collaboration on related issues.",
        "Maintain visibility across organizational boundaries.",
      ],
      "ticket-fields": [
        "Customize data collection for your specific needs.",
        "Create custom fields to track industry-specific information.",
        "Ensure you capture all relevant details for each ticket.",
      ],
      inbox: [
        "Centralize incoming requests from all channels.",
        "Triage and assign tickets from a unified interface.",
        "Prioritize and organize incoming support requests.",
      ],
      slack: [
        "Integrate Thena directly with your Slack workspace.",
        "Create tickets from Slack messages with one click.",
        "Respond to tickets without leaving Slack.",
      ],
      email: [
        "Convert email messages into trackable tickets.",
        "Maintain email conversations within the ticketing system.",
        "Ensure no customer inquiry falls through the cracks.",
      ],
      "web-chat": [
        "Offer real-time support through your website.",
        "Convert chat conversations into trackable tickets.",
        "Provide immediate assistance to website visitors.",
      ],
      "ms-teams": [
        "Integrate Thena with Microsoft Teams.",
        "Create and manage tickets without leaving Teams.",
        "Collaborate on support issues in your primary workspace.",
      ],
      "slack-groups": [
        "Organize Slack channels for different support teams.",
        "Route messages to the appropriate group automatically.",
        "Ensure the right experts see relevant conversations.",
      ],
      "Auto-responder": [
        "Automatically acknowledge incoming tickets.",
        "Set expectations for response times.",
        "Provide immediate feedback to customers.",
      ],

      // Accounts benefits
      accounts: [
        "Organize customer information in a centralized database.",
        "Track relationships with organizations and companies.",
        "Maintain a complete view of customer history and interactions.",
      ],
      contacts: [
        "Manage individual relationships within customer accounts.",
        "Track communication preferences and history by contact.",
        "Ensure personalized interactions with each stakeholder.",
      ],
      "account-fields": [
        "Customize the data you collect about accounts.",
        "Add fields specific to your industry or business needs.",
        "Track custom metrics and attributes for each account.",
      ],
      "contact-fields": [
        "Customize the data you collect about contacts.",
        "Store communication preferences for each contact.",
        "Add custom fields relevant to your customer relationships.",
      ],
      "account-notes": [
        "Document key discussions and decisions about accounts.",
        "Share important context with team members.",
        "Maintain a record of account-related observations.",
      ],
      "account-tasks": [
        "Track work related to account management.",
        "Assign and monitor account-related activities.",
        "Ensure follow-up actions are completed on time.",
      ],
      "account-activity": [
        "View a timeline of all interactions with an account.",
        "Track emails, calls, meetings, and other touchpoints.",
        "Maintain a complete history of customer relationships.",
      ],
      attributes: [
        "Categorize accounts based on custom criteria.",
        "Create attributes for industry, size, tier, or other dimensions.",
        "Segment accounts for targeted communications and reporting.",
      ],

      // Knowledge Base benefits
      "help-centers": [
        "Create branded self-service knowledge bases.",
        "Reduce support volume by empowering customers to find answers.",
        "Organize information in a user-friendly, searchable format.",
      ],
      articles: [
        "Create comprehensive documentation for common issues.",
        "Reduce repetitive support requests with searchable content.",
        "Maintain a single source of truth for product information.",
      ],
      "wysiwyg-editor": [
        "Create rich, formatted content without coding knowledge.",
        "Include images, videos, and other media in your articles.",
        "Maintain consistent styling across your knowledge base.",
      ],
      "live-presence": [
        "See who is currently viewing or editing an article.",
        "Avoid conflicts when multiple authors are working.",
        "Collaborate in real-time on knowledge base content.",
      ],
      comments: [
        "Allow customers to provide feedback on articles.",
        "Identify gaps in your documentation based on questions.",
        "Continuously improve content based on user input.",
      ],
      "custom-domains": [
        "Host your knowledge base on your own domain.",
        "Maintain brand consistency across all customer touchpoints.",
        "Create a seamless experience between your website and help center.",
      ],

      // AI Agents benefits
      "ai-agents": [
        "Automate routine support tasks with AI.",
        "Provide instant responses to common questions.",
        "Scale your support operations without adding headcount.",
      ],
      flows: [
        "Create custom workflows for AI agents to follow.",
        "Design conversational paths for different scenarios.",
        "Ensure consistent handling of common support issues.",
      ],
      authorization: [
        "Control what actions AI agents can perform.",
        "Set permissions based on sensitivity and complexity.",
        "Ensure human oversight for critical operations.",
      ],
      executions: [
        "Track task-level actions performed by AI agents.",
        "Monitor performance and success rates.",
        "Identify opportunities for workflow improvements.",
      ],
      knowledge: [
        "Provide AI agents with access to your knowledge base.",
        "Train agents on your specific products and processes.",
        "Improve response accuracy with domain-specific information.",
      ],

      // Workflows benefits
      "ticketing-workflows": [
        "Automate routine ticketing processes.",
        "Ensure consistent handling of common ticket types.",
        "Reduce manual work in ticket management.",
      ],
      "account-workflows": [
        "Automate routine account management tasks.",
        "Ensure consistent processes for account updates.",
        "Reduce manual work in account management.",
      ],

      // Broadcasts benefits
      "slack-broadcasts": [
        "Send targeted messages to specific Slack channels.",
        "Reach the right audience with your broadcasts.",
        "Increase engagement with your support content.",
      ],
      "email-broadcasts": [
        "Send targeted emails to specific customer segments.",
        "Reach the right audience with your broadcasts.",
        "Increase engagement with your support content.",
      ],
      "broadcasts-wysiwyg-editor": [
        "Create rich content for your broadcasts.",
        "Format messages with headings, lists, images, and more.",
        "Maintain consistent styling across your broadcasts.",
      ],
      "static-audience-list": [
        "Create lists of customers based on static criteria.",
        "Target specific groups with your broadcasts.",
        "Increase engagement with your support content.",
      ],
      "dynamic-audience-list": [
        "Create lists of customers based on dynamic criteria.",
        "Target specific groups with your broadcasts.",
        "Increase engagement with your support content.",
      ],

      // Others benefits
      apps: [
        "Integrate with other tools and services.",
        "Extend functionality with third-party applications.",
        "Create a unified workflow across your tech stack.",
      ],
      "custom-insight-dashboards": [
        "Create personalized dashboards with key metrics.",
        "Visualize data with customizable charts and graphs.",
        "Monitor performance metrics in real-time.",
      ],
      "ai-text-editor": [
        "Create and edit text with AI assistance.",
        "Improve writing quality and consistency.",
        "Enhance team collaboration and productivity.",
      ],
      search: [
        "Find information quickly across all your data.",
        "Locate relevant tickets, accounts, and knowledge articles.",
        "Reduce time spent searching for information.",
      ],
      "emoji-actions": [
        "Take quick actions on tickets with simple emoji reactions.",
        "Streamline common workflows with intuitive shortcuts.",
        "Reduce context switching with in-line actions.",
        "Improve team collaboration with visual cues.",
      ],

      // Default benefits for any other features
      default: [
        "Streamline your workflow and save time.",
        "Improve team collaboration and communication.",
        "Enhance customer experience with faster responses.",
      ],
    };

    return benefitsMap[featureId] || benefitsMap.default;
  };

  // Organize features by category
  const ticketingFeatures = [
    {
      id: "tickets",
      title: "Tickets",
      description: "Manage customer support requests.",
      illustration: <TicketsIllustration />,
      content:
        "Tickets are the core of your support workflow. They allow you to track, prioritize, and resolve customer inquiries across channels. Each ticket contains a complete history of the conversation, along with metadata like status, priority, and assignee.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/tickets",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "multi-team-boards",
      title: "Multi-team boards",
      description: "View tickets across multiple teams.",
      illustration: <MultiTeamBoardsIllustration />,
      content:
        "Multi-team boards allow you to view and manage tickets across different teams in a single interface. This is especially useful for organizations with cross-functional support processes or when coordination between teams is required. You can customize the board view to show tickets from specific teams, with various filtering and grouping options.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/teams",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "ticket-fields",
      title: "Ticket fields",
      description: "Customize ticket data collection.",
      illustration: <TicketFieldsIllustration />,
      content:
        "Ticket fields allow you to customize the data you collect for each ticket. You can create custom fields to gather specific information relevant to your business or industry, ensuring that support agents have all the details they need to resolve issues efficiently. Fields can be required or optional, and can include various data types.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/ticket-fields",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "inbox",
      title: "Inbox",
      description: "Centralize incoming support requests.",
      illustration: <InboxIllustration />,
      content:
        "The inbox provides a centralized view of all incoming support requests across channels. It allows you to triage, prioritize, and assign tickets efficiently. The inbox can be customized with various views and filters to focus on specific types of requests or priority levels.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/inbox",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "slack",
      title: "Slack",
      description: "Integrate with Slack for ticket management.",
      illustration: <SlackIllustration />,
      content:
        "The Slack integration allows you to create and manage tickets directly from your Slack workspace. You can convert Slack messages into tickets, update ticket status, and collaborate on ticket resolution without leaving Slack. This integration streamlines support workflows for teams that primarily use Slack for communication.",
      learnMoreLink: "https://docs.thena.ai/guides/sources/slack",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "email",
      title: "Email",
      description: "Convert emails into trackable tickets.",
      illustration: <EmailIllustration />,
      content:
        "The email integration converts incoming emails into trackable tickets automatically. Replies to these emails are captured in the ticket thread, maintaining a complete conversation history. This allows you to manage email-based support through the same interface as other channels, with all the benefits of ticket tracking and assignment.",
      learnMoreLink: "https://docs.thena.ai/guides/sources/email",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "web-chat",
      title: "Web chat",
      description: "Provide real-time support on your website.",
      illustration: <WebChatIllustration />,
      content:
        "Web chat enables real-time support directly on your website. Visitors can start conversations that can be handled immediately or converted into tickets for follow-up. The chat widget can be customized to match your brand and can be configured to appear on specific pages or based on visitor behavior.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/web-chat",
      category: "ticketing",
      comingSoon: true,
    },
    {
      id: "ms-teams",
      title: "MS Teams",
      description: "Integrate with Microsoft Teams.",
      illustration: <MSTeamsIllustration />,
      content:
        "The Microsoft Teams integration allows you to create and manage tickets directly from Teams. Similar to the Slack integration, it enables support workflows within your Teams environment, reducing context switching and streamlining communication for organizations that use Microsoft's collaboration platform.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/ms-teams",
      category: "ticketing",
      comingSoon: true,
    },
    {
      id: "slack-groups",
      title: "Slack groups",
      description: "Organize support channels in Slack.",
      illustration: <SlackGroupsIllustration />,
      content:
        "Slack groups allow you to organize your support channels in Slack by function, team, or other criteria. You can route tickets to specific Slack groups based on rules, ensuring that the right team members see relevant tickets. This helps maintain specialized support channels for different types of requests or products.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/slack-groups",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "groups",
      title: "Groups",
      description: "Split teams into specialization and timezones.",
      illustration: <GroupsIllustration />,
      content:
        "Groups are sub-teams within a Team—built for structure, specialization, and smart routing. Each group has its own members, working hours, timezone settings, holidays, and assignment logic. Use groups to segment team members by skill, function, or region for better ticket routing and specialized support.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/groups",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "routing",
      title: "Routing",
      description: "Automate ticket assignment with routing rules.",
      illustration: <RoutingIllustration />,
      content:
        "Routing rules allow you to automatically assign tickets to the right team members or groups based on predefined criteria. This helps ensure that tickets are handled by the most appropriate people, reducing response times and improving customer satisfaction.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/routing",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "statuses",
      title: "Statuses",
      description: "Track ticket progress with custom statuses.",
      illustration: <StatusesIllustration />,
      content:
        "Statuses help you track the progress of tickets through your workflow. You can create custom statuses to match your team's processes, and move tickets through these statuses as they progress toward resolution.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/statuses",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "tags",
      title: "Tags",
      description: "Categorize tickets with customizable tags.",
      illustration: <TagsIllustration />,
      content:
        "Tags help you categorize tickets for better organization and reporting. You can create custom tags and apply them to tickets, then use these tags for filtering, searching, and generating reports.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/tags",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "forms",
      title: "Forms",
      description: "Create custom forms for ticket submission.",
      illustration: <FormsIllustration />,
      content:
        "Forms provide a structured way for customers to submit tickets. You can create custom forms with specific fields, validation rules, and conditional logic to collect all the information needed to address customer issues efficiently. Fields can be required or optional, and can include various data types.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/forms",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "email-notifications",
      title: "Email notifications",
      description: "Stay informed with email notifications.",
      illustration: <EmailNotificationsIllustration />,
      content:
        "Email notifications keep you and your team informed about ticket updates. You can customize which events trigger notifications and who receives them, ensuring that everyone stays in the loop without being overwhelmed by unnecessary emails.",
      learnMoreLink:
        "https://docs.thena.ai/guides/ticketing/email-notifications",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "slack-notifications",
      title: "Slack notifications",
      description: "Stay informed with Slack notifications.",
      illustration: <SlackNotificationsIllustration />,
      content:
        "Slack notifications keep you and your team informed about ticket updates. You can customize which events trigger notifications and who receives them, ensuring that everyone stays in the loop without being overwhelmed by unnecessary notifications.",
      learnMoreLink:
        "https://docs.thena.ai/guides/ticketing/slack-notifications",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "toast-notifications",
      title: "Toast notifications",
      description: "Stay informed with toast notifications.",
      illustration: <ToastNotificationsIllustration />,
      content:
        "Toast notifications keep you and your team informed about ticket updates. You can customize which events trigger notifications and who receives them, ensuring that everyone stays in the loop without being overwhelmed by unnecessary notifications.",
      learnMoreLink:
        "https://docs.thena.ai/guides/ticketing/toast-notifications",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "customer-portal",
      title: "Customer portal",
      description: "Provide customers with a self-service portal.",
      illustration: <CustomerPortalIllustration />,
      content:
        "The customer portal allows customers to view their tickets and account information. This feature reduces the number of incoming support requests and provides customers with a self-service option.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/customer-portal",
      category: "ticketing",
      comingSoon: true,
    },
    {
      id: "sla",
      title: "SLA",
      description: "Set and monitor service level agreements.",
      illustration: <SLAIllustration />,
      content:
        "Service Level Agreements (SLAs) help you set expectations for response and resolution times. You can create SLA policies based on ticket properties, and the system will track compliance with these policies, alerting you when SLAs are at risk of being breached.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/sla",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "csat",
      title: "CSAT",
      description: "Measure customer satisfaction.",
      illustration: <CSATIllustration />,
      content:
        "CSAT (Customer Satisfaction) allows you to measure how satisfied your customers are with your support. You can create surveys, track responses, and analyze results to identify areas for improvement and optimize your support strategy.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/csat",
      category: "ticketing",
      comingSoon: true,
    },
    {
      id: "working-hours",
      title: "Working hours",
      description: "Set your team's working hours.",
      illustration: <WorkingHoursIllustration />,
      content:
        "Working hours allow you to set the hours during which your team is available to provide support. This helps you manage customer expectations, ensure timely responses, and maintain a healthy work-life balance for your team members.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/working-hours",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "notes",
      title: "Ticket notes",
      description: "Add private notes to tickets.",
      illustration: <NotesIllustration />,
      content:
        "Ticket notes allow you to add important information and context to tickets. These notes are only visible to your team members, making them ideal for internal discussions and documentation about specific tickets.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/notes",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "internal-threads",
      title: "Internal threads",
      description: "Collaborate with your team privately.",
      illustration: <InternalThreadsIllustration />,
      content:
        "Internal threads provide a private channel for team collaboration within tickets. You can discuss issues, share information, and coordinate actions without exposing these conversations to customers.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/internal-threads",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "slack-triage",
      title: "Slack triage",
      description: "Triage tickets directly from Slack.",
      illustration: <SlackTriageIllustration />,
      content:
        "Slack triage allows you to manage tickets directly from Slack. You can view ticket details, assign tickets, and take action without leaving Slack, making it easier to collaborate with your team and respond to customer issues quickly.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/slack-triage",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "archive",
      title: "Archive",
      description: "Archive resolved tickets for future reference.",
      illustration: <ArchiveIllustration />,
      content:
        "The archive feature allows you to store resolved tickets for future reference. This helps keep your active ticket list clean while ensuring that you can still access historical ticket data when needed.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/archive",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "insights",
      title: "Insights",
      description: "Gain insights from ticket analytics.",
      illustration: <InsightsIllustration />,
      content:
        "Insights provide analytics and reporting on your ticket data. You can track metrics like response time, resolution time, and customer satisfaction to identify trends and areas for improvement in your support process.",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "Auto-responder",
      title: "Auto-responder",
      description: "Set up automatic replies for OOO and holidays.",
      illustration: <AutoResponderIllustration />,
      content:
        "Auto-responders provide automatic replies when agents are out of office or when your team is celebrating holidays. This ensures customers receive timely acknowledgments with information about when they can expect a response. Auto-responders can be configured for individual agents or team-wide for special events and holidays.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/auto-responder",
      category: "ticketing",
      comingSoon: true,
    },
    {
      id: "whatsapp",
      title: "WhatsApp",
      description: "Support customers via WhatsApp.",
      illustration: <Phone className={styles.whatsappIcon} />,
      content:
        "The WhatsApp integration allows you to provide support through WhatsApp, one of the world's most popular messaging platforms. Conversations from WhatsApp are converted into tickets, allowing you to manage them alongside other support channels. This is particularly valuable for businesses with customers who prefer mobile messaging.",
      category: "ticketing",
      comingSoon: false,
    },
    {
      id: "emoji-actions",
      title: "Emoji actions",
      description: "Use emoji reactions to take actions.",
      illustration: <EmojiActionsIllustration />,
      content:
        "Emoji actions allow you to take common actions on tickets using emoji reactions. This provides a quick and intuitive way to update ticket status, assign tickets, or add tags without having to navigate through multiple menus.",
      learnMoreLink: "https://docs.thena.ai/guides/ticketing/emoji-actions",
      category: "ticketing",
      comingSoon: false,
    },
  ];

  const accountsFeatures = [
    {
      id: "accounts",
      title: "Accounts",
      description: "Manage customer accounts.",
      illustration: <AccountsIllustration />,
      content:
        "Accounts provide a centralized view of your customer information. You can store contact details, track interactions, and manage relationships with your customers. This helps you provide personalized support and maintain a complete history of your customer engagements.",
      learnMoreLink: "https://docs.thena.ai/guides/accounts/accounts-view",
      category: "accounts",
      comingSoon: false,
    },
    {
      id: "contacts",
      title: "Contacts",
      description: "Manage contacts within accounts.",
      illustration: <ContactsIllustration />,
      content:
        "Contacts represent individual people associated with an account. You can store contact information, track communication preferences, and maintain relationships with multiple stakeholders within each customer organization.",
      category: "accounts",
      comingSoon: false,
    },
    {
      id: "account-fields",
      title: "Account fields",
      description: "Customize account data fields.",
      illustration: <AccountFieldsIllustration />,
      content:
        "Account fields allow you to customize the data you collect about your accounts. You can add custom fields to track industry-specific information, account health metrics, or any other data relevant to your business.",
      category: "accounts",
      comingSoon: false,
    },
    {
      id: "contact-fields",
      title: "Contact fields",
      description: "Customize contact data fields.",
      illustration: <ContactFieldsIllustration />,
      content:
        "Contact fields allow you to customize the data you collect about your contacts. You can add custom fields to track role-specific information, communication preferences, or any other data relevant to your customer relationships.",
      category: "accounts",
      comingSoon: false,
    },
    {
      id: "attributes",
      title: "Attributes",
      description: "Add custom attributes to accounts.",
      illustration: <AttributesIllustration />,
      content:
        "Attributes allow you to categorize and segment your accounts based on custom criteria. You can create attributes for industry, company size, customer tier, or other dimensions relevant to your business.",
      category: "accounts",
      comingSoon: false,
    },
    {
      id: "account-notes",
      title: "Account notes",
      description: "Add notes to account records.",
      illustration: <NotesIllustration />,
      content:
        "Account notes allow you to add important information and context to account records. You can document key discussions, decisions, and observations to ensure that all team members have access to relevant account information.",
      category: "accounts",
      comingSoon: false,
    },
    {
      id: "account-tasks",
      title: "Account tasks",
      description: "Create and assign tasks for accounts.",
      illustration: <TasksIllustration />,
      content:
        "Account tasks help you manage and track work related to accounts. You can create tasks, assign them to team members, set due dates, and track progress to ensure that nothing falls through the cracks.",
      category: "accounts",
      comingSoon: false,
    },
    {
      id: "account-activity",
      title: "Account activity",
      description: "Track account activity history.",
      illustration: <ActivityIllustration />,
      content:
        "Account activity tracking provides a comprehensive history of all interactions with an account. You can see a timeline of emails, calls, meetings, and other touchpoints, giving you a complete view of your relationship with each customer.",
      category: "accounts",
      comingSoon: false,
    },
  ];

  const knowledgeBaseFeatures = [
    {
      id: "help-centers",
      title: "Help centers",
      description: "Create self-service help centers.",
      illustration: <HelpCentersIllustration />,
      content:
        "Help centers provide a self-service knowledge base for your customers. You can create articles, organize them into categories, and make them searchable, allowing customers to find answers to common questions without contacting support.",
      learnMoreLink: "https://docs.thena.ai/guides/helpcenter/help-centers",
      category: "knowledge-base",
      comingSoon: false,
    },
    {
      id: "articles",
      title: "Articles",
      description: "Create and manage knowledge base articles.",
      illustration: <ArticlesIllustration />,
      content:
        "Articles are the building blocks of your knowledge base. You can create rich content with text, images, videos, and code snippets to provide comprehensive answers to common questions and document processes.",
      learnMoreLink: "https://docs.thena.ai/guides/helpcenter/articles",
      category: "knowledge-base",
      comingSoon: false,
    },
    {
      id: "wysiwyg-editor",
      title: "WYSIWYG editor",
      description: "Edit articles with a rich text editor.",
      illustration: <WYSIWYGEditorIllustration />,
      content:
        "The WYSIWYG (What You See Is What You Get) editor makes it easy to create and format knowledge base articles. You can add headings, lists, tables, images, and other elements without knowing HTML or markdown.",
      learnMoreLink: "https://docs.thena.ai/guides/helpcenter/wysiwyg-editor",
      category: "knowledge-base",
      comingSoon: false,
    },
    {
      id: "live-presence",
      title: "Live presence",
      description: "See who's viewing and editing articles.",
      illustration: <LivePresenceIllustration />,
      content:
        "Live presence shows you who is currently viewing or editing an article. This helps prevent conflicts when multiple team members are working on the same content and facilitates collaboration.",
      learnMoreLink: "https://docs.thena.ai/guides/helpcenter/live-presence",
      category: "knowledge-base",
      comingSoon: false,
    },
    {
      id: "comments",
      title: "Comments",
      description: "Add comments to articles.",
      illustration: <CommentsIllustration />,
      content:
        "Comments allow team members to discuss and provide feedback on knowledge base articles. This facilitates collaboration and helps ensure that your knowledge base content is accurate, comprehensive, and up-to-date.",
      learnMoreLink: "https://docs.thena.ai/guides/helpcenter/comments",
      category: "knowledge-base",
      comingSoon: false,
    },
    {
      id: "custom-domains",
      title: "Custom domains",
      description: "Use custom domains for help centers.",
      illustration: <CustomDomainsIllustration />,
      content:
        "Custom domains allow you to host your help center on your own domain (e.g., help.yourcompany.com). This provides a seamless experience for your customers and reinforces your brand identity.",
      learnMoreLink: "https://docs.thena.ai/guides/helpcenter/custom-domains",
      category: "knowledge-base",
      comingSoon: false,
    },
    {
      id: "knowledge-base",
      title: "Knowledge base",
      description: "Create a self-service knowledge base.",
      illustration: <HelpCentersIllustration />,
      content:
        "The knowledge base allows you to create a self-service resource for customers to find answers to common questions. You can organize articles into categories, add rich media content, and link related articles. A comprehensive knowledge base reduces ticket volume by enabling customers to solve issues on their own.",
      learnMoreLink: "https://docs.thena.ai/guides/helpcenter/knowledge-base",
      category: "knowledge-base",
      comingSoon: false,
    },
  ];

  const aiAgentsFeatures = [
    {
      id: "ai-agents",
      title: "AI agents",
      description: "Create AI-powered support agents.",
      illustration: <AIAgentsIllustration />,
      content:
        "AI agents use artificial intelligence to automate support tasks. You can create agents that answer common questions, route tickets, or perform specific actions based on natural language inputs from customers or team members.",
      category: "ai-agents",
      comingSoon: false,
    },
    {
      id: "flows",
      title: "Flows",
      description: "Access specific AI agent capabilities.",
      illustration: <FlowsIllustration />,
      content:
        "Flows represent specific capabilities that an AI agent can perform. Each capability of an AI agent is called a flow, such as AI status management. Flows allow you to implement specific AI features without coding and integrate them seamlessly into your support process.",
      category: "ai-agents",
      comingSoon: false,
    },
    {
      id: "authorization",
      title: "Authorization",
      description: "Manage access to workflows.",
      illustration: <AuthorizationIllustration />,
      content:
        "Authorization controls who can create, edit, and run workflows. You can set permissions based on roles, teams, or individual users to ensure that workflows are managed securely and appropriately.",
      category: "ai-agents",
      comingSoon: false,
    },
    {
      id: "executions",
      title: "Executions",
      description: "Track AI agent activities at the task level.",
      illustration: <ExecutionsIllustration />,
      content:
        "Executions provide a detailed log of all tasks performed by AI agents. This feature shows exactly what actions AI agents have taken at a task level, allowing you to see precisely what each AI agent did, when it was done, and whether it was successful. This transparency helps with troubleshooting and building trust in AI agent capabilities.",
      category: "ai-agents",
      comingSoon: false,
    },
    {
      id: "knowledge",
      title: "Knowledge",
      description: "Manage and organize knowledge.",
      illustration: <KnowledgeIllustration />,
      content:
        "Knowledge management tools help you organize and maintain your knowledge base. You can categorize articles, tag content, and create a structured hierarchy to make information findable through multiple paths.",
      category: "ai-agents",
      comingSoon: false,
    },
  ];

  const workflowsFeatures = [
    {
      id: "ticketing-workflows",
      title: "Ticketing workflows",
      description: "Automate ticket processes.",
      illustration: <TicketingWorkflowsIllustration />,
      content:
        "Ticketing workflows automate common support processes. You can create workflows that assign tickets, update statuses, send notifications, or perform other actions based on ticket properties or events.",
      category: "workflows",
      comingSoon: false,
    },
    {
      id: "account-workflows",
      title: "Account workflows",
      description: "Automate account processes.",
      illustration: <AccountWorkflowsIllustration />,
      content:
        "Account workflows automate processes related to customer accounts. You can create workflows that update account information, create tasks, send follow-up emails, or perform other actions based on account events or properties.",
      category: "workflows",
      comingSoon: false,
    },
  ];

  const broadcastsFeatures = [
    {
      id: "slack-broadcasts",
      title: "Slack broadcasts",
      description: "Send targeted messages to specific Slack channels.",
      illustration: <SlackBroadcastsIllustration />,
      content:
        "Slack broadcasts allow you to send targeted messages to specific Slack channels. You can reach the right audience with your broadcasts and increase engagement with your support content.",
      category: "broadcasts",
      comingSoon: true,
    },
    {
      id: "email-broadcasts",
      title: "Email broadcasts",
      description: "Send targeted emails to specific customer segments.",
      illustration: <EmailBroadcastsIllustration />,
      content:
        "Email broadcasts allow you to send targeted emails to specific customer segments. You can reach the right audience with your broadcasts and increase engagement with your support content.",
      category: "broadcasts",
      comingSoon: true,
    },
    {
      id: "broadcasts-wysiwyg-editor",
      title: "Broadcasts WYSIWYG editor",
      description: "Create rich content for your broadcasts.",
      illustration: <BroadcastsWYSWIGEditorIllustration />,
      content:
        "The broadcasts WYSIWYG editor allows you to create rich content for your broadcasts. You can format messages with headings, lists, images, and more, and maintain consistent styling across your broadcasts.",
      category: "broadcasts",
      comingSoon: true,
    },
    {
      id: "static-audience-list",
      title: "Static audience list",
      description: "Create lists of customers based on static criteria.",
      illustration: <StaticAudienceListIllustration />,
      content:
        "Static audience lists allow you to create lists of customers based on static criteria. You can target specific groups with your broadcasts and increase engagement with your support content.",
      category: "broadcasts",
      comingSoon: true,
    },
    {
      id: "dynamic-audience-list",
      title: "Dynamic audience list",
      description: "Create lists of customers based on dynamic criteria.",
      illustration: <DynamicAudienceListIllustration />,
      content:
        "Dynamic audience lists allow you to create lists of customers based on dynamic criteria. You can target specific groups with your broadcasts and increase engagement with your support content.",
      category: "broadcasts",
      comingSoon: true,
    },
  ];

  const othersFeatures = [
    {
      id: "search",
      title: "Search",
      description: "Find information quickly with search.",
      illustration: <SearchIllustration />,
      content:
        "The search feature allows you to quickly find tickets, articles, and other content across the platform. It supports advanced search operators, filters, and saved searches to help you locate exactly what you're looking for.",
      learnMoreLink: "https://docs.thena.ai/guides/platform/search",
      category: "others",
      comingSoon: false,
    },
    {
      id: "views",
      title: "Views",
      description: "Create custom views of data.",
      illustration: <ViewsIllustration />,
      content:
        "Views allow you to create customized displays of your data. You can define filters, columns, sorting, and grouping to create focused views that show exactly the information you need for specific tasks or roles.",
      category: "others",
      comingSoon: false,
    },
    {
      id: "filters",
      title: "Filters",
      description: "Filter data to find what you need.",
      illustration: <FiltersIllustration />,
      content:
        "Filters help you narrow down data to find exactly what you're looking for. You can filter by properties, dates, status, assignee, or any other field to focus on the most relevant information.",
      category: "others",
      comingSoon: false,
    },
    {
      id: "display",
      title: "Display",
      description: "Customize how data is displayed.",
      illustration: <DisplayIllustration />,
      content:
        "Display options allow you to customize how information is presented. You can choose between list, board, or calendar views, adjust density, select visible columns, and set other preferences to optimize your workflow.",
      category: "others",
      comingSoon: false,
    },
    {
      id: "apps",
      title: "Apps",
      description: "Extend functionality with apps.",
      illustration: <_AppsIllustration />,
      content:
        "Apps allow you to extend the platform's functionality by integrating with other tools and services. You can connect to CRM systems, marketing platforms, development tools, and other services to create a unified workflow across your tech stack.",
      category: "others",
      comingSoon: false,
    },
    {
      id: "custom-insight-dashboards",
      title: "Custom insight dashboards",
      description: "Create personalized data visualization dashboards.",
      illustration: <CustomInsightDashboardsIllustration />,
      content:
        "Custom insight dashboards allow you to create personalized views of your most important metrics. You can build dashboards with various charts, graphs, and data visualizations to monitor performance, track goals, and identify trends across your support operations.",
      learnMoreLink:
        "https://docs.thena.ai/guides/analytics/custom-insight-dashboards",
      category: "others",
      comingSoon: false,
    },
    {
      id: "ai-text-editor",
      title: "AI text editor",
      description: "Use AI to help write responses.",
      illustration: <AITextEditorIllustration />,
      content:
        "The AI text editor uses artificial intelligence to help you write better responses faster. It can suggest completions, rephrase text, check grammar, and even generate entire responses based on the context of the conversation.",
      learnMoreLink: "https://docs.thena.ai/guides/ai/ai-text-editor",
      category: "others",
      comingSoon: false,
    },
  ];

  // Combine all features
  const allFeatures = useMemo(
    () => [
      ...ticketingFeatures,
      ...accountsFeatures,
      ...knowledgeBaseFeatures,
      ...aiAgentsFeatures,
      ...workflowsFeatures,
      ...broadcastsFeatures,
      ...othersFeatures,
    ],
    [
      ticketingFeatures,
      accountsFeatures,
      knowledgeBaseFeatures,
      aiAgentsFeatures,
      workflowsFeatures,
      broadcastsFeatures,
      othersFeatures,
    ],
  );

  // Filter features based on active filter and search query
  const filteredFeatures = filterFeaturesByCategory(allFeatures);
  const filteredAndSearchedFeatures = filterFeatures(filteredFeatures);

  // Category label mapping
  const _getCategoryInfo = (category) => {
    switch (category) {
      case "ticketing":
        return { text: "Ticketing", className: styles.ticketingLabel };
      case "accounts":
        return { text: "Accounts", className: styles.accountsLabel };
      case "knowledge-base":
        return { text: "Knowledge Base", className: styles.knowledgeBaseLabel };
      case "ai-agents":
        return { text: "AI agents", className: styles.aiAgentsLabel };
      case "workflows":
        return { text: "Workflows", className: styles.workflowsLabel };
      case "broadcasts":
        return { text: "Broadcasts", className: styles.broadcastsLabel };
      default:
        return { text: "Others", className: styles.othersLabel };
    }
  };

  // Preload all images when component mounts
  useEffect(() => {
    // Since we're not actually using the preloaded images anymore,
    // this effect can be simplified or removed
    const _allFeaturesList = allFeatures;

    // This function is no longer needed but kept for reference
    const _preloadAllImages = async () => {
      // Implementation removed to avoid lint errors
    };

    // Not calling _preloadAllImages() since we don't need it anymore
  }, [allFeatures]); // Adding allFeatures to dependency array

  return (
    <div className={styles.guideContainer}>
      <div
        className="container mx-auto py-8 px-8"
        style={{ maxHeight: "calc(100vh - 64px)", overflowY: "auto" }}
      >
        <div className="space-y-6">
          <div className="space-y-5">
            <div className={styles.searchContainer}>
              <Search className={styles.searchIcon} size={20} />
              <Input
                type="text"
                placeholder="Search features..."
                className={styles.searchInput}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* All buttons in a single horizontal row */}
            <div className={styles.filterContainer}>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "all"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("all")}
              >
                All
              </button>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "coming-soon"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("coming-soon")}
              >
                Coming soon
              </button>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "ticketing"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("ticketing")}
              >
                <Ticket size={16} className={styles.ticketingFilterIcon} />
                Ticketing
              </button>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "accounts"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("accounts")}
              >
                <Building2 size={16} className={styles.accountsFilterIcon} />
                Accounts
              </button>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "knowledge-base"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("knowledge-base")}
              >
                <BookOpen
                  size={16}
                  className={styles.knowledgeBaseFilterIcon}
                />
                Knowledge base
              </button>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "ai-agents"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("ai-agents")}
              >
                <Zap size={16} className={styles.aiAgentsFilterIcon} />
                AI agents
              </button>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "workflows"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("workflows")}
              >
                <Workflow size={16} className={styles.workflowsFilterIcon} />
                Workflows
              </button>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "broadcasts"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("broadcasts")}
              >
                <MessageCircle
                  size={16}
                  className={styles.broadcastsFilterIcon}
                />
                Broadcasts
              </button>
              <button
                className={`${styles.filterButton} ${
                  activeFilter === "others"
                    ? styles.filterButtonActive
                    : styles.filterButtonInactive
                }`}
                onClick={() => setActiveFilter("others")}
              >
                <Puzzle size={16} className={styles.othersFilterIcon} />
                Others
              </button>

              {/* External links in the same row */}
              <a
                href="https://docs.thena.ai/guides"
                target="_blank"
                rel="noopener noreferrer"
                className={`${styles.filterButton} ${styles.filterButtonInactive}`}
              >
                <BookOpen size={16} className="mr-1.5" />
                Product guides <ExternalLink className="h-3 w-3 ml-1" />
              </a>

              <a
                href="https://docs.thena.ai/platform"
                target="_blank"
                rel="noopener noreferrer"
                className={`${styles.filterButton} ${styles.filterButtonInactive}`}
              >
                <FileText size={16} className="mr-1.5" />
                Technical documentation{" "}
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>

              <a
                href="https://docs.thena.ai/api-reference"
                target="_blank"
                rel="noopener noreferrer"
                className={`${styles.filterButton} ${styles.filterButtonInactive}`}
              >
                <Code2 size={16} className="mr-1.5" />
                API reference <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
          </div>
          {/* Combined grid of all filtered features */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5 mt-4">
            {filteredAndSearchedFeatures.map((feature) => (
              <FeatureCard
                key={feature.id}
                feature={feature}
                onClick={handleFeatureSelect}
              />
            ))}
          </div>

          {filteredAndSearchedFeatures.length === 0 && (
            <div className="flex flex-col items-center justify-center py-12">
              <p className="text-muted-foreground">
                No features found matching your search criteria.
              </p>
            </div>
          )}

          <Dialog
            open={!!selectedFeature}
            onOpenChange={(open) => {
              if (!open) {
                setSelectedFeature(null);
              }
            }}
          >
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto p-0">
              {selectedFeature && (
                <>
                  <DialogHeader
                    className="px-9 pt-9 pb-0 border-none"
                    style={{ marginBottom: 0 }}
                  >
                    <h2
                      className={styles.dialogMainHeading + " mb-0"}
                      style={{ lineHeight: "1", marginBottom: "4px" }}
                    >
                      {selectedFeature.title}
                    </h2>
                    <p
                      className={styles.dialogDescription + " mt-0"}
                      style={{ marginBottom: 0 }}
                    >
                      {selectedFeature.description.endsWith(".")
                        ? selectedFeature.description
                        : `${selectedFeature.description}.`}
                    </p>
                  </DialogHeader>
                  <div
                    className={styles.dialogContent + " px-9"}
                    style={{ marginTop: 0 }}
                  >
                    {hasImageForFeature(selectedFeature.id) ? (
                      <div className="relative rounded overflow-hidden mt-3 mb-4">
                        {/* Show loading state if image is still loading */}
                        {(!imagesLoaded[selectedFeature.id] ||
                          imageLoadingStatus[selectedFeature.id] ===
                            "loading") && (
                          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 animate-pulse">
                            <div className="text-gray-500">
                              Loading image...
                            </div>
                          </div>
                        )}
                        <Image
                          src={getImagePathForFeature(selectedFeature.id)}
                          alt={`${selectedFeature.title} configuration in Thena`}
                          className="rounded w-full"
                          width={800}
                          height={450}
                          priority={true}
                          unoptimized={true}
                          loading="eager"
                          onLoadingComplete={(img) => {
                            if (img) {
                              // Update loading status
                              setImagesLoaded((prevLoaded) => ({
                                ...prevLoaded,
                                [selectedFeature.id]: true,
                              }));
                              setImageLoadingStatus((prevStatus) => ({
                                ...prevStatus,
                                [selectedFeature.id]: "loaded",
                              }));
                            }
                          }}
                        />
                      </div>
                    ) : (
                      <div className="border-t border-gray-100 dark:border-gray-800 mt-0 mb-4"></div>
                    )}

                    <p
                      className={styles.dialogText}
                      style={{ fontSize: "14px" }}
                    >
                      {selectedFeature.content}
                    </p>

                    <h3
                      className={styles.dialogSubheading + " mt-4"}
                      style={{ borderBottom: "none", paddingBottom: 0 }}
                    >
                      Key benefits
                    </h3>
                    <ul
                      className={styles.dialogList}
                      style={{ fontSize: "14px" }}
                    >
                      {getFeatureBenefits(selectedFeature.id).map(
                        (benefit, index) => (
                          <li key={index}>{benefit}</li>
                        ),
                      )}
                    </ul>
                  </div>
                  <div className="flex justify-end gap-2 pt-4 px-9 pb-9">
                    <Button
                      onClick={() => setSelectedFeature(null)}
                      variant="ghost"
                      style={{
                        height: "40px",
                        padding: "0 16px",
                        display: "inline-flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: "14px",
                        border: "none",
                      }}
                    >
                      Cancel
                    </Button>

                    {selectedFeature.learnMoreLink && (
                      <a
                        href={selectedFeature.learnMoreLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          display: "inline-flex",
                          alignItems: "center",
                          justifyContent: "center",
                          height: "40px",
                          padding: "0 16px",
                          background:
                            "linear-gradient(to right, #6A00FF, #3B01B7)",
                          color: "white",
                          fontWeight: 500,
                          fontSize: "14px",
                          borderRadius: "6px",
                          textDecoration: "none",
                          transition: "all 0.2s",
                          cursor: "pointer",
                        }}
                        onMouseOver={(e) => {
                          e.currentTarget.style.opacity = "0.9";
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.opacity = "1";
                        }}
                      >
                        Learn more
                      </a>
                    )}
                  </div>
                </>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </div>
      {activeFilter === "all" && (
        <div className="mt-16 border-t pt-8">
          <h2 className="text-xl font-semibold mb-4">Additional resources</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="https://docs.thena.ai/guides"
              target="_blank"
              rel="noopener noreferrer"
              className="flex flex-col p-6 rounded-lg border border-gray-200 hover:border-purple-300 transition-colors"
            >
              <div className="flex items-center gap-2 mb-2">
                <BookOpen className="text-purple-600" size={20} />
                <h3 className="font-medium">Product guides</h3>
              </div>
              <p className="text-sm text-gray-600">
                Detailed guides on how to use Thena&apos;s features effectively.
              </p>
            </a>

            <a
              href="https://docs.thena.ai/platform"
              target="_blank"
              rel="noopener noreferrer"
              className="flex flex-col p-6 rounded-lg border border-gray-200 hover:border-purple-300 transition-colors"
            >
              <div className="flex items-center gap-2 mb-2">
                <FileText className="text-purple-600" size={20} />
                <h3 className="font-medium">Technical documentation</h3>
              </div>
              <p className="text-sm text-gray-600">
                Technical details about Thena&apos;s platform architecture and
                components.
              </p>
            </a>

            <a
              href="https://docs.thena.ai/api-reference"
              target="_blank"
              rel="noopener noreferrer"
              className="flex flex-col p-6 rounded-lg border border-gray-200 hover:border-purple-300 transition-colors"
            >
              <div className="flex items-center gap-2 mb-2">
                <Code2 className="text-purple-600" size={20} />
                <h3 className="font-medium">API reference</h3>
              </div>
              <p className="text-sm text-gray-600">
                Complete API documentation for developers integrating with
                Thena.
              </p>
            </a>
          </div>
        </div>
      )}
    </div>
  );
}
