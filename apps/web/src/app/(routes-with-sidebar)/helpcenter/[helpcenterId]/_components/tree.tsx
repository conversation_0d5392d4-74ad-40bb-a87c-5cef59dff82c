"use client";

import { useApiMutation } from "@/hooks/use-api-mutation";
import { triggerPostMoveFlash } from "@atlaskit/pragmatic-drag-and-drop-flourish/trigger-post-move-flash";
import {
  type Instruction,
  type ItemMode,
} from "@atlaskit/pragmatic-drag-and-drop-hitbox/tree-item";
import { monitorForElements } from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import debounce from "lodash/debounce";
import _isEqual from "lodash/isEqual";
import memoizeOne from "memoize-one";
import {
  memo,
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from "react";
import { toast } from "sonner";
import { revalidateHelpcenter } from "../../_components/api";
import {
  TreeContext,
  useDependencyContext,
  type TreeContextValue,
} from "./tree-context";
import TreeItem from "./tree-item";
import {
  getChildCollectionsCount,
  tree,
  treeStateReducer,
  type TreeItem as TreeItemType,
} from "./util";

function createTreeItemRegistry() {
  const registry = new Map<
    string,
    { element: HTMLElement; actionMenuTrigger: HTMLElement }
  >();

  const registerTreeItem = ({
    itemId,
    element,
    actionMenuTrigger,
  }: {
    itemId: string;
    element: HTMLElement;
    actionMenuTrigger: HTMLElement;
  }) => {
    registry.set(itemId, { element, actionMenuTrigger });
    return () => {
      registry.delete(itemId);
    };
  };

  return { registry, registerTreeItem };
}

type Props = {
  defaultTree: TreeItemType[]; // Should be memoized
  prevCollectionId: string;
  searchValue: string;
  refetch: VoidFunction;
  controllerRef: RefObject<AbortController | null>;
};

const debouncedRefetchQuery = debounce((func: VoidFunction) => {
  func();
}, 300);

function Tree({
  defaultTree,
  prevCollectionId,
  searchValue,
  refetch,
  controllerRef,
}: Props) {
  const [state, updateState] = useReducer(treeStateReducer, {
    data: defaultTree,
    lastAction: null,
  });
  const ref = useRef<HTMLDivElement>(null);
  const { extractInstruction } = useDependencyContext();

  const cancelCurrentCall = useCallback(() => {
    controllerRef.current?.abort();
  }, [controllerRef]);

  useEffect(() => {
    updateState({
      type: "set-data",
      data: { updated: defaultTree, prevCollectionId, searchValue },
    });
  }, [defaultTree, prevCollectionId, searchValue]);

  const [{ registry, registerTreeItem }] = useState(createTreeItemRegistry);

  const { mutate: reorderArticleMutate } = useApiMutation(
    `/articles`,
    {},
    "PATCH",
    "kb",
  );

  const { mutate: reorderCollectionMutate } = useApiMutation(
    `/collections`,
    {},
    "PATCH",
    "kb",
  );

  const { data, lastAction } = state;

  const lastStateRef = useRef<TreeItemType[]>(data);

  useEffect(() => {
    if (!lastAction || lastAction.type !== "instruction") {
      return;
    }

    if (_isEqual(lastStateRef.current, data)) {
      return;
    }

    const { itemId, targetId } = lastAction;

    const originalSource = tree.find(lastStateRef.current, itemId);

    if (!originalSource) {
      console.log("CANNOT_FIND_SOURCE", { lastStateRef, itemId });
      return;
    }
    const target = tree.find(data, targetId);

    if (!target) {
      console.log("CANNOT_FIND_TARGET", { targetId });
      return;
    }

    const droppedSource = tree.find(data, itemId);

    const pathname = window.location.pathname.split("/");

    const helpCenterId = pathname[pathname.length - 1];

    if (!droppedSource) {
      console.log("CANNOT_FIND_DROPPED_SOURCE", { itemId });
      return;
    }

    if (originalSource.type === "article") {
      if (!originalSource.collectionId) {
        console.log("CANNOT_FIND_ARTICLE_COLLECTION_ID", { originalSource });
        //
        // : LOG TO SENTRY WHENEVER SENTRY IS ADDED
        // sendErrorToSentry({
        //   message: "Tree DND error: Cannot find article's collection ID",
        //   error: new Error("Tree DND error", { cause: originalSource }),
        //   tag: {
        //     key: "Tree DND",
        //     value: "error",
        //   },
        // });
        return;
      }

      let toCollectionId = target._id;
      if (target.type == "article") {
         
        toCollectionId = target.collectionId!;
      }

      if (originalSource.type === "article") {
        (async () => {
          try {
            cancelCurrentCall();
            await reorderArticleMutate(
              {
                fromCollectionId: originalSource.collectionId,
                toCollectionId,
                order: droppedSource.order,
                helpCenterId,
              },
              {},
              `/${itemId}/reorder`,
            );
            debouncedRefetchQuery(refetch);
            revalidateHelpcenter([`getAllCollections-${helpCenterId}`]);
          } catch (error) {
            console.error(error);
            toast.error(
              error instanceof Error
                ? error.message
                : "Failed to reorder article",
            );
          }
        })();
      }
    } else {
      const reorder =
        lastAction.instruction.type === "reorder-above" ||
        lastAction.instruction.type === "reorder-below";

      let toCollectionId = target._id;
      if (reorder && target.type === "collection") {
        // When dropped the collection on the root level
        if (!target.parentId) {
          (async () => {
            try {
              cancelCurrentCall();
              await reorderCollectionMutate(
                {
                  order: droppedSource.order,
                  helpCenterId,
                },
                {},
                `/${itemId}/reorder`,
              );
              debouncedRefetchQuery(refetch);
              revalidateHelpcenter([`getAllCollections-${helpCenterId}`]);
            } catch (error) {
              console.error(error);
              toast.error(
                error instanceof Error
                  ? error.message
                  : "Failed to reorder collection",
              );
            }
          })();
          return;
        }
        // When reordering above or below a collection
        toCollectionId = target.parentId!;
      }

      if (target.type == "article") {
        toCollectionId = target.collectionId!;
      }
      (async () => {
        try {
          cancelCurrentCall();
          await reorderCollectionMutate(
            {
              order: droppedSource.order,
              helpCenterId,
              toCollectionId,
            },
            {},
            `/${itemId}/reorder`,
          );
          debouncedRefetchQuery(refetch);
          revalidateHelpcenter([`getAllCollections-${helpCenterId}`]);
        } catch (error) {
          console.error(error);
          toast.error(
            error instanceof Error
              ? error.message
              : "Failed to reorder collection",
          );
        }
      })();
    }
  }, [lastAction, data]);

  useEffect(() => {
    lastStateRef.current = data;
  }, [data]);

  useEffect(() => {
    if (lastAction === null) {
      return;
    }

    if (lastAction.type === "modal-move") {
      const { element, actionMenuTrigger } =
        registry.get(lastAction.itemId) ?? {};
      if (element) {
        triggerPostMoveFlash(element);
      }

      /**
       * Only moves triggered by the modal will result in focus being
       * returned to the trigger.
       */
      actionMenuTrigger?.focus();

      return;
    }

    if (lastAction.type === "instruction") {
      const { element } = registry.get(lastAction.itemId) ?? {};
      if (element) {
        triggerPostMoveFlash(element);
      }

      return;
    }
  }, [lastAction, registry]);

  const getChildrenOfItem = useCallback((itemId: string) => {
    const data = lastStateRef.current;

    /**
     * An empty string is representing the root
     */
    if (itemId === "") {
      return data;
    }

    const item = tree.find(data, itemId);

    if (!item) {
      return [];
    }
    return item.children;
  }, []);

  const context = useMemo<TreeContextValue>(
    () => ({
      dispatch: updateState,
      uniqueContextId: Symbol("unique-id"),
      // memoizing this function as it is called by all tree items repeatedly
      // An ideal refactor would be to update our data shape
      // to allow quick lookups of parents
      getPathToItem: memoizeOne(
        (targetId: string) =>
          tree.getPathToItem({ current: lastStateRef.current, targetId }) ?? [],
      ),
      getChildrenOfItem,
      registerTreeItem,
    }),
    [getChildrenOfItem, registerTreeItem],
  );

  useEffect(() => {
    return monitorForElements({
      canMonitor: ({ source }) =>
        source.data.uniqueContextId === context.uniqueContextId,
      onDragStart() {
        cancelCurrentCall();
      },
      onDrop(args) {
        const { location, source } = args;
        // didn't drop on anything
        if (!location.current.dropTargets.length) {
          return;
        }

        if (source.data.type === "tree-item") {
          const itemId = source.data.id as string;

          const target = location.current.dropTargets[0];
          const targetId = target.data.id as string;

          const instruction: Instruction | null = extractInstruction(
            target.data,
          );

          if (instruction !== null) {
            if (source.data.isArticle) {
              updateState({
                type: "instruction",
                instruction,
                itemId,
                targetId,
              });
            } else {
              const targetCollection = tree.find(
                lastStateRef.current,
                targetId,
              );

              const sourceCollection = tree.find(lastStateRef.current, itemId);

              if (targetCollection && sourceCollection) {
                const sourceChildCollectionCount =
                  getChildCollectionsCount(sourceCollection);

                const targetCollectionDepth = targetCollection.depth ?? -1; // -1 represents root level

                const reorder =
                  instruction.type === "reorder-above" ||
                  instruction.type === "reorder-below";

                const disableAction = () => {
                  if (targetCollectionDepth === -1 && reorder) {
                    return false;
                  }
                  if (
                    targetCollectionDepth === -1 &&
                    sourceChildCollectionCount > 2
                  ) {
                    return true;
                  }
                  if (
                    targetCollectionDepth === 0 &&
                    sourceChildCollectionCount > 1
                  ) {
                    return true;
                  }

                  if (
                    targetCollectionDepth === 1 &&
                    sourceChildCollectionCount > 0
                  ) {
                    return true;
                  }
                  return false;
                };

                if (disableAction()) {
                  toast.error("Exceeded maximum allowed depth");
                  console.log("ACTION_BLOCKED", {
                    targetCollection,
                    sourceCollection,
                  });
                  return;
                }

                updateState({
                  type: "instruction",
                  instruction,
                  itemId,
                  targetId,
                });
              }
            }
          }
        }
      },
    });
  }, [cancelCurrentCall, context, extractInstruction]);

  const topLevelCollections = data
    .filter((item) => item.type === "collection" && !item.parentId)
    .map((item) => item._id);

  return (
    <TreeContext.Provider value={context}>
      <div ref={ref}>
        {data.map((item, index, array) => {
          const type: ItemMode = (() => {
            if (item.children.length && item.isOpen) {
              return "expanded";
            }

            if (index === array.length - 1) {
              return "last-in-group";
            }

            return "standard";
          })();

          return (
            <TreeItem
              item={item}
              level={0}
              key={item._id}
              mode={type}
              index={index}
              topLevelCollections={topLevelCollections}
              tree={data}
              searchTerm={searchValue}
            />
          );
        })}
      </div>
    </TreeContext.Provider>
  );
}
export default memo(Tree);
