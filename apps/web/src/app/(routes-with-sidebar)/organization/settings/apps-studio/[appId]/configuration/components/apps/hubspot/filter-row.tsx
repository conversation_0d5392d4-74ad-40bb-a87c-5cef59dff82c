"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Trash2 } from "lucide-react";
import {
  HUBSPOT_FIELD_OPERATORS,
  HubSpotArrayOperator,
  HubSpotFilter,
  HubSpotNoValueOperator,
  HubSpotOperator,
  NO_VALUE_OPERATORS,
  OPERATOR_LABELS,
} from "./types";

interface FilterRowProps {
  filter: HubSpotFilter;
  availableFields: string[];
  onUpdate: (filter: HubSpotFilter) => void;
  onDelete: () => void;
}

export function FilterRow({
  filter,
  availableFields,
  onUpdate,
  onDelete,
}: FilterRowProps) {
  const needsValue = !NO_VALUE_OPERATORS.includes(
    filter.operator as HubSpotNoValueOperator,
  );
  const isArrayValue = [
    HUBSPOT_FIELD_OPERATORS.IN,
    HUBSPOT_FIELD_OPERATORS.NOT_IN,
  ].includes(filter.operator as HubSpotArrayOperator);

  const handleOperatorChange = (value: string) => {
    const operator = value as HubSpotOperator;
    const isNewOperatorArray = [
      HUBSPOT_FIELD_OPERATORS.IN,
      HUBSPOT_FIELD_OPERATORS.NOT_IN,
    ].includes(operator as HubSpotArrayOperator);

    // Convert value based on the new operator type
    let newValue = filter.value;
    if (isNewOperatorArray && !Array.isArray(filter.value)) {
      newValue = filter.value ? [filter.value.toString()] : [];
    } else if (!isNewOperatorArray && Array.isArray(filter.value)) {
      newValue = filter.value.join(", ");
    }

    onUpdate({
      ...filter,
      operator,
      value: newValue,
    });
  };

  return (
    <div className="flex items-center gap-2">
      <Select
        value={filter.field}
        onValueChange={(value) => onUpdate({ ...filter, field: value })}
      >
        <SelectTrigger className="min-w-[120px] w-fit">
          <SelectValue placeholder="Select field" />
        </SelectTrigger>
        <SelectContent>
          {availableFields.map((field) => (
            <SelectItem key={field} value={field}>
              {field}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={filter.operator} onValueChange={handleOperatorChange}>
        <SelectTrigger className="min-w-[100px] w-fit">
          <SelectValue placeholder="Select operator" />
        </SelectTrigger>
        <SelectContent>
          {(Object.entries(HUBSPOT_FIELD_OPERATORS) as [string, string][]).map(
            ([key, value]) => (
              <SelectItem key={key} value={value}>
                {OPERATOR_LABELS[value as keyof typeof OPERATOR_LABELS]}
              </SelectItem>
            ),
          )}
        </SelectContent>
      </Select>

      {needsValue && (
        <Input
          type="text"
          value={
            isArrayValue
              ? (filter.value as string[])?.join(", ") || ""
              : (filter.value as string) || ""
          }
          onChange={(e) => {
            const value = e.target.value;
            onUpdate({
              ...filter,
              value: isArrayValue
                ? value
                    .split(",")
                    .map((v) => v.trim())
                    .filter(Boolean)
                : value,
            });
          }}
          placeholder={isArrayValue ? "Value1, Value2, Value3" : "Enter value"}
          className="flex-1"
        />
      )}

      <Button
        variant="ghost"
        size="icon"
        onClick={onDelete}
        className="h-10 w-10"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
}
