// Common integration status type
export interface HubSpotIntegrationStatus {
  enabled: boolean;
  integratedBy?: string;
  integratedOn?: string; // ISO date string
}

// HubSpot-specific constants and types
export const HUBSPOT_FIELD_OPERATORS = {
  EQ: "EQ",
  NEQ: "NEQ",
  GT: "GT",
  GTE: "GTE",
  LT: "LT",
  LTE: "LTE",
  CONTAINS: "CONTAINS",
  NOT_CONTAINS: "NOT_CONTAINS",
  HAS_PROPERTY: "HAS_PROPERTY",
  NOT_HAS_PROPERTY: "NOT_HAS_PROPERTY",
  IN: "IN",
  NOT_IN: "NOT_IN",
  IS_EMPTY: "IS_EMPTY",
  IS_NOT_EMPTY: "IS_NOT_EMPTY",
  IS_TRUE: "IS_TRUE",
  IS_FALSE: "IS_FALSE",
  STARTS_WITH: "STARTS_WITH",
  ENDS_WITH: "ENDS_WITH",
} as const;

// Human-readable labels for operators
export const OPERATOR_LABELS = {
  [HUBSPOT_FIELD_OPERATORS.EQ]: "Equals",
  [HUBSPOT_FIELD_OPERATORS.NEQ]: "Not equals",
  [HUBSPOT_FIELD_OPERATORS.GT]: "Greater than",
  [HUBSPOT_FIELD_OPERATORS.GTE]: "Greater than or equal",
  [HUBSPOT_FIELD_OPERATORS.LT]: "Less than",
  [HUBSPOT_FIELD_OPERATORS.LTE]: "Less than or equal",
  [HUBSPOT_FIELD_OPERATORS.CONTAINS]: "Contains",
  [HUBSPOT_FIELD_OPERATORS.NOT_CONTAINS]: "Does not contain",
  [HUBSPOT_FIELD_OPERATORS.HAS_PROPERTY]: "Has property",
  [HUBSPOT_FIELD_OPERATORS.NOT_HAS_PROPERTY]: "Does not have property",
  [HUBSPOT_FIELD_OPERATORS.IN]: "Is any of",
  [HUBSPOT_FIELD_OPERATORS.NOT_IN]: "Is none of",
  [HUBSPOT_FIELD_OPERATORS.IS_EMPTY]: "Is empty",
  [HUBSPOT_FIELD_OPERATORS.IS_NOT_EMPTY]: "Is not empty",
  [HUBSPOT_FIELD_OPERATORS.IS_TRUE]: "Is true",
  [HUBSPOT_FIELD_OPERATORS.IS_FALSE]: "Is false",
  [HUBSPOT_FIELD_OPERATORS.STARTS_WITH]: "Starts with",
  [HUBSPOT_FIELD_OPERATORS.ENDS_WITH]: "Ends with",
} as const;

// Operators that don't require a value input
export const NO_VALUE_OPERATORS = [
  HUBSPOT_FIELD_OPERATORS.HAS_PROPERTY,
  HUBSPOT_FIELD_OPERATORS.NOT_HAS_PROPERTY,
  HUBSPOT_FIELD_OPERATORS.IS_EMPTY,
  HUBSPOT_FIELD_OPERATORS.IS_NOT_EMPTY,
  HUBSPOT_FIELD_OPERATORS.IS_TRUE,
  HUBSPOT_FIELD_OPERATORS.IS_FALSE,
] as const;

export type HubSpotNoValueOperator = (typeof NO_VALUE_OPERATORS)[number];

export type HubSpotOperator =
  (typeof HUBSPOT_FIELD_OPERATORS)[keyof typeof HUBSPOT_FIELD_OPERATORS];

export type HubSpotArrayOperator =
  | typeof HUBSPOT_FIELD_OPERATORS.IN
  | typeof HUBSPOT_FIELD_OPERATORS.NOT_IN;

export interface HubSpotFilter {
  field: string;
  operator: HubSpotOperator;
  value: string | string[];
}

export interface HubSpotSettingsResponseDto {
  integration: HubSpotIntegrationStatus;
  companies: {
    enabled: boolean;
    selectedFields: string[];
    filters: HubSpotFilter[];
    totalCount?: number;
    syncedCount?: number;
  };
  contacts: {
    enabled: boolean;
    selectedFields: string[];
    filters: HubSpotFilter[];
    totalCount?: number;
    syncedCount?: number;
  };
}

export interface UpdateHubSpotSettingsDto {
  integration?: Pick<HubSpotIntegrationStatus, "enabled">;
  companies?: {
    enabled: boolean;
    selectedFields: string[];
    filters: HubSpotFilter[];
  };
  contacts?: {
    enabled: boolean;
    selectedFields: string[];
    filters: HubSpotFilter[];
  };
}

export interface HubSpotConfig {
  accounts: {
    enabled: boolean;
    selectedFields: string[];
    filters: HubSpotFilter[];
    totalCount: number;
    syncedCount: number;
  };
  contacts: {
    enabled: boolean;
    selectedFields: string[];
    filters: HubSpotFilter[];
    totalCount: number;
    syncedCount: number;
  };
}

// Sync status types
export type SyncStatus =
  | "waiting"
  | "active"
  | "completed"
  | "failed"
  | "delayed";

export interface SyncOperation {
  id: string;
  status: SyncStatus;
  progress: number;
  createdAt: string;
  processedAt?: string;
  finishedAt?: string;
  failedReason?: string;
  data: {
    organizationId: string;
    jobType: "account-sync" | "contact-sync";
  };
}

export interface HubSpotSyncStatusResponse {
  accountSync: SyncOperation[];
  contactSync: SyncOperation[];
}

// Trigger sync types
export interface TriggerSyncRequest {
  syncAccounts?: boolean;
  syncContacts?: boolean;
}

export interface TriggerSyncResponse {
  success: boolean;
  message: string;
  jobsCreated: string[];
}
