"use client";

import { InstallationSteps } from "@/components/app-studio/installation/installation-steps";
import { SettingsStep } from "@/components/app-studio/installation/settings-step";
import ThenaLoader from "@/components/thena-loader";
import { Button } from "@/components/ui/button";
import { GET_APP } from "@/services/app-studio";
import { AppResponseDto } from "@/types/app-studio";
import { ChevronRight, Settings } from "lucide-react";
import { redirect, useRouter } from "next/navigation";
import { use, useEffect, useState } from "react";

type Props = {
  params: Promise<{ appId: string }>;
  searchParams: Promise<{ step?: string }>;
};

export default function InstallSettingsPage({ params, searchParams }: Props) {
  const { appId } = use(params);
  const { step = "settings" } = use(searchParams);
  const [app, setApp] = useState<AppResponseDto | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const loadApp = async () => {
      try {
        const res = await fetch(GET_APP(appId));
        const data = await res.json();

        if (!res.ok) {
          // Only redirect on unauthorized
          if (res.status === 401) {
            redirect("/auth/login");
          }
          throw new Error(data.error || "Failed to fetch app");
        }

        setApp(data);
        setError(null);
      } catch (error) {
        console.error("Error fetching app:", error);
        setError(
          error instanceof Error ? error.message : "Failed to fetch app",
        );
      }
    };

    loadApp();
  }, [appId]);

  // Handle step navigation after app is loaded
  useEffect(() => {
    if (!app) return;

    if (step === "review") {
      redirect(`/organization/settings/apps-studio/${appId}/install`);
    } else if (step === "complete") {
      redirect(`/organization/settings/apps-studio/${appId}/install/complete`);
    }
  }, [app, appId, step]);

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-lg font-semibold text-red-600 mb-2">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!app) {
    return (
      <div className="flex-1 flex justify-center">
        <div className="max-w-7xl w-full mx-auto">
          <div className="h-[calc(100vh-75px)] overflow-y-auto flex items-center justify-center">
            <ThenaLoader />
          </div>
        </div>
      </div>
    );
  }

  const hasNoSettings =
    !app.manifest.configuration.required_settings?.length &&
    !app.manifest.configuration.optional_settings?.length;

  return (
    <div className="flex-1 flex justify-center">
      <div className="max-w-3xl w-full mx-auto px-8 py-8">
        {/* App Header */}
        <div>
          {/* Header space */}
          <div className="h-6" />
          {/* Header */}
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-sm bg-background overflow-hidden">
              <img
                src={app.manifest.app.icons.small}
                alt={app.manifest.app.name}
                crossOrigin="anonymous"
                className="h-full w-full object-cover"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold">{app.manifest.app.name}</h1>
              <p className="text-muted-foreground">
                {app.manifest.metadata.title}
              </p>
            </div>
          </div>

          {/* Installation Steps */}
          <InstallationSteps currentStep="settings" />
        </div>

        {hasNoSettings ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="rounded-full bg-muted p-4">
              <Settings className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="mt-4 text-lg font-semibold">No Settings Required</h3>
            <p className="mt-2 max-w-sm text-sm text-muted-foreground">
              This app doesn&apos;t require any configuration. You can proceed
              to complete the installation.
            </p>
            <Button
              onClick={() =>
                router.push(
                  `/organization/settings/apps-studio/${app.uid}/install/complete`,
                )
              }
              className="mt-4"
            >
              Continue to Installation
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
        ) : (
          <SettingsStep app={app} />
        )}
      </div>
    </div>
  );
}
