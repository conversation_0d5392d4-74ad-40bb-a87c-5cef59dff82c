"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Cross2Icon } from "@radix-ui/react-icons";
import { Info, Loader2 } from "lucide-react";
import * as React from "react";

interface FieldOption {
  value: string;
  label: string;
}

interface CustomFieldsSelectorProps {
  value?: string[];
  onChange?: (fields: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  appId?: string;
  fieldType?: "company" | "contact";
}

// Default fields that cannot be removed
const DEFAULT_FIELDS = {
  company: {
    name: "Maps to Thena account name",
    domain: "Maps to Thena account primaryDomain",
    hubspot_owner_id:
      "Maps to Thena account accountOwnerId (if the owner email matches a Thena user)",
    logo: "Maps to Thena account logo",
    description:
      'Used in a generated description: "Imported from HubSpot - ${company.name}"',
    hubspot_company_id: "Stores the HubSpot company ID for reference",
    hubspot_company_name: "Stores the HubSpot company name",
  },
  contact: {
    email: "Maps to Thena contact email (required field)",
    firstname: "Maps to Thena contact firstName",
    lastname: "Maps to Thena contact lastName (optional, can be null)",
    phone: "Maps to Thena contact phoneNumber (optional, can be null)",
    hubspot_contact_id:
      "Stores the HubSpot contact ID for reference (source: 'customer_contact')",
  },
};

export function CustomFieldsSelector({
  value = [],
  onChange,
  placeholder = "Search fields...",
  disabled = false,
  className,
  appId,
  fieldType,
}: CustomFieldsSelectorProps) {
  const [inputValue, setInputValue] = React.useState("");
  const [open, setOpen] = React.useState(false);
  const [searchResults, setSearchResults] = React.useState<FieldOption[]>([]);
  const [isSearching, setIsSearching] = React.useState(false);
  const [searchError, setSearchError] = React.useState<string | null>(null);

  const inputRef = React.useRef<HTMLInputElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const searchTimeoutRef = React.useRef<NodeJS.Timeout | undefined>(undefined);

  // Get default fields for the current field type
  const getDefaultFields = () => {
    const type = fieldType || "company";
    return Object.keys(DEFAULT_FIELDS[type]);
  };

  // Merge default fields with user selections
  const getAllFields = () => {
    const defaultFields = getDefaultFields();
    const userFields = value.filter((field) => !defaultFields.includes(field));
    return [...defaultFields, ...userFields];
  };

  // Get display value ensuring default fields are always included
  const displayValue = getAllFields();

  // Handle clicks outside to close dropdown
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Search API function
  const searchFields = React.useCallback(
    async (query: string) => {
      if (!appId || !fieldType || query.length < 2) {
        setSearchResults([]);
        return;
      }

      setIsSearching(true);
      setSearchError(null);

      try {
        const response = await fetch(
          `/api/app-studio/apps/${appId}/hubspot/search-fields`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ query, type: fieldType }),
          },
        );

        if (!response.ok) {
          throw new Error("Failed to search fields");
        }

        const data = await response.json();
        setSearchResults(data.fields || []);
      } catch (error) {
        console.error("Error searching fields:", error);
        setSearchError("Failed to search fields");
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    },
    [appId, fieldType],
  );

  // Debounced search effect
  React.useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (inputValue.length >= 2) {
      searchTimeoutRef.current = setTimeout(() => {
        searchFields(inputValue);
      }, 300);
    }
    // Don't clear search results when input becomes empty - let them persist

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [inputValue, searchFields]);

  // Handle selecting a field from dropdown
  const handleSelect = (selectedField: FieldOption) => {
    const isAlreadySelected = value.includes(selectedField.value);
    const isDefault = isDefaultField(selectedField.value);

    // Don't allow selecting/deselecting default fields
    if (isDefault) {
      return;
    }

    if (isAlreadySelected) {
      // Remove if already selected
      onChange?.(value.filter((field) => field !== selectedField.value));
    } else {
      // Add to selection
      onChange?.([...value, selectedField.value]);
    }

    // Clear input but keep search results and dropdown open
    setInputValue("");
    setOpen(true); // Explicitly keep dropdown open

    // Keep focus on input for continued searching
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  // Handle removing a selected field chip
  const handleRemove = (fieldToRemove: string) => {
    // Don't allow removing default fields
    if (isDefaultField(fieldToRemove)) {
      return;
    }
    onChange?.(value.filter((field) => field !== fieldToRemove));
  };

  // Handle input changes
  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    if (!open) setOpen(true);
  };

  // Get display label for a selected field value
  const getFieldLabel = (fieldValue: string) => {
    // Always show the field value
    return fieldValue;
  };

  // Check if a field is a default/required field
  const isDefaultField = (fieldValue: string) => {
    const type = fieldType || "company";
    return fieldValue in DEFAULT_FIELDS[type];
  };

  // Get tooltip content for default fields
  const getDefaultFieldTooltip = (fieldValue: string) => {
    const type = fieldType || "company";
    return DEFAULT_FIELDS[type][
      fieldValue as keyof (typeof DEFAULT_FIELDS)[typeof type]
    ];
  };

  return (
    <TooltipProvider delayDuration={0}>
      <div
        ref={containerRef}
        className={cn(
          "relative h-auto w-full overflow-visible bg-transparent",
          className,
        )}
      >
        <div className="flex flex-col gap-2 bg-transparent text-sm">
          {/* Selected fields as chips - now positioned at the top */}
          {displayValue.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {displayValue.map((field) => {
                const isDefault = isDefaultField(field);
                return (
                  <div
                    key={field}
                    className={cn(
                      "inline-flex h-7 items-center rounded-md border px-2 py-1 text-xs",
                      isDefault
                        ? "bg-blue-50 border-blue-200 text-blue-700"
                        : "bg-secondary border-secondary text-secondary-foreground",
                    )}
                  >
                    <span className="mr-1">{getFieldLabel(field)}</span>
                    {isDefault ? (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-3 w-3 text-blue-500" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs text-sm">
                            <strong>Default field:</strong>
                            <br />
                            {getDefaultFieldTooltip(field)}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    ) : (
                      <button
                        type="button"
                        className="ml-1 rounded-sm opacity-50 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                        onClick={() => handleRemove(field)}
                        disabled={disabled}
                      >
                        <Cross2Icon className="h-3 w-3" />
                        <span className="sr-only">
                          Remove {getFieldLabel(field)}
                        </span>
                      </button>
                    )}
                  </div>
                );
              })}
            </div>
          )}

          {/* Input field with dropdown positioned relative to it - only show if not disabled */}
          {!disabled && (
            <div className="relative">
              <div className="flex w-full items-center rounded-[4px] bg-background border">
                <input
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => handleInputChange(e.target.value)}
                  onFocus={() => setOpen(true)}
                  placeholder={placeholder}
                  disabled={disabled}
                  className="flex-1 bg-transparent outline-none border-0 placeholder:text-muted-foreground disabled:cursor-not-allowed pl-3 py-2"
                />
              </div>

              {/* Dropdown positioned right below the input */}
              {open && (
                <div className="absolute top-[100%] z-[99] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md mt-1 max-h-60 overflow-y-auto">
                  {/* Initial state: prompt to type */}
                  {inputValue.length < 2 && searchResults.length === 0 && (
                    <div className="px-2 py-1.5 text-sm text-muted-foreground">
                      Type at least 2 characters to search
                    </div>
                  )}

                  {/* Loading state */}
                  {inputValue.length >= 2 && isSearching && (
                    <div className="flex items-center gap-2 px-2 py-1.5 text-sm text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Searching...
                    </div>
                  )}

                  {/* Error state */}
                  {inputValue.length >= 2 && !isSearching && searchError && (
                    <div className="px-2 py-1.5 text-sm text-destructive">
                      {searchError}
                    </div>
                  )}

                  {/* Empty results state */}
                  {inputValue.length >= 2 &&
                    !isSearching &&
                    !searchError &&
                    searchResults.length === 0 && (
                      <div className="p-2 text-sm text-muted-foreground">
                        No fields found for &ldquo;{inputValue}&rdquo;
                      </div>
                    )}

                  {/* Debug: Show results count */}
                  {!isSearching && !searchError && searchResults.length > 0 && (
                    <div className="px-2 py-1.5 text-xs text-blue-500 border-b">
                      Found {searchResults.length} results
                      {inputValue.length === 0 && " (from previous search)"}
                    </div>
                  )}

                  {/* Search results - show if we have results, regardless of input length */}
                  {!isSearching &&
                    !searchError &&
                    searchResults.length > 0 &&
                    searchResults.map((field) => (
                      <div
                        key={field.value}
                        onClick={() => handleSelect(field)}
                        className="flex items-center gap-2 px-2 py-1.5 cursor-pointer hover:bg-accent hover:text-accent-foreground"
                      >
                        <div className="flex h-4 w-4 items-center justify-center rounded border border-primary">
                          {displayValue.includes(field.value) && (
                            <div className="h-2 w-2 rounded-sm bg-primary" />
                          )}
                        </div>
                        <span>
                          {field.label} | {field.value}
                        </span>
                      </div>
                    ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
}
