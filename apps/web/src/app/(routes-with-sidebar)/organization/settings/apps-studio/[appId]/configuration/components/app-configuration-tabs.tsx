"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useAppStudioStore } from "@/store/app-studio-store";
import { APP_HOMES, SupportedAppSlug } from "./apps";

interface AppConfigurationTabsProps {
  children: React.ReactNode;
}

// Extend the app manifest type to include the slug
interface AppManifestWithSlug {
  app: {
    name: string;
    icons?: {
      large: string;
      small: string;
    };
    category: string;
    description: string;
    supported_locales: string[];
    slug?: string;
  };
}

interface ExtendedApp {
  appId: string;
  appManifest: AppManifestWithSlug;
  // ... other properties
}

export function AppConfigurationTabs({ children }: AppConfigurationTabsProps) {
  const app = useAppStudioStore((state) => {
    const selectedApp = state.selectedApp;
    if (!selectedApp) return null;

    // Type guard to ensure the app has the expected structure
    const hasSlug = selectedApp.appManifest?.app?.slug;
    if (!hasSlug) return null;

    return selectedApp as ExtendedApp;
  });

  // Check if the app's slug is in our supported apps list
  if (!app?.appManifest.app.slug || !(app.appManifest.app.slug in APP_HOMES)) {
    return children;
  }

  const AppHome = APP_HOMES[app.appManifest.app.slug as SupportedAppSlug];

  return (
    <Tabs defaultValue="app-home" className="w-full">
      <TabsList className="w-full justify-start bg-background border-b rounded-none p-0 shadow-none h-min">
        <TabsTrigger
          value="app-home"
          className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b-[2px] data-[state=active]:border-b-primary py-3"
        >
          App Home
        </TabsTrigger>
        <TabsTrigger
          value="thena-config"
          className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b-[2px] data-[state=active]:border-b-primary py-3"
        >
          Thena Config
        </TabsTrigger>
      </TabsList>

      <TabsContent value="app-home" className="mt-6">
        <AppHome />
      </TabsContent>

      <TabsContent value="thena-config" className="mt-6">
        {children}
      </TabsContent>
    </Tabs>
  );
}
