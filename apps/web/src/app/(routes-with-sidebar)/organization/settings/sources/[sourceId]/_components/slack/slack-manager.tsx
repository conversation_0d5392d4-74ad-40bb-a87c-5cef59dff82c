import { SourceDetails } from "@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/sources/slack/page";
import { Separator } from "@/components/ui/separator";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { User } from "@/types/global";
import { Team } from "@/types/team";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import AddTeamDialog from "./slack-add-team-dialog";
import SlackConnectedWorkspacesView from "./slack-connected-workspaces-view";
import SlackDisconnectWorkspaceDialog from "./slack-disconnect-workspace-dialog";
import SlackNotConnectedView from "./slack-not-connected-view";
import SlackRemoveTeamDialog from "./slack-remove-team-dialog";
import SlackTeamAccessSection from "./slack-team-access-section";
interface SlackManagerProps {
  sourceDetails: SourceDetails;
  setSourceDetails: (sourceDetails: SourceDetails) => void;
  sourceId: string;
  currentUser: User;
  teams: Team[];
}

const SlackManager = (props: SlackManagerProps) => {
  const { sourceDetails, setSourceDetails, sourceId, currentUser, teams } =
    props;
  const [workspaceToDisconnect, setWorkspaceToDisconnect] = useState<
    string | null
  >(null);
  const [teamToRemove, setTeamToRemove] = useState<string | null>(null);
  const [addTeamDialogOpen, setAddTeamDialogOpen] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState<string | null>(null);
  const { mutate: addBotToTeam, error: _addBotToTeamError } = useApiMutation(
    `/apps/add-app-to-teams`,
    {},
    "POST",
    "apps-platform",
  );

  const { mutate: removeBotFromTeam, error: _removeBotFromTeamError } =
    useApiMutation(`/apps/remove-app-from-teams`, {}, "POST", "apps-platform");
  const [currentInstallationId, setCurrentInstallationId] = useState<
    string | null
  >(null);

  const handleAddTeam = async (workspaces: string[]) => {
    try {
      if (!currentInstallationId) {
        toast.error("Installation ID not found");
        return;
      }
      // Add bot to team first
      await addBotToTeam({
        teamIds: [selectedTeamId],
        installationId: currentInstallationId,
      });

      // Check for errors
      if (_addBotToTeamError) {
        throw new Error(
          `Failed to add bot to team, ${
            (_addBotToTeamError as Error)?.message ??
            "Unknown error please try again."
          }`,
        );
      }

      // Then make the team connection with proper headers
      const response = await fetch(
        `/api/workspace/sources/${sourceId}/slack/teams/${selectedTeamId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-auth-token": sourceDetails.key,
            "x-current-user": currentUser.uid,
          },
          body: JSON.stringify({ workspaces }),
        },
      );

      if (!response.ok) throw new Error("Failed to add team");

      // Update local state
      const teamToAdd = teams.find((t) => t.uid === selectedTeamId);
      if (!teamToAdd) return;

      setSourceDetails({
        ...sourceDetails,
        teamIds: [...sourceDetails.teamIds, selectedTeamId],
        teamConnections: [
          ...sourceDetails.teamConnections,
          {
            id: selectedTeamId,
            name: teamToAdd.name,
            connectedAt: new Date().toISOString(),
            connectedBy: {
              name: currentUser.name,
              email: currentUser.email,
            },
            channels: 0,
            workspaces,
          },
        ],
      });

      toast.success("Team added successfully.");
    } catch (error) {
      console.error("Error adding team:", error);
      toast.error("Failed to add team.");
    }
  };

  const handleRemoveTeam = async (teamId: string) => {
    try {
      if (!currentInstallationId) {
        toast.error("Installation ID not found");
        return;
      }
      // Remove bot from team first
      await removeBotFromTeam({
        teamIds: [teamId],
        installationId: currentInstallationId,
      });

      // Check for errors
      if (_removeBotFromTeamError) {
        throw new Error(
          `Failed to remove bot from team, ${
            (_removeBotFromTeamError as Error)?.message ??
            "Unknown error please try again."
          }`,
        );
      }
      // Then make the team connection with proper headers
      const response = await fetch(
        `/api/workspace/sources/${sourceId}/slack/teams/${teamId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            "x-auth-token": sourceDetails.key,
            "x-current-user": currentUser.uid,
          },
          body: JSON.stringify({
            workspaces: sourceDetails.connections.flatMap((c) => c.teamId),
          }),
        },
      );
      if (!response.ok) throw new Error("Failed to remove team");

      // Update local state
      setSourceDetails({
        ...sourceDetails,
        teamIds: sourceDetails.teamIds.filter((t) => t !== teamId),
        teamConnections: sourceDetails.teamConnections.filter(
          (t) => t.id !== teamId,
        ),
      });

      toast.success("Team removed successfully.");
    } catch (error) {
      console.error("Error removing team:", error);
      toast.error("Failed to remove team.");
    }
  };

  // Get all sources
  const getAllSources = async () => {
    try {
      const sourcesResponse = await fetch("/api/workspace/sources");
      const sourcesData = await sourcesResponse.json();
      setCurrentInstallationId(
        sourcesData.sources.find((s: SourceDetails) => s.id === sourceId)
          ?.installationId,
      );
    } catch (error) {
      console.error("Error fetching sources:", error);
    }
  };

  useEffect(() => {
    getAllSources();
  }, []);

  if (!sourceDetails?.isConnected) {
    return (
      <SlackNotConnectedView
        sourceDetails={sourceDetails}
        sourceName={sourceDetails.name}
      />
    );
  }
  return (
    <div className="max-w-[640px] mx-auto">
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-medium">{sourceDetails.name}</h1>
          <p className="text-sm text-muted-foreground mt-1">
            Connected to {sourceDetails.connections.length} workspace
            {sourceDetails.connections.length !== 1 ? "s" : ""}
          </p>
        </div>

        {/* Connected Workspaces Section */}
        <SlackConnectedWorkspacesView sourceDetails={sourceDetails} />

        <Separator />

        {/* Team Access Section */}
        <SlackTeamAccessSection
          sourceDetails={sourceDetails}
          setAddTeamDialogOpen={setAddTeamDialogOpen}
          setSelectedTeamId={setSelectedTeamId}
          teams={teams}
          setTeamToRemove={setTeamToRemove}
          setSourceDetails={setSourceDetails}
          sourceId={sourceId}
        />
        <SlackRemoveTeamDialog
          teamToRemove={teamToRemove}
          setTeamToRemove={setTeamToRemove}
          handleRemoveTeam={async (teamId: string) => {
            await handleRemoveTeam(teamId);
            setTeamToRemove(null);
          }}
        />
        <SlackDisconnectWorkspaceDialog
          workspaceToDisconnect={workspaceToDisconnect}
          setWorkspaceToDisconnect={setWorkspaceToDisconnect}
          sourceId={sourceId}
          setSourceDetails={setSourceDetails}
        />

        {/* Add Team Dialog */}
        <AddTeamDialog
          open={addTeamDialogOpen}
          onOpenChange={setAddTeamDialogOpen}
          sourceDetails={sourceDetails}
          onConfirm={async (workspaces) => {
            await handleAddTeam(workspaces);
          }}
        />
      </div>
    </div>
  );
};

export default SlackManager;
