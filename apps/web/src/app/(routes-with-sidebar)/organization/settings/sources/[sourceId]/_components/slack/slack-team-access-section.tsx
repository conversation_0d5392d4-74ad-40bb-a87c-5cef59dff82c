import type { SourceDetails } from "@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/sources/slack/page";
import {
  HeroIcon,
  type HeroIconColor,
  type HeroIconName,
} from "@/components/hero-icon";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Team } from "@/types/team";
import { ArrowUpRight, MoreVertical, Plus, Trash2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { toast } from "sonner";
interface SlackTeamAccessSectionProps {
  sourceDetails: SourceDetails;
  setAddTeamDialogOpen: (open: boolean) => void;
  setSelectedTeamId: (teamId: string) => void;
  teams: Team[];
  setTeamToRemove: (teamId: string) => void;
  setSourceDetails: (sourceDetails) => void;
  sourceId: string;
}
const SlackTeamAccessSection = (props: SlackTeamAccessSectionProps) => {
  const {
    sourceDetails,
    setAddTeamDialogOpen,
    setSelectedTeamId,
    teams,
    setTeamToRemove,
    setSourceDetails,
    sourceId,
  } = props;

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Team access</h3>
        {sourceDetails.teamConnections.length > 0 && (
          <Select
            onValueChange={(teamId) => {
              setSelectedTeamId(teamId);
              setAddTeamDialogOpen(true);
            }}
          >
            <SelectTrigger className="w-auto min-w-[132px] gap-2">
              <SelectValue placeholder="Add to team" />
            </SelectTrigger>
            <SelectContent>
              {(() => {
                const availableTeams = teams.filter(
                  (team) => !sourceDetails.teamIds.includes(team.uid),
                );

                if (availableTeams.length === 0) {
                  return (
                    <Link
                      href="/organization/settings/new-team"
                      className="flex items-center gap-2 px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                    >
                      <Plus className="w-4 h-4" />
                      Create team
                    </Link>
                  );
                }

                return availableTeams.map((team) => (
                  <SelectItem className="p-2" key={team.uid} value={team.uid}>
                    <div className="flex justify-between items-center gap-2">
                      <HeroIcon
                        name={team.icon as HeroIconName}
                        color={team.color as HeroIconColor}
                        className="w-4 h-4"
                      />
                      <span>{team.name}</span>
                    </div>
                  </SelectItem>
                ));
              })()}
            </SelectContent>
          </Select>
        )}
      </div>

      <div className="grid gap-4">
        {sourceDetails.teamConnections.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 rounded-sm border border-dashed text-center">
            <div className="bg-muted/50 rounded-full p-3 mb-4">
              <Plus className="w-6 h-6 text-muted-foreground" />
            </div>
            <h4 className="text-base font-medium mb-2">No teams connected</h4>
            <p className="text-sm text-muted-foreground mb-4 max-w-md">
              Select the teams you want to give access to the connected
              workspaces
            </p>
            <Select
              onValueChange={(teamId) => {
                setSelectedTeamId(teamId);
                setAddTeamDialogOpen(true);
              }}
            >
              <SelectTrigger className="w-auto min-w-[150px]">
                <SelectValue placeholder="Select a team" />
              </SelectTrigger>
              <SelectContent>
                {teams
                  .filter((team) => !sourceDetails.teamIds.includes(team.uid))
                  .map((team) => (
                    <SelectItem key={team.uid} value={team.uid}>
                      <div className="flex items-center gap-2">
                        <HeroIcon
                          name={team.icon as HeroIconName}
                          color={team.color as HeroIconColor}
                          className="w-4 h-4"
                        />
                        <span>{team.name}</span>
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        ) : (
          sourceDetails.teamConnections
            .filter((team) => teams.find((t) => t.uid === team.id))
            .map((team) => {
              const teamData = teams.find((t) => t.uid === team.id);

              return (
                <div
                  key={team.id}
                  className="space-y-4 p-4 rounded-sm border shadow-none group hover:border-muted-foreground/20 transition-colors bg-card"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex items-center justify-center w-8 h-8">
                          <HeroIcon
                            name={teamData?.icon as HeroIconName}
                            color={teamData?.color as HeroIconColor}
                            className="w-4 h-4"
                          />
                        </div>
                        <p className="font-medium text-base">
                          {teams.find((t) => t.uid === team.id)?.name}
                        </p>
                      </div>
                      <div className="ml-1 space-y-2">
                        {(() => {
                          const teamConnection =
                            sourceDetails.teamConnections.find(
                              (tc) => tc.id === team.id,
                            );
                          if (!teamConnection) return null;
                          return (
                            <div className="flex items-center gap-3 text-sm text-muted-foreground">
                              <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                              <p>
                                {new Date(
                                  teamConnection.connectedAt,
                                ).toLocaleDateString()}
                              </p>
                              <span>•</span>
                              <p>by {teamConnection.connectedBy.name}</p>
                              <span>•</span>
                              <span className="px-2 py-0.5 rounded-[4px] text-xs bg-blue-100 dark:bg-blue-950 text-blue-700 dark:text-blue-300 font-medium">
                                {teamConnection.channels} channels
                              </span>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Link
                        href={`/dashboard/${team.id}/settings/sources/slack`}
                      >
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-sm gap-2 h-7"
                        >
                          Team controls
                          <ArrowUpRight className="w-3.5 h-3.5" />
                        </Button>
                      </Link>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-muted-foreground hover:text-foreground"
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => setTeamToRemove(team.id)}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Remove team
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium whitespace-nowrap">
                        Connected workspaces:
                      </p>
                      <div className="flex items-center flex-wrap gap-2">
                        {(() => {
                          const teamConnection =
                            sourceDetails.teamConnections.find(
                              (tc) => tc.id === team.id,
                            );
                          if (!teamConnection) return null;

                          return teamConnection.workspaces.map(
                            (workspaceName) => {
                              const connection = sourceDetails.connections.find(
                                (c) => c.workspaceName === workspaceName,
                              );
                              if (!connection) return null;

                              return (
                                <div
                                  key={connection.workspaceName}
                                  className="flex items-center gap-2 px-2 py-1 rounded-sm bg-secondary text-sm"
                                >
                                  <Image
                                    src={connection.workspaceIcon}
                                    alt={connection.workspaceName}
                                    width={16}
                                    height={16}
                                    className="rounded"
                                    unoptimized
                                  />
                                  <span>{connection.workspaceName}</span>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-4 w-4 p-0 hover:text-foreground"
                                      >
                                        <MoreVertical className="h-3 w-3" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem
                                        className="text-destructive focus:text-destructive"
                                        onClick={async () => {
                                          try {
                                            const response = await fetch(
                                              `/api/workspace/sources/${sourceId}/slack/teams/${team.id}/workspaces`,
                                              {
                                                method: "DELETE",
                                                headers: {
                                                  "Content-Type":
                                                    "application/json",
                                                },
                                                body: JSON.stringify({
                                                  workspaceId:
                                                    connection.workspaceName,
                                                }),
                                              },
                                            );

                                            if (!response.ok)
                                              throw new Error(
                                                "Failed to remove workspace",
                                              );

                                            // Update local state after successful API call
                                            setSourceDetails((prev) => {
                                              if (!prev) return prev;
                                              return {
                                                ...prev,
                                                teamConnections:
                                                  prev.teamConnections.map(
                                                    (tc) => {
                                                      if (tc.id === team.id) {
                                                        return {
                                                          ...tc,
                                                          workspaces:
                                                            tc.workspaces.filter(
                                                              (w) =>
                                                                w !==
                                                                connection.workspaceName,
                                                            ),
                                                        };
                                                      }
                                                      return tc;
                                                    },
                                                  ),
                                              };
                                            });

                                            toast.success(
                                              "Workspace removed successfully.",
                                            );
                                          } catch (error) {
                                            console.error(
                                              "Error removing workspace:",
                                              error,
                                            );
                                            toast.error(
                                              "Failed to remove workspace.",
                                            );
                                          }
                                        }}
                                      >
                                        <Trash2 className="h-3 w-3 mr-2" />
                                        Remove
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              );
                            },
                          );
                        })()}
                      </div>
                      {(() => {
                        const teamConnection =
                          sourceDetails.teamConnections.find(
                            (tc) => tc.id === team.id,
                          );
                        const availableWorkspaces =
                          sourceDetails.connections.filter((connection) => {
                            return !teamConnection?.workspaces.includes(
                              connection.workspaceName,
                            );
                          });

                        if (availableWorkspaces.length === 0) return null;

                        return (
                          <Select
                            onValueChange={async (workspaceId) => {
                              try {
                                const response = await fetch(
                                  `/api/workspace/sources/${sourceId}/slack/teams/${team.id}/workspaces`,
                                  {
                                    method: "POST",
                                    headers: {
                                      "Content-Type": "application/json",
                                    },
                                    body: JSON.stringify({ workspaceId }),
                                  },
                                );

                                if (!response.ok)
                                  throw new Error("Failed to add workspace");

                                // Update local state after successful API call
                                setSourceDetails((prev: SourceDetails) => {
                                  if (!prev) return prev;
                                  return {
                                    ...prev,
                                    teamConnections: prev.teamConnections.map(
                                      (tc) => {
                                        if (tc.id === team.id) {
                                          return {
                                            ...tc,
                                            workspaces: [
                                              ...tc.workspaces,
                                              workspaceId,
                                            ],
                                          };
                                        }
                                        return tc;
                                      },
                                    ),
                                  };
                                });

                                toast.success("Workspace added successfully.");
                              } catch (error) {
                                console.error("Error adding workspace:", error);
                                toast.error("Failed to add workspace.");
                              }
                            }}
                            value=""
                          >
                            <SelectTrigger className="w-[180px]">
                              <SelectValue placeholder="Select a workspace" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableWorkspaces.map((workspace) => (
                                <SelectItem
                                  key={workspace.workspaceName}
                                  value={workspace.workspaceName}
                                  className="cursor-pointer"
                                >
                                  <div className="flex items-center gap-2">
                                    <Image
                                      src={workspace.workspaceIcon}
                                      alt={workspace.workspaceName}
                                      width={16}
                                      height={16}
                                      className="rounded"
                                      unoptimized
                                    />
                                    <span>{workspace.workspaceName}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              );
            })
        )}
      </div>
    </div>
  );
};

export default SlackTeamAccessSection;
