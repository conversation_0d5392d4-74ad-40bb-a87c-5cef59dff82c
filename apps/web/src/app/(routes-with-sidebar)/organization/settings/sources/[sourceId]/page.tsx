"use client";

import { SourceDetails } from "@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/sources/slack/page";
import Thena<PERSON>oader from "@/components/thena-loader";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { SourceTypes } from "@/types/sources";
import { redirect, useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import MsTeamsManager from "./_components/ms-teams/ms-teams-manager";
import SlackManager from "./_components/slack/slack-manager";
export default function ManageSource() {
  const params = useParams();
  const { teams } = useTicketMetaStore();
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const [refetchSourceDetails, setRefetchSourceDetails] = useState(false);
  const searchParams = useSearchParams();
  const sourceId = params.sourceId as string;
  const botUserId = searchParams.get("botUserId");
  const type = searchParams.get("type") as SourceTypes;

  // States
  const [loading, setLoading] = useState(true);

  const [sourceDetails, setSourceDetails] = useState<SourceDetails | null>(
    null,
  );

  if (!botUserId) {
    redirect("/workspace/settings/sources");
  }
  const fetchAndSetSourceDetails = async () => {
    try {
      const response = await fetch(
        `/api/workspace/sources/${sourceId}?botUserId=${botUserId}&type=${type}`,
      );
      if (!response.ok) throw new Error("Failed to fetch source details");
      const data = await response.json();
      setSourceDetails(data);
      return data;
    } catch (_error) {
      toast.error("Failed to load source details.");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const data = await fetchAndSetSourceDetails();
      setSourceDetails(data);
    };
    fetchData();
  }, [sourceId, botUserId, type, refetchSourceDetails]);

  if (loading) {
    return (
      <div className="container mx-auto pt-[56px] pb-6 h-full flex items-center justify-center min-h-[640px]">
        <ThenaLoader loaderText="Loading source details..." />
      </div>
    );
  }

  const renderSourceViewBasedOnType = () => {
    switch (type) {
      case "slack":
        return (
          <SlackManager
            sourceDetails={sourceDetails}
            setSourceDetails={setSourceDetails}
            sourceId={sourceId}
            currentUser={currentUser}
            teams={teams}
          />
        );
      case "ms_teams":
        return (
          <MsTeamsManager
            sourceDetails={sourceDetails}
            setSourceDetails={setSourceDetails}
            sourceId={sourceId}
            currentUser={currentUser}
            teams={teams}
            setRefetchSourceDetails={setRefetchSourceDetails}
          />
        );
      default:
        return null;
    }
  };
  return (
    <div className="container mx-auto pt-[56px] pb-6 h-full overflow-y-auto">
      {renderSourceViewBasedOnType()}
    </div>
  );
}
