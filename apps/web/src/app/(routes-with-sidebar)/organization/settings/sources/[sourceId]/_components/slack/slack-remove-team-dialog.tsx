import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
interface SlackRemoveTeamDialogProps {
  teamToRemove: string | null;
  setTeamToRemove: (teamToRemove: string | null) => void;

  handleRemoveTeam: (teamId: string) => Promise<void>;
}
const SlackRemoveTeamDialog = (props: SlackRemoveTeamDialogProps) => {
  const { teamToRemove, setTeamToRemove, handleRemoveTeam } = props;

  return (
    <Dialog open={!!teamToRemove} onOpenChange={() => setTeamToRemove(null)}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Remove team access?</DialogTitle>
          <DialogDescription>
            Are you sure you want to remove this team&apos;s access to the Slack
            integration? This will remove their access to all connected
            workspaces.
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <Button variant="ghost" onClick={() => setTeamToRemove(null)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={() => {
              handleRemoveTeam(teamToRemove);
            }}
          >
            Remove access
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SlackRemoveTeamDialog;
