import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Dispatch, SetStateAction } from "react";
import { toast } from "sonner";

interface SlackDisconnectWorkspaceDialogProps {
  workspaceToDisconnect: string | null;
  setWorkspaceToDisconnect: Dispatch<SetStateAction<string>>;
  sourceId: string;
  setSourceDetails: (sourceDetails) => void;
}
const SlackDisconnectWorkspaceDialog = (
  props: SlackDisconnectWorkspaceDialogProps,
) => {
  const {
    workspaceToDisconnect,
    setWorkspaceToDisconnect,
    sourceId,
    setSourceDetails,
  } = props;
  {
    /* Disconnect Workspace Dialog */
  }
  return (
    <Dialog
      open={!!workspaceToDisconnect}
      onOpenChange={() => setWorkspaceToDisconnect(null)}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Disconnect workspace?</DialogTitle>
          <DialogDescription>
            Are you sure you want to disconnect this workspace? This will remove
            access for all teams that use this workspace.
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end gap-3 mt-4">
          <Button
            variant="ghost"
            onClick={() => setWorkspaceToDisconnect(null)}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={async () => {
              if (workspaceToDisconnect) {
                try {
                  const response = await fetch(
                    `/api/workspace/sources/${sourceId}/slack/workspaces/${workspaceToDisconnect}`,
                    { method: "DELETE" },
                  );

                  if (!response.ok)
                    throw new Error("Failed to disconnect workspace");

                  // Update local state
                  setSourceDetails((prev) => {
                    if (!prev) return prev;
                    return {
                      ...prev,
                      connections: prev.connections.filter(
                        (c) => c.workspaceName !== workspaceToDisconnect,
                      ),
                    };
                  });

                  toast.success("Workspace disconnected successfully.");
                } catch (error) {
                  console.error("Error disconnecting workspace:", error);
                  toast.error("Failed to disconnect workspace.");
                } finally {
                  setWorkspaceToDisconnect(null);
                }
              }
            }}
          >
            Disconnect
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SlackDisconnectWorkspaceDialog;
