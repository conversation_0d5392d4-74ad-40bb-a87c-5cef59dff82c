import { SourceDetails } from "@/app/(routes-with-sidebar)/dashboard/[teamId]/settings/sources/slack/page";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical, RefreshCw, Trash2 } from "lucide-react";
import Image from "next/image";
import { toast } from "sonner";
import { THENA_SLACK_APP_URL } from "../../../../../../../../config/constant";
import { ConnectWorkspaceButton } from "../connect-workspace-button";

interface SlackConnectedWorkspacesViewProps {
  sourceDetails: SourceDetails;
}
const SlackConnectedWorkspacesView = (
  props: SlackConnectedWorkspacesViewProps,
) => {
  const { sourceDetails } = props;

  // Function to sync internal users
  const syncInternalUsers = async (authToken: string, teamId: string) => {
    try {
      // Make the API call to sync internal users
      const response = await fetch(
        `${THENA_SLACK_APP_URL}/v1/slack/sync/internal-users`,
        {
          method: "POST",
          headers: {
            "x-auth-token": authToken,
            "x-slack-id": teamId,
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to sync internal users");
      }

      toast.success("Successfully synced internal users");
    } catch (error) {
      console.error("Error syncing internal users:", error);
      toast.error(
        error instanceof Error
          ? `Failed to sync internal users: ${error.message}`
          : "Failed to sync internal users",
      );
    }
  };
  return (
    <>
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Connected workspaces</h3>
          <ConnectWorkspaceButton sourceDetails={sourceDetails} />
        </div>
        <div className="grid gap-4">
          {sourceDetails.connections.map((connection, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 rounded-sm border shadow-none group hover:border-muted-foreground/20 transition-colors bg-card"
            >
              <div className="flex items-start space-x-6">
                <div className="w-12 h-12 flex items-center justify-center">
                  <Image
                    src={connection.workspaceIcon}
                    alt={`${connection.workspaceName} icon`}
                    width={40}
                    height={40}
                    className="w-full h-full rounded-sm"
                    unoptimized
                  />
                </div>
                <div>
                  <p className="font-medium">{connection.workspaceName}</p>
                  <div className="flex items-center gap-3 mt-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                      <p>
                        Connected{" "}
                        {new Date(connection.connectedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <span>•</span>
                    <p>by {connection.connectedBy.name}</p>
                    {connection.channels && (
                      <>
                        <span>•</span>
                        <div className="flex items-center gap-2">
                          <span className="px-2 py-0.5 rounded-[4px] text-xs bg-blue-100 dark:bg-blue-950 text-blue-700 dark:text-blue-300 font-medium">
                            {connection.channels} channels
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground hover:text-foreground"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="text-destructive focus:text-destructive"
                    // onClick={() =>
                    //   setWorkspaceToDisconnect(connection.workspaceName)
                    // }
                    onClick={() => {
                      toast("Disconnect isn’t available yet—launching soon.");
                    }}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Disconnect workspace
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      syncInternalUsers(sourceDetails.key, connection.teamId);
                    }}
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Sync internal users
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default SlackConnectedWorkspacesView;
