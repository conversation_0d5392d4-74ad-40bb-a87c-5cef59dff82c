"use client";

import { uploadFile } from "@/app/actions/uploadFile";
import Avatar from "@/components/common/Avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useApiMutation } from "@/hooks/use-api-mutation";
import { UPDATE_USER_PERSONAL_SETTING } from "@/services/settings";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { ImageIcon, UploadIcon } from "lucide-react";
import { useRef, useState } from "react";
import { toast } from "sonner";
import { getOrgDetails } from "../../../../../../utils/browserUtils";

export default function GeneralSettingsPage() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { mutate } = useApiMutation(UPDATE_USER_PERSONAL_SETTING, {}, "PATCH");
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);

  const [formData, setFormData] = useState({
    name: currentUser?.name || "",
    email: currentUser?.email || "",
    profileImage: null as File | null,
    currentProfileImage: currentUser?.avatarUrl || "",
  });

  const [originalData, setOriginalData] = useState({
    name: currentUser?.name || "",
    email: currentUser?.email || "",
    currentProfileImage: currentUser?.avatarUrl,
  });

  const [hasChanges, setHasChanges] = useState({
    name: false,
    email: false,
    profileImage: false,
  });

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setHasChanges((prev) => ({
      ...prev,
      [field]: value !== originalData[field],
    }));
  };

  const handleImageChange = (file: File | null) => {
    // Check file size if file exists (10MB limit)
    if (file) {
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        toast.error(
          "Profile image must be less than 10MB. Please select a smaller file.",
        );
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        return;
      }
    }

    setFormData((prev) => ({ ...prev, profileImage: file }));
    setHasChanges((prev) => ({ ...prev, profileImage: true }));
  };
  const { orgUid } = getOrgDetails();
  const handleUpdate = async (field: keyof typeof formData) => {
    try {
      if (field === "profileImage" && formData.profileImage) {
        const formDataToUpload = new FormData();
        formDataToUpload.append("file", formData.profileImage);

        const result = await uploadFile(formDataToUpload, orgUid);

        if (result.urls?.publicUrl) {
          mutate({ avatarUrl: result.urls?.publicUrl }).then(() => {
            const updatedAllUserIds = currentUser?.allUserIds.map((user) =>
              user.uid === currentUser?.uid
                ? { ...user, avatarUrl: result.urls?.publicUrl }
                : user,
            );
            dispatch({
              type: "UPDATE_CURRENT_USER",
              payload: {
                avatarUrl: result.urls?.publicUrl,
                allUserIds: updatedAllUserIds,
              },
            });
          });
          setFormData((prev) => ({
            ...prev,
            currentProfileImage: result.urls?.publicUrl,
          }));
          setOriginalData((prev) => ({
            ...prev,
            currentProfileImage: result.urls?.publicUrl,
          }));
          setHasChanges((prev) => ({ ...prev, profileImage: false }));
        } else {
          console.log("Failed to upload image:", result.error);
        }
      } else {
        mutate({ name: formData.name });
        setHasChanges((prev) => ({ ...prev, [field]: false }));
        if (field !== "profileImage") {
          setOriginalData((prev) => ({ ...prev, [field]: formData[field] }));
          const updatedAllUserIds = currentUser?.allUserIds.map((user) =>
            user.uid === currentUser?.uid
              ? { ...user, name: formData["name"] }
              : user,
          );
          dispatch({
            type: "UPDATE_CURRENT_USER",
            payload: {
              name: formData["name"],
              allUserIds: updatedAllUserIds,
            },
          });
        }
      }
    } catch (error) {
      console.error("Error updating field:", error);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="flex-1 bg-background py-8 flex items-center justify-center w-[640px]">
        <div className="w-full pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-medium">Profile</h2>
              <p className="text-sm text-gray-500 pb-6">
                Manage your personal profile information and settings.
              </p>
            </div>
          </div>

          <form className="space-y-8">
            <div className="space-y-4">
              {/* Name Section */}
              <div className="w-full">
                <Label
                  htmlFor="name"
                  className="text-sm font-medium flex items-center gap-1"
                >
                  Your name
                  <span className="text-red-500">*</span>
                </Label>
                <div className="flex flex-col gap-2 mt-2">
                  <div className="flex gap-2 w-full">
                    <Input
                      id="name"
                      placeholder="Enter your name"
                      className="flex-1"
                      value={formData.name}
                      onChange={(e) =>
                        handleInputChange("name", e.target.value)
                      }
                    />
                    {hasChanges.name && (
                      <Button
                        onClick={() => handleUpdate("name")}
                        className="h-[40px]"
                      >
                        Update
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {/* Email Section */}
              <div>
                <Label htmlFor="email" className="text-sm font-medium">
                  Email ID <span className="text-red-500">*</span>
                </Label>
                <p className="text-sm text-gray-500 mb-2">
                  Your email address for communications and notifications.
                </p>
                <div className="flex gap-2">
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    disabled
                    value={formData.email}
                  />
                </div>
              </div>

              {/* Profile Image Section */}
              <div>
                <Label className="text-sm font-medium">Profile image</Label>
                <p className="text-sm text-gray-500 mb-2">
                  Upload your profile picture. Recommended size 512x512px.
                  Maximum file size is 10MB.
                </p>
                <div className="flex items-start gap-4">
                  <div className="w-16 h-16 rounded-sm border flex items-center justify-center bg-gray-50 relative">
                    {formData.currentProfileImage ||
                    formData.profileImage ||
                    currentUser?.avatarUrl ? (
                      <Avatar
                        src={
                          formData.profileImage
                            ? URL.createObjectURL(formData.profileImage)
                            : currentUser?.avatarUrl
                        }
                        imgClassnames="w-16 h-16"
                      ></Avatar>
                    ) : (
                      <div className="flex h-16 w-16 items-center justify-center">
                        <ImageIcon className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          handleImageChange(file);
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="h-[40px]"
                    >
                      <UploadIcon className="w-4 h-4 mr-2" />
                      Upload new image
                    </Button>
                    {hasChanges.profileImage && (
                      <Button
                        type="button"
                        onClick={() => handleUpdate("profileImage")}
                        className="h-[40px]"
                      >
                        Save image
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
