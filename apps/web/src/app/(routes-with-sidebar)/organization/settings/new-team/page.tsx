"use client";

import { IconName, IconPicker } from "@/components/icon-picker";
import TooltipWrapper from "@/components/tooltip-wrapper";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { ICON_COLORS } from "@/constants/icon-picker";
import { cn } from "@/lib/utils";
import { GET_ALL_TEAMS } from "@/services/kanban";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { IconColor } from "@/types/icon-picker";
import { generateIdentifier } from "@/utils/teamsUtils";
import * as HeroIcons from "@heroicons/react/24/solid";
import { kebabCase } from "lodash";
import { Globe2, Loader2, Lock } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export default function NewTeamPage() {
  const [formData, setFormData] = useState({
    name: "",
    identifier: "",
    visibility: "public",
    icon: "",
    iconColor: ICON_COLORS.purple,
  });
  const [errors, setErrors] = useState({
    name: "",
    identifier: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const [iconSearch, setIconSearch] = useState("");
  const filteredIcons = Object.keys(HeroIcons).filter((iconName) =>
    iconName.toLowerCase().includes(iconSearch.toLowerCase()),
  );
  const teamsList = useTicketMetaStore((state) => state.teams);
  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: "",
      identifier: "",
    };

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = "Team name is required";
      isValid = false;
    } else if (
      !/^[\p{L}\p{N}\p{Emoji}][\p{L}\p{N}\p{Emoji}\s\-_]*[\p{L}\p{N}\p{Emoji}]$/u.test(
        formData.name.trim(),
      )
    ) {
      newErrors.name =
        "Team name must start and end with a letter, and contain only letters, numbers, emojis, single spaces, hyphens, or underscores.";
      isValid = false;
    }

    // Identifier validation
    if (!formData.identifier.trim()) {
      newErrors.identifier = "Team identifier is required";
      isValid = false;
    } else if (formData.identifier.length < 1) {
      newErrors.identifier = "Identifier must be at least 1 character";
      isValid = false;
    } else if (formData.identifier.length > 7) {
      newErrors.identifier = "Identifier must not exceed 7 characters";
      isValid = false;
    } else if (!/^[A-Z]+$/.test(formData.identifier)) {
      newErrors.identifier = "Identifier must contain only capital letters";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const identifierExists = teamsList.some(
      (team) =>
        team.identifier.toLowerCase() ===
        formData.identifier.trim().toLowerCase(),
    );

    if (identifierExists) {
      setErrors((prev) => ({
        ...prev,
        identifier: "A team with this identifier already exists",
      }));
      return;
    }
    setIsLoading(true);
    const body = {
      name: formData.name.trim(),
      identifier: formData.identifier.trim(),
      isPrivate: formData.visibility === "private",
      icon: formData.icon,
      color: formData.iconColor,
    };
    try {
      const response = await fetch("/api/teams", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      const data = await response.json();
      if (!response.ok) {
        // Handle validation errors from the server
        if (Array.isArray(data)) {
          const newErrors = { ...errors };
          data.forEach((error) => {
            if (error.property in newErrors) {
              newErrors[error.property as keyof typeof newErrors] =
                error.constraints.matches;
            }
          });
          setErrors(newErrors);
          return;
        }
        toast.error(data?.error || "Failed to create team");
        return;
      }
      const userId = useGlobalConfigPersistStore.getState().currentUser.uid;
      const teamsList = await fetch(GET_ALL_TEAMS(userId), {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const teamsData = await teamsList.json();
      useTicketMetaStore.getState().setAllTeams([...teamsData]);
      router.push(`/dashboard/${data.data?.teamId}`);
    } catch (error) {
      console.error("Error creating team:", error);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="flex-1 bg-background py-14 flex items-center justify-center w-[640px]">
        <div className="w-full">
          <div className="mb-6">
            <h2 className="text-2xl font-medium">Create new team</h2>
            <p className="text-sm text-muted-text mt-0.5">
              Teams help you organize configurations, workflows, and
              notifications independently
            </p>
          </div>

          <form className="space-y-8 mt-4" onSubmit={handleSubmit} noValidate>
            <div className="space-y-4">
              <div className="w-full">
                <Label
                  htmlFor="team-name"
                  className="text-sm font-medium flex items-center gap-1"
                >
                  Team icon and name
                  <span className="text-red-500">*</span>
                </Label>
                <div className="flex flex-col gap-2 mt-2">
                  <div className="flex gap-2 w-full">
                    <div className="flex gap-2 flex-1">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            className="w-[36px] h-[36px] p-0 flex-shrink-0 hover:bg-color-bg-subtle"
                            aria-label="Select icon"
                            style={{
                              borderRadius: "4px",
                              backgroundColor: formData.iconColor
                                ? formData.iconColor
                                    .replace("rgb", "rgba")
                                    .replace(")", ", 0.2)")
                                : ICON_COLORS.purple
                                    .replace("rgb", "rgba")
                                    .replace(")", ", 0.2)"),
                            }}
                          >
                            <IconPicker
                              iconColor={formData.iconColor}
                              name={formData.icon as IconName}
                            />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[320px] p-3 h-[400px] overflow-y-auto">
                          <div className="space-y-3 h-full">
                            <div>
                              <div className="flex flex-wrap gap-2 mb-3">
                                {Object.keys(ICON_COLORS).map((color) => (
                                  <Button
                                    key={color}
                                    style={{
                                      backgroundColor:
                                        ICON_COLORS[color as IconColor],
                                    }}
                                    className={cn(
                                      "h-[20px] w-[20px] p-0",
                                      formData.iconColor === color &&
                                        "border-primary",
                                    )}
                                    onClick={() => {
                                      // @ts-expect-error fix types
                                      setFormData((prev) => ({
                                        ...prev,
                                        iconColor:
                                          ICON_COLORS[color as IconColor],
                                      }));
                                    }}
                                  >
                                    <div
                                      className={cn(
                                        "w-4 h-4 rounded-full",
                                        ICON_COLORS[color as IconColor],
                                      )}
                                    />
                                  </Button>
                                ))}
                              </div>
                              <Separator className="my-1" />
                              <Input
                                placeholder="Search icons..."
                                value={iconSearch}
                                onChange={(e) => setIconSearch(e.target.value)}
                                className="h-7 my-3"
                              />
                              <div className="grid grid-cols-8 gap-2 mt-3">
                                {filteredIcons.map((iconName) => (
                                  <TooltipWrapper
                                    key={iconName}
                                    tooltipContent={kebabCase(iconName)}
                                    asChild
                                  >
                                    <Button
                                      key={iconName}
                                      variant="ghost"
                                      className="h-[36px] w-[36px] p-0"
                                      onClick={() => {
                                        setFormData((prev) => ({
                                          ...prev,
                                          icon: iconName,
                                        }));
                                      }}
                                    >
                                      <IconPicker
                                        iconColor={formData.iconColor}
                                        name={iconName as IconName}
                                        showColor={false}
                                      />
                                    </Button>
                                  </TooltipWrapper>
                                ))}
                              </div>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                      <Input
                        id="team-name"
                        placeholder="E.g - Customer support"
                        value={formData.name}
                        onChange={(e) => {
                          const newName = e.target.value;
                          setFormData((prev) => ({
                            ...prev,
                            name: newName,
                            identifier:
                              generateIdentifier(newName) || prev.identifier,
                          }));
                          setErrors((prev) => ({ ...prev, name: "" }));
                        }}
                        className={errors.name ? "border-red-500" : ""}
                        disabled={isLoading}
                        required
                        aria-invalid={!!errors.name}
                        aria-describedby={
                          errors.name ? "name-error" : undefined
                        }
                      />
                    </div>
                  </div>
                  {errors.name && (
                    <p id="name-error" className="text-sm text-red-500">
                      {errors.name}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="identifier" className="text-sm font-medium">
                  Identifier <span className="text-red-500">*</span>
                </Label>
                <p className="text-sm text-muted-text mb-2">
                  Create a unique ID to reference tickets for this team.
                </p>
                <div
                  className={cn(
                    "flex gap-2",
                    errors.identifier ? "flex-col" : "",
                  )}
                >
                  <Input
                    id="identifier"
                    maxLength={7}
                    placeholder="E.g - SUPPORT"
                    value={formData.identifier}
                    onChange={(e) => {
                      const upperValue = e.target.value.toUpperCase();
                      setFormData((prev) => ({
                        ...prev,
                        identifier: upperValue,
                      }));
                      setErrors((prev) => ({ ...prev, identifier: "" }));
                    }}
                    className={errors.identifier ? "border-red-500" : ""}
                    aria-invalid={!!errors.identifier}
                    aria-describedby={
                      errors.identifier ? "identifier-error" : undefined
                    }
                  />
                  {errors.identifier && (
                    <p
                      id="identifier-error"
                      className="text-sm text-red-500 mt-1"
                    >
                      {errors.identifier}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">
                  Set team visibility
                </Label>
                <div className="text-sm text-muted-text mb-4">
                  <ul className="list-disc ml-4">
                    <li>
                      Public: Accessible to everyone in your organization.
                    </li>
                    <li>Private: Only accessible to invited members.</li>
                  </ul>
                </div>
                <RadioGroup
                  value={formData.visibility}
                  className="grid grid-cols-2 gap-4"
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, visibility: value }))
                  }
                >
                  <Label
                    htmlFor="public"
                    className={cn(
                      "cursor-pointer flex items-center border rounded-sm px-4 py-3 flex-row-reverse justify-between hover:bg-accent transition-colors",
                      formData.visibility === "public" &&
                        "border border-primary",
                    )}
                  >
                    <RadioGroupItem
                      value="public"
                      id="public"
                      className="data-[state=checked]:border-primary"
                    />
                    <span className="font-normal flex items-center gap-2">
                      <Globe2 size={20} className="text-muted-foreground" />
                      Public
                    </span>
                  </Label>
                  <Label
                    htmlFor="private"
                    className={cn(
                      "cursor-pointer flex items-center border rounded-sm px-4 py-3 flex-row-reverse justify-between hover:bg-accent transition-colors",
                      formData.visibility === "private" &&
                        "border border-primary",
                    )}
                  >
                    <RadioGroupItem
                      value="private"
                      id="private"
                      className="data-[state=checked]:border-primary"
                    />
                    <span className="font-normal flex items-center gap-2">
                      <Lock size={20} className="text-muted-foreground" />
                      Private
                    </span>
                  </Label>
                </RadioGroup>
              </div>
            </div>

            <div className="flex justify-start">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Creating...
                  </>
                ) : (
                  "Create team"
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
