"use client";

import { AuthLayout } from "@/components/auth-layout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { validatePassword } from "@/utils/onboardingUtils";
import { Eye, EyeOff, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { setOrgDetails } from "../../utils/browserUtils";
import {
  getCurrentUser,
  refreshSession,
  updateSignupProgress,
  updateUserPassword,
} from "../actions/auth";

export default function Page() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.hash.substring(1)); // Remove the leading #
    const refreshToken = urlParams.get("refresh_token");
    if (refreshToken) {
      refreshSession(refreshToken);
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      // Password validation
      if (password !== confirmPassword) {
        toast.error("Passwords do not match");
        setIsLoading(false);
        return;
      }

      // Validate password before submission
      const passwordValidationError = validatePassword(password);
      if (passwordValidationError) {
        toast.error(passwordValidationError);
        setIsLoading(false);
        return;
      }

      const currentUser = await getCurrentUser();
      // Validate user data
      if (!currentUser?.email) {
        toast.error("User email is missing");
        setIsLoading(false);
        return;
      }

      const requestBody = {
        orgId: currentUser.user_metadata.invitedOrgId,
        inviteeEmail: currentUser.email, // Ensure we're using the email from currentUser
        invitingUserId: currentUser.user_metadata.invitingUserId,
      };
      await updateSignupProgress({
        signupComplete: true,
      });
      const result = await updateUserPassword(password);
      if (result?.error) {
        toast.error(
          typeof result.error === "string"
            ? result.error
            : result.error.message,
        );
        return;
      }

      const response = await fetch("/api/organizations/join", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("API error response:", errorData);
        throw new Error(errorData.message || "Failed to join organization");
      }
      const res = await response.json();
      setOrgDetails(
        String(res.data.organizationId),
        String(res.data.organizationUid),
      );

      // Get search settings and store them
      try {
        const searchSettingsResponse = await fetch(
          `/api/search/settings?orgId=${res.data.organizationId}`,
        );
        if (searchSettingsResponse.ok) {
          const searchSettings = await searchSettingsResponse.json();
          dispatch({
            type: "SET_SEARCH_KEYS",
            payload: {
              ticketsAndComments: searchSettings.ticketsAndComments,
              accounts: searchSettings.accounts,
              teams: searchSettings.teams,
            },
          });
        }
      } catch (error) {
        console.error("Error fetching search settings:", error);
      }

      if (currentUser.user_metadata.invite_team_id) {
        const response = await fetch(
          `/api/teams/${currentUser.user_metadata.invite_team_id}/members`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "x-org-uid": res.data.organizationUid,
            },
            body: JSON.stringify({ email: currentUser.email }),
          },
        );

        if (!response.ok) {
          throw new Error("Failed to add member");
        }
      }
      router.push("/dashboard");
    } catch (err) {
      console.error("Error in handleSubmit:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout>
      {/* Password Container */}
      <div className="relative w-[496px] px-4">
        <Card className="w-[496px] p-8 bg-background border border-border rounded-sm shadow-none">
          <CardHeader className="p-0 space-y-2 mb-6">
            <CardTitle className="text-2xl font-semibold text-foreground">
              Set your password
            </CardTitle>
            <CardDescription className="text-muted-foreground text-sm">
              Choose a strong password to secure your account
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit}>
            <CardContent className="p-0 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password" className="text-foreground">
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    placeholder="Enter your password"
                    className="w-full px-4 py-3 bg-background border border-border rounded-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-[var(--brand-gradient-start)]/50 focus:border-[var(--brand-gradient-start)] transition-all"
                  />

                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 flex items-center pr-4 text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-foreground">
                  Confirm password
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    placeholder="Confirm your password"
                    className="w-full px-4 py-3 bg-background border border-border rounded-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-[var(--brand-gradient-start)]/50 focus:border-[var(--brand-gradient-start)] transition-all"
                  />

                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute inset-y-0 right-0 flex items-center pr-4 text-muted-foreground hover:text-foreground"
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="p-0 pt-4">
              <Button
                type="submit"
                className="w-full py-3 bg-gradient-to-b from-[var(--brand-gradient-start)] to-[var(--brand-gradient-end)] hover:from-[var(--brand-gradient-hover-start)] hover:to-[var(--brand-gradient-hover-end)] text-white transition-all rounded-sm font-medium focus:outline-none focus:ring-2 focus:ring-[var(--brand-gradient-start)]/50 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isLoading}
                loading={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Set password
                  </>
                ) : (
                  "Set password"
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </AuthLayout>
  );
}
