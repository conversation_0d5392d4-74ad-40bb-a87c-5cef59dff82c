import { WORKFLOWS_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { HUBSPOT_ACTIVITIES, generateHubspotActivityId } from "../constants";

type RouteParams = Promise<{ appId: string }>;

export async function GET(
  request: Request,
  context: { params: RouteParams },
): Promise<Response> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value || !orgUid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const params = await context.params;
    const { appId } = params;

    const activityName = generateHubspotActivityId(
      HUBSPOT_ACTIVITIES.GET_SETTINGS,
      appId,
      orgUid,
    );

    const response = await fetch(
      `${WORKFLOWS_URL}/api/v1/workflows/${activityName}/execute`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${session.value}`,
          "x-org-ID": orgUid,
        },
      },
    );

    if (!response.ok) {
      throw new Error("Failed to fetch settings");
    }

    const { data } = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch settings" },
      { status: 500 },
    );
  }
}

export async function PUT(
  request: Request,
  context: { params: RouteParams },
): Promise<Response> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value || !orgUid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const params = await context.params;
    const { appId } = params;
    const body = await request.json();

    const activityName = generateHubspotActivityId(
      HUBSPOT_ACTIVITIES.UPDATE_SETTINGS,
      appId,
      orgUid,
    );

    const response = await fetch(
      `${WORKFLOWS_URL}/api/v1/workflows/${activityName}/execute`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.value}`,
          "x-org-ID": orgUid,
        },
        body: JSON.stringify(body),
      },
    );

    if (!response.ok) {
      throw new Error("Failed to update settings");
    }

    const { data } = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error updating settings:", error);
    return NextResponse.json(
      { error: "Failed to update settings" },
      { status: 500 },
    );
  }
}
