import { WORKFLOWS_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { HUBSPOT_ACTIVITIES, generateHubspotActivityId } from "../constants";

type RouteParams = Promise<{ appId: string }>;

export async function GET(
  request: Request,
  context: { params: RouteParams },
): Promise<Response> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value || !orgUid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const params = await context.params;
    const { appId } = params;

    // Get page and limit from search params
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "100");

    const activityName = generateHubspotActivityId(
      HUBSPOT_ACTIVITIES.GET_SYNC_AUDIT_LOGS,
      appId,
      orgUid,
    );

    const response = await fetch(
      `${WORKFLOWS_URL}/api/v1/workflows/${activityName}/execute`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.value}`,
          "x-org-ID": orgUid,
        },
        body: JSON.stringify({ page, limit }),
      },
    );

    if (!response.ok) {
      throw new Error("Failed to fetch audit logs");
    }

    const { data } = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    return NextResponse.json(
      { error: "Failed to fetch audit logs" },
      { status: 500 },
    );
  }
}
