import * as crypto from "crypto";
import { NextRequest, NextResponse } from "next/server";

/**
 * Check if the content type is an image
 */
function isImageContentType(contentType: string): boolean {
  const imageTypes = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "image/svg+xml",
    "image/bmp",
    "image/tiff",
    "image/x-icon",
    "image/heic",
  ];
  return imageTypes.includes(contentType);
}

/**
 * Generate ETag for file based on metadata
 */
function generateETag(url: string, contentType: string, size?: number): string {
  const input = `${url}-${contentType}-${size || 0}`;
  return `"${crypto.createHash('md5').update(input).digest('hex')}"`;
}

export async function GET(request: NextRequest) {
  const url = request.nextUrl.searchParams.get("url");
  
  if (!url) {
    return NextResponse.json({ error: "URL parameter is required." }, { status: 400 });
  }

  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch image: ${response.statusText}` },
        { status: response.status }
      );
    }

    const contentType = response.headers.get("content-type") || "image/jpeg";
    const buffer = await response.arrayBuffer();

    const headers: Record<string, string> = {
      "Content-Type": contentType,
    };

    // Set cache headers for images
    if (isImageContentType(contentType)) {
      const etag = generateETag(url, contentType, buffer.byteLength);
      headers["Cache-Control"] = "public, max-age=2592000"; // 30 days
      headers["ETag"] = etag;
    } else {
      headers["Cache-Control"] = "public, max-age=86400"; // 1 day for non-images
    }

    return new NextResponse(buffer, { headers });
  } catch (error) {
    console.error("Error proxying image:", error);
    return NextResponse.json(
      { error: "Failed to fetch image." },
      { status: 500 }
    );
  }
}
