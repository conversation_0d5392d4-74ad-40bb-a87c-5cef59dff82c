import { EMAIL_SERVICE_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

interface EmailConfigResponse {
  message: string;
  emailConfig: {
    id: string;
    uid: string;
    userId: string;
    teamId: string;
    organizationId: string;
    forwardingEmailAddress: string;
    forwardingVerificationCode: string | null;
    customEmail: string | null;
    domain: string | null;
    isEmailForwardingVerified: boolean;
    sendersPreferredChoice: string;
    sendersPreferredChoiceValue: string;
    deletedAt: string | null;
    lastVerifiedAt: string | null;
    createdAt: string;
    updatedAt: string;
  };
}

export async function POST(request: Request): Promise<Response> {
  const { searchParams } = new URL(request.url);
  const teamId = searchParams.get("teamId");

  if (!teamId) {
    return NextResponse.json({ error: "Team ID is required" }, { status: 400 });
  }

  const cookieStore = await cookies();
  const session = cookieStore.get("base-access");
  const orgUid = request.headers.get("x-org-uid");

  if (!session?.value || !orgUid) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const res = await fetch(`${EMAIL_SERVICE_URL}/email-config/enable-email`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.value}`,
        "x-org-id": orgUid,
      },
      body: JSON.stringify({
        teamId,
      }),
    });

    if (!res.ok) {
      throw new Error("Failed to fetch default email");
    }

    const data: EmailConfigResponse = await res.json();
    return NextResponse.json({
      email: data.emailConfig.forwardingEmailAddress,
    });
  } catch (error) {
    console.error("Error fetching default email:", error);
    return NextResponse.json(
      { error: "Failed to fetch default email" },
      { status: 500 },
    );
  }
}
