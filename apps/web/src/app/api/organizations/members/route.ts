import { BASE_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

interface Member {
  id: string;
  name: string;
  email: string;
  role: string;
  avatarUrl?: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  userType: string;
  status: string;
  isActive: boolean;
  avatarUrl: string | null;
  timezone: string;
  metadata?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

interface ApiResponse {
  data: User[];
  status: boolean;
  message: string;
  timestamp: string;
}

interface InviteResponse {
  data: {
    inviteeEmail: string;
    inviter: {
      id: string;
    };
    organization: {
      id: string;
    };
    invitationLink: string | null;
    expiresAt: string | null;
    id: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
  status: boolean;
  message: string;
  timestamp: string;
}

export async function GET(request: Request): Promise<Response> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value || !orgUid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const response = await fetch(`${BASE_URL}/v1/users/list`, {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${session.value}`,
        "Content-Type": "application/json",
        "x-org-id": orgUid,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch members");
    }

    const result = (await response.json()) as ApiResponse;

    // Map API response to Member interface
    const members: Member[] = result.data
      .filter((user) => user.status === "ACTIVE") // Only include active users
      .map((user) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.userType === "ORG_ADMIN" ? "admin" : "member", // Map USER and BOT_USER to member
        avatarUrl: user.avatarUrl || undefined,
      }));

    return NextResponse.json({ members });
  } catch (error) {
    console.error("Error fetching members:", error);
    return NextResponse.json(
      { error: "Failed to fetch members" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request): Promise<Response> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");
    if (!session?.value || !orgUid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    const response = await fetch(`${BASE_URL}/v1/organizations/invite`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.value}`,
        "x-org-id": orgUid,
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      throw new Error("Failed to invite member");
    }

    const result = (await response.json()) as InviteResponse;

    return NextResponse.json({
      success: true,
      message: `Successfully invited ${email}`,
      data: result.data,
    });
  } catch (error) {
    console.error("Error inviting member:", error);
    return NextResponse.json(
      { error: "Failed to invite member" },
      { status: 500 },
    );
  }
}
