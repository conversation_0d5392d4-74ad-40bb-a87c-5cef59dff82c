import { WORKFLOWS_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

interface ApiResponse<T> {
  data?: T;
  error?: string;
}

enum WorkflowType {
  MANUAL = "MANUAL",
  AUTOMATED = "AUTOMATED",
}

enum WorkflowSubType {
  WORKFLOW = "WORKFLOW",
  AUTO_RESPONDER = "AUTO_RESPONDER",
  AI_AGENT = "AI_AGENT",
}

interface WorkflowVersion {
  uid: string;
  type: WorkflowType;
  subType: WorkflowSubType;
  name: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  teamId: string;
  uniqueIdentifier: string;
  version: number;
}

type Workflow = Omit<WorkflowVersion, "uniqueIdentifier" | "version">;

export async function GET(
  request: Request,
): Promise<NextResponse<ApiResponse<Workflow[]>>> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value) {
      return NextResponse.json(
        { error: "Unauthorized: No session token found" },
        { status: 401 },
      );
    }

    if (!orgUid) {
      return NextResponse.json(
        { error: "Unauthorized: No organization ID found" },
        { status: 401 },
      );
    }

    const response = await fetch(
      `${WORKFLOWS_URL}/api/v1/workflows?subTypes=${WorkflowSubType.WORKFLOW},${WorkflowSubType.AI_AGENT}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.value}`,
          "x-org-id": orgUid,
        },
      },
    );

    let result;
    try {
      result = await response.json();
    } catch (error) {
      console.error("Error parsing workflow response as JSON:", error);
      console.log("Response status:", response.status, response.statusText);
      const rawText = await response.text();
      console.log("Raw response text:", rawText);
      return NextResponse.json(
        { error: "Invalid response format from workflow service" },
        { status: 500 },
      );
    }

    if (!response.ok) {
      const error = result;
      return NextResponse.json(
        { error: error.message || "Failed to fetch workflows" },
        { status: response.status },
      );
    }

    // Group workflows by uniqueIdentifier
    const workflowGroups = result.results.reduce(
      (
        groups: { [key: string]: WorkflowVersion[] },
        workflow: WorkflowVersion,
      ) => {
        const key = workflow.uniqueIdentifier;
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(workflow);
        return groups;
      },
      {},
    );

    // Get the latest version from each group
    const latestWorkflows = (
      Object.values(workflowGroups) as WorkflowVersion[][]
    ).map((versions) => {
      return versions.reduce((latest, current) => {
        return latest.version > current.version ? latest : current;
      });
    });

    return NextResponse.json({
      data: latestWorkflows,
      status: true,
      message: "Workflows fetched successfully",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching workflows:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: Request): Promise<NextResponse> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value) {
      return NextResponse.json(
        { error: "Unauthorized: No session token found" },
        { status: 401 },
      );
    }

    if (!orgUid) {
      return NextResponse.json(
        { error: "Unauthorized: No organization ID found" },
        { status: 401 },
      );
    }

    const body = await request.json();

    const response = await fetch(`${WORKFLOWS_URL}/api/v1/workflows`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.value}`,
        "x-org-id": orgUid,
      },
      body: JSON.stringify(body),
    });

    const result = await response.json();

    if (!response.ok) {
      let error;
      try {
        error = await response.json();
      } catch (error) {
        console.error("Error parsing workflow response as JSON:", error);
        console.log("Response status:", response.status, response.statusText);
        const rawText = await response.text();
        console.log("Raw response text:", rawText);
        return NextResponse.json(
          { error: "Invalid response format from workflow service" },
          { status: 500 },
        );
      }
      return NextResponse.json(
        { error: error.message || "Failed to create workflow" },
        { status: response.status },
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error creating workflow:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
