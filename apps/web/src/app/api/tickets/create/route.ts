import { BASE_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

// Define request schema for validation
const createTicketSchema = z.object({
  teamId: z.string().min(1, "Team ID is required"),
  formId: z.string().min(1, "Form ID is required"),
  submitterEmail: z.string().email("Valid submitter email is required"),
  formData: z.record(z.unknown()),
});

export async function POST(request: NextRequest) {
  const session = await cookies();
  const accessToken = session.get("base-access");
  const orgUid = request.headers.get("x-org-uid");

  // Validate auth token
  if (!accessToken?.value) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Validate org ID
  if (!orgUid) {
    return NextResponse.json(
      { error: "Organization ID required" },
      { status: 401 },
    );
  }

  try {
    const body = await request.json();

    // Validate request body
    const validationResult = createTicketSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: validationResult.error.format(),
        },
        { status: 400 },
      );
    }

    const { teamId, formId, submitterEmail, formData } = validationResult.data;

    // Transform form data into the required format
    const customFieldValues = Object.entries(formData)
      .filter(([key]) => key.startsWith("custom_"))
      .map(([key, value]) => {
        // Remove 'custom_' prefix to get the actual field ID
        const customFieldId = key.replace("custom_", "");
        return {
          customFieldId,
          data: Array.isArray(value)
            ? value.map((v) => ({ value: v }))
            : [{ value: value }],
          metadata: {},
        };
      });

    // Extract standard fields
    const standardFields = Object.entries(formData)
      .filter(([key]) => !key.startsWith("custom_") && key !== "requester")
      .reduce((acc, [key, value]) => {
        // Map fields to their corresponding IDs
        // - 'type' field maps to 'typeId' in the API
        // - 'status' field maps to 'statusId' in the API
        // - 'priority' field maps to 'priorityId' in the API
        switch (key) {
          case "type":
            return { ...acc, typeId: value };
          case "status":
            return { ...acc, statusId: value };
          case "priority":
            return { ...acc, priorityId: value };
          default:
            return { ...acc, [key]: value };
        }
      }, {});

    // Prepare the request body in the required format
    const requestBody = {
      ...standardFields,
      teamId,
      formId,
      requestorEmail: body.requestorEmail || submitterEmail,
      submitterEmail, // The person creating the ticket (logged in user)
      metadata: {
        source: "manual",
      },
      customFieldValues:
        customFieldValues.length > 0 ? customFieldValues : undefined,
    };

    // Forward request to external API
    const response = await fetch(`${BASE_URL}/v1/tickets`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: `Bearer ${accessToken.value}`,
        "x-org-id": orgUid,
        "x-request-source": "manual", // Identify tickets created manually through the UI
      },
      body: JSON.stringify(requestBody),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: data.message || "Failed to create ticket" },
        { status: response.status },
      );
    }

    // Extract ticket data from response
    const ticketData = data.data || data;

    return NextResponse.json({
      message: "Ticket created successfully",
      ticket: ticketData,
    });
  } catch (error) {
    console.error("Error creating ticket:", error);
    return NextResponse.json(
      { error: "Failed to create ticket" },
      { status: 500 },
    );
  }
}
