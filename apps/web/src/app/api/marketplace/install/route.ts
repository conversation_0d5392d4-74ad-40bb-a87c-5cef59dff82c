import { APP_STUDIO_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

const PLATFORM_API_URL =
  process.env.NEXT_PUBLIC_APPS_PLATFORM_URL || APP_STUDIO_URL;

export async function POST(request: Request): Promise<Response> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value || !orgUid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { appId, teamIds, appConfiguration } = body;

    if (!appId || !teamIds || !Array.isArray(teamIds) || teamIds.length === 0) {
      return NextResponse.json(
        { error: "Invalid request. appId and teamIds are required" },
        { status: 400 },
      );
    }

    const response = await fetch(`${PLATFORM_API_URL}/apps/install`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.value}`,
        "x-org-id": orgUid,
      },
      body: JSON.stringify({
        appId,
        teamIds,
        appConfiguration: appConfiguration || {
          required_settings: [],
          optional_settings: [],
        },
      }),
    });

    const responseData = await response.json();
    if (!response.ok) {
      return NextResponse.json(
        {
          error:
            responseData.message ||
            responseData.error ||
            "Failed to install app",
        },
        { status: response.status },
      );
    }

    return NextResponse.json(responseData);
  } catch (_error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
