import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { WORKFLOWS_URL } from "../../../config/constant";

export async function GET(req: NextRequest) {
  const searchParams = req.nextUrl.searchParams;
  const workflowId = searchParams.get("workflowId");
  const cookieStore = await cookies();
  const session = cookieStore.get("base-access");
  const orgUid = req.headers.get("x-org-uid");

  if (!orgUid || !session) {
    return NextResponse.json({ data: [] }, { status: 200 });
  }

  if (!workflowId) {
    return NextResponse.json({ data: [] }, { status: 200 });
  }

  try {
    const response = await fetch(
      `${WORKFLOWS_URL}/api/v1/workflows/${workflowId}/execute`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${session.value}`,
          "x-org-id": orgUid,
        },
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json({ data: data.data }, { status: 200 });
  } catch (err) {
    console.error("Error fetching internal users:", err);
    return NextResponse.json(
      { data: [], error: "Internal server error" },
      { status: 500 },
    );
  }
}
