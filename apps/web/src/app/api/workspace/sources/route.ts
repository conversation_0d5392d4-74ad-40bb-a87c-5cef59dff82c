import { APPS_PLATFORM_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

interface App {
  clientSecret: string;
  createdAt: string;
  deletedAt: string;
  developerId: string;
  eventsWebhookUrl: string;
  iconUrlLarge: string;
  description: string;
  iconUrlSmall: string;
  id: string;
  installationCount: number;
  type: string;
  installationsWebhookUrl: string;
  isActive: boolean;
  manifest: Record<string, unknown>;
  name: string;
  organizationId: string;
  privacyPolicyUrl: string;
  uid: string;
  updatedAt: string;
  visibility: string;
}

interface InstalledApp {
  appConfiguration: Record<string, unknown>;
  appId: string;
  appManifest: Record<string, unknown>;
  description: string;
  installedByEmail: string;
  installationId: string;
  installedBySub: string;
  name: string;
  teamId: string[];
  updatedAt: string;
}

export async function GET(request: Request) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("base-access")?.value;
    const orgUid = request.headers.get("x-org-uid");
    if (!token || !orgUid) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // First fetch available apps
    const availableAppsResponse = await fetch(
      `${APPS_PLATFORM_URL}/apps/fetch-apps?includePrivileged=true`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          "x-org-id": orgUid,
        },
        cache: "no-store",
      },
    );

    const availableApps = await availableAppsResponse.json();
    if (!availableAppsResponse.ok) {
      throw new Error(
        `Failed to fetch available apps: ${availableAppsResponse.statusText}`,
      );
    }

    // Then fetch installed apps
    const installedAppsResponse = await fetch(
      `${APPS_PLATFORM_URL}/apps/installed-apps?includePrivileged=true`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          "x-org-id": orgUid,
        },
        cache: "no-store",
      },
    );

    const installedAppsJson = await installedAppsResponse.json();
    if (!installedAppsResponse.ok && installedAppsJson.statusCode !== 404) {
      throw new Error(
        `Failed to fetch installed apps: ${installedAppsResponse.statusText}`,
      );
    }

    const installedApps = installedAppsJson.apps;

    // Transform the data to match our sources format
    const sources = availableApps.data.map((app: App) => {
      const isInstalled = installedApps?.some(
        (installed: InstalledApp) => installed.appId === app.uid,
      );

      const installedApp = installedApps?.find(
        (installed: InstalledApp) => installed.appId === app.uid,
      );

      return {
        id: app.uid,
        name: app.name,
        description: app.description,
        icon: app.iconUrlLarge || app.iconUrlSmall,
        installationId: installedApp?.installationId,
        status: isInstalled ? "connected" : "disconnected",
        isEnabled: true,
        type:
          app.manifest.app && typeof app.manifest.app === "object"
            ? (app.manifest.app as Record<string, string>).category
            : app.type,
        isConnected: isInstalled,
        botUserId: installedApp?.botUserId,
        connectionDetails: installedApp
          ? {
              workspaceName: installedApp?.workspaceName,
              connectedAt: installedApp?.installedAt,
            }
          : undefined,
      };
    });

    return NextResponse.json({ sources });
  } catch (error) {
    console.error("Error fetching sources:", error);
    return NextResponse.json(
      { error: "Failed to fetch sources" },
      { status: 500 },
    );
  }
}
