"use server";

type RouteParams = Promise<{ sourceId: string; teamId: string }>;

export async function POST(
  request: Request,
  context: { params: RouteParams },
): Promise<Response> {
  try {
    const headers = request.headers;
    const body = await request.json();
    const params = await context.params;
    const { teamId } = params;

    const token = headers.get("x-auth-token");
    const installingUser = headers.get("x-current-user");

    // Check if the request body is valid
    if (!body || !body.workspaces) {
      return new Response(JSON.stringify({ error: "Invalid request body" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check if the workspaces array is valid
    if (!Array.isArray(body.workspaces) || body.workspaces.length === 0) {
      return new Response(JSON.stringify({ error: "Invalid workspaces" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    const url = `${process.env.NEXT_PUBLIC_THENA_SLACK_APP_URL}/v1/slack/teams`;
    const addTeamToSlackResponse = await fetch(url, {
      method: "POST",
      body: JSON.stringify({
        teamId,
        workspaces: body.workspaces,
        installedBy: installingUser,
      }),
      headers: {
        "Content-Type": "application/json",
        "x-auth-token": token,
        "x-slack-id": body.workspaces[0],
      },
    });

    const addTeamToSlackData = await addTeamToSlackResponse.json();
    if (!addTeamToSlackResponse.ok) {
      return new Response(JSON.stringify({ error: addTeamToSlackData }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    return new Response(
      JSON.stringify({ success: true, data: addTeamToSlackData }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    console.error("Error adding team access to Slack source:", error);
    return new Response(
      JSON.stringify({ error: "Failed to add team access to Slack source" }),
      { status: 500, headers: { "Content-Type": "application/json" } },
    );
  }
}

export async function DELETE(
  request: Request,
  context: { params: RouteParams },
): Promise<Response> {
  try {
    const headers = request.headers;
    const body = await request.json();
    const params = await context.params;
    const { teamId } = params;

    const token = headers.get("x-auth-token");

    // Check if the request body is valid
    if (!body || !body.workspaces) {
      return new Response(JSON.stringify({ error: "Invalid request body" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check if the workspaces array is valid
    if (!Array.isArray(body.workspaces) || body.workspaces.length === 0) {
      return new Response(JSON.stringify({ error: "Invalid workspaces" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    const url = `${process.env.NEXT_PUBLIC_THENA_SLACK_APP_URL}/v1/slack/teams`;
    const removeTeamFromSlackResponse = await fetch(url, {
      method: "DELETE",
      body: JSON.stringify({
        teamId,
      }),
      headers: {
        "Content-Type": "application/json",
        "x-auth-token": token,
        "x-slack-id": body.workspaces[0],
      },
    });

    const removeTeamFromSlackData = await removeTeamFromSlackResponse.json();
    if (!removeTeamFromSlackResponse.ok) {
      return new Response(JSON.stringify({ error: removeTeamFromSlackData }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    return new Response(
      JSON.stringify({ success: true, data: removeTeamFromSlackData }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    console.error("Error removing team access from Slack source:", error);
    return new Response(
      JSON.stringify({
        error: "Failed to remove team access from Slack source",
      }),
      { status: 500, headers: { "Content-Type": "application/json" } },
    );
  }
}
