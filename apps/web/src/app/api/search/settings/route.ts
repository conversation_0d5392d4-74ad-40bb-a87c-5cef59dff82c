import { TYPESENSE_CONFIG, getTypesenseNodeConfig } from "@/config/typesense";
import { createClient } from "@/lib/supabase-server";
import { NextResponse } from "next/server";
import Typesense from "typesense";

// Initialize Typesense client for tickets and comments
const typesenseClientTicketsAndComments = new Typesense.Client({
  nodes: [getTypesenseNodeConfig()],
  apiKey: TYPESENSE_CONFIG.SEARCH_ONLY_API_KEY_TICKETS_AND_COMMENTS,
});

// Initialize Typesense client for accounts
const typesenseClientAccounts = new Typesense.Client({
  nodes: [getTypesenseNodeConfig()],
  apiKey: TYPESENSE_CONFIG.SEARCH_ONLY_API_KEY_ACCOUNTS,
});

export async function GET(request: Request) {
  // First try to get orgId from header, then fallback to query params
  const orgId = request.headers.get("x-org-id");
  const { searchParams } = new URL(request.url);
  const organization_id = (orgId || searchParams.get("orgId")) ?? "";

  if (!organization_id) {
    return NextResponse.json(
      { error: "Organization ID not found" },
      { status: 400 },
    );
  }

  const supabase = await createClient();

  const supabaseUser = await supabase.auth.getUser();
  if (!supabaseUser?.data?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { data: user } = await supabase
      .from("user")
      .select("id")
      .eq("auth_id", supabaseUser.data.user.id)
      .eq("organization_id", organization_id)
      .single();

    // Get all team memberships for the user in the current org
    const { data: allTeamMemberships, error: teamMembershipsError } =
      await supabase
        .from("team_member")
        .select("team:team_id(uid)")
        .eq("user_id", user.id)
        .eq("organization_id", organization_id)
        .eq("is_active", true)
        .is("deleted_at", null)
        .is("is_bot", false)
        .returns<{ team: { uid: string } }[]>();

    if (teamMembershipsError) {
      console.error("Error fetching team memberships:", teamMembershipsError);
      return NextResponse.json(
        { error: "Failed to fetch team memberships" },
        { status: 500 },
      );
    }

    const teamUids = allTeamMemberships?.map((tm) => tm.team?.uid) || [];

    // Get organization UID
    const { data: org } = await supabase
      .from("organization")
      .select("uid")
      .eq("id", organization_id)
      .single();

    if (!org?.uid) {
      return NextResponse.json(
        { error: "Organization UID not found" },
        { status: 500 },
      );
    }

    // Generate search parameters for tickets and comments
    const ticketsAndCommentsParameters = {
      filter_by:
        teamUids.length === 0
          ? `orgId:=${org.uid}`
          : `orgId:=${org.uid} && teamId:=[${teamUids.join(",")}]`,
      expires_at: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60, // 7 days expiry
    };

    // Generate search parameters for accounts
    const accountsParameters = {
      filter_by: `orgId:=${org.uid}`,
      expires_at: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60, // 7 days expiry
    };

    // Generate scoped keys using respective Typesense clients
    const ticketsAndCommentsKey = typesenseClientTicketsAndComments
      .keys()
      .generateScopedSearchKey(
        TYPESENSE_CONFIG.SEARCH_ONLY_API_KEY_TICKETS_AND_COMMENTS,
        ticketsAndCommentsParameters,
      );

    const accountsKey = typesenseClientAccounts
      .keys()
      .generateScopedSearchKey(
        TYPESENSE_CONFIG.SEARCH_ONLY_API_KEY_ACCOUNTS,
        accountsParameters,
      );

    return NextResponse.json({
      ticketsAndComments: {
        key: ticketsAndCommentsKey,
        parameters: ticketsAndCommentsParameters,
      },
      accounts: {
        key: accountsKey,
        parameters: accountsParameters,
      },
      teams: teamUids,
    });
  } catch (error) {
    console.error("Error in search settings API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
