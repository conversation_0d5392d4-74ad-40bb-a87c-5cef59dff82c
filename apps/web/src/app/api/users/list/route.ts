import { BASE_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function GET(request: Request): Promise<NextResponse> {
  try {
    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value) {
      return NextResponse.json(
        { error: "Unauthorized: No session token found" },
        { status: 401 },
      );
    }

    if (!orgUid) {
      return NextResponse.json(
        { error: "Unauthorized: No organization ID found" },
        { status: 401 },
      );
    }

    const response = await fetch(`${BASE_URL}/v1/users/list`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${session.value}`,
        "Content-Type": "application/json",
        "x-org-id": orgUid,
      },
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      console.error("Platform API Error:", {
        status: response.status,
        statusText: response.statusText,
        data: errorData,
      });
      return NextResponse.json(
        { error: `API Error: ${response.statusText}` },
        { status: response.status },
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching org user list:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
