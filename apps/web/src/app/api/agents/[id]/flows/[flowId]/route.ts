 
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

const AGENT_STUDIO_API =
  process.env.NEXT_AGENT_STUDIO_URL || "http://localhost:8008";

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; flowId: string }> },
): Promise<NextResponse> {
  const cookieStore = await cookies();
  const session = cookieStore.get("base-access");
  const orgUid = request.headers.get("x-org-uid");

  if (!session?.value || !orgUid) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const resolvedParams = await params;
    const { id: agentId, flowId } = resolvedParams;

    if (!flowId || !agentId) {
      throw new Error("Flow ID and Agent ID are required");
    }

    console.log("Updating flow:", {
      flowId,
      agentId,
      body,
      url: `${AGENT_STUDIO_API}/api/v1/flows/${flowId}`,
    });

    // Call the backend API to update the flow
    const res = await fetch(
      `${AGENT_STUDIO_API}/api/v1/flows/${flowId}?agent_id=${agentId}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.value}`,
          "x-org-id": orgUid,
        },
        body: JSON.stringify(body),
      },
    );

    if (!res.ok) {
      const errorData = await res.text();
      console.error("Update Flow API Error:", {
        status: res.status,
        statusText: res.statusText,
        body: errorData,
      });
      throw new Error(`Failed to update flow: ${res.status} ${res.statusText}`);
    }

    const data = await res.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error updating flow:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to update flow",
      },
      { status: 500 },
    );
  }
}
