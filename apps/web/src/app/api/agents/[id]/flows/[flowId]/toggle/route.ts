 
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

const AGENT_STUDIO_API =
  process.env.NEXT_AGENT_STUDIO_URL || "http://localhost:8008";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; flowId: string }> },
): Promise<NextResponse> {
  const cookieStore = await cookies();
  const session = cookieStore.get("base-access");
  const orgUid = request.headers.get("x-org-uid");

  if (!session?.value || !orgUid) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const resolvedParams = await params;
    const { id: agentId, flowId } = resolvedParams;

    if (!flowId || !agentId) {
      throw new Error("Flow ID and Agent ID are required");
    }

    console.log("Toggling flow status:", {
      flowId,
      agentId,
      body,
      orgUid: orgUid,
      url: `${AGENT_STUDIO_API}/api/v1/flows/${flowId}/toggle?agent_id=${agentId}`,
    });

    // Call the backend API to toggle the flow
    const res = await fetch(
      `${AGENT_STUDIO_API}/api/v1/flows/${flowId}/toggle?agent_id=${agentId}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.value}`,
          "x-org-id": orgUid,
        },
        body: JSON.stringify({ is_active: body.isActive }),
      },
    );

    if (!res.ok) {
      const errorData = await res.text();
      console.error("Toggle Flow API Error:", {
        status: res.status,
        statusText: res.statusText,
        body: errorData,
      });
      throw new Error(`Failed to toggle flow: ${res.status} ${res.statusText}`);
    }

    const data = await res.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error toggling flow:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to toggle flow",
      },
      { status: 500 },
    );
  }
}
