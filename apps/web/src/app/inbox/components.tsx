"use client";
import InboxHeader from "@/components/inbox/inbox-header";
import { InboxLayout } from "@/components/inbox/inbox-layout";
import { MessageList } from "@/components/inbox/message-list";
import Providers from "@/components/providers";
import { DashboardSidebar } from "@/components/sidebar/sidebar";
import { ContextMenu, ContextMenuTrigger } from "@/components/ui/context-menu";
import { SidebarProvider } from "@/components/ui/sidebar";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useApi } from "@/hooks/use-api";
import {
  GET_ALL_TEAMS,
  GET_BOOTSTRAP,
  GET_FORM_BY_TEAM_ID,
  GET_TICKET_BY_ID,
  GET_TICKET_TYPES_BY_TEAM_ID,
} from "@/services/kanban";
import { GET_ALL_SOURCES } from "@/services/ticket-sources";
import { Source, useAppsSourcesStore } from "@/store/apps-sources-store";
import { useChatStore } from "@/store/chat-store";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useInboxPersistStore } from "@/store/inbox-persist";
import { useInboxStore } from "@/store/inbox-store";
import { useKanbanStore } from "@/store/kanbanStore";
import { useKanbanStorePersist } from "@/store/kanbanStorePersist";
import { useTicketDetailsDisplayOptions } from "@/store/ticket-details-display-options";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { useTicketDrawerStore } from "@/store/ticketDrawerStore";
import { useKanbanDisplayOptions } from "@/stores/use-kanban-display-options";
import {
  FormType,
  Priority,
  SavedViewConfig,
  SingleTicket,
  Status,
  Team,
  TeamMembers,
  ThenaTicket,
  TicketTypes,
  UserConfig,
} from "@/types/kanban";
import { useQuery } from "@tanstack/react-query";
import { subDays } from "date-fns";
import cloneDeep from "lodash/cloneDeep";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { TeamGroup } from "../(routes-with-sidebar)/dashboard/[teamId]/settings/routing/types";
import AccountPrimaryHeader from "../(routes-with-sidebar)/dashboard/account-primary-header";
import PrimaryHeader from "../(routes-with-sidebar)/dashboard/primary-header";
import { WebChatWidget } from "../../hooks/use-web-chat-init";
import { getOrgDetails } from "../../utils/browserUtils";
import { getUserSessionDetails } from "../actions/auth";

type BootstrapResponse = {
  statuses: Status[];
  priorities: Priority[];
  teamMembers: TeamMembers[];
  statusOrder: UserConfig["status_order"];
  userFilterConfig: UserConfig["userFilterConfig"];
  savedViews: SavedViewConfig[];
  hiddenColumns: string[];
};

type formResponse = {
  results: FormType[];
  pageTotal: number;
  total: number;
  offset: number;
};

interface ThenaWidgetWindow extends Window {
  thenaWidget?: WebChatWidget;
  thena?: {
    open: () => void;
    close: () => void;
    toggle: () => void;
    toggleDarkMode: () => void;
    toggleVisibility: () => void;
  };
}

export default function InboxPage() {
  const inboxDispatch = useInboxPersistStore((state) => state.dispatch);
  const currentNotificationId = useInboxPersistStore(
    (state) => state.currentNotificationId,
  );
  const isOpen = useChatStore((state) => state.isOpen);
  const pathname = usePathname();
  const isAccountsPage = pathname?.includes("/accounts");
  const isDashboardPage = pathname === `/dashboard`;
  const { ticketId, closeDrawer } = useTicketDrawerStore();
  const searchParams = useSearchParams();
  const ticketIdFromParams = searchParams.get("ticketId");
  const router = useRouter();
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);

  const { filteredNotifications, feedClient, groupedNotificationsArray } =
    useInboxStore();

  useEffect(() => {
    if (ticketIdFromParams) {
      useTicketDrawerStore.getState().openDrawer(ticketIdFromParams, router);
    } else {
      useTicketDrawerStore.getState().closeDrawer(router);
    }
  }, [ticketIdFromParams, router]);

  const {
    data: ticketData,
    isLoading: loading,
    isFetching: isRefetching,
  } = useQuery({
    queryKey: ["ticket", ticketId],
    queryFn: async () => {
      if (!ticketId) return null;
      const response = await fetch(GET_TICKET_BY_ID(ticketId), {
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (!response.ok) {
        throw new Error("Failed to fetch ticket");
      }
      return response.json();
    },
    enabled: !!ticketId,
  });

  const renderHeader = useMemo(() => {
    if (!loading && !isRefetching && ticketId) {
      return (
        <ContextMenu>
          <ContextMenuTrigger className="w-full">
            <InboxHeader
              ticketData={ticketData as SingleTicket}
              closeDrawer={closeDrawer}
            />
          </ContextMenuTrigger>
        </ContextMenu>
      );
    }
  }, [closeDrawer, ticketData, loading, isRefetching, ticketId]);
  const userId = useGlobalConfigPersistStore((state) => state)?.currentUser
    ?.uid;
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);
  const [isHydrated, setIsHydrated] = useState(false);
  const { orgUid, orgId } = getOrgDetails();
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    const fetchUserSession = async () => {
      const result = await getUserSessionDetails(orgUid, orgId);
      const userData = result?.data?.user;
      if (userData) {
        dispatch({
          type: "SET_CURRENT_USER",
          payload: {
            currentUser: {
              id: userData.id,
              email: userData.email,
              name: userData.name,
              metadata: userData.metadata,
              uid: userData.currentUserUid || null,
              avatarUrl: userData.avatarUrl || null,
              allUserIds: userData.allUserIds || [],
            },
          },
        });
        dispatch({
          type: "SET_ORGS",
          payload: {
            orgs: userData.allOrgs || [],
            currentOrgId: userData.currentOrgId || null,
          },
        });
        dispatch({
          type: "SET_CURRENT_USER_UID",
          payload: {
            uid: userData.currentUserUid || null,
          },
        });
        dispatch({
          type: "SET_CURRENT_ORG",
          payload: {
            currentOrgId: userData.currentOrgId || null,
          },
        });
      }
    };
    if ((!currentUser || !currentUser?.uid) && isHydrated) {
      fetchUserSession();
    }
  }, [currentUser, dispatch, isHydrated]);

  const { data: teams } = useApi<Team[]>(
    GET_ALL_TEAMS(userId),
    {},
    {
      isNextApi: true,
      enabled: !!userId,
    },
  );

  // Fetch Sources
  const { data: sources } = useApi<{ sources: Source[] }>(
    GET_ALL_SOURCES,
    {},
    { isNextApi: true, enabled: !!orgUid },
  );

  useEffect(() => {
    if (teams?.length > 0) {
      const nonDeletedTeams = teams.filter((team) => !team.deleted_at);
      useTicketMetaStore.getState().setAllTeams(nonDeletedTeams);
    }

    if (sources?.sources?.length > 0) {
      useAppsSourcesStore.getState().dispatch({
        type: "SET_SOURCES",
        payload: { sources: sources.sources },
      });
    }
  }, [teams, sources]);

  const currentTeamId = filteredNotifications.find(
    (notification) => notification.data?.entityId === currentNotificationId,
  )?.data?.entityData?.teamId;

  const { data: bootstrapData, error } = useApi<BootstrapResponse>(
    GET_BOOTSTRAP(currentTeamId),
    {},
    {
      isNextApi: true,
      enabled: !!currentTeamId,
    },
  );
  useEffect(() => {
    if (error) {
      toast.error("Error fetching data", {
        description: error.message,
      });
    }
  }, [error]);

  useEffect(() => {
    if (bootstrapData) {
      const {
        statuses,
        priorities,
        teamMembers,
        statusOrder = {} as UserConfig["status_order"],
        userFilterConfig = {},
        savedViews,
        hiddenColumns,
      } = bootstrapData;
      useTicketMetaStore.getState().setAllStatuses(statuses);
      useTicketMetaStore.getState().setAllPriorities(priorities);
      useTicketMetaStore.getState().setAllTeamMembers(teamMembers);
      useTicketMetaStore.getState().setSavedViews(savedViews);

      const { metadata = {}, filterUid } =
        userFilterConfig as UserConfig["userFilterConfig"];

      const {
        queryValuesMap = {},
        selectedFiltersOrder = [],
        dateFilters = {
          dateRange: { from: subDays(new Date(), 30), to: new Date() },
          preselectedRange: "",
        },
        drawerDisplayOptions,
        kanbanDisplayOptions,
      } = metadata as UserConfig["userFilterConfig"]["metadata"];

      if (drawerDisplayOptions) {
        useTicketDetailsDisplayOptions
          .getState()
          .setStateOverwrite(drawerDisplayOptions);
      }

      if (kanbanDisplayOptions) {
        useKanbanDisplayOptions
          .getState()
          .setStateOverwrite(kanbanDisplayOptions);
      }

      useKanbanStorePersist.dispatch({
        type: "SET_DEFAULT_FILTERS",
        payload: {
          queryValuesMap,
          selectedFiltersOrder,
          dateFilters,
          filterUid,
        },
      });
      // Fallback if the data was added via API
      const newStatusOrder = cloneDeep(statusOrder.statusOrder || {});

      statuses.forEach((status) => {
        const isParent = !status.parent_status;

        if (isParent) {
          if (newStatusOrder[status.uid]) {
            return;
          }
          newStatusOrder[status.uid] = {
            order: Object.keys(newStatusOrder).length,
            sub_statuses: statuses
              .filter((s) => s.parent_status?.uid === status.uid)
              .map((s) => s.uid),
          };

          return;
        }

        const isPresentInMap = newStatusOrder[
          status.parent_status.uid
        ].sub_statuses.find((s) => s === status.uid);

        if (isPresentInMap) {
          return;
        }

        newStatusOrder[status.parent_status.uid] = {
          order: newStatusOrder[status.parent_status.uid].order,
          sub_statuses: [
            ...newStatusOrder[status.parent_status.uid].sub_statuses,
            status.uid,
          ],
        };
      });

      useKanbanStore.getState().setStatusOrder(newStatusOrder);
      useKanbanStore
        .getState()
        .setStatusOrderUid(statusOrder.statusOrderUid ?? "");

      useKanbanStore.getState().setHiddenColumns(
        hiddenColumns.map((uid) => {
          const column = statuses.find((status) => status.uid === uid);
          return {
            uid,
            count: 0, // This to be updated from tickets
            name: column?.display_name ?? "",
          };
        }),
      );

      if (statuses.length) {
        const statusMap = statuses
          .filter((status) => status.child_statuses.length === 0)
          .reduce(
            (acc, status) => {
              acc[status.uid] = [];
              return acc;
            },
            {} as Record<string, ThenaTicket[]>,
          );

        useKanbanStore.getState().setStatusOrderMap(statusMap);
      }
    }
  }, [bootstrapData]);

  const { data: groups } = useApi<TeamGroup[]>(
    `/v1/teams/${currentTeamId}/sub-teams`,
    {},
    { enabled: !!currentTeamId, isNextApi: false, method: "GET" },
  );

  const { data: ticketTypes } = useApi<TicketTypes[]>(
    GET_TICKET_TYPES_BY_TEAM_ID(currentTeamId),
    {},
    { isNextApi: false, enabled: !!currentTeamId },
  );

  useEffect(() => {
    if (ticketTypes) {
      useTicketMetaStore.getState().setAllTicketType(ticketTypes);
    }
  }, [ticketTypes]);

  const { data: formDataFromApi } = useApi<formResponse>(
    GET_FORM_BY_TEAM_ID(currentTeamId),
    {},
    { enabled: !!currentTeamId, isNextApi: false },
  );

  useEffect(() => {
    if (formDataFromApi?.results) {
      useTicketMetaStore.getState().setForms(formDataFromApi.results);
    }
    if (groups) {
      useTicketMetaStore.getState().setGroups(groups);
    }
  }, [formDataFromApi, groups]);

  const showWidget = useGlobalConfigPersistStore((state) => state.showWidget);

  useEffect(() => {
    // Initialize Thena Widget
    const globalWindow = window as ThenaWidgetWindow;
    if (!showWidget) {
      const element = document.getElementById("thena-widget-launcher");
      if (element) {
        element.style.display = "none";
      }
      // Close the widget if it's open
      if (globalWindow.thena?.close) {
        globalWindow.thena.close();
      }
    } else {
      const element = document.getElementById("thena-widget-launcher");
      if (element) {
        element.style.display = "flex";
      }
    }
  }, [showWidget]);

  return (
    <Providers>
      <SidebarProvider>
        <TooltipProvider delayDuration={700}>
          <div className="flex min-h-screen w-screen bg-color-bg-subtle grow">
            <DashboardSidebar />
            <main
              className={`flex-1 overflow-auto transition-all duration-300  ${
                isOpen ? "mr-[432px]" : ""
              }`}
            >
              {isAccountsPage ? (
                <AccountPrimaryHeader />
              ) : (
                <PrimaryHeader renderInboxHeader={renderHeader} />
              )}
              <aside className="px-4">
                <div
                  className={`bg-background overflow-hidden ${
                    isDashboardPage ? "h-full" : "h-[calc(100vh-75px)]"
                  }`}
                >
                  <InboxLayout>
                    <MessageList
                      messages={(groupedNotificationsArray || []).map(
                        (item) => item.latestNotification,
                      )}
                      selectedId={currentNotificationId}
                      onMessageSelect={(id) => {
                        inboxDispatch({
                          type: "SET_CURRENT_SELECTED_NOTIFICATION",
                          payload: { currentNotificationId: id },
                        });
                      }}
                      feedClient={feedClient}
                    />
                  </InboxLayout>
                </div>
              </aside>
            </main>
          </div>
        </TooltipProvider>
      </SidebarProvider>
    </Providers>
  );
}
