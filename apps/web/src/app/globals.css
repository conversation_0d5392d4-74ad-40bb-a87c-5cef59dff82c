@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 50% 99.5%;
    --background-translucent: rgba(253, 254, 255, 0.7);
    --foreground: 240 10% 4%;

    --card: 210 50% 99.5%;
    --card-foreground: 240 10% 4%;

    --popover: 210 50% 99.5%;
    --popover-foreground: 240 10% 4%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 214 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 214 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --muted-text: 240 4% 46%;

    --accent: 214 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 240 10% 4%;

    /* Commented out Thena-native variables */
    /*
    --thena-native-start: #141c25;
    --thena-native-end: #18202a;
    --thena-native-hover-start: #475569;
    --thena-native-hover-end: #171c25;
    --thena-native-border: #0d141d;
    */

    /* Themed Button Variables */
    --themed-btn-from: hsl(222.2 47.4% 20%);
    --themed-btn-to: hsl(222.2 52.4% 10%);
    --themed-btn-text: hsl(210 40% 98%);
    --themed-btn-hover-from: hsl(222.2 47.4% 30%);
    --themed-btn-hover-to: hsl(222.2 47.4% 20%);
    --themed-btn-shadow: rgba(27, 38, 67, 0.25);
    --themed-btn-shadow-hover: rgba(27, 38, 67, 0.35);
    --themed-btn-glow: rgba(27, 38, 67, 0.15);
    --themed-btn-glow-hover: rgba(27, 38, 67, 0.25);

    --counter-blue-bg: #f0f5ff;
    --counter-blue-text: #3b82f6;
    --counter-orange-bg: #fff8f0;
    --counter-orange-text: #f97316;
    --counter-purple-bg: #f8f5ff;
    --counter-purple-text: #8b5cf6;

    --highlight-1: #e4e7ed;

    --color-card-bg-light: #fcfdfe;
    --color-bg-inverse: #0f172a;
    --radius: 0.5rem;
    --color-bg-subtle: #f8fafc;
    --color-text: #09090b;
    --color-text-placeholder: #64748b;
    --color-icon-muted: #71717a;
    --modal-bg: #ffffff;
    --modal-bg-rgb: 255, 255, 255;
    --modal-border: #e4e7ed;
    --modal-text: #09090b;
    --color-text-info: #2563eb;
    --color-border: #e4e7ed;
    --color-text-error: #dc2626;
    --color-text-success: #047857;
    --color-text-warning: #fb923c;
    --color-bg-warning-muted: #fefce8;
    --color-bg-elevated: #f1f5f9;
    --color-bg-success-muted: #f0fdf4;
    --color-border-success: #10b981;
    --color-text-disabled: #64748b;
    --color-bg-error-muted: #fef2f2;
    --color-icon-info: #2563eb;
    --color-bg-disabled: #f1f5f9;
    --color-text-muted: #64748b;
    --color-border-magic: #7c3aed;
    --color-bg-magic-muted: #f5f3ff;
    --slack-code-color: #c01343;
    --slack-code-border: rgba(29, 28, 29, 0.13);
    --slack-code-block-bg: rgba(29, 28, 29, 0.04);
    --slack-blockquote-left-border: rgb(221, 221, 221);
    --color-icon-warning: #d97706;
    --color-widget-launcher: #2563eb;

    --color-strong: #eaeef6;
    --color-table-header: #f1f5fa;
    --color-icon-interactive: #1e3a8a;
    --color-border-primary: #18181b;
    --color-text-body: #334155;
    --color-bg-card: #fff;

    /* New variables for kanban styling */
    --kanban-card-bg: #fff;
    --kanban-board-bg: #f8fafc;
    --kanban-column-bg: #f9fcfe;
    --kanban-card-shadow: 1px 1px 2px rgba(0, 0, 0, 0.04);
    --kanban-card-hover: #f3f8fa;

    /* New variables for mention styling */
    --mention-color: #1263A3 !important;
    --mention-bg: #EBF5FA !important;
    --mention-padding: 0 4px !important;
    --mention-border-radius: 4px !important;
    --mention-font-weight: 500 !important;

    /* New variable for URL styling */
    --url-color: #1263A3 !important;

    --color-bg-loader: #e6ecf5;
    --thread-hover-bg: #f9fafc;
    --thread-border-color: #e2e8f0;
    --color-progress: #ec4899;
    --color-icon-magic: #7c3aed;
    --color-text-magic: #9333ea;
    --text-disabled: #94a3b8;

    --color-bg-warning-strong: #eab308;
    --color-border-warning: #ca8a04;
    --color-progress-dot: #7a01ff;
    --color-progress-dot-inactive: #ededed;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --color-icon: #0f172a;
    --color-border-info-muted: #60a5fa;
    --color-border-error-muted: #f87171;
    --color-border-success-muted: #6ee7b7;
    --color-border-warning-muted: #fbbf24;
    --color-bg-success-strong: #059669;
    --color-hover-accent: #6a00ff;
    --online-border-color: #f8fafc;

    /* Badge styling for notifications */
    --badge-bg: #fefeff;
    --badge-border: #e4e7ec;

    /* Chart colors for insights page */
    --chart-color-1: rgb(96, 165, 250);
    /* blue */
    --chart-color-2: rgb(52, 211, 153);
    /* green */
    --chart-color-3: rgb(168, 85, 247);
    /* purple */
    --chart-color-4: rgb(251, 146, 60);
    /* orange */
    --chart-color-5: rgb(192, 132, 252);
    /* violet */
    --chart-color-6: rgb(251, 191, 36);
    /* yellow */
    --chart-color-7: rgb(129, 140, 248);
    /* indigo */
    --chart-color-8: rgb(232, 121, 249);
    /* pink */
    --chart-color-9: rgb(163, 230, 53);
    /* lime */
    --chart-color-10: rgb(56, 137, 232);
    /* deeper blue */

    /* Accent colors for document and auth icons */
    --color-bg-document: #fdf2f8;
    --color-text-document: #db2777;
    --color-bg-auth: #e6fffa;
    --color-text-auth: #0d9488;

    /* Brand colors - consistent across all themes */
    --brand-gradient-start: #6a00ff;
    --brand-gradient-end: #3b01b7;
    --brand-gradient-hover-start: #7d1aff;
    --brand-gradient-hover-end: #4b02e9;

    /* Input field gradients */
    --input-gradient-light-from: #f5f7fa;
    --input-gradient-light-via: #f8fafc;
    --input-gradient-light-to: #ffffff;
    --input-gradient-dark-from: #1e293b;
    --input-gradient-dark-via: #1e293b;
    --input-gradient-dark-to: #0f172a;

    /* Theme indicator colors */
    --theme-indicator-mist-outer: #f1f5f9;
    --theme-indicator-mist-inner: #adc3db;
    --theme-indicator-breeze-outer: #ffffff;
    --theme-indicator-breeze-inner: #d3b6fb;
    --theme-indicator-lumen-outer: #ffffff;
    --theme-indicator-lumen-inner: #71717a;
    --theme-indicator-carbon-outer: #f1f5f9;
    --theme-indicator-carbon-inner: #18181b;
    --theme-indicator-midnight-outer: #f1f5f9;
    --theme-indicator-midnight-inner: #1e3a8a;
    --theme-indicator-nebula-outer: #f1f5f9;
    --theme-indicator-nebula-inner: #50229f;

    /* overriding rsuite datepicker colors */
    .rs-theme-light {
      --rs-bg-active: hsl(var(--primary));
      --rs-text-active: hsl(var(--primary-foreground));
      --rs-input-focus-border: var(--color-border) !important;
      --rs-color-focus-ring: transparent !important;
      --rs-btn-primary-bg: hsl(var(--primary));
      --rs-btn-primary-hover-bg: hsl(var(--primary));
      --rs-state-hover-bg: hsl(var(--primary));
      --rs-listbox-option-hover-text: hsl(var(--secondary-foreground));
      --rs-listbox-option-hover-bg: hsl(var(--primary));
      --rs-calendar-cell-selected-hover-bg: hsl(var(--primary));
      --rs-calendar-time-unit-bg: var(--modal-bg) !important;
      --rs-calendar-date-selected-text: hsl(var(--secondary-foreground));
      --rs-btn-primary-text: hsl(var(--secondary-foreground));
      --rs-input-bg: hsl(var(--background));
      --rs-input-disabled-bg: hsl(var(--background));
    }

    .rs-picker-popup,
    .rs-calendar .rs-calendar-time-view,
    .rs-calendar-body,
    .rs-calendar-month-dropdown-row-wrapper,
    .rs-calendar-btn-close,
    .rs-calendar-time-dropdown {
      background-color: hsl(var(--background)) !important;
      border-color: var(--modal-border);
      border-width: 1px;
    }
  }

  /* Breeze Theme (Brand color light) */
  .breeze {
    --background: 0 0% 100%;
    --background-translucent: rgba(255, 255, 255, 0.7);
    --foreground: 260 30% 25%;

    --card: 0 0% 100%;
    --card-foreground: 260 30% 25%;

    --popover: 0 0% 100%;
    --popover-foreground: 260 30% 25%;

    --primary: 260 40% 40%;
    --primary-foreground: 0 0% 100%;

    --secondary: 260 30% 95%;
    --secondary-foreground: 260 40% 25%;

    --muted: 260 30% 95%;
    --muted-foreground: 260 30% 50%;
    --muted-text: 260 20% 45%;

    --accent: 260 40% 92%;
    --accent-foreground: 260 40% 25%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 260 30% 90%;
    --input: 260 30% 90%;
    --ring: 260 40% 40%;
    --ring-hover: 260 40% 50%;

    /* Theme indicator colors */
    --theme-indicator-mist-outer: #f1f5f9;
    --theme-indicator-mist-inner: #adc3db;
    --theme-indicator-breeze-outer: #ffffff;
    --theme-indicator-breeze-inner: #d3b6fb;
    --theme-indicator-lumen-outer: #ffffff;
    --theme-indicator-lumen-inner: #71717a;
    --theme-indicator-carbon-outer: #f1f5f9;
    --theme-indicator-carbon-inner: #18181b;
    --theme-indicator-midnight-outer: #f1f5f9;
    --theme-indicator-midnight-inner: #1e3a8a;
    --theme-indicator-nebula-outer: #f1f5f9;
    --theme-indicator-nebula-inner: #50229f;

    /* Themed Button Variables */
    --themed-btn-from: hsl(260 40% 30%);
    --themed-btn-to: hsl(260 45% 20%);
    --themed-btn-text: hsl(0 0% 100%);
    --themed-btn-hover-from: hsl(260 40% 40%);
    --themed-btn-hover-to: hsl(260 40% 30%);
    --themed-btn-shadow: rgba(76, 29, 149, 0.25);
    --themed-btn-shadow-hover: rgba(76, 29, 149, 0.35);
    --themed-btn-glow: rgba(76, 29, 149, 0.15);
    --themed-btn-glow-hover: rgba(76, 29, 149, 0.25);

    --thread-hover-bg: #f5f0ff;
    --thread-border-color: #e6deff;

    --counter-blue-bg: #f5f7ff;
    --counter-blue-text: #4f46e5;
    --counter-orange-bg: #fef6ee;
    --counter-orange-text: #ea580c;
    --counter-purple-bg: #f9f5ff;
    --counter-purple-text: #7c3aed;

    --highlight-1: #e6deff;

    --color-card-bg-light: #ffffff;
    --color-bg-inverse: #0f172a;
    --radius: 0.5rem;
    --color-bg-subtle: #ffffff;
    --color-text: #4a2a82;
    --color-text-placeholder: #64748b;
    --color-icon-muted: #64748b;
    --modal-bg: #ffffff;
    --modal-bg-rgb: 255, 255, 255;
    --modal-border: #e6deff;
    --modal-text: #4a2a82;
    --color-text-info: #6d28d9;
    --color-border: #e6deff;
    --color-text-error: #dc2626;
    --color-text-success: #059669;
    --color-text-warning: #ea580c;
    --color-bg-warning-muted: #fefce8;
    --color-bg-elevated: #f8f4ff;
    --color-bg-success-muted: #f0fdf4;
    --color-border-success: #10b981;
    --color-text-disabled: #94a3b8;
    --color-bg-error-muted: #fef2f2;
    --color-icon-info: #6d28d9;
    --color-bg-disabled: #f8fafc;
    --color-text-muted: #64748b;
    --color-border-magic: #6d28d9;
    --color-bg-magic-muted: #f5f0ff;
    --slack-code-color: #6d28d9;
    --slack-code-border: rgba(32, 32, 32, 0.13);
    --slack-code-block-bg: rgba(32, 32, 32, 0.06);
    --slack-blockquote-left-border: #e6deff;
    --color-widget-launcher: #2563eb;

    --color-strong: #f5f0ff;
    --color-table-header: #f5f0ff;
    --color-icon-interactive: #6d28d9;
    --color-border-primary: #6d28d9;
    --color-text-body: #4a2a82;
    --color-bg-info-muted: #f5f0ff;
    --color-bg-card: #fff;
    --color-bg-secondary: #f8fafc;
    --color-bg-dark-secondary: #27272a;
    --color-blue-1: #6d28d9;
    --color-orange-1: #ed9740;
    --color-gray-3: #e6deff;
    --color-green-3: #40ab8c;
    --color-red-1: #f86464;
    --color-gray-6: #94a3b8;
    --badge-bg: #fff;
    --badge-border: #e6deff;
    --card-hover: #f5f0ff;

    --color-progress: #6d28d9;
    --color-icon-magic: #6d28d9;
    --color-text-magic: #6d28d9;
    --text-disabled: #94a3b8;

    --color-bg-warning-strong: #eab308;
    --color-border-warning: #ca8a04;
    --color-progress-dot: #6d28d9;
    --color-progress-dot-inactive: #e6deff;

    --chart-1: 260 60% 50%;
    --chart-2: 160 60% 75%;
    --chart-3: 30 80% 70%;
    --chart-4: 240 60% 70%;
    --chart-5: 340 70% 70%;

    --online-border-color: #e6deff;
    --color-icon: #4a2a82;

    --kanban-card-bg: #fff;
    --kanban-board-bg: #ffffff;
    --kanban-column-bg: #f7f6fa;
    --kanban-card-shadow: 1px 1px 2px rgba(0, 0, 0, 0.04);
    --kanban-card-hover: #f5f0ff;

    /* New variables for mention styling */
    --mention-color: #6d28d9 !important;
    --mention-bg: rgba(109, 40, 217, 0.1) !important;
    --mention-padding: 0 4px !important;
    --mention-border-radius: 4px !important;
    --mention-font-weight: 500 !important;

    /* New variable for URL styling */
    --url-color: #6d28d9 !important;

    --color-bg-loader: #e6deff;
    --color-border-info-muted: #a78bfa;
    --color-border-error-muted: #f87171;
    --color-border-success-muted: #6ee7b7;
    --color-border-warning-muted: #fbbf24;
    --color-bg-warning-strong: #eab308;
    --color-border-warning: #ca8a04;
    --color-bg-success-strong: #059669;
    --color-hover-accent: #6a00ff;

    /* Accent colors for document and auth icons */
    --color-bg-document: #fdf2f8;
    --color-text-document: #db2777;
    --color-bg-auth: #e6fffa;
    --color-text-auth: #0d9488;

    /* Brand colors - consistent across all themes */
    --brand-gradient-start: #6a00ff;
    --brand-gradient-end: #3b01b7;
    --brand-gradient-hover-start: #7d1aff;
    --brand-gradient-hover-end: #4b02e9;

    /* overriding rsuite datepicker colors */
    .rs-theme-light {
      --rs-bg-active: hsl(var(--primary));
      --rs-text-active: hsl(var(--primary-foreground));
      --rs-input-focus-border: var(--color-border) !important;
      --rs-color-focus-ring: transparent !important;
      --rs-btn-primary-bg: hsl(var(--primary));
      --rs-btn-primary-hover-bg: hsl(var(--primary));
      --rs-state-hover-bg: hsl(var(--primary));
      --rs-listbox-option-hover-text: hsl(var(--secondary-foreground));
      --rs-listbox-option-hover-bg: hsl(var(--primary));
      --rs-calendar-cell-selected-hover-bg: hsl(var(--primary));
      --rs-calendar-time-unit-bg: var(--modal-bg) !important;
      --rs-calendar-date-selected-text: hsl(var(--secondary-foreground));
      --rs-btn-primary-text: hsl(var(--secondary-foreground));
      --rs-input-bg: hsl(var(--background));
      --rs-input-disabled-bg: hsl(var(--background));
    }

    .rs-picker-popup,
    .rs-calendar .rs-calendar-time-view,
    .rs-calendar-body,
    .rs-calendar-month-dropdown-row-wrapper,
    .rs-calendar-btn-close,
    .rs-calendar-time-dropdown {
      background-color: hsl(var(--background)) !important;
      border-color: var(--modal-border);
      border-width: 1px;
    }

    /* Chart colors for insights page */
    --chart-color-1: rgb(155, 135, 245);
    /* main purple */
    --chart-color-2: rgb(179, 164, 246);
    /* lighter purple */
    --chart-color-3: rgb(125, 93, 233);
    /* deeper purple */
    --chart-color-4: rgb(171, 152, 245);
    /* light purple */
    --chart-color-5: rgb(140, 117, 242);
    /* medium purple */
    --chart-color-6: rgb(186, 170, 246);
    /* lightest purple */
    --chart-color-7: rgb(135, 112, 240);
    /* vibrant purple */
    --chart-color-8: rgb(165, 145, 245);
    /* soft purple */
    --chart-color-9: rgb(145, 125, 242);
    /* medium-light purple */
    --chart-color-10: rgb(130, 103, 235);
    /* medium-deep purple */

    /* Input field gradients */
    --input-gradient-light-from: #f8f5ff;
    --input-gradient-light-via: #fbfaff;
    --input-gradient-light-to: #ffffff;
    --input-gradient-dark-from: #4a1d96;
    --input-gradient-dark-via: #4a1d96;
    --input-gradient-dark-to: #3b0764;
  }

  /* Lumen Theme (Neutral light) */
  .lumen {
    --background: 0 0% 100%;
    --background-translucent: rgba(255, 255, 255, 0.7);
    --foreground: 0 0% 15%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 15%;

    --primary: 0 0% 11.2%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 95%;
    --secondary-foreground: 0 0% 25%;

    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 60%;
    --muted-text: 0 0% 50%;

    --accent: 0 0% 95%;
    --accent-foreground: 0 0% 25%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 40%;

    /* Theme indicator colors */
    --theme-indicator-mist-outer: #f1f5f9;
    --theme-indicator-mist-inner: #adc3db;
    --theme-indicator-breeze-outer: #ffffff;
    --theme-indicator-breeze-inner: #d3b6fb;
    --theme-indicator-lumen-outer: #ffffff;
    --theme-indicator-lumen-inner: #71717a;
    --theme-indicator-carbon-outer: #f1f5f9;
    --theme-indicator-carbon-inner: #18181b;
    --theme-indicator-midnight-outer: #f1f5f9;
    --theme-indicator-midnight-inner: #1e3a8a;
    --theme-indicator-nebula-outer: #f1f5f9;
    --theme-indicator-nebula-inner: #50229f;

    /* Commented out Thena-native variables */
    /*
    --thena-native-start: #2c2c35;
    --thena-native-end: #18181b;
    --thena-native-hover-start: #3f3f46;
    --thena-native-hover-end: #27272a;
    --thena-native-border: #6b7280;
    */

    /* Themed Button Variables */
    --themed-btn-from: hsl(0 0% 20%);
    --themed-btn-to: hsl(0 0% 10%);
    --themed-btn-text: hsl(0 0% 100%);
    --themed-btn-hover-from: hsl(0 0% 30%);
    --themed-btn-hover-to: hsl(0 0% 20%);
    --themed-btn-shadow: rgba(33, 33, 33, 0.25);
    --themed-btn-shadow-hover: rgba(33, 33, 33, 0.35);
    --themed-btn-glow: rgba(33, 33, 33, 0.15);
    --themed-btn-glow-hover: rgba(33, 33, 33, 0.25);

    --thread-hover-bg: #f9fafb;
    --thread-border-color: #e5e5e5;

    --counter-blue-bg: #f5faff;
    --counter-blue-text: #2563eb;
    --counter-orange-bg: #fef8f1;
    --counter-orange-text: #dd6b20;
    --counter-purple-bg: #faf5ff;
    --counter-purple-text: #7e22ce;

    --highlight-1: #e5e5e5;

    --color-card-bg-light: #ffffff;
    --color-bg-inverse: #1f1f1f;
    --radius: 0.5rem;
    --color-bg-subtle: #f9f9f9;
    --color-text: #1f1f1f;
    --color-text-placeholder: #6b6b6b;
    --color-icon-muted: #6b6b6b;
    --modal-bg: #ffffff;
    --modal-bg-rgb: 255, 255, 255;
    --modal-border: #e5e5e5;
    --modal-text: #1f1f1f;
    --color-text-info: #404040;
    --color-border: #e5e5e5;
    --color-text-error: #dc2626;
    --color-text-success: #059669;
    --color-text-warning: #ea580c;
    --color-bg-warning-muted: #f9f9f9;
    --color-bg-elevated: #f1f1f1;
    --color-bg-success-muted: #f9f9f9;
    --color-border-success: #10b981;
    --color-text-disabled: #9c9c9c;
    --color-bg-error-muted: #f9f9f9;
    --color-icon-info: #404040;
    --color-bg-disabled: #f9f9f9;
    --color-text-muted: #6b6b6b;
    --color-border-magic: #8b8b8b;
    --color-bg-magic-muted: #f5f5f5;
    --slack-code-color: #404040;
    --slack-code-border: rgba(32, 32, 32, 0.13);
    --slack-code-block-bg: rgba(32, 32, 32, 0.06);
    --slack-blockquote-left-border: #dddddd;
    --color-widget-launcher: #2563eb;

    --color-strong: #f1f1f1;
    --color-table-header: #f3f3f3;
    --color-icon-interactive: #4b4b4b;
    --color-border-primary: #373737;
    --color-text-body: #4b4b4b;
    --color-bg-info-muted: #f5f5f5;
    --color-bg-card: #fff;
    --color-bg-secondary: #f3f3f3;
    --color-bg-dark-secondary: #373737;
    --color-blue-1: #4d4d4d;
    --color-orange-1: #4d4d4d;
    --color-gray-3: #d1d1d1;
    --color-green-3: #10b981;
    --color-red-1: #ef4444;
    --color-gray-6: #9c9c9c;
    --badge-bg: #fff;
    --badge-border: #e5e5e5;
    --card-hover: #f3f3f3;

    --color-progress: #6b6b6b;
    --color-escalation-greyscaled: #6b6b6b;
    --color-icon-magic: #6b6b6b;
    --color-text-magic: #6b6b6b;
    --text-disabled: #9c9c9c;

    --color-bg-warning-strong: #6b6b6b;
    --color-border-warning: #ca8a04;
    --color-progress-dot: #6b6b6b;
    --color-progress-dot-inactive: #e5e5e5;

    --chart-1: 0 0% 30%;
    --chart-2: 0 0% 45%;
    --chart-3: 0 0% 60%;
    --chart-4: 0 0% 75%;
    --chart-5: 0 0% 20%;

    --online-border-color: #f3f3f3;
    --color-icon: #373737;

    --kanban-card-bg: #fff;
    --kanban-board-bg: #f9f9f9;
    --kanban-column-bg: #f9f9f9;
    --kanban-card-shadow: 1px 1px 2px rgba(0, 0, 0, 0.04);
    --kanban-card-hover: #f1f1f1;

    /* New variables for mention styling */
    --mention-color: #1263A3 !important;
    --mention-bg: #EBF5FA !important;
    --mention-padding: 0 4px !important;
    --mention-border-radius: 4px !important;
    --mention-font-weight: 500 !important;

    /* New variable for URL styling */
    --url-color: #1263A3 !important;

    --color-bg-loader: #e5e5e5;
    --color-border-info-muted: #6b6b6b;
    --color-border-error-muted: #f87171;
    --color-border-success-muted: #6ee7b7;
    --color-border-warning-muted: #9c9c9c;
    --color-bg-warning-strong: #6b6b6b;
    --color-border-warning: #6b6b6b;
    --color-bg-success-strong: #059669;
    --color-hover-accent: #6b6b6b;

    /* Accent colors for document and auth icons */
    --color-bg-document: #fdf2f8;
    --color-text-document: #db2777;
    --color-bg-auth: #e6fffa;
    --color-text-auth: #0d9488;

    /* Brand colors - consistent across all themes */
    --brand-gradient-start: #6a00ff;
    --brand-gradient-end: #3b01b7;
    --brand-gradient-hover-start: #7d1aff;
    --brand-gradient-hover-end: #4b02e9;

    /* overriding rsuite datepicker colors */
    .rs-theme-light {
      --rs-bg-active: hsl(var(--primary));
      --rs-text-active: hsl(var(--primary-foreground));
      --rs-input-focus-border: var(--color-border) !important;
      --rs-color-focus-ring: transparent !important;
      --rs-btn-primary-bg: hsl(var(--primary));
      --rs-btn-primary-hover-bg: hsl(var(--primary));
      --rs-state-hover-bg: hsl(var(--primary));
      --rs-listbox-option-hover-text: hsl(var(--secondary-foreground));
      --rs-listbox-option-hover-bg: hsl(var(--primary));
      --rs-calendar-cell-selected-hover-bg: hsl(var(--primary));
      --rs-calendar-time-unit-bg: var(--modal-bg) !important;
      --rs-calendar-date-selected-text: hsl(var(--secondary-foreground));
      --rs-btn-primary-text: hsl(var(--secondary-foreground));
      --rs-input-bg: hsl(var(--background));
      --rs-input-disabled-bg: hsl(var(--background));
    }

    .rs-picker-popup,
    .rs-calendar .rs-calendar-time-view,
    .rs-calendar-body,
    .rs-calendar-month-dropdown-row-wrapper,
    .rs-calendar-btn-close,
    .rs-calendar-time-dropdown {
      background-color: hsl(var(--background)) !important;
      border-color: var(--modal-border);
      border-width: 1px;
    }

    /* Chart colors for insights page */
    --chart-color-1: rgb(107, 114, 128);
    /* medium gray */
    --chart-color-2: rgb(156, 163, 175);
    /* lighter gray */
    --chart-color-3: rgb(75, 85, 99);
    /* dark gray */
    --chart-color-4: rgb(209, 213, 219);
    /* very light gray */
    --chart-color-5: rgb(120, 127, 141);
    /* medium-light gray */
    --chart-color-6: rgb(85, 95, 110);
    /* medium-dark gray */
    --chart-color-7: rgb(176, 183, 195);
    /* light gray with slight blue */
    --chart-color-8: rgb(96, 104, 118);
    /* medium gray with slight blue */
    --chart-color-9: rgb(140, 147, 161);
    /* medium gray with slight blue */
    --chart-color-10: rgb(65, 75, 89);
    /* dark gray with slight blue */

    /* Input field gradients */
    --input-gradient-light-from: #f7f7f7;
    --input-gradient-light-via: #fafafa;
    --input-gradient-light-to: #ffffff;
    --input-gradient-dark-from: #27272a;
    --input-gradient-dark-via: #27272a;
    --input-gradient-dark-to: #18181b;
  }

  /* Carbon Theme (Neutral dark) */
  .carbon {
    --background: 220 3% 3.9%;
    --background-translucent: rgba(0, 0, 0, 0.7);
    --foreground: 0 0% 98%;

    --card: 220 3% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 220 3% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 220 3% 10%;

    --secondary: 220 3% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 220 3% 15.9%;
    --muted-foreground: 220 3% 64.9%;
    --muted-text: 220 3% 64.9%;

    --accent: 220 3% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84% 60%;
    /* #FA5252 - A light red color */
    --destructive-foreground: 0 0% 98%;
    /* #FAFAFA - Almost white */

    --border: 220 3% 15.9%;
    --input: 220 3% 15.9%;
    --ring: 224.3 76.3% 48%;

    /* Commented out Thena-native variables */
    /*
    --thena-native-start: #e2e8f0;
    --thena-native-end: #cbd5e1;
    --thena-native-hover-start: #f1f5f9;
    --thena-native-hover-end: #e2e8f0;
    --thena-native-border: #0f172a;
    */

    /* Themed Button Variables */
    --themed-btn-from: hsl(0 0% 98%);
    --themed-btn-to: hsl(0 0% 88%);
    --themed-btn-text: hsl(220 3% 15%);
    --themed-btn-hover-from: hsl(0 0% 100%);
    --themed-btn-hover-to: hsl(0 0% 94%);
    --themed-btn-shadow: rgba(255, 255, 255, 0.25);
    --themed-btn-shadow-hover: rgba(255, 255, 255, 0.35);
    --themed-btn-glow: rgba(255, 255, 255, 0.15);
    --themed-btn-glow-hover: rgba(255, 255, 255, 0.25);

    --thread-hover-bg: #1f2124;
    --thread-border-color: #2f3033;

    --counter-blue-bg: #1a1f2e;
    --counter-blue-text: #93c5fd;
    --counter-orange-bg: #1f1915;
    --counter-orange-text: #fcd34d;
    --counter-purple-bg: #1e1a24;
    --counter-purple-text: #d8b4fe;

    --highlight-1: #242424;

    --color-card-bg-light: #1a1c21;
    --color-bg-inverse: #f8fafc;
    --radius: 0.5rem;
    --color-bg-subtle: #0c0c0d;
    --color-text: #e2e2e2;
    --color-text-placeholder: #7a7a7a;
    --color-icon-muted: #7a7a7a;
    --modal-bg: #0c0c0c;
    --modal-bg-rgb: 12, 12, 12;
    --modal-border: #2f3033;
    --modal-text: #e2e2e2;
    --color-text-info: #a0a0a0;
    --color-border: #2b2b2b;
    --color-text-error: #f87171;
    --color-text-success: #6ee7b7;
    --color-text-warning: #fb923c;
    --color-bg-warning-muted: #422006;
    --color-bg-elevated: #1a1c21;
    --color-bg-success-muted: #052e16;
    --color-border-success: #10b981;
    --color-text-disabled: #7a7a7a;
    --color-bg-error-muted: #450a0a;
    --color-icon-info: #a0a0a0;
    --color-bg-disabled: #0c0c0d;
    --color-text-muted: #7a7a7a;
    --color-border-magic: #3f1b76;
    --color-bg-magic-muted: #a78bfa;
    --slack-code-color: #e8912d;
    --slack-code-border: rgba(232, 232, 232, 0.13);
    --slack-code-block-bg: rgba(232, 232, 232, 0.06);
    --slack-blockquote-left-border: rgb(82, 82, 82);
    --color-widget-launcher: #2563eb;

    --color-strong: #1f2124;
    --color-table-header: #1a1c21;
    --color-icon-interactive: #d1d1d1;
    --color-border-primary: #e2e2e2;
    --color-text-body: #d1d1d1;
    --color-bg-info-muted: #2a2a2a;
    --color-bg-card: #000000;
    --color-bg-secondary: #1a1c21;
    --color-bg-dark-secondary: #000000;
    --color-blue-1: #a0a0a0;
    --color-orange-1: #fb923c;
    --color-gray-3: #242424;
    --color-green-3: #34d399;
    --color-red-1: #f87171;
    --color-gray-6: #9ca3af;
    --badge-bg: #1f1f1f;
    --badge-border: #2f2f2f;
    --card-hover: #242424;

    --color-progress: #f472b6;
    --color-escalation-greyscaled: #6f6f75;
    --color-icon-magic: #c084fc;
    --text-disabled: #989fa6;
    --color-text-magic: #c084fc;

    --kanban-card-bg: #151515;
    --kanban-board-bg: #000000;
    --kanban-column-bg: #0c0c0d;
    --kanban-card-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    --kanban-card-hover: #1c1c1c;

    /* New variables for mention styling */
    --mention-color: #60A5FA !important;
    --mention-bg: rgba(96, 165, 250, 0.15) !important;
    --mention-padding: 0 4px !important;
    --mention-border-radius: 4px !important;
    --mention-font-weight: 500 !important;

    /* New variable for URL styling */
    --url-color: #60A5FA !important;

    --color-bg-loader: #242424;
    --color-progress-dot: #7a01ff;
    --color-progress-dot-inactive: #ededed;

    --chart-1: 220 80% 55%;
    --chart-2: 160 70% 50%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 70% 60%;
    --chart-5: 330 80% 55%;

    --color-icon: #d1d1d1;
    --color-border-info-muted: #4d4d4d;
    --color-border-error-muted: #ef4444;
    --color-border-success-muted: #10b981;
    --color-border-warning-muted: #f59e0b;
    --color-bg-warning-strong: #d97706;
    --color-border-warning: #b45309;
    --color-bg-success-strong: #059669;
    --color-hover-accent: #242424;
    --online-border-color: #000000;

    /* Accent colors for document and auth icons */
    --color-bg-document: rgba(219, 39, 119, 0.2);
    --color-text-document: #f472b6;
    --color-bg-auth: rgba(13, 148, 136, 0.2);
    --color-text-auth: #2dd4bf;

    /* Brand colors - consistent across all themes */
    --brand-gradient-start: #6a00ff;
    --brand-gradient-end: #3b01b7;
    --brand-gradient-hover-start: #7d1aff;
    --brand-gradient-hover-end: #4b02e9;

    /* overriding rsuite datepicker colors */
    .rs-theme-dark {
      --rs-bg-active: hsl(var(--primary));
      --rs-text-active: hsl(var(--primary-foreground));
      --rs-input-focus-border: var(--color-border) !important;
      --rs-color-focus-ring: transparent !important;
      --rs-btn-primary-bg: hsl(var(--primary));
      --rs-btn-primary-hover-bg: hsl(var(--primary));
      --rs-state-hover-bg: hsl(var(--primary));
      --rs-listbox-option-hover-text: hsl(var(--secondary-foreground));
      --rs-listbox-option-hover-bg: hsl(var(--primary));
      --rs-calendar-cell-selected-hover-bg: hsl(var(--primary));
      --rs-calendar-time-unit-bg: var(--modal-bg) !important;
      --rs-calendar-date-selected-text: hsl(var(--secondary-foreground));
      --rs-btn-primary-text: hsl(var(--secondary-foreground));
      --rs-input-bg: hsl(var(--background));
      --rs-input-disabled-bg: hsl(var(--background));
    }

    .rs-picker-popup,
    .rs-calendar .rs-calendar-time-view,
    .rs-calendar-body,
    .rs-calendar-month-dropdown-row-wrapper,
    .rs-calendar-btn-close,
    .rs-calendar-time-dropdown {
      background-color: hsl(var(--background)) !important;
      border-color: var(--modal-border);
      border-width: 1px;
    }

    /* Theme indicator colors */
    --theme-indicator-mist-outer: #f1f5f9;
    --theme-indicator-mist-inner: #adc3db;
    --theme-indicator-breeze-outer: #ffffff;
    --theme-indicator-breeze-inner: #d3b6fb;
    --theme-indicator-lumen-outer: #ffffff;
    --theme-indicator-lumen-inner: #71717a;
    --theme-indicator-carbon-outer: #f1f5f9;
    --theme-indicator-carbon-inner: #18181b;
    --theme-indicator-midnight-outer: #f1f5f9;
    --theme-indicator-midnight-inner: #1e3a8a;
    --theme-indicator-nebula-outer: #f1f5f9;
    --theme-indicator-nebula-inner: #50229f;

    /* Chart colors for insights page */
    --chart-color-1: rgb(59, 170, 220);
    /* bright blue (inspired by Midnight) */
    --chart-color-2: rgb(110, 231, 183);
    /* light green (from Midnight) */
    --chart-color-3: rgb(192, 132, 252);
    /* light purple (from Midnight) */
    --chart-color-4: rgb(253, 186, 116);
    /* light orange */
    --chart-color-5: rgb(147, 197, 253);
    /* very light blue */
    --chart-color-6: rgb(251, 191, 36);
    /* yellow (brighter) */
    --chart-color-7: rgb(134, 239, 172);
    /* bright green (from Midnight) */
    --chart-color-8: rgb(249, 168, 212);
    /* pink (from Midnight) */
    --chart-color-9: rgb(202, 138, 4);
    /* amber */
    --chart-color-10: rgb(167, 139, 250);
    /* light violet (from Midnight) */

    /* Input field gradients */
    --input-gradient-light-from: #f7f7f7;
    --input-gradient-light-via: #fafafa;
    --input-gradient-light-to: #ffffff;
    --input-gradient-dark-from: #18181b;
    --input-gradient-dark-via: #18181b;
    --input-gradient-dark-to: #09090b;
  }

  /* Nebula Theme (Brand color dark) */
  .nebula {
    --background: 260 30% 8%;
    --background-translucent: rgba(18, 15, 29, 0.7);
    --foreground: 0 0% 95%;

    --card: 260 25% 12%;
    --card-foreground: 0 0% 95%;

    --popover: 260 25% 12%;
    --popover-foreground: 0 0% 95%;

    --primary-gradient: linear-gradient(to bottom,
        var(--brand-gradient-start),
        var(--brand-gradient-end));
    --primary-hover-gradient: linear-gradient(to bottom,
        var(--brand-gradient-hover-start),
        var(--brand-gradient-hover-end));
    --primary: 260 30% 90%;
    --primary-foreground: 260 30% 15%;

    --secondary: 260 20% 20%;
    --secondary-foreground: 0 0% 95%;

    --muted: 260 20% 18%;
    --muted-foreground: 0 0% 70%;
    --muted-text: 260 10% 70%;

    --accent: 265 40% 25%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 95%;

    --border: 260 20% 20%;
    --input: 260 20% 20%;
    --ring: 260 30% 90%;

    /* Commented out Thena-native variables */
    /*
    --thena-native-start: #7c3aed;
    --thena-native-end: #6d28d9;
    --thena-native-hover-start: #8b5cf6;
    --thena-native-hover-end: #7c3aed;
    --thena-native-border: #2e1065;
    */

    /* Themed Button Variables */
    --themed-btn-from: hsl(260 60% 90%);
    --themed-btn-to: hsl(260 65% 80%);
    --themed-btn-text: hsl(260 30% 15%);
    --themed-btn-hover-from: hsl(260 60% 95%);
    --themed-btn-hover-to: hsl(260 60% 85%);
    --themed-btn-shadow: rgba(216, 180, 254, 0.25);
    --themed-btn-shadow-hover: rgba(216, 180, 254, 0.35);
    --themed-btn-glow: rgba(216, 180, 254, 0.15);
    --themed-btn-glow-hover: rgba(216, 180, 254, 0.25);

    --thread-hover-bg: #252131;
    --thread-border-color: #302a3d;

    --counter-blue-bg: #1a1a2e;
    --counter-blue-text: #bfdbfe;
    --counter-orange-bg: #1e1813;
    --counter-orange-text: #fcd34d;
    --counter-purple-bg: #1d1526;
    --counter-purple-text: #e9d5ff;

    --highlight-1: #302a3d;

    --color-card-bg-light: #252131;
    --color-bg-inverse: #f8fafc;
    --radius: 0.5rem;
    --color-bg-subtle: #1a1724;
    --color-text: #f8fafc;
    --color-text-placeholder: #a1a1aa;
    --color-icon-muted: #a1a1aa;
    --modal-bg: #15131d;
    --modal-bg-rgb: 21, 19, 29;
    --modal-border: #302a3d;
    --modal-text: #f8fafc;
    --color-text-info: #a5b4fc;
    --color-border: #302a3d;
    --color-text-error: #f87171;
    --color-text-success: #6ee7b7;
    --color-text-warning: #fb923c;
    --color-bg-warning-muted: #422006;
    --color-bg-elevated: #252131;
    --color-bg-success-muted: #064e3b;
    --color-border-success: #10b981;
    --color-text-disabled: #71717a;
    --color-bg-error-muted: #450a0a;
    --color-icon-info: #a5b4fc;
    --color-bg-disabled: #1a1724;
    --color-text-muted: #a1a1aa;
    --color-border-magic: #7c3aed;
    --color-bg-magic-muted: #3b2c62;
    --slack-code-color: #d4d4d8;
    --slack-code-border: rgba(232, 232, 232, 0.13);
    --slack-code-block-bg: rgba(232, 232, 232, 0.06);
    --slack-blockquote-left-border: #302a3d;
    --color-widget-launcher: #2563eb;

    --color-strong: #252131;
    --color-table-header: #252131;
    --color-icon-interactive: #d8cbfc;
    --color-border-primary: #d8cbfc;
    --color-text-body: #f1f5f9;
    --color-bg-info-muted: #1e3a8a;
    --color-bg-card: #15131d;
    --color-bg-secondary: #252131;
    --color-bg-dark-secondary: #15131d;
    --color-blue-1: #60a5fa;
    --color-orange-1: #fb923c;
    --color-gray-3: #302a3d;
    --color-green-3: #34d399;
    --color-red-1: #f87171;
    --color-gray-6: #9ca3af;
    --badge-bg: #2e2442;
    --badge-border: #4a3a6b;
    --card-hover: #302a3d;

    --text-disabled: #6b7280;

    --kanban-card-bg: #252131;
    --kanban-board-bg: #15131d;
    --kanban-column-bg: #161320;
    --kanban-card-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    --kanban-card-hover: #302a3d;

    /* New variables for mention styling */
    --mention-color: #A5B4FC !important;
    --mention-bg: rgba(165, 180, 252, 0.15) !important;
    --mention-padding: 0 4px !important;
    --mention-border-radius: 4px !important;
    --mention-font-weight: 500 !important;

    /* New variable for URL styling */
    --url-color: #A5B4FC !important;

    --color-bg-loader: #302a3d;
    --color-progress: #c4b5fd;
    --color-progress-dot: #c4b5fd;
    --color-progress-dot-inactive: #302a3d;
    --color-escalation-greyscaled: #6f6f75;

    --chart-1: 265 40% 75%;
    --chart-2: 220 70% 60%;
    --chart-3: 180 70% 60%;
    --chart-4: 300 70% 60%;
    --chart-5: 340 70% 60%;

    --color-icon: #f1f5f9;
    --color-border-info-muted: #3b82f6;
    --color-border-error-muted: #ef4444;
    --color-border-success-muted: #10b981;
    --color-border-warning-muted: #f59e0b;
    --color-bg-warning-strong: #d97706;
    --color-border-warning: #b45309;
    --color-bg-success-strong: #059669;
    --color-hover-accent: #c4b5fd;
    --online-border-color: #15131d;

    /* Accent colors for document and auth icons */
    --color-bg-document: rgba(219, 39, 119, 0.2);
    --color-text-document: #f472b6;
    --color-bg-auth: rgba(13, 148, 136, 0.2);
    --color-text-auth: #2dd4bf;

    /* Brand colors - consistent across all themes */
    --brand-gradient-start: #6a00ff;
    --brand-gradient-end: #3b01b7;
    --brand-gradient-hover-start: #7d1aff;
    --brand-gradient-hover-end: #4b02e9;

    /* overriding rsuite datepicker colors */
    .rs-theme-dark {
      --rs-bg-active: var(--primary-gradient);
      --rs-text-active: hsl(var(--primary-foreground));
      --rs-input-focus-border: var(--color-border) !important;
      --rs-color-focus-ring: transparent !important;
      --rs-btn-primary-bg: var(--primary-gradient);
      --rs-btn-primary-hover-bg: var(--primary-hover-gradient);
      --rs-state-hover-bg: var(--primary-gradient);
      --rs-listbox-option-hover-text: hsl(var(--secondary-foreground));
      --rs-listbox-option-hover-bg: var(--primary-gradient);
      --rs-calendar-cell-selected-hover-bg: var(--primary-gradient);
      --rs-calendar-time-unit-bg: var(--modal-bg) !important;
      --rs-calendar-date-selected-text: hsl(var(--secondary-foreground));
      --rs-btn-primary-text: hsl(var(--secondary-foreground));
      --rs-input-bg: hsl(var(--background));
      --rs-input-disabled-bg: hsl(var(--background));
    }

    .rs-picker-popup,
    .rs-calendar .rs-calendar-time-view,
    .rs-calendar-body,
    .rs-calendar-month-dropdown-row-wrapper,
    .rs-calendar-btn-close,
    .rs-calendar-time-dropdown {
      background-color: hsl(var(--background)) !important;
      border-color: var(--modal-border);
      border-width: 1px;
    }

    /* Theme indicator colors */
    --theme-indicator-mist-outer: #f1f5f9;
    --theme-indicator-mist-inner: #adc3db;
    --theme-indicator-breeze-outer: #ffffff;
    --theme-indicator-breeze-inner: #d3b6fb;
    --theme-indicator-lumen-outer: #ffffff;
    --theme-indicator-lumen-inner: #71717a;
    --theme-indicator-carbon-outer: #f1f5f9;
    --theme-indicator-carbon-inner: #18181b;
    --theme-indicator-midnight-outer: #f1f5f9;
    --theme-indicator-midnight-inner: #1e3a8a;
    --theme-indicator-nebula-outer: #f1f5f9;
    --theme-indicator-nebula-inner: #50229f;

    /* Chart colors for insights page */
    --chart-color-1: rgb(139, 92, 246);
    /* bright purple */
    --chart-color-2: rgb(167, 139, 250);
    /* light purple */
    --chart-color-3: rgb(124, 58, 237);
    /* medium purple */
    --chart-color-4: rgb(196, 181, 253);
    /* very light purple */
    --chart-color-5: rgb(109, 40, 217);
    /* deep purple */
    --chart-color-6: rgb(153, 119, 247);
    /* medium-light purple */
    --chart-color-7: rgb(181, 156, 251);
    /* light-medium purple */
    --chart-color-8: rgb(130, 75, 240);
    /* medium-deep purple */
    --chart-color-9: rgb(116, 50, 230);
    /* deep-medium purple */
    --chart-color-10: rgb(147, 103, 246);
    /* bright-medium purple */

    /* Input field gradients */
    --input-gradient-light-from: #f5f3ff;
    --input-gradient-light-via: #f8f7ff;
    --input-gradient-light-to: #ffffff;
    --input-gradient-dark-from: #2a1a4a;
    --input-gradient-dark-via: #251a40;
    --input-gradient-dark-to: #1f1937;
  }

  /* Midnight Theme (Bluish dark) */
  .midnight {
    --background: 222.2 84% 4.9%;
    --background-translucent: rgba(2, 6, 23, 0.7);
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --muted-text: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --thena-native-start: #93c5fd;
    --thena-native-end: #60a5fa;
    --thena-native-hover-start: #bfdbfe;
    --thena-native-hover-end: #93c5fd;
    --thena-native-border: #1e3a8a;

    /* Themed Button Variables */
    --themed-btn-from: hsl(213 94% 85%);
    --themed-btn-to: hsl(213 94% 75%);
    --themed-btn-text: hsl(222.2 47.4% 15%);
    --themed-btn-hover-from: hsl(213 94% 90%);
    --themed-btn-hover-to: hsl(213 94% 80%);
    --themed-btn-shadow: rgba(147, 197, 253, 0.25);
    --themed-btn-shadow-hover: rgba(147, 197, 253, 0.35);
    --themed-btn-glow: rgba(147, 197, 253, 0.15);
    --themed-btn-glow-hover: rgba(147, 197, 253, 0.25);

    /* Theme indicator colors */
    --theme-indicator-mist-outer: #ffffff;
    --theme-indicator-mist-inner: #2e4053;
    --theme-indicator-breeze-outer: #ffffff;
    --theme-indicator-breeze-inner: #3b82f6;
    --theme-indicator-lumen-outer: #ffffff;
    --theme-indicator-lumen-inner: #71717a;
    --theme-indicator-carbon-outer: #f1f5f9;
    --theme-indicator-carbon-inner: #18181b;
    --theme-indicator-midnight-outer: #f1f5f9;
    --theme-indicator-midnight-inner: #1e3a8a;
    --theme-indicator-nebula-outer: #f1f5f9;
    --theme-indicator-nebula-inner: #50229f;

    --thread-hover-bg: #1a2333;
    --thread-border-color: #334155;

    --counter-blue-bg: #0f1c2a;
    --counter-blue-text: #bfdbfe;
    --counter-orange-bg: #1a1612;
    --counter-orange-text: #fcd34d;
    --counter-purple-bg: #1a1425;
    --counter-purple-text: #e9d5ff;

    --highlight-1: #334155;

    --color-card-bg-light: #131a2a;
    --color-bg-inverse: #f8fafc;
    --radius: 0.5rem;
    --color-bg-subtle: #0a1020;
    --color-text: #f1f5f9;
    --color-text-placeholder: #94a3b8;
    --color-icon-muted: #94a3b8;
    --modal-bg: #0a0a0a;
    --modal-bg-rgb: 10, 10, 10;
    --modal-border: #27272a;
    --modal-text: #f4f4f5;
    --color-text-info: #60a5fa;
    --color-border: #334155;
    --color-text-error: #f87171;
    --color-text-success: #6ee7b7;
    --color-text-warning: #fb923c;
    --color-bg-warning-muted: #422006;
    --color-bg-elevated: #131a2a;
    --color-bg-success-muted: #052e16;
    --color-border-success: #10b981;
    --color-text-disabled: #94a3b8;
    --color-bg-error-muted: #450a0a;
    --color-icon-info: #60a5fa;
    --color-bg-disabled: #0a1020;
    --color-text-muted: #94a3b8;
    --color-border-magic: #2e1065;
    --color-bg-magic-muted: #a78bfa;
    --slack-code-color: #e8912d;
    --slack-code-border: rgba(232, 232, 232, 0.13);
    --slack-code-block-bg: rgba(232, 232, 232, 0.06);
    --slack-blockquote-left-border: rgb(82, 82, 82);
    --color-widget-launcher: #2563eb;

    --color-strong: #1a2333;
    --color-table-header: #131a2a;
    --color-icon-interactive: #bfdbfe;
    --color-border-primary: #e2e8f0;
    --color-text-body: #e2e8f0;
    --color-bg-info-muted: #1e3a8a;
    --color-bg-card: #020617;
    --color-bg-secondary: #131a2a;
    --color-bg-dark-secondary: #020617;
    --color-blue-1: #60a5fa;
    --color-orange-1: #fb923c;
    --color-gray-3: #334155;
    --color-green-3: #34d399;
    --color-red-1: #f87171;
    --color-gray-6: #9ca3af;
    --badge-bg: #020617;
    --badge-border: #334155;
    --card-hover: #1a2333;
    --thread-hover-bg: #1a2333;
    --color-progress: #f472b6;
    --color-escalation-greyscaled: #6f6f75;
    --color-icon-magic: #c084fc;
    --color-text-magic: #c084fc;
    --text-disabled: #cbd5e1;
    --color-bg-card: #020617;

    --kanban-card-bg: #131c30;
    --kanban-board-bg: #020618;
    --kanban-column-bg: #0a1020;
    --kanban-card-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    --kanban-card-hover: #1a2333;

    /* New variables for mention styling */
    --mention-color: #93C5FD !important;
    --mention-bg: rgba(147, 197, 253, 0.15) !important;
    --mention-padding: 0 4px !important;
    --mention-border-radius: 4px !important;
    --mention-font-weight: 500 !important;

    /* New variable for URL styling */
    --url-color: #93C5FD !important;

    --color-bg-loader: #334155;
    --color-progress-dot: #7a01ff;
    --color-progress-dot-inactive: #ededed;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 70% 60%;
    --chart-5: 330 80% 55%;

    --color-icon: #e2e8f0;
    --color-border-info-muted: #3b82f6;
    --color-border-error-muted: #ef4444;
    --color-border-success-muted: #10b981;
    --color-border-warning-muted: #f59e0b;
    --color-bg-warning-strong: #d97706;
    --color-border-warning: #b45309;
    --color-bg-success-strong: #059669;
    --color-hover-accent: #3b82f6;
    --online-border-color: #020617;

    /* Accent colors for document and auth icons */
    --color-bg-document: rgba(219, 39, 119, 0.2);
    --color-text-document: #f472b6;
    --color-bg-auth: rgba(13, 148, 136, 0.2);
    --color-text-auth: #2dd4bf;

    /* Brand colors - consistent across all themes */
    --brand-gradient-start: #6a00ff;
    --brand-gradient-end: #3b01b7;
    --brand-gradient-hover-start: #7d1aff;
    --brand-gradient-hover-end: #4b02e9;

    /* overriding rsuite datepicker colors */
    .rs-theme-dark {
      --rs-bg-active: hsl(var(--primary));
      --rs-text-active: hsl(var(--primary-foreground));
      --rs-input-focus-border: var(--color-border) !important;
      --rs-color-focus-ring: transparent !important;
      --rs-btn-primary-bg: hsl(var(--primary));
      --rs-btn-primary-hover-bg: hsl(var(--primary));
      --rs-state-hover-bg: hsl(var(--primary));
      --rs-listbox-option-hover-text: hsl(var(--secondary-foreground));
      --rs-listbox-option-hover-bg: hsl(var(--primary));
      --rs-calendar-cell-selected-hover-bg: hsl(var(--primary));
      --rs-calendar-time-unit-bg: var(--modal-bg) !important;
      --rs-calendar-date-selected-text: hsl(var(--secondary-foreground));
      --rs-btn-primary-text: hsl(var(--secondary-foreground));
      --rs-input-bg: hsl(var(--background));
      --rs-input-disabled-bg: hsl(var(--background));
    }

    .rs-picker-popup,
    .rs-calendar .rs-calendar-time-view,
    .rs-calendar-body,
    .rs-calendar-month-dropdown-row-wrapper,
    .rs-calendar-btn-close,
    .rs-calendar-time-dropdown {
      background-color: hsl(var(--background)) !important;
      border-color: var(--modal-border);
      border-width: 1px;
    }

    /* Chart colors for insights page */
    --chart-color-1: rgb(59, 130, 246);
    /* bright blue */
    --chart-color-2: rgb(110, 231, 183);
    /* light green */
    --chart-color-3: rgb(252, 165, 165);
    /* light red */
    --chart-color-4: rgb(253, 230, 138);
    /* light yellow */
    --chart-color-5: rgb(192, 132, 252);
    /* light purple */
    --chart-color-6: rgb(251, 146, 60);
    /* orange */
    --chart-color-7: rgb(134, 239, 172);
    /* bright green */
    --chart-color-8: rgb(249, 168, 212);
    /* pink */
    --chart-color-9: rgb(191, 219, 254);
    /* very light blue */
    --chart-color-10: rgb(167, 139, 250);
    /* light violet */

    /* Input field gradients */
    --input-gradient-light-from: #f1f5f9;
    --input-gradient-light-via: #f8fafc;
    --input-gradient-light-to: #ffffff;
    --input-gradient-dark-from: #0f172a;
    --input-gradient-dark-via: #0f172a;
    --input-gradient-dark-to: #020617;
  }

  /* Custom styles for dropdown search inputs */
  .css-1rhbuit-multiValue::selection,
  .css-qbdosj-Input input::selection,
  .css-1fsrxvh-Input input::selection,
  .css-1k3x8v3-control input::selection,
  .css-1insrsq-control input::selection {
    background-color: rgba(0, 0, 0, 0.1) !important;
    /* Subtle gray instead of blue */
  }

  .css-qbdosj-Input input,
  .css-1fsrxvh-Input input,
  .css-1k3x8v3-control input,
  .css-1insrsq-control input {
    caret-color: var(--color-text-muted) !important;
    /* Change cursor color */
    width: 100% !important;
    /* Make input take full width */
  }

  .css-qbdosj-Input,
  .css-1fsrxvh-Input {
    width: 100% !important;
  }

  .css-1rhbuit-multiValue svg,
  .css-26l3qy-menu svg,
  .css-1n6sfyn-MenuList svg {
    min-width: 16px !important;
    min-height: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }

  [data-type="custom-emoji"] {
    display: inline;
  }

  [data-type="emoji"]>img {
    width: 20px;
    height: 20px;
    display: inline;
  }

  /* Editor table styles */
  .ProseMirror {
    .tableWrapper {
      @apply my-6;
    }

    &.resize-cursor {
      cursor: ew-resize;
      cursor: col-resize;
    }

    table {
      @apply border-collapse border-black/10 rounded box-border w-full;
      @apply dark:border-white/20;

      td,
      th {
        @apply border border-black/10 min-w-[100px] p-2 relative text-left align-top;
        @apply dark:border-white/20;

        &:first-of-type:not(a) {
          @apply mt-0;
        }

        p {
          @apply m-0;

          &+p {
            @apply mt-3;
          }
        }
      }

      /* 
      th {
        @apply font-bold bg-color-bg-subtle;
      } */

      .column-resize-handle {
        @apply -bottom-[2px] flex pointer-events-none absolute -right-[3px] top-0 w-1;

        &::before {
          @apply bg-black/20 h-full w-[1px] ml-2;
          @apply dark:bg-white/20;
          content: "";
        }
      }

      .grip-column,
      .grip-row {
        @apply items-center bg-black/5 cursor-pointer flex justify-center absolute z-10;
        @apply dark:bg-white/10;
      }

      .grip-column {
        @apply w-[calc(100%+1px)] border-l border-black/20 h-3 left-0 -ml-[1px] -top-3;
        @apply dark:border-white/20;

        &:hover,
        &.selected {
          &::before {
            content: "";
            @apply w-2.5;
          }
        }

        &:hover {
          @apply bg-black/10;
          @apply dark:bg-white/20;

          &::before {
            @apply border-b-2 border-dotted border-black/60;
            @apply dark:border-white/60;
          }
        }

        &.first {
          @apply border-transparent rounded-tl-sm;
        }

        &.last {
          @apply rounded-tr-sm;
        }

        &.selected {
          @apply bg-black/30 border-black/30 shadow-sm;
          @apply dark:bg-white/30 dark:border-white/30;

          &::before {
            @apply border-b-2 border-dotted;
          }
        }
      }

      .grip-row {
        @apply h-[calc(100%+1px)] border-t border-black/20 -left-3 w-3 top-0 -mt-[1px];
        @apply dark:border-white/20;

        &:hover,
        &.selected {
          &::before {
            @apply h-2.5;
            content: "";
          }
        }

        &:hover {
          @apply bg-black/10;
          @apply dark:bg-white/20;

          &::before {
            @apply border-l-2 border-dotted border-black/60;
            @apply dark:border-white/60;
          }
        }

        &.first {
          @apply border-transparent rounded-tl-sm;
        }

        &.last {
          @apply rounded-bl-sm;
        }

        &.selected {
          @apply bg-black/30 border-black/30 shadow-sm;
          @apply dark:bg-white/30 dark:border-white/30;

          &::before {
            @apply border-l-2 border-dotted;
          }
        }
      }
    }
  }

  .button-focus-ring {
    @apply ring-offset-background rounded-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  .mention-user {
    color: #1263A3 !important;
    background-color: #EBF5FA !important;
    padding: 0 4px !important;
    border-radius: 4px;
  }

  @keyframes rotate3d {
    0% {
      transform: perspective(1000px) rotateY(0deg);
    }

    100% {
      transform: perspective(1000px) rotateY(360deg);
    }
  }

  .rotate-3d {
    animation: rotate3d 2s linear infinite;
    transform-style: preserve-3d;
  }


}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Ensure all elements use Inter font by default */
  html,
  body,
  button,
  input,
  select,
  textarea,
  div,
  span,
  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "inter", sans-serif !important;
  }

  ul,
  ol {
    list-style: revert;
  }

  input:focus {
    outline: none !important;
    --tw-ring-offset-width: 0 !important;
    --tw-ring-offset-color: transparent !important;
    --tw-ring-color: transparent !important;
  }
}

@layer utilities {
  * {
    /* custom scrollbar classes from WithSidebar.tsx
    Reference - https://adoxography.github.io/tailwind-scrollbar/examples#global-scrollbar-colours
     */
    @apply scrollbar-thin dark:scrollbar-thumb-scrollbar-dark-bg scrollbar-thumb-scrollbar-light-bg scrollbar-track-transparent;
  }

  .main-layout {
    @apply w-full flex justify-start flex-col items-start pt-6 px-6 overflow-scroll;
  }

  /* Prevent ReactFlow from capturing scrolls within these elements */
  .nodrag {
    pointer-events: auto !important;
  }

  /* Apply these styles to elements inside nodrag elements */
  .nodrag pre,
  .nodrag .overflow-auto,
  .nodrag [style*="overflow"] {
    pointer-events: auto !important;
  }

  /* Prevent ReactFlow's wheel event capturing within scrollable elements */
  .nodrag pre {
    touch-action: auto !important;
  }

  .tooltip-animation {
    transition:
      opacity 0.3s ease-in-out,
      transform 0.3s ease-in-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .tooltip-fade-in-up {
    animation: fadeInUp 0.3s ease-in-out forwards;
  }

  .menu-disabled {
    color: #94a3b8 !important;
    cursor: not-allowed;
  }

  .menu-disabled:hover,
  .menu-disabled:focus,
  .menu-disabled:active {
    color: #94a3b8 !important;
  }

  /* Drawer animation improvements */
  .drawer-overlay-animated-div {
    will-change: opacity;
  }

  .drawer-content-animated-div {
    will-change: transform;
  }

  .debug {
    border: 1px solid red;
  }

  .dnd-column-drop-target {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text);
    font-weight: 500;
    z-index: 20;
    background: var(--background-translucent);
    border-radius: 4px;
    width: 320px;
    font-size: 16px;
    line-height: 20px;
  }

  [data-type="custom-emoji"] {
    display: inline;
  }
}

@layer base {

  /* Thread hover style */
  .thread-card:hover {
    background-color: var(--thread-hover-bg) !important;
  }

  /* Thread button hover style */
  .thread-hover {
    border-color: var(--thread-border-color, #e2e8f0) !important;
  }

  .thread-hover:hover {
    background-color: var(--thread-hover-bg) !important;
  }

  /* Kanban DND hack */
  .height-preserving-container:empty {
    min-height: calc(var(--child-height));
    box-sizing: border-box;
  }
}

@layer base {

  /* Dark theme overrides for field types */
  .carbon .bg-blue-100,
  .midnight .bg-blue-100,
  .nebula .bg-blue-100 {
    background-color: rgba(59, 130, 246, 0.2) !important;
  }

  .carbon .text-blue-700,
  .midnight .text-blue-700,
  .nebula .text-blue-700 {
    color: #93c5fd !important;
  }

  .carbon .hover\:bg-blue-100:hover,
  .midnight .hover\:bg-blue-100:hover,
  .nebula .hover\:bg-blue-100:hover {
    background-color: rgba(59, 130, 246, 0.3) !important;
  }

  .carbon .bg-purple-100,
  .midnight .bg-purple-100,
  .nebula .bg-purple-100 {
    background-color: rgba(147, 51, 234, 0.2) !important;
  }

  .carbon .text-purple-700,
  .midnight .text-purple-700,
  .nebula .text-purple-700 {
    color: #d8b4fe !important;
  }

  .carbon .hover\:bg-purple-100:hover,
  .midnight .hover\:bg-purple-100:hover,
  .nebula .hover\:bg-purple-100:hover {
    background-color: rgba(147, 51, 234, 0.3) !important;
  }

  .carbon .bg-indigo-100,
  .midnight .bg-indigo-100,
  .nebula .bg-indigo-100 {
    background-color: rgba(99, 102, 241, 0.2) !important;
  }

  .carbon .text-indigo-700,
  .midnight .text-indigo-700,
  .nebula .text-indigo-700 {
    color: #a5b4fc !important;
  }

  .carbon .hover\:bg-indigo-100:hover,
  .midnight .hover\:bg-indigo-100:hover,
  .nebula .hover\:bg-indigo-100:hover {
    background-color: rgba(99, 102, 241, 0.3) !important;
  }

  .carbon .bg-emerald-100,
  .midnight .bg-emerald-100,
  .nebula .bg-emerald-100 {
    background-color: rgba(16, 185, 129, 0.2) !important;
  }

  .carbon .text-emerald-700,
  .midnight .text-emerald-700,
  .nebula .text-emerald-700 {
    color: #6ee7b7 !important;
  }

  .carbon .hover\:bg-emerald-100:hover,
  .midnight .hover\:bg-emerald-100:hover,
  .nebula .hover\:bg-emerald-100:hover {
    background-color: rgba(16, 185, 129, 0.3) !important;
  }

  .carbon .bg-teal-100,
  .midnight .bg-teal-100,
  .nebula .bg-teal-100 {
    background-color: rgba(20, 184, 166, 0.2) !important;
  }

  .carbon .text-teal-700,
  .midnight .text-teal-700,
  .nebula .text-teal-700 {
    color: #5eead4 !important;
  }

  .carbon .hover\:bg-teal-100:hover,
  .midnight .hover\:bg-teal-100:hover,
  .nebula .hover\:bg-teal-100:hover {
    background-color: rgba(20, 184, 166, 0.3) !important;
  }

  .carbon .bg-lime-100,
  .midnight .bg-lime-100,
  .nebula .bg-lime-100 {
    background-color: rgba(132, 204, 22, 0.2) !important;
  }

  .carbon .text-lime-700,
  .midnight .text-lime-700,
  .nebula .text-lime-700 {
    color: #bef264 !important;
  }

  .carbon .hover\:bg-lime-100:hover,
  .midnight .hover\:bg-lime-100:hover,
  .nebula .hover\:bg-lime-100:hover {
    background-color: rgba(132, 204, 22, 0.3) !important;
  }

  .carbon .bg-amber-100,
  .midnight .bg-amber-100,
  .nebula .bg-amber-100 {
    background-color: rgba(217, 119, 6, 0.2) !important;
  }

  .carbon .text-amber-700,
  .midnight .text-amber-700,
  .nebula .text-amber-700 {
    color: #fcd34d !important;
  }

  .carbon .hover\:bg-amber-100:hover,
  .midnight .hover\:bg-amber-100:hover,
  .nebula .hover\:bg-amber-100:hover {
    background-color: rgba(217, 119, 6, 0.3) !important;
  }

  .carbon .bg-yellow-100,
  .midnight .bg-yellow-100,
  .nebula .bg-yellow-100 {
    background-color: rgba(234, 179, 8, 0.2) !important;
  }

  .carbon .text-yellow-700,
  .midnight .text-yellow-700,
  .nebula .text-yellow-700 {
    color: #fde047 !important;
  }

  .carbon .hover\:bg-yellow-100:hover,
  .midnight .hover\:bg-yellow-100:hover,
  .nebula .hover\:bg-yellow-100:hover {
    background-color: rgba(234, 179, 8, 0.3) !important;
  }

  .carbon .bg-orange-100,
  .midnight .bg-orange-100,
  .nebula .bg-orange-100 {
    background-color: rgba(249, 115, 22, 0.2) !important;
  }

  .carbon .text-orange-700,
  .midnight .text-orange-700,
  .nebula .text-orange-700 {
    color: #fdba74 !important;
  }

  .carbon .hover\:bg-orange-100:hover,
  .midnight .hover\:bg-orange-100:hover,
  .nebula .hover\:bg-orange-100:hover {
    background-color: rgba(249, 115, 22, 0.3) !important;
  }

  .carbon .bg-green-100,
  .midnight .bg-green-100,
  .nebula .bg-green-100 {
    background-color: rgba(34, 197, 94, 0.2) !important;
  }

  .carbon .text-green-700,
  .midnight .text-green-700,
  .nebula .text-green-700 {
    color: #86efac !important;
  }

  .carbon .hover\:bg-green-100:hover,
  .midnight .hover\:bg-green-100:hover,
  .nebula .hover\:bg-green-100:hover {
    background-color: rgba(34, 197, 94, 0.3) !important;
  }

  .carbon .bg-pink-100,
  .midnight .bg-pink-100,
  .nebula .bg-pink-100 {
    background-color: rgba(236, 72, 153, 0.2) !important;
  }

  .carbon .text-pink-700,
  .midnight .text-pink-700,
  .nebula .text-pink-700 {
    color: #f9a8d4 !important;
  }

  .carbon .hover\:bg-pink-100:hover,
  .midnight .hover\:bg-pink-100:hover,
  .nebula .hover\:bg-pink-100:hover {
    background-color: rgba(236, 72, 153, 0.3) !important;
  }

  .carbon .bg-fuchsia-100,
  .midnight .bg-fuchsia-100,
  .nebula .bg-fuchsia-100 {
    background-color: rgba(192, 38, 211, 0.2) !important;
  }

  .carbon .text-fuchsia-700,
  .midnight .text-fuchsia-700,
  .nebula .text-fuchsia-700 {
    color: #f0abfc !important;
  }

  .carbon .hover\:bg-fuchsia-100:hover,
  .midnight .hover\:bg-fuchsia-100:hover,
  .nebula .hover\:bg-fuchsia-100:hover {
    background-color: rgba(192, 38, 211, 0.3) !important;
  }

  .carbon .bg-violet-100,
  .midnight .bg-violet-100,
  .nebula .bg-violet-100 {
    background-color: rgba(139, 92, 246, 0.2) !important;
  }

  .carbon .text-violet-700,
  .midnight .text-violet-700,
  .nebula .text-violet-700 {
    color: #c4b5fd !important;
  }

  .carbon .hover\:bg-violet-100:hover,
  .midnight .hover\:bg-violet-100:hover,
  .nebula .hover\:bg-violet-100:hover {
    background-color: rgba(139, 92, 246, 0.3) !important;
  }

  .carbon .bg-sky-100,
  .midnight .bg-sky-100,
  .nebula .bg-sky-100 {
    background-color: rgba(14, 165, 233, 0.2) !important;
  }

  .carbon .text-sky-700,
  .midnight .text-sky-700,
  .nebula .text-sky-700 {
    color: #7dd3fc !important;
  }

  .carbon .hover\:bg-sky-100:hover,
  .midnight .hover\:bg-sky-100:hover,
  .nebula .hover\:bg-sky-100:hover {
    background-color: rgba(14, 165, 233, 0.3) !important;
  }

  .carbon .bg-cyan-100,
  .midnight .bg-cyan-100,
  .nebula .bg-cyan-100 {
    background-color: rgba(6, 182, 212, 0.2) !important;
  }

  .carbon .text-cyan-700,
  .midnight .text-cyan-700,
  .nebula .text-cyan-700 {
    color: #67e8f9 !important;
  }

  .carbon .hover\:bg-cyan-100:hover,
  .midnight .hover\:bg-cyan-100:hover,
  .nebula .hover\:bg-cyan-100:hover {
    background-color: rgba(6, 182, 212, 0.3) !important;
  }

  .carbon .bg-slate-100,
  .midnight .bg-slate-100,
  .nebula .bg-slate-100 {
    background-color: rgba(100, 116, 139, 0.2) !important;
  }

  .carbon .text-slate-700,
  .midnight .text-slate-700,
  .nebula .text-slate-700 {
    color: #cbd5e1 !important;
  }

  .carbon .hover\:bg-slate-100:hover,
  .midnight .hover\:bg-slate-100:hover,
  .nebula .hover\:bg-slate-100:hover {
    background-color: rgba(100, 116, 139, 0.3) !important;
  }

  .carbon .bg-rose-100,
  .midnight .bg-rose-100,
  .nebula .bg-rose-100 {
    background-color: rgba(244, 63, 94, 0.2) !important;
  }

  .carbon .text-rose-700,
  .midnight .text-rose-700,
  .nebula .text-rose-700 {
    color: #fda4af !important;
  }

  .carbon .hover\:bg-rose-100:hover,
  .midnight .hover\:bg-rose-100:hover,
  .nebula .hover\:bg-rose-100:hover {
    background-color: rgba(244, 63, 94, 0.3) !important;
  }

  .carbon .bg-red-100,
  .midnight .bg-red-100,
  .nebula .bg-red-100 {
    background-color: rgba(239, 68, 68, 0.2) !important;
  }

  .carbon .text-red-700,
  .midnight .text-red-700,
  .nebula .text-red-700 {
    color: #fca5a5 !important;
  }

  .carbon .hover\:bg-red-100:hover,
  .midnight .hover\:bg-red-100:hover,
  .nebula .hover\:bg-red-100:hover {
    background-color: rgba(239, 68, 68, 0.3) !important;
  }

  .carbon .bg-gray-100,
  .midnight .bg-gray-100,
  .nebula .bg-gray-100 {
    background-color: rgba(107, 114, 128, 0.2) !important;
  }

  .carbon .text-gray-700,
  .midnight .text-gray-700,
  .nebula .text-gray-700 {
    color: #ffffff !important;
  }

  .carbon .hover\:bg-gray-100:hover,
  .midnight .hover\:bg-gray-100:hover,
  .nebula .hover\:bg-gray-100:hover {
    background-color: rgba(107, 114, 128, 0.3) !important;
  }
}

/* Make overlay transparent for AI Ticket Insights Modal (frosted glass) */
[data-radix-dialog-portal]>[data-radix-dialog-overlay][data-state="open"]:has(+ [data-radix-dialog-content][data-ai-insights-modal][data-state="open"]) {
  background: var(--background-translucent) !important;
}