// hooks/useWebChatInit.ts
import { useCallback, useEffect, useRef, useState } from "react";

interface WidgetSettings {
  targetElementId?: string;
  useCustomLauncher?: boolean;
  initialPosition?: {
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  };
  themeColorStart?: string;
  themeColorEnd?: string;
  gradientDirection?: string;
  brandLogoUrl?: string;
  userContextExample?: {
    email?: string;
    name?: string;
  };
}

interface DeploymentConfig {
  agent_key: string;
  agent_id: string;
  wsEndpoint: string;
  baseUrl: string;
  user?: {
    email: string;
    name: string;
    hash: string;
  };
  widget_settings?: WidgetSettings;
}

export interface WebChatWidget {
  baseUrl: string;
  apiKey: string;
  agentId: string;
  wsEndpoint: string;
  useCustomLauncher: boolean;
  user: {
    email: string;
    name: string;
  };
  initialPosition?: {
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  };
  themeColorStart?: string;
  themeColorEnd?: string;
  gradientDirection?: string;
  brandLogoUrl?: string;
  // Add cleanup methods if available
  destroy?: () => void;
  close?: () => void;
}

declare global {
  interface Window {
    thenaWidget?: WebChatWidget;
    // Add any global widget cleanup functions if they exist
    thenaWidgetDestroy?: () => void;
  }
}

const baseUrl = process.env.NEXT_PUBLIC_AGENT_STUDIO_URL;
const wsEndpoint = process.env.NEXT_PUBLIC_AGENT_STUDIO_WS_URL;

export const useWebChatInit = (agentId: string) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const initializationRef = useRef<string | null>(null);
  const cleanupTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const cleanupWidget = useCallback(() => {
    // Clear any pending cleanup timeout
    if (cleanupTimeoutRef.current) {
      clearTimeout(cleanupTimeoutRef.current);
      cleanupTimeoutRef.current = null;
    }
    // Clean up existing widget
    if (window.thenaWidget) {
      // Call destroy method if available
      if (typeof window.thenaWidget.destroy === "function") {
        window.thenaWidget.destroy();
      }
      // Or call close method if available
      if (typeof window.thenaWidget.close === "function") {
        window.thenaWidget.close();
      }
      // Or call global cleanup function if available
      if (typeof window.thenaWidgetDestroy === "function") {
        window.thenaWidgetDestroy();
      }

      window.thenaWidget = undefined;
    }

    // Remove existing script
    const existingScript = document.querySelector(
      'script[src="https://widget.thena.tools/shim.js"]',
    );
    if (existingScript) {
      existingScript.remove();
    }

    // Remove any widget DOM elements (adjust selectors based on your widget)
    const widgetElements = document.querySelectorAll(
      '[id*="thena"], [class*="thena"], [class*="widget"]',
    );
    widgetElements.forEach((element) => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });

    setIsLoaded(false);
    setError(null);
  }, []);

  const initializeWebChat = useCallback(async () => {
    // Prevent multiple simultaneous initializations for the same agent
    if (isInitializing || initializationRef.current === agentId) {
      return;
    }

    // Validate agentId
    if (!agentId || agentId.trim() === "") {
      setError("Agent ID is required");
      return;
    }

    setIsInitializing(true);
    initializationRef.current = agentId;
    setError(null);

    try {
      // Clean up existing widget first
      cleanupWidget();

      // Small delay to ensure cleanup is complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Fetch deployment configuration from API
      const response = await fetch(`/api/agents/${agentId}/deployments`);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Failed to fetch deployment config: ${response.status} ${errorText}`,
        );
      }

      const config: DeploymentConfig | DeploymentConfig[] =
        await response.json();

      // Handle both single config and array of configs
      const latestConfig = Array.isArray(config) ? config[0] : config;

      if (!latestConfig) {
        throw new Error("No deployment configuration found");
      }

      // Validate required config fields
      if (!latestConfig.agent_key || !latestConfig.agent_id) {
        throw new Error(
          "Invalid deployment configuration: missing required fields",
        );
      }

      // Set up the widget configuration
      window.thenaWidget = {
        baseUrl: baseUrl || "https://thena-agent-studio.thena.tools",
        apiKey: latestConfig.agent_key,
        agentId: latestConfig.agent_id,
        wsEndpoint: wsEndpoint || "wss://thena-agent-studio.thena.tools",
        themeColorStart: latestConfig.widget_settings?.themeColorStart,
        themeColorEnd: latestConfig.widget_settings?.themeColorEnd,
        gradientDirection: latestConfig.widget_settings?.gradientDirection,
        brandLogoUrl: latestConfig.widget_settings?.brandLogoUrl,
        useCustomLauncher:
          latestConfig.widget_settings?.useCustomLauncher || false,
        user: latestConfig.user || {
          email: "<EMAIL>",
          name: "Guest User",
        },
        initialPosition: latestConfig.widget_settings?.initialPosition || {
          bottom: "30px",
          right: "35px",
        },
      };

      // Load the widget script
      const script = document.createElement("script");
      script.src = "https://widget.thena.tools/shim.js";
      script.async = true;

      script.onload = () => {
        setIsLoaded(true);
        setIsInitializing(false);
      };

      script.onerror = () => {
        setError("Failed to load widget script");
        setIsInitializing(false);
        initializationRef.current = null;
      };

      document.body.appendChild(script);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      setIsInitializing(false);
      initializationRef.current = null;
    }
  }, [agentId, cleanupWidget]);

  const reinitialize = useCallback(() => {
    // Reset initialization tracking
    initializationRef.current = null;
    if (agentId) {
      initializeWebChat();
    }
  }, [agentId, initializeWebChat]);

  // Initial load effect - only run when agentId changes and widget isn't already loaded
  useEffect(() => {
    if (
      agentId &&
      !window.thenaWidget &&
      !isInitializing &&
      initializationRef.current !== agentId
    ) {
      initializeWebChat();
    }
  }, [agentId]); // Remove initializeWebChat from dependencies to prevent re-runs

  // Cleanup on unmount or agentId change
  useEffect(() => {
    return () => {
      if (cleanupTimeoutRef.current) {
        clearTimeout(cleanupTimeoutRef.current);
      }
      initializationRef.current = null;
    };
  }, [cleanupWidget]);

  return {
    isLoaded,
    error,
    isInitializing,
    reinitialize,
    cleanup: cleanupWidget,
  };
};
