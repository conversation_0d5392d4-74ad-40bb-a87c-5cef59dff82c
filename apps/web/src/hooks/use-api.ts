import {
  APPS_PLATFORM_URL,
  AUTH_URL,
  BASE_URL,
  BASE_URL_APP,
  EMAIL_SERVICE_URL,
  KB_URL,
  SLA_URL,
  THENA_ANNOTATOR_URL,
} from "@/config/constant";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useEffect, useMemo, useState } from "react";
import { getOrgDetails } from "../utils/browserUtils";

type ApiResponse<T> = {
  data: T | null;
  error: Error | null;
  loading: boolean;
  isRefetching: boolean;
};

type ServerResponse<T> = {
  data: T;
  message?: string;
  status?: number;
};

export type ApiOptions = {
  enabled?: boolean;
  queryParams?: Record<string, string | number | boolean>;
  isNextApi?: boolean;
  urlOption?:
    | "auth"
    | "sla"
    | "kb"
    | "annotator"
    | "email"
    | "apps-platform"
    | null;
  method?: "GET" | "POST";
  body?: Record<string, unknown>;
};

export const getNonNextApiBaseUrl = (urlOption: ApiOptions["urlOption"]) => {
  switch (urlOption) {
    case "auth":
      return AUTH_URL;
    case "email":
      return EMAIL_SERVICE_URL;
    case "sla":
      return SLA_URL;
    case "kb":
      return KB_URL;
    case "annotator":
      return THENA_ANNOTATOR_URL;
    case "apps-platform":
      return APPS_PLATFORM_URL;
    default:
      return BASE_URL;
  }
};

export function useApi<T>(
  endpoint: string,
  apiOptions: RequestInit = {},
  options: ApiOptions = {
    enabled: true,
    isNextApi: false,
    method: "GET",
    body: null,
    urlOption: null,
  },
  key = 1,
) {
  const [state, setState] = useState<ApiResponse<T>>({
    data: null,
    error: null,
    loading: options.enabled,
    isRefetching: false,
  });
  const { orgId, orgUid } = getOrgDetails();
  const fetchData = async (isRefetching = false) => {
    try {
      if (isRefetching) {
        setState((prev) => ({ ...prev, isRefetching: true }));
      } else {
        setState((prev) => ({ ...prev, loading: true }));
      }

      const url = new URL(`${BASE_URL_APP}${endpoint}`);

      const getUserHeaders = () => {
        return options.urlOption === "kb"
          ? {
              "x-user-id":
                useGlobalConfigPersistStore.getState().currentUser.uid ?? "",
            }
          : {};
      };

      if (options.queryParams) {
        Object.entries(options.queryParams).forEach(([key, value]) => {
          url.searchParams.append(key, String(value));
        });
      }

      let response;

      if (options.isNextApi && options.method === "POST") {
        response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-store, max-age=0",
            ...apiOptions.headers,
            "x-org-id": orgId,
            "x-org-uid": orgUid,
            ...getUserHeaders(),
          },
          body: JSON.stringify(options.body),
        });
      } else if (options.isNextApi) {
        response = await fetch(url, {
          ...apiOptions,
          method: "GET",
          headers: {
            "Cache-Control": "no-store, max-age=0",
            "Content-Type": "application/json",
            ...apiOptions.headers,
            "x-org-id": orgId,
            "x-org-uid": orgUid,
            ...getUserHeaders(),
          },
        });
      } else {
        const baseUrl = getNonNextApiBaseUrl(options.urlOption);
        const url = new URL(`${baseUrl}${endpoint}`);

        response = await fetch("/api/proxy", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-store, max-age=0",
            "x-org-id": orgId,
            "x-org-uid": orgUid,
            ...getUserHeaders(),
          },
          body: JSON.stringify({ url, method: "GET", body: options.body }),
        });
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const serverResponse: ServerResponse<T> = await response.json();
      setState({
        data: options.isNextApi
          ? (serverResponse as T)
          : // eslint-disable-next-line @typescript-eslint/no-explicit-any
            serverResponse.data || (serverResponse as any),
        error: null,
        loading: false,
        isRefetching: false,
      });
    } catch (error) {
      console.log({ error });
      setState({
        data: null,
        error: error as Error,
        loading: false,
        isRefetching: false,
      });
    }
  };

  useEffect(() => {
    if (options.enabled && orgId !== null && orgId) {
      fetchData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options.enabled, endpoint, key, orgId]);

  return useMemo(
    () => ({ ...state, refetch: () => fetchData(true) }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [state],
  );
}
