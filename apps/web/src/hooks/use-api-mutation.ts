import { useCallback, useState } from "react";
import { useGlobalConfigPersistStore } from "../store/globalConfigPersistStore";
import { ApiOptions, getNonNextApiBaseUrl } from "./use-api";

interface UseApiMutationOptions {
  headers?: HeadersInit;
}

export interface UseApiMutationReturn<TResponse, TError, TBody> {
  mutate: (
    body?: TBody,
    fetchOptions?: RequestInit,
    param?: string,
  ) => Promise<TResponse>;
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  data: TResponse | null;
  error: TError | null;
}

export const useApiMutation = <
  TResponse = unknown,
  TError = unknown,
  TBody = unknown,
>(
  apiUrl: string,
  options: UseApiMutationOptions = {},
  method: "POST" | "PUT" | "PATCH" | "DELETE" | "GET" = "POST",
  baseUrlAppName?: ApiOptions["urlOption"],
): UseApiMutationReturn<TResponse, TError, TBody> => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);
  const [data, setData] = useState<TResponse | null>(null);
  const [error, setError] = useState<TError | null>(null);

  const mutate = useCallback(
    async (
      body: TBody,
      fetchOptions: RequestInit = {},
      param: string | undefined,
    ): Promise<TResponse> => {
      setIsLoading(true);
      setIsError(false);
      setIsSuccess(false);
      setError(null);

      try {
        const baseUrl = getNonNextApiBaseUrl(baseUrlAppName);

        let url = new URL(`${baseUrl}${apiUrl}`);
        if (param) {
          url = new URL(`${baseUrl}${apiUrl}${param}`);
        }

        const userUid =
          useGlobalConfigPersistStore.getState()?.currentUser?.uid;

        const response = await fetch("/api/proxy", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...options.headers,
            ...(baseUrlAppName === "kb" ? { "x-user-id": userUid ?? "" } : {}),
          },
          body: JSON.stringify({ ...body, url, method }),
          ...fetchOptions,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw errorData as TError;
        }

        const responseData = (await response.json()) as TResponse;
        setData(responseData);
        setIsSuccess(true);
        return responseData;
      } catch (err) {
        setIsError(true);
        setError(err as TError);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [apiUrl, method, options.headers, baseUrlAppName],
  );

  return {
    mutate,
    isLoading,
    isSuccess,
    isError,
    data,
    error,
  };
};
