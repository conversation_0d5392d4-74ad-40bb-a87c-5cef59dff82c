export interface AppScope {
  scope: string;
  reason: string;
  description: string;
}

export interface AppEvent {
  event: string;
  reason: string;
  description: string;
}

export interface AppManifest {
  app: {
    name: string;
    icons: {
      large: string;
      small: string;
    };
    category: string;
    description: string;
    supported_locales: string[];
  };
  events: {
    publish: AppEvent[];
    subscribe: AppEvent[];
  };
  scopes: {
    required: {
      platform: AppScope[];
    };
  };
  metadata: {
    title: string;
    rating: number;
    pricing: {
      yearly: number;
      monthly: number;
    };
    category: string;
    capabilities: string[];
  };
  developer: {
    name: string;
    website: string;
    terms_url: string;
    support_email: string;
    documentation_url: string;
    privacy_policy_url: string;
  };
  activities: Array<{
    name: string;
    description: string;
    http_config: {
      headers: Record<string, string>;
      httpVerb: string;
      endpoint_url: string;
    };
    request_schema: Record<string, unknown>;
    response_schema: Record<string, unknown>;
  }>;
  integration: {
    webhooks: {
      events: string;
      installations: string;
    };
    entry_points: {
      main: string;
      configuration: string;
      oauth_redirect: string;
    };
    interactivity: {
      request_url: string;
      message_menu_option_url: string;
    };
  };
  configuration: {
    optional_settings: Array<{
      key: string;
      type: string;
      label: string;
      description: string;
      required: boolean;
      default?: unknown;
      enum?: string[];
    }>;
    required_settings: Array<{
      key: string;
      type: string;
      label: string;
      description: string;
      required: boolean;
      enum?: string[];
    }>;
  };
}

export interface InstalledAppDto {
  appId: string;
  name: string;
  description: string;
  appManifest: AppManifest;
  appConfiguration: Record<string, unknown>;
  installedBySub: string;
  installedByEmail: string;
  updatedAt: string;
}

export interface InstalledAppsResponseDto {
  organizationId: string;
  totalCount: number;
  apps: InstalledAppDto[];
}

interface Scope {
  scope: string;
  reason: string;
  description: string;
}

interface Event {
  event: string;
  reason: string;
  description: string;
}

interface Activity {
  name: string;
  description: string;
  http_config: {
    endpoint_url: string;
    httpVerb: string;
    headers: Record<string, string>;
  };
  request_schema: Record<string, unknown>;
  response_schema: Record<string, unknown>;
}

export interface AppManifest {
  scopes: {
    required: {
      platform: Scope[];
    };
  };
  events: {
    subscribe: Event[];
    publish: Event[];
  };
  activities: Activity[];
}

export interface AppResponseDto {
  uid: string;
  name: string;
  manifest: AppManifest;
  description: string;
  visibility: 'public' | 'private' | 'unlisted';
  createdAt: string;
  updatedAt: string;
  isAgentApp?: boolean;
}

// Common types
export type AppVisibility = 'public' | 'private' | 'unlisted';

// API Response types
export interface ApiErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

// Request types
export interface InstallAppDto {
  appId: string;
  organizationId: string;
}

export interface UninstallAppDto {
  appId: string;
  organizationId: string;
}
