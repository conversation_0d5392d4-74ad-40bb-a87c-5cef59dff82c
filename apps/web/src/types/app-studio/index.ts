export interface AppScope {
  scope: string;
  reason: string;
  description: string;
}

export interface AppEvent {
  event: string;
  reason: string;
  description: string;
}

// Define a reusable type for settings to avoid duplication
type SettingDefinition = {
  key: string;
  type: string;
  label: string;
  description: string;
  required: boolean;
  default?: unknown;
  enum?: string[];
  options?: Array<{ value: string; label: string }>;
  validation?: {
    min?: number;
    max?: number;
    step?: number;
    minItems?: number;
    maxItems?: number;
  };
};

export interface AppManifest {
  app: {
    name: string;
    icons: {
      large: string;
      small: string;
    };
    category: string;
    description: string;
    supported_locales: string[];
    slug?: string;
  };
  events: {
    publish: _Event[];
    subscribe: _Event[];
  };
  scopes: {
    required: {
      platform: _Scope[];
    };
    optional: {
      platform: _Scope[];
    };
  };
  metadata: {
    title: string;
    rating: number;
    pricing: {
      yearly: number;
      monthly: number;
    };
    category: string;
    capabilities: string[];
  };
  developer: {
    name: string;
    website: string;
    terms_url: string;
    support_email: string;
    documentation_url: string;
    privacy_policy_url: string;
  };
  activities: _Activity[];
  integration: {
    webhooks: {
      events: string;
      installations: string;
    };
    entry_points: {
      main: string;
      configuration: string;
      oauth_redirect: string;
    };
    interactivity: {
      request_url: string;
      message_menu_option_url: string;
    };
  };
  configuration: {
    required_settings: SettingDefinition[];
    optional_settings: SettingDefinition[];
  };
}

export interface InstalledAppDto {
  appId: string;
  name: string;
  description: string;
  appManifest: AppManifest;
  appConfiguration: Record<string, unknown>;
  installedBySub: string;
  installedByEmail: string;
  updatedAt: string;
}

export interface InstalledAppsResponseDto {
  organizationId: string;
  totalCount: number;
  apps: InstalledAppDto[];
}

interface _Scope {
  scope: string;
  reason: string;
  description: string;
}

interface _Event {
  event: string;
  reason: string;
  description: string;
}

interface _Activity {
  name: string;
  description: string;
  http_config: {
    endpoint_url: string;
    httpVerb: string;
    headers: Record<string, string>;
  };
  request_schema: Record<string, unknown>;
  response_schema: Record<string, unknown>;
}

export interface AppResponseDto {
  uid: string;
  name: string;
  manifest: AppManifest;
  description: string;
  visibility: "public" | "private" | "unlisted";
  createdAt: string;
  updatedAt: string;
}

// Common types
export type AppVisibility = "public" | "private" | "unlisted";

// API Response types
export interface ApiErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

// Request types
export interface InstallAppDto {
  appId: string;
  organizationId: string;
}

export interface UninstallAppDto {
  appId: string;
  organizationId: string;
}

export interface AppSettingsResponseDto {
  integration: {
    enabled: boolean;
    integratedBy?: string;
    integratedOn?: string;
  };
  companies: {
    enabled: boolean;
    selectedFields: string[];
    filters: Array<{
      field: string;
      operator: string;
      value: string | string[];
    }>;
    totalCount?: number;
    syncedCount?: number;
  };
  contacts: {
    enabled: boolean;
    selectedFields: string[];
    filters: Array<{
      field: string;
      operator: string;
      value: string | string[];
    }>;
    totalCount?: number;
    syncedCount?: number;
  };
}

export interface UpdateAppSettingsDto {
  integration?: {
    enabled: boolean;
  };
  companies?: {
    enabled: boolean;
    selectedFields: string[];
    filters: Array<{
      field: string;
      operator: string;
      value: string | string[];
    }>;
  };
  contacts?: {
    enabled: boolean;
    selectedFields: string[];
    filters: Array<{
      field: string;
      operator: string;
      value: string | string[];
    }>;
  };
}
