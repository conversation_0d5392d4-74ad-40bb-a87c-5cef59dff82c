export const tabId = Math.random().toString(36).substring(2, 15);

export function getLatestTabId() {
  try {
    const tabData = JSON.parse(localStorage.getItem("tabData") || "[]");
    if (tabData.length === 0) {
      return null;
    }
    tabData.sort((a, b) => b.timestamp - a.timestamp);

    return tabData[0].id;
  } catch (err) {
    console.log({ err });
  }
}

export function getOrgDetails() {
  if (typeof window === "undefined") {
    return {
      orgUid: null,
      orgId: null,
    };
  }
  if (window.sessionStorage.getItem("currentOrgId")) {
    const orgId = window.sessionStorage.getItem("currentOrgId");
    const orgUid = window.sessionStorage.getItem("currentOrgUid");
    return {
      orgUid,
      orgId,
    };
  } else {
    const orgId = window.localStorage.getItem("currentOrgId");
    const orgUid = window.localStorage.getItem("currentOrgUid");
    window.sessionStorage.setItem("currentOrgId", orgId);
    window.sessionStorage.setItem("currentOrgUid", orgUid);
    return {
      orgUid,
      orgId,
    };
  }
}

export function setOrgDetails(orgId: string, orgUid: string) {
  if (
    typeof window === "undefined" ||
    window?.location?.pathname?.includes("/signup/verify")
  ) {
    return {
      orgUid: null,
      orgId: null,
    };
  }
  window.sessionStorage.setItem("currentOrgId", orgId);
  window.sessionStorage.setItem("currentOrgUid", orgUid);
  window.localStorage.setItem("currentOrgId", orgId);
  window.localStorage.setItem("currentOrgUid", orgUid);
}
