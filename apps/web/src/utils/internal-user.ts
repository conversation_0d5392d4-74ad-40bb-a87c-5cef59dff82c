import { Source } from "../store/apps-sources-store";
import { InternalUser } from "../store/internal-users-store";

/**
 * Fetches internal users from various sources (slack, github, etc.)
 * @param sources Array of source objects to fetch users from
 * @param currentOrgId Organization ID
 * @returns Array of internal users
 */
export const fetchInternalUsers = async (
  sources: Source[],
  currentOrgId: string,
): Promise<InternalUser[]> => {
  if (!sources || sources.length === 0 || !currentOrgId) {
    return [];
  }

  try {
    const slackSource = sources.find((source) => source.type === "slack");

    if (!slackSource?.id) {
      return [];
    }

    const response = await fetch(
      `/api/internal-users?workflowId=slack.getMembers-${slackSource.id}-${currentOrgId}`,
      {
        method: "GET",
        headers: {
          accept: "application/json",
          "x-org-id": currentOrgId,
        },
      },
    );
    const result = await response.json();
    if (result?.data?.data && Array.isArray(result?.data?.data)) {
      const internalUsers: InternalUser[] = result?.data?.data.map(
        (contact) => ({
          id: contact.slackId,
          uid: contact.slackId,
          name: contact.displayName || contact.realName || "",
          email: contact.slackProfileEmail,
          source: "Slack member",
          avatar_url: contact.images?.image_192 || contact.images?.image_72,
          is_active: !contact.slackDeleted,
          slackData: {
            slackId: contact.slackId,
            slackDeleted: contact.slackDeleted,
            displayName: contact.displayName,
            realName: contact.realName,
            tz: contact.tz,
            tzLabel: contact.tzLabel,
            isAdmin: contact.isAdmin,
            isOwner: contact.isOwner,
            isRestricted: contact.isRestricted,
            isUltraRestricted: contact.isUltraRestricted,
            isBot: contact.isBot,
            userTitle: contact.userTitle,
            slackProfileRealName: contact.slackProfileRealName,
            slackProfileDisplayName: contact.slackProfileDisplayName,
            slackProfilePhone: contact.slackProfilePhone,
            slackStatusText: contact.slackStatusText,
            slackStatusEmoji: contact.slackStatusEmoji,
            slackProfileEmail: contact.slackProfileEmail,
            images: contact.images,
            metadata: contact.metadata,
            userDump: contact.userDump,
          },
        }),
      );

      return internalUsers;
    }

    return [];
  } catch (error) {
    console.error("Error fetching internal users:", error);
    return [];
  }
};
