import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";
const extractDomain = (url) => {
  if (!url) return "";
  return url.replace(/^https?:\/\//, "");
};

// Create base security headers
const createSecurityHeaders = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseDomain = extractDomain(supabaseUrl);
  const tipTapAppId = process.env.NEXT_PUBLIC_TIPTAP_APP_ID;
  return [
    {
      key: "X-Frame-Options",
      value: "DENY",
    },
    {
      key: "Content-Security-Policy",
      value:
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' accounts.google.com apis.google.com cdn.lrkt-in.com *.logrocket.com cdn.logrocket.com widget.thena.tools widget.thena.ai *.thena.tools *.thena.ai; " +
        "script-src-elem 'self' 'unsafe-inline' accounts.google.com apis.google.com cdn.lrkt-in.com *.logrocket.com cdn.logrocket.com widget.thena.tools widget.thena.ai *.thena.tools *.thena.ai http://localhost:3001/dist/shim.js https://widget.thena.ai/shim.js; " +
        "worker-src 'self' blob: data: *.logrocket.com cdn.logrocket.com; " +
        "style-src 'self' 'unsafe-inline' accounts.google.com; " +
        "img-src 'self' data: blob: https: http://localhost:8000; " +
        "media-src 'self' data: blob: https://*.amazonaws.com http://localhost:8000; " +
        "connect-src 'self' https: " +
        "ws://localhost:* " +
        "http://localhost:* " +
        `wss://${supabaseDomain}/realtime/v1/websocket ` +
        "wss://*.supabase.co/realtime/v1/websocket " +
        "wss://realtime.supabase.co " +
        "wss://api.knock.app/ws/v1/websocket " +
        (tipTapAppId ? `wss://${tipTapAppId}.collab.tiptap.cloud ` : "") +
        "*.logrocket.com " +
        "cdn.logrocket.com " +
        "accounts.google.com " +
        "widget.thena.tools " +
        "widget.thena.ai " +
        "*.thena.tools " +
        "*.thena.ai; " +
        "font-src 'self' data: fonts.gstatic.com; " +
        "frame-src 'self' accounts.google.com widget.thena.tools widget.thena.ai *.thena.tools *.thena.ai *.thena.support *.thena.work http://localhost:3001/ https://widget.thena.ai/; " +
        "frame-ancestors 'none'; " +
        "base-uri 'self'; " +
        "form-action 'self';"
    },
    {
      key: "Strict-Transport-Security",
      value: "max-age=********; includeSubDomains; preload",
    },
    {
      key: "X-Content-Type-Options",
      value: "nosniff",
    },
    {
      key: "Referrer-Policy",
      value: "strict-origin-when-cross-origin",
    },
    {
      key: "Permissions-Policy",
      value: "camera=(), microphone=(), geolocation=()",
    },
    {
      key: "X-XSS-Protection",
      value: "1; mode=block",
    },
  ];
};

const securityHeaders = createSecurityHeaders();

const nextConfig: NextConfig = {
  typescript: {
    tsconfigPath: "./tsconfig.json",
  },
  experimental: {
    typedRoutes: true,
    serverActions: {
      bodySizeLimit: 10 * 1024 * 1024, // 10MB in bytes
    },
  },
  images: {
    domains: [
      "dwua6jkk624sw.cloudfront.net",
      "localhost",
      "127.0.0.1",
      "platform.thena.tools",
      "lh3.googleusercontent.com", // Add this for Google profile images
      "widget.thena.tools", // Add Thena widget domain
      "widget.thena.ai", // Add Thena widget AI domain
    ],
    formats: ["image/avif", "image/webp"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
      {
        protocol: "http",
        hostname: "**",
      },
    ],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  async headers() {
    return [
      {
        source: "/(.*)", // Applies to all routes
        headers: securityHeaders,
      },
    ];
  },
};

// Only apply Sentry in production environment
const isProduction = process.env.VERCEL_ENV === "production";

// Export the config with or without Sentry based on environment
export default isProduction
  ? withSentryConfig(nextConfig, {
      org: "thena",
      project: "thena-dashboard",
      authToken: process.env.SENTRY_AUTH_TOKEN,
      widenClientFileUpload: true,
      disableLogger: true,
      automaticVercelMonitors: true,
    })
  : nextConfig;
