import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import {
  Account,
  AccountAttributeType,
  AccountAttributeValue,
  AccountRepository,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedAccountRepository,
  CustomFieldValues,
  TransactionService,
  User,
} from "@repo/thena-platform-entities";
import { AccountEvents, AccountSinks } from "@repo/thena-shared-interfaces";
import { cloneDeep, isArray, mergeWith } from "lodash";
import { FindOptionsRelations, FindOptionsWhere, In } from "typeorm";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { extractNameFromDomain } from "../../common/utils/extract-email-details";
import { CustomFieldValuesService } from "../../custom-field/services/custom-field-values.service";
import { UsersService } from "../../users/services/users.service";
import {
  CreateAccountDto,
  FindAllAccountsDto,
  UpdateAccountDto,
} from "../dtos/account.dto";
import { PaginatedResponseDto } from "../dtos/response/common-response.dto";
import { AccountsEventsFactory } from "../events/accounts-events.factory";
import { AccountsSNSEventsFactory } from "../events/accounts-sns-events.factory";
import { EmittableAccountEvents } from "../events/accounts.events";
import { AccountCommonService } from "./account-commons.service";

@Injectable()
export class AccountsService {
  constructor(
    // Injected repositories
    private readonly accountRepository: AccountRepository,
    private readonly cachedAccountRepository: CachedAccountRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Users service
    private readonly usersService: UsersService,

    // Custom field values service
    private readonly customFieldValuesService: CustomFieldValuesService,

    // Event emitter
    private readonly accountsEventsFactory: AccountsEventsFactory,
    private readonly eventEmitter: EventEmitter2,
    private readonly accountsSNSEventsFactory: AccountsSNSEventsFactory,
  ) {}

  queryAccountsAll(
    where: FindOptionsWhere<Account>,
    relations?: FindOptionsRelations<Account>,
  ): Promise<Account[]> {
    return this.accountRepository.findAll({
      where,
      relations: relations || {},
    });
  }

  /**
   * Finds an account by its primary domain and organization ID.
   * @param primaryDomain The primary domain of the account to find.
   * @param organizationId The ID of the organization to find the account in.
   * @returns The account.
   */
  findOneByPrimaryDomain(
    primaryDomain: string,
    organizationId: string,
  ): Promise<Account> {
    return this.cachedAccountRepository.findByCondition({
      where: { primaryDomain, organizationId, isActive: true },
    });
  }

  /**
   * Finds an account by its UID and organization ID.
   * @param accountId The UID of the account to find.
   * @param organizationId The ID of the organization to find the account in.
   * @returns The account.
   */
  findOneByAccountId(
    accountId: string,
    organizationId: string,
  ): Promise<Account> {
    return this.cachedAccountRepository.findByCondition({
      where: { uid: accountId, organizationId, isActive: true },
    });
  }

  /**
   * Finds accounts by their UIDs and organization ID.
   * @param accountIds The UIDs of the accounts to find.
   * @param organizationId The ID of the organization to find the accounts in.
   * @returns The accounts.
   */
  findAccountsByPublicIds(
    accountIds: string[],
    organizationId: string,
  ): Promise<Account[]> {
    return this.accountRepository.findAll({
      where: { uid: In(accountIds), organizationId, isActive: true },
    });
  }

  /**
   * Finds accounts by their primary domains and organization ID.
   * @param primaryDomains The primary domains of the accounts to find.
   * @param organizationId The ID of the organization to find the accounts in.
   * @returns The accounts.
   */
  findAccountsByPrimaryDomains(
    primaryDomains: string[],
    organizationId: string,
  ): Promise<Account[]> {
    return this.accountRepository.findAll({
      where: {
        primaryDomain: In(primaryDomains),
        organizationId,
        isActive: true,
      },
    });
  }

  /**
   * Finds all accounts.
   * @param organizationId The ID of the organization to find the accounts in.
   * @returns The accounts.
   */
  async findAllAccounts(
    organizationId: string,
    query: FindAllAccountsDto,
  ): Promise<PaginatedResponseDto<Account>> {
    const whereClause: FindOptionsWhere<Account> = {
      organizationId,
      isActive: true,
    };

    if (query.source) {
      whereClause.source = query.source;
    }

    if (query.status) {
      const statusAttribute = await this.validateAndGetAttributeValue(
        query.status,
        organizationId,
        AccountAttributeType.ACCOUNT_STATUS,
      );
      whereClause.status = statusAttribute.id;
    }

    if (query.classification) {
      const classificationAttribute = await this.validateAndGetAttributeValue(
        query.classification,
        organizationId,
        AccountAttributeType.ACCOUNT_CLASSIFICATION,
      );
      whereClause.classification = classificationAttribute.id;
    }

    if (query.health) {
      const healthAttribute = await this.validateAndGetAttributeValue(
        query.health,
        organizationId,
        AccountAttributeType.ACCOUNT_HEALTH,
      );
      whereClause.health = healthAttribute.id;
    }

    if (query.industry) {
      const industryAttribute = await this.validateAndGetAttributeValue(
        query.industry,
        organizationId,
        AccountAttributeType.ACCOUNT_INDUSTRY,
      );
      whereClause.industry = industryAttribute.id;
    }

    if (query.accountOwnerId) {
      const accountOwner = await this.usersService.findOneByPublicId(
        query.accountOwnerId,
      );

      if (!accountOwner) {
        throw new NotFoundException("Account owner not found");
      }

      whereClause.accountOwnerId = accountOwner.id;
    }

    const results = await this.accountRepository.fetchPaginatedResults(
      {
        page: query.page > 0 ? query.page - 1 : 0,
        limit: Math.min(query.limit ?? 10, 100),
      },
      {
        where: whereClause,
        relations: [
          "accountOwner",
          "statusAttribute",
          "classificationAttribute",
          "healthAttribute",
          "industryAttribute",
          "customerContacts",
          "customFieldValues",
          "customFieldValues.customField",
        ],
      },
    );

    return {
      results: results.results,
      meta: {
        totalCount: results.total,
        totalPages: Math.ceil(results.total / results.results.length),
        currentPage: query.page > 0 ? query.page : 1,
        currentPageCount: results.results.length,
      },
    };
  }

  /**
   * Finds all accounts by their primary domains.
   * @param primaryDomains The primary domains of the accounts to find.
   * @param organizationId The ID of the organization to find the accounts in.
   * @returns The accounts.
   */
  findAllAccountsByPrimaryDomains(
    primaryDomains: string[],
    organizationId: string,
  ): Promise<Account[]> {
    return this.accountRepository.findAll({
      where: {
        primaryDomain: In(primaryDomains),
        organizationId,
        isActive: true,
      },
    });
  }

  /**
   * Finds the details of an account.
   * @param user The current user.
   * @param accountId The ID of the account to find.
   * @returns The account details.
   */
  async findAccountDetails(
    user: CurrentUser,
    accountId: string,
  ): Promise<Account> {
    const relations = [
      "accountOwner",
      "statusAttribute",
      "classificationAttribute",
      "healthAttribute",
      "industryAttribute",
      "customFieldValues",
      "customFieldValues.customField",
    ];
    const account = await this.cachedAccountRepository.findByCondition({
      where: { uid: accountId, organizationId: user.orgId, isActive: true },
      relations,
    });
    if (!account) {
      throw new NotFoundException("Account not found!");
    }
    return account;
  }

  /**
   * Checks if an attribute value is in use.
   *
   * @param attributeValueId The ID of the attribute value
   * @param attributeType The attribute type
   * @returns Whether the attribute value is in use
   */
  async isAccountAttributeValueInUse(
    attributeValueId: string,
    attributeType: AccountAttributeType,
  ): Promise<boolean> {
    const attributeTypeColMapping = {
      [AccountAttributeType.ACCOUNT_STATUS]: "status",
      [AccountAttributeType.ACCOUNT_CLASSIFICATION]: "classification",
      [AccountAttributeType.ACCOUNT_HEALTH]: "health",
      [AccountAttributeType.ACCOUNT_INDUSTRY]: "industry",
    };
    const colName = attributeTypeColMapping[attributeType];

    if (!colName) {
      throw new BadRequestException("Invalid attribute type provided.");
    }

    const count = await this.accountRepository.count({
      where: { [colName]: attributeValueId, isActive: true },
    });
    return count > 0;
  }

  async updateAccountAttributeValue(
    attributeValueId: string,
    defaultAttributeValueId: string,
    attributeType: AccountAttributeType,
  ): Promise<void> {
    const attributeTypeColMapping = {
      [AccountAttributeType.ACCOUNT_STATUS]: "status",
      [AccountAttributeType.ACCOUNT_CLASSIFICATION]: "classification",
      [AccountAttributeType.ACCOUNT_HEALTH]: "health",
      [AccountAttributeType.ACCOUNT_INDUSTRY]: "industry",
    };

    const colName = attributeTypeColMapping[attributeType];
    if (!colName) {
      throw new BadRequestException("Invalid attribute type provided.");
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Find accounts using the previous account attribute
      const accounts = await this.accountRepository.findAll({
        where: { [colName]: attributeValueId },
      });

      if (accounts.length === 0) {
        return;
      }

      // Update the account attribute for each account
      for (const account of accounts) {
        account[colName] = defaultAttributeValueId;
      }

      // Save the accounts
      await this.accountRepository.saveManyWithTxn(txnContext, accounts);

      // Invalidate the cache for each account
      accounts.forEach((account) => {
        this.cachedAccountRepository.invalidateAccountCache({
          organizationId: account.organizationId,
          accountId: account.uid,
          primaryDomain: account.primaryDomain,
        });
      });
    });
  }

  private async findIfDomainIsUsed(
    domain: string,
    organizationId: string,
  ): Promise<boolean> {
    const count = await this.accountRepository.count({
      where: [
        {
          organizationId,
          primaryDomain: domain,
          isActive: true,
        },
        {
          organizationId,
          secondaryDomain: domain,
          isActive: true,
        },
      ],
    });
    return count > 0;
  }

  /**
   * Validates and gets an account task attribute value. (One of the task type, task status, or task priority)
   *
   * @param attributeValue The UID of the attribute value
   * @param organizationId The ID of the organization
   * @param attributeType The attribute type
   * @returns The account attribute value
   */
  private async validateAndGetAttributeValue(
    attributeValue: string,
    organizationId: string,
    attributeType: AccountAttributeType,
  ): Promise<AccountAttributeValue> {
    const attributeValueEntity =
      await this.accountCommonService.getAttributeValue(
        attributeValue,
        attributeType,
        organizationId,
      );

    if (
      !attributeValueEntity ||
      attributeValueEntity.attribute !== attributeType
    ) {
      throw new BadRequestException(
        `Provided value for ${attributeType} is not valid!`,
      );
    }

    return attributeValueEntity;
  }

  /**
   * Gets or creates an account from a primary domain.
   * @param currentUser The current user.
   * @param primaryDomain The primary domain of the account.
   * @returns The account.
   */
  async getOrCreateAccountFromPrimaryDomain(
    currentUser: CurrentUser,
    primaryDomain: string,
  ): Promise<Account> {
    const existingAccount = await this.findOneByPrimaryDomain(
      primaryDomain,
      currentUser.orgId,
    );

    if (existingAccount) {
      return existingAccount;
    }

    const accountName = extractNameFromDomain(primaryDomain);

    const account = await this.createAccount(currentUser, {
      name: accountName,
      primaryDomain,
    });

    return account;
  }

  /**
   * Gets or creates accounts from primary domains.
   * @param currentUser The current user.
   * @param primaryDomains The primary domains of the accounts to get or create.
   * @returns A map of the accounts.
   */
  async getOrCreateAccountsFromPrimaryDomains(
    currentUser: CurrentUser,
    primaryDomains: string[],
    source?: AccountSinks,
    logo?: string,
  ): Promise<Map<string, Account>> {
    const existingAccounts = await this.findAllAccountsByPrimaryDomains(
      primaryDomains,
      currentUser.orgId,
    );

    const existingAccountsMap = new Map<string, Account>();
    for (const account of existingAccounts) {
      existingAccountsMap.set(account.primaryDomain, account);
    }

    const domainsToCreateAccountsFor = primaryDomains.filter(
      (domain) => !existingAccountsMap.has(domain),
    );

    for (const domain of domainsToCreateAccountsFor) {
      const account = await this.createAccount(currentUser, {
        name: extractNameFromDomain(domain),
        primaryDomain: domain,
        source: source,
        logo: logo,
      });

      existingAccountsMap.set(domain, account);
    }

    return existingAccountsMap;
  }

  /**
   * Creates a new account.
   *
   * @param currentUser The current user.
   * @param createAccountDto {@link CreateAccountDto} The DTO containing the account details.
   * @returns The created account.
   */
  async createAccount(
    currentUser: CurrentUser,
    createAccountDto: CreateAccountDto,
  ): Promise<Account> {
    const organizationId = currentUser.orgId;

    // const primaryDomainExists = await this.findIfDomainIsUsed(
    //   createAccountDto.primaryDomain,
    //   organizationId,
    // );
    // if (primaryDomainExists) {
    //   throw new BadRequestException(
    //     "Primary domain is already in use by another account!",
    //   );
    // }

    let statusAttributeValue: AccountAttributeValue;
    if (createAccountDto.status) {
      statusAttributeValue = await this.validateAndGetAttributeValue(
        createAccountDto.status,
        organizationId,
        AccountAttributeType.ACCOUNT_STATUS,
      );
    } else {
      statusAttributeValue =
        await this.accountCommonService.findDefaultAttributeValue(
          organizationId,
          AccountAttributeType.ACCOUNT_STATUS,
        );
    }

    let classificationAttributeValue: AccountAttributeValue;
    if (createAccountDto.classification) {
      classificationAttributeValue = await this.validateAndGetAttributeValue(
        createAccountDto.classification,
        organizationId,
        AccountAttributeType.ACCOUNT_CLASSIFICATION,
      );
    }

    let healthAttributeValue: AccountAttributeValue;
    if (createAccountDto.health) {
      healthAttributeValue = await this.validateAndGetAttributeValue(
        createAccountDto.health,
        organizationId,
        AccountAttributeType.ACCOUNT_HEALTH,
      );
    }

    let industryAttributeValue: AccountAttributeValue;
    if (createAccountDto.industry) {
      industryAttributeValue = await this.validateAndGetAttributeValue(
        createAccountDto.industry,
        organizationId,
        AccountAttributeType.ACCOUNT_INDUSTRY,
      );
    }

    let accountOwner: User;
    if (createAccountDto.accountOwnerId) {
      accountOwner = await this.usersService.findOneByPublicId(
        createAccountDto.accountOwnerId,
      );
      if (!accountOwner) {
        throw new BadRequestException("Account owner not found!");
      }
    }

    let customFieldValues: CustomFieldValues[];
    if (isArray(createAccountDto.customFieldValues)) {
      customFieldValues =
        await this.customFieldValuesService.createCustomFieldValues(
          createAccountDto.customFieldValues,
          organizationId,
        );
    }

    // if (createAccountDto.secondaryDomain) {
    //   const secondaryDomainExists = await this.findIfDomainIsUsed(
    //     createAccountDto.secondaryDomain,
    //     organizationId,
    //   );
    //   if (secondaryDomainExists) {
    //     throw new BadRequestException("Secondary domain is already in use!");
    //   }
    // }

    const account = this.accountRepository.create({
      organizationId,
      accountOwner: accountOwner ?? null,
      name: createAccountDto.name,
      description: createAccountDto.description ?? null,
      source:
        createAccountDto.source && typeof createAccountDto.source === "string"
          ? createAccountDto.source[0].toUpperCase() +
            createAccountDto.source.slice(1).toLowerCase().replace("_", " ")
          : "Thena",
      primaryDomain: createAccountDto.primaryDomain,
      secondaryDomain: createAccountDto.secondaryDomain ?? null,
      logo: createAccountDto.logo ?? null,
      statusAttribute: statusAttributeValue ?? null,
      classificationAttribute: classificationAttributeValue ?? null,
      healthAttribute: healthAttributeValue ?? null,
      industryAttribute: industryAttributeValue ?? null,
      annualRevenue: createAccountDto.annualRevenue ?? null,
      employees: createAccountDto.employees ?? null,
      website: createAccountDto.website ?? null,
      billingAddress: createAccountDto.billingAddress ?? null,
      shippingAddress: createAccountDto.shippingAddress ?? null,
      customFieldValues,
      metadata: createAccountDto.metadata,
    });

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Save in db
      await this.accountRepository.saveWithTxn(txnContext, account);

      // Invalidate cache for the account with uid and primary domain
      await this.cachedAccountRepository.invalidateAccountCache({
        organizationId,
        accountId: account.uid,
        primaryDomain: account.primaryDomain,
      });

      // Record account created activity
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: currentUser.sub },
          entityId: account.id,
          entityUid: account.uid,
          entityType: AuditLogEntityType.ACCOUNT,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account ${account.id} was created!`,
          description: `Account ${account.id} with primary domain ${account.primaryDomain} was created by ${currentUser.email}!`,
        },
        txnContext,
      );

      // Publish to SNS
      const accountCreatedEvent =
        this.accountsSNSEventsFactory.createAccountCreatedSNSEvent(
          currentUser,
          account,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_CREATED,
        accountCreatedEvent,
        currentUser,
      );
    });

    if (createAccountDto.addExistingUsersToAccountContacts) {
      const triggerAccountContactsCreationEvent =
        this.accountsEventsFactory.createTriggerAccountContactsCreationEvent(
          account.organizationId,
          account.uid,
          account.primaryDomain,
        );
      this.eventEmitter.emit(
        EmittableAccountEvents.LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN,
        triggerAccountContactsCreationEvent,
      );
    }

    // Return from cache
    return this.findAccountDetails(currentUser, account.uid);
  }

  /**
   * Bulk creates accounts.
   * @param currentUser The current user.
   * @param bulkCreateAccountDto {@link CreateAccountDto} The DTO containing the account details.
   * @returns The created accounts.
   */
  async bulkCreateAccounts(
    currentUser: CurrentUser,
    bulkCreateAccountDto: CreateAccountDto[],
  ): Promise<Account[]> {
    if (!bulkCreateAccountDto.length) {
      return [];
    }

    const existingAccounts = await this.findAllAccountsByPrimaryDomains(
      bulkCreateAccountDto.map((dto) => dto.primaryDomain),
      currentUser.orgId,
    );

    const existingAccountsMap = new Map<string, Account>();
    existingAccounts.forEach((account) => {
      existingAccountsMap.set(account.primaryDomain, account);
    });

    const organizationId = currentUser.orgId;

    // Prepare common attribute values and cache them for reuse
    const attributeCache = new Map<string, AccountAttributeValue>();

    const getOrCacheAttributeValue = async (
      attributeValue: string,
      attributeType: AccountAttributeType,
    ): Promise<AccountAttributeValue> => {
      const attributeValueFromCache = attributeCache.get(
        `${attributeType}-${attributeValue}`,
      );
      if (attributeValueFromCache) {
        return attributeValueFromCache;
      }

      const value = await this.validateAndGetAttributeValue(
        attributeValue,
        organizationId,
        attributeType,
      );

      attributeCache.set(`${attributeType}-${attributeValue}`, value);
      return value;
    };

    const defaultStatusAttributeValue =
      await this.accountCommonService.findDefaultAttributeValue(
        organizationId,
        AccountAttributeType.ACCOUNT_STATUS,
      );

    // Prefetch account owners
    const ownerIdsSet = new Set<string>();
    const ownerMap = new Map<string, User>();

    bulkCreateAccountDto.forEach((dto) => {
      if (dto.accountOwnerId) {
        ownerIdsSet.add(dto.accountOwnerId);
      }
    });

    const owners = await this.usersService.findManyByPublicIds(
      Array.from(ownerIdsSet),
    );
    owners.forEach((owner) => {
      ownerMap.set(owner.uid, owner);
    });

    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        const accountsToCreate: Account[] = [];

        for (const createAccountDto of bulkCreateAccountDto) {
          if (existingAccountsMap.has(createAccountDto.primaryDomain)) {
            continue;
          }

          // Get or fetch status attribute value
          let statusAttributeValue: AccountAttributeValue;
          if (createAccountDto.status) {
            statusAttributeValue = await getOrCacheAttributeValue(
              createAccountDto.status,
              AccountAttributeType.ACCOUNT_STATUS,
            );
          } else {
            statusAttributeValue = defaultStatusAttributeValue;
          }

          // Get or fetch other attribute values
          let classificationAttributeValue: AccountAttributeValue;
          if (createAccountDto.classification) {
            classificationAttributeValue = await getOrCacheAttributeValue(
              createAccountDto.classification,
              AccountAttributeType.ACCOUNT_CLASSIFICATION,
            );
          }

          let healthAttributeValue: AccountAttributeValue;
          if (createAccountDto.health) {
            healthAttributeValue = await getOrCacheAttributeValue(
              createAccountDto.health,
              AccountAttributeType.ACCOUNT_HEALTH,
            );
          }

          let industryAttributeValue: AccountAttributeValue;
          if (createAccountDto.industry) {
            industryAttributeValue = await getOrCacheAttributeValue(
              createAccountDto.industry,
              AccountAttributeType.ACCOUNT_INDUSTRY,
            );
          }

          // Get account owner from cache
          let accountOwner: User;
          if (createAccountDto.accountOwnerId) {
            accountOwner = ownerMap.get(createAccountDto.accountOwnerId);
            if (!accountOwner) {
              throw new BadRequestException(
                `Account owner ${createAccountDto.accountOwnerId} not found!`,
              );
            }
          }

          // Create custom field values if needed
          let customFieldValues: CustomFieldValues[];
          if (isArray(createAccountDto.customFieldValues)) {
            customFieldValues =
              await this.customFieldValuesService.createCustomFieldValues(
                createAccountDto.customFieldValues,
                organizationId,
              );
          }

          // Create account entity
          const account = this.accountRepository.create({
            organizationId,
            accountOwner: accountOwner ?? null,
            name: createAccountDto.name,
            description: createAccountDto.description ?? null,
            source: createAccountDto.source,
            primaryDomain: createAccountDto.primaryDomain,
            secondaryDomain: createAccountDto.secondaryDomain ?? null,
            logo: createAccountDto.logo ?? null,
            statusAttribute: statusAttributeValue ?? null,
            classificationAttribute: classificationAttributeValue ?? null,
            healthAttribute: healthAttributeValue ?? null,
            industryAttribute: industryAttributeValue ?? null,
            annualRevenue: createAccountDto.annualRevenue ?? null,
            employees: createAccountDto.employees ?? null,
            website: createAccountDto.website ?? null,
            billingAddress: createAccountDto.billingAddress ?? null,
            shippingAddress: createAccountDto.shippingAddress ?? null,
            customFieldValues,
            metadata: createAccountDto.metadata,
          });

          accountsToCreate.push(account);
        }

        const createdAccounts = await this.accountRepository.saveManyWithTxn(
          txnContext,
          accountsToCreate,
        );

        for (const account of createdAccounts) {
          // Record audit log for account creation
          await this.activitiesService.recordAuditLog(
            {
              organization: { id: organizationId },
              activityPerformedBy: { id: currentUser.sub },
              entityId: account.id,
              entityUid: account.uid,
              entityType: AuditLogEntityType.ACCOUNT,
              op: AuditLogOp.CREATED,
              visibility: AuditLogVisibility.ORGANIZATION,
              activity: `Account ${account.id} was created!`,
              description: `Account ${account.id} with primary domain ${account.primaryDomain} was created by ${currentUser.email}!`,
            },
            txnContext,
          );

          // Publish to SNS
          const accountCreatedEvent =
            this.accountsSNSEventsFactory.createAccountCreatedSNSEvent(
              currentUser,
              account,
            );

          this.accountCommonService.publishEventToSNSQueue(
            AccountEvents.ACCOUNT_CREATED,
            accountCreatedEvent,
            currentUser,
          );
        }

        return [...createdAccounts, ...existingAccounts];
      },
    );
  }

  /**
   * Updates an existing account.
   *
   * @param uid The ID of the account to update.
   * @param currentUser The current user.
   * @param updateAccountDto {@link UpdateAccountDto} The DTO containing the account details.
   * @returns The updated account.
   */
  async updateAccount(
    uid: string,
    currentUser: CurrentUser,
    updateAccountDto: UpdateAccountDto,
  ): Promise<Account> {
    const organizationId = currentUser.orgId;

    const account = await this.findAccountDetails(currentUser, uid);

    const existingAccount = cloneDeep(account);

    if (!account) {
      throw new NotFoundException("Account not found!");
    }

    if (updateAccountDto.status) {
      const statusAttributeValue = await this.validateAndGetAttributeValue(
        updateAccountDto.status,
        organizationId,
        AccountAttributeType.ACCOUNT_STATUS,
      );

      account.statusAttribute = statusAttributeValue;
    }

    if (updateAccountDto.classification) {
      const classificationAttributeValue =
        await this.validateAndGetAttributeValue(
          updateAccountDto.classification,
          organizationId,
          AccountAttributeType.ACCOUNT_CLASSIFICATION,
        );

      account.classificationAttribute = classificationAttributeValue;
    }

    if (updateAccountDto.health) {
      const healthAttributeValue = await this.validateAndGetAttributeValue(
        updateAccountDto.health,
        organizationId,
        AccountAttributeType.ACCOUNT_HEALTH,
      );

      account.healthAttribute = healthAttributeValue;
    }

    if (updateAccountDto.industry) {
      const industryAttributeValue = await this.validateAndGetAttributeValue(
        updateAccountDto.industry,
        organizationId,
        AccountAttributeType.ACCOUNT_INDUSTRY,
      );

      account.industryAttribute = industryAttributeValue;
    }

    if (updateAccountDto.accountOwnerId) {
      const accountOwner = await this.usersService.findOneByPublicId(
        updateAccountDto.accountOwnerId,
      );
      if (!accountOwner) {
        throw new BadRequestException("Account owner not found!");
      }

      account.accountOwner = accountOwner;
    }

    if (updateAccountDto.primaryDomain) {
      // const primaryDomainExists = await this.findIfDomainIsUsed(
      //   updateAccountDto.primaryDomain,
      //   organizationId,
      // );
      // if (primaryDomainExists) {
      //   throw new BadRequestException(
      //     "Primary domain is already in use by another account!",
      //   );
      // }

      account.primaryDomain = updateAccountDto.primaryDomain;
    }

    if (updateAccountDto.secondaryDomain) {
      // const secondaryDomainExists = await this.findIfDomainIsUsed(
      //   updateAccountDto.secondaryDomain,
      //   organizationId,
      // );
      // if (secondaryDomainExists) {
      //   throw new BadRequestException(
      //     "Secondary domain is already in use by another account!",
      //   );
      // }

      account.secondaryDomain = updateAccountDto.secondaryDomain;
    }

    if (isArray(updateAccountDto.customFieldValues)) {
      const newCustomFieldValues: CustomFieldValues[] =
        await this.customFieldValuesService.createCustomFieldValues(
          updateAccountDto.customFieldValues,
          organizationId,
        );

      const allowedPrevCustomFieldValues: CustomFieldValues[] = [];
      const existingCustomFieldValues: CustomFieldValues[] =
        account.customFieldValues ?? [];

      // For an account if C1 has A chosen and C2 has B chosen initially and update req comes with C1 and value C.
      // This means that for C1: A is removed and B is chosen and C2 remains unchanged.
      // In case of multiple choice, if C2 has D E F chosen initially and update req comes for C2 with value G.
      // This means D E F are removed and G is chosen.
      for (const customFieldValue of existingCustomFieldValues) {
        if (
          !newCustomFieldValues.find(
            (value) => value.customField.id === customFieldValue.customField.id,
          )
        ) {
          allowedPrevCustomFieldValues.push(customFieldValue);
        }
      }

      account.customFieldValues = [
        ...allowedPrevCustomFieldValues,
        ...newCustomFieldValues,
      ];
    }

    if (updateAccountDto.metadata) {
      account.metadata = mergeWith(
        account.metadata || {},
        updateAccountDto.metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );
    }

    account.name = updateAccountDto.name ?? account.name;
    account.logo = updateAccountDto.logo ?? account.logo;
    account.description = updateAccountDto.description ?? account.description;
    account.annualRevenue =
      updateAccountDto.annualRevenue ?? account.annualRevenue;
    account.employees = updateAccountDto.employees ?? account.employees;
    account.website = updateAccountDto.website ?? account.website;
    account.billingAddress =
      updateAccountDto.billingAddress ?? account.billingAddress;
    account.shippingAddress =
      updateAccountDto.shippingAddress ?? account.shippingAddress;

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Save in db
      await this.accountRepository.saveWithTxn(txnContext, account);

      // Invalidate cache for the account with uid and primary domain
      await this.cachedAccountRepository.invalidateAccountCache({
        organizationId,
        accountId: account.uid,
        primaryDomain: account.primaryDomain,
      });

      // Record account updated activities
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: organizationId },
          activityPerformedBy: { id: currentUser.sub },
          entityId: account.id,
          entityUid: account.uid,
          entityType: AuditLogEntityType.ACCOUNT,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Account ${account.id} was updated!`,
          description: `Account ${account.id} was updated by ${currentUser.email}!`,
          metadata: {
            updatedFields: [
              existingAccount.name !== account.name && {
                field: "name",
                previousValue: existingAccount.name,
                updatedToValue: account.name,
              },
              existingAccount.logo !== account.logo && {
                field: "logo",
                previousValue: existingAccount.logo,
                updatedToValue: account.logo,
              },
              existingAccount.accountOwnerId !== account.accountOwnerId && {
                field: "accountOwnerId",
                previousValue: existingAccount.accountOwnerId,
                updatedToValue: account.accountOwnerId,
              },
              existingAccount.primaryDomain !== account.primaryDomain && {
                field: "primaryDomain",
                previousValue: existingAccount.primaryDomain,
                updatedToValue: account.primaryDomain,
              },
              existingAccount.status !== account.status && {
                field: "status",
                previousValue: existingAccount.status,
                updatedToValue: account.status,
              },
              existingAccount.classification !== account.classification && {
                field: "classification",
                previousValue: existingAccount.classification,
                updatedToValue: account.classification,
              },
              existingAccount.health !== account.health && {
                field: "health",
                previousValue: existingAccount.health,
                updatedToValue: account.health,
              },
            ].filter(Boolean),
          },
        },
        txnContext,
      );

      // Publish events to SNS
      const accountUpdatedEvent =
        this.accountsSNSEventsFactory.createAccountUpdatedSNSEvent(
          currentUser,
          existingAccount,
          account,
        );

      this.accountCommonService.publishEventToSNSQueue(
        AccountEvents.ACCOUNT_UPDATED,
        accountUpdatedEvent,
        currentUser,
      );
    });

    if (
      existingAccount.primaryDomain !== account.primaryDomain &&
      updateAccountDto.addExistingUsersToAccountContacts
    ) {
      const triggerAccountContactsCreationEvent =
        this.accountsEventsFactory.createTriggerAccountContactsCreationEvent(
          account.organizationId,
          account.uid,
          account.primaryDomain,
        );
      this.eventEmitter.emit(
        EmittableAccountEvents.LINK_CONTACTS_TO_ACCOUNT_BY_EMAIL_DOMAIN,
        triggerAccountContactsCreationEvent,
      );
    }

    // Return from cache
    return this.findAccountDetails(currentUser, account.uid);
  }

  /**
   * Deletes an existing account.
   *
   * @param uid The UID of the account to delete.
   * @param currentUser The current user.
   */
  async deleteAccount(
    uid: string,
    currentUser: CurrentUser,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const organizationId = currentUser.orgId;

    const account = await this.findOneByAccountId(uid, organizationId);
    if (!account) {
      throw new NotFoundException("Account not found!");
    }

    account.isActive = false;
    account.deletedAt = new Date();

    if (metadata) {
      account.metadata = mergeWith(account.metadata || {}, metadata, (a, b) => {
        if (isArray(a)) {
          return a.concat(b);
        }
      });
    }

    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Save in db
        await this.accountRepository.saveWithTxn(txnContext, account);

        // Invalidate cache for the account with uid and primary domain
        await this.cachedAccountRepository.invalidateAccountCache({
          organizationId,
          accountId: account.uid,
          primaryDomain: account.primaryDomain,
        });

        // Record account deleted activity
        await this.activitiesService.recordAuditLog(
          {
            organization: { id: organizationId },
            activityPerformedBy: { id: currentUser.sub },
            entityId: account.id,
            entityUid: account.uid,
            entityType: AuditLogEntityType.ACCOUNT,
            op: AuditLogOp.DELETED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `Account ${account.id} was deleted!`,
            description: `Account ${account.id} was deleted by ${currentUser.email}!`,
            metadata: {
              updatedFields: [
                {
                  field: "isActive",
                  previousValue: "true",
                  updatedToValue: "false",
                },
                {
                  field: "deletedAt",
                  previousValue: null,
                  updatedToValue: account.deletedAt.toISOString(),
                },
              ],
            },
          },
          txnContext,
        );

        // Publish to SNS
        const accountDeletedEvent =
          this.accountsSNSEventsFactory.createAccountDeletedSNSEvent(
            currentUser,
            account,
          );

        this.accountCommonService.publishEventToSNSQueue(
          AccountEvents.ACCOUNT_DELETED,
          accountDeletedEvent,
          currentUser,
        );

        return;
      },
    );
  }

  async getCustomFields(currentUser: CurrentUser): Promise<any> {
    const customFields = await this.accountRepository.findAll({
      where: {
        organizationId: currentUser.orgId,
        isActive: true,
      },
      relations: ["customFieldValues", "customFieldValues.customField"],
    });
    return customFields;
  }
}
