import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  Account,
  AccountAttributeType,
  AccountAttributeValue,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedCustomerContactRepository,
  CustomerContact,
  CustomerContactRepository,
  CustomFieldValues,
  TransactionService,
} from "@repo/thena-platform-entities";
import { cloneDeep, isArray, mergeWith } from "lodash";
import {
  FindOptionsRelations,
  FindOptionsWhere,
  ILike,
  In,
  Like,
} from "typeorm";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators";
import { extractEmailDetails } from "../../common/utils/extract-email-details";
import { CustomFieldValuesService } from "../../custom-field/services/custom-field-values.service";
import { SettingsService } from "../../settings/settings.service";
import {
  BulkCreateCustomerContactsDto,
  CreateCustomerContactDto,
  FindAllCustomerContactsDto,
  SearchCustomerContactsDto,
  UpdateCustomerContactDto,
} from "../dtos/customer-contact.dto";
import { PaginatedResponseDto } from "../dtos/response/common-response.dto";
import { CustomerContactBulkResponseDto } from "../dtos/response/customer-contact.dto";
import { AccountCommonService } from "./account-commons.service";
import { AccountsService } from "./accounts.service";

interface CreateCustomerContactOptions {
  returnIfExists?: boolean;
}

@Injectable()
export class CustomerContactActionService {
  constructor(
    // Injected repositories
    private readonly customerContactRepository: CustomerContactRepository,
    private readonly cachedCustomerContactRepository: CachedCustomerContactRepository,

    // Accounts services
    private readonly accountCommonService: AccountCommonService,
    private readonly accountsService: AccountsService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Activities service
    private readonly activitiesService: ActivitiesService,

    // Custom field values service
    private readonly customFieldValuesService: CustomFieldValuesService,
    private readonly settingsService: SettingsService,
  ) {}

  queryCustomerContacts(
    where: FindOptionsWhere<CustomerContact>,
    relations?: FindOptionsRelations<CustomerContact>,
  ) {
    return this.customerContactRepository.findAll({
      where,
      relations,
    });
  }

  private async validateAndGetContactType(
    orgId: string,
    contactTypeUID: string,
  ): Promise<AccountAttributeValue> {
    const contactType = await this.accountCommonService.getAttributeValue(
      contactTypeUID,
      AccountAttributeType.CONTACT_TYPE,
      orgId,
    );
    if (
      !contactType ||
      contactType.attribute !== AccountAttributeType.CONTACT_TYPE
    ) {
      throw new NotFoundException("Contact type not found");
    }
    return contactType;
  }

  async isContactTypeAttributeInUse(
    contactTypeAttributeId: string,
  ): Promise<boolean> {
    const count = await this.cachedCustomerContactRepository.count({
      where: { contactType: contactTypeAttributeId, isActive: true },
    });
    return count > 0;
  }

  async updateContactTypeAttribute(
    prevContactTypeAttributeId: string,
    newContactTypeAttributeId: string,
  ): Promise<void> {
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Find contacts using the previous contact type attribute
      const customerContacts = await this.customerContactRepository.findAll({
        where: { contactType: prevContactTypeAttributeId },
      });

      if (customerContacts.length === 0) {
        return;
      }

      // Update the contact type attribute for each contact
      for (const customerContact of customerContacts) {
        customerContact.contactType = newContactTypeAttributeId;
      }

      // Save the contacts
      await this.customerContactRepository.saveManyWithTxn(
        txnContext,
        customerContacts,
      );

      // Invalidate the cache for contacts
      this.cachedCustomerContactRepository.invalidateCustomerContactCache({
        organizationId: customerContacts[0].organizationId,
        emails: customerContacts.map(
          (customerContact) => customerContact.email,
        ),
        uids: customerContacts.map((customerContact) => customerContact.uid),
      });
    });
  }

  /**
   * Find customer contact by email
   *
   * @param organizationId - Organization ID
   * @param email - Email
   * @param includeInactive - Whether to include inactive contacts (defaults to false)
   * @returns Customer contact
   */
  public findCustomerContactByEmail(
    organizationId: string,
    email: string,
    includeInactive: boolean = false,
  ): Promise<CustomerContact> {
    const whereClause: any = { email, organizationId };
    
    if (!includeInactive) {
      whereClause.isActive = true;
    }

    return this.customerContactRepository.findByCondition({
      where: whereClause,
      relations: [
        "contactTypeAttribute",
        "accounts",
        "customFieldValues",
        "customFieldValues.customField",
      ],
    });
  }

  /**
   * Find customer contact by uid
   *
   * @param contactId - Contact ID
   * @returns Customer contact
   */
  private findCustomerContactByUID(
    contactId: string,
  ): Promise<CustomerContact> {
    return this.customerContactRepository.findByCondition({
      where: { uid: contactId, isActive: true },
      relations: [
        "contactTypeAttribute",
        "accounts",
        "customFieldValues",
        "customFieldValues.customField",
      ],
    });
  }

  /**
   * Adds contacts matching a domain to an account
   *
   * @param organizationId - Organization ID
   * @param domain - Email domain
   * @param accountId - Account ID
   */
  async addContactsMatchingDomainToAccount(
    organizationId: string,
    domain: string,
    accountId: string,
  ): Promise<void> {
    const account = await this.accountCommonService.validateAndGetAccount(
      accountId,
      organizationId,
    );

    const contacts = await this.customerContactRepository.findAll({
      where: { email: Like(`%@${domain}`), organizationId, isActive: true },
      relations: ["accounts"],
    });

    contacts.map((contact) => {
      contact.accounts.push(account);
    });

    await this.customerContactRepository.saveMany(contacts);
  }

  /**
   * Find customer contacts by account ID and / or contact type
   *
   * - if accountId is provided, finds all contacts for the account
   * - if accountId is not provided, finds all contacts for the organization
   * - if contact type is provided, finds all contacts for the account with the given contact type
   * - if contact type is not provided, finds all contacts for the account
   *
   * @param user - Current user
   * @param query {@link FindAllCustomerContactsDto} - Query to find customer contacts
   * @returns All contacts for the account
   */
  async findCustomerContacts(
    user: CurrentUser,
    query: FindAllCustomerContactsDto,
  ): Promise<PaginatedResponseDto<CustomerContact>> {
    const whereClause: any = {
      organizationId: user.orgId,
      isActive: true,
    };

    if (query.accountId) {
      const account = await this.accountCommonService.validateAndGetAccount(
        query.accountId,
        user.orgId,
      );
      whereClause.accounts = { id: account.id };
    }

    if (query.contactType) {
      const contactType = await this.validateAndGetContactType(
        user.orgId,
        query.contactType,
      );
      whereClause.contactType = contactType.id;
    }

    const results = await this.customerContactRepository.fetchPaginatedResults(
      { page: query.page > 0 ? query.page - 1 : 0, limit: query.limit ?? 10 },
      {
        where: whereClause,
        order: { id: "DESC" },
        relations: [
          "contactTypeAttribute",
          "accounts",
          "customFieldValues",
          "customFieldValues.customField",
        ],
      },
    );

    return {
      results: results.results,
      meta: {
        totalCount: results.total,
        totalPages: Math.ceil(results.total / results.results.length),
        currentPage: query.page > 0 ? query.page : 1,
        currentPageCount: results.results.length,
      },
    };
  }

  filterCustomerContactsByIds(user: CurrentUser, ids: string[]) {
    return this.customerContactRepository.findAll({
      where: { uid: In(ids), organizationId: user.orgId, isActive: true },
      relations: [
        "contactTypeAttribute",
        "accounts",
        "customFieldValues",
        "customFieldValues.customField",
      ],
    });
  }

  /**
   * Create a new customer contact
   *
   * @param user - Current user
   * @param createDto {@link CreateCustomerContactDto} - Create customer contact DTO
   * @returns The created customer contact
   */
  async createCustomerContact(
    user: CurrentUser,
    createDto: CreateCustomerContactDto,
    options?: CreateCustomerContactOptions,
  ): Promise<CustomerContact> {
    // Find account
    const accounts: Account[] = [];
    if (createDto.accountIds) {
      for (const accountId of createDto.accountIds) {
        const account = await this.accountCommonService.validateAndGetAccount(
          accountId,
          user.orgId,
        );
        accounts.push(account);
      }
    }

    let contactType: AccountAttributeValue;
    if (!createDto.contactType) {
      // Use default contact type if not provided
      const defaultContactType =
        await this.accountCommonService.findDefaultAttributeValue(
          user.orgId,
          AccountAttributeType.CONTACT_TYPE,
        );

      contactType = defaultContactType;
    } else {
      // Use provided contact type
      contactType = await this.validateAndGetContactType(
        user.orgId,
        createDto.contactType,
      );
    }

    // Find existing contact by email (including inactive ones)
    const existingContact = await this.findCustomerContactByEmail(
      user.orgId,
      createDto.email,
      true, // includeInactive = true
    );
    
    if (existingContact) {
      // If contact is active
      if (existingContact.isActive) {
        // If returnIfExists is true, return the existing contact
        if (options?.returnIfExists) {
          return existingContact;
        }
        throw new BadRequestException("Contact with this email already exists");
      } else {
        // Contact is inactive - reactivate and update it
        let customFieldValues: CustomFieldValues[];
        if (isArray(createDto.customFieldValues)) {
          customFieldValues =
            await this.customFieldValuesService.createCustomFieldValues(
              createDto.customFieldValues,
              user.orgId,
            );
        }

        await this.transactionService.runInTransaction(async (txnContext) => {
          // Update the contact with new details
          if (createDto.firstName !== undefined) {
            existingContact.firstName = createDto.firstName;
          }
          if (createDto.lastName !== undefined) {
            existingContact.lastName = createDto.lastName;
          }
          if (createDto.phoneNumber !== undefined) {
            existingContact.phoneNumber = createDto.phoneNumber;
          }
          if (createDto.avatarUrl !== undefined) {
            existingContact.avatarUrl = createDto.avatarUrl;
          }
          
          existingContact.isActive = true;
          existingContact.contactTypeAttribute = contactType;
          
          if (customFieldValues) {
            const existingCustomFieldValues = existingContact.customFieldValues;
            existingContact.customFieldValues = [
              ...existingCustomFieldValues,
              ...customFieldValues,
            ];
          }
          
          if (createDto.metadata) {
            existingContact.metadata = createDto.metadata;
          }

          // Update accounts if provided
          if (accounts.length > 0) {
            existingContact.accounts = accounts;
          } else if (accounts.length === 0) {
            const createAccountSettingsOnContactCreation =
              (await this.settingsService.getKey(
                "account_creation_while_contact_creation",
                {
                  organizationId: user.orgId,
                  userId: null,
                },
              )) || false;

            if (createAccountSettingsOnContactCreation) {
              const emailDomain = extractEmailDetails(createDto.email).domain;
              const account =
                await this.accountsService.getOrCreateAccountFromPrimaryDomain(
                  user,
                  emailDomain,
                );
              existingContact.accounts = [account];
            }
          }

          // Save updated contact
          await this.customerContactRepository.saveWithTxn(
            txnContext,
            existingContact,
          );

          // Record audit log
          await this.activitiesService.recordAuditLog(
            {
              organization: { id: user.orgId },
              activityPerformedBy: { id: user.sub },
              entityId: existingContact.id,
              entityUid: existingContact.uid,
              entityType: AuditLogEntityType.CUSTOMER_CONTACT,
              op: AuditLogOp.UPDATED,
              visibility: AuditLogVisibility.ORGANIZATION,
              activity: `Customer contact ${existingContact.email} was reactivated!`,
              description: `Customer contact ${existingContact.email} was reactivated and updated by ${user.email}!`,
            },
            txnContext,
          );

          // Invalidate cache
          await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
            {
              organizationId: user.orgId,
              emails: [createDto.email],
              uids: [existingContact.uid],
            },
          );
        });

        // Return the reactivated contact
        return this.findCustomerContactByEmail(user.orgId, createDto.email);
      }
    }

    // No existing contact found - create new one
    let customFieldValues: CustomFieldValues[];
    if (isArray(createDto.customFieldValues)) {
      customFieldValues =
        await this.customFieldValuesService.createCustomFieldValues(
          createDto.customFieldValues,
          user.orgId,
        );
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      if (accounts.length === 0) {
        const createAccountSettingsOnContactCreation =
          (await this.settingsService.getKey(
            "account_creation_while_contact_creation",
            {
              organizationId: user.orgId,
              userId: null,
            },
          )) || false;

        if (createAccountSettingsOnContactCreation) {
          const emailDomain = extractEmailDetails(createDto.email).domain;

          const account =
            await this.accountsService.getOrCreateAccountFromPrimaryDomain(
              user,
              emailDomain,
            );

          accounts.push(account);
        }
      }

      const customerContact = this.customerContactRepository.create({
        organizationId: user.orgId,
        firstName: createDto.firstName,
        lastName: createDto.lastName,
        email: createDto.email,
        phoneNumber: createDto.phoneNumber,
        accounts,
        contactTypeAttribute: contactType,
        avatarUrl: createDto.avatarUrl,
        ...(createDto.metadata && { metadata: createDto.metadata }),
        customFieldValues,
      });

      // Save customer contact
      await this.customerContactRepository.saveWithTxn(
        txnContext,
        customerContact,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: customerContact.id,
          entityUid: customerContact.uid,
          entityType: AuditLogEntityType.CUSTOMER_CONTACT,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Customer contact ${customerContact.email} was created!`,
          description: `Customer contact ${customerContact.email}  was created by ${user.email}!`,
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
        {
          organizationId: user.orgId,
          emails: [createDto.email],
          uids: [customerContact.uid],
        },
      );
    });

    // Return from cache
    return this.findCustomerContactByEmail(user.orgId, createDto.email);
  }

  /**
   * Bulk create customer contacts
   *
   * @param user - Current user
   * @param createDto {@link BulkCreateCustomerContactsDto} - Bulk create customer contacts DTO
   * @returns The created customer contacts
   */
  async bulkCreateCustomerContacts(
    user: CurrentUser,
    createDto: BulkCreateCustomerContactsDto,
  ): Promise<CustomerContactBulkResponseDto> {
    if (!createDto.contacts.length) {
      throw new BadRequestException("No contacts to create");
    }

    const accounts: Account[] = [];
    if (createDto.accountIds) {
      // Find account
      for (const accountId of createDto.accountIds) {
        const account = await this.accountCommonService.validateAndGetAccount(
          accountId,
          user.orgId,
        );
        accounts.push(account);
      }
    }

    // Find contact type
    let contactType: AccountAttributeValue;
    if (createDto.contactType) {
      contactType = await this.validateAndGetContactType(
        user.orgId,
        createDto.contactType,
      );
    } else {
      contactType = await this.accountCommonService.findDefaultAttributeValue(
        user.orgId,
        AccountAttributeType.CONTACT_TYPE,
      );
    }

    // Find existing contacts by email and skip them if they exist
    const existingContacts = await this.customerContactRepository.findAll({
      where: {
        email: In(createDto.contacts.map((contact) => contact.email)),
        isActive: true,
      },
    });

    const contactsToCreate = createDto.contacts.filter(
      (contact) => !existingContacts.some((c) => c.email === contact.email),
    );

    const distinctEmails = [
      ...new Set(contactsToCreate.map((contact) => contact.email)),
    ];

    if (distinctEmails.length !== contactsToCreate.length) {
      throw new BadRequestException(
        "Duplicate emails found in the list of contacts to create",
      );
    }

    let domainToAccountMap: Map<string, Account>;

    if (accounts.length === 0) {
      const createAccountSettingsOnContactCreation =
        (await this.settingsService.getKey(
          "account_creation_while_contact_creation",
          {
            organizationId: user.orgId,
            userId: null,
          },
        )) || false;

      if (createAccountSettingsOnContactCreation) {
        const emailDomains = [
          ...new Set(
            contactsToCreate.map(
              (contact) => extractEmailDetails(contact.email).domain,
            ),
          ),
        ];

        domainToAccountMap =
          await this.accountsService.getOrCreateAccountsFromPrimaryDomains(
            user,
            emailDomains,
            createDto.source,
          );
      }
    }

    // Create contacts
    const createdContacts = contactsToCreate.map((contact) => {
      let contactAccounts = [...accounts];

      if (accounts.length === 0 && domainToAccountMap) {
        const domain = extractEmailDetails(contact.email).domain;
        const accountForDomain = domainToAccountMap.get(domain);
        if (accountForDomain) {
          contactAccounts = [accountForDomain];
        }
      }

      return this.customerContactRepository.create({
        organizationId: user.orgId,
        firstName: contact.firstName,
        lastName: contact.lastName,
        email: contact.email,
        phoneNumber: contact.phoneNumber,
        accounts: contactAccounts,
        contactTypeAttribute: contactType,
        avatarUrl: contact.avatarUrl,
      });
    });

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Save contacts
      const savedContacts =
        await this.customerContactRepository.saveManyWithTxn(
          txnContext,
          createdContacts,
        );

      // Record audit logs only for newly created contacts
      await this.activitiesService.recordBulkAuditLog(
        savedContacts.map((contact) => ({
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: contact.id,
          entityUid: contact.uid,
          entityType: AuditLogEntityType.CUSTOMER_CONTACT,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Customer contact ${contact.email} was created`,
          description: `Customer contact ${contact.email} was created by ${user.email}!`,
        })),
        txnContext,
      );

      // Invalidate cache
      await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
        {
          organizationId: user.orgId,
          emails: createdContacts.map((contact) => contact.email),
          uids: createdContacts.map((contact) => contact.uid),
        },
      );
    });

    return {
      total: createDto.contacts.length,
      created: createdContacts.length,
      skipped: createDto.contacts.length - createdContacts.length,
    };
  }

  /**
   * Update a customer contact
   *
   * @param user - Current user
   * @param contactId - Contact ID
   * @param updateDto {@link UpdateCustomerContactDto} - Update customer contact DTO
   * @returns The updated customer contact
   */
  async updateCustomerContact(
    user: CurrentUser,
    contactId: string,
    updateDto: UpdateCustomerContactDto,
  ): Promise<CustomerContact> {
    // Find existing contact by email and throw error if it exists
    const customerContact = await this.findCustomerContactByUID(contactId);
    if (!customerContact) {
      throw new NotFoundException("Contact not found");
    }

    const existingContact = cloneDeep(customerContact);

    if (updateDto.accountIds) {
      const newAccounts: Account[] = [];
      for (const accountId of updateDto.accountIds) {
        const account = await this.accountCommonService.validateAndGetAccount(
          accountId,
          user.orgId,
        );
        newAccounts.push(account);
      }

      customerContact.accounts = newAccounts;
    }

    if (updateDto.contactType) {
      const contactType = await this.validateAndGetContactType(
        user.orgId,
        updateDto.contactType,
      );

      customerContact.contactTypeAttribute = contactType;
    }

    if (updateDto.email && updateDto.email !== customerContact.email) {
      const existingContactByEmail = await this.findCustomerContactByEmail(
        user.orgId,
        updateDto.email,
      );

      if (existingContactByEmail) {
        throw new BadRequestException("Contact with this email already exists");
      }

      customerContact.email = updateDto.email;
    }

    if (isArray(updateDto.customFieldValues)) {
      const newCustomFieldValues: CustomFieldValues[] =
        await this.customFieldValuesService.createCustomFieldValues(
          updateDto.customFieldValues,
          user.orgId,
        );

      const allowedPrevCustomFieldValues: CustomFieldValues[] = [];
      const existingCustomFieldValues: CustomFieldValues[] =
        customerContact.customFieldValues ?? [];

      // For an account if C1 has A chosen and C2 has B chosen initially and update req comes with C1 and value C.
      // This means that for C1: A is removed and B is chosen and C2 remains unchanged.
      // In case of multiple choice, if C2 has D E F chosen initially and update req comes for C2 with value G.
      // This means D E F are removed and G is chosen.
      for (const customFieldValue of existingCustomFieldValues) {
        if (
          !newCustomFieldValues.find(
            (value) => value.customField.id === customFieldValue.customField.id,
          )
        ) {
          allowedPrevCustomFieldValues.push(customFieldValue);
        }
      }

      customerContact.customFieldValues = [
        ...allowedPrevCustomFieldValues,
        ...newCustomFieldValues,
      ];
    }

    if (updateDto.metadata) {
      customerContact.metadata = mergeWith(
        customerContact.metadata || {},
        updateDto.metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );
    }

    customerContact.firstName =
      updateDto.firstName ?? customerContact.firstName;
    customerContact.lastName = updateDto.lastName ?? customerContact.lastName;
    customerContact.phoneNumber =
      updateDto.phoneNumber ?? customerContact.phoneNumber;
    customerContact.avatarUrl =
      updateDto.avatarUrl ?? customerContact.avatarUrl;

    await this.transactionService.runInTransaction(async (txnContext) => {
      // Save customer contact
      await this.customerContactRepository.saveWithTxn(
        txnContext,
        customerContact,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: customerContact.id,
          entityUid: customerContact.uid,
          entityType: AuditLogEntityType.CUSTOMER_CONTACT,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Customer contact ${customerContact.email} was updated!`,
          description: `Customer contact ${customerContact.email} was updated by ${user.email}!`,
          metadata: {
            updatedFields: Object.keys(updateDto).map((key) => ({
              field: key,
              previousValue: existingContact[key],
              updatedToValue: customerContact[key],
            })),
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
        {
          organizationId: user.orgId,
          emails: [customerContact.email],
          uids: [customerContact.uid],
        },
      );
    });

    // Return from cache
    return this.findCustomerContactByEmail(user.orgId, customerContact.email);
  }

  /**
   * Delete a customer contact
   *
   * @param user - Current user
   * @param contactId - Contact ID
   */
  async deleteCustomerContact(
    user: CurrentUser,
    contactId: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    // Find existing contact by email and throw error if it exists
    const existingContact = await this.findCustomerContactByUID(contactId);
    if (!existingContact) {
      throw new NotFoundException("Contact not found");
    }

    await this.transactionService.runInTransaction(async (txnContext) => {
      existingContact.isActive = false;
      existingContact.metadata = mergeWith(
        existingContact.metadata || {},
        metadata,
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );

      // Save customer contact
      await this.customerContactRepository.saveWithTxn(
        txnContext,
        existingContact,
      );

      // Record audit log
      await this.activitiesService.recordAuditLog(
        {
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: existingContact.id,
          entityUid: existingContact.uid,
          entityType: AuditLogEntityType.CUSTOMER_CONTACT,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Customer contact ${existingContact.email} was deleted!`,
          description: `Customer contact ${existingContact.email} was deleted by ${user.email}!`,
          metadata: {
            updatedFields: [
              {
                field: "isActive",
                previousValue: "true",
                updatedToValue: "false",
              },
            ],
          },
        },
        txnContext,
      );

      // Invalidate cache
      await this.cachedCustomerContactRepository.invalidateCustomerContactCache(
        {
          organizationId: user.orgId,
          emails: [existingContact.email],
          uids: [existingContact.uid],
        },
      );
    });
  }

  searchCustomerContacts(
    user: CurrentUser,
    query: SearchCustomerContactsDto,
  ): Promise<CustomerContact[]> {
    const whereClause: any = {
      organizationId: user.orgId,
      isActive: true,
      email: ILike(`%${query.email}%`),
    };

    return this.customerContactRepository.findAll({
      where: whereClause,
      relations: [
        "contactTypeAttribute",
        "accounts",
        "customFieldValues",
        "customFieldValues.customField",
      ],
    });
  }
}
