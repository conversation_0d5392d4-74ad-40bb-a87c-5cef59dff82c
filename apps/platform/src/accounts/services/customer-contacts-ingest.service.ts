import {
  BadRequestException,
  ForbiddenException,
  Injectable,
} from "@nestjs/common";
import {
  Account,
  CustomerContact,
  CustomerContactRepository,
  TransactionService,
  UserType,
} from "@repo/thena-platform-entities";
import { IngestCustomerContactDTO } from "@repo/thena-shared-interfaces";
import { merge } from "lodash";
import { DeepPartial, In } from "typeorm";
import { CurrentUser } from "../../common/decorators";
import { extractEmailDetails } from "../../common/utils/extract-email-details";
import { SettingsService } from "../../settings/settings.service";
import { CustomerContactResponseDto } from "../dtos/response/customer-contact.dto";
import { AccountsService } from "./accounts.service";

@Injectable()
export class CustomerContactsIngestService {
  constructor(
    // User repositories
    private readonly customerContactRepository: CustomerContactRepository,

    // Injected services
    private readonly transactionService: TransactionService,

    // Injected services
    private readonly accountsService: AccountsService,

    // Injected services
    private readonly settingsService: SettingsService,
  ) {}

  /**
   * Ingests customer contacts
   * @param currentUser The current user
   * @param data The data to ingest
   * @returns The ingested customer contacts
   */
  async ingestCustomerContacts(
    currentUser: CurrentUser,
    data: IngestCustomerContactDTO,
  ): Promise<CustomerContactResponseDto[]> {
    // Check if the current user is an organization admin
    const isOrgAdmin = currentUser.userType === UserType.ORG_ADMIN;

    // Check if the current user is the same as the organization
    const isSameOrg = currentUser.orgId === currentUser.orgId;

    // If the current user is not the same as the organization, throw an error
    if (!isOrgAdmin && !isSameOrg) {
      throw new ForbiddenException(
        "You are not authorized to ingest users for this organization",
      );
    }

    // If the users array is empty, throw an error
    if (data.users.length === 0) {
      throw new BadRequestException("Users array is empty");
    }

    // If the sink source is not provided, throw an error
    if (data.users.length > 1000) {
      throw new BadRequestException(
        "You can only ingest up to 1000 users at a time",
      );
    }

    const accountsMap: Map<string, Account> = new Map();
    if (data.users.some((user) => user.accountIds)) {
      // Find accounts
      const accounts = await this.accountsService.findAccountsByPublicIds(
        data.users.flatMap((user) => user.accountIds),
        currentUser.orgId,
      );

      accounts.forEach((account) => {
        accountsMap.set(account.uid, account);
      });
    }

    // Add domain-based account creation logic
    let domainToAccountMap: Map<string, Account> = new Map();
    if (
      data.users.some(
        (user) => !user.accountIds || user.accountIds.length === 0,
      )
    ) {
      const createAccountSettingsOnContactCreation =
        (await this.settingsService.getKey(
          "account_creation_while_contact_creation",
          {
            organizationId: currentUser.orgId,
            userId: null,
          },
        )) || false;

      if (createAccountSettingsOnContactCreation) {
        // Extract unique domains from users that don't have account IDs
        const emailDomains = [
          ...new Set(
            data.users
              .filter(
                (user) => !user.accountIds || user.accountIds.length === 0,
              )
              .map((user) => extractEmailDetails(user.email).domain),
          ),
        ];

        // Create or get accounts based on domains
        domainToAccountMap =
          await this.accountsService.getOrCreateAccountsFromPrimaryDomains(
            currentUser,
            emailDomains,
          );
      }
    }

    // Get the existing contacts
    const existingContacts = await this.customerContactRepository.findAll({
      where: {
        email: In(data.users.map((user) => user.email)),
        organizationId: currentUser.orgId,
      },
    });

    const existingContactsMap = new Map(
      existingContacts.map((contact) => [contact.email, contact]),
    );

    // Create the raw customer contacts
    const rawCustomers: Array<DeepPartial<CustomerContact>> = data.users?.map(
      (user) => {
        let accounts =
          user.accountIds?.map((accountId) => accountsMap.get(accountId)) || [];

        // If user has no account IDs, try to find an account based on their email domain
        if (accounts.length === 0 && domainToAccountMap.size > 0) {
          const domain = extractEmailDetails(user.email).domain;
          const accountForDomain = domainToAccountMap.get(domain);
          if (accountForDomain) {
            accounts = [accountForDomain];
          }
        }

        if (existingContactsMap.has(user.email)) {
          const existingMetadata = existingContactsMap.get(user.email).metadata;
          const newMetadata = merge(existingMetadata || {}, user.metadata);
          const existingAccounts =
            existingContactsMap.get(user.email)?.accounts || [];

          return {
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            avatarUrl: user.avatarUrl,
            organizationId: currentUser.orgId,
            phoneNumber: user.phoneNumber,
            accounts: [...existingAccounts, ...accounts],
            metadata: newMetadata,
          };
        }
        return {
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          organizationId: currentUser.orgId,
          phoneNumber: user.phoneNumber,
          avatarUrl: user.avatarUrl,
          accounts,
          metadata: user.metadata,
        };
      },
    );

    // Start transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Create customer contacts
      await this.customerContactRepository.upsertWithTxn(
        txnContext,
        rawCustomers,
        {
          conflictPaths: ["email", "organizationId"],
        },
      );
    });

    const ingestedContacts = await this.customerContactRepository.findAll({
      where: {
        email: In(rawCustomers.map((customer) => customer.email)),
        organization: { id: currentUser.orgId },
      },
      relations: ["accounts"],
    });

    const ingestedContactsMap = new Map(
      ingestedContacts.map((contact) => [contact.email, contact]),
    );

    for (const rawCustomer of rawCustomers) {
      if (rawCustomer.accounts.length > 0) {
        const contact = ingestedContactsMap.get(rawCustomer.email);

        if (contact) {
          const existingAccountIds = new Set(
            contact.accounts.map((account) => account.id),
          );

          const newAccountIds = rawCustomer.accounts
            .filter(
              (account) =>
                account && account.id && !existingAccountIds.has(account.id),
            )
            .map((account) => account.id);

          if (newAccountIds.length > 0) {
            await this.customerContactRepository
              .createQueryBuilder()
              .relation(CustomerContact, "accounts")
              .of(contact.id)
              .add(newAccountIds);
          }
        }
      }
    }

    // Return the ingested customer contacts
    return ingestedContacts.map(CustomerContactResponseDto.fromEntity);
  }
}
