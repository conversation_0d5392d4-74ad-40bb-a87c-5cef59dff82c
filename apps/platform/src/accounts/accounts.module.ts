import { BullModule } from "@nestjs/bullmq";
import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SentryService } from "@repo/nestjs-commons/filters";
import {
  ApiKeyGrpcStrategy,
  AuthenticationGrpcClient,
  BearerTokenGrpcStrategy,
  GrpcAuthGuard,
  UserOrgInternalGrpcStrategy,
} from "@repo/nestjs-commons/guards";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SNSPublisherService } from "@repo/thena-eventbridge";
import {
  Account,
  AccountActivity,
  AccountActivityRepository,
  AccountAttributeValue,
  AccountAttributeValueRepository,
  AccountNote,
  AccountNoteRepository,
  AccountRelationship,
  AccountRelationshipRepository,
  AccountRelationshipType,
  AccountRelationshipTypeRepository,
  AccountRepository,
  AccountTask,
  AccountTaskRepository,
  CachedAccountActivityRepository,
  CachedAccountAttributeValueRepository,
  CachedAccountNoteRepository,
  CachedAccountRelationshipRepository,
  CachedAccountRelationshipTypeRepository,
  CachedAccountRepository,
  CachedAccountTaskRepository,
  CachedCustomerContactRepository,
  CustomerContact,
  CustomerContactRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { ActivitiesModule } from "../activities/activities.module";
import { CommonModule } from "../common/common.module";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { QueueNames } from "../constants/queue.constants";
import { CustomFieldModule } from "../custom-field/custom-field.module";
import { SettingsModule } from "../settings/settings.module";
import { StorageModule } from "../storage/storage.module";
import { UsersModule } from "../users/users.module";
import { AccountActivityActionController } from "./controllers/account-activity.action.controller";
import { AccountAttributeValueActionController } from "./controllers/account-attribute-value.action.controller";
import { AccountNoteActionController } from "./controllers/account-note.action.controller";
import { AccountRelationshipActionController } from "./controllers/account-relationship.action.controller";
import { AccountTaskActionController } from "./controllers/account-task.action.controller";
import { AccountsController } from "./controllers/accounts.controller";
import { CustomerContactActionController } from "./controllers/customer-contact.action.controller";
import { CustomerContactsIngestController } from "./controllers/customer-contacts-ingest.controller";
import { AccountActivitiesGrpcController } from "./controllers/grpc/account-activities.grpc.controller";
import { AccountAnnotatorGrpcController } from "./controllers/grpc/account-annotator.grpc.controller";
import { AccountAttributeValuesGrpcController } from "./controllers/grpc/account-attribute-values.grpc.controller";
import { AccountNotesGrpcController } from "./controllers/grpc/account-notes.grpc.controller";
import { AccountRelationshipsGrpcController } from "./controllers/grpc/account-relationships.grpc.controller";
import { AccountTasksGrpcController } from "./controllers/grpc/account-tasks.grpc.controller";
import { AccountsGrpcController } from "./controllers/grpc/accounts.grpc.controller";
import { AccountsEventsFactory } from "./events/accounts-events.factory";
import { AccountsSNSEventsFactory } from "./events/accounts-sns-events.factory";
import { AccountsListeners } from "./listeners/accounts.listeners";
import { AccountsSNSPublisher } from "./processors/sns-publisher.processor";
import { AccountActivityActionService } from "./services/account-activity.action.service";
import { AccountAnnotatorService } from "./services/account-annotator.service";
import { AccountAttributeValueActionService } from "./services/account-attribute-value.action.service";
import { AccountCommonService } from "./services/account-commons.service";
import { AccountNoteActionService } from "./services/account-note.action.service";
import { AccountRelationshipActionService } from "./services/account-relationship.action.service";
import { AccountTaskActionService } from "./services/account-task.action.service";
import { AccountsService } from "./services/accounts.service";
import { CustomerContactActionService } from "./services/customer-contact.action.service";
import { CustomerContactsIngestService } from "./services/customer-contacts-ingest.service";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Account,
      CustomerContact,
      AccountAttributeValue,
      AccountRelationship,
      AccountRelationshipType,
      AccountActivity,
      AccountTask,
      AccountNote,

      AccountRepository,
      CustomerContactRepository,
      AccountAttributeValueRepository,
      AccountRelationshipRepository,
      AccountRelationshipTypeRepository,
      AccountActivityRepository,
      AccountNoteRepository,
      AccountTaskRepository,
    ]),
    ConfigModule,
    CommonModule,
    UsersModule,
    ActivitiesModule,
    CustomFieldModule,
    StorageModule,
    SettingsModule,
    BullModule.registerQueueAsync({
      name: QueueNames.ACCOUNTS_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
  ],
  controllers: [
    AccountsController,
    AccountAttributeValueActionController,
    AccountRelationshipActionController,
    CustomerContactActionController,
    AccountActivityActionController,
    AccountNoteActionController,
    AccountTaskActionController,
    CustomerContactsIngestController,

    // gRPC controllers
    AccountAnnotatorGrpcController,
    AccountsGrpcController,
    AccountRelationshipsGrpcController,
    AccountActivitiesGrpcController,
    AccountNotesGrpcController,
    AccountTasksGrpcController,
    AccountAttributeValuesGrpcController,
  ],
  providers: [
    // SNS publisher
    {
      provide: "ACCOUNTS_SNS_PUBLISHER",
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) => {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_ACCOUNTS_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, "Sentry", "CustomLogger"],
    },

    // Services
    AccountsService,
    AccountCommonService,
    AccountAttributeValueActionService,
    AccountRelationshipActionService,
    CustomerContactActionService,
    AccountActivityActionService,
    AccountNoteActionService,
    AccountTaskActionService,
    AccountAnnotatorService,
    CustomerContactsIngestService,

    // Repositories
    TransactionService,
    AccountRepository,
    CustomerContactRepository,
    AccountAttributeValueRepository,
    AccountRelationshipRepository,
    AccountRelationshipTypeRepository,
    AccountActivityRepository,
    AccountNoteRepository,
    AccountTaskRepository,

    CachedAccountRepository,
    CachedCustomerContactRepository,
    CachedAccountAttributeValueRepository,
    CachedAccountRelationshipRepository,
    CachedAccountRelationshipTypeRepository,
    CachedAccountActivityRepository,
    CachedAccountNoteRepository,
    CachedAccountTaskRepository,

    // Events
    AccountsEventsFactory,
    AccountsSNSEventsFactory,

    // Listeners
    AccountsListeners,
    AccountsSNSPublisher,

    // gRPC Auth guard
    AuthenticationGrpcClient,
    GrpcAuthGuard,
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    UserOrgInternalGrpcStrategy,
  ],
  exports: [
    // Services
    AccountsService,
    AccountAttributeValueActionService,
    AccountRelationshipActionService,
    CustomerContactActionService,
    AccountActivityActionService,
    AccountNoteActionService,
    AccountTaskActionService,
    AccountAnnotatorService,
  ],
})
export class AccountsModule {}
