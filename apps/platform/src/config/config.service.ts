import { Injectable } from "@nestjs/common";
import * as dotenv from "dotenv";
import * as fs from "fs";
import * as <PERSON><PERSON> from "joi";

export enum ConfigKeys {
  APP_TAG = "APP_TAG",
  SERVICE_TAG = "SERVICE_TAG",
  NODE_ENV = "NODE_ENV",
  BASE_URL = "BASE_URL",

  // Auth
  JWT_SECRET = "JWT_SECRET",

  // Database
  THENA_PLATFORM_DB_HOST = "THENA_PLATFORM_DB_HOST",
  THENA_PLATFORM_DB_PORT = "THENA_PLATFORM_DB_PORT",
  THENA_PLATFORM_DB_NAME = "THENA_PLATFORM_DB_NAME",
  THENA_PLATFORM_DB_USER = "THENA_PLATFORM_DB_USER",
  THENA_PLATFORM_DB_PASSWORD = "THENA_PLATFORM_DB_PASSWORD",

  // Redis
  REDIS_HOST = "REDIS_HOST",
  REDIS_PORT = "REDIS_PORT",
  REDIS_USERNAME = "REDIS_USERNAME",
  REDIS_PASSWORD = "REDIS_PASSWORD",

  // AWS Secrets
  AWS_ACCESS_KEY = "AWS_ACCESS_KEY",
  AWS_SECRET_KEY = "AWS_SECRET_KEY",
  AWS_REGION = "AWS_REGION",

  AWS_SNS_TICKET_TOPIC_ARN = "AWS_SNS_TICKET_TOPIC_ARN",
  AWS_SNS_ACCOUNTS_TOPIC_ARN = "AWS_SNS_ACCOUNTS_TOPIC_ARN",
  AWS_SNS_ORGANIZATION_TOPIC_ARN = "AWS_SNS_ORGANIZATION_TOPIC_ARN",
  AWS_SNS_CUSTOM_OBJECT_TOPIC_ARN = "AWS_SNS_CUSTOM_OBJECT_TOPIC_ARN",
  AWS_SQS_NOTIFICATIONS_QUEUE_URL = "AWS_SQS_NOTIFICATIONS_QUEUE_URL",

  // Sentry
  SENTRY_DSN = "SENTRY_DSN",

  // gRPC
  PLATFORM_GRPC_URL = "PLATFORM_GRPC_URL",
  WORKFLOWS_GRPC_URL = "WORKFLOWS_GRPC_URL",
  AUTH_GRPC_URL = "AUTH_GRPC_URL",
  APPS_GRPC_URL = "APPS_GRPC_URL",

  // Knock
  KNOCK_API_KEY = "KNOCK_API_KEY",
  KNOCK_SLACK_CHANNEL_ID = "KNOCK_SLACK_CHANNEL_ID",
  APPS_STUDIO_SLACK_APP_ID = "APPS_STUDIO_SLACK_APP_ID",

  // Supabase
  SUPABASE_URL = "SUPABASE_URL",
  SUPABASE_KEY = "SUPABASE_KEY",
  SUPABASE_BUCKET = "SUPABASE_BUCKET",
  ENCRYPTION_KEY = "ENCRYPTION_KEY",

  // VAULT FOR CERTIFICATES
  VAULT_URL = "VAULT_URL",
  VAULT_TOKEN = "VAULT_TOKEN",
  CERT_PATH = "CERT_PATH",

  // BullMQ Dashboard
  BULL_BOARD_ADMIN_USER = "BULL_BOARD_ADMIN_USER",
  BULL_BOARD_ADMIN_PASSWORD = "BULL_BOARD_ADMIN_PASSWORD",

  // Swagger
  GENERATE_SWAGGER = "GENERATE_SWAGGER",

  EMAIL_GRPC_URL = "EMAIL_GRPC_URL",
  // Typesense
  TYPESENSE_HOST = "TYPESENSE_HOST",
  TYPESENSE_API_KEY = "TYPESENSE_API_KEY",
  /**
   * This key must be a Typesense search-only API key (with only 'documents:search' permission).
   * Do NOT use the global admin/master key here. This is critical for security.
   */
  TYPESENSE_SEARCH_ONLY_API_KEY = "TYPESENSE_SEARCH_ONLY_API_KEY",
}

@Injectable()
export class ConfigService {
  private readonly envConfig: { [key: string]: string };

  constructor(filePath: string) {
    // Load the .env file
    const config = fs.existsSync(filePath)
      ? dotenv.parse(fs.readFileSync(filePath))
      : process.env;

    // Load the secrets from AWS Secrets Manager
    this.envConfig = this.validateEnv(config);
  }

  get(key: ConfigKeys): string {
    if (!(key in this.envConfig)) {
      throw new Error(`Configuration key "${key}" does not exist`);
    }
    return String(this.envConfig[key]);
  }

  private validateEnv(config: { [key: string]: string }): {
    [key: string]: string;
  } {
    const envVarsSchema = Joi.object({
      [ConfigKeys.APP_TAG]: Joi.string().required(),
      [ConfigKeys.SERVICE_TAG]: Joi.string().required(),
      [ConfigKeys.BASE_URL]: Joi.string().required(),
      [ConfigKeys.NODE_ENV]: Joi.string()
        .valid("development", "production", "test", "staging")
        .required(),
      // Database
      [ConfigKeys.THENA_PLATFORM_DB_HOST]: Joi.string().required(),
      [ConfigKeys.THENA_PLATFORM_DB_PORT]: Joi.number().required(),
      [ConfigKeys.THENA_PLATFORM_DB_NAME]: Joi.string().required(),
      [ConfigKeys.THENA_PLATFORM_DB_USER]: Joi.string().required(),
      [ConfigKeys.THENA_PLATFORM_DB_PASSWORD]: Joi.string().required(),
      // AWS
      [ConfigKeys.AWS_ACCESS_KEY]: Joi.string().required(),
      [ConfigKeys.AWS_SECRET_KEY]: Joi.string().required(),
      [ConfigKeys.AWS_REGION]: Joi.string().required(),
      [ConfigKeys.APPS_STUDIO_SLACK_APP_ID]: Joi.string().required(),

      [ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN]: Joi.string().required(),
      [ConfigKeys.AWS_SNS_ACCOUNTS_TOPIC_ARN]: Joi.string().required(),
      [ConfigKeys.AWS_SNS_ORGANIZATION_TOPIC_ARN]: Joi.string().required(),
      [ConfigKeys.AWS_SNS_CUSTOM_OBJECT_TOPIC_ARN]: Joi.string().required(),
      [ConfigKeys.AWS_SQS_NOTIFICATIONS_QUEUE_URL]: Joi.string().required(),

      // Redis
      [ConfigKeys.REDIS_HOST]: Joi.string().required(),
      [ConfigKeys.REDIS_PORT]: Joi.number().required().default(18283),
      [ConfigKeys.REDIS_USERNAME]: Joi.string().default("default"),
      [ConfigKeys.REDIS_PASSWORD]: Joi.string().required(),

      // Sentry
      [ConfigKeys.SENTRY_DSN]: Joi.string().required(),
      // gRPC
      [ConfigKeys.PLATFORM_GRPC_URL]: Joi.string().required(),
      [ConfigKeys.WORKFLOWS_GRPC_URL]: Joi.string().required(),
      [ConfigKeys.AUTH_GRPC_URL]: Joi.string().required(),
      [ConfigKeys.APPS_GRPC_URL]: Joi.string()
        .optional()
        .default("0.0.0.0:50053"),
      [ConfigKeys.EMAIL_GRPC_URL]: Joi.string().required(),
      // Knock
      [ConfigKeys.KNOCK_API_KEY]: Joi.string().required(),
      [ConfigKeys.KNOCK_SLACK_CHANNEL_ID]: Joi.string().required(),
      // Supabase
      [ConfigKeys.SUPABASE_URL]: Joi.string().required(),
      [ConfigKeys.SUPABASE_KEY]: Joi.string().required(),
      [ConfigKeys.SUPABASE_BUCKET]: Joi.string().required(),
      // VAULT
      [ConfigKeys.VAULT_URL]: Joi.string().required(),
      [ConfigKeys.VAULT_TOKEN]: Joi.string().required(),
      [ConfigKeys.CERT_PATH]: Joi.string().required(),
      // BullMQ Dashboard
      [ConfigKeys.BULL_BOARD_ADMIN_USER]: Joi.string().required(),
      [ConfigKeys.BULL_BOARD_ADMIN_PASSWORD]: Joi.string().required(),
      // Typesense
      [ConfigKeys.TYPESENSE_HOST]: Joi.string().required(),
      [ConfigKeys.TYPESENSE_API_KEY]: Joi.string().required(),
      [ConfigKeys.TYPESENSE_SEARCH_ONLY_API_KEY]: Joi.string().required(),
    }).unknown();

    const { error, value: validatedEnvConfig } = envVarsSchema.validate(config);
    if (error) {
      throw new Error(`Config validation error: ${error.message}`);
    }

    return validatedEnvConfig;
  }
}
