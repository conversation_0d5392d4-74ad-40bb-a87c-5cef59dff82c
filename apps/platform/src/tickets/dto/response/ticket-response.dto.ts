import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { ValidateNested } from "class-validator";
import { ResponseMessage } from "../../../common/utils/response-dto.utils";
import { TicketResponseDto } from "../../transformer/ticket-response.dto";

export class GetAllTicketsResponse extends ResponseMessage {
  @ApiProperty({
    description: "The tickets fetched",
    type: [TicketResponseDto],
  })
  @ValidateNested({ each: true })
  @Type(() => TicketResponseDto)
  @Expose()
  data: TicketResponseDto[];
}

export class CommonTicketResponse extends ResponseMessage {
  @ApiProperty({
    description: "The response for create/update/delete ticket operations",
    type: TicketResponseDto,
  })
  @ValidateNested()
  @Type(() => TicketResponseDto)
  @Expose()
  data: TicketResponseDto;
}
