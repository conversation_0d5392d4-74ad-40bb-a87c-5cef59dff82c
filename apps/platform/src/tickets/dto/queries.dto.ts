import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { CommentType, CommentVisibility } from "@repo/thena-platform-entities";
import { Transform } from "class-transformer";
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  <PERSON>,
  <PERSON>,
} from "class-validator";

export class GetDraftTicketQuery {
  @IsNumber()
  @Min(1, { message: "Page must be greater than or equal to 1" })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @ApiPropertyOptional({
    description: "The page number to fetch tickets by",
    example: 1,
    default: 1,
    minimum: 1,
  })
  page?: number;

  @IsNumber()
  @Min(1, { message: "Limit must be greater than or equal to 1" })
  @Max(100, { message: "Limit cannot exceed 100" })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @ApiPropertyOptional({
    description: "The limit number of tickets to fetch",
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  limit?: number;
}

export class GetTicketQuery {
  @ApiPropertyOptional({
    description: "The ID of the team to filter tickets by",
    example: "T00M677SAK",
  })
  @IsString()
  @IsOptional()
  teamId?: string;

  @IsNumber()
  @Min(0, { message: "Page must be greater than or equal to 0" })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @ApiPropertyOptional({
    description: "The page number to fetch tickets by",
    example: 0,
    default: 0,
    minimum: 0,
  })
  page?: number;

  @IsNumber()
  @Min(1, { message: "Limit must be greater than or equal to 1" })
  @Max(100, { message: "Limit cannot exceed 100" })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @ApiPropertyOptional({
    description: "The limit number of tickets to fetch",
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  limit?: number;
}

export class GetCommentsForTicketQuery {
  @IsOptional()
  @IsNumber()
  @Min(0, { message: "Page must be greater than or equal to 0" })
  @Transform(({ value }) => parseInt(value))
  @ApiPropertyOptional({ description: "The page number" })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Min(1, { message: "Limit must be greater than or equal to 1" })
  @Max(200, { message: "Limit cannot exceed 200" })
  @Transform(({ value }) => parseInt(value))
  @ApiPropertyOptional({ description: "The limit number of comments to fetch" })
  limit?: number;

  @IsString()
  @IsEnum(CommentType)
  @ApiProperty({
    description: "The type of comments to fetch",
    enum: CommentType,
  })
  commentType: CommentType;

  @IsString()
  @IsEnum(CommentVisibility)
  @ApiProperty({
    description: "The visibility of the comments to fetch",
    enum: CommentVisibility,
  })
  visibility: CommentVisibility;
}

export class GetTicketRelatedQuery {
  /**
   * Whether to fetch linked tickets.
   */
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true")
  @ApiPropertyOptional({ description: "Whether to fetch linked tickets" })
  linked?: boolean;

  /**
   * Whether to fetch subtickets.
   */
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true")
  @ApiPropertyOptional({ description: "Whether to fetch subtickets" })
  subtickets?: boolean;

  /**
   * Whether to fetch duplicate tickets.
   */
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true")
  @ApiPropertyOptional({ description: "Whether to fetch duplicate tickets" })
  duplicate?: boolean;

  /**
   * The page number to fetch tickets by.
   */
  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ description: "The page number to fetch tickets by" })
  page?: number;

  /**
   * The limit of tickets to fetch.
   */
  @IsNumber()
  @IsOptional()
  @ApiPropertyOptional({ description: "The limit of tickets to fetch" })
  limit?: number;
}

export interface AssignTicketQuery {
  unassign?: boolean;
}

export interface SearchTicketsQuery {
  /**
   * The search query to filter tickets by.
   */
  q?: string;

  /**
   * The ID of the team to filter tickets by.
   */
  team_id?: string;

  /**
   * The ID of the organization to filter tickets by.
   */
  organization_id?: string;

  /**
   * The page number to fetch tickets by.
   */
  page?: number;

  /**
   * The limit of tickets to fetch.
   */
  perPage?: number;

  /**
   * The field to sort the tickets by.
   */
  sortBy?: string;

  /**
   * The order to sort the tickets by.
   */
  sortOrder?: "asc" | "desc";
}

/**
 * Interface representing the response structure for ticket search operations
 * @interface SearchTicketsResponse
 */
export interface SearchTicketsResponse {
  /**
   * Array of search hits containing matched documents and their highlights
   * Each hit contains the matched document and an array of highlighted matches
   */
  hits: Array<{
    /** The matched document containing all ticket data */
    document: any;
    /** Array of highlighted matches found in the document fields */
    highlights: any[];
  }>;

  /**
   * Current page number in the search results
   * @default 1
   */
  page: number;

  /**
   * Number of results per page
   * @default 20
   */
  perPage: number;

  /**
   * Total number of documents matching the search criteria
   */
  totalHits: number;

  /**
   * Total number of pages available for the search results
   * Calculated as Math.ceil(totalHits / perPage)
   */
  totalPages: number;
}
