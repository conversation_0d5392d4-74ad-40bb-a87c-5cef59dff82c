import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEmail,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from "class-validator";
import { ExternalCustomFieldValuesDto } from "../../custom-field/dto";

/**
 * Common fields for creating and updating a ticket
 */
class CommonTicketFields {
  /**
   * The ID of the assigned agent
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The ID of the assigned agent" })
  assignedAgentId?: string;

  /**
   * The ID of the account
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The ID of the account" })
  accountId?: string;

  /**
   * The email of the assigned agent
   */
  @IsEmail()
  @IsOptional()
  @ApiPropertyOptional({ description: "The email of the assigned agent" })
  assignedAgentEmail?: string;

  /**
   * The description of the ticket
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The description of the ticket" })
  description?: string;

  /**
   * The due date of the ticket
   */
  @IsDate()
  @IsOptional()
  @ApiPropertyOptional({ description: "The due date of the ticket" })
  dueDate?: Date;

  /**
   * The email of the submitter
   */
  @IsEmail()
  @IsOptional()
  @ApiPropertyOptional({ description: "The email of the submitter" })
  submitterEmail?: string;

  /**
   * The ID of the status
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      "The ID of the status, status id if provided is used over status name",
  })
  statusId?: string;

  /**
   * The name of the status to match against, we use levenshtein distance to match against the status name
   * in the database
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The name of the status to match against",
  })
  statusName?: string;

  /**
   * The ID of the priority
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      "The ID of the priority, priority id if provided is used over priority name",
  })
  priorityId?: string;

  /**
   * The name of the priority to match against, we use levenshtein distance to match against the priority name
   * in the database
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The name of the priority to match against",
  })
  priorityName?: string;

  /**
   * The ID of the sentiment
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The ID of the sentiment" })
  sentimentId?: string;

  /**
   * The metadata of the ticket
   */
  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({
    description: "The metadata of the ticket",
  })
  metadata?: Record<string, unknown>;

  /**
   * The ID of the type
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The ID of the type" })
  typeId?: string;

  /**
   * Whether the ticket is private
   */
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({ description: "Whether the ticket is private" })
  isPrivate?: boolean;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The source of the ticket" })
  source?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The AI generated title of the ticket" })
  aiGeneratedTitle?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The AI generated summary of the ticket",
  })
  aiGeneratedSummary?: string;

  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({ description: "The attachment URLs" })
  attachmentUrls?: string[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ExternalCustomFieldValuesDto)
  @ApiPropertyOptional({
    description: "The custom field values",
    type: [ExternalCustomFieldValuesDto],
  })
  customFieldValues?: ExternalCustomFieldValuesDto[];
}

/**
 * Fields for escalating a ticket
 */
export class EscalateTicketBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The reason for the escalation" })
  reason: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The details of the escalation" })
  details: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The impact of the escalation" })
  impact: string;
}

/**
 * Fields for updating a ticket
 */
export class UpdateTicketBody extends CommonTicketFields {
  /**
   * The title of the ticket
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The title of the ticket" })
  title?: string;

  /**
   * The ID of the sub-team
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The ID of the sub-team" })
  subTeamId?: string;

  /**
   * The custom field values
   */
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ExternalCustomFieldValuesDto)
  customFieldValues?: ExternalCustomFieldValuesDto[];

  /**
   * The ID of the form
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The ID of the form" })
  formId?: string;
}

/**
 * Fields for creating a ticket
 */
export class CreateTicketBody extends CommonTicketFields {
  /**
   * The title of the ticket
   */
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The title of the ticket" })
  title: string;

  /**
   * The email of the requestor
   */
  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({ description: "The email of the requestor" })
  requestorEmail: string;

  /**
   * Whether to perform routing
   */
  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether to perform routing",
  })
  performRouting?: boolean;

  /**
   * The ID of the team
   */
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The ID of the team" })
  teamId: string;

  /**
   * The ID of the form
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({ description: "The ID of the form" })
  formId?: string;

  // Comment-related fields for backward compatibility with Slack app
  /**
   * The content of the initial comment (HTML format)
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The content of the initial comment (HTML format)",
  })
  commentContentHtml?: string;

  /**
   * The content of the initial comment (plain text format)
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The content of the initial comment (plain text format)",
  })
  commentContent?: string;

  /**
   * The content of the initial comment (JSON format)
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The content of the initial comment (JSON format)",
  })
  commentContentJson?: string;

  /**
   * The attachment IDs for the initial comment
   */
  @IsArray()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The attachment IDs for the initial comment",
  })
  commentAttachmentIds?: string[];

  /**
   * The metadata for the initial comment
   */
  @IsOptional()
  @IsObject()
  @ApiPropertyOptional({ description: "The metadata for the initial comment" })
  commentMetadata?: Record<string, unknown>;

  /**
   * The email of the impersonated user for the initial comment
   */
  @IsEmail()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The email of the impersonated user for the initial comment",
  })
  commentImpersonatedUserEmail?: string;

  /**
   * The name of the impersonated user for the initial comment
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The name of the impersonated user for the initial comment",
  })
  commentImpersonatedUserName?: string;

  /**
   * The avatar URL of the impersonated user for the initial comment
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      "The avatar URL of the impersonated user for the initial comment",
  })
  commentImpersonatedUserAvatar?: string;
}

export class AssignTicketBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The ID of the agent to assign" })
  assignedAgentId: string;
}

export class AssignTeamToTicketBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The ID of the team to assign" })
  teamId: string;
}

/**
 * Fields for marking or creating a sub-ticket
 */
export class MarkOrCreateSubTicketBody extends CommonTicketFields {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The ID of the parent ticket" })
  parentTicketId: string;

  /**
   * The ID of the ticket to mark as a sub-ticket
   */
  @IsString()
  @IsOptional()
  @ApiPropertyOptional({
    description: "The ID of the ticket to mark as a sub-ticket",
  })
  subTicketId?: string;

  /**
   * The title of the ticket
   */
  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => !o.subTicketId)
  @ApiPropertyOptional({ description: "The title of the ticket" })
  title?: string;
}

export class MarkDuplicateBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The ID of the ticket to mark as duplicate" })
  duplicateTicketId: string;

  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => o.duplicateTicketId !== o.duplicateOfTicketId, {
    message: "A ticket cannot be marked as a duplicate of itself",
  })
  @ApiProperty({ description: "The ID of the ticket that is being duplicated" })
  duplicateOfTicketId: string;
}

export class LinkTicketsBody {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The ID of the source ticket" })
  sourceTicketId: string;

  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => o.sourceTicketId !== o.linkedTicketId, {
    message: "A ticket cannot be linked to itself",
  })
  @ApiProperty({ description: "The ID of the target ticket to link to" })
  linkedTicketId: string;
}
