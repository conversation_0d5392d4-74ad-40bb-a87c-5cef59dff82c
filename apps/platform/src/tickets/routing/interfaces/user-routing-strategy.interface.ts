import { Team, TeamCapacity, User } from "@repo/thena-platform-entities";
import { CurrentUser } from "../../../common/decorators";

export interface UserRoutingStrategy {
  /**
   * Assigns a selected team's user to the provided team based on the
   * team's selected strategy.
   */
  assignUser(team: Team, user: CurrentUser): Promise<User | null>;

  /**
   * Returns a list of available users for the provided team.
   */
  getAvailableUsers(team: Team, user: CurrentUser): Promise<Array<User>>;

  /**
   * Checks if the user is available.
   */
  checkUserAvailability(user: User): Promise<boolean>;

  /**
   * Checks if the user has capacity.
   */
  filterUsersByCapacity(
    users: Array<User>,
    capacity: TeamCapacity,
    team: Team,
  ): Array<User>;
}
