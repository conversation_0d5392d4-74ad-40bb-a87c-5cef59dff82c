import { Inject, Injectable } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  RuleGroup,
  Team,
  TeamRoutingRule,
  TeamRoutingRulesRepository,
  TeamUserRoutingStrategy,
  Ticket,
  TicketRepository,
  TransactionService,
  User,
} from "@repo/thena-platform-entities";
import { DeepPartial } from "typeorm";
import { ActivitiesService } from "../../../../../activities/services/activities.service";
import { CurrentUser } from "../../../../../common/decorators";
import { TeamsService } from "../../../../../teams/services/teams.service";
import { RequestRouterEngine } from "../../../interfaces";
import { ThenaAgentAllocator } from "../agent-allocator";
import { ThenaRuleEvaluator } from "../rule-evaluator";

enum TicketRoutingState {
  INITIAL = "INITIAL",
  TEAM_EVALUATED = "TEAM_EVALUATED",
  TEAM_ASSIGNED = "TEAM_ASSIGNED",
  USER_EVALUATED = "USER_EVALUATED",
  USER_ASSIGNED = "USER_ASSIGNED",
  FAILED = "FAILED",
}

enum TicketRoutingEvent {
  EVALUATE_TEAM = "EVALUATE_TEAM",
  ASSIGN_TEAM = "ASSIGN_TEAM",
  EVALUATE_USER = "EVALUATE_USER",
  ASSIGN_USER = "ASSIGN_USER",
  FAIL = "FAIL",
}

// State machine context to hold data
interface TicketRoutingContext {
  forTeam: Team;
  exitEarly?: boolean;
  ticket: Ticket;
  rules: RuleGroup[];
  user: CurrentUser;
  evaluatedTeam?: Team;
  evaluatedUser?: User;
  error?: Error;
}

@Injectable()
export class ThenaRequestRouterEngine implements RequestRouterEngine {
  private spanId: string;

  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private readonly eventEmitter: EventEmitter2,

    // Activity service
    private readonly activitiesService: ActivitiesService,

    // Transaction service
    private readonly transactionService: TransactionService,

    // Rule evaluator
    private readonly ruleEvaluator: ThenaRuleEvaluator,

    // Team service
    private readonly teamService: TeamsService,

    // Agent allocator
    private readonly agentAllocator: ThenaAgentAllocator,

    // Team routing rules repository
    private readonly teamRoutingRulesRepository: TeamRoutingRulesRepository,

    // Ticket repository
    private readonly ticketRepository: TicketRepository,
  ) {
    this.spanId = "[ThenaRequestRouterEngine]";
  }

  private async transition(
    currentState: TicketRoutingState,
    event: TicketRoutingEvent,
    context: TicketRoutingContext,
  ) {
    try {
      switch (currentState) {
        // Initial state
        case TicketRoutingState.INITIAL:
          if (event === TicketRoutingEvent.EVALUATE_TEAM) {
            // Evaluate the rules
            const team = await this.ruleEvaluator.evaluate(
              context.ticket,
              context.rules,
              context.user,
            );

            // If no teams are available, exit early
            if (!team) {
              this.logger.warn(
                `${this.spanId} No team available for ticket: ${context.ticket.id}`,
              );

              // Create an audit log
              const auditLog: DeepPartial<AuditLog> = {
                team: { id: context.forTeam.id },
                organization: { id: context.user.orgId },
                activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                entityId: context.ticket.id,
                entityUid: context.ticket.uid,
                entityType: AuditLogEntityType.TICKET,
                op: AuditLogOp.INFO,
                visibility: AuditLogVisibility.ORGANIZATION,
                activity: `No available team found for ticket: ${context.ticket.id}`,
                description: `No available team found for ticket ${context.ticket.id} based on the routing rules configured for team ${context.forTeam.name} therefore the ticket was not routed to any team.`,
              };

              // Record the audit log
              await this.activitiesService.recordAuditLog(auditLog);

              // If there is a fallback sub team, evaluate it
              if (context.forTeam?.configuration?.fallbackSubTeam?.uid) {
                const team = await this.teamService.findOneByTeamId(
                  context.forTeam.configuration.fallbackSubTeam.uid,
                  context.user.orgId,
                );

                // If the team is not found, exit early
                if (!team) {
                  const fallbackTeamNotFoundLog: DeepPartial<AuditLog> = {
                    team: { id: context.forTeam.id },
                    organization: { id: context.user.orgId },
                    activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                    entityId: context.ticket.id,
                    entityUid: context.ticket.uid,
                    entityType: AuditLogEntityType.TICKET,
                    op: AuditLogOp.INFO,
                    visibility: AuditLogVisibility.ORGANIZATION,
                    activity: `Fallback team not found for ticket: ${context.ticket.id}`,
                    description: `Fallback team ${team.name} not found for ticket ${context.ticket.id} based on the routing rules configured for team ${context.forTeam.name} therefore the ticket was not routed to any team.`,
                  };

                  // Record the audit log
                  await this.activitiesService.recordAuditLog(
                    fallbackTeamNotFoundLog,
                  );

                  context.exitEarly = true;
                  return TicketRoutingState.FAILED;
                }

                const fallBackTeamFoundLog: DeepPartial<AuditLog> = {
                  team: { id: context.forTeam.id },
                  organization: { id: context.user.orgId },
                  activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                  entityId: context.ticket.id,
                  entityUid: context.ticket.uid,
                  entityType: AuditLogEntityType.TICKET,
                  op: AuditLogOp.INFO,
                  visibility: AuditLogVisibility.ORGANIZATION,
                  activity: `Fallback team found for ticket: ${context.ticket.id}`,
                  description: `Fallback team ${team.name} found for ticket ${context.ticket.id} based on the routing rules configured for team ${context.forTeam.name} therefore the ticket was routed to the fallback team.`,
                };

                // Record the audit log
                await this.activitiesService.recordAuditLog(
                  fallBackTeamFoundLog,
                );

                const isFallbackSubTeamAvailable = await this.ruleEvaluator.checkIfTeamAvailable(
                  team,
                  context.user
                );

                // If no sub teams are available, exit early
                if (!isFallbackSubTeamAvailable) {
                  this.logger.warn(
                    `${this.spanId} No fallback sub team available for ticket: ${context.ticket.id}`,
                  );

                  // Create an audit log
                  const auditLog: DeepPartial<AuditLog> = {
                    team: { id: context.forTeam.id },
                    organization: { id: context.user.orgId },
                    activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                    entityId: context.ticket.id,
                    entityUid: context.ticket.uid,
                    entityType: AuditLogEntityType.TICKET,
                    op: AuditLogOp.INFO,
                    visibility: AuditLogVisibility.ORGANIZATION,
                    activity: `No available fallback sub team found for ticket: ${context.ticket.id}`,
                    description: `No available fallback sub team found for ticket ${context.ticket.id} based on the routing rules configured for team ${context.forTeam.name} therefore the ticket was not routed to any team.`,
                  };

                  // Record the audit log
                  await this.activitiesService.recordAuditLog(auditLog);
                  context.exitEarly = true;
                  return TicketRoutingState.FAILED;
                }

                context.evaluatedTeam = team;

                // Emit the team evaluated event
                this.eventEmitter.emit("ticket.team.evaluated", {
                  ticketId: context.ticket.id,
                  teamId: team.id,
                });

                return TicketRoutingState.TEAM_EVALUATED;
              }

              context.exitEarly = true;
            } else {
              context.evaluatedTeam = team;

              // Emit the team evaluated event
              this.eventEmitter.emit("ticket.team.evaluated", {
                ticketId: context.ticket.id,
                teamId: team.id,
              });
            }

            return TicketRoutingState.TEAM_EVALUATED;
          }

          break;

        // Team evaluated state
        case TicketRoutingState.TEAM_EVALUATED:
          if (event === TicketRoutingEvent.ASSIGN_TEAM) {
            // Update the ticket with the evaluated team
            await this.transactionService.runInTransaction(
              async (txnContext) => {
                const oldTeamId = context.ticket.subTeamId;

                // Update the ticket with the evaluated team
                await this.ticketRepository.updateWithTxn(
                  txnContext,
                  { id: context.ticket.id },
                  { subTeamId: context.evaluatedTeam.id },
                );

                // Create an audit log
                const auditLog: DeepPartial<AuditLog> = {
                  team: { id: context.forTeam.id },
                  organization: { id: context.user.orgId },
                  activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                  entityId: context.ticket.id,
                  entityUid: context.ticket.uid,
                  entityType: AuditLogEntityType.TICKET,
                  op: AuditLogOp.UPDATED,
                  visibility: AuditLogVisibility.ORGANIZATION,
                  activity: `The team was evaluated for ticket ${context.ticket.id}`,
                  description: `Based on the routing rules configured for team ${context.forTeam.name}, the team ${context.evaluatedTeam.name} was evaluated for ticket ${context.ticket.id}`,
                  metadata: {
                    updatedFields: [
                      {
                        field: "subTeamId",
                        previousValue: oldTeamId,
                        updatedToValue: context.evaluatedTeam.id,
                      },
                    ],
                  },
                };

                // Record the audit log
                await this.activitiesService.recordAuditLog(
                  auditLog,
                  txnContext,
                );
              },
            );

            // Emit the team assigned event
            this.eventEmitter.emit("ticket.team.assigned", {
              ticketId: context.ticket.id,
              teamId: context.evaluatedTeam.id,
            });

            return TicketRoutingState.TEAM_ASSIGNED;
          }
          break;

        // Team assigned state
        case TicketRoutingState.TEAM_ASSIGNED:
          if (event === TicketRoutingEvent.EVALUATE_USER) {
            const { evaluatedTeam } = context;
            const { userRoutingStrategy } = evaluatedTeam.configuration;

            // If the user routing strategy is manual or not set/null, we need to exit early
            if (!userRoutingStrategy || userRoutingStrategy === TeamUserRoutingStrategy.MANUAL) {
              context.exitEarly = true;

              // Create an audit log
              const auditLog: DeepPartial<AuditLog> = {
                team: { id: context.forTeam.id },
                organization: { id: context.user.orgId },
                activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                entityId: context.ticket.id,
                entityUid: context.ticket.uid,
                entityType: AuditLogEntityType.TICKET,
                op: AuditLogOp.INFO,
                visibility: AuditLogVisibility.ORGANIZATION,
                activity: `Manual routing strategy configured for team ${context.forTeam.name}`,
                description: `Manual routing strategy configured for team ${context.forTeam.name} therefore the ticket was not routed to any user.`,
              };

              // Record the audit log
              await this.activitiesService.recordAuditLog(auditLog);

              return TicketRoutingState.USER_EVALUATED;
            }

            const user = await this.agentAllocator.allocate(
              context.evaluatedTeam,
              context.user,
            );

            // If no user is available, exit early
            if (!user) {
              this.logger.warn(
                `${this.spanId} No user available for ticket: ${context.ticket.id}`,
              );

              // Create an audit log
              const auditLog: DeepPartial<AuditLog> = {
                team: { id: context.forTeam.id },
                organization: { id: context.user.orgId },
                activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                entityId: context.ticket.id,
                entityUid: context.ticket.uid,
                entityType: AuditLogEntityType.TICKET,
                op: AuditLogOp.INFO,
                visibility: AuditLogVisibility.ORGANIZATION,
                activity: `No available user found for ticket: ${context.ticket.id}`,
                description: `No available user found for ticket ${context.ticket.id} based on the configured strategy for team ${context.forTeam.name} therefore the ticket was not routed to any user.`,
              };

              // Record the audit log
              await this.activitiesService.recordAuditLog(auditLog);

              context.exitEarly = true;
            } else {
              context.evaluatedUser = user;
              this.eventEmitter.emit("ticket.user.evaluated", {
                ticketId: context.ticket.id,
                userId: user?.id,
              });
            }

            return TicketRoutingState.USER_EVALUATED;
          }

          break;

        // User evaluated state
        case TicketRoutingState.USER_EVALUATED:
          if (
            event === TicketRoutingEvent.ASSIGN_USER &&
            context.evaluatedUser
          ) {
            // Update the ticket with the assigned user
            await this.transactionService.runInTransaction(
              async (txnContext) => {
                const oldAssignedAgentId = context.ticket.assignedAgentId;

                // Update the ticket with the evaluated team
                await this.ticketRepository.updateWithTxn(
                  txnContext,
                  { id: context.ticket.id },
                  { assignedAgentId: context.evaluatedUser.id },
                );

                // Create an audit log
                const auditLog: DeepPartial<AuditLog> = {
                  team: { id: context.forTeam.id },
                  organization: { id: context.user.orgId },
                  activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
                  entityId: context.ticket.id,
                  entityUid: context.ticket.uid,
                  entityType: AuditLogEntityType.TICKET,
                  op: AuditLogOp.UPDATED,
                  visibility: AuditLogVisibility.ORGANIZATION,
                  activity: `The user was assigned to ticket ${context.ticket.ticketId}`,
                  description: `Based on the routing strategy configured for team ${context.forTeam.name}, the user ${context.evaluatedUser.name} was assigned to ticket ${context.ticket.ticketId}`,
                  metadata: {
                    updatedFields: [
                      {
                        field: "assignedAgentId",
                        previousValue: oldAssignedAgentId,
                        updatedToValue: context.evaluatedUser.id,
                      },
                    ],
                  },
                };

                // Record the audit log
                await this.activitiesService.recordAuditLog(
                  auditLog,
                  txnContext,
                );
              },
            );

            // Emit the user assigned event
            this.eventEmitter.emit("ticket.user.assigned", {
              ticketId: context.ticket.id,
              userId: context.evaluatedUser.id,
            });

            return TicketRoutingState.USER_ASSIGNED;
          }

          break;
      }

      // If we get here, either the state transition wasn't valid or something failed
      throw new Error(`Invalid state transition: ${currentState} -> ${event}`);
    } catch (error) {
      context.error = error;

      this.eventEmitter.emit("ticket.routing.failed", {
        ticketId: context.ticket.id,
        error: error.message,
        state: currentState,
      });

      return TicketRoutingState.FAILED;
    }
  }

  validateRoutingRules(_rules: TeamRoutingRule) {
    // TODO: Implement validation
  }

  /**
   * Validates the existing routing rules.
   * @param rules The routing rules to validate.
   * @param team The team to validate the routing rules for.
   */
  private validateExistingRoutingRules(
    rules: RuleGroup[],
    team: Team,
  ): boolean {
    // If no routing rules are found, return null
    if (!rules) {
      this.logger.warn(
        `${this.spanId} No routing rules found for team: ${team.uid}`,
      );

      return false;
    }

    // If no rule groups are found, return null
    if (rules.length === 0) {
      this.logger.warn(
        `${this.spanId} No rule groups found for team: ${team.uid}`,
      );

      return false;
    }

    return true;
  }

  async assignUser(context: TicketRoutingContext): Promise<void> {
    // Update the ticket with the assigned user
    await this.transactionService.runInTransaction(
      async (txnContext) => {
        const oldAssignedAgentId = context.ticket.assignedAgentId;

        // Update the ticket with the evaluated team
        await this.ticketRepository.updateWithTxn(
          txnContext,
          { id: context.ticket.id },
          { assignedAgentId: context.evaluatedUser?.id ?? null, assignedAgent: context.evaluatedUser },
        );

        // Create an audit log
        const auditLog: DeepPartial<AuditLog> = {
          team: { id: context.forTeam.id },
          organization: { id: context.user.orgId },
          activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
          entityId: context.ticket.id,
          entityUid: context.ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `The user was assigned to ticket ${context.ticket.ticketId}`,
          description: `Based on the routing strategy configured for team ${context.forTeam.name}, the user ${context.evaluatedUser?.name} was assigned to ticket ${context.ticket.ticketId}`,
          metadata: {
            updatedFields: [
              {
                field: "assignedAgentId",
                previousValue: oldAssignedAgentId,
                updatedToValue: context.evaluatedUser?.id ?? null,
              },
            ],
          },
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(
          auditLog,
          txnContext,
        );
      },
    );
  }
  
  async executeRerouting(
    ticket: Ticket,
    team: Team,
    subTeam: Team,
    user: CurrentUser,
    routeToFallbackTeam: boolean = false,
  ): Promise<void> {

    const evaluatedTeam = subTeam ? subTeam : team;

    // Create the context
    const context: TicketRoutingContext = {
      ticket,
      user,
      forTeam: team,
      evaluatedTeam,
      rules: [],
    };

    if (!evaluatedTeam.configuration.userRoutingStrategy || evaluatedTeam.configuration.userRoutingStrategy === TeamUserRoutingStrategy.MANUAL) {
      context.evaluatedUser = null;
      context.exitEarly = true;
      await this.assignUser(context);
      return;
    }

    // Get the parent team and current team configuration
    const [currentTeamConfig, parentTeamConfig] = await Promise.all([
      this.teamService.getTeamConfigurations(evaluatedTeam.uid, user),
      this.teamService.getTeamConfigurations(evaluatedTeam?.parentTeam?.uid, user),
    ]);

    // Check if the team is available
    const isTeamAvailable = this.ruleEvaluator.checkTeamAvailability(
      parentTeamConfig,
      currentTeamConfig,
    );

    if (!isTeamAvailable) {
      if (team.configuration.fallbackSubTeam && !routeToFallbackTeam) {
        const fallbackTeam = await this.teamService.findOneByTeamId(
          team.configuration.fallbackSubTeam.uid,
          user.orgId,
          team.id,
        );

        await this.executeRerouting(ticket, team, fallbackTeam, user, true);
      } else {
        context.evaluatedUser = null;
        context.exitEarly = true;
        await this.assignUser(context);
      }
      return;
    }

    // Define transitions for direct user evaluation
    const transitions: [TicketRoutingState, TicketRoutingEvent][] = [
      [TicketRoutingState.TEAM_ASSIGNED, TicketRoutingEvent.EVALUATE_USER],
      [TicketRoutingState.USER_EVALUATED, TicketRoutingEvent.ASSIGN_USER],
    ];

    let currentState = TicketRoutingState.TEAM_ASSIGNED; // Jump directly to this state

    // Execute user evaluation transitions
    for (const [expectedState, event] of transitions) {
      // If the current state is not the expected state, log the error and throw an error
      if (currentState !== expectedState) {
        // Log the error
        this.logger.error(
          `${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}`,
        );

        // Throw an error
        throw new Error(
          `${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}`,
        );
      }

      // If the exit early flag is set, break out of the loop
      if (context.exitEarly) break;

      // Transition to the next state
      currentState = await this.transition(currentState, event, context);

      // If the state is failed, throw the error
      if (currentState === TicketRoutingState.FAILED) {
        throw context.error;
      }
    }
    return;
  }

  async executeRouting(
    ticket: Ticket,
    team: Team,
    user: CurrentUser,
  ): Promise<void> {
    // Find the team routing rules
    const teamsRoutingRules = await this.teamRoutingRulesRepository.findAll({
      where: {
        team: { id: team.id },
        organization: { id: user.orgId },
      },
    });

    // Map the rules to the format we need
    const mappedRules: RuleGroup[] = teamsRoutingRules.map((rule) => {
      return {
        andRules: rule.andRules,
        orRules: rule.orRules,
        parentTeamId: team.id,
        priority: rule.priority,
        resultTeamId: rule.resultTeamId,
        fallbackTeamId: rule.fallbackTeamId,
      };
    });

    // Validate the existing routing rules if they exist
    const validatedRules = this.validateExistingRoutingRules(mappedRules, team);

    // Check if the sub teams exist
    const hasSubTeams = await this.teamService.checkExists({
      parentTeamId: team.id,
      organizationId: user.orgId,
    });

    // Start the state machine
    let currentState = TicketRoutingState.INITIAL;

    // Create the context
    const context: TicketRoutingContext = {
      ticket,
      rules: mappedRules,
      user,
      forTeam: team,
    };

    // If there are no sub teams and the rules are not valid, we need to evaluate the user directly
    if (!hasSubTeams && !validatedRules) {
      // Set the evaluated team to the current team
      context.evaluatedTeam = team;

      const { userRoutingStrategy } = team.configuration;
      // If the user routing strategy is manual or not set/null, we need to exit early
      if (!userRoutingStrategy || userRoutingStrategy === TeamUserRoutingStrategy.MANUAL) {
        context.exitEarly = true;

        // Create an audit log
        const auditLog: DeepPartial<AuditLog> = {
          team: { id: context.forTeam.id },
          organization: { id: context.user.orgId },
          activityPerformedBy: { id: context.user.sub }, // TODO: Change this to the user who is evaluating the team
          entityId: context.ticket.id,
          entityUid: context.ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.INFO,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Manual routing strategy configured for team ${context.forTeam.name}`,
          description: `Manual routing strategy configured for team ${context.forTeam.name} therefore the ticket was not routed to any user.`,
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog);

        return;
      }

      // Define transitions for direct user evaluation
      const transitions: [TicketRoutingState, TicketRoutingEvent][] = [
        [TicketRoutingState.TEAM_ASSIGNED, TicketRoutingEvent.EVALUATE_USER],
        [TicketRoutingState.USER_EVALUATED, TicketRoutingEvent.ASSIGN_USER],
      ];

      currentState = TicketRoutingState.TEAM_ASSIGNED; // Jump directly to this state

      // Execute user evaluation transitions
      for (const [expectedState, event] of transitions) {
        // If the current state is not the expected state, log the error and throw an error
        if (currentState !== expectedState) {
          // Log the error
          this.logger.error(
            `${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}`,
          );

          // Throw an error
          throw new Error(
            `${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}`,
          );
        }

        // If the exit early flag is set, break out of the loop
        if (context.exitEarly) break;

        // Transition to the next state
        currentState = await this.transition(currentState, event, context);

        // If the state is failed, throw the error
        if (currentState === TicketRoutingState.FAILED) {
          throw context.error;
        }
      }

      return;
    }

    // If the routing rules are not valid, return null
    if (!validatedRules) {
      if (team.configuration.fallbackSubTeam) {
        const fallbackTeam = await this.teamService.findOneByTeamId(
          team.configuration.fallbackSubTeam.uid,
          user.orgId,
          team.id,
        );

        // Get the parent team and current team configuration
        const [currentTeamConfig, parentTeamConfig] = await Promise.all([
          this.teamService.getTeamConfigurations(fallbackTeam.uid, user),
          this.teamService.getTeamConfigurations(fallbackTeam.parentTeam.uid, user),
        ]);

        // Check if the team is available
        const isTeamAvailable = this.ruleEvaluator.checkTeamAvailability(
          parentTeamConfig,
          currentTeamConfig,
        );

        if (!isTeamAvailable) {
          return null;
        }

        // Set the evaluated team to the current team
        context.evaluatedTeam = fallbackTeam;

        // Define transitions for direct user evaluation
        const transitions: [TicketRoutingState, TicketRoutingEvent][] = [
          [TicketRoutingState.TEAM_EVALUATED, TicketRoutingEvent.ASSIGN_TEAM],
          [TicketRoutingState.TEAM_ASSIGNED, TicketRoutingEvent.EVALUATE_USER],
          [TicketRoutingState.USER_EVALUATED, TicketRoutingEvent.ASSIGN_USER],
        ];

        currentState = TicketRoutingState.TEAM_EVALUATED; // Jump directly to this state

        // Execute user evaluation transitions
        for (const [expectedState, event] of transitions) {
          // If the current state is not the expected state, log the error and throw an error
          if (currentState !== expectedState) {
            // Log the error
            this.logger.error(
              `${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}`,
            );

            // Throw an error
            throw new Error(
              `${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}`,
            );
          }

          // If the exit early flag is set, break out of the loop
          if (context.exitEarly) break;

          // Transition to the next state
          currentState = await this.transition(currentState, event, context);

          // If the state is failed, throw the error
          if (currentState === TicketRoutingState.FAILED) {
            throw context.error;
          }
        }
        return;
      } else {
        return null;
      }
    }

    // Define the state transitions
    const transitions: [TicketRoutingState, TicketRoutingEvent][] = [
      [TicketRoutingState.INITIAL, TicketRoutingEvent.EVALUATE_TEAM],
      [TicketRoutingState.TEAM_EVALUATED, TicketRoutingEvent.ASSIGN_TEAM],
      [TicketRoutingState.TEAM_ASSIGNED, TicketRoutingEvent.EVALUATE_USER],
      [TicketRoutingState.USER_EVALUATED, TicketRoutingEvent.ASSIGN_USER],
    ];

    // Execute the state transitions
    for (const [expectedState, event] of transitions) {
      // If the current state is not the expected state, log the error and throw an error
      if (currentState !== expectedState) {
        // Log the error
        this.logger.error(
          `${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}`,
        );

        // Throw an error
        throw new Error(
          `${this.spanId} Unexpected state ${currentState}, expected state ${expectedState}`,
        );
      }

      // If the exit early flag is set, break out of the loop
      if (context.exitEarly) break;

      // Transition to the next state
      currentState = await this.transition(currentState, event, context);

      // If the state is failed, throw the error
      if (currentState === TicketRoutingState.FAILED) {
        throw context.error;
      }
    }
  }
}
