import { Injectable } from "@nestjs/common";
import type { DayConfig, Rule, RuleGroup } from "@repo/thena-platform-entities";
import {
  BusinessHoursConfig,
  BusinessSlot,
  Team,
  Ticket,
} from "@repo/thena-platform-entities";
import { DateTime, DateTimeOptions } from "luxon";
import { CurrentUser } from "../../../../../common/decorators";
import {
  CombinedTeamConfig,
  TeamsService,
} from "../../../../../teams/services/teams.service";
import {
  RuleEvaluator,
  ValidationError,
  ValidationResult,
} from "../../../../routing/interfaces";
import { RuleEvaluatorAbstract } from "../../../abstract";

/**
 * The rule evaluator for the thena request router.
 */
@Injectable()
export class ThenaRuleEvaluator
  extends RuleEvaluatorAbstract
  implements RuleEvaluator
{
  constructor(private readonly teamService: TeamsService) {
    super(teamService);
  }

  async evaluate(
    ticket: Ticket,
    rules: RuleGroup[],
    user: CurrentUser,
  ): Promise<Team | null> {
    // TODO: Implement token bucket pattern for capacity
    // Sort the rule groups by priority
    const sortedGroups = [...rules].sort((a, b) => a.priority - b.priority);

    // Evaluate each rule group and match team's if they are available
    for (const group of sortedGroups) {
      const matches = await this.evaluateRuleGroup(ticket, group);
      if (matches) {
        // Get the team
        const team = await this.teamService.findOneByTeamId(
          group.resultTeamId,
          user.orgId,
          group.parentTeamId,
        );

        // Get the parent team and current team configuration
        const [currentTeamConfig, parentTeamConfig] = await Promise.all([
          this.teamService.getTeamConfigurations(team.uid, user),
          this.teamService.getTeamConfigurations(team.parentTeam.uid, user),
        ]);

        // Check if the team is available
        const isTeamAvailable = this.checkTeamAvailability(
          parentTeamConfig,
          currentTeamConfig,
        );

        // If the team is available, return it
        if (isTeamAvailable) {
          return team;
        }
      }
    }
  }

  async getMatchingTeam(ticket: Ticket, rules: RuleGroup[], user: CurrentUser): Promise<Team | null> {

    // Sort the rule groups by priority
    const sortedGroups = [...rules].sort((a, b) => a.priority - b.priority);
    
    // Evaluate each rule group and match team's if they are available
    for (const group of sortedGroups) {
      const matches = await this.evaluateRuleGroup(ticket, group);
      if (matches) {
        // Get the team
        const team = await this.teamService.findOneByTeamId(
          group.resultTeamId,
          user.orgId,
          group.parentTeamId,
        );

        return team;
      }
    }

    return null;
  }

  async checkIfTeamAvailable(team: Team, user: CurrentUser): Promise<boolean> {

    // Get the parent team and current team configuration
    const [currentTeamConfig, parentTeamConfig] = await Promise.all([
      this.teamService.getTeamConfigurations(team.uid, user),
      this.teamService.getTeamConfigurations(team.parentTeam.uid, user),
    ]);

    // Check if the team is available
    return this.checkTeamAvailability(
      parentTeamConfig,
      currentTeamConfig,
    );
  }

  validateRule(rule: Rule): ValidationResult {
    // Run base validations
    const baseValidations = super.validateRule(rule);
    if (!baseValidations.isValid) {
      return baseValidations;
    }

    // Run thena request router specific validations
    const errors: ValidationError[] = [...baseValidations.errors];

    // Validate precedence
    if (typeof rule.precedence !== "number" || rule.precedence < 0) {
      errors.push({
        field: "precedence",
        code: "INVALID_PRECEDENCE",
        message: "Precedence must be a non-negative number",
      });
    }

    // Validate field exists in ticket schema
    if (!this.isValidTicketField(rule.field)) {
      errors.push({
        field: "field",
        code: "INVALID_FIELD",
        message: `Field '${rule.field}' is not a valid ticket field`,
      });
    }

    return { isValid: true, errors: [] };
  }

  /**
   * Evaluates a rule group against a ticket.
   * @param ticket The ticket to evaluate.
   * @param group The rule group to evaluate.
   * @returns True if all rules in the group match (for AND) or any rule matches (for OR).
   */
  private async evaluateRuleGroup(
    ticket: Ticket,
    group: RuleGroup,
  ): Promise<boolean> {
    try {
      const isAndEmpty = !group.andRules || group.andRules.length === 0;
      const andRuleMatches = await Promise.all(
        (group.andRules || []).map((rule) =>
          this.evaluateSingleRule(ticket, rule),
        ),
      );

      const isOrEmpty = !group.orRules || group.orRules.length === 0;
      const orRuleMatches = await Promise.all(
        (group.orRules || []).map((rule) =>
          this.evaluateSingleRule(ticket, rule),
        ),
      );

      // Match all the AND rules
      const andRuleMatch = isAndEmpty
        ? true
        : andRuleMatches.every((match) => match);

      // Match at least one of the OR rules
      const orRuleMatch = isOrEmpty
        ? true
        : orRuleMatches.some((match) => match);

      // If the AND rules match and the OR rules match, then the rule group matches
      return andRuleMatch && orRuleMatch;
    } catch (error) {
      console.error("Failed to evaluate rule group", error);
      return false;
    }
  }

  /**
   * Validates if a field exists in the ticket schema.
   * @param field The field to validate.
   * @returns True if the field exists in the ticket schema, false otherwise.
   */
  private isValidTicketField(field: string): boolean {
    const validFields = [
      "id",
      "title",
      "description",
      "priority",
      "status",
      "category",
    ];

    return validFields.includes(field.split(".")[0]);
  }

  /**
   * Checks if the team is available.
   * @param parentTeamConfig The parent team configuration.
   * @param currentTeamConfig The current team configuration.
   * @returns True if the team is available, false otherwise.
   */
  public checkTeamAvailability(
    parentTeamConfig: CombinedTeamConfig,
    currentTeamConfig: CombinedTeamConfig,
  ): boolean {
    const { teamConfig: parentTeamConfigurations } = parentTeamConfig;
    const { teamConfig: currentTeamConfigurations, businessHoursConfig } =
      currentTeamConfig;

    // Check if the parent team config doesn't respect timezone, then this team is always available
    if (!parentTeamConfigurations.routingRespectsTimezone) {
      return true;
    }

    // Check if the current team's business hours are active
    const teamTimezone = currentTeamConfigurations.timezone || "UTC";
    const now = DateTime.now().setZone(teamTimezone);

    // Check if today is a holiday
    const todayString = now.toFormat("MM-dd");
    if (currentTeamConfigurations.holidays?.includes(todayString)) {
      return false;
    }

    // Get current day of the week in lowercase
    const currentDay = now.weekdayLong.toLowerCase();

    // Get business hours for the current day
    const businessHours = (
      businessHoursConfig[currentDay as keyof BusinessHoursConfig] as
        | DayConfig
        | undefined
    ).slots as BusinessSlot[] | undefined;

    // If no business hours are set for the current day, the team is unavailable
    if (!businessHours || businessHours.length === 0) return false;

    // Current time in HH:mm format
    const currentTime = now.toFormat("HH:mm");

    // Check if the current time is within any of the business hours slots
    return businessHours.some((slot) => {
      // Convert slot times to DateTime objects in team's timezone for proper comparison
      const zone: DateTimeOptions = { zone: teamTimezone };
      const slotStart = DateTime.fromFormat(slot.start, "HH:mm", zone);
      const slotEnd = DateTime.fromFormat(slot.end, "HH:mm", zone);

      // Handle slots that span midnight
      if (slotEnd < slotStart) {
        // If current time is before midnight, compare with start time
        if (currentTime >= slot.start) {
          return true;
        }

        // If current time is after midnight, compare with end time
        if (currentTime <= slot.end) {
          return true;
        }

        return false;
      }

      // Normal slot comparison
      return currentTime >= slot.start && currentTime <= slot.end;
    });
  }
}
