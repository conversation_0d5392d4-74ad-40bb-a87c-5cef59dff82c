import { InjectQueue } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { Team, Ticket, User } from "@repo/thena-platform-entities";

import { ILogger } from "@repo/nestjs-commons/logger";
import { AuditLog, AuditLogEntityType, AuditLogOp, AuditLogVisibility } from "@repo/thena-platform-entities";
import { Job, Queue } from "bullmq";
import { DeepPartial } from "typeorm";
import { ActivitiesService } from "../../../../../../activities/services/activities.service";
import { CurrentUser } from "../../../../../../common/decorators";
import { BullQueueNames } from "../../../../../../constants/queue.constants";
import { AvailabilityResult } from "../interfaces/user-availability.interface";

@Injectable()
export class TicketAssignmentQueue {
  constructor(
    @InjectQueue(BullQueueNames.TICKET_ASSIGNMENTS) private queue: Queue,
    @Inject('CustomLogger') private logger: ILogger,
    private activitiesService: ActivitiesService,
  ) {}

  async scheduleAssignment(
    ticket: Ticket,
    team: Team,
    targetUser: User,
    availability: AvailabilityResult,
    requestContext: CurrentUser
  ) {
    try {
      const jobId = `org_${requestContext.orgId}_ticket_${ticket.id}`;
      
      // Check for existing job
      const existingJob = await this.queue.getJob(jobId);
      if (existingJob) {
        const state = await existingJob.getState();
        if (['active', 'waiting', 'delayed'].includes(state)) {
          this.logger.warn(`Assignment already scheduled for ticket ${ticket.id}`);
          return existingJob;
        }
      }

      // Schedule new job
      let job: Job | undefined;

      try {
        job = await this.queue.add(
          BullQueueNames.TICKET_ASSIGNMENTS,
          {
            ticketId: ticket.id,
            teamId: team.id,
            teamUid: team.uid,
            targetUserId: targetUser.id,
            metadata: {
              availabilityCalculation: availability,
              routingContext: {
                orgId: requestContext.orgId,
                requestedBy: requestContext,
                teamConfiguration: team.configuration,
              },
              originalAssignmentData: {
                ticketStatus: ticket.status,
                teamId: ticket.teamId,
                assignedAgentId: ticket.assignedAgentId,
              },
            },
          },
          {
            jobId,
            delay: availability.durationUntilAvailable,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 60000, // 1 minute
            },
            removeOnComplete: {
              age: 24 * 3600,
              count: 1000,
            },
            removeOnFail: { age: 24 * 3600 },
          }
        );
  
        this.logger.log(
          `Scheduled assignment for ticket ${ticket.id} to user ${targetUser.id} at ${availability.availableAt}`
        );
      } catch (error) {
        if (error?.code === 'EJOBEXISTS') {
          this.logger.warn(`Duplicate schedule avoided for ${jobId}`);
          job = await this.queue.getJob(jobId);
        } else {
          throw error;
        } 
      }

      const auditLog: DeepPartial<AuditLog> = {
        team: { id: team.id },
        organization: { id: requestContext.orgId },
        activityPerformedBy: { id: requestContext.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: `Scheduled assignment for ticket ${ticket.id} to user ${targetUser.id} at ${availability.availableAt}`,
        description: `Scheduled assignment job (${job.id}) was created for ticket ${ticket.id} to user ${targetUser.id} at ${availability.availableAt}`,
        metadata: {},
      };

      await this.activitiesService.recordAuditLog(auditLog);

      return job;
    } catch (error) {
      this.logger.error(
        `Failed to schedule assignment for ticket ${ticket.id}: ${error.message}`,
        error.stack
      );

      const auditLog: DeepPartial<AuditLog> = {
        team: { id: team.id },
        organization: { id: requestContext.orgId },
        activityPerformedBy: { id: requestContext.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: `Error scheduling assignment for ticket ${ticket.id}`,
        description: `Error scheduling assignment job for ticket ${ticket.id}`,
        metadata: {},
      };

      await this.activitiesService.recordAuditLog(auditLog);

      throw error;
    }
  }

  async findTicketJob(orgId: string, ticketId: string) {
    const jobId = `org_${orgId}_ticket_${ticketId}`;
    return await this.queue.getJob(jobId);
  }

  async cancelTicketJob(ticket: Ticket, user: CurrentUser) {
    try {
      const job = await this.findTicketJob(ticket.organizationId, ticket.id);
      if (job) {
        await job.remove();
        this.logger.log(`Cancelled scheduled assignment for ticket ${ticket.id}, orgId: ${ticket.organizationId}, jobId: ${job.id}`);

        const auditLog: DeepPartial<AuditLog> = {
          team: { id: ticket.teamId },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: ticket.id,
          entityUid: ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Scheduled assignment cancelled for ticket ${ticket.id}`,
          description: `Scheduled assignment job (${job.id}) was cancelled by ${user.email}`,
          metadata: {},
        };

        await this.activitiesService.recordAuditLog(auditLog);
      }
    } catch (error) {
      this.logger.error(
        `Failed to cancel scheduled assignment for ticket ${ticket.id}: ${error.message}`,
        error.stack
      );

      const auditLog: DeepPartial<AuditLog> = {
        team: { id: ticket.teamId },
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: `Error cancelling scheduled assignment for ticket ${ticket.id}`,
        description: `Error cancelling scheduled assignment job for ticket ${ticket.id}`,
        metadata: {},
      };

      await this.activitiesService.recordAuditLog(auditLog);

      throw error;
    }
  }
}
