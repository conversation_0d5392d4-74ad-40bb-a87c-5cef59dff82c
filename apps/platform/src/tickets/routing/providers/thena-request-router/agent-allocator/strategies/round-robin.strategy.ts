import { Injectable } from "@nestjs/common";
import { RedisCacheProvider } from "@repo/nestjs-commons/cache";
import {
  BusinessHoursConfig,
  BusinessSlot,
  DayConfig,
  Team,
  TeamCapacity,
  TimeOffRepository,
  User,
} from "@repo/thena-platform-entities";
import { DateTime, DateTimeOptions } from "luxon";
import { DeepPartial, IsNull, LessThanOrEqual, MoreThanOrEqual } from "typeorm";
import { CurrentUser } from "../../../../../../common/decorators";
import { TeamsService } from "../../../../../../teams/services/teams.service";
import { UserRoutingStrategy } from "../../../../interfaces";

@Injectable()
export class RoundRobinStrategy implements UserRoutingStrategy {
  constructor(
    private readonly cacheProvider: RedisCacheProvider,
    private readonly teamService: TeamsService,
    private readonly timeOffRepository: TimeOffRepository,
  ) {}

  async assignUser(team: Team, user: CurrentUser): Promise<User | null> {
    // Get the available users
    const availableUsers = await this.getAvailableUsers(team, user);
    if (availableUsers.length === 0) {
      return null;
    }

    // Get the last assigned user index
    const nextIndex = await this.getAndIncrementIndex(
      team.id,
      availableUsers.length,
    );

    // Return the next user
    return availableUsers[nextIndex];
  }

  /**
   * Gets the next index for a team.
   */
  private async getAndIncrementIndex(
    teamId: string,
    maxIndex: number,
  ): Promise<number> {
    // Get the key for the last assigned index
    const key = `team:${teamId}:last_assigned_index`;

    // Lua script to get and increment the index, this is done so we can do it atomically
    // note the reason for NOT using `redis.incr` is that it doesn't support the modulo operation
    // which makes this operation non-atomic and prone to race conditions
    const script = `
      -- Get the last assigned index for this team
      local index = redis.call('GET', KEYS[1]) or -1

      -- Increment the index and wrap it around if it exceeds the max index
      index = (tonumber(index) + 1) % tonumber(ARGV[1])

      -- Save the new index
      redis.call('SET', KEYS[1], index)

      -- Return the new index
      return index
    `;

    // Get the next index
    const result = await this.cacheProvider.eval<number>(
      script,
      1,
      key,
      maxIndex,
    );

    return result;
  }

  async getAvailableUsers(team: Team, user: CurrentUser): Promise<User[]> {
    // Get the team users
    const teamUsers = await this.teamService.findActiveTeamMembers(
      team.uid,
      user,
    );

    // Generate the user maps from team members
    const users = teamUsers.map((member) => member.user);

    // Filter the users using their business hours
    const availableUsers = (
      await Promise.all(
        users.map(async (user) => {
          const isAvailable = await this.checkUserAvailability(user);
          return isAvailable ? user : null;
        }),
      )
    ).filter(Boolean);

    // Fetch team capacity configuration
    const teamCapacity = await this.teamService.getTeamCapacity(team.uid, user);

    // Filter users by capacity
    const usersWithCapacity = this.filterUsersByCapacity(
      availableUsers,
      teamCapacity,
      team,
    );

    return usersWithCapacity;
  }

  /**
   * Checks if the team is available.
   * @param user The user to check the availability of.
   * @returns True if the team is available, false otherwise.
   */
  async checkUserAvailability(user: User): Promise<boolean> {
    // Check if the user has any active time off first
    const activeTimeOff = await this.timeOffRepository.findByCondition({
      where: {
        user: { id: user.id },
        startDate: LessThanOrEqual(DateTime.now().toJSDate()),
        endDate: MoreThanOrEqual(DateTime.now().toJSDate()),
        deletedAt: IsNull(),
      },
    });

    // If the user has any active time off, they are not available
    if (activeTimeOff) return false;

    const { businessHoursConfig } = user;
    // If the user has no business hours config, they are always available
    if (!businessHoursConfig) return true;

    // Check if the business hours config is active
    const teamTimezone = user.timezone || "UTC";
    const now = DateTime.now().setZone(teamTimezone);

    // Get current day of the week in lowercase
    const currentDay = now.weekdayLong.toLowerCase();

    // Get business hours for the current day
    const businessHours = (
      businessHoursConfig[currentDay as keyof BusinessHoursConfig] as
        | DayConfig
        | undefined
    ).slots as BusinessSlot[] | undefined;

    // If no business hours are set for the current day, the team is unavailable
    if (!businessHours || businessHours.length === 0) return false;

    // Current time in HH:mm format
    const currentTime = now.toFormat("HH:mm");

    // Check if the current time is within any of the business hours slots
    return businessHours.some((slot) => {
      // Convert slot times to DateTime objects in team's timezone for proper comparison
      const zone: DateTimeOptions = { zone: teamTimezone };
      const slotStart = DateTime.fromFormat(slot.start, "HH:mm", zone);
      const slotEnd = DateTime.fromFormat(slot.end, "HH:mm", zone);

      // Handle slots that span midnight
      if (slotEnd < slotStart) {
        // If current time is before midnight, compare with start time
        if (currentTime >= slot.start) {
          return true;
        }

        // If current time is after midnight, compare with end time
        if (currentTime <= slot.end) {
          return true;
        }

        return false;
      }

      // Normal slot comparison
      return currentTime >= slot.start && currentTime <= slot.end;
    });
  }

  /**
   * Checks if the user has capacity.
   */
  checkUserCapacity(
    user: User,
    capacity: DeepPartial<TeamCapacity>,
    teamId: string,
  ) {
    const userCurrentTickets = user?.metadata?.workload?.[teamId]?.count ?? 0;
    const maxTicketsPerUser = capacity.maxTicketsPerUser;

    // If the max tickets per user is not set, the user has capacity
    if (maxTicketsPerUser == null) return true;

    // Check if the user has capacity
    return userCurrentTickets < capacity.maxTicketsPerUser;
  }

  /**
   * Filters users by capacity.
   */
  filterUsersByCapacity(
    users: Array<User>,
    capacity: TeamCapacity | null,
    team: Team,
  ): Array<User> {
    // Filter users by capacity
    const usersWithCapacity = users.filter((user) => {
      // Get the user's personal capacity
      const usersPersonalCapacity = user.userTeamCapacity?.[0] || {};

      // Merge the team capacity with the user's personal capacity
      const finalCapacity: DeepPartial<TeamCapacity> = {
        ...(capacity ?? {}),
        ...usersPersonalCapacity,
      };

      // Check if the user has capacity
      return this.checkUserCapacity(user, finalCapacity, team.uid);
    });

    // Return the users with capacity
    return usersWithCapacity;
  }
}
