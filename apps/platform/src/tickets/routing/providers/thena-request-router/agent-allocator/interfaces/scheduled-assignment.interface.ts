import { CurrentUser } from "@repo/nestjs-commons/utils";
import { AvailabilityResult } from "./user-availability.interface";

export interface ScheduledAssignment {
  id: string;
  ticketId: string;
  teamId: string;
  teamUid: string;
  targetUserId: string;
  scheduledTime: Date;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  attempts: number;
  lastAttemptAt?: Date;
  error?: string;
  metadata: {
    availabilityCalculation: AvailabilityResult;
    routingContext: {
      orgId: string;
      requestedBy: CurrentUser;
      teamConfiguration: Record<string, unknown>;
    };
    originalAssignmentData: {
      ticketStatus: string;
      teamId: string;
      assignedAgentId?: string;
    };
  }
}
