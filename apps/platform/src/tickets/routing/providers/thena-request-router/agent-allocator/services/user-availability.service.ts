import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  BusinessHoursConfig,
  BusinessSlot,
  DayConfig,
  TeamConfiguration,
  TimeOffRepository,
  User,
} from "@repo/thena-platform-entities";
import { DateTime, DateTimeOptions } from "luxon";
import { IsNull, LessThanOrEqual, MoreThanOrEqual } from "typeorm";
import { AvailabilityResult } from "../interfaces/user-availability.interface";

@Injectable()
export class UserAvailabilityService {
  private readonly MAX_DAYS_SEARCH = 30;

  constructor(
    @Inject('CustomLogger')
    private readonly logger: ILogger,
    private readonly timeOffRepository: TimeOffRepository,
  ) {}

  /**
   * Checks if a user is currently available based on team configuration
   */
  async checkUserAvailability(user: User, teamConfiguration: TeamConfiguration): Promise<boolean> {
    const teamTimezone = teamConfiguration.routingRespectsUserTimezone 
      ? (user.timezone || "UTC") 
      : "UTC";
    const now = DateTime.now().setZone(teamTimezone);
    
    if (teamConfiguration.routingRespectsUserAvailability) {
      const activeTimeOff = await this.timeOffRepository.findByCondition({
        where: {
          user: { id: user.id },
          startDate: LessThanOrEqual(now.toJSDate()),
          endDate: MoreThanOrEqual(now.toJSDate()),
          deletedAt: IsNull(),
        },
      });

      if (activeTimeOff) return false;
    }

    if (!teamConfiguration.routingRespectsUserBusinessHours) {
      return true;
    }

    return this.isWithinBusinessHours(user, now, teamTimezone);
  }

  /**
   * Calculates when a user will next be available
   */
  async calculateTimeUntilAvailable(
    user: User, 
    teamConfiguration: TeamConfiguration
  ): Promise<AvailabilityResult> {
    const teamTimezone = teamConfiguration?.routingRespectsUserTimezone 
      ? (user.timezone || "UTC") 
      : "UTC";
    const now = DateTime.now().setZone(teamTimezone);
    
    // If neither check is enabled, user is always available
    if (!teamConfiguration.routingRespectsUserAvailability && 
        !teamConfiguration.routingRespectsUserBusinessHours) {
      this.logger.debug(`User ${user.id} is always available - no availability checks enabled`);
      return this.createAvailabilityResult(now);
    }

    // No business hours config means always available for business hours check
    if (!user.businessHoursConfig && teamConfiguration.routingRespectsUserBusinessHours) {
      if (teamConfiguration.routingRespectsUserAvailability) {
        const timeOffEnd = await this.getTimeOffEndDate(user, now);
        if (timeOffEnd) {
          return this.createAvailabilityResult(
            timeOffEnd,
            'TIME_OFF'
          );
        }
      }
      return this.createAvailabilityResult(now);
    }

    // Get next available times for both conditions
    const timeOffEnd = teamConfiguration.routingRespectsUserAvailability
      ? await this.getTimeOffEndDate(user, now)
      : null;
      
    const nextBusinessHourStart = teamConfiguration.routingRespectsUserBusinessHours
      ? this.getNextBusinessHourSlot(user, now, teamTimezone, timeOffEnd)
      : null;

    // Log availability constraints if they exist
    if (timeOffEnd || nextBusinessHourStart) {
      this.logger.log(
        `Availability constraints for user ${user.id}: ${
          timeOffEnd ? `Time-off ends: ${timeOffEnd.toFormat('fff')}` : 'No time-off'
        }, ${
          nextBusinessHourStart ? `Next business hours: ${nextBusinessHourStart.toFormat('fff')}` : 'No business hours restriction'
        }`
      );
    }

    const effectiveNextAvailable = this.getEffectiveNextAvailable(
      timeOffEnd, 
      nextBusinessHourStart,
      now
    );

    const result = this.createAvailabilityResult(
      effectiveNextAvailable,
      this.determineReason(timeOffEnd, nextBusinessHourStart),
      nextBusinessHourStart
    );

    // Log final availability decision
    if (result.reason !== 'ALREADY_AVAILABLE') {
      this.logger.log(
        `User ${user.id} next availability scheduled for ${result.availableAt.toFormat('fff')} due to ${result.reason}`
      );
    }

    return result;
  }

  private async getTimeOffEndDate(user: User, now: DateTime): Promise<DateTime | null> {
    const nowUtc = now.toUTC();

    const activeTimeOff = await this.timeOffRepository.findByCondition({
      where: {
        user: { id: user.id },
        startDate: LessThanOrEqual(nowUtc.toJSDate()),
        endDate: MoreThanOrEqual(nowUtc.toJSDate()),
        deletedAt: IsNull(),
      },
    });
    
    if (activeTimeOff) {
      this.logger.log(
        `Active time-off found for user ${user.id} ending at ${DateTime.fromJSDate(activeTimeOff.endDate).toFormat('fff')}`
      );
    }
    
    return activeTimeOff 
      ? DateTime.fromJSDate(activeTimeOff.endDate)
      : null;
  }

  private getNextBusinessHourSlot(
    user: User, 
    now: DateTime,
    timezone: string,
    timeOffEnd: DateTime | null
  ): DateTime | null {
    const { businessHoursConfig } = user;
    if (!businessHoursConfig) {
      this.logger.debug(`No business hours config for user ${user.id} - considering always available`);
      return now;
    }

    let dayOffset = 0;
    let foundSlot = false;
    let nextSlotStart: DateTime | null = null;

    while (!foundSlot) {
      // FAIL FAST if no slot found within limit
      if (dayOffset >= this.MAX_DAYS_SEARCH) {
        this.logger.error(
          `No business hours slot found for user ${user.id} within ${this.MAX_DAYS_SEARCH} days. Possible configuration issue.`
        );
        throw new Error(
          `No available business hours slot found within ${this.MAX_DAYS_SEARCH} days for user ${user.id}. Please check business hours configuration.`
        );
      }

      if (dayOffset > 1) {
        this.logger.debug(
          `Searching for next business hours slot for user ${user.id} - checking day offset: ${dayOffset}`
        );
      }

      const checkDate = now.plus({ days: dayOffset });
      const dayName = checkDate.weekdayLong.toLowerCase();
      const dayConfig = businessHoursConfig[dayName as keyof BusinessHoursConfig] as DayConfig;
      
      if (dayConfig?.slots?.length) {
        for (const slot of dayConfig.slots) {
          const slotStart = DateTime.fromFormat(slot.start, "HH:mm", { zone: timezone })
            .set({
              year: checkDate.year,
              month: checkDate.month,
              day: checkDate.day,
            });

          if (slotStart > now) {
            nextSlotStart = slotStart;
            foundSlot = true;
            break;
          }

          const slotEnd = DateTime.fromFormat(slot.end, "HH:mm", { zone: timezone })
          .set({
            year: checkDate.year,
            month: checkDate.month,
            day: checkDate.day,
          });

          if (slotStart <= now && now <= slotEnd && (!timeOffEnd || timeOffEnd < slotEnd)) {
            nextSlotStart = timeOffEnd;
            foundSlot = true;
            break;
          }
        }
      }

      if (!foundSlot) {
        dayOffset++;
      }
    }

    return nextSlotStart;
  }

  private isWithinBusinessHours(
    user: User,
    now: DateTime,
    timezone: string
  ): boolean {
    const { businessHoursConfig } = user;
    if (!businessHoursConfig) return true;

    const currentDay = now.weekdayLong.toLowerCase();
    const businessHours = (
      businessHoursConfig[currentDay as keyof BusinessHoursConfig] as DayConfig | undefined
    )?.slots as BusinessSlot[] | undefined;

    if (!businessHours?.length) return false;

    const currentTime = now.toFormat("HH:mm");

    return businessHours.some((slot) => {
      const zone: DateTimeOptions = { zone: timezone };
      const slotStart = DateTime.fromFormat(slot.start, "HH:mm", zone);
      const slotEnd = DateTime.fromFormat(slot.end, "HH:mm", zone);

      if (slotEnd < slotStart) {
        if (currentTime >= slot.start || currentTime <= slot.end) {
          return true;
        }
        return false;
      }

      return currentTime >= slot.start && currentTime <= slot.end;
    });
  }

  private getEffectiveNextAvailable(
    timeOffEnd: DateTime | null, 
    nextBusinessHourStart: DateTime | null,
    now: DateTime
  ): DateTime {
    if (!timeOffEnd && !nextBusinessHourStart) return now;
    if (!timeOffEnd) return nextBusinessHourStart || now;
    if (!nextBusinessHourStart) return timeOffEnd;
    
    return timeOffEnd > nextBusinessHourStart ? timeOffEnd : nextBusinessHourStart;
  }

  private determineReason(
    timeOffEnd: DateTime | null, 
    nextBusinessHourStart: DateTime | null
  ): AvailabilityResult['reason'] {
    if (timeOffEnd && nextBusinessHourStart) return 'BOTH';
    if (timeOffEnd) return 'TIME_OFF';
    if (nextBusinessHourStart) return 'BUSINESS_HOURS';
    return 'ALREADY_AVAILABLE';
  }

  private createAvailabilityResult(
    availableAt: DateTime,
    reason: AvailabilityResult['reason'] = 'ALREADY_AVAILABLE',
    businessHourStart?: DateTime | null
  ): AvailabilityResult {
    const now = DateTime.now();
    const durationMs = Math.max(0, availableAt.diff(now).toMillis());

    // Create human readable availability message
    let readableAvailability: string;
    if (reason === 'ALREADY_AVAILABLE') {
      readableAvailability = 'User is currently available';
    } else {
      const formattedDate = availableAt.toLocaleString({
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        timeZoneName: 'short',
      });
      
      let reasonText = '';
      switch (reason) {
        case 'TIME_OFF':
          reasonText = 'time-off period ends';
          break;
        case 'BUSINESS_HOURS':
          reasonText = 'next business hours begin';
          break;
        case 'BOTH':
          reasonText = 'both time-off ends and business hours begin';
          break;
      }
      
      readableAvailability = `User will be available when ${reasonText} at ${formattedDate}`;
    }

    const result: AvailabilityResult = {
      availableAt,
      durationUntilAvailable: durationMs,
      reason,
      readableAvailability,
    };

    if (businessHourStart) {
      result.businessHoursInfo = {
        nextSlotStart: businessHourStart,
        dayOfWeek: businessHourStart.weekdayLong,
        slotTime: businessHourStart.toFormat('HH:mm'),
      };
    }

    return result;
  }
}
