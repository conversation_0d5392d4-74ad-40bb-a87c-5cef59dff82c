import { AuditLog, AuditLogEntityType, AuditLogOp, AuditLogVisibility, Team, Ticket, TicketRepository, TransactionService, User } from "@repo/thena-platform-entities";

import { ILogger } from "@repo/nestjs-commons/logger";

import { Inject, Injectable, forwardRef } from "@nestjs/common";
import { Redis } from "ioredis";
import { TicketAssignmentQueue } from "../queues/ticket-assignment.queue";

import { InjectQueue } from "@nestjs/bullmq";
import { TicketEvents } from "@repo/thena-shared-interfaces";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { DeepPartial } from "typeorm";
import { ActivitiesService } from "../../../../../../activities/services/activities.service";
import { CurrentUser } from "../../../../../../common/decorators";
import { QueueNames } from "../../../../../../constants/queue.constants";
import { TeamsService } from "../../../../../../teams/services/teams.service";
import { UsersService } from "../../../../../../users/services/users.service";
import { TicketsService } from "../../../../../services/tickets.service";
import { UserAvailabilityService } from "./user-availability.service";

interface TicketRedisData {
  ticketId: string;
  teamId: string;
  scheduledJobId: string;
  targetUserId: string;
  scheduledAt: string;
}

@Injectable()
export class ScheduledAssignmentService {
  constructor(
    @Inject('CustomLogger') private logger: ILogger,
    @Inject('REDIS_CLIENT') private readonly redis: Redis,
    private ticketAssignmentQueue: TicketAssignmentQueue,
    @InjectQueue(QueueNames.TICKET_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,
    private userAvailabilityService: UserAvailabilityService,
    private teamsService: TeamsService,
    @Inject(forwardRef(() => TicketsService))
    private ticketService: TicketsService,
    private activitiesService: ActivitiesService,
    private transactionService: TransactionService,
    private ticketRepository: TicketRepository,
    private userService: UsersService,
  ) {}

  async scheduleForLaterAssignment(
    ticket: Ticket,
    team: Team,
    usersToSchedule: User[],
    user: CurrentUser
  ) {
    try {
      if (!usersToSchedule || usersToSchedule.length === 0) {
        this.logger.warn(
          `No users to schedule for ticket ${ticket.id}`
        );
        return null;
      }

      // Calculate availability for all users
      const availabilityPromises = usersToSchedule.map(async (u) => ({
        user: u,
        availability: await this.userAvailabilityService
          .calculateTimeUntilAvailable(u, team.configuration),
      }));

      const availabilities = await Promise.all(availabilityPromises);

      // Sort by earliest available
      const sortedByAvailability = availabilities.sort((a, b) => 
        a.availability.durationUntilAvailable - b.availability.durationUntilAvailable
      );

      const nextAvailable = sortedByAvailability[0];
      if (!nextAvailable) {
        this.logger.warn(
          `No available users found for future scheduling of ticket ${ticket.id}`
        );
        return null;
      }

      // Schedule the assignment
      const scheduledJob = await this.ticketAssignmentQueue.scheduleAssignment(
        ticket,
        team,
        nextAvailable.user,
        nextAvailable.availability,
        user
      );

      if (scheduledJob) {
        // Store in Redis
        const pendingTicket = {
          ticketId: ticket.id,
          teamId: team.id,
          scheduledJobId: scheduledJob.id,
          targetUserId: nextAvailable.user.id,
          scheduledAt: new Date().toISOString(),
        };

        const redisKey = `subteam:${team.id}:pending_tickets`;
        
        const pipeline = this.redis.pipeline();
        
        // Add to pending tickets set
        pipeline.sadd(redisKey, ticket.id);
        
        // Store ticket details in a hash
        const ticketKey = `ticket:${ticket.id}:assignment`;
        pipeline.hmset(ticketKey, pendingTicket);
        
        // Set TTL for cleanup (e.g., 30 days)
        pipeline.expire(ticketKey, 60 * 60 * 24 * 30);

        await pipeline.exec();

        this.logger.log(
          `Stored pending ticket assignment in Redis for ticket ${ticket.id} in team ${team.id}`
        );
      }

      return scheduledJob;

    } catch (error) {
      this.logger.error(
        `Failed to schedule assignment for ticket ${ticket.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  public async cleanupRedisAssignmentData(ticket: Ticket, teamId: string) {
    try {
      const redisKey = `subteam:${teamId}:pending_tickets`;
      const ticketKey = `ticket:${ticket.id}:assignment`;
      
      const pipeline = this.redis.pipeline();
      pipeline.srem(redisKey, ticket.id);
      pipeline.del(ticketKey);
      await pipeline.exec();

      this.logger.log(`Cleaned up Redis assignment data for ticket ${ticket.id}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup Redis assignment data for ticket ${ticket.id}:`, error);
    }
  }

  async cancelScheduledAssignment(
    ticket: Ticket,
    user: CurrentUser,
  ) {
    try {
      await this.ticketAssignmentQueue.cancelTicketJob(ticket, user);
      
      await this.cleanupRedisAssignmentData(ticket, ticket.teamId);

      this.logger.log(`Cancelled scheduled assignment for ticket ${ticket.id}`);
    } catch (error) {
      this.logger.error(
        `Failed to cancel scheduled assignment for ticket ${ticket.id}: ${error.message}`,
        error.stack
      );
    }
  }

  async handleUserAvailabilityChange(user: CurrentUser) {
    let userEntity: User | null = null;
    try {
      userEntity = await this.userService.findOneByUserId(user.uid);
      const userTeams = await this.teamsService.findAllTeamsByUser(userEntity);
      const subTeams = userTeams.filter((team) => team.parentTeamId);

      for (const team of subTeams) {
        const redisKey = `subteam:${team.id}:pending_tickets`;
        const pendingTicketIds = await this.redis.smembers(redisKey);

        if (pendingTicketIds.length === 0) continue;

        // Batch fetch ticket data
        const pipeline = this.redis.pipeline();
        pendingTicketIds.forEach(ticketId => {
          pipeline.hgetall(`ticket:${ticketId}:assignment`);
        });
        const ticketDataResults = await pipeline.exec();

        // Calculate availability once per team
        const availability = await this.userAvailabilityService
          .calculateTimeUntilAvailable(userEntity, team.configuration);

        for (let i = 0; i < pendingTicketIds.length; i++) {
          const ticketId = pendingTicketIds[i];
          const ticketData = ticketDataResults[i][1] as TicketRedisData;

          // Corrected user check
          if (ticketData?.targetUserId !== userEntity.id) continue;

          try {
            const ticket = await this.ticketService.getById(ticketId);

            if (availability.durationUntilAvailable === 0) {
              // Cancel existing schedule
              await this.cancelScheduledAssignment(
                ticket,
                user
              );

              // Immediate assignment
              await this.assignTicket(
                ticket,
                userEntity,
                team
              );

              this.logger.log(
                `Immediately assigned ticket ${ticketId} to user ${userEntity.id} after availability change`
              );
            } else {
              // Reschedule with new availability
              if (ticket) {
                // First cancel the existing scheduled job
                await this.cancelScheduledAssignment(
                  ticket,
                  user
                );

                // Then schedule with new availability time
                await this.scheduleForLaterAssignment(
                  ticket,
                  team,
                  [userEntity],
                  user
                );
                
                this.logger.log(
                  `Rescheduled ticket ${ticketId} for user ${userEntity.id} with new availability`
                );
              }
            }
          } catch (ticketError) {
            // Handle individual ticket errors without failing the entire process
            this.logger.error(
              `Failed to process ticket ${ticketId} for user ${userEntity.id}:`,
              ticketError
            );
            continue;
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to handle availability change for user ${userEntity?.id}:`,
        error
      );
      throw error;
    }
  }

  async assignTicket(ticket: Ticket, user: User, team: Team) {
    await this.transactionService.runInTransaction(
      async (txnContext) => {
        const oldAssignedAgentId = ticket.assignedAgentId;

        // Update the ticket with the evaluated team
        await this.ticketRepository.updateWithTxn(
          txnContext,
          { id: ticket.id },
          { assignedAgentId: user?.id ?? null, assignedAgent: user },
        );

        // Create an audit log
        const auditLog: DeepPartial<AuditLog> = {
          team: { id: team?.parentTeamId },
          organization: { id: ticket.organizationId },
          activityPerformedBy: { id: user.id },
          entityId: ticket.id,
          entityUid: ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `The user was assigned to ticket ${ticket.ticketId}`,
          description: `Based on the routing strategy configured for team ${team.name}, the user ${user?.name} was assigned to ticket ${ticket.ticketId}`,
          metadata: {
            updatedFields: [
              {
                field: "assignedAgentId",
                previousValue: oldAssignedAgentId,
                updatedToValue: user?.id ?? null,
              },
            ],
          },
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(
          auditLog,
          txnContext,
        );

        await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
          ticket: ticket.uid,
          eventType: TicketEvents.ASSIGNED,
          user: user,
          team: ticket.team,
          previousTicket: [],
          reqId: rTracer.id(),
        });
      },
    );
  }
}
