import { On<PERSON><PERSON><PERSON><PERSON><PERSON>, Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Team,
  Ticket,
  TicketRepository,
  TransactionService,
  User,
} from "@repo/thena-platform-entities";
import { Job } from "bullmq";
import { DeepPartial } from "typeorm";
import { ActivitiesService } from "../../../../../../activities/services/activities.service";
import { BullQueueNames } from "../../../../../../constants/queue.constants";
import { TeamsService } from "../../../../../../teams/services/teams.service";
import { UsersService } from "../../../../../../users/services/users.service";
import { ScheduledAssignment } from "../interfaces/scheduled-assignment.interface";
import { ScheduledAssignmentService } from "../services/scheduled-assignment.service";
import { UserAvailabilityService } from "../services/user-availability.service";

// Custom error types
class AssignmentValidationError extends Error {
  constructor(message: string, public readonly context: any) {
    super(message);
    this.name = 'AssignmentValidationError';
  }
}

@Injectable()
@Processor(BullQueueNames.TICKET_ASSIGNMENTS)
export class TicketAssignmentProcessor extends WorkerHost {
  constructor(
    @Inject('CustomLogger') private logger: ILogger,
    private userAvailabilityService: UserAvailabilityService,
    private ticketRepository: TicketRepository,
    private teamService: TeamsService,
    private userService: UsersService,
    private transactionService: TransactionService,
    private activitiesService: ActivitiesService,
    private scheduledAssignmentService: ScheduledAssignmentService,
  ) {
    super();
  }

  async process(job: Job<ScheduledAssignment>) {
    const { ticketId, teamId, teamUid, targetUserId, metadata } = job.data;

    this.logger.log(`Processing ticket assignment for ticket ${ticketId} with team ${teamId} and target user ${targetUserId}`);

    try {
      // Load and validate entities
      const [ticket, team, targetUser] = await Promise.all([
        this.ticketRepository.findOneById(ticketId).catch(() => null),
        this.teamService.findOneByTeamId(teamUid, metadata.routingContext.orgId).catch(() => null),
        this.userService.findOne(targetUserId).catch(() => null),
      ]);

      // Basic validation
      await this.validateAssignment(ticket, team, targetUser, metadata);

      // Check if ticket is already assigned to someone else
      if (ticket.assignedAgentId && 
          ticket.assignedAgentId !== metadata.originalAssignmentData.assignedAgentId) {
        this.logger.log(
          `Skipping assignment - ticket ${ticketId} already assigned to ${ticket.assignedAgentId}`
        );
        return {
          status: 'skipped',
          reason: 'already assigned',
          assignedTo: ticket.assignedAgentId,
        };
      }

      // Check current availability of target user
      const isAvailable = await this.userAvailabilityService
        .checkUserAvailability(targetUser, team.configuration);

      if (isAvailable) {
        await this.performAssignment(ticket, team, targetUser, metadata);
        return;
      }

      // Target user not available, try finding other available users
      const teamUsers = await this.teamService.findActiveTeamMembers(team.uid, metadata.routingContext.requestedBy);
      
      // Generate the user maps from team members
      const users = teamUsers.map((member) => member.user);

      // Filter the users using their business hours
      const availableUsers = (
        await Promise.all(
          users.map(async (user) => {
            const isAvailable = await this.userAvailabilityService
              .checkUserAvailability(user, team.configuration);
            return isAvailable ? user : null;
          }),
        )
      ).filter(Boolean);

      if (availableUsers.length > 0) {
        const alternateUser = availableUsers[0];
        await this.performAssignment(ticket, team, alternateUser, metadata);
        return;
      }

      // No available users, reschedule using existing service
      await this.scheduledAssignmentService.scheduleForLaterAssignment(
        ticket,
        team,
        users,
        metadata.routingContext.requestedBy
      );
      
      this.logger.log(
        `Rescheduled assignment for ticket ${ticketId} with team ${teamId}`
      );

    } catch (error) {
      const errorContext = {
        jobId: job.id,
        ticketId,
        teamId,
        userId: targetUserId,
        attempts: job.attemptsMade,
      };

      if (error instanceof AssignmentValidationError) {
        this.logger.warn(
          `Assignment validation failed: ${error.message}`,
          { ...errorContext, ...error.context }
        );
      } else {
        this.logger.error(
          `Assignment processing failed: ${error.message}`,
          { ...errorContext, ...error }
        );
      }

      throw error;
    }
  }

  private async performAssignment(
    ticket: Ticket,
    team: Team,
    user: User,
    metadata: ScheduledAssignment['metadata']
  ) {
    await this.transactionService.runInTransaction(async (txn) => {
      // Refresh ticket state within transaction
      const currentTicket = await this.ticketRepository
        .findOneById(ticket.id);

      // Revalidate within transaction
      await this.validateAssignment(currentTicket, team, user, metadata);

      // Update ticket
      await this.ticketRepository.updateWithTxn(
        txn,
        { id: ticket.id },
        { 
          assignedAgentId: user.id,
          assignedAgent: user,
        }
      );

      // Create audit log
      await this.createAssignmentAuditLog(
        currentTicket,
        team,
        user,
        metadata.routingContext,
        txn
      );
    });

    // Clean up Redis data after successful assignment
    await this.scheduledAssignmentService.cleanupRedisAssignmentData(ticket, team.id);

    this.logger.log(
      `Successfully assigned ticket ${ticket.id} to user ${user.id}`
    );
  }

  private async validateAssignment(
    ticket: Ticket | null,
    team: Team | null,
    user: User | null,
    metadata: ScheduledAssignment['metadata']
  ): Promise<void> {
    // Entity existence
    if (!ticket || !team || !user) {
      throw new AssignmentValidationError(
        'Required entities not found',
        { ticket: !!ticket, team: !!team, user: !!user }
      );
    }

    // Ticket state validation
    if (ticket.assignedAgentId !== metadata.originalAssignmentData.assignedAgentId && 
        ticket.assignedAgentId !== null) {
      throw new AssignmentValidationError(
        'Ticket state has changed - already assigned to different agent',
        {
          currentStatus: ticket.status,
          expectedStatus: metadata.originalAssignmentData.ticketStatus,
          currentAssignee: ticket.assignedAgentId,
          expectedAssignee: metadata.originalAssignmentData.assignedAgentId,
          message: 'Cannot reassign ticket that has been manually assigned',
        }
      );
    }

    // Team configuration validation
    if (!team.configuration) {
      throw new AssignmentValidationError(
        'Team configuration not found',
        { teamId: team.id }
      );
    }

    // User-team relationship
    const isUserInTeam = await this.teamService.userBelongsToTeam(user.id, team.id, metadata.routingContext.orgId);
    if (!isUserInTeam) {
      throw new AssignmentValidationError(
        'User no longer in team',
        { userId: user.id, teamId: team.id }
      );
    }

    if (!user?.isActive) {
      throw new AssignmentValidationError(
        'User is not active',
        { userId: user?.id }
      );
    }
  }

  private async createAssignmentAuditLog(
    ticket: Ticket,
    team: Team,
    user: User,
    routingContext: ScheduledAssignment['metadata']['routingContext'],
    txn: any
  ): Promise<void> {
    const auditLog: DeepPartial<AuditLog> = {
      team: { id: team.id },
      organization: { id: routingContext.orgId },
      activityPerformedBy: { id: routingContext.requestedBy.sub },
      entityId: ticket.id,
      entityUid: ticket.uid,
      entityType: AuditLogEntityType.TICKET,
      op: AuditLogOp.UPDATED,
      visibility: AuditLogVisibility.ORGANIZATION,
      activity: `Scheduled assignment completed for ticket ${ticket.id}`,
      description: `User ${user.email} was assigned to ticket ${ticket.id} through scheduled assignment`,
      metadata: {
        updatedFields: [
          {
            field: 'assignedAgentId',
            previousValue: ticket.assignedAgentId,
            updatedToValue: user.id,
          },
        ],
      },
    };

    await this.activitiesService.recordAuditLog(auditLog, txn);
  }

  @OnWorkerEvent('failed')
  async handleFailure(job: Job<ScheduledAssignment>, error: Error, prev?: string) {
    const baseContext = `jobId=${job.id} ticketId=${job.data.ticketId} attempts=${job.attemptsMade} previousState=${prev}`;

    if (error instanceof AssignmentValidationError) {
      await this.logger.warn(
        'Assignment validation failed on retry',
        `${baseContext} validationContext=${error.name}:${error.message}`
      );
    } else {
      await this.logger.error(
        'Assignment processing failed on retry',
        `${baseContext} error=${error.name}:${error.message}`
      );
    }

      const auditLog: DeepPartial<AuditLog> = {
      team: { id: job.data.teamId },
      organization: { id: job.data.metadata.routingContext.orgId },
      activityPerformedBy: { id: job.data.metadata.routingContext.requestedBy.sub },
      entityId: job.data.ticketId,
      entityUid: job.data.ticketId,
      entityType: AuditLogEntityType.TICKET,
      op: AuditLogOp.UPDATED,
      visibility: AuditLogVisibility.ORGANIZATION,
      activity: `Scheduled assignment failed for ticket ${job.data.ticketId}`,
      description: `User ${job.data.metadata.originalAssignmentData.assignedAgentId} was not assigned to ticket ${job.data.ticketId} through scheduled assignment`,
      metadata: {
        updatedFields: [
          {
            field: 'assignedAgentId',
            previousValue: job.data.metadata.originalAssignmentData.assignedAgentId,
            updatedToValue: null,
          },
        ],
      },
    };

    await this.activitiesService.recordAuditLog(auditLog);
  }
}
