import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import { ContextUserType, SNSPublisherService } from "@repo/thena-eventbridge";
import { TicketRepository } from "@repo/thena-platform-entities";
import { TicketEvents } from "@repo/thena-shared-interfaces";
import { Job } from "bullmq";
import * as rTracer from "cls-rtracer";
import { v4 as uuidv4 } from "uuid";
import { ConfigKeys, ConfigService } from "../../config/config.service";
import { QueueNames } from "../../constants/queue.constants";
import { SnsTicketCreatedPayload } from "../interfaces/sns-ticket-created-payload.interface";
import {
  EAGER_TICKET_RELATIONS,
  TICKET_SNS_PUBLISHER,
} from "../utils/tickets.constants";

@Injectable()
@Processor(QueueNames.TICKET_SNS_PUBLISHER)
export class TicketSnsPublisherConsumer extends WorkerHost {
  constructor(
    @Inject("CustomLogger") private readonly logger: ILogger,
    @Inject(TICKET_SNS_PUBLISHER)
    private readonly snsPublisherService: SNSPublisherService,
    private readonly ticketRepository: TicketRepository,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  private async publishToSNS(snsPayload: SnsTicketCreatedPayload) {
    await this.snsPublisherService.publishSNSMessage({
      subject: snsPayload.eventType,
      message: JSON.stringify(snsPayload),
      topicArn: this.configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
      messageAttributes: {
        event_name: snsPayload.eventType,
        event_id: snsPayload.eventId, // Unique identifier for the event
        event_timestamp: snsPayload.timestamp,
        context_user_id: snsPayload.actor.id,
        context_user_type: snsPayload.actor.type as ContextUserType,
        context_organization_id: snsPayload.orgId,
      },
      messageGroupId: snsPayload.orgId,
    });
  }

  async process(job: Job) {
    try {
      const { ticket, comment, eventType, user, team, previousTicket, reqId } =
        job.data;

      await rTracer.runWithId(async () => {
        this.logger.log(`Processing SNS message for job ${job.id}`);

        const ticketData = await this.ticketRepository.findByCondition({
          where: { uid: ticket },
          relations: EAGER_TICKET_RELATIONS,
        });

        if (!ticketData && eventType !== TicketEvents.DELETED) {
          const error = new Error(`Ticket not found with id ${ticket}`);
          error.name = "TicketNotFoundError";
          throw error;
        }

        const snsPayload = this.constructSnsPayload(
          eventType === TicketEvents.DELETED ? previousTicket : ticketData,
          eventType,
          user,
          team,
          previousTicket,
          comment,
        );

        // First publish the main update event
        await this.publishToSNS(snsPayload);

        // If this is an update event, check for specific field changes and emit additional events
        if (eventType === TicketEvents.UPDATED && previousTicket) {
          const additionalEvents = [];

          // Check status change
          if (ticketData.status?.uid !== previousTicket.status?.uid) {
            additionalEvents.push({
              ...snsPayload,
              eventType: TicketEvents.STATUS_CHANGED,
              eventId: uuidv4(),
            });
          }

          // Check priority change
          if (ticketData.priority?.uid !== previousTicket.priority?.uid) {
            additionalEvents.push({
              ...snsPayload,
              eventType: TicketEvents.PRIORITY_CHANGED,
              eventId: uuidv4(),
            });
          }

          // Check assigned agent change
          if (
            ticketData.assignedAgent?.uid !== previousTicket.assignedAgent?.uid
          ) {
            additionalEvents.push({
              ...snsPayload,
              eventType: TicketEvents.ASSIGNED,
              eventId: uuidv4(),
            });
          }

          // Check custom fields change
          const hasCustomFieldsChanged =
            JSON.stringify(ticketData.customFieldValues) !==
            JSON.stringify(previousTicket.customFieldValues);
          if (hasCustomFieldsChanged) {
            additionalEvents.push({
              ...snsPayload,
              eventType: TicketEvents.CUSTOM_FIELD_UPDATED,
              eventId: uuidv4(),
            });
          }

          // Check tags change
          const currentTags = new Set(
            ticketData.tags?.map((tag) => tag.uid) || [],
          );
          const previousTags = new Set(
            previousTicket.tags?.map((tag) => tag.uid) || [],
          );
          const hasTagsChanged =
            currentTags.size !== previousTags.size ||
            [...currentTags].some((tag) => !previousTags.has(tag));

          if (hasTagsChanged) {
            additionalEvents.push({
              ...snsPayload,
              eventType: TicketEvents.TAG_UPDATED,
              eventId: uuidv4(),
            });
          }

          // Publish all additional events
          if (additionalEvents.length > 0) {
            await Promise.race([
              Promise.all(
                additionalEvents.map((payload) => this.publishToSNS(payload)),
              ),
              new Promise((_, reject) =>
                setTimeout(() => {
                  const error = new Error(
                    "SNS publish timeout for additional events",
                  );
                  error.name = "SNSTimeoutError";
                  reject(error);
                }, 5000),
              ),
            ]);
          }
        }

        return {
          success: true,
          processedAt: new Date(),
          ticketId: ticket,
        };
      }, reqId);
    } catch (error) {
      this.logger.error(
        `Error publishing message to SNS queue for job ${job.id}: ${error.message}`,
        error?.stack,
      );

      // Determine if we should retry based on error type
      if (error.name === "TicketNotFoundError") {
        // Permanent failure - don't retry
        throw error;
      }

      // For transient errors (network issues, timeouts), allow BullMQ to retry
      if (job.attemptsMade < job.opts.attempts - 1) {
        const retryError = new Error(
          `SNS publishing failed (attempt ${job.attemptsMade + 1}): ${
            error.message
          }`,
        );
        retryError.name = "RetryableError";
        throw retryError;
      }

      // On final attempt, mark as permanent failure
      throw error;
    }
  }

  private constructSnsPayload(
    ticketData: any, // TODO: Add the ticketData Interface
    eventType: string,
    user: any,
    team: any,
    previousTicket: any, // TODO: Add the previousTicket Interface
    comment: any,
  ): SnsTicketCreatedPayload {
    const { organization, status, priority, assignedAgent, sentiment } =
      ticketData;

    const payload: SnsTicketCreatedPayload = {
      eventId: uuidv4(),
      eventType: eventType,
      timestamp: new Date().getTime().toString(),
      orgId: organization?.uid,
      teamId: team?.uid,
      actor: {
        id: user?.uid,
        type: user?.userType,
        email: user?.email,
      },
      payload: {
        ticket: {
          id: ticketData.uid,
          title: ticketData.title,
          description: ticketData.description,
          priorityId: priority?.uid,
          ticketId: ticketData.ticketId,
          priorityName: priority?.name,
          statusId: status?.uid,
          statusName: status?.name,
          sentimentId: sentiment?.uid,
          sentimentName: sentiment?.name,
          source: ticketData.source,
          teamId: team?.uid,
          teamName: team?.name,
          teamIdentifier: team?.identifier,
          subTeamId: ticketData.subTeam?.uid,
          subTeamName: ticketData.subTeam?.name,
          subTeamIdentifier: ticketData.subTeam?.identifier,
          customerContactFirstName: ticketData.customerContact?.firstName,
          customerContactLastName: ticketData.customerContact?.lastName,
          customerContactEmail: ticketData.customerContact?.email,
          assignedTo: assignedAgent?.uid,
          assignedAgent: {
            id: assignedAgent?.uid,
            email: assignedAgent?.email,
            name: assignedAgent?.name,
          },
          customer: {
            id: ticketData.requestorId,
            email: ticketData.requestorEmail,
            name: ticketData.requestorName,
          },
          tags: ticketData.tags?.map((tag) => tag.uid) || [],
          customFields:
            ticketData.customFieldValues?.map((customField) => ({
              [customField.customField.uid]: customField.value,
            })) || [],
          createdAt: ticketData.createdAt,
          metadata: ticketData.metadata,
          isEscalated: ticketData.isEscalated,
          isArchived: ticketData.archivedAt ? true : false,
          aiGeneratedTitle: ticketData.aiGeneratedTitle,
          aiGeneratedSummary: ticketData.aiGeneratedSummary,
        },
        ...(comment && { comment }),
      },
    };

    // Add previous ticket data if event type is update
    if (eventType === TicketEvents.UPDATED && previousTicket) {
      payload.payload.previousTicket = {
        id: previousTicket.uid,
        title: previousTicket.title,
        ticketId: previousTicket.ticketId,
        description: previousTicket.description,
        priorityId: previousTicket.priority?.uid,
        priorityName: previousTicket.priority?.name,
        statusId: previousTicket.status?.uid,
        statusName: previousTicket.status?.name,
        sentimentId: previousTicket.sentiment?.uid,
        sentimentName: previousTicket.sentiment?.name,
        source: previousTicket.source,
        teamId: previousTicket.team?.uid,
        teamName: previousTicket.team?.name,
        teamIdentifier: previousTicket.team?.identifier,
        subTeamId: previousTicket.subTeam?.uid,
        subTeamName: previousTicket.subTeam?.name,
        subTeamIdentifier: previousTicket.subTeam?.identifier,
        customerContactFirstName: previousTicket?.customerContact?.firstName,
        customerContactLastName: previousTicket?.customerContact?.lastName,
        customerContactEmail: previousTicket?.customerContact?.email,
        assignedTo: previousTicket.assignedAgent?.uid,
        assignedAgent: {
          id: previousTicket?.assignedAgent?.uid,
          email: previousTicket?.assignedAgent?.email,
          name: previousTicket?.assignedAgent?.name,
        },
        tags: previousTicket.tags?.map((tag) => tag.uid) || [],
        customFields:
          previousTicket.customFieldValues?.map((customField) => ({
            [customField.customField.uid]: customField.value,
          })) || [],
        metadata: previousTicket.metadata,
        isEscalated: previousTicket.isEscalated,
        isArchived: previousTicket.archivedAt ? true : false,
        createdAt: previousTicket.createdAt,
        customer: {
          id: previousTicket.requestorId,
          email: previousTicket.requestorEmail,
          name: previousTicket.requestorName,
        },
      };
    }

    return payload;
  }
}
