import { BullModule } from "@nestjs/bullmq";
import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SentryService } from "@repo/nestjs-commons/filters";
import {
  ApiKeyGrpcStrategy,
  AuthenticationGrpcClient,
  BearerTokenGrpcStrategy,
  GrpcAuthGuard,
  UserOrgInternalGrpcStrategy,
} from "@repo/nestjs-commons/guards";
import { ILogger } from "@repo/nestjs-commons/logger";
import { GrpcClient } from "@repo/nestjs-commons/providers";
import { SNSPublisherService } from "@repo/thena-eventbridge";
import {
  CachedDraftsRepository,
  CachedTicketPriorityRepository,
  CachedTicketRelationshipsRepository,
  CachedTicketRepository,
  CachedTicketStatusRepository,
  CachedTicketTypeRepository,
  CustomField,
  CustomFieldRepository,
  Draft,
  DraftRepository,
  TeamRoutingRules,
  TeamRoutingRulesRepository,
  Ticket,
  TicketPriority,
  TicketPriorityRepository,
  TicketRelationships,
  TicketRelationshipsRepository,
  TicketRepository,
  TicketSentiment,
  TicketSentimentRepository,
  TicketStatus,
  TicketStatusRepository,
  TicketTimeLog,
  TicketTimeLogRepository,
  TicketType,
  TicketTypeRepository,
  TimeOff,
  TimeOffRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { Redis } from "ioredis";
import { AccountsModule } from "../accounts/accounts.module";
import { ActivitiesModule } from "../activities/activities.module";
import { CommonModule } from "../common/common.module";
import { CommunicationsModule } from "../communications/communications.module";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { BullQueueNames, QueueNames } from "../constants/queue.constants";
import { CsatModule } from "../csat/csat.module";
import { CustomFieldModule } from "../custom-field/custom-field.module";
import { FormsModule } from "../forms/forms.module";
import { StorageModule } from "../storage/storage.module";
import { TeamsModule } from "../teams/teams.module";
import { UsersModule } from "../users/users.module";
import { TicketsGrpcController } from "./controllers/grpc/tickets-grpc.controller";
import { TicketBulkActionController } from "./controllers/ticket-bulk.action.controller";
import { TicketDraftController } from "./controllers/ticket-draft.action.controller";
import { TicketPriorityActionController } from "./controllers/ticket-priority.action.controller";
import { TicketSentimentActionController } from "./controllers/ticket-sentiment.controller";
import { TicketStatusActionController } from "./controllers/ticket-status.action.controller";
import { TicketTypeActionController } from "./controllers/ticket-type.action.controller";
import { TicketsController } from "./controllers/tickets.controller";
import { TicketsEventsFactory } from "./events/tickets-events.factory";
import { TicketsListeners } from "./listeners/tickets.listeners";
import { TicketSnsPublisherConsumer } from "./processors/ticket.sns-publish.processor";
import {
  ThenaAgentAllocator,
  ThenaRequestRouterEngine,
  ThenaRuleEvaluator,
} from "./routing";
import { RequestRouterProvider } from "./routing/providers/request-router.provider";
import { TicketAssignmentProcessor } from "./routing/providers/thena-request-router/agent-allocator/processors/ticket-assignment.processor";
import { TicketAssignmentQueue } from "./routing/providers/thena-request-router/agent-allocator/queues/ticket-assignment.queue";
import { ScheduledAssignmentService } from "./routing/providers/thena-request-router/agent-allocator/services/scheduled-assignment.service";
import { UserAvailabilityService } from "./routing/providers/thena-request-router/agent-allocator/services/user-availability.service";
import { RoundRobinStrategy } from "./routing/providers/thena-request-router/agent-allocator/strategies";
import { TicketAnnotatorService } from "./services/ticket-annotator.service";
import { TicketsBulkActionService } from "./services/ticket-bulk.action.service";
import { TicketDraftService } from "./services/ticket-draft.action.service";
import { TicketPriorityActionService } from "./services/ticket-priority.action.service";
import { TicketSentimentActionService } from "./services/ticket-sentiment.service";
import { TicketStatusActionService } from "./services/ticket-status.action.service";
import { TicketTypeActionService } from "./services/ticket-type.action.service";
import { TicketValidationService } from "./services/ticket-validation.service";
import { TicketsService } from "./services/tickets.service";
import { TicketResponseDto } from "./transformer/ticket-response.dto";
import { TICKET_SNS_PUBLISHER } from "./utils/tickets.constants";

@Module({
  imports: [
    ConfigModule,
    CsatModule,
    CommonModule,
    ActivitiesModule,
    BullModule.registerQueueAsync({
      name: QueueNames.TICKET_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
    BullModule.registerQueueAsync({
      name: QueueNames.COMMENT_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600,
            count: 1000,
          },
          removeOnFail: {
            age: 24 * 3600,
          },
        },
      }),
    }),
    BullModule.registerQueueAsync({
      name: BullQueueNames.TICKET_ASSIGNMENTS,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600,
            count: 1000,
          },
          removeOnFail: {
            age: 24 * 3600,
          },
        },
      }),
    }),
    TypeOrmModule.forFeature([
      // Tickets
      Ticket,
      TicketRepository,

      // Ticket Status
      TicketStatus,
      TicketStatusRepository,

      // Ticket Priority
      TicketPriority,
      TicketPriorityRepository,

      // Ticket Type
      TicketType,
      TicketTypeRepository,

      // Ticket Relationships
      TicketRelationships,
      TicketRelationshipsRepository,

      // Ticket Sentiment
      TicketSentiment,
      TicketSentimentRepository,

      // Custom Field
      CustomField,
      CustomFieldRepository,

      // Ticket Drafts [This is a generic entity for all draft types]
      Draft,
      DraftRepository,

      // Team Routing Rules
      TeamRoutingRules,
      TeamRoutingRulesRepository,

      // Ticket Time Logs
      TicketTimeLog,
      TicketTimeLogRepository,

      // Time off
      TimeOff,
      TimeOffRepository,
    ]),
    forwardRef(() => CommunicationsModule),
    UsersModule,
    TeamsModule,
    AccountsModule,
    CustomFieldModule,
    StorageModule,
    FormsModule,
  ],
  controllers: [
    TicketsController,
    TicketsGrpcController,
    TicketBulkActionController,
    TicketTypeActionController,
    TicketStatusActionController,
    TicketPriorityActionController,
    TicketDraftController,
    TicketSentimentActionController,
  ],
  providers: [
    // Bull service
    TicketSnsPublisherConsumer,

    // Transaction Service
    TransactionService,

    // Auth GRPC
    ApiKeyGrpcStrategy,
    BearerTokenGrpcStrategy,
    AuthenticationGrpcClient,
    GrpcAuthGuard,
    UserOrgInternalGrpcStrategy,

    // Request Router
    RequestRouterProvider,
    ThenaRequestRouterEngine,
    ThenaRuleEvaluator,
    ThenaAgentAllocator,
    TeamRoutingRulesRepository,
    RoundRobinStrategy,

    // Scheduled Assignment
    ScheduledAssignmentService,
    TicketAssignmentQueue,
    TicketAssignmentProcessor,
    UserAvailabilityService,

    // Tickets
    TicketsService,
    TicketRepository,
    TicketsBulkActionService,
    CachedTicketRepository,

    // Ticket Relationships
    TicketRelationshipsRepository,
    CachedTicketRelationshipsRepository,

    // Ticket Status
    TicketStatusActionService,
    TicketStatusRepository,
    CachedTicketStatusRepository,

    // Ticket Priority
    TicketPriorityActionService,
    TicketPriorityRepository,
    CachedTicketPriorityRepository,

    // Ticket Type
    TicketTypeActionService,
    TicketTypeRepository,
    CachedTicketTypeRepository,

    // Ticket Draft
    TicketDraftService,
    DraftRepository,
    CachedDraftsRepository,

    // Ticket Sentiment
    TicketSentimentActionService,
    TicketSentimentRepository,

    // Ticket Time Logs
    TicketTimeLogRepository,

    // Time off
    TimeOff,
    TimeOffRepository,

    // Ticket Validation
    TicketValidationService,

    // Ticket Response DTO
    TicketResponseDto,

    // SNS Publisher
    {
      provide: TICKET_SNS_PUBLISHER,
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) => {
        return new SNSPublisherService(
          {
            topicArn: configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
            region: configService.get(ConfigKeys.AWS_REGION),
            credentials: {
              accessKeyId: configService.get(ConfigKeys.AWS_ACCESS_KEY),
              secretAccessKey: configService.get(ConfigKeys.AWS_SECRET_KEY),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, "Sentry", "CustomLogger"],
    },
    // Base services
    TicketAnnotatorService,

    // Tickets Listeners and events factory
    TicketsListeners,
    TicketsEventsFactory,

    // Grpc Client
    GrpcClient,

    {
      provide: 'REDIS_CLIENT',
      useFactory: (configService: ConfigService) => {
        return new Redis({
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        });
      },
      inject: [ConfigService],
    },
  ],
  exports: [
    TicketsService,
    TicketStatusActionService,
    TicketPriorityActionService,
    TicketTypeActionService,
    TicketDraftService,
    TicketValidationService,
    TicketRepository,
    CachedTicketPriorityRepository,
    CachedTicketStatusRepository,
    CachedTicketTypeRepository,
    CachedTicketRepository,
    TicketResponseDto,
  ],
})
export class TicketsModule {}
