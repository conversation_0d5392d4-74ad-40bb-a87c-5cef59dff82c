import { ApiProperty } from "@nestjs/swagger";
import { RequestSource } from "@repo/nestjs-commons/middlewares";
import { Ticket } from "@repo/thena-platform-entities";
import { ExternalCustomFieldValuesDto } from "../../custom-field/dto/custom-field-values.dto";

/**
 * Ticket response DTO, this transforms ticket response as it goes out of the platform via HTTP Calls
 * Note that a ticket is assigned to a sub-team (which for us is the `team_id`) which belongs to a parent team (which for us is the `parent_team_id`)
 * however when this ticket is fetched, we only fetch the sub-team and parent team ids and map them to the `team_id` and `parent_team_id` respectively
 */
export class TicketResponseDto {
  @ApiProperty({ description: "The unique identifier of the ticket" })
  id: string;

  @ApiProperty({ description: "The title of the ticket" })
  title: string;

  @ApiProperty({ description: "The ticket ID of the ticket" })
  ticketId: number;

  @ApiProperty({ description: "The description of the ticket" })
  description: string;

  @ApiProperty({ description: "The source of the ticket" })
  source?: RequestSource;

  @ApiProperty({ description: "The account ID of the ticket" })
  accountId: string;

  @ApiProperty({ description: "The status of the ticket" })
  status?: string;

  @ApiProperty({ description: "The status ID of the ticket" })
  statusId?: string;

  @ApiProperty({ description: "The priority of the ticket" })
  priority?: string;

  @ApiProperty({ description: "The priority ID of the ticket" })
  priorityId?: string;

  @ApiProperty({ description: "The story points of the ticket" })
  storyPoints?: number;

  @ApiProperty({ description: "The account of the ticket" })
  account?: string;

  @ApiProperty({ description: "The team ID of the ticket" })
  teamId: string;

  @ApiProperty({ description: "The name of the team of the ticket" })
  teamName?: string;

  @ApiProperty({ description: "The identifier of the team of the ticket" })
  teamIdentifier?: string;

  @ApiProperty({ description: "The sub team ID of the ticket" })
  subTeamId?: string;

  @ApiProperty({ description: "The name of the sub team of the ticket" })
  subTeamName?: string;

  @ApiProperty({ description: "The identifier of the sub team of the ticket" })
  subTeamIdentifier?: string;

  @ApiProperty({ description: "Whether the ticket is private" })
  isPrivate: boolean;

  @ApiProperty({ description: "The type ID of the ticket" })
  typeId?: string;

  @ApiProperty({ description: "The name of the type of the ticket" })
  type?: string;

  @ApiProperty({ description: "The assigned agent of the ticket" })
  assignedAgent?: string;

  @ApiProperty({ description: "The assigned agent ID of the ticket" })
  assignedAgentId?: string;

  @ApiProperty({ description: "The assigned agent email of the ticket" })
  assignedAgentEmail?: string;

  @ApiProperty({ description: "The requestor email of the ticket" })
  requestorEmail: string;

  @ApiProperty({ description: "The customer contact ID of the ticket" })
  customerContactId?: string;

  @ApiProperty({ description: "The customer contact first name of the ticket" })
  customerContactFirstName?: string;

  @ApiProperty({ description: "The customer contact last name of the ticket" })
  customerContactLastName?: string;

  @ApiProperty({ description: "The customer contact email of the ticket" })
  customerContactEmail?: string;

  @ApiProperty({ description: "The submitter email of the ticket" })
  submitterEmail: string;

  @ApiProperty({ description: "The custom field values" })
  customFieldValues?: ExternalCustomFieldValuesDto[];

  @ApiProperty({ description: "The deleted at date of the ticket" })
  deletedAt?: string;

  @ApiProperty({ description: "The archived at date of the ticket" })
  archivedAt?: string;

  @ApiProperty({ description: "The created at date of the ticket" })
  createdAt: string;

  @ApiProperty({ description: "The updated at date of the ticket" })
  updatedAt: string;

  @ApiProperty({ description: "The form ID of the ticket" })
  formId?: string;

  @ApiProperty({ description: "The AI generated title of the ticket" })
  aiGeneratedTitle?: string;

  @ApiProperty({ description: "The AI generated summary of the ticket" })
  aiGeneratedSummary?: string;

  @ApiProperty({ description: "The sentiment of the ticket" })
  sentiment?: string;

  @ApiProperty({ description: "The sentiment ID of the ticket" })
  sentimentId?: string;

  static fromEntity(entity: Ticket): TicketResponseDto {
    const dto = new TicketResponseDto();

    dto.id = entity.uid;
    dto.ticketId = entity.ticketId;
    dto.title = entity.title;
    dto.description = entity.description;
    dto.source = entity.source;
    dto.status = entity.status?.displayName ?? entity.status?.name;
    dto.priority = entity.priority?.displayName ?? entity.priority?.name;
    dto.account = entity.account?.name;
    dto.accountId = entity.account?.uid;

    // Team maps to parent team
    dto.teamId = entity?.team?.uid;
    dto.teamName = entity?.team?.name;
    dto.teamIdentifier = entity?.team?.identifier;

    // Sub team maps to current team
    dto.subTeamId = entity?.subTeam?.uid;
    dto.subTeamName = entity?.subTeam?.name;
    dto.subTeamIdentifier = entity?.subTeam?.identifier;

    dto.isPrivate = entity.isPrivate;
    dto.typeId = entity.type?.uid;
    dto.formId = entity.form?.uid;
    dto.type = entity.type?.name;
    dto.assignedAgent = entity.assignedAgent?.name;
    dto.assignedAgentId = entity.assignedAgent?.uid;
    dto.assignedAgentEmail = entity.assignedAgent?.email;
    dto.requestorEmail = entity.requestorEmail;
    dto.submitterEmail = entity.submitterEmail;
    dto.customFieldValues = entity.customFieldValues?.map((cfv) => ({
      customFieldId: cfv.customField.uid,
      data: cfv.data,
      metadata: cfv.metadata,
    }));
    dto.customerContactId = entity.customerContact?.uid;
    dto.customerContactFirstName = entity.customerContact?.firstName;
    dto.customerContactLastName = entity.customerContact?.lastName;
    dto.customerContactEmail = entity.customerContact?.email;
    dto.statusId = entity.status?.uid;
    dto.priorityId = entity.priority?.uid;
    dto.sentimentId = entity.sentiment?.uid;
    dto.sentiment = entity.sentiment?.name;
    dto.storyPoints = entity.storyPoints;
    dto.aiGeneratedTitle = entity.aiGeneratedTitle;
    dto.aiGeneratedSummary = entity.aiGeneratedSummary;
    dto.deletedAt = entity.deletedAt
      ? new Date(entity.deletedAt).toISOString()
      : undefined;
    dto.archivedAt = entity.archivedAt
      ? new Date(entity.archivedAt).toISOString()
      : undefined;
    dto.createdAt = entity.createdAt
      ? new Date(entity.createdAt).toISOString()
      : undefined;
    dto.updatedAt = entity.updatedAt
      ? new Date(entity.updatedAt).toISOString()
      : undefined;

    return dto;
  }
}
