import { Inject, Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { ILogger } from "@repo/nestjs-commons/logger";
import { TicketRepository } from "@repo/thena-platform-entities";
import { EmittableUserEvents, UserAvailabilityChangedEvent } from "../../users/events/users.events";
import { UsersService } from "../../users/services/users.service";
import {
  EmittableTicketEvents,
  TicketAssignedEvent,
} from "../events/tickets.events";
import { ScheduledAssignmentService } from "../routing/providers/thena-request-router/agent-allocator/services/scheduled-assignment.service";

@Injectable()
export class TicketsListeners {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,

    // Tickets Repository
    private readonly ticketRepository: TicketRepository,

    // Users Service
    private readonly usersService: UsersService,

    private readonly scheduledAssignmentService: ScheduledAssignmentService,
  ) {}

  @OnEvent(EmittableTicketEvents.TICKET_ASSIGNED)
  @OnEvent(EmittableTicketEvents.TICKET_UNASSIGNED)
  async handleTicketAssignedOrUnassignedEvent(event: TicketAssignedEvent) {
    try {
      const { agentId, ticketId, orgId, teamId, teamUid } = event;

      this.logger.log(
        `Ticket assigned event received: ${JSON.stringify({
          agentId,
          ticketId,
        })}`,
      );

      // Fetch the ticket
      const ticketExists = await this.ticketRepository.exists({
        where: {
          id: ticketId,
          organizationId: orgId,
        },
      });

      // If the ticket is not found, log an error and return
      if (!ticketExists) {
        this.logger.error(
          `Ticket not found: ${JSON.stringify({ ticketId, orgId })}`,
        );
        return;
      }

      const totalCount = await this.ticketRepository.count({
        where: {
          assignedAgentId: agentId,
          organizationId: orgId,
          teamId: teamId,
        },
      });

      // Update the user's workload
      await this.usersService.updateUserWorkload(agentId, orgId, {
        forTeamId: teamUid,
        newTotalTickets: totalCount,
      });
    } catch (error) {
      this.logger.error(
        `Error handling ticket assigned event: ${error.message}, stack: ${error.stack}`,
      );
    }
  }

  @OnEvent(EmittableUserEvents.USER_AVAILABILITY_CHANGED)
  async handleUserAvailabilityChangeEvent(event: UserAvailabilityChangedEvent) {
    try {
      const { user, orgId, changeType } = event;

      this.logger.log(
        `User availability change event received: ${JSON.stringify({
          user,
          orgId,
          changeType,
        })}`,
      );

      // Call the scheduled assignment service to handle the availability change
      await this.scheduledAssignmentService.handleUserAvailabilityChange(
        user
      );

      this.logger.log(
        `Successfully processed availability change for user ${user.uid}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling user availability change event: ${error.message}, stack: ${error.stack}`,
      );
    }
  }
}
