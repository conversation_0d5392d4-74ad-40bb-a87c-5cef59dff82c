import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import {
  TicketRepository,
  TicketSentiment,
  TicketSentimentRepository,
} from "@repo/thena-platform-entities";
import { FindOptionsRelations, FindOptionsWhere } from "typeorm";
import { CurrentUser } from "../../common/decorators";
import { TeamsService } from "../../teams/services/teams.service";
import { CreateTicketSentimentDto, UpdateTicketSentimentDto } from "../dto";

@Injectable()
export class TicketSentimentActionService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database repositories
    private readonly ticketSentimentRepository: TicketSentimentRepository,
    private readonly ticketRepository: TicketRepository,

    // Injected services
    private readonly teamsService: TeamsService,
  ) {}

  /**
   * Queries a ticket sentiment.
   * @param where The where clause.
   * @param relations The relations to include.
   * @returns The ticket sentiment.
   */
  queryTicketSentiment(
    where: FindOptionsWhere<TicketSentiment>,
    relations?: FindOptionsRelations<TicketSentiment>,
  ) {
    return this.ticketSentimentRepository.findByCondition({
      where,
      relations,
    });
  }

  /**
   * Queries many ticket sentiments.
   * @param where The where clause.
   * @param relations The relations to include.
   * @returns The ticket sentiments.
   */
  queryManyTicketSentiments(
    where: FindOptionsWhere<TicketSentiment>,
    relations?: FindOptionsRelations<TicketSentiment>,
  ) {
    return this.ticketSentimentRepository.findAll({
      where,
      relations,
    });
  }

  /**
   * Finds the closest match for a ticket sentiment name using levenshtein distance.
   * @param name The name of the ticket sentiment to match against.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The closest match for the ticket sentiment name.
   */
  findClosestMatchLevenshtein(name: string, orgId: string, teamId?: string) {
    return this.ticketSentimentRepository.findClosestMatchLevenshtein(
      name,
      orgId,
      teamId,
    );
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @param organizationId The ID of the organization.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    // Fetch the team
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    // If the team is not found, throw an error
    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  /**
   * Finds a duplicate default sentiment.
   * @param organizationId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The duplicate default sentiment.
   */
  private async findDuplicateDefaultSentiment(
    organizationId: string,
    teamId: string,
  ) {
    const sentimentExists =
      await this.ticketSentimentRepository.findByCondition({
        where: {
          isDefault: true,
          team: { id: teamId },
          organization: { id: organizationId },
        },
      });

    return sentimentExists;
  }

  /**
   * Finds all ticket sentiments for an organization.
   * @param user The current user.
   * @param teamId The ID of the team to filter by.
   * @returns The ticket sentiments.
   */
  async findAllTicketSentiments(user: { orgId: string }, teamId?: string) {
    // Build the where clause
    const whereClause: FindOptionsWhere<TicketSentiment> = {
      organization: { id: user.orgId },
    };

    // If the team ID is provided, filter by team ID
    if (teamId?.trim()) {
      const team = await this.validateAndFetchTeam(teamId, user.orgId);
      whereClause.team = { id: team.id };

      // If the team has a parent team then we'll use the parent team's sentiments
      if (team.parentTeamId) {
        whereClause.team = { id: team.parentTeamId };
      }
    }

    // Find all ticket sentiments for the organization
    const ticketSentiments = await this.ticketSentimentRepository.findAll({
      where: whereClause,
      relations: { team: true, organization: true },
    });

    return ticketSentiments;
  }

  /**
   * Finds a ticket sentiment by its ID.
   * @param id The ID of the ticket sentiment to find.
   * @param user The current user.
   * @returns The ticket sentiment.
   */
  async findTicketSentimentById(id: string, user: CurrentUser) {
    const ticketSentiment =
      await this.ticketSentimentRepository.findByCondition({
        where: { uid: id, organization: { id: user.orgId } },
        relations: { team: true, organization: true },
      });

    // If the ticket sentiment is not found, throw an error
    if (!ticketSentiment) {
      throw new NotFoundException("Ticket sentiment not found!");
    }

    return ticketSentiment;
  }

  /**
   * Creates a new ticket sentiment.
   * @param createTicketSentimentDto The create ticket sentiment DTO.
   * @param user The current user.
   * @returns The newly created ticket sentiment.
   */
  async createTicketSentiment(
    createTicketSentimentDto: CreateTicketSentimentDto,
    user: CurrentUser,
  ) {
    const team = await this.validateAndFetchTeam(
      createTicketSentimentDto.teamId,
      user.orgId,
    );

    // If the team has a parent team then we'll throw since child teams cannot have custom sentiments
    if (team.parentTeamId) {
      this.logger.debug(
        `Child team ${team.name} (${team.id}) has a parent team ${team.parentTeamId}, so we cannot create a custom sentiment for it!`,
      );

      throw new BadRequestException(
        "Child teams cannot have custom sentiments!",
      );
    }

    // If the sentiment is default, check if there is already a default sentiment for the team
    if (createTicketSentimentDto.isDefault) {
      const existingDefaultSentiment = await this.findDuplicateDefaultSentiment(
        user.orgId,
        team.id,
      );

      // If the default sentiment already exists, throw an error
      if (existingDefaultSentiment) {
        throw new BadRequestException("Default sentiment already exists!");
      }
    }

    // Create a new ticket sentiment
    const newTicketSentiment = await this.ticketSentimentRepository.save({
      name: createTicketSentimentDto.name,
      description: createTicketSentimentDto.description,
      isDefault: createTicketSentimentDto.isDefault,
      icon: createTicketSentimentDto.icon,

      // Relationships
      team: { id: team.id },
      organization: { id: user.orgId },
    });

    // Find the newly created ticket sentiment
    const ticketSentiment = await this.findTicketSentimentById(
      newTicketSentiment.id,
      user,
    );

    return ticketSentiment;
  }

  /**
   * Updates a ticket sentiment.
   * @param id The ID of the ticket sentiment to update.
   * @param updateTicketSentimentDto The update ticket sentiment DTO.
   * @param user The current user.
   * @returns The updated ticket sentiment.
   */
  async updateTicketSentiment(
    id: string,
    updateTicketSentimentDto: UpdateTicketSentimentDto,
    user: CurrentUser,
  ) {
    // Find the ticket sentiment by its ID
    const ticketSentiment = await this.findTicketSentimentById(id, user);

    // Update the ticket sentiment
    if (updateTicketSentimentDto.isDefault) {
      const existingDefaultSentiment = await this.findDuplicateDefaultSentiment(
        user.orgId,
        ticketSentiment.team.id,
      );

      // If the default sentiment already exists, throw an error
      if (existingDefaultSentiment) {
        throw new BadRequestException("Default sentiment already exists!");
      }
    }

    // Update the ticket sentiment
    await this.ticketSentimentRepository.update(id, {
      name: updateTicketSentimentDto.name,
      description: updateTicketSentimentDto.description,
      isDefault: updateTicketSentimentDto.isDefault,
      icon: updateTicketSentimentDto.icon,
    });

    // Find the updated ticket sentiment
    const updatedTicketSentiment = await this.findTicketSentimentById(id, user);
    return updatedTicketSentiment;
  }

  /**
   * Deletes a ticket sentiment.
   * @param id The ID of the ticket sentiment to delete.
   * @param user The current user.
   * @returns The deleted ticket sentiment.
   */
  async deleteTicketSentiment(id: string, user: CurrentUser) {
    // Find the ticket sentiment by its ID
    const ticketSentiment = await this.findTicketSentimentById(id, user);

    // Find the number of tickets with the sentiment
    const ticketsWithSentimentCount = await this.ticketRepository.count({
      where: {
        sentiment: { id: ticketSentiment.id },
        organizationId: user.orgId,
      },
    });

    // If there are tickets with the sentiment, throw an error
    if (ticketsWithSentimentCount > 0) {
      throw new BadRequestException(
        "Cannot delete a sentiment that has tickets!",
      );
    }

    // Delete the ticket sentiment
    await this.ticketSentimentRepository.softDelete({
      id: ticketSentiment.id,
      uid: ticketSentiment.uid,
      organizationId: user.orgId,
    });

    return ticketSentiment;
  }
}
