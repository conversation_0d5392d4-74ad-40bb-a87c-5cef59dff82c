import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import {
  CachedTicketPriorityRepository,
  TicketPriority,
  TicketPriorityRepository,
  TicketRepository,
  User,
} from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { FindManyOptions, In } from "typeorm";
import { TeamsService } from "../../teams/services/teams.service";
import { UsersService } from "../../users/services/users.service";
import {
  CreateTicketPriorityDto,
  UpdateTicketPriorityDto,
} from "../dto/ticket-priority.dto";

@Injectable()
export class TicketPriorityActionService {
  constructor(
    // Injected cached repositories
    private cachedTicketPriorityRepository: CachedTicketPriorityRepository,

    // Injected repositories
    private ticketPriorityRepository: TicketPriorityRepository,
    private ticketRepository: TicketRepository,

    // Injected services
    private usersService: UsersService,
    private teamsService: TeamsService,
  ) {}

  /**
   * Extracts the user from the request.
   * @param request The request object.
   * @returns The user.
   */
  private async extractUserFromRequest(request: FastifyRequest): Promise<User> {
    const userEmail = request.user.email;
    if (!userEmail) {
      throw new UnauthorizedException("User is not authenticated!");
    }

    const user = await this.usersService.findOneByEmail(userEmail);
    if (!user) {
      throw new UnauthorizedException("User is not authenticated!");
    }

    return user;
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  /**
   * Finds a duplicate default priority.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The duplicate default priority.
   */
  private async findDuplicateDefaultPriority(orgId: string, teamId: string) {
    return await this.ticketPriorityRepository.findByCondition({
      where: {
        isDefault: true,
        organizationId: orgId,
        teamId: teamId,
      },
    });
  }

  /**
   * Finds the closest match for a ticket status name using levenshtein distance.
   * @param name The name of the ticket status to match against.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The closest match for the ticket status name.
   */
  findClosestMatchLevenshtein(name: string, orgId: string, teamId?: string) {
    return this.ticketPriorityRepository.findClosestMatchLevenshtein(
      name,
      orgId,
      teamId,
    );
  }

  /**
   * Finds ticket priorities by their public IDs.
   * @param priorityIds The public IDs of the ticket priorities.
   * @param organizationId The ID of the organization.
   * @returns The ticket priorities.
   */
  findTicketPrioritiesByPublicIds(
    priorityIds: string[],
    organizationId: string,
  ) {
    return this.ticketPriorityRepository.findAll({
      where: { uid: In(priorityIds), organizationId },
    });
  }

  /**
   * @internal
   * Gets a default ticket priority.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The internal default ticket priority.
   */
  internalGetDefaultTicketPriority(orgId: string, teamId: string) {
    return this.ticketPriorityRepository.findByCondition({
      where: {
        isDefault: true,
        organizationId: orgId,
        teamId,
      },
    });
  }

  /**
   * @internal
   * Gets a ticket priority by its public ID.
   * @param priorityId The public ID of the ticket priority.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The internal ticket priority.
   */
  findTicketPriorityByPublicId(
    priorityId: string,
    orgId: string,
    teamId?: string,
  ) {
    const query: FindManyOptions<TicketPriority> = {
      where: { uid: priorityId, organizationId: orgId },
    };

    if (teamId) {
      query.where = { ...query.where, teamId };
    }

    return this.ticketPriorityRepository.findByCondition(query);
  }

  /**
   * Finds all ticket priorities for the organization.
   * @param user The user.
   * @param teamId The ID of the team to filter by.
   * @returns All ticket priorities for the organization.
   */
  async findAllTicketPriorities(user: { orgId: string }, teamId?: string) {
    // Build the where clause
    const whereClause: Record<string, string> = {
      organizationId: user.orgId,
    };

    // If the team ID is provided, filter by team ID
    if (teamId?.trim()) {
      // If the team ID is provided, filter by team ID
      const team = await this.validateAndFetchTeam(teamId, user.orgId);
      whereClause.teamId = team.id;

      // If the team has a parent team then we'll use the parent team's priorities
      if (team.parentTeamId) {
        whereClause.teamId = team.parentTeamId;
      }
    }

    // Find all ticket priorities for the organization
    const ticketPriorities = await this.ticketPriorityRepository.findAll({
      where: whereClause,
      relations: ["team", "organization"],
    });

    return ticketPriorities;
  }

  async findTicketPrioritiesByTeamId(teamId: string, organizationId: string) {
    const ticketPriorities = await this.ticketPriorityRepository.findAll({
      where: { teamId, organizationId },
      relations: ["team", "organization"],
    });

    return ticketPriorities;
  }

  /**
   * Finds a ticket priority by its ID.
   * @param ticketPriorityId The ID of the ticket priority to find.
   * @returns The ticket priority.
   */
  async findTicketPriorityById(
    ticketPriorityId: string,
    request: FastifyRequest,
  ) {
    // Get the organization ID from the user object attached to the request
    const user = await this.extractUserFromRequest(request);

    const ticketPriority =
      await this.cachedTicketPriorityRepository.findByCondition({
        where: { uid: ticketPriorityId, organizationId: user.organizationId },
        relations: ["team", "organization"],
      });

    if (!ticketPriority) {
      throw new NotFoundException("Ticket priority not found!");
    }

    return ticketPriority;
  }

  /**
   * Creates a new ticket priority.
   * @param createTicketPriorityDto The ticket priority data to create.
   * @param request The request object.
   * @returns The created ticket priority.
   */
  async createNewCustomPriority(
    createTicketPriorityDto: CreateTicketPriorityDto,
    request: FastifyRequest,
  ) {
    // Get the user from the request
    const user = await this.extractUserFromRequest(request);

    // Get the organization ID from the user object
    const orgId = user.organizationId;

    // Validate and fetch the team
    const team = await this.validateAndFetchTeam(
      createTicketPriorityDto.teamId,
      orgId,
    );

    // If the team has a parent team then we'll throw since child teams cannot have custom priorities
    if (team.parentTeamId) {
      throw new BadRequestException(
        "You cannot create priorities for a sub-teams!",
      );
    }

    // If the priority is default, check if there is already a default priority for the team
    if (createTicketPriorityDto.isDefault) {
      const existingDefaultPriority = await this.findDuplicateDefaultPriority(
        orgId,
        team.id,
      );
      if (existingDefaultPriority) {
        throw new BadRequestException("Default priority already exists!");
      }
    }

    // Create the ticket priority
    const newTicketPriority = this.cachedTicketPriorityRepository.create({
      name: createTicketPriorityDto.name,
      displayName:
        createTicketPriorityDto.displayName ?? createTicketPriorityDto.name,
      isDefault: createTicketPriorityDto.isDefault,
      description: createTicketPriorityDto.description,
      organizationId: orgId,
      teamId: team.id,
    });

    // Save the new ticket priority
    const ticketPriority = await this.cachedTicketPriorityRepository.save(
      newTicketPriority,
    );

    return ticketPriority;
  }

  /**
   * Updates a ticket priority.
   * @param ticketPriorityId The ID of the ticket priority to update.
   * @param updateTicketPriorityDto The ticket priority data to update.
   * @param request The request object.
   * @returns The updated ticket priority.
   */
  async updateCustomPriorityData(
    ticketPriorityId: string,
    updateTicketPriorityDto: UpdateTicketPriorityDto,
    request: FastifyRequest,
  ) {
    // Get the user email from the request
    const user = await this.extractUserFromRequest(request);

    // Find the ticket priority by its ID
    const ticketPriority =
      await this.cachedTicketPriorityRepository.findByCondition({
        where: { uid: ticketPriorityId, organizationId: user.organizationId },
      });

    if (!ticketPriority) {
      throw new NotFoundException("Ticket priority not found!");
    }

    // If the priority is default, check if there is already a default priority for the team
    if (updateTicketPriorityDto.isDefault) {
      const existingDefaultPriority = await this.findDuplicateDefaultPriority(
        user.organizationId,
        ticketPriority.teamId,
      );

      if (existingDefaultPriority) {
        throw new BadRequestException("Default priority already exists!");
      }
    }

    // Update the ticket priority
    const updatedTicketPriority =
      await this.cachedTicketPriorityRepository.save({
        ...ticketPriority,
        ...updateTicketPriorityDto,
      });

    return updatedTicketPriority;
  }

  /**
   * Deletes a ticket priority.
   * @param ticketPriorityId The ID of the ticket priority to delete.
   * @param request The request object.
   * @returns The deleted ticket priority.
   */
  async deleteCustomPriority(
    ticketPriorityId: string,
    request: FastifyRequest,
  ): Promise<Partial<TicketPriority>> {
    // Get the user from the request
    const user = await this.extractUserFromRequest(request);

    // Find the ticket priority by its ID
    const ticketPriority = await this.ticketPriorityRepository.findByCondition({
      where: { uid: ticketPriorityId, organizationId: user.organizationId },
    });

    // If the ticket priority is not found, throw an error
    if (!ticketPriority) {
      throw new NotFoundException("Ticket priority not found!");
    }

    // Find the number of tickets with the priority
    const ticketsWithPriorityCount = await this.ticketRepository.count({
      where: {
        priorityId: ticketPriority.id,
        organizationId: user.organizationId,
      },
    });

    // If there are tickets with the priority, throw an error
    if (ticketsWithPriorityCount > 0) {
      throw new BadRequestException(
        "Cannot delete a priority that has tickets!",
      );
    }

    // Delete the ticket priority
    const removedTicketPriority =
      await this.cachedTicketPriorityRepository.remove(ticketPriority);

    return removedTicketPriority;
  }
}
