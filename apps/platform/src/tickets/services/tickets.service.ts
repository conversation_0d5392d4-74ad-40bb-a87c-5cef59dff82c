import { InjectQueue } from "@nestjs/bullmq";
import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { ILogger } from "@repo/nestjs-commons/logger";
import { RequestSource } from "@repo/nestjs-commons/middlewares";
import {
  Account,
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  CachedTicketRelationshipsRepository,
  CachedTicketRepository,
  Comment,
  CommentRepository,
  CommentType,
  CommentVisibility,
  CustomerContact,
  CustomFieldValues,
  Form,
  Team,
  Ticket,
  TicketPriority,
  TicketPriorityRepository,
  TicketRelations,
  TicketRelationshipsRepository,
  TicketRepository,
  TicketSentiment,
  TicketSentimentRepository,
  TicketStatus,
  TicketStatusRepository,
  TicketTimeLogRepository,
  TicketType,
  TicketTypeRepository,
  TransactionService,
  User,
  UserType,
} from "@repo/thena-platform-entities";
import { TicketEvents } from "@repo/thena-shared-interfaces";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { cloneDeep, isArray, isEmpty, mergeWith } from "lodash";
import {
  DeepPartial,
  FindManyOptions,
  FindOptionsWhere,
  In,
  IsNull,
} from "typeorm";
import { AccountsService } from "../../accounts/services/accounts.service";
import { CustomerContactActionService } from "../../accounts/services/customer-contact.action.service";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators";
import { getMaxLimit } from "../../common/utils/api-helpers.utils";
import {
  extractEmailDetails,
  extractNameFromEmail,
} from "../../common/utils/extract-email-details";
import { CommentsActionService } from "../../communications/services/comments.action.service";
import { ReactionsActionService } from "../../communications/services/reactions.action.service";
import { QueueNames } from "../../constants/queue.constants";
import {
  CreateCustomFieldValuesDto,
  ExternalCustomFieldValuesDto,
} from "../../custom-field/dto/custom-field-values.dto";
import { CustomFieldValuesService } from "../../custom-field/services/custom-field-values.service";
import { ThenaRestrictedFieldService } from "../../custom-field/services/thena-restricted-field.service";
import { FormService } from "../../forms/services/form.service";
import { FormsValidatorService } from "../../forms/validators/form.validator";
import { StorageService } from "../../storage/services/storage-service";
import { TeamsService } from "../../teams/services/teams.service";
import { UsersService } from "../../users/services/users.service";
import { GetTicketsRelations, TicketSelect } from "../constants";
import {
  AssignTicketBody,
  AssignTicketQuery,
  CreateTicketBody,
  EscalateTicketBody,
  GetCommentsForTicketQuery,
  GetTicketQuery,
  GetTicketRelatedQuery,
  LinkTicketsBody,
  MarkDuplicateBody,
  MarkOrCreateSubTicketBody,
  TicketTimeLogDto,
  UpdateTicketBody,
} from "../dto";
import { TicketsEventsFactory } from "../events/tickets-events.factory";
import { EmittableTicketEvents } from "../events/tickets.events";
import { RequestRouterEngine } from "../routing/interfaces/engine.interface";
import { REQUEST_ROUTER_ENGINE } from "../routing/providers/request-router.provider";
import { EAGER_TICKET_RELATIONS } from "../utils/tickets.constants";
import { TicketPriorityActionService } from "./ticket-priority.action.service";
import { TicketSentimentActionService } from "./ticket-sentiment.service";
import { TicketStatusActionService } from "./ticket-status.action.service";
import { TicketTypeActionService } from "./ticket-type.action.service";

@Injectable()
export class TicketsService implements OnModuleInit {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,

    // Router
    @Inject(REQUEST_ROUTER_ENGINE)
    private readonly routingEngine: RequestRouterEngine,

    @InjectQueue(QueueNames.TICKET_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,

    // Event emitter
    private readonly eventEmitter: EventEmitter2,
    private readonly ticketsEventsFactory: TicketsEventsFactory,

    // Injected Repositories
    private readonly ticketRepository: TicketRepository,
    private readonly cachedTicketRepository: CachedTicketRepository,
    private readonly ticketRelationshipsRepository: TicketRelationshipsRepository,
    private readonly cachedTicketRelationshipsRepository: CachedTicketRelationshipsRepository,
    private readonly ticketTimeLogRepository: TicketTimeLogRepository,
    private readonly commentRepository: CommentRepository,

    private readonly ticketSentimentRepository: TicketSentimentRepository,
    private readonly ticketStatusRepository: TicketStatusRepository,
    private readonly ticketPriorityRepository: TicketPriorityRepository,
    private readonly ticketTypeRepository: TicketTypeRepository,

    // Injected Services
    private readonly activitiesService: ActivitiesService,
    private readonly transactionService: TransactionService,
    private readonly usersService: UsersService,
    private readonly teamsService: TeamsService,
    private readonly accountsService: AccountsService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly ticketTypeService: TicketTypeActionService,
    private readonly ticketStatusService: TicketStatusActionService,
    private readonly ticketPriorityService: TicketPriorityActionService,
    private readonly storageService: StorageService,
    private readonly customFieldValuesService: CustomFieldValuesService,
    private readonly formsValidatorService: FormsValidatorService,
    private readonly formService: FormService,
    private readonly thenaRestrictedFieldsService: ThenaRestrictedFieldService,
    @Inject(forwardRef(() => ReactionsActionService))
    private readonly reactionsActionService: ReactionsActionService,
    private readonly ticketSentimentService: TicketSentimentActionService,
    private readonly commentsActionService: CommentsActionService,
  ) {}
  onModuleInit() {}

  /**
   * Validates and fetches a status by its ID. Otherwise, returns default status.
   * @param statusId The ID of the status to fetch.
   * @param orgId The ID of the organization.
   * @returns The status.
   */
  private async validateAndFetchStatus(
    statusId: string,
    orgId: string,
    teamId: string,
    statusName?: string,
  ) {
    this.logger.log(
      `Validating and fetching status for org ${orgId}, team ${teamId}`,
    );

    if (!statusId && statusName && teamId) {
      this.logger.log(
        `Attempting to find closest status match for "${statusName}"`,
      );
      const closestMatch =
        await this.ticketStatusService.findClosestMatchLevenshtein(
          statusName,
          orgId,
          teamId,
        );

      if (closestMatch) {
        this.logger.log(`Found closest status match: ${closestMatch[0].name}`);
        return closestMatch[0];
      }
    }

    if (!statusId) {
      this.logger.log("No status ID provided, fetching default status");
      return this.ticketStatusService.internalGetDefaultTicketStatus(
        orgId,
        teamId,
      );
    }

    this.logger.log(`Fetching status with ID ${statusId}`);
    const providedStatus =
      await this.ticketStatusService.findTicketStatusByPublicId(
        statusId,
        orgId,
        teamId,
      );

    if (!providedStatus) {
      this.logger.warn(`Status not found with ID ${statusId}`);
      throw new NotFoundException("Status not found!");
    }

    const defaultSubStatus =
      await this.ticketStatusService.findDefaultSubStatus(
        providedStatus.id,
        orgId,
        teamId,
      );

    if (defaultSubStatus) {
      this.logger.log(`Using default sub-status for status ${statusId}`);
      return defaultSubStatus;
    }

    return providedStatus;
  }

  /**
   * Validates and fetches a sentiment by its ID. Otherwise, returns default sentiment.
   * @param sentimentId The ID of the sentiment to fetch.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The sentiment.
   */
  private async validateAndFetchSentiment(
    sentimentId: string,
    orgId: string,
    teamId: string,
    sentimentName?: string,
  ) {
    if (!sentimentId && sentimentName && teamId) {
      const closestMatch =
        await this.ticketSentimentService.findClosestMatchLevenshtein(
          sentimentName,
          orgId,
          teamId,
        );

      if (closestMatch) {
        return closestMatch[0];
      }
    }

    // If the sentiment ID is not provided, throw an error
    if (!sentimentId) {
      return this.ticketSentimentService.queryTicketSentiment({
        isDefault: true,
        team: { id: teamId },
        organization: { id: orgId },
      });
    }

    // Fetch the provided sentiment
    const providedSentiment =
      await this.ticketSentimentService.queryTicketSentiment({
        uid: sentimentId,
        team: { id: teamId },
        organization: { id: orgId },
      });

    // If the sentiment is not found, throw an error
    if (!providedSentiment) {
      throw new NotFoundException("Sentiment not found!");
    }

    return providedSentiment;
  }

  /**
   * Validates and fetches a priority by its ID. Otherwise, returns default priority.
   * @param priorityId The ID of the priority to fetch.
   * @param orgId The ID of the organization.
   * @param teamId The ID of the team.
   * @returns The priority.
   */
  private async validateAndFetchPriority(
    priorityId: string,
    orgId: string,
    teamId: string,
    priorityName?: string,
  ) {
    // If the status ID and status name are not provided, try using closest match
    if (!priorityId && priorityName && teamId) {
      const closestMatch =
        await this.ticketPriorityService.findClosestMatchLevenshtein(
          priorityName,
          orgId,
          teamId,
        );

      // If the closest match is found, return it
      if (closestMatch) {
        return closestMatch[0];
      }
    }

    if (!priorityId) {
      return this.ticketPriorityService.internalGetDefaultTicketPriority(
        orgId,
        teamId,
      );
    }

    const providedPriority =
      await this.ticketPriorityService.findTicketPriorityByPublicId(
        priorityId,
        orgId,
        teamId,
      );

    if (!providedPriority) {
      throw new NotFoundException("Priority not found!");
    }

    return providedPriority;
  }

  private async fetchStatusPriorityAndType(
    statusId: string,
    priorityId: string,
    typeId: string,
    orgId: string,
    teamId: string,
    validate?: boolean,
  ) {
    const promises: Array<Promise<TicketStatus | TicketPriority | TicketType>> =
      [
        this.validateAndFetchStatus(statusId, orgId, teamId),
        this.validateAndFetchPriority(priorityId, orgId, teamId),
        typeId ? this.validateAndFetchType(typeId, orgId) : null,
      ];

    const [status, priority, type] = await Promise.all(promises);

    // If the validate flag is true, validate the status and priority
    if (validate) {
      if (!status) {
        throw new BadRequestException(
          "Default ticket status is required but not configured for the team.",
        );
      }

      if (!priority) {
        throw new BadRequestException(
          "Default ticket priority is required but not configured for the team.",
        );
      }

      if (typeId && !type) {
        throw new BadRequestException("Provided ticket type was not found!");
      }
    }

    return { status, priority, type };
  }

  /**
   * Validates and fetches a type by its ID.
   * @param typeId The ID of the type to fetch.
   * @param orgId The ID of the organization.
   * @returns The type.
   */
  private async validateAndFetchType(typeId: string, orgId: string) {
    const providedType = await this.ticketTypeService.findTicketTypeByPublicId(
      typeId,
      orgId,
    );

    return providedType;
  }

  private async canPerformAction(
    user: CurrentUser,
    ticket: Ticket,
    team: Team,
    actions: { read: boolean; write: boolean },
  ) {
    // Check if the user belongs to the team
    const isAgentPartOfTeam = await this.teamsService.userBelongsToTeam(
      user.sub,
      team,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!isAgentPartOfTeam) {
      // If the team is private, throw an error
      if (team.isPrivate) {
        throw new NotFoundException("Team not found!");
      }

      // If the user does not have write access, throw an error
      if (actions.write) {
        throw new ForbiddenException("You are not a member of this team!");
      }
    }

    return { isAgentPartOfTeam };
  }

  /**
   * Gets the comments for a ticket.
   * @param ticketId The ID of the ticket.
   * @param query The query object.
   * @param currentUser The current user.
   * @returns The comments for the ticket.
   */
  public async getCommentsForTicket(
    ticketId: string,
    query: GetCommentsForTicketQuery,
    currentUser: CurrentUser,
  ) {
    const { page, limit, commentType, visibility } = query;

    this.logger.log(`GET_COMMENTS:GET_TICKET - START:${Date.now()}`);
    // Fetch the ticket
    const ticket = await this.ticketRepository.findByCondition({
      where: { uid: ticketId, organization: { id: currentUser.orgId } },
    });

    // If the ticket is not found, throw an error
    if (!ticket) {
      throw new NotFoundException("Ticket not found");
    }

    this.logger.log(`GET_COMMENTS:GET_TICKET - END:${Date.now()}`);

    // Build the where clause
    let whereClause: FindOptionsWhere<Comment> | FindOptionsWhere<Comment>[];

    // Filter comment types
    if (commentType === CommentType.COMMENT) {
      // If the comment type is provided, add it to the where clause
      whereClause = {
        ticket: { id: ticket.id },
        organizationId: currentUser.orgId,
        parentCommentId: IsNull(),
        commentType: CommentType.COMMENT,
        commentVisibility: visibility,
      };
    } else {
      // If the comment type is note, then we need to fetch all the public and private notes (for the user)
      whereClause = [
        {
          ticket: { id: ticket.id },
          organizationId: currentUser.orgId,
          parentCommentId: IsNull(),
          commentType: CommentType.NOTE,
          commentVisibility: CommentVisibility.PUBLIC,
        },
        {
          ticket: { id: ticket.id },
          organizationId: currentUser.orgId,
          parentCommentId: IsNull(),
          commentType: CommentType.NOTE,
          author: { id: currentUser.sub },
          commentVisibility: CommentVisibility.PRIVATE,
        },
      ];
    }

    this.logger.log(`GET_COMMENTS:FETCH_COMMENTS - START:${Date.now()}`);
    // Find comments for the ticket
    const comments = await this.commentRepository.fetchPaginatedResults(
      { limit: limit ?? 100, page: page ?? 0 },
      { where: whereClause, relations: { attachments: true } },
    );

    this.logger.log(`GET_COMMENTS:FETCH_COMMENTS - END:${Date.now()}`);

    this.logger.log(
      `GET_COMMENTS:FETCH_AUTHORS_AND_CUSTOMER_CONTACTS - START:${Date.now()}`,
    );
    // Create a set of unique values for the authors and customer contacts
    const authorSet = new Set<string>();
    const customerContactSet = new Set<string>();

    // Populate the sets
    for (const comment of comments.results) {
      authorSet.add(comment.authorId);
      customerContactSet.add(comment.customerContactId);
    }

    // Fetch the authors and customer contacts
    const [authors, customerContacts] = await Promise.all([
      // Fetch the authors
      this.usersService.queryUsers({ id: In(Array.from(authorSet)) }),

      // Fetch the customer contacts
      this.customerContactActionService.queryCustomerContacts({
        id: In(Array.from(customerContactSet)),
      }),
    ]);

    // Create a map of authors and customer contacts
    const authorMap = new Map<string, User>();
    const customerContactMap = new Map<string, CustomerContact>();

    // Populate the maps
    for (const author of authors) {
      authorMap.set(author.id, author);
    }
    for (const customerContact of customerContacts) {
      customerContactMap.set(customerContact.id, customerContact);
    }

    this.logger.log(`GET_COMMENTS:ENHANCE_COMMENTS - START:${Date.now()}`);

    // Join the comments with the authors and customer contacts
    const joinedComments = comments.results.map((comment) => {
      return {
        ...comment,
        author: authorMap.get(comment.authorId),
        customerContact: customerContactMap.get(comment.customerContactId),
      };
    }) as Array<Comment & { author: User; customerContact: CustomerContact }>;

    // Enhance the comments with user reactions
    const enhancedComments =
      await this.commentsActionService.enhanceCommentsWithUserReactions(
        joinedComments,
        currentUser,
      );

    this.logger.log(`GET_COMMENTS:ENHANCE_COMMENTS - END:${Date.now()}`);

    return {
      results: enhancedComments,
      total: comments.total,
    };
  }

  /**
   * Finds tickets by status ID.
   * @param statusId The ID of the status.
   * @param organizationId The ID of the organization.
   * @returns The tickets.
   */
  public async findTicketsByStatusId(
    statusId: string,
    organizationId: string,
    count?: boolean,
  ) {
    const query: FindManyOptions<Ticket> = {
      where: { statusId, organizationId },
    };

    // If the count flag is true, return the count of tickets
    if (count) {
      return this.ticketRepository.count(query);
    }

    // Otherwise, return the tickets
    const tickets = await this.ticketRepository.findAll(query);
    return tickets;
  }

  /**
   * Finds tickets by priority ID.
   * @param priorityId The ID of the priority.
   * @param organizationId The ID of the organization.
   * @returns The tickets.
   */
  public async findTicketsByPriorityId(
    priorityId: string,
    organizationId: string,
    count?: boolean,
  ) {
    const query: FindManyOptions<Ticket> = {
      where: { priorityId, organizationId },
    };

    // If the count flag is true, return the count of tickets
    if (count) {
      return this.ticketRepository.count(query);
    }

    // Otherwise, return the tickets
    const tickets = await this.ticketRepository.findAll(query);
    return tickets;
  }

  /**
   * Gets a ticket by its ID.
   * @param id The ID of the ticket.
   * @param organizationId The ID of the organization.
   * @returns The ticket.
   */
  public async getTicketById(id: string, organizationId: string) {
    const ticket = await this.ticketRepository.findByCondition({
      where: { uid: id, organizationId },
      relations: EAGER_TICKET_RELATIONS,
    });

    return ticket;
  }

  /**
   * Gets the total number of tickets for a user.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @returns The total number of tickets for the user.
   */
  public async getTotalTicketsForUser(userId: string, orgId: string) {
    const tickets = await this.ticketRepository.count({
      where: { assignedAgentId: userId, organizationId: orgId },
    });

    return tickets;
  }

  /**
   * Checks if a ticket exists.
   * @param id The ID of the ticket.
   * @param organizationId The ID of the organization.
   * @returns Whether the ticket exists.
   */
  public async ticketExists(id: string, organizationId: string) {
    const exists = await this.ticketRepository.exists({
      where: { uid: id, organizationId },
    });

    return exists;
  }

  /**
   * Gets the sub-types of a ticket.
   * @param request The request object.
   * @param id The ID of the ticket.
   * @param query The query object.
   * @returns The sub-types of the ticket.
   */
  public async getTicketRelated(
    user: CurrentUser,
    id: string,
    query: GetTicketRelatedQuery,
  ) {
    const { linked, subtickets, duplicate, page, limit } = query;
    const MAX_PAGE_SIZE = 100;
    const validatedLimit = Math.min(limit ?? 50, MAX_PAGE_SIZE);
    const validatedPage = Math.max(page ?? 0, 0);

    // Fetch the ticket
    const ticket = await this.cachedTicketRepository.findByCondition({
      where: {
        uid: id,
        organizationId: user.orgId,
      },
    });

    // If the ticket is not found, throw an error
    if (!ticket) {
      throw new NotFoundException("Ticket not found!");
    }

    // Ensure only one relationship type is specified
    const relationshipTypes = [linked, subtickets, duplicate].filter(Boolean);
    if (relationshipTypes.length !== 1) {
      throw new BadRequestException(
        "Specify exactly one relationship type: linked, subtickets, or duplicate.",
      );
    }

    let ticketType = null;
    if (linked) {
      ticketType = TicketRelations.RELATED;
    } else if (subtickets) {
      ticketType = TicketRelations.CHILD;
    } else if (duplicate) {
      ticketType = TicketRelations.DUPLICATE;
    } else {
      throw new BadRequestException("Invalid query parameters!");
    }

    const relatedTickets =
      await this.ticketRelationshipsRepository.fetchPaginatedResults(
        { page: validatedPage, limit: validatedLimit },
        {
          where: {
            relationshipType: ticketType,
            sourceTicketId: ticket.id,
            organizationId: user.orgId,
          },
          relations: ["targetTicket"],
        },
      );

    return relatedTickets;
  }

  /**
   * Gets all tickets for the user.
   * @param request The request object.
   * @param query The query object.
   * @returns All tickets for the user.
   */
  public async getTickets(
    user: CurrentUser,
    team: Team | null,
    query: GetTicketQuery,
  ) {
    // Define the default find options
    const findOptions: FindManyOptions<Ticket> = {
      where: {
        organizationId: user.orgId,
        archivedAt: IsNull(),
        deletedAt: IsNull(),
      },
      order: { createdAt: "DESC" },
      relations: [
        // "team",
        "team",
        // "sentiment",
        // "status",
        // "priority",
        // "assignedAgent",
        // "account",
        // "type",
        // "form",
      ],
    };

    // If the team ID is provided, filter the tickets by the team ID
    if (team) {
      findOptions.where = { ...findOptions.where, teamId: team.id };
    } else {
      const userInTeams = await this.teamsService.findAllTeams(user);
      findOptions.where = {
        ...findOptions.where,
        teamId: In(userInTeams.map((team) => team.id)),
      };
    }

    // Fetch the tickets
    const tickets = await this.ticketRepository.fetchPaginatedResults(
      {
        page: query.page ?? 0,
        limit: query.limit ?? 50,
      },
      findOptions,
    );

    // Create a set of unique values for the tickets
    const sentimentSet = new Set<string>();
    const statusSet = new Set<string>();
    const prioritySet = new Set<string>();
    const assignedAgentSet = new Set<string>();
    const typeSet = new Set<string>();
    const formSet = new Set<string>();
    const accountSet = new Set<string>();

    // Populate the sets for tickets
    for (const ticket of tickets.results) {
      sentimentSet.add(ticket.sentimentId);
      statusSet.add(ticket.statusId);
      prioritySet.add(ticket.priorityId);
      assignedAgentSet.add(ticket.assignedAgentId);
      typeSet.add(ticket.typeId);
      formSet.add(ticket.formId);
      accountSet.add(ticket.accountId);
    }

    // Fetch the values for the above sets
    const [
      sentiments,
      statuses,
      priorities,
      assignedAgents,
      types,
      forms,
      accounts,
    ] = await Promise.all([
      // Fetch ticket sentiments
      this.ticketSentimentRepository.findAll({
        where: { id: In(Array.from(sentimentSet)) },
      }),

      // Fetch ticket statuses
      this.ticketStatusRepository.findAll({
        where: { id: In(Array.from(statusSet)) },
      }),

      // Fetch ticket priorities
      this.ticketPriorityRepository.findAll({
        where: { id: In(Array.from(prioritySet)) },
      }),

      // Fetch ticket assigned agents
      this.usersService.queryUsers({ id: In(Array.from(assignedAgentSet)) }),

      // Fetch ticket types
      this.ticketTypeRepository.findAll({
        where: { id: In(Array.from(typeSet)) },
      }),

      // Fetch ticket forms
      this.formService.queryForms({ id: In(Array.from(formSet)) }),

      // Fetch ticket accounts
      this.accountsService.queryAccountsAll({
        id: In(Array.from(accountSet)),
      }),
    ]);

    const sentimentMap = new Map<string, TicketSentiment>();
    const statusMap = new Map<string, TicketStatus>();
    const priorityMap = new Map<string, TicketPriority>();
    const assignedAgentMap = new Map<string, User>();
    const typeMap = new Map<string, TicketType>();
    const formMap = new Map<string, Form>();
    const accountMap = new Map<string, Account>();

    for (const sentiment of sentiments) {
      sentimentMap.set(sentiment.id, sentiment);
    }

    for (const status of statuses) {
      statusMap.set(status.id, status);
    }

    for (const priority of priorities) {
      priorityMap.set(priority.id, priority);
    }

    for (const assignedAgent of assignedAgents) {
      assignedAgentMap.set(assignedAgent.id, assignedAgent);
    }

    for (const type of types) {
      typeMap.set(type.id, type);
    }

    for (const form of forms) {
      formMap.set(form.id, form);
    }

    for (const account of accounts) {
      accountMap.set(account.id, account);
    }

    // Join the tickets
    const joinedTickets = tickets.results.map((ticket) => {
      return {
        ...ticket,
        team,
        sentiment: sentimentMap.get(ticket.sentimentId),
        status: statusMap.get(ticket.statusId),
        priority: priorityMap.get(ticket.priorityId),
        assignedAgent: assignedAgentMap.get(ticket.assignedAgentId),
        type: typeMap.get(ticket.typeId),
        form: formMap.get(ticket.formId),
        account: accountMap.get(ticket.accountId),
      };
    });

    return {
      results: joinedTickets,
      total: tickets.total,
    };
  }

  /**
   * Gets the tickets with cursor.
   * @param user The user making the request.
   * @param query The query object.
   * @returns The tickets and the cursor.
   */
  public async getTicketsWithCursor(
    user: CurrentUser,
    query: { limit: number; afterCursor?: string; teamId?: string },
  ) {
    const { limit, afterCursor } = query || {};

    // Validate the limit
    const validatedLimit = getMaxLimit(limit);

    // Get the teams that the user belongs to
    let teams: Array<{ teamId: string }> | null = null;

    // If the team ID is provided, check if the user belongs to the team
    if (query.teamId) {
      // Check if the user belongs to the team
      const { userInTeam, team } = await this.teamsService.canUserReadTeam(
        query.teamId,
        user,
      );

      // If the user does not belong to the team, throw an error
      if (!userInTeam) {
        throw new NotFoundException(
          "Either you're not a member of this team or this team does not exist!",
        );
      }

      // Add the team ID to the teams array
      teams = [{ teamId: team.id }];
    } else {
      // Get the teams that the user belongs to
      teams = await this.teamsService.getTeamsByUser(user, {
        teamId: true,
      });
    }

    // Fetch the tickets
    const { data, cursor } = await this.ticketRepository.fetchWithCursor(
      { limit: validatedLimit, order: "DESC", afterCursor },
      {
        organizationId: user.orgId,
        teamId: In(teams.map((team) => team.teamId)),
      },
      "tickets",
      [
        { key: "tickets.team", alias: "team" },
        { key: "tickets.status", alias: "status" },
        { key: "tickets.priority", alias: "priority" },
        { key: "tickets.assignedAgent", alias: "assignedAgent" },
        { key: "tickets.sentiment", alias: "sentiment" },
        { key: "tickets.type", alias: "type" },
        { key: "tickets.account", alias: "account" },
      ],
    );

    return { data, cursor };
  }

  /**
   * Creates custom field values for a ticket
   * @param customFieldValues The custom field values to create
   * @param organizationId The organization ID
   * @returns Array of created custom field values
   */
  private async createCustomFieldValues(
    customFieldValues: Array<{
      customFieldId: string;
      data: any;
      metadata?: Record<string, any>;
    }>,
    organizationId: string,
  ): Promise<CustomFieldValues[]> {
    const newCustomFieldValues: CustomFieldValues[] = [];

    for (const customFieldValue of customFieldValues) {
      const customFieldValuesDto = new CreateCustomFieldValuesDto();
      customFieldValuesDto.customFieldId = customFieldValue.customFieldId;
      customFieldValuesDto.data = customFieldValue.data;
      customFieldValuesDto.metadata = customFieldValue.metadata;
      customFieldValuesDto.organizationId = organizationId;

      const customField = await this.customFieldValuesService.create(
        organizationId,
        customFieldValuesDto,
      );
      newCustomFieldValues.push(customField);
    }

    return newCustomFieldValues;
  }

  /**
   * Creates a ticket.
   * @param request The request object.
   * @param body The ticket payload.
   * @returns The created ticket.
   */
  public async createTicket(
    user: CurrentUser,
    team: Team,
    body: CreateTicketBody,
    source: RequestSource,
  ) {
    this.logger.log(
      `Creating new ticket for team ${team.uid} by user ${user.uid}`,
    );
    const { performRouting = true } = body;

    // Validate the form
    let form: Form | null = null;
    if (!body.formId) {
      this.logger.log("No form ID provided, fetching team's default form");
      form = await this.formService.getDefaultFormForTeam(team.id);

      if (!form) {
        this.logger.log(
          "No default form found, fetching organization's default form",
        );
        form = await this.formService.getDefaultForm(user.orgId);
      }
    } else {
      this.logger.log(`Fetching form with ID ${body.formId}`);
      const forms = await this.formService.findByIds(
        user.orgId,
        [body.formId],
        [team.id],
      );
      form = forms?.items?.[0];

      if (form?.teamId && form.teamId !== team.id) {
        this.logger.warn(
          `Form ${form.uid} does not belong to team ${team.uid}`,
        );
        throw new BadRequestException(
          `Form ${form.uid} does not belong to team ${team?.name || ""}`,
        );
      }
    }

    let updatedForm: Form | null = null;
    if (form) {
      const formData = await this.formValidations(
        user.orgId,
        body,
        form,
        body.teamId,
      );
      updatedForm = formData.form;
    }

    // Initially code begins here
    const dataPromises: any = [
      this.validateAndFetchStatus(
        body.statusId,
        user.orgId,
        team.parentTeam?.id ?? team.id,
        body.statusName,
      ),
      this.validateAndFetchPriority(
        body.priorityId,
        user.orgId,
        team.parentTeam?.id ?? team.id,
        body.priorityName,
      ),
      this.validateAndFetchSentiment(
        body.sentimentId,
        user.orgId,
        team.parentTeam?.id ?? team.id,
      ),
    ];

    // Get type for the ticket if provided otherwise fallback to default
    if (body.typeId) {
      dataPromises.push(this.validateAndFetchType(body.typeId, user.orgId));
    } else {
      dataPromises.push(new Promise((resolve) => resolve(null)));
    }

    // Fetch the account
    let account: Account | null;
    if (body.accountId) {
      account = await this.accountsService.findOneByAccountId(
        body.accountId,
        user.orgId,
      );

      if (!account) {
        throw new NotFoundException("Account not found!");
      }
    } else {
      const { domain } = extractEmailDetails(body.requestorEmail);
      account = await this.accountsService.findOneByPrimaryDomain(
        domain,
        user.orgId,
      );
    }

    let customerContact: CustomerContact | null = null;
    const { firstName, lastName } = extractNameFromEmail(body.requestorEmail);
    try {
      customerContact =
        await this.customerContactActionService.createCustomerContact(
          user,
          {
            accountIds: account ? [account.uid] : [],
            email: body.requestorEmail,
            firstName,
            lastName,
          },
          { returnIfExists: true },
        );
    } catch (error) {
      // BadRequestException is expected if the contact already exists
      if (!(error instanceof BadRequestException)) {
        throw error;
      }
    }

    // If the assigned agent ID is provided, fetch the agent
    let agent: User | null;
    if (body?.assignedAgentId) {
      agent = await this.usersService.findOneByPublicId(body.assignedAgentId);
      if (!agent) {
        throw new NotFoundException("Assigned agent not found!");
      }

      // Check if the assigned agent belongs to the team
      const teamMember = await this.teamsService.userBelongsToTeam(
        agent.id,
        team,
        user.orgId,
      );

      // If the assigned agent does not belong to the team, throw an error
      if (!teamMember) {
        throw new BadRequestException(
          "Assigned agent is not a member of this team!",
        );
      }
    }

    // Fetch the status and priority
    const [status, priority, sentiment, type] = await Promise.all(dataPromises);

    if (!status) {
      throw new BadRequestException(
        "Default status not set! Please set a default status.",
      );
    }

    if (!priority) {
      throw new BadRequestException(
        "Default priority not set! Please set a default priority.",
      );
    }

    if (!sentiment) {
      throw new BadRequestException(
        "Default sentiment not set! Please set a default sentiment.",
      );
    }

    // If the type ID is provided but the type is not found, throw an error
    if (body.typeId && !type) {
      throw new BadRequestException(
        "Default type not set! Please set a default type.",
      );
    }

    // Create custom field values
    let customFieldValues: CustomFieldValues[] | null = null;
    if (body.customFieldValues?.length) {
      customFieldValues = await this.createCustomFieldValues(
        body.customFieldValues,
        user.orgId,
      );
    }

    const savedTicket = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create the ticket
        const ticket = this.ticketRepository.create({
          title: body.title,
          description: body.description,
          accountId: account?.id,
          requestorEmail: body.requestorEmail,
          subTeamId: team.id,
          teamId: team.id,
          statusId: status.id,
          organizationId: user.orgId,
          sentiment: { id: sentiment.id },
          isPrivate: body.isPrivate,
          priorityId: priority.id,
          assignedAgentId: agent?.id,
          dueDate: body.dueDate,
          submitterEmail: body.submitterEmail ?? body.requestorEmail,
          typeId: type?.id,
          aiGeneratedTitle: body.aiGeneratedTitle || null,
          aiGeneratedSummary: body.aiGeneratedSummary || null,
          attachments: [], // Initialize empty attachments array
          customerContact: { id: customerContact.id },
          customFieldValues,
          formId: updatedForm?.id,
          metadata: body.metadata,
          source,
        });

        // Save the ticket first to get the ID
        const savedTicket = await this.ticketRepository.saveWithTxn(
          txnContext,
          ticket,
        );

        // Create the audit log
        const auditLog: DeepPartial<AuditLog> = {
          team: { id: team.id },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: savedTicket.id,
          entityUid: savedTicket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `A new ticket was created by <^user!:${user.uid}> in team <^team!:${team.uid}> with title: <^title!:${savedTicket.title}>`,
          description: `A new ticket was created by <^user!:${user.uid}> in team <^team!:${team.uid}> with title: <^title!:${savedTicket.title}>`,
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Process attachments if they exist
        if (body.attachmentUrls?.length > 0) {
          savedTicket.attachments =
            await this.storageService.attachFilesToEntity(
              body.attachmentUrls,
              user.orgId,
            );

          // Save the ticket with attachments
          return this.ticketRepository.saveWithTxn(txnContext, savedTicket);
        }

        // Publish the ticket to SNS
        await this.snsPublishQueue.add(
          QueueNames.TICKET_SNS_PUBLISHER,
          {
            ticket: savedTicket.uid,
            eventType: TicketEvents.CREATED,
            user: user,
            team: team,
            reqId: rTracer.id(),
          },
          {
            attempts: 3,
            backoff: {
              type: "exponential",
              delay: 1000, // 1 second
            },
          },
        );

        return savedTicket;
      },
    );

    // Create initial comment if comment-related fields are provided (for backward compatibility with Slack app)
    if (
      body.commentContent ||
      body.commentContentHtml ||
      body.commentContentJson
    ) {
      try {
        // Determine the author ID - use impersonated user if provided, otherwise use current user
        let authorId = user.sub;
        let customerId: string | undefined;

        // If impersonation fields are provided, try to find the user by email
        if (body.commentImpersonatedUserEmail) {
          const impersonatedUser = await this.usersService.findOneByEmail(
            body.commentImpersonatedUserEmail,
            user.orgId,
          );

          if (impersonatedUser) {
            authorId = impersonatedUser.id;
          } else {
            // If user not found, create or find customer contact
            const { firstName, lastName } = extractNameFromEmail(
              body.commentImpersonatedUserEmail,
            );
            const customerContact =
              await this.customerContactActionService.createCustomerContact(
                user,
                {
                  email: body.commentImpersonatedUserEmail,
                  firstName: body.commentImpersonatedUserName || firstName,
                  lastName: lastName,
                  avatarUrl: body.commentImpersonatedUserAvatar,
                },
                { returnIfExists: true },
              );
            customerId = customerContact.id;
          }
        }

        // Create the comment data
        const commentData: any = {
          content: body.commentContent || body.commentContentHtml || "",
          contentHtml: body.commentContentHtml,
          contentJson: body.commentContentJson,
          organizationId: user.orgId,
          authorId,
          customerId,
          ticketId: savedTicket.id,
          teamId: team.id,
          commentVisibility: CommentVisibility.PUBLIC,
          commentType: CommentType.COMMENT,
          metadata: body.commentMetadata || {},
          source: source,
        };

        // Create the comment
        await this.commentsActionService.createComment(commentData);

        this.logger.log(
          `Created initial comment for ticket ${savedTicket.uid}`,
        );
      } catch (commentError) {
        this.logger.error(
          `Failed to create initial comment for ticket ${savedTicket.uid}:`,
          commentError,
        );
        // Don't fail the ticket creation if comment creation fails
      }
    }

    // Fetch the returnable ticket
    const returnableTicket = await this.cachedTicketRepository.findByCondition({
      select: TicketSelect,
      relations: GetTicketsRelations,
      where: { id: savedTicket.id, organizationId: user.orgId },
    });

    // If routing is not disabled, route the ticket
    if (performRouting) {
      // Route this ticket
      this.routingEngine
        .executeRouting(returnableTicket, team, user)
        .catch((routingError) => {
          this.logger.error(
            `Failed to route ticket ${returnableTicket.uid}:`,
            routingError,
          );
        });
    }

    // Emit the ticket assigned event
    if (returnableTicket.assignedAgentId) {
      const ticketAssignedEvent =
        this.ticketsEventsFactory.createTicketAssignedEvent(
          savedTicket,
          user.orgId,
          team.uid,
        );

      this.eventEmitter.emit(
        EmittableTicketEvents.TICKET_ASSIGNED,
        ticketAssignedEvent,
      );
    }

    this.logger.log(`Successfully created ticket ${savedTicket.uid}`);
    return returnableTicket;
  }

  /**
   * Links two tickets.
   * @param request The request object.
   * @param body The ticket payload.
   * @returns The linked tickets.
   */
  public async linkTickets(
    user: CurrentUser,
    body: LinkTicketsBody,
  ): Promise<Ticket> {
    // Fetch the user and team
    const { sourceTicketId, linkedTicketId } = body;

    // Fetch the tickets
    const tickets = await this.cachedTicketRepository.findAll({
      where: {
        uid: In([sourceTicketId, linkedTicketId]),
        organizationId: user.orgId,
      },
    });

    // Find the source ticket
    const sourceTicket = tickets.find(
      (ticket) => ticket.uid === sourceTicketId,
    );

    if (!sourceTicket) {
      throw new NotFoundException("Source ticket not found!");
    }

    // Find the linked ticket
    const linkedTicket = tickets.find(
      (ticket) => ticket.uid === linkedTicketId,
    );

    if (!linkedTicket) {
      throw new NotFoundException("Linked ticket not found!");
    }

    // Check if the source and linked ticket belong to the same team
    if (sourceTicket.teamId !== linkedTicket.teamId) {
      throw new BadRequestException("Tickets must belong to the same team!");
    }

    const common = {
      relationshipType: TicketRelations.RELATED,
      organizationId: user.orgId,
    };

    // Check if the tickets are already linked
    const alreadyExistingRelationship =
      await this.cachedTicketRelationshipsRepository.findByCondition({
        where: {
          sourceTicketId: sourceTicket.id,
          targetTicketId: linkedTicket.id,
          ...common,
        },
      });

    if (alreadyExistingRelationship) {
      throw new BadRequestException("Tickets are already linked!");
    }

    // Create the relationship
    const ticketRelationships =
      this.cachedTicketRelationshipsRepository.createMany([
        {
          sourceTicketId: sourceTicket.id,
          targetTicketId: linkedTicket.id,
          ...common,
        },
        {
          sourceTicketId: linkedTicket.id,
          targetTicketId: sourceTicket.id,
          ...common,
        },
      ]);

    // Save the relationship
    await this.cachedTicketRelationshipsRepository.saveMany(
      ticketRelationships,
    );

    return sourceTicket;
  }

  /**
   * Marks a ticket as a duplicate.
   * @param request The request object.
   * @param body The ticket payload.
   * @returns The parent ticket.
   */
  public async markDuplicateTicket(
    user: CurrentUser,
    body: MarkDuplicateBody,
  ): Promise<Ticket> {
    const { duplicateOfTicketId, duplicateTicketId } = body;

    // Fetch the tickets
    const tickets = await this.cachedTicketRepository.findAll({
      where: {
        uid: In([duplicateOfTicketId, duplicateTicketId]),
        organizationId: user.orgId,
      },
    });

    // Find the parent ticket
    const duplicateOfTicket = tickets.find(
      (ticket) => ticket.uid === duplicateOfTicketId,
    );

    if (!duplicateOfTicket) {
      throw new NotFoundException("Parent ticket not found!");
    }

    // Find the duplicate ticket
    const duplicateTicket = tickets.find(
      (ticket) => ticket.uid === duplicateTicketId,
    );

    if (!duplicateTicket) {
      throw new NotFoundException("Duplicate ticket not found!");
    }

    // Check if the parent and duplicate ticket belong to the same team
    if (duplicateOfTicket.teamId !== duplicateTicket.teamId) {
      throw new BadRequestException(
        "Parent and duplicate ticket must belong to the same team!",
      );
    }

    // Check if the duplicate ticket is archived or deleted
    if (duplicateTicket.archivedAt || duplicateTicket.deletedAt) {
      throw new BadRequestException("Duplicate ticket is archived or deleted!");
    }

    // Check for circular reference
    const circularReference =
      await this.cachedTicketRelationshipsRepository.findByCondition({
        where: {
          sourceTicketId: duplicateTicket.id,
          targetTicketId: duplicateOfTicket.id,
          relationshipType: TicketRelations.DUPLICATE,
          organizationId: user.orgId,
        },
      });

    if (circularReference) {
      throw new BadRequestException("Circular reference detected!");
    }

    // Define the common body
    const commonBody = {
      sourceTicketId: duplicateOfTicket.id,
      targetTicketId: duplicateTicket.id,
      relationshipType: TicketRelations.DUPLICATE,
      organizationId: user.orgId,
    };

    // Check if the duplicate ticket is already marked as a duplicate
    const isAlreadyMarkedAsDuplicate =
      await this.cachedTicketRelationshipsRepository.findByCondition({
        where: commonBody,
      });

    if (isAlreadyMarkedAsDuplicate) {
      throw new BadRequestException(
        "Tickets are already marked as duplicates!",
      );
    }

    // Archive the duplicate ticket
    await this.archiveTicket(user, duplicateTicketId);

    // Create the relationship
    const ticketRelationship =
      this.cachedTicketRelationshipsRepository.create(commonBody);

    await this.cachedTicketRelationshipsRepository.save(ticketRelationship);

    return duplicateOfTicket;
  }

  /**
   * Marks a ticket as a sub-ticket or creates a new sub-ticket.
   * @param request The request object.
   * @param body The ticket payload.
   * @returns The created sub-ticket.
   */
  public async markOrCreateSubTicket(
    user: CurrentUser,
    body: MarkOrCreateSubTicketBody,
  ): Promise<Ticket> {
    // Fetch the user and team
    const { parentTicketId, subTicketId, ...rest } = body;

    // Fetch the ticket
    const ticketsToFetch = [parentTicketId];
    if (subTicketId) {
      ticketsToFetch.push(subTicketId);
    }

    const tickets = await this.cachedTicketRepository.findAll({
      where: { uid: In(ticketsToFetch), organizationId: user.orgId },
    });

    // Find the parent ticket
    const parentTicket = tickets.find(
      (ticket) => ticket.uid === parentTicketId,
    );

    if (!parentTicket) {
      throw new NotFoundException("Parent ticket not found!");
    }

    // Find the sub ticket
    let subTicket: Ticket | null;
    if (subTicketId) {
      const existingTicket = tickets.find(
        (ticket) => ticket.uid === subTicketId,
      );

      if (!existingTicket) {
        throw new NotFoundException("Ticket to mark as sub-ticket not found!");
      }

      // Check if the parent and sub-ticket belong to the same team
      if (parentTicket.teamId !== existingTicket.teamId) {
        throw new BadRequestException(
          "Parent and sub-ticket must belong to the same team!",
        );
      }

      // Check if the sub-ticket is archived or deleted
      if (existingTicket.archivedAt || existingTicket.deletedAt) {
        throw new BadRequestException("Sub-ticket is archived or deleted!");
      }

      const existingParentRelation =
        await this.ticketRelationshipsRepository.findByCondition({
          where: {
            targetTicketId: existingTicket.id,
            relationshipType: TicketRelations.CHILD,
            organizationId: user.orgId,
          },
        });

      if (existingParentRelation) {
        throw new BadRequestException("Ticket already has a parent ticket.");
      }

      // Check for circular parent-child relationship
      const circularRelation =
        await this.ticketRelationshipsRepository.findByCondition({
          where: {
            sourceTicketId: existingTicket.id,
            targetTicketId: parentTicket.id,
            relationshipType: TicketRelations.CHILD,
            organizationId: user.orgId,
          },
        });

      if (circularRelation) {
        throw new BadRequestException(
          "Circular parent-child relationship detected.",
        );
      }

      subTicket = existingTicket;
    } else {
      if (!rest.title) {
        throw new BadRequestException(
          "Title is required to create a new sub-ticket!",
        );
      }

      // Fetch the status and priority
      const { status, priority, type } = await this.fetchStatusPriorityAndType(
        body.statusId,
        body.priorityId,
        body?.typeId,
        user.orgId,
        parentTicket.teamId,
        true,
      );

      // If the type is provided and does not belong to the same team as the parent ticket, throw an error
      if (type && type?.teamId !== parentTicket.teamId) {
        throw new BadRequestException(
          "Ticket type must belong to the same team as the parent ticket!",
        );
      }

      // Create the sub ticket
      const newSubTicket = this.cachedTicketRepository.create({
        teamId: parentTicket.teamId,
        accountId: parentTicket?.accountId,
        organizationId: user.orgId,
        title: rest.title,
        description: rest?.description,
        priorityId: priority.id,
        requestorEmail: parentTicket.requestorEmail,
        statusId: status.id,
        typeId: type?.id,
      });

      // Save the sub ticket
      subTicket = await this.cachedTicketRepository.save(newSubTicket);
    }

    // Create the relationship
    const ticketRelationship = this.cachedTicketRelationshipsRepository.create({
      sourceTicketId: parentTicket.id,
      targetTicketId: subTicket.id,
      relationshipType: TicketRelations.CHILD,
      organizationId: user.orgId,
    });

    // Save the relationship
    await this.cachedTicketRelationshipsRepository.save(ticketRelationship);

    // Fetch the returnable ticket
    const returnableTicket = await this.ticketRepository.findByCondition({
      where: { id: subTicket.id, organizationId: user.orgId },
      relations: EAGER_TICKET_RELATIONS,
    });

    return returnableTicket;
  }

  /**
   * Updates a ticket.
   * @param request The request object.
   * @param id The ID of the ticket to update.
   * @param body The ticket payload.
   * @returns The updated ticket.
   */
  public async updateTicket(
    user: CurrentUser,
    id: string,
    body: UpdateTicketBody,
  ) {
    this.logger.log(`Updating ticket ${id} by user ${user.uid}`);

    if (isEmpty(body)) {
      this.logger.warn("Update ticket called with empty body");
      throw new BadRequestException("No fields provided to update!");
    }

    let oldTicket: Ticket | null = null;
    // Fetch the ticket
    const ticket = await this.cachedTicketRepository.findByCondition({
      where: { uid: id, organizationId: user.orgId },
      relations: EAGER_TICKET_RELATIONS,
    });

    oldTicket = cloneDeep(ticket);

    // If the ticket is not found, throw an error
    if (!ticket) {
      this.logger.warn(`Ticket ${id} not found for update`);
      throw new NotFoundException("Ticket not found!");
    }
    // Initialize the audit logs
    const auditLogs: Array<DeepPartial<AuditLog>> = [];
    const auditCommon = {
      team: { id: ticket.teamId },
      organization: { id: user.orgId },
      activityPerformedBy: { id: user.sub },
      entityId: ticket.id,
      entityUid: ticket.uid,
      entityType: AuditLogEntityType.TICKET,
      op: AuditLogOp.UPDATED,
      visibility: AuditLogVisibility.ORGANIZATION,
    };

    const updatedBody = this.buildFormValidationBodyFromTicket(ticket, body);

    // Validate and fetch the status
    let status: TicketStatus | null;
    if (body.statusId) {
      status = await this.validateAndFetchStatus(
        body.statusId,
        user.orgId,
        ticket.team?.id ?? ticket.subTeam?.id,
      );

      // If the status is not found, throw an error
      if (!status) {
        throw new NotFoundException("Provided status was not found!");
      }

      // Add the status ID to the update set
      ticket.statusId = status.id;
      ticket.status = status;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket status from <^status!:${oldTicket?.status?.uid}> to <^status!:${status.uid}>`,
        description: `Status for ticket ${ticket.ticketId} was updated from <^status!:${oldTicket?.status?.uid}> to <^status!:${status.uid}> by <^user!:${user.uid}>`,
      });
    }

    // If the sentiment ID is provided, fetch the sentiment
    let sentiment: TicketSentiment | null;
    if (body.sentimentId) {
      sentiment = await this.validateAndFetchSentiment(
        body.sentimentId,
        user.orgId,
        ticket.team?.id ?? ticket.subTeam?.id,
      );

      // If the sentiment is not found, throw an error
      if (!sentiment) {
        throw new NotFoundException("Provided sentiment was not found!");
      }

      ticket.sentiment = sentiment;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket sentiment from <^sentiment!:${oldTicket?.sentiment?.uid}> to <^sentiment!:${sentiment.uid}>`,
        description: `Sentiment for ticket ${ticket.ticketId} was updated from <^sentiment!:${oldTicket?.sentiment?.uid}> to <^sentiment!:${sentiment.uid}> by <^user!:${user.uid}>`,
      });
    }

    if (body?.formId) {
      const form = await this.formService.findOneById(
        body.formId,
        user.orgId,
        ticket.team?.id,
      );

      if (!form) {
        throw new BadRequestException("Form not found!");
      }

      if (form.teamId && form.teamId !== ticket?.team?.id) {
        throw new BadRequestException(
          "Form is not applicable for the provided team!",
        );
      }

      const isStatusClosed =
        ticket?.status?.parentStatus?.name?.toLowerCase() === "closed" ||
        ticket?.status?.name?.toLowerCase() === "closed";

      await this.formValidations(
        user.orgId,
        updatedBody,
        form,
        ticket.team?.uid,
        isStatusClosed,
      );

      ticket.form = form;
      ticket.formId = body.formId;
    }

    // Fetch the account
    let account: Account | null = null;
    if (body.accountId) {
      account = await this.accountsService.findOneByAccountId(
        body.accountId,
        user.orgId,
      );

      if (!account) {
        throw new NotFoundException("Account not found!");
      }
    }

    if (body.metadata) {
      ticket.metadata = mergeWith(ticket.metadata, body.metadata, (a, b) => {
        if (isArray(a)) {
          return a.concat(b);
        }
      });
    }

    // Check if the user can perform the action
    await this.canPerformAction(user, ticket, ticket.team, {
      read: true,
      write: true,
    });

    // Check if the sub-team exists
    if (body.subTeamId === "UNSET" || body.subTeamId === null) {
      ticket.subTeamId = null;
      ticket.subTeam = null;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> unassigned the ticket from <^team!:${oldTicket?.team?.uid}>`,
        description: `Team for ticket ${ticket.ticketId} was unassigned from <^team!:${oldTicket?.team?.uid}> by <^user!:${user.uid}>`,
      });
    } else if (body.subTeamId) {
      const subTeam = await this.teamsService.findOneByTeamId(
        body.subTeamId,
        user.orgId,
        ticket.team.id,
      );

      // If the sub-team is not found, throw an error
      if (!subTeam) {
        throw new NotFoundException("Provided sub-team was not found.");
      }

      ticket.subTeamId = subTeam.id;
      ticket.subTeam = subTeam;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket team from <^team!:${oldTicket?.team?.uid}> to <^team!:${subTeam.uid}>`,
        description: `Team for ticket ${ticket.ticketId} was updated from <^team!:${oldTicket?.team?.uid}> to <^team!:${subTeam.uid}> by <^user!:${user.uid}>`,
      });
    }

    if (body.title) {
      ticket.title = body.title;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket title from <^title!:${oldTicket?.title}> to <^title!:${body.title}>`,
        description: `Title for ticket ${ticket.ticketId} was updated from <^title!:${oldTicket?.title}> to <^title!:${body.title}> by <^user!:${user.uid}>`,
      });
    }

    if (body.description) {
      ticket.description = body.description;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket description from <^description!:${oldTicket?.description}> to <^description!:${body.description}>`,
        description: `Description for ticket ${ticket.ticketId} was updated from <^description!:${oldTicket?.description}> to <^description!:${body.description}> by <^user!:${user.uid}>`,
      });
    }

    if (body.accountId) {
      ticket.account = account;
      ticket.accountId = account.id;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket account from <^account!:${oldTicket?.account?.uid}> to <^account!:${account.uid}>`,
        description: `Account for ticket ${ticket.ticketId} was updated from <^account!:${oldTicket?.account?.uid}> to <^account!:${account.uid}> by <^user!:${user.uid}>`,
      });
    }

    if (body.dueDate) {
      ticket.dueDate = body.dueDate;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket due date from <^dueDate!:${oldTicket?.dueDate}> to <^dueDate!:${body.dueDate}>`,
        description: `Due date for ticket ${ticket.ticketId} was updated from <^dueDate!:${oldTicket?.dueDate}> to <^dueDate!:${body.dueDate}> by <^user!:${user.uid}>`,
      });
    }

    if (body.isPrivate) {
      ticket.isPrivate = body.isPrivate;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket privacy from <^isPrivate!:${oldTicket?.isPrivate}> to <^isPrivate!:${body.isPrivate}>`,
        description: `Privacy for ticket ${ticket.ticketId} was updated from <^isPrivate!:${oldTicket?.isPrivate}> to <^isPrivate!:${body.isPrivate}> by <^user!:${user.uid}>`,
      });
    }

    if (body.submitterEmail) {
      ticket.submitterEmail = body.submitterEmail;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket submitter email from <^submitterEmail!:${oldTicket?.submitterEmail}> to <^submitterEmail!:${body.submitterEmail}>`,
        description: `Submitter email for ticket ${ticket.ticketId} was updated from <^submitterEmail!:${oldTicket?.submitterEmail}> to <^submitterEmail!:${body.submitterEmail}> by <^user!:${user.uid}>`,
      });
    }

    if (body.aiGeneratedTitle) {
      ticket.aiGeneratedTitle = body.aiGeneratedTitle;

      // TODO: This probably is a bot what should we do here?
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket AI generated title from <^aiGeneratedTitle!:${oldTicket?.aiGeneratedTitle}> to <^aiGeneratedTitle!:${body.aiGeneratedTitle}>`,
        description: `AI generated title for ticket ${ticket.ticketId} was updated from <^aiGeneratedTitle!:${oldTicket?.aiGeneratedTitle}> to <^aiGeneratedTitle!:${body.aiGeneratedTitle}> by <^user!:${user.uid}>`,
      });
    }

    if (body.aiGeneratedSummary) {
      ticket.aiGeneratedSummary = body.aiGeneratedSummary;

      // TODO: This probably is a bot what should we do here?
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket AI generated summary from <^aiGeneratedSummary!:${oldTicket?.aiGeneratedSummary}> to <^aiGeneratedSummary!:${body.aiGeneratedSummary}>`,
        description: `AI generated summary for ticket ${ticket.ticketId} was updated from <^aiGeneratedSummary!:${oldTicket?.aiGeneratedSummary}> to <^aiGeneratedSummary!:${body.aiGeneratedSummary}> by <^user!:${user.uid}>`,
      });
    }

    // Validate and fetch the priority
    let priority: TicketPriority | null;
    if (body.priorityId) {
      priority = await this.validateAndFetchPriority(
        body.priorityId,
        user.orgId,
        ticket.team?.id ?? ticket.subTeam?.id,
      );

      // If the priority is not found, throw an error
      if (!priority) {
        throw new NotFoundException("Provided priority was not found!");
      }

      // Add the priority ID to the update set
      ticket.priorityId = priority.id;
      ticket.priority = priority;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket priority from <^priority!:${oldTicket?.priority?.uid}> to <^priority!:${priority.uid}>`,
        description: `Priority for ticket ${ticket.ticketId} was updated from <^priority!:${oldTicket?.priority?.uid}> to <^priority!:${priority.uid}> by <^user!:${user.uid}>`,
      });
    }

    // Get type for the ticket if provided otherwise fallback to default
    let type: TicketType | null;
    if (body.typeId) {
      type = await this.validateAndFetchType(body.typeId, user.orgId);

      // If the type is not found, throw an error
      if (!type) {
        throw new BadRequestException("Ticket type not found!");
      }

      // Add the type ID to the update set
      ticket.typeId = type.id;
      ticket.type = type;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> updated the ticket type from <^ticketType!:${oldTicket?.type?.uid}> to <^ticketType!:${type.uid}>`,
        description: `Type for ticket ${ticket.ticketId} was updated from <^ticketType!:${oldTicket?.type?.uid}> to <^ticketType!:${type.uid}> by <^user!:${user.uid}>`,
      });
    }

    let assignedAgent: User | null;
    if (body.assignedAgentId === "UNASSIGN") {
      // If the assigned agent ID is "UNASSIGN", set the assigned agent to null
      ticket.assignedAgentId = null;
      ticket.assignedAgent = null;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> unassigned the ticket from <^assignedAgent!:${oldTicket?.assignedAgent?.uid}>`,
        description: `Ticket ${ticket.ticketId} was unassigned from <^assignedAgent!:${oldTicket?.assignedAgent?.uid}> by <^user!:${user.uid}>`,
      });

      ticket.subTeamId = null;
      ticket.subTeam = null;
    } else if (body.assignedAgentId || body.assignedAgentEmail) {
      if (body.assignedAgentId) {
        assignedAgent = await this.usersService.findOneByPublicId(
          body.assignedAgentId,
        );
      } else if (body.assignedAgentEmail) {
        assignedAgent = await this.usersService.findOneByEmail(
          body.assignedAgentEmail,
          user.orgUid,
        );
      }

      // If the assigned agent is not found, throw an error
      if (!assignedAgent) {
        throw new NotFoundException("Assigned agent not found!");
      }

      // If the ticket is already assigned to the same user, throw an error
      if (ticket.assignedAgentId === assignedAgent.id) {
        throw new BadRequestException(
          "This ticket is already assigned to this user!",
        );
      }

      // If the assigned agent is a bot, throw an error
      if (
        assignedAgent.userType === UserType.BOT_USER &&
        assignedAgent.metadata?.isAgent === false // If this bot is NOT an AI Agent then we cannot assign
      ) {
        throw new BadRequestException(
          "Assigned agent is a bot and cannot be assigned to a ticket!",
        );
      }

      const teams = await this.teamsService.findAllTeamsByUser(
        assignedAgent,
        ticket?.team?.id,
      );

      if (teams.length === 1) {
        const team = await this.teamsService.findOneByTeamId(
          teams[0].uid,
          assignedAgent.organizationId,
        );
        ticket.subTeamId = team.id;
        ticket.subTeam = team;
      } else {
        ticket.subTeamId = null;
        ticket.subTeam = null;
      }

      // Check if the assigned agent belongs to the team
      const isAgentPartOfTeam = await this.teamsService.userBelongsToTeam(
        assignedAgent.id,
        ticket.subTeam ?? ticket.team,
        user.orgId,
      );

      // If the assigned agent does not belong to the team, throw an error
      if (!isAgentPartOfTeam) {
        throw new BadRequestException(
          "Assigned agent was either not a member of this team or not found!",
        );
      }

      // Add the assigned agent ID to the update set
      ticket.assignedAgentId = assignedAgent.id;
      ticket.assignedAgent = assignedAgent;

      // Add the audit log
      auditLogs.push({
        ...auditCommon,
        activity: `<^user!:${user.uid}> assigned the ticket to <^assignedAgent!:${assignedAgent.uid}>`,
        description: `Ticket ${ticket.ticketId} was assigned to <^assignedAgent!:${assignedAgent.uid}> by <^user!:${user.uid}>`,
      });
    }

    if (isArray(body.customFieldValues)) {
      const newCustomFieldValues: CustomFieldValues[] =
        await this.customFieldValuesService.createCustomFieldValues(
          body.customFieldValues,
          user.orgId,
        );

      const allowedPrevCustomFieldValues: CustomFieldValues[] = [];
      const existingCustomFieldValues: CustomFieldValues[] =
        ticket.customFieldValues ?? [];

      for (const customFieldValue of existingCustomFieldValues) {
        if (
          !newCustomFieldValues.find(
            (value) => value.customField.id === customFieldValue.customField.id,
          )
        ) {
          allowedPrevCustomFieldValues.push(customFieldValue);
        }
      }

      ticket.customFieldValues = [
        ...allowedPrevCustomFieldValues,
        ...newCustomFieldValues,
      ];
    }

    // Update the ticket
    const updatedTicket = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Record the audit logs
        await this.activitiesService.recordBulkAuditLog(auditLogs, txnContext);

        // Update the ticket
        const updatedTicket = await this.ticketRepository.saveWithTxn(
          txnContext,
          { ...ticket },
        );

        // Publish the ticket to SNS - BULL MQ
        await this.snsPublishQueue.add(
          QueueNames.TICKET_SNS_PUBLISHER,
          {
            ticket: updatedTicket.uid,
            eventType: TicketEvents.UPDATED,
            user: user,
            team: updatedTicket.team,
            previousTicket: oldTicket,
            reqId: rTracer.id(),
          },
          {
            attempts: 3,
            backoff: {
              type: "exponential",
              delay: 1000, // 1 second
            },
          },
        );

        return updatedTicket;
      },
    );

    if ("subTeamId" in body && body.subTeamId !== null) {
      const parentTeam = await this.teamsService.findOneByTeamId(
        updatedTicket.team?.uid,
        user.orgId,
      );
      const subTeam = await this.teamsService.findOneByTeamId(
        updatedTicket.subTeam?.uid,
        user.orgId,
      );

      // Re-route this ticket
      this.routingEngine
        .executeRerouting(updatedTicket, parentTeam, subTeam, user, false)
        .catch((routingError) => {
          this.logger.error(
            `Failed to re-route ticket ${updatedTicket.uid}:`,
            routingError,
          );
        });
    }

    // Invalidate cache for the ticket
    await this.cachedTicketRepository.invalidateTicketCacheWithKey({
      where: { uid: id, organizationId: user.orgId },
      relations: EAGER_TICKET_RELATIONS,
    });

    // Fetch the returnable ticket
    const returnableTicket = await this.getTicketById(id, user.orgId);

    try {
      const { results } = await this.commentRepository.fetchPaginatedResults(
        { limit: 1, page: 0 },
        {
          where: { ticketId: returnableTicket.id, organizationId: user.orgId },
          order: { createdAt: "ASC" },
        },
      );

      // If there are results, find and execute the emoji action inverse
      if (results.length > 0) {
        // Update here
        this.reactionsActionService.findAndExecuteEmojiActionInverse(
          results[0],
          user,
          status,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error encountered while finding and executing emoji action inverse: ${error.message}`,
        error?.stack,
      );
    }

    this.logger.log(`Successfully updated ticket ${id}`);
    return returnableTicket;
  }

  /**
   * Escalates a ticket.
   * @param user The user.
   * @param id The ID of the ticket to escalate.
   * @returns The escalated ticket.
   */
  public async escalateTicket(
    user: CurrentUser,
    id: string,
    body: EscalateTicketBody,
    metadata?: Record<string, any>,
  ) {
    // Fetch the ticket
    const ticket = await this.getTicketById(id, user.orgId);
    if (!ticket) {
      throw new NotFoundException("Ticket not found!");
    }

    // Check if the user belongs to the team
    const userInTeam = await this.teamsService.userBelongsToTeam(
      user.sub,
      ticket.team,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!userInTeam) {
      throw new ForbiddenException(
        "You are not authorized to escalate this ticket!",
      );
    }

    // If the ticket is already escalated, throw an error
    if (ticket.isEscalated) {
      throw new BadRequestException("Ticket already escalated!");
    }

    // Update the ticket in a transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Update the ticket
      const ticketMetadata = metadata
        ? mergeWith(ticket.metadata, metadata, (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          })
        : ticket.metadata;

      ticketMetadata.escalation_details = {
        escalated_by: { userId: user.sub },
        reason: body.reason,
        escalation_time: Date.now(),
        details: body.details,
        impact: body.impact,
      };

      await this.ticketRepository.updateWithTxn(
        txnContext,
        { id: ticket.id },
        {
          isEscalated: true,
          metadata: ticketMetadata,
        },
      );

      // Create the audit log
      const auditLog: DeepPartial<AuditLog> = {
        team: { id: ticket.teamId },
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.UPDATED,
        visibility: AuditLogVisibility.ORGANIZATION,
        activity: `Ticket <^ticket!:${ticket.uid}> was escalated by <^user!:${user.uid}>!`,
        description: `Ticket <^ticket!:${ticket.uid}> was escalated by <^user!:${user.uid}>! With reason: <^reason!:${body.reason}>`,
      };

      // Record the audit log
      await this.activitiesService.recordAuditLog(auditLog, txnContext);

      // Publish the ticket to SNS - BULL MQ
      await this.snsPublishQueue.add(
        QueueNames.TICKET_SNS_PUBLISHER,
        {
          ticket: ticket.uid,
          eventType: TicketEvents.ESCALATED,
          user: user,
          team: ticket.team,
          reqId: rTracer.id(),
        },
        {
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 1000, // 1 second
          },
        },
      );
    });

    // Fetch the returnable ticket
    const returnableTicket = await this.getTicketById(ticket.uid, user.orgId);
    return returnableTicket;
  }

  /**
   * Assigns a ticket to an agent.
   * @param request The request object.
   * @param id The ID of the ticket to assign.
   * @param body The ticket payload.
   * @returns The updated ticket.
   */
  public async assignTicket(
    user: CurrentUser,
    id: string,
    body: AssignTicketBody,
    query: AssignTicketQuery,
    metadata?: Record<string, any>,
  ) {
    // Assign the ticket
    const staleTicket = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Fetch the ticket
        const ticket = await this.cachedTicketRepository.findByCondition({
          where: { uid: id, organizationId: user.orgId },
          relations: EAGER_TICKET_RELATIONS,
        });

        // If the ticket is not found, throw an error
        if (!ticket) {
          throw new NotFoundException("Ticket not found!");
        }

        const invalidationQuery = {
          where: { uid: id, organizationId: user.orgId },
          relations: EAGER_TICKET_RELATIONS,
        };

        const ticketMetadata = metadata
          ? mergeWith(ticket.metadata, metadata, (a, b) => {
              if (isArray(a)) {
                return a.concat(b);
              }
            })
          : ticket.metadata;

        // If the unassign query is provided, unassign the ticket
        if (query.unassign) {
          await this.ticketRepository.updateWithTxn(
            txnContext,
            { id: ticket.id },
            { assignedAgentId: null, metadata: ticketMetadata },
          );

          await this.cachedTicketRepository.invalidateTicketCacheWithKey(
            invalidationQuery,
          );

          return ticket;
        }

        // If the assigned agent ID is provided, fetch the agent
        const agent = await this.usersService.findOneByPublicId(
          body.assignedAgentId,
        );

        // If the agent is not found, throw an error
        if (!agent) {
          throw new NotFoundException("Assigned agent not found!");
        }

        // Check if the assigned agent belongs to the team
        const teamMember = await this.teamsService.userBelongsToTeam(
          agent.id,
          ticket.teamId,
          user.orgId,
        );

        // If the assigned agent does not belong to the team, throw an error
        if (!teamMember) {
          throw new BadRequestException(
            "Assigned agent is not a member of this team!",
          );
        }

        // If the ticket is already assigned to the agent, return the ticket
        if (ticket.assignedAgentId === agent.id) {
          throw new BadRequestException(
            "This ticket is already assigned to this user!",
          );
        }

        // Create the audit log
        const auditLog: DeepPartial<AuditLog> = {
          team: { id: ticket.teamId },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: ticket.id,
          entityUid: ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Ticket ${ticket.ticketId} was assigned to ${agent.email}`,
          description: `Ticket ${ticket.ticketId} was assigned to ${agent.email} by ${user.email}`,
          metadata: {
            updatedFields: [
              {
                field: "assignedAgentId",
                previousValue: ticket.assignedAgentId,
                updatedToValue: agent.id,
              },
            ],
          },
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Update the ticket
        await this.ticketRepository.updateWithTxn(
          txnContext,
          { id: ticket.id },
          { assignedAgentId: agent.id, metadata: ticketMetadata },
        );

        // Invalidate cache for the ticket
        await this.cachedTicketRepository.invalidateTicketCacheWithKey(
          invalidationQuery,
        );

        // Publish the ticket to SNS - BULL MQ
        await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
          ticket: ticket.uid,
          eventType: TicketEvents.ASSIGNED,
          user: user,
          team: ticket.team,
          previousTicket: [],
          reqId: rTracer.id(),
        });
        return ticket;
      },
    );

    // Fetch the returnable ticket
    const returnableTicket = await this.getTicketById(
      staleTicket.uid,
      user.orgId,
    );

    // Emit event for ticket assigned or unassigned
    if (query.unassign) {
      const ticketUnassignedEvent =
        this.ticketsEventsFactory.createTicketUnassignedEvent(
          staleTicket,
          user.orgId,
          staleTicket.team.uid,
        );

      // Emit the event
      this.eventEmitter.emit(
        EmittableTicketEvents.TICKET_UNASSIGNED,
        ticketUnassignedEvent,
      );
    } else {
      const ticketAssignedEvent =
        this.ticketsEventsFactory.createTicketAssignedEvent(
          returnableTicket,
          user.orgId,
          returnableTicket.team.uid,
        );

      // Emit the event
      this.eventEmitter.emit(
        EmittableTicketEvents.TICKET_ASSIGNED,
        ticketAssignedEvent,
      );
    }

    return returnableTicket;
  }

  /**
   * Reassigns a ticket to a team.
   * @param request The request object.
   * @param id The ID of the ticket to assign.
   * @param body The ticket payload.
   * @returns The updated ticket.
   */
  public async reassignTeamToTicket(user: CurrentUser, team: Team, id: string) {
    const updatedTicket = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Fetch the ticket
        const ticket = await this.cachedTicketRepository.findByCondition({
          where: { uid: id, organizationId: user.orgId },
        });

        const previousTicket = cloneDeep(ticket);

        // If the ticket is not found, throw an error
        if (!ticket) {
          throw new NotFoundException("Ticket not found!");
        }

        // If the ticket is assigned to an agent, unassign it
        if (ticket.assignedAgentId) {
          const ticketUnassignedEvent =
            this.ticketsEventsFactory.createTicketUnassignedEvent(
              ticket,
              user.orgId,
              team.uid,
            );

          this.eventEmitter.emit(
            EmittableTicketEvents.TICKET_UNASSIGNED,
            ticketUnassignedEvent,
          );
        }

        // Update the ticket
        const updatedTicket = this.ticketRepository.saveWithTxn(txnContext, {
          ...ticket,
          teamId: team.id,
          assignedAgentId: null,
        });

        // Create the audit log
        const auditLog: DeepPartial<AuditLog> = {
          team: { id: ticket.teamId },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: ticket.id,
          entityUid: ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Ticket ${ticket.ticketId} was reassigned to team ${team.name}`,
          description: `Ticket ${ticket.ticketId} was reassigned to team ${team.name} by ${user.email}`,
          metadata: {
            updatedFields: [
              {
                field: "teamId",
                previousValue: ticket.teamId,
                updatedToValue: team.id,
              },
            ],
          },
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Invalidate the ticket cache
        await this.cachedTicketRepository.invalidateTicketCacheWithKey({
          where: { uid: id, organizationId: user.orgId },
          relations: EAGER_TICKET_RELATIONS,
        });

        // Publish the ticket to SNS - BULL MQ
        await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
          ticket: ticket.uid,
          eventType: TicketEvents.UPDATED,
          user: user,
          team: ticket.team,
          previousTicket,
          reqId: rTracer.id(),
        });

        return updatedTicket;
      },
    );

    // Fetch the returnable ticket
    const returnableTicket = await this.getTicketById(
      updatedTicket.uid,
      user.orgId,
    );

    return returnableTicket;
  }

  /**
   * Deletes a ticket.
   * @param request The request object.
   * @param id The ID of the ticket to delete.
   * @returns The deleted ticket.
   */
  public async deleteTicket(user: CurrentUser, id: string) {
    this.logger.log(`Attempting to delete ticket ${id} by user ${user.uid}`);

    const ticket = await this.cachedTicketRepository.findByCondition({
      where: { uid: id, organizationId: user.orgId },
    });

    if (!ticket) {
      this.logger.warn(`Ticket ${id} not found for deletion`);
      throw new NotFoundException("Ticket not found!");
    }

    if (ticket.deletedAt) {
      this.logger.warn(`Attempt to delete already deleted ticket ${id}`);
      throw new BadRequestException("Ticket already deleted!");
    }

    // Check if the user belongs to the team
    const userBelongsToTeam = await this.teamsService.userBelongsToTeam(
      user.sub,
      ticket.teamId,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam) {
      this.logger.warn(
        `User ${user.uid} not authorized to delete ticket ${id}`,
      );
      throw new ForbiddenException(
        "You are not authorized to delete this ticket!",
      );
    }

    const deletedTicket = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create audit log
        const auditLog: DeepPartial<AuditLog> = {
          team: { id: ticket.teamId },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityId: ticket.id,
          entityUid: ticket.uid,
          entityType: AuditLogEntityType.TICKET,
          op: AuditLogOp.DELETED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `Ticket <^ticket!:${ticket.uid}> was deleted by <^user!:${user.uid}>`,
          description: `Ticket <^ticket!:${ticket.uid}> was deleted by <^user!:${user.uid}>`,
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Get full ticket data before deletion
        const fullTicketData = await this.ticketRepository.findByCondition({
          where: { uid: ticket.uid },
          relations: EAGER_TICKET_RELATIONS,
        });

        // Soft delete the ticket
        const deleted = await this.ticketRepository.softDeleteWithTxn(
          txnContext,
          {
            id: ticket.id,
            uid: ticket.uid,
            organizationId: ticket.organizationId,
          },
        );

        // Publish to SNS with full ticket data
        await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
          ticket: fullTicketData.uid,
          eventType: TicketEvents.DELETED,
          user: user,
          team: ticket.team,
          previousTicket: fullTicketData,
          reqId: rTracer.id(),
        });

        // Invalidate the ticket cache
        await this.cachedTicketRepository.invalidateTicketCacheWithKey({
          where: { uid: id, organizationId: user.orgId },
          relations: EAGER_TICKET_RELATIONS,
        });

        return deleted;
      },
    );

    this.logger.log(`Successfully deleted ticket ${id}`);
    return deletedTicket;
  }

  /**
   * Unarchives a ticket.
   * @param request The request object.
   * @param id The ID of the ticket to unarchive.
   * @returns The unarchived ticket.
   */
  public async unArchiveTicket(user: CurrentUser, id: string) {
    this.logger.log(`Attempting to unarchive ticket ${id} by user ${user.uid}`);

    // Fetch the ticket
    const ticket = await this.cachedTicketRepository.findByCondition({
      where: { uid: id, organizationId: user.orgId },
    });

    if (!ticket) {
      this.logger.warn(`Ticket ${id} not found for unarchiving`);
      throw new NotFoundException("Ticket not found!");
    }

    if (!ticket.archivedAt) {
      this.logger.warn(`Attempt to unarchive already unarchived ticket ${id}`);
      throw new BadRequestException("Ticket already unarchived!");
    }

    // Check if the user belongs to the team
    const userBelongsToTeam = await this.teamsService.userBelongsToTeam(
      user.sub,
      ticket.teamId,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam) {
      this.logger.warn(
        `User ${user.uid} not authorized to unarchive ticket ${id}`,
      );
      throw new ForbiddenException(
        "You are not authorized to unarchive this ticket!",
      );
    }

    // Archive the ticket
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Archive the ticket
      const updatedTicket = await this.ticketRepository.updateWithTxn(
        txnContext,
        { id: ticket.id },
        { archivedAt: null },
      );

      await this.cachedTicketRepository.invalidateTicketCacheWithKey({
        where: { uid: id, organizationId: user.orgId },
        relations: EAGER_TICKET_RELATIONS,
      });

      const auditLog: DeepPartial<AuditLog> = {
        team: { id: ticket.teamId },
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.UPDATED,
        activity: `Ticket <^ticket!:${ticket.uid}> was unarchived by <^user!:${user.uid}>`,
        description: `Ticket <^ticket!:${ticket.uid}> was unarchived by <^user!:${user.uid}>`,
        visibility: AuditLogVisibility.ORGANIZATION,
      };

      // Record the audit log
      await this.activitiesService.recordAuditLog(auditLog, txnContext);

      // Publish the ticket to SNS - BULL MQ
      await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
        ticket: ticket.uid,
        eventType: TicketEvents.UPDATED,
        user: user,
        team: ticket.team,
        reqId: rTracer.id(),
      });

      return updatedTicket;
    });

    const returnableTicket = await this.getTicketById(ticket.uid, user.orgId);
    this.logger.log(`Successfully unarchived ticket ${id}`);
    return returnableTicket;
  }

  /**
   * Archives a ticket.
   * @param request The request object.
   * @param id The ID of the ticket to archive.
   * @returns The archived ticket.
   */
  public async archiveTicket(
    user: CurrentUser,
    id: string,
    metadata?: Record<string, any>,
  ) {
    this.logger.log(`Attempting to archive ticket ${id} by user ${user.uid}`);

    // Fetch the ticket
    const ticket = await this.cachedTicketRepository.findByCondition({
      where: { uid: id, organizationId: user.orgId },
    });

    if (!ticket) {
      this.logger.warn(`Ticket ${id} not found for archiving`);
      throw new NotFoundException("Ticket not found!");
    }

    if (ticket.archivedAt) {
      this.logger.warn(`Attempt to archive already archived ticket ${id}`);
      throw new BadRequestException("Ticket already archived!");
    }

    // Check if the user belongs to the team
    const userBelongsToTeam = await this.teamsService.userBelongsToTeam(
      user.sub,
      ticket.teamId,
      user.orgId,
    );

    // If the user does not belong to the team, throw an error
    if (!userBelongsToTeam) {
      this.logger.warn(
        `User ${user.uid} not authorized to archive ticket ${id}`,
      );
      throw new ForbiddenException(
        "You are not authorized to archive this ticket!",
      );
    }

    // Archive the ticket
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Archive the ticket

      const ticketMetadata = metadata
        ? mergeWith(ticket.metadata, metadata, (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          })
        : ticket.metadata;

      const updatedTicket = await this.ticketRepository.updateWithTxn(
        txnContext,
        { id: ticket.id },
        { archivedAt: new Date(), metadata: ticketMetadata },
      );

      await this.cachedTicketRepository.invalidateTicketCacheWithKey({
        where: { uid: id, organizationId: user.orgId },
        relations: EAGER_TICKET_RELATIONS,
      });

      const auditLog: DeepPartial<AuditLog> = {
        team: { id: ticket.teamId },
        organization: { id: user.orgId },
        activityPerformedBy: { id: user.sub },
        entityId: ticket.id,
        entityUid: ticket.uid,
        entityType: AuditLogEntityType.TICKET,
        op: AuditLogOp.ARCHIVED,
        activity: `Ticket <^ticket!:${ticket.uid}> was archived by <^user!:${user.uid}>`,
        description: `Ticket <^ticket!:${ticket.uid}> was archived by <^user!:${user.uid}>`,
        visibility: AuditLogVisibility.ORGANIZATION,
      };

      // Record the audit log
      await this.activitiesService.recordAuditLog(auditLog, txnContext);

      // Publish the ticket to SNS - BULL MQ
      await this.snsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
        ticket: ticket.uid,
        eventType: TicketEvents.ARCHIVED,
        user: user,
        team: ticket.team,
        reqId: rTracer.id(),
      });

      return updatedTicket;
    });

    const returnableTicket = await this.getTicketById(ticket.uid, user.orgId);
    this.logger.log(`Successfully archived ticket ${id}`);
    return returnableTicket;
  }

  /**
   * Gets the time logs for a ticket
   * @param user The current user
   * @param id The ID of the ticket
   * @returns The time logs for the ticket
   */
  public async getTimeLogsForTicket(user: CurrentUser, id: string) {
    const ticket = await this.getTicketById(id, user.orgId);
    if (!ticket) {
      throw new NotFoundException("Ticket not found!");
    }

    return this.ticketTimeLogRepository.findAll({
      where: { ticketId: ticket.id, organizationId: user.orgId },
      relations: ["user", "ticket"],
    });
  }

  /**
   * Logs time for a ticket
   * @param user The current user
   * @param id The ID of the ticket
   * @param body The time log body
   * @returns The logged ticket
   */
  public async logTimeForTicket(
    user: CurrentUser,
    id: string,
    body: TicketTimeLogDto,
  ) {
    const ticket = await this.getTicketById(id, user.orgId);
    if (!ticket) {
      throw new NotFoundException("Ticket not found!");
    }

    // Record the time log
    const recordedTimeLog = await this.ticketTimeLogRepository.recordTimeLog({
      ticketId: ticket.id,
      userId: user.sub,
      description: body.description,
      timeSpentMinutes: body.timeSpentMinutes,
      organizationId: user.orgId,
    });

    // Fetch the logged time log
    const loggedTimeLog = await this.ticketTimeLogRepository.findByCondition({
      where: { id: recordedTimeLog.id },
      relations: ["user", "ticket"],
    });

    return loggedTimeLog;
  }

  async formValidations(
    orgId: string,
    body: CreateTicketBody | UpdateTicketBody,
    form: Form,
    teamId: string,
    isTicketClosing?: boolean,
  ) {
    if (!form) {
      throw new BadRequestException("Form is required!");
    }

    const thenaRestrictedFields =
      await this.thenaRestrictedFieldsService.getAllFields();

    const thenaRestrictedIdToKeyMap =
      this.formsValidatorService.getThenaRestrictedFieldIdToTicketKeyMap(
        thenaRestrictedFields,
      );

    // Build the form field values structure from the ticket
    const formFieldValues =
      this.formsValidatorService.buildFieldValuesStructure(
        body.customFieldValues,
      );

    if (form.conditions?.length) {
      this.formsValidatorService.applyConditions(
        form,
        body,
        formFieldValues,
        thenaRestrictedIdToKeyMap,
        teamId,
      );
    }

    await Promise.all([
      this.formsValidatorService.validateThenaRestrictedFieldValues(
        form,
        thenaRestrictedFields,
        body,
      ),
      this.formsValidatorService.validateFormValues(
        orgId,
        form,
        formFieldValues,
        isTicketClosing,
      ),
    ]);
    return { form };
  }

  buildFormValidationBodyFromTicket(
    ticket: Ticket,
    body: CreateTicketBody | UpdateTicketBody,
  ) {
    const updatedTicketBody: CreateTicketBody = {
      requestorEmail: ticket.requestorEmail,
      title: body.title || ticket.title,
      description: body.description || ticket.description,
      statusId: body.statusId || ticket.status?.uid,
      priorityId: body.priorityId || ticket.priority?.uid,
      assignedAgentId: body.assignedAgentId || ticket.assignedAgent?.uid,
      accountId: ticket.account?.uid,
      teamId: ticket.team?.uid, // @karan Update this to support sub-teams if required
      dueDate: body.dueDate || ticket.dueDate,
      submitterEmail: body.submitterEmail || ticket.submitterEmail,
      typeId: body.typeId || ticket.type?.uid,
      isPrivate: body.isPrivate || ticket.isPrivate,
      attachmentUrls:
        body.attachmentUrls ||
        ticket.attachments?.map((attachment) => attachment.url),
      customFieldValues: this.buildCustomFieldValuesFromTicket(ticket, body),
      // metadata: body.metadata || ticket.metadata,
      // metadata: {
      //   sla_details: body.metadata?.sla_details,
      // },
    };
    return updatedTicketBody;
  }

  buildCustomFieldValuesFromTicket(
    ticket: Ticket,
    body: CreateTicketBody | UpdateTicketBody,
  ) {
    const customFieldValues: ExternalCustomFieldValuesDto[] = [];
    const updatedCustomFieldIds = [];
    for (const customField of body.customFieldValues || []) {
      customFieldValues.push({
        customFieldId: customField.customFieldId,
        data: customField.data,
      });
      updatedCustomFieldIds.push(customField.customFieldId);
    }

    for (const customField of ticket.customFieldValues || []) {
      if (!updatedCustomFieldIds.includes(customField.customField.uid)) {
        customFieldValues.push({
          customFieldId: customField.customField.uid,
          data: customField.data,
        });
      }
    }
    return customFieldValues;
  }
}
