import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import { RequestSource } from "@repo/nestjs-commons/middlewares";
import {
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Draft,
  DraftRepository,
  DraftScope,
  DraftStatus,
  DraftType,
  Team,
  TransactionService,
  UserType,
} from "@repo/thena-platform-entities";
import { FindManyOptions, FindOptionsWhere, In, IsNull, Not } from "typeorm";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { UsersService } from "../../users/services/users.service";
import { GetDraftTicketQuery } from "../dto/queries.dto";
import {
  CreateDraftTicketDto,
  DraftTicketResponseDto,
  PaginatedResponseDto,
  UpdateDraftTicketDto,
} from "../interfaces/draft.ticket.interface";
import { TicketValidationService } from "./ticket-validation.service";
import { TicketsService } from "./tickets.service";

@Injectable()
export class TicketDraftService {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,
    private readonly draftRepository: DraftRepository,
    private readonly ticketValidationService: TicketValidationService,
    private readonly transactionService: TransactionService,
    private readonly activitiesService: ActivitiesService,
    private readonly usersService: UsersService,
    private readonly ticketService: TicketsService,
  ) {}

  /**
   * Creates a new draft ticket
   *
   * Creation Rules:
   * - Initially created with IN_PROGRESS status
   * - Content is validated before creation
   * - Activity is recorded after creation
   * - Draft scope (personal/team) is set during creation
   *
   * Content Requirements:
   * - Must contain all required ticket fields
   * - Fields are validated using ticketValidationService
   * - Metadata is optional
   *
   * Access Control:
   * - Users can create drafts in their own organization
   * - Draft is automatically assigned to creator
   * - Organization context is preserved
   *
   * @param draftTicketData - Data for the draft ticket
   *   - title: Ticket title
   *   - description?: Ticket description
   *   - requestorEmail: Email of the requestor
   *   - statusId?: Ticket status ID
   *   - priorityId?: Ticket priority ID
   *   - typeId?: Ticket type ID
   *   - assignedAgentId?: Assigned agent ID
   *   - metadata?: Additional metadata
   * @param user - Current user creating the draft
   *   - user.sub: User ID
   *   - user.orgId: Organization ID
   * @param team - Team context for the draft
   *   - team.id: Team ID
   * @param draftScope - Scope of the draft (PERSONAL/TEAM)
   *
   * @throws {BadRequestException} When:
   *   - Validation fails
   *   - Required fields are missing
   * @throws {ForbiddenException} When:
   *   - User doesn't belong to the team
   *   - Organization mismatch
   *
   * @returns {Promise<DraftTicketResponseDto>} Created draft ticket
   */
  async createDraftTicket(
    draftTicketData: CreateDraftTicketDto,
    user: CurrentUser,
    team: Team,
    draftScope: DraftScope,
  ): Promise<DraftTicketResponseDto> {
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        const validatedDraft: CreateDraftTicketDto =
          await this.ticketValidationService.validateTicketFields(
            draftTicketData,
            user,
            team,
          );

        const draft = this.draftRepository.create({
          entityType: DraftType.TICKET,
          draftScope,
          status: DraftStatus.IN_PROGRESS,
          content: validatedDraft,
          metadata: draftTicketData.metadata || {},
          organizationId: user.orgId,
          createdBy: user.sub,
        });

        const savedDraft = await this.draftRepository.saveWithTxn(
          txnContext,
          draft,
        );

        await this.activitiesService.recordAuditLog({
          team: { id: team.id },
          organization: { id: user.orgId },
          activityPerformedBy: { id: user.sub },
          entityType: AuditLogEntityType.DRAFTS,
          entityId: savedDraft.id,
          entityUid: savedDraft.uid,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          activity: `A draft was created with id: ${savedDraft.id}`,
          description: `A draft of type ${savedDraft.entityType} with scope ${savedDraft.draftScope} was created by ${user.email}!`,
        });
        const modifiedDraft = {
          ...savedDraft,
          createdBy: user.uid,
        } as Draft;

        return this.ticketValidationService.draftResponseFormatter(
          modifiedDraft,
        );
      },
    );
  }

  /**
   * Retrieves a paginated list of draft tickets based on user permissions
   *
   * Access Control Rules:
   * - Admins can view:
   *   - All team-scoped drafts
   *   - Their own personal drafts
   *   - Cannot view other users' personal drafts
   * - Regular users can view:
   *   - Their own personal drafts
   *   - Their own team-scoped drafts
   *
   * Filtering Rules:
   * - Excludes published and discarded drafts
   * - Orders by last updated date (newest first)
   * - Supports pagination
   *
   * @param user - Current user requesting drafts
   *   - user.sub: User ID
   *   - user.orgId: Organization ID
   *   - user.userType: Type of user (admin/regular)
   * @param query - Query parameters
   *   - page: Page number (0-based)
   *   - limit: Number of items per page
   *
   * @throws {ForbiddenException} When:
   *   - User not found
   *   - User tries to access unauthorized drafts
   *
   * @returns {Promise<PaginatedResponseDto<DraftTicketResponseDto>>} Paginated list of drafts
   *   - items: Array of formatted draft tickets
   *   - meta: Pagination metadata
   *     - totalItems: Total number of items
   *     - currentPage: Current page number
   *     - itemsPerPage: Items per page
   */
  async getDraftTickets(
    user: CurrentUser,
    query: GetDraftTicketQuery,
  ): Promise<PaginatedResponseDto<DraftTicketResponseDto>> {
    const { orgId, sub } = user;
    const { page, limit } = query;

    const userDetails = await this.usersService.findOne(sub);
    if (!userDetails) {
      throw new ForbiddenException("User not found");
    }

    const baseQuery: FindManyOptions<Draft> = {
      where: {
        organizationId: orgId,
        entityType: DraftType.TICKET,
        status: Not(In([DraftStatus.PUBLISHED, DraftStatus.DISCARDED])),
      },
      order: { updatedAt: "DESC" },
      relations: ["createdByUser", "lastModifiedByUser", "organization"],
    };

    let whereClause: FindOptionsWhere<Draft> | FindOptionsWhere<Draft>[];

    if (userDetails.userType === UserType.ORG_ADMIN) {
      // Org admin can see both team drafts and their own personal drafts
      whereClause = [
        {
          ...baseQuery.where,
          draftScope: DraftScope.TEAM,
        },
        {
          ...baseQuery.where,
          draftScope: DraftScope.PERSONAL,
          createdBy: sub,
        },
      ];
    } else {
      // Regular users can only see their own personal drafts
      whereClause = {
        ...baseQuery.where,
        draftScope: DraftScope.PERSONAL,
        createdBy: sub,
      };
    }

    const finalQuery: FindManyOptions<Draft> = {
      ...baseQuery,
      where: whereClause,
    };

    this.logger.log(`Final query: ${JSON.stringify(finalQuery)}`);

    const { results } = await this.draftRepository.fetchPaginatedResults(
      {
        page: page ?? 0,
        limit: limit ?? 50,
      },
      finalQuery,
    );
    const paginatedResponse = {
      items: results.map((draft) => ({
        ...draft,
        createdBy: draft.createdByUser?.uid || draft.createdBy,
      })),
      meta: {
        totalItems: results.length,
        currentPage: page ?? 0,
        itemsPerPage: limit ?? 50,
      },
    };
    const formattedResponse =
      this.ticketValidationService.formatPaginatedDraftResponse(
        paginatedResponse,
      );
    return formattedResponse;
  }

  /**
   * Retrieves a specific draft ticket with comprehensive access control
   *
   * Access Control Rules:
   * - Admins can access:
   *   - Any team-scoped drafts
   *   - Their own personal drafts
   * - Regular users can only access:
   *   - Their own drafts
   *
   * @param uid - Unique identifier of the draft ticket
   * @param user - Current user making the request
   *   - user.sub: User ID
   *   - user.userType: Type of user (admin/regular)
   *   - user.orgId: Organization ID
   *
   * @throws {NotFoundException} When draft is not found
   * @throws {ForbiddenException} When user doesn't have access permission
   * @throws {BadRequestException} When uid is invalid
   *
   * @returns {Promise<DraftTicketResponseDto>} Formatted draft ticket response
   */
  async getDraftTicketByUid(
    uid: string,
    user: CurrentUser,
  ): Promise<DraftTicketResponseDto> {
    const draft = await this.draftRepository.findByCondition({
      where: {
        uid,
        entityType: DraftType.TICKET,
        organizationId: user.orgId,
        deletedAt: IsNull(),
      },
      relations: ["createdByUser", "lastModifiedByUser"],
    });

    if (!draft) {
      this.logger.debug(`Draft not found with uid: ${uid}`);
      throw new NotFoundException("Draft ticket not found");
    }

    // Access control logic
    const isAdmin = user.userType === UserType.ORG_ADMIN;
    const isCreator = draft.createdBy === user.sub;
    const isPersonalDraft = draft.draftScope === DraftScope.PERSONAL;

    const hasAccess =
      isCreator || // Creator always has access
      (!isPersonalDraft && isAdmin); // Admin has access to non-personal drafts

    if (!hasAccess) {
      this.logger.warn(
        `Access denied to draft ${uid} for user ${user.sub}. ` +
          `Admin: ${isAdmin}, Creator: ${isCreator}, Personal: ${isPersonalDraft}`,
      );
      throw new ForbiddenException("Access denied to draft");
    }
    try {
      return this.ticketValidationService.draftResponseFormatter(draft);
    } catch (error) {
      this.logger.error(
        `Error formatting draft response for ${uid}: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException("Error formatting draft response");
    }
  }

  /**
   * Updates a draft ticket with comprehensive access control and validation
   *
   * Access Control Rules:
   * - Admins can update:
   *   - Any team-scoped drafts
   *   - Their own personal drafts
   * - Regular users can only update:
   *   - Their own drafts (both personal and team-scoped)
   *
   * Update Rules:
   * - Original content is preserved before update
   * - Cannot update published drafts
   * - Cannot update discarded drafts
   * - Status changes are validated
   * - Team/organization context is preserved
   *
   * @param uid - Unique identifier of the draft ticket
   * @param draftTicketData - Data to update the draft with
   * @param user - Current user making the request
   *   - user.sub: User ID
   *   - user.userType: Type of user (admin/regular)
   *   - user.orgId: Organization ID
   *
   * @throws {NotFoundException} When draft is not found
   * @throws {ForbiddenException} When user doesn't have access permission
   * @throws {BadRequestException} When:
   *   - Draft is published/discarded
   *   - Invalid status transition
   *   - Required fields are missing
   *   - Invalid data format
   *
   * @returns {Promise<DraftTicketResponseDto>} Updated draft ticket
   */
  async updateDraftTicket(
    uid: string,
    draftTicketData: UpdateDraftTicketDto,
    user: CurrentUser,
  ): Promise<DraftTicketResponseDto> {
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        try {
          const draft = await this.draftRepository.findByCondition({
            where: {
              uid,
              entityType: DraftType.TICKET,
              organizationId: user.orgId,
              deletedAt: IsNull(),
            },
          });

          if (!draft) {
            this.logger.debug(`Draft not found with uid: ${uid}`);
            throw new NotFoundException("Draft ticket not found");
          }

          // Status validation
          if (draft.status === DraftStatus.PUBLISHED) {
            throw new BadRequestException("Cannot update published draft");
          }

          if (draft.status === DraftStatus.DISCARDED) {
            throw new BadRequestException("Cannot update discarded draft");
          }

          // Access control logic
          const isAdmin = user.userType === UserType.ORG_ADMIN;
          const isCreator = draft.createdBy === user.sub;
          const isPersonalDraft = draft.draftScope === DraftScope.PERSONAL;

          const hasAccess =
            isCreator || // Creator always has access
            (!isPersonalDraft && isAdmin); // Admin has access to non-personal drafts

          if (!hasAccess) {
            this.logger.warn(
              `Update access denied to draft ${uid} for user ${user.sub}. ` +
                `Admin: ${isAdmin}, Creator: ${isCreator}, Personal: ${isPersonalDraft}`,
            );
            throw new ForbiddenException("Access denied to update draft");
          }

          // Validate status transition if status is being updated
          if (draftTicketData.status) {
            this.ticketValidationService.validateStatusTransition(
              draft.status,
              draftTicketData.status,
            );
          }

          // Update content and metadata
          Object.assign(draft, {
            originalContent: draft.content,
            content: { ...draft.content, ...draftTicketData },
            metadata: { ...draft.metadata, ...draftTicketData.metadata },
            status: draftTicketData.status || draft.status,
            lastModifiedBy: user.sub,
          });

          const updatedDraft = await this.draftRepository.saveWithTxn(
            txnContext,
            draft,
          );

          const modifiedDraft = {
            ...updatedDraft,
            createdBy: user.uid,
          } as Draft;

          return this.ticketValidationService.draftResponseFormatter(
            modifiedDraft,
          );
        } catch (error) {
          this.logger.error(`Error updating draft ticket: ${error.message}`);
          throw new BadRequestException(
            `Error updating draft: ${error.message}`,
          );
        }
      },
    );
  }

  /**
   * Deletes (soft-delete) a draft ticket with scope-based access control
   *
   * Access Control Rules:
   * - Admins can delete:
   *   - Any team-scoped drafts
   *   - Their own personal drafts
   * - Regular users can only delete:
   *   - Their own drafts (both personal and team-scoped)
   *
   * Deletion Rules:
   * - Soft deletes the draft (sets deletedAt)
   * - Changes status to DISCARDED
   * - Cannot delete already published drafts
   * - Cannot delete already discarded drafts
   *
   * @param uid - Unique identifier of the draft ticket to delete
   * @param user - Current user attempting the deletion
   *   - user.sub: User ID
   *   - user.userType: Type of user (admin/regular)
   *   - user.orgId: Organization ID
   *
   * @throws {NotFoundException} When draft is not found
   * @throws {ForbiddenException} When user doesn't have delete permission
   * @throws {BadRequestException} When:
   *   - Draft is already published
   *   - Draft is already discarded
   *
   * @returns {Promise<Draft>} The deleted (discarded) draft
   */
  async deleteDraftTicket(uid: string, user: CurrentUser): Promise<Draft> {
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        const draft = await this.draftRepository.findByCondition({
          where: {
            uid,
            entityType: DraftType.TICKET,
            deletedAt: IsNull(),
            organizationId: user.orgId,
          },
          relations: ["createdByUser", "lastModifiedByUser"],
        });

        if (!draft) {
          throw new NotFoundException("Draft ticket not found");
        }

        // Access control logic
        const isAdmin = user.userType === UserType.ORG_ADMIN;
        const isCreator = draft.createdBy === user.sub;
        const isPersonalDraft = draft.draftScope === DraftScope.PERSONAL;

        if (
          (!isAdmin && !isCreator) || // Non-admin can only delete their own drafts
          (isAdmin && isPersonalDraft && !isCreator) // Admin can't delete others' personal drafts
        ) {
          this.logger.warn(
            `Delete access denied to draft ${uid} for user ${user.sub}. ` +
              `Admin: ${isAdmin}, Creator: ${isCreator}, Personal: ${isPersonalDraft}`,
          );
          throw new ForbiddenException("Access denied to delete this draft");
        }

        // Status checks
        if (draft.status === DraftStatus.DISCARDED) {
          throw new BadRequestException("Draft is already discarded");
        }

        if (draft.status === DraftStatus.PUBLISHED) {
          throw new BadRequestException("Cannot delete published draft");
        }

        draft.deletedAt = new Date();
        draft.status = DraftStatus.DISCARDED;
        const deletedDraft = await this.draftRepository.saveWithTxn(
          txnContext,
          draft,
        );
        return deletedDraft;
      },
    );
  }

  /**
   * Publishes a draft ticket by creating an actual ticket
   *
   * Access Control Rules:
   * - Only the creator can publish their own drafts
   * - Admins can publish team-scoped drafts
   * - No one can publish others' personal drafts
   *
   * Publishing Rules:
   * - Draft must be in READY_TO_PUBLISH status
   * - Cannot publish already published drafts
   * - Cannot publish discarded drafts
   * - All required ticket fields must be valid
   * - Creates a new ticket and marks draft as published
   *
   * @param uid - Unique identifier of the draft ticket
   * @param user - Current user attempting to publish
   *   - user.sub: User ID
   *   - user.userType: Type of user (admin/regular)
   *   - user.orgId: Organization ID
   *
   * @throws {NotFoundException} When draft is not found
   * @throws {ForbiddenException} When:
   *   - User doesn't have publish permission
   *   - Trying to publish others' personal drafts
   * @throws {BadRequestException} When:
   *   - Draft is not in READY_TO_PUBLISH status
   *   - Draft is already published
   *   - Draft is incomplete
   *   - Validation fails
   *
   * @returns {Promise<DraftTicketResponseDto>} The published draft
   */
  async publishDraftTicket(
    uid: string,
    user: CurrentUser,
  ): Promise<DraftTicketResponseDto> {
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Get the raw draft entity instead of the formatted response
        const draft = await this.draftRepository.findByCondition({
          where: {
            uid,
            entityType: DraftType.TICKET,
            deletedAt: IsNull(),
          },
          relations: ["createdByUser", "lastModifiedByUser"],
        });

        if (!draft) {
          throw new NotFoundException("Draft ticket not found");
        }

        // Status validation
        if (draft.status === DraftStatus.PUBLISHED) {
          throw new BadRequestException("Draft is already published");
        }

        if (draft.status === DraftStatus.DISCARDED) {
          throw new BadRequestException("Cannot publish discarded draft");
        }

        if (draft.status !== DraftStatus.READY_TO_PUBLISH) {
          throw new BadRequestException(
            "Draft must be in READY_TO_PUBLISH status before publishing",
          );
        }

        // Access control logic
        const isAdmin = user.userType === UserType.ORG_ADMIN;
        const isCreator = draft.createdBy === user.sub;
        const isPersonalDraft = draft.draftScope === DraftScope.PERSONAL;

        if (
          (!isAdmin && !isCreator) || // Non-admin can only publish their own drafts
          (isAdmin && isPersonalDraft && !isCreator) // Admin can't publish others' personal drafts
        ) {
          this.logger.warn(
            `Publish access denied to draft ${uid} for user ${user.sub}. ` +
              `Admin: ${isAdmin}, Creator: ${isCreator}, Personal: ${isPersonalDraft}`,
          );
          throw new ForbiddenException("Access denied to publish this draft");
        }

        // Validate draft completeness
        if (!this.ticketValidationService.isValidTicketDraft(draft)) {
          this.logger.error(`Draft is incomplete: ${draft.uid}`);
          throw new BadRequestException("Draft is incomplete");
        }

        try {
          //Create new ticket body and call the service
          const team = await this.ticketValidationService.validateTeam(
            draft.content.teamId,
            user.orgId,
          );

          const ticketBody =
            this.ticketValidationService.createTicketFromDraft(draft);
          const savedTicket = await this.ticketService.createTicket(
            user,
            team,
            ticketBody,
            RequestSource.WEB,
          );

          // Update draft status
          draft.status = DraftStatus.PUBLISHED;
          await this.draftRepository.saveWithTxn(txnContext, draft);

          // Record activity
          await this.activitiesService.recordAuditLog({
            team: { id: team.id },
            organization: { id: user.orgId },
            activityPerformedBy: { id: user.sub },
            entityType: AuditLogEntityType.DRAFTS,
            entityId: draft.id,
            entityUid: draft.uid,
            op: AuditLogOp.UPDATED,
            visibility: AuditLogVisibility.ORGANIZATION,
            activity: `A ticket with id: ${savedTicket.id} was published from draft with id: ${draft.id}`,
            description: `A ticket with id: ${savedTicket.id} was published from draft with id: ${draft.id} by ${user.email}!`,
            metadata: {
              updatedFields: [
                {
                  field: "status",
                  previousValue: DraftStatus.READY_TO_PUBLISH,
                  updatedToValue: DraftStatus.PUBLISHED,
                },
              ],
            },
          });

          const modifiedDraft = {
            ...draft,
            createdBy: user.uid,
          } as Draft;

          return this.ticketValidationService.draftResponseFormatter(
            modifiedDraft,
          );
          // return savedTicket;
        } catch (error) {
          this.logger.error(`Failed to publish draft ticket: ${error.message}`);
          throw new BadRequestException(
            `Failed to publish draft: ${error.message}`,
          );
        }
      },
    );
  }
}
