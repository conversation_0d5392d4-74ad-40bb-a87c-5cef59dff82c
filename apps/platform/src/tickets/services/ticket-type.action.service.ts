import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import {
  CachedTicketTypeRepository,
  TicketTypeRepository,
  User,
} from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { In } from "typeorm";
import { TeamsService } from "../../teams/services/teams.service";
import { UsersService } from "../../users/services/users.service";
import {
  CreateTicketTypeDto,
  UpdateTicketTypeDto,
} from "../dto/ticket-type.dto";

@Injectable()
export class TicketTypeActionService {
  constructor(
    // Injected cached repositories
    private cachedTicketTypeRepository: CachedTicketTypeRepository,

    // Injected repositories
    private ticketTypeRepository: TicketTypeRepository,
    private usersService: UsersService,
    private teamsService: TeamsService,
  ) {}

  /**
   * Finds ticket types by their public IDs.
   * @param typeIds The public IDs of the ticket types to find.
   * @param organizationId The ID of the organization to find the ticket types in.
   * @returns The ticket types.
   */
  findTicketTypesByPublicIds(typeIds: Array<string>, organizationId: string) {
    return this.ticketTypeRepository.findAll({
      where: { uid: In(typeIds), organizationId },
    });
  }

  findTicketTypeByPublicId(publicId: string, orgId: string) {
    return this.cachedTicketTypeRepository.findByCondition({
      where: { uid: publicId, organizationId: orgId },
    });
  }

  /**
   * Extracts the user from the request.
   * @returns The user.
   */
  private async extractUserFromRequest(request: FastifyRequest): Promise<User> {
    const userEmail = request.user.email;
    if (!userEmail) {
      throw new UnauthorizedException("User is not authenticated!");
    }

    const user = await this.usersService.findOneByEmail(userEmail);
    if (!user) {
      throw new UnauthorizedException("User is not authenticated!");
    }

    return user;
  }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );
    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  /**
   * Finds all ticket types for the organization.
   * @returns All ticket types for the organization.
   */
  async findAllTicketTypes(user: { orgId: string }, teamId?: string) {
    // Build the where clause
    const whereClause: Record<string, string> = {
      organizationId: user.orgId,
    };

    // If the team ID is provided, filter by team ID
    if (teamId?.trim()) {
      const team = await this.validateAndFetchTeam(teamId, user.orgId);
      whereClause.teamId = team.id;

      // If the team has a parent team then we'll use the parent team's statuses
      if (team.parentTeamId) {
        whereClause.teamId = team.parentTeamId;
      }
    }

    // Find all ticket statuses for the organization
    const ticketTypes = await this.ticketTypeRepository.findAll({
      where: whereClause,
      relations: ["organization", "team"],
    });

    return ticketTypes;
  }

  /**
   * Finds a ticket type by its ID.
   * @param ticketTypeId The ID of the ticket type to find.
   * @returns The ticket type.
   */
  async findTicketTypeById(ticketTypeId: string, request: FastifyRequest) {
    // Get the organization ID from the user object attached to the request
    const user = await this.extractUserFromRequest(request);

    const ticketType = await this.cachedTicketTypeRepository.findByCondition({
      where: { uid: ticketTypeId, organizationId: user.organizationId },
      relations: ["organization", "team"],
    });

    if (!ticketType) {
      throw new NotFoundException("Ticket type not found!");
    }

    return ticketType;
  }

  /**
   * Creates a new ticket status.
   * @param createTicketTypeDto The ticket type data to create.
   * @returns The created ticket type.
   */
  async createTicketType(
    createTicketTypeDto: CreateTicketTypeDto,
    request: FastifyRequest,
  ) {
    // Get the user from the request
    const user = await this.extractUserFromRequest(request);

    // Validate and fetch the team
    const team = await this.validateAndFetchTeam(
      createTicketTypeDto.teamId,
      user.organizationId,
    );

    // If the team has a parent team then we'll throw since child teams cannot have custom types
    if (team.parentTeamId) {
      throw new BadRequestException("You cannot create types for a sub-teams!");
    }

    // Get the organization ID from the user object
    const orgId = user.organizationId;

    // Create the ticket type
    const newTicketType = this.cachedTicketTypeRepository.create({
      name: createTicketTypeDto.name,
      color: createTicketTypeDto.color,
      icon: createTicketTypeDto.icon,
      autoAssign: createTicketTypeDto.autoAssign,
      isActive: createTicketTypeDto.isActive,
      organizationId: orgId,
      teamId: team.id,
    });

    // Save the new ticket type
    const ticketType = await this.cachedTicketTypeRepository.save(
      newTicketType,
    );

    return ticketType;
  }

  /**
   * Updates a ticket type.
   * @param ticketTypeId The ID of the ticket type to update.
   * @param updateTicketTypeDto The ticket type data to update.
   * @returns The updated ticket type.
   */
  async updateTicketType(
    ticketTypeId: string,
    updateTicketTypeDto: UpdateTicketTypeDto,
    request: FastifyRequest,
  ) {
    // Get the user email from the request
    const user = await this.extractUserFromRequest(request);

    // Find the ticket status by its ID
    const ticketType = await this.cachedTicketTypeRepository.findByCondition({
      where: { uid: ticketTypeId, organizationId: user.organizationId },
    });

    if (!ticketType) {
      throw new NotFoundException("Ticket type not found!");
    }

    // Update the ticket type
    const updatedTicketType = await this.cachedTicketTypeRepository.save({
      ...ticketType,
      ...updateTicketTypeDto,
    });

    return updatedTicketType;
  }

  /**
   * Deletes a ticket type.
   * @param ticketTypeId The ID of the ticket type to delete.
   * @returns The deleted ticket type.
   */
  async deleteTicketType(ticketTypeId: string, request: FastifyRequest) {
    // Get the user from the request
    const user = await this.extractUserFromRequest(request);

    // Find the ticket status by its ID
    const ticketType = await this.cachedTicketTypeRepository.findByCondition({
      where: { uid: ticketTypeId, organizationId: user.organizationId },
    });

    if (!ticketType) {
      throw new NotFoundException("Ticket type not found!");
    }

    // Delete the ticket type
    await this.cachedTicketTypeRepository.remove(ticketType);
  }
}
