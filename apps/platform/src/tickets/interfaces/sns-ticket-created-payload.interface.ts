interface Actor {
  id: string;
  type: string;
  email: string;
}

interface Customer {
  id: string;
  email: string;
  name: string;
}

interface Agent {
  id: string;
  email: string;
  name: string;
}

interface TicketPayload {
  id: string;
  ticketId: string;
  title: string;
  description: string;
  customerContactFirstName: string;
  customerContactLastName: string;
  customerContactEmail: string;
  priorityId: string;
  statusId: string;
  statusName: string;
  priorityName: string;
  sentimentId: string;
  sentimentName: string;
  source: string;
  createdAt: Date;
  teamId: string;
  teamName: string;
  teamIdentifier: string;
  subTeamId?: string;
  subTeamName?: string;
  subTeamIdentifier?: string;
  isEscalated: boolean;
  customer: Customer;
  assignedTo: string | null;
  assignedAgent: Agent | null;
  tags?: string[];
  isArchived: boolean;
  customFields?: Record<string, string>;
  metadata?: Record<string, string>;
  aiGeneratedTitle?: string;
  aiGeneratedSummary?: string;
}

interface Payload {
  ticket: TicketPayload;
  previousTicket?: TicketPayload;
}

export interface SnsTicketCreatedPayload {
  eventId: string;
  eventType: string;
  timestamp: string;
  orgId: string;
  teamId: string;
  actor: Actor;
  payload: Payload;
}
