import { <PERSON>ada<PERSON> } from "@grpc/grpc-js";
import {
  BadRequestException,
  Controller,
  Inject,
  NotFoundException,
  UseGuards,
} from "@nestjs/common";
import { GrpcMethod, RpcException } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import { ILogger } from "@repo/nestjs-commons/logger";
import { RequestSource } from "@repo/nestjs-commons/middlewares";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { tickets } from "@repo/shared-proto";
import { Ticket } from "@repo/thena-platform-entities";
import { TeamsService } from "../../../teams/services/teams.service";
import { CreateTicketBody, UpdateTicketBody } from "../../../tickets/dto";
import { UsersService } from "../../../users/services/users.service";
import { TicketAnnotatorService } from "../../services/ticket-annotator.service";
import { TicketsService } from "../../services/tickets.service";
import { createTicketValidator, updateTicketValidator } from "./validators";

@Controller("v1/tickets")
@UseGuards(GrpcAuthGuard)
export class TicketsGrpcController {
  constructor(
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly ticketsService: TicketsService,
    private readonly userService: UsersService,
    private readonly teamsService: TeamsService,
    private readonly ticketAnnotatorService: TicketAnnotatorService,
  ) {}

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, "AssignTicket")
  async assignTicket(data: tickets.AssignTicketRequest, metadata: Metadata) {
    try {
      const { ticketId, agentId } = data || {};

      // Validate the request
      if (!ticketId || !agentId) {
        throw new RpcException("Invalid request!");
      }

      // Fetch the user
      const currentUser = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      // Assign the ticket
      const ticket = await this.ticketsService.assignTicket(
        currentUser,
        ticketId,
        { assignedAgentId: agentId },
        { unassign: false },
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : {},
      );

      const ticketResponse: tickets.CommonTicketResponse =
        this.formatTicketResponse(ticket);

      const response: tickets.AssignTicketResponse = {
        success: true,
        ticket: ticketResponse,
      };

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, "CreateTicket")
  async createTicket(data: tickets.CreateTicketRequest, metadata: Metadata) {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      // Validate the request
      const parsedData = createTicketValidator.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException("Invalid request!");
      }

      // Fetch the team
      const team = await this.validateAndFetchTeam(
        parsedData.data.teamId,
        user.orgId,
      );

      const payload = {
        ...parsedData.data,
        dueDate: parsedData.data.dueDate
          ? new Date(parsedData.data.dueDate)
          : undefined,
        metadata: workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : {},
      } as CreateTicketBody;

      // Create the ticket
      // TODO: @amit make sure source is not comming from body rather than metadata
      const ticket = await this.ticketsService.createTicket(
        user,
        team,
        payload,
        (payload.source as RequestSource) ?? RequestSource.UNKNOWN,
      );

      // Format the response
      const response = this.formatTicketResponse(ticket);

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, "UpdateTicket")
  async updateTicket(data: tickets.UpdateTicketRequest, metadata: Metadata) {
    try {
      this.logger.log("Updating ticket", JSON.stringify(data));
      // Extract the user
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      // If the ticket ID is not provided, throw an error
      if (!data.id) {
        throw new BadRequestException("Ticket ID is required");
      }

      // Validate the request
      const parsedData = updateTicketValidator.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException("Invalid request!");
      }

      this.logger.log(
        "Ticket update api: Parsed data",
        JSON.stringify(parsedData.data),
      );

      const payload = {
        ...parsedData.data,
        dueDate: parsedData.data.dueDate
          ? new Date(parsedData.data.dueDate)
          : undefined,
        metadata: workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : {},
      } as UpdateTicketBody;

      // Update the ticket
      const updatedTicket = await this.ticketsService.updateTicket(
        user,
        data.id,
        payload,
      );

      // Format the response
      const response: tickets.CommonTicketResponse =
        this.formatTicketResponse(updatedTicket);

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, "GetTicket")
  async getTicket(data: tickets.GetTicketRequest, metadata: Metadata) {
    try {
      // Extract the user
      const user = extractUserMetadata(metadata);

      // If the ticket ID is not provided, throw an error
      if (!data.id) {
        throw new BadRequestException("Ticket ID is required!");
      }

      // Fetch the ticket
      const ticket = await this.ticketsService.getTicketById(
        data.id,
        user.orgId,
      );

      // If the ticket is not found, throw an error
      if (!ticket) {
        throw new NotFoundException("Ticket not found!");
      }

      // Format the response
      const response = this.formatTicketResponse(ticket);

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, "GetTicketsWithCursor")
  async getTicketsWithCursor(
    data: tickets.GetTicketsWithCursorRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user
      const user = extractUserMetadata(metadata);

      // Fetch the tickets
      const { data: tickets, cursor } =
        await this.ticketsService.getTicketsWithCursor(user, {
          limit: data.limit,
          afterCursor: data.afterCursor,
        });

      // Format the response
      const response: tickets.GetTicketsWithCursorResponse = {
        tickets: tickets.map((ticket) => this.formatTicketResponse(ticket)),
        cursor: cursor.afterCursor,
      };

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, "ArchiveTicket")
  async archiveTicket(data: tickets.ArchiveTicketRequest, metadata: Metadata) {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      // If the ticket ID is not provided, throw an error
      if (!data.id) {
        throw new BadRequestException("Ticket ID is required!");
      }

      // Archive the ticket
      const archivedTicket = await this.ticketsService.archiveTicket(
        user,
        data.id,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : {},
      );

      // Format the response
      const response = this.formatTicketResponse(archivedTicket);

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKETS_SERVICE_NAME, "EscalateTicket")
  async escalateTicket(
    data: tickets.EscalateTicketRequest,
    metadata: Metadata,
  ) {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      // If the ticket ID is not provided, throw an error
      if (!data.id) {
        throw new BadRequestException("Ticket ID is required!");
      }

      // Escalate the ticket
      const escalatedTicket = await this.ticketsService.escalateTicket(
        user,
        data.id,
        {
          reason: data.reason,
          details: data.details,
          impact: data.impact,
        },
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : {},
      );

      // Format the response
      const response = this.formatTicketResponse(escalatedTicket);

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  // Field metadata apis ========================================================================================
  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, "GetTicketFieldMetadata")
  getTicketFieldMetadata(): tickets.GetTicketFieldMetadataResponse {
    try {
      return this.ticketAnnotatorService.getTicketFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    tickets.TICKET_ANNOTATOR_SERVICE_NAME,
    "GetTicketStatusFieldMetadata",
  )
  async getTicketStatusFieldMetadata(
    data: tickets.GetTicketStatusFieldMetadataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketStatusFieldMetadataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketStatusFieldMetadata(
        data?.teamId,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    tickets.TICKET_ANNOTATOR_SERVICE_NAME,
    "GetTicketTypeFieldMetadata",
  )
  async getTicketTypeFieldMetadata(
    data: tickets.GetTicketTypeFieldMetadataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketTypeFieldMetadataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketTypeFieldMetadata(
        data?.teamId,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    tickets.TICKET_ANNOTATOR_SERVICE_NAME,
    "GetTicketPriorityFieldMetadata",
  )
  async getTicketPriorityFieldMetadata(
    data: tickets.GetTicketPriorityFieldMetadataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketPriorityFieldMetadataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketPriorityFieldMetadata(
        data?.teamId,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    tickets.TICKET_ANNOTATOR_SERVICE_NAME,
    "GetTicketSentimentFieldMetadata",
  )
  async getTicketSentimentFieldMetadata(
    request: tickets.GetTicketSentimentFieldMetadataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketSentimentFieldMetadataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketSentimentFieldMetadata(
        request.teamId,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, "GetTicketData")
  async getTicketData(
    request: tickets.GetTicketDataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketData(
        request.ticketId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, "FilterTicketData")
  async filterTicketData(
    request: tickets.FilterTicketDataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.filterTicketDataWithRelations(
        JSON.parse(request.conditions),
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, "GetTicketStatusData")
  async getTicketStatusData(
    request: tickets.GetTicketStatusDataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketStatusDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketStatusData(
        request.statusId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, "GetTicketTypeData")
  async getTicketTypeData(
    request: tickets.GetTicketTypeDataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketTypeDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketTypeData(
        request.typeId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, "GetTicketPriorityData")
  async getTicketPriorityData(
    request: tickets.GetTicketPriorityDataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketPriorityDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketPriorityData(
        request.priorityId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(tickets.TICKET_ANNOTATOR_SERVICE_NAME, "GetTicketSentimentData")
  async getTicketSentimentData(
    request: tickets.GetTicketSentimentDataRequest,
    metadata: Metadata,
  ): Promise<tickets.GetTicketSentimentDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.ticketAnnotatorService.getTicketSentimentData(
        request.sentimentId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  formatTicketResponse(ticket: Ticket) {
    const response: tickets.CommonTicketResponse = {
      id: ticket.uid,
      organizationId: ticket.organization.uid,
      title: ticket.title,
      ticketId: ticket.ticketId,
      description: ticket.description,
      accountId: ticket.account?.uid,
      account: ticket.account?.name,
      teamId: ticket.team.uid,
      teamName: ticket.team.name,
      isPrivate: ticket.isPrivate,
      requestorEmail: ticket.requestorEmail,
      status: ticket.status.name,
      statusId: ticket.status.uid,
      priority: ticket.priority.name,
      priorityId: ticket.priority.uid,
      type: ticket.type?.name,
      typeId: ticket.type?.uid,
      assignedAgent: ticket.assignedAgent?.name,
      assignedAgentId: ticket.assignedAgent?.uid,
      submitterEmail: ticket.submitterEmail,
      deletedAt: ticket.deletedAt
        ? new Date(ticket.deletedAt).toISOString()
        : undefined,
      archivedAt: ticket.archivedAt
        ? new Date(ticket.archivedAt).toISOString()
        : undefined,
      createdAt: ticket.createdAt
        ? new Date(ticket.createdAt).toISOString()
        : undefined,
      updatedAt: ticket.updatedAt
        ? new Date(ticket.updatedAt).toISOString()
        : undefined,
      aiGeneratedTitle: ticket.aiGeneratedTitle,
      aiGeneratedSummary: ticket.aiGeneratedSummary,
      customFieldValues: ticket.customFieldValues.map((cfv) => ({
        customFieldId: cfv.customField.uid,
        data: cfv.data,
        metadata: cfv.metadata ? JSON.stringify(cfv.metadata) : undefined,
      })),
    };

    return response;
  }
}
