import { z } from "zod";

export const createTicketValidator = z.object({
  title: z.string(),
  requestorEmail: z.string().email(),
  teamId: z.string(),
  accountId: z.string().optional(),
  assignedAgentId: z.string().optional(),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  submitterEmail: z.string().optional(),
  statusId: z.string().optional(),
  priorityId: z.string().optional(),
  typeId: z.string().optional(),
  isPrivate: z.boolean().optional(),
  attachmentUrls: z.array(z.string()).optional(),
  source: z.string().optional(),
  commentContent: z.string().optional(),
  commentContentHtml: z.string().optional(),
  commentContentJson: z.string().optional(),
  commentAttachmentIds: z.array(z.string()).optional(),
  commentMetadata: z.string().optional(),
  commentImpersonatedUserName: z.string().optional(),
  commentImpersonatedUserEmail: z.string().optional(),
  commentImpersonatedUserAvatar: z.string().optional(),
});

export const updateTicketValidator = z.object({
  id: z.string(),
  title: z.string().optional(),
  subTeamId: z.string().optional(),
  accountId: z.string().optional(),
  assignedAgentId: z.string().optional(),
  assignedAgentEmail: z.string().optional(),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  statusId: z.string().optional(),
  priorityId: z.string().optional(),
  typeId: z.string().optional(),
  submitterEmail: z.string().optional(),
  isPrivate: z.boolean().optional(),
  attachmentUrls: z.array(z.string()).optional(),
  aiGeneratedTitle: z.string().optional(),
  aiGeneratedSummary: z.string().optional(),
  metadata: z.string().optional(),
  statusName: z.string().optional(),
  priorityName: z.string().optional(),
  customFieldValues: z
    .array(
      z.object({
        customFieldId: z.string(),
        data: z.array(
          z.object({
            value: z.string().optional(),
            id: z.string().optional(),
          }),
        ),
        metadata: z.string().optional(),
      }),
    )
    .optional(),
});
