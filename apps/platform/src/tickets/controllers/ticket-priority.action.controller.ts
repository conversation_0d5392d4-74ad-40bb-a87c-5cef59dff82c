import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseInterceptors,
} from "@nestjs/common";
import { ApiBody, ApiOperation, ApiTags } from "@nestjs/swagger";
import { SkipThrottle } from "@nestjs/throttler";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { OrganizationTier } from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { CurrentUser } from "../../common/decorators";
import {
  CommonTicketPriorityResponse,
  CreateTicketPriorityDto,
  GetAllTicketPrioritiesResponse,
  UpdateTicketPriorityDto,
} from "../dto";
import { TicketPriorityActionService } from "../services/ticket-priority.action.service";
import { TicketPriorityResponseDto } from "../transformer/ticket-priority.dto";

@ApiTags("Tickets")
@SkipThrottle()
@Controller("v1/tickets/priority")
@UseInterceptors(ResponseTransformInterceptor)
export class TicketPriorityActionController {
  constructor(
    private readonly ticketPriorityActionService: TicketPriorityActionService,
  ) {}

  @Get()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket priorities fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all ticket priorities",
    responseType: GetAllTicketPrioritiesResponse,
  })
  async getAllTicketPriorities(
    @CurrentUser() user: CurrentUser,
    @Query("teamId") teamId?: string,
  ) {
    const ticketPriorities =
      await this.ticketPriorityActionService.findAllTicketPriorities(
        user,
        teamId,
      );

    // Map the ticket priorities to the returnable format
    const returnableTicketPriorities = ticketPriorities.map(
      TicketPriorityResponseDto.fromEntity,
    );

    return returnableTicketPriorities;
  }

  @Get("/:id")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket priority fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get a ticket priority by ID",
    responseType: CommonTicketPriorityResponse,
  })
  async getTicketPriorityById(
    @Param("id") id: string,
    @Req() request: FastifyRequest,
  ) {
    const ticketPriority =
      await this.ticketPriorityActionService.findTicketPriorityById(
        id,
        request,
      );

    return TicketPriorityResponseDto.fromEntity(ticketPriority);
  }

  @Post()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiBody({ type: CreateTicketPriorityDto })
  @ApiResponseMessage("Created new ticket priority successfully!")
  @ApiCreateEndpoint({
    summary: "Create a new custom ticket priority",
    responseType: CommonTicketPriorityResponse,
  })
  async createNewCustomPriority(
    @Body() createTicketPriorityDto: CreateTicketPriorityDto,
    @Req() request: FastifyRequest,
  ) {
    const ticketPriority =
      await this.ticketPriorityActionService.createNewCustomPriority(
        createTicketPriorityDto,
        request,
      );

    return TicketPriorityResponseDto.fromEntity(ticketPriority);
  }

  @Patch("/:id")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiBody({ type: UpdateTicketPriorityDto })
  @ApiResponseMessage("Ticket priority updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update a custom ticket priority",
    responseType: CommonTicketPriorityResponse,
  })
  async updateCustomPriority(
    @Param("id") id: string,
    @Body() updateTicketPriorityDto: UpdateTicketPriorityDto,
    @Req() request: FastifyRequest,
  ) {
    const updatedTicketPriority =
      await this.ticketPriorityActionService.updateCustomPriorityData(
        id,
        updateTicketPriorityDto,
        request,
      );

    return TicketPriorityResponseDto.fromEntity(updatedTicketPriority);
  }

  @Delete("/:id")
  @ApiResponseMessage("Ticket priority deleted successfully!")
  @ApiDeleteEndpoint({ summary: "Delete a custom ticket priority" })
  async deleteCustomPriority(
    @Param("id") id: string,
    @Req() request: FastifyRequest,
  ) {
    await this.ticketPriorityActionService.deleteCustomPriority(id, request);
  }
}
