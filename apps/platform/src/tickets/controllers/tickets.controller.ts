import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  forwardRef,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiBody, ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  RequestSourceType,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { RequestSource } from "@repo/nestjs-commons/middlewares";
import { CommentEntityTypes, OrganizationTier, Team } from "@repo/thena-platform-entities";
import { CurrentUser } from "../../common/decorators";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CreateCommentDto } from "../../common/dto/comments.dto";
import {
  CommonCommentResponse,
  GetAllCommentsResponse,
} from "../../communications/dto";
import { CommunicationsService } from "../../communications/services/communications.service";
import { CommentResponseDto } from "../../communications/transformers/comment-response.transformer";
import { TeamsService } from "../../teams/services/teams.service";
import {
  AssignTeamToTicketBody,
  AssignTicketBody,
  AssignTicketQuery,
  CommonTicketResponse,
  CommonTicketTimeLogResponse,
  CreateTicketBody,
  EscalateTicketBody,
  GetAllTicketsResponse,
  GetAllTicketTimeLogsResponse,
  GetCommentsForTicketQuery,
  GetTicketQuery,
  GetTicketRelatedQuery,
  LinkTicketsBody,
  MarkDuplicateBody,
  MarkOrCreateSubTicketBody,
  TicketTimeLogDto,
  UpdateTicketBody,
} from "../dto";
import { TicketAnnotatorService } from "../services/ticket-annotator.service";
import { TicketsService } from "../services/tickets.service";
import { TicketResponseDto } from "../transformer/ticket-response.dto";
import { TicketTimeLogResponseDto } from "../transformer/ticket-time-log.dto";

@ApiTags("Tickets")
@Controller("v1/tickets")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class TicketsController {
  private readonly logSpanId = "TicketsController";

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly ticketsService: TicketsService,
    private readonly teamsService: TeamsService,
    @Inject(forwardRef(() => CommunicationsService))
    private readonly communicationsService: CommunicationsService,
    private readonly ticketMetadataService: TicketAnnotatorService,
  ) { }

  /**
   * Validates and fetches a team by its ID.
   * @param teamId The ID of the team to fetch.
   * @returns The team.
   */
  private async validateAndFetchTeam(teamId: string, organizationId: string) {
    const team = await this.teamsService.findOneByTeamId(
      teamId,
      organizationId,
    );

    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    return team;
  }

  @Get()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiResponseMessage("Tickets fetched successfully")
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiGetEndpoint({
    summary: "Get all tickets",
    responseType: GetAllTicketsResponse,
  })
  async getTickets(
    @CurrentUser() user: CurrentUser,
    @Query() query: GetTicketQuery,
  ) {
    // Validate the team ID
    let team: Team | null = null;
    if (query.teamId) {
      team = await this.validateAndFetchTeam(query.teamId, user.orgId);
    }

    // Fetch the tickets
    const { results: tickets } = await this.ticketsService.getTickets(
      user,
      team,
      query,
    );

    // Format the results
    const formattedResults = tickets.map(TicketResponseDto.fromEntity);
    return formattedResults;
  }

  @Get("/:id")
  @ApiResponseMessage("Ticket fetched successfully")
  @ApiGetEndpoint({
    summary: "Get a ticket",
    responseType: CommonTicketResponse,
  })
  async getTicket(@CurrentUser() user: CurrentUser, @Param("id") id: string) {
    if (!id) throw new BadRequestException("Ticket ID is required!");

    const ticket = await this.ticketsService.getTicketById(id, user.orgId);
    if (!ticket) throw new NotFoundException("Ticket not found!");

    return TicketResponseDto.fromEntity(ticket);
  }

  @Get("/:id/related")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiResponseMessage("Ticket types fetched successfully")
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiGetEndpoint({
    summary: "Get ticket related",
    responseType: GetAllTicketsResponse,
  })
  async getTicketRelated(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Query() query: GetTicketRelatedQuery,
  ) {
    const { results: relatedTickets } =
      await this.ticketsService.getTicketRelated(user, id, query);

    // Format the results
    const formattedResults = relatedTickets.map((ticket) =>
      TicketResponseDto.fromEntity(ticket.targetTicket),
    );

    return formattedResults;
  }

  @Post()
  @ApiOperation({
    summary: "Create a new ticket",
    description: `Creates a new ticket in the system. You must provide a title, requestorEmail, and teamId. Optionally, you can specify fields such as description, accountId, assignedAgentId, priority, status, formId, customFieldValues, and more.\n\n- If a formId and customFieldValues are provided, the ticket will be associated with a form and its custom fields.\n- If assignedAgentId is provided, the ticket will be assigned to that agent.\n- If performRouting is true, the system will attempt to auto-assign the ticket based on routing rules.\n- Additional metadata, attachments, and AI-generated fields can also be included.\n\nReturns the created ticket object.`,
  })
  @ApiBody({
    required: true,
    schema: {
      oneOf: [
        {
          description: "Minimal required fields",
          example: {
            title: "Need help with my account!",
            requestorEmail: "<EMAIL>",
            teamId: "team_12345",
          },
        },
        {
          description: "All possible fields",
          example: {
            title: "Cannot access dashboard",
            requestorEmail: "<EMAIL>",
            teamId: "team_67890",
            description: "I am unable to access the dashboard after login.",
            accountId: "account_abcde",
            assignedAgentId: "agent_123",
            assignedAgentEmail: "<EMAIL>",
            dueDate: "2024-07-01T12:00:00.000Z",
            submitterEmail: "<EMAIL>",
            statusId: "status_open",
            statusName: "Open",
            priorityId: "priority_high",
            priorityName: "High",
            sentimentId: "sentiment_positive",
            metadata: { source: "web", campaign: "summer2024" },
            typeId: "type_bug",
            isPrivate: false,
            source: "web",
            aiGeneratedTitle: "Login Issue",
            aiGeneratedSummary: "User cannot access dashboard after login.",
            attachmentUrls: [
              "https://example.com/attachment1.png",
              "https://example.com/attachment2.pdf",
            ],
            customFieldValues: [
              {
                customFieldId: "cf_001",
                data: [
                  { value: "Sample text" },
                ],
                metadata: { required: true },
              },
              {
                customFieldId: "cf_002",
                data: [
                  { value: "Option A", id: "opt_a" },
                  { value: "Option B", id: "opt_b" },
                ],
              },
            ],
            performRouting: true,
            formId: "form_12345",
          },
        },
      ],
    },
  })
  @ApiResponseMessage("Ticket created successfully")
  @ApiCreateEndpoint({
    summary: "Create a ticket",
    responseType: CommonTicketResponse,
    operationId: "create",
  })
  async createTicket(
    @CurrentUser() user: CurrentUser,
    @Body() body: CreateTicketBody,
    @RequestSourceType() source: RequestSource,
  ) {
    console.log(`Request came from: ${source}`);
    const team = await this.validateAndFetchTeam(body.teamId, user.orgId);
    const createdTicket = await this.ticketsService.createTicket(
      user,
      team,
      body,
      source,
    );

    return TicketResponseDto.fromEntity(createdTicket);
  }

  @Patch("/:id")
  @ApiResponseMessage("Ticket updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update a ticket",
    responseType: CommonTicketResponse,
  })
  async updateTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Body() body: UpdateTicketBody,
  ): Promise<TicketResponseDto> {
    // If the ticket ID is not provided, throw an error
    if (!id) throw new BadRequestException("Ticket ID is required!");

    // Update the ticket
    const updatedTicket = await this.ticketsService.updateTicket(
      user,
      id,
      body,
    );

    return TicketResponseDto.fromEntity(updatedTicket);
  }

  @Patch("/:id/escalate")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiResponseMessage("Ticket escalated successfully!")
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiUpdateEndpoint({
    summary: "Escalate a ticket",
    responseType: CommonTicketResponse,
  })
  async escalateTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Body() body: EscalateTicketBody,
  ) {
    if (!id) throw new BadRequestException("Ticket ID is required!");

    const escalatedTicket = await this.ticketsService.escalateTicket(
      user,
      id,
      body,
    );
    return TicketResponseDto.fromEntity(escalatedTicket);
  }

  @Patch("/:id/assign")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiResponseMessage("Ticket assigned successfully!")
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiUpdateEndpoint({
    summary: "Assign a ticket to an agent",
    responseType: CommonTicketResponse,
  })
  async assignTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Query() query: AssignTicketQuery,
    @Body() body: AssignTicketBody,
  ): Promise<TicketResponseDto> {
    const assignedTicket = await this.ticketsService.assignTicket(
      user,
      id,
      body,
      query,
    );

    return TicketResponseDto.fromEntity(assignedTicket);
  }

  /**
   * @deprecated
   */
  @Post("/:id/comment")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comment created successfully!")
  @ApiCreateEndpoint({
    summary: "Comment on a ticket",
    responseType: CommonCommentResponse,
  })
  async commentOnTicket(
    @CurrentUser() currentUser: CurrentUser,
    @Param("id") id: string,
    @Body() body: CreateCommentDto,
  ) {
    // If the ticket ID is not provided, throw an error
    if (id === undefined) {
      throw new BadRequestException("Ticket ID is required!");
    }

    // Fetch the ticket
    const ticket = await this.ticketsService.getTicketById(
      id,
      currentUser.orgId,
    );

    // If the ticket is not found, throw an error
    if (!ticket) {
      throw new NotFoundException("Ticket not found!");
    }

    // Create the comment
    const comment = await this.communicationsService.createCommentOnAnEntity(
      ticket,
      CommentEntityTypes.TICKET,
      body,
      currentUser,
    );

    return CommentResponseDto.fromEntity(comment);
  }

  /**
   * @deprecated
   */
  @Get("/:id/comments")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comments fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get comments for a ticket",
    responseType: GetAllCommentsResponse,
  })
  async getCommentsForATicket(
    @CurrentUser() currentUser: CurrentUser,
    @Param("id") id: string,
    @Query() query: GetCommentsForTicketQuery,
  ) {
    // If the ticket ID is not provided, throw an error
    if (!id) throw new BadRequestException("Ticket ID is required!");

    // Fetch the ticket
    this.logger.log(`GET_COMMENTS - START:${Date.now()}`);
    const { results: comments } =
      await this.ticketsService.getCommentsForTicket(id, query, currentUser);
    this.logger.log(`GET_COMMENTS - END:${Date.now()}`);

    return comments.map(CommentResponseDto.fromEntity);
  }

  @Patch("/sub-ticket")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket marked as sub-ticket successfully!")
  @ApiUpdateEndpoint({
    summary: "Mark a ticket as sub-ticket",
    responseType: CommonTicketResponse,
  })
  async markOrCreateSubTicket(
    @CurrentUser() user: CurrentUser,
    @Body() body: MarkOrCreateSubTicketBody,
  ) {
    const markedSubTicket = await this.ticketsService.markOrCreateSubTicket(
      user,
      body,
    );

    return TicketResponseDto.fromEntity(markedSubTicket);
  }

  @Patch("/mark-duplicate") 
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket marked as duplicate successfully!")
  @ApiUpdateEndpoint({
    summary: "Mark a ticket as duplicate",
    responseType: CommonTicketResponse,
  })
  async markDuplicate(
    @CurrentUser() user: CurrentUser,
    @Body() body: MarkDuplicateBody,
  ) {
    const markedDuplicateTicket = await this.ticketsService.markDuplicateTicket(
      user,
      body,
    );

    return TicketResponseDto.fromEntity(markedDuplicateTicket);
  }

  @Patch("/link")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Tickets linked successfully!")
  @ApiUpdateEndpoint({
    summary: "Link tickets",
    responseType: CommonTicketResponse,
  })
  async linkTickets(
    @CurrentUser() user: CurrentUser,
    @Body() body: LinkTicketsBody,
  ) {
    const linkedTicket = await this.ticketsService.linkTickets(user, body);

    return TicketResponseDto.fromEntity(linkedTicket);
  }

  @Patch("/:id/reassign-team")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket reassigned successfully!")
  @ApiUpdateEndpoint({
    summary: "Reassign a ticket to a team",
    responseType: CommonTicketResponse,
  })
  async reassignTeamToTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Body() body: AssignTeamToTicketBody,
  ): Promise<TicketResponseDto> {
    const team = await this.validateAndFetchTeam(body.teamId, user.orgId);
    if (!team) {
      throw new NotFoundException("Team not found!");
    }

    const reassignedTicket = await this.ticketsService.reassignTeamToTicket(
      user,
      team,
      id,
    );

    return TicketResponseDto.fromEntity(reassignedTicket);
  }

  @Patch("/:id/archive")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket archived successfully!")
  @ApiUpdateEndpoint({
    summary: "Archive a ticket",
    responseType: CommonTicketResponse,
  })
  async archiveTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
  ): Promise<TicketResponseDto> {
    const archivedTicket = await this.ticketsService.archiveTicket(user, id);
    return TicketResponseDto.fromEntity(archivedTicket);
  }

  @Patch("/:id/unarchive")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket unarchived successfully!")
  @ApiUpdateEndpoint({
    summary: "Unarchive a ticket",
    responseType: CommonTicketResponse,
  })
  async unarchiveTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
  ): Promise<TicketResponseDto> {
    try {
      // Unarchive the ticket
      const unArchivedTicket = await this.ticketsService.unArchiveTicket(
        user,
        id,
      );

      return TicketResponseDto.fromEntity(unArchivedTicket);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to unarchive ticket ${id}, ${error.message}`,
          error.stack,
        );
      } else {
        console.error(`Failed to unarchive ticket ${id}`, error);
      }

      // If the error is an HTTP exception, throw it
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException(
        "Failed to unarchive ticket, please try again later!",
      );
    }
  }

  @Patch("/:id/log")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket time logged successfully!")
  @ApiUpdateEndpoint({
    summary: "Log time for a ticket",
    responseType: CommonTicketTimeLogResponse,
  })
  async logTimeForTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
    @Body() body: TicketTimeLogDto,
  ) {
    // If the ticket ID is not provided, throw an error
    if (!id) throw new BadRequestException("Ticket ID is required!");

    // Log the time for the ticket
    const loggedTimeLog = await this.ticketsService.logTimeForTicket(
      user,
      id,
      body,
    );

    return TicketTimeLogResponseDto.fromEntity(loggedTimeLog);
  }

  @Get("/:id/time-logs")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket time logs fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get time logs for a ticket",
    responseType: GetAllTicketTimeLogsResponse,
  })
  async getTimeLogsForTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
  ) {
    // If the ticket ID is not provided, throw an error
    if (!id) throw new BadRequestException("Ticket ID is required!");

    // Get the time logs for the ticket
    const timeLogs = await this.ticketsService.getTimeLogsForTicket(user, id);
    return timeLogs.map(TicketTimeLogResponseDto.fromEntity);
  }

  @Delete("/:id")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket deleted successfully!")
  @ApiDeleteEndpoint({
    summary: "Delete a ticket",
    responseType: CommonTicketResponse,
  })
  async deleteTicket(
    @CurrentUser() user: CurrentUser,
    @Param("id") id: string,
  ) {
    await this.ticketsService.deleteTicket(user, id);
  }

  @Get("ticket-types")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket types fetched successfully!")
  getTicketFieldTypes(@Query("slaEnabled") slaEnabled: string): {
    success: boolean;
    data: any;
  } {
    const isSlaEnabled = slaEnabled === "true";
    const data = this.ticketMetadataService.getEntityFieldTypes(isSlaEnabled);
    return {
      success: true,
      data,
    };
  }
}
