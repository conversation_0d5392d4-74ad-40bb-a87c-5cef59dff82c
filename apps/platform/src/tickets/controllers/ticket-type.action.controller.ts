import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseInterceptors,
} from "@nestjs/common";
import { ApiBody, ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { OrganizationTier } from "@repo/thena-platform-entities";
import { FastifyRequest } from "fastify";
import { CurrentUser } from "../../common/decorators";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CommonTicketTypeResponse, GetAllTicketTypesResponse } from "../dto";
import {
  CreateTicketTypeDto,
  UpdateTicketTypeDto,
} from "../dto/ticket-type.dto";
import { TicketTypeActionService } from "../services/ticket-type.action.service";
import { TicketTypeResponseDto } from "../transformer/ticket-type.dto";

@ApiTags("Tickets")
@Controller("v1/tickets/type")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class TicketTypeActionController {
  constructor(
    private readonly ticketTypeActionService: TicketTypeActionService,
  ) {}

  @Get()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE) 
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket types fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get all ticket types",
    responseType: GetAllTicketTypesResponse,
  })
  async getAllTicketTypes(
    @CurrentUser() user: CurrentUser,
    @Query("teamId") teamId?: string,
  ) {
    const ticketTypes = await this.ticketTypeActionService.findAllTicketTypes(
      user,
      teamId,
    );

    return ticketTypes.map(TicketTypeResponseDto.fromEntity);
  }

  @Get("/:id")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket type fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get a ticket type by ID",
    responseType: CommonTicketTypeResponse,
  })
  async getTicketTypeById(
    @Param("id") id: string,
    @Req() request: FastifyRequest,
  ) {
    const ticketType = await this.ticketTypeActionService.findTicketTypeById(
      id,
      request,
    );

    return TicketTypeResponseDto.fromEntity(ticketType);
  }

  @Post()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiBody({ type: CreateTicketTypeDto })
  @ApiResponseMessage("Created new ticket type successfully!")
  @ApiCreateEndpoint({
    summary: "Create a new custom ticket type",
    responseType: CommonTicketTypeResponse,
  })
  async createTicketType(
    @Body() createTicketTypeDto: CreateTicketTypeDto,
    @Req() request: FastifyRequest,
  ) {
    const ticketType = await this.ticketTypeActionService.createTicketType(
      createTicketTypeDto,
      request,
    );

    return TicketTypeResponseDto.fromEntity(ticketType);
  }

  @Patch("/:id")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiBody({ type: UpdateTicketTypeDto })
  @ApiResponseMessage("Ticket type updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update a custom ticket type",
    responseType: CommonTicketTypeResponse,
  })
  async updateTicketType(
    @Param("id") id: string,
    @Body() updateTicketTypeDto: UpdateTicketTypeDto,
    @Req() request: FastifyRequest,
  ) {
    const ticketType = await this.ticketTypeActionService.updateTicketType(
      id,
      updateTicketTypeDto,
      request,
    );

    return TicketTypeResponseDto.fromEntity(ticketType);
  }

  @Delete("/:id")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description: "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Ticket type deleted successfully!")
  @ApiDeleteEndpoint({ summary: "Delete a custom ticket type" })
  async deleteTicketType(
    @Param("id") id: string,
    @Req() request: FastifyRequest,
  ) {
    await this.ticketTypeActionService.deleteTicketType(id, request);
  }
}
