import {
  BadRequestException,
  Controller,
  Delete,
  Get,
  Param,
  PayloadTooLargeException,
  Post,
  Query,
  Req,
  Res,
} from "@nestjs/common";
import * as crypto from "crypto";
import {
  ApiConsumes,
  ApiExcludeEndpoint,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from "@nestjs/swagger";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { CurrentUser } from "@repo/nestjs-commons/utils";
import { OrganizationTier } from "@repo/thena-platform-entities";
import { FastifyReply, FastifyRequest } from "fastify";
import { Public } from "../../auth/decorators/auth.decorator";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { ExpiryFormat } from "../interfaces/storage.interface";
import { StorageService } from "../services/storage-service";

/**
 * Controller for handling file storage operations including
 * upload, retrieval via private URLs, and retrieval via signed URLs.
 */
@ApiTags("Storage")
@Controller("storage")
@SkipAllThrottler()
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  /**
   * Check if the content type is an image
   */
  private isImageContentType(contentType: string): boolean {
    const imageTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml",
      "image/bmp",
      "image/tiff",
      "image/x-icon",
      "image/heic",
    ];
    return imageTypes.includes(contentType);
  }

  /**
   * Generate ETag for file based on metadata
   */
  private generateETag(filename: string, contentType: string, size?: number): string {
    const input = `${filename}-${contentType}-${size || 0}`;
    return `"${crypto.createHash('md5').update(input).digest('hex')}"`;
  }

  /**
   * Set cache headers for images
   */
  private setCacheHeaders(response: FastifyReply, file: any): void {
    if (this.isImageContentType(file.contentType)) {
      const etag = this.generateETag(file.filename, file.contentType, file.data?.length);
      response
        .header('Cache-Control', 'public, max-age=2592000') // 30 days
        .header('ETag', etag);
    }
  }

  /**
   * Upload a file to storage with configurable expiration
   *
   * @param request The FastifyRequest containing the file
   * @param user The current authenticated user
   * @param expiry Optional expiration format (1d, 1w, 1m, 1y)
   * @returns Information about the uploaded file and its access URL
   */
  @Post("upload-file")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    operationId: "uploadFile",
    summary: "Upload a single file",
    description:
      "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiConsumes("multipart/form-data")
  @ApiQuery({
    name: "expiry",
    enum: ["1d", "1w", "1m", "1y"],
    required: false,
    description: "Expiry format for the file if shouldExpire is true",
  })
  async upload(
    @Req() request: FastifyRequest,
    @CurrentUser() user: CurrentUser,
    @Query("expiry") expiry?: ExpiryFormat,
  ) {
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    // Define allowed MIME types
    const ALLOWED_MIME_TYPES = [
      // Images
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml",
      "image/bmp",
      "image/tiff",
      "image/x-icon",
      "image/heic",

      // PDFs
      "application/pdf",

      // Audio
      "audio/mpeg", // .mp3
      "audio/ogg", // .ogg
      "audio/wav", // .wav
      "audio/webm", // .webm audio
      "audio/aac",
      "audio/flac",
      "audio/mp4",
      "audio/x-ms-wma",

      // PDFs
      "application/pdf",
      // Audio
      "audio/mpeg",
      "audio/ogg",
      "audio/wav",
      "audio/webm",

      // Video
      "video/mp4",
      "video/webm",
      "video/ogg",
      "video/x-msvideo", // .avi
      "video/x-ms-wmv", // .wmv
      "video/mpeg", // .mpeg, .mpg
      "video/3gpp",
      "video/3gpp2",
      "video/quicktime", // .mov

      // Documents
      "application/msword", // .doc
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
      "application/vnd.oasis.opendocument.text", // .odt

      // Spreadsheets
      "application/vnd.ms-excel", // .xls
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
      "application/vnd.oasis.opendocument.spreadsheet", // .ods
      "application/csv",
      "text/csv",

      // Presentations
      "application/vnd.ms-powerpoint", // .ppt
      "application/vnd.openxmlformats-officedocument.presentationml.presentation", // .pptx
      "application/vnd.oasis.opendocument.presentation", // .odp

      // Text and rich text
      "text/plain",
      "text/markdown",
      "application/rtf",

      // Compressed archives (optional)
      "application/zip",
      "application/x-tar",
      "application/x-7z-compressed",
      "application/x-rar-compressed",
    ];

    try {
      const file = await request.file();
      if (!file) {
        throw new BadRequestException("No files received in request");
      }

      // Validate file type
      if (!ALLOWED_MIME_TYPES.includes(file.mimetype)) {
        throw new BadRequestException(
          `File type not allowed. Allowed types: ${ALLOWED_MIME_TYPES.join(
            ", ",
          )}`,
        );
      }

      const buffer = await file.toBuffer();
      if (buffer.length >= MAX_FILE_SIZE) {
        throw new PayloadTooLargeException(
          `File size exceeds the maximum allowed size of ${
            MAX_FILE_SIZE / (1024 * 1024)
          }MB`,
        );
      }
      const storageFile = await this.storageService.uploadFileToStorage(
        file,
        user,
        expiry,
      );
      return {
        success: true,
        data: storageFile,
      };
    } catch (error) {
      if (error.code === "FST_REQ_FILE_TOO_LARGE") {
        throw new PayloadTooLargeException(
          `File size exceeds the limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`,
        );
      }
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get a file by its identifier
   *
   * @param uid The unique identifier of the file
   * @param user The current authenticated user
   * @param response The Fastify response object
   */
  @Get("fetchDocumentByIdentifier")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    operationId: "fetchDocumentByIdentifier",
    summary: "Get a file by its identifier",
    description:
      "This endpoint is only available for enterprise tier organizations.",
  })
  @ApiQuery({
    name: "uid",
    required: true,
    description: "The unique identifier of the file",
  })
  async getFileById(
    @Query("uid") uid: string,
    @CurrentUser() user: CurrentUser,
    @Res() response: FastifyReply,
  ) {
    if (!uid) {
      throw new BadRequestException("Identifier is required");
    }
    const file = await this.storageService.serveFileById(uid, user);
    this.setCacheHeaders(response, file);
    response
      .header("Content-Type", file.contentType)
      .header("Content-Disposition", `inline; filename="${file.filename}"`)
      .send(file.data);
  }

  /**
   * Get a private file by its identifier, authenticated by user token
   *
   * @param fileName The name of the file
   * @param storageId The unique storage identifier
   * @param userToken The user's authentication token
   * @param user The current authenticated user
   * @param response The Fastify response object
   */

  @Get("private/document/:fileName/:storageId/:userToken")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    summary: "Get private URL for a file",
    description:
      "This endpoint is only available for enterprise tier organizations.",
  })
  async getPrivateUrl(
    @Param("fileName") fileName: string,
    @Param("storageId") storageId: string,
    @Param("userToken") userToken: string,
    @CurrentUser() user: CurrentUser,
    @Res() response: FastifyReply,
  ) {
    try {
      const { orgId, authId } = user;
      if (userToken !== userToken) {
        throw new BadRequestException("Invalid user token");
      }
      const file = await this.storageService.servePublicFile(
        orgId,
        fileName,
        storageId,
        authId,
      );
      this.setCacheHeaders(response, file);
      response
        .header("Content-Type", file.contentType)
        .header("Content-Disposition", `inline; filename="${file.filename}"`)
        .send(file.data);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Delete a file by its identifier
   *
   * @param uid The unique identifier of the file
   * @param user The current authenticated user
   */
  @Delete("deleteDocumentByIdentifier")
  @RequiredTier(OrganizationTier.ENTERPRISE)
  @ApiOperation({
    summary: "Delete a file by its identifier",
    description:
      "This endpoint is only available for enterprise tier organizations.",
  })
  async deleteDocumentByIdentifier(
    @Query("uid") uid: string,
    @CurrentUser() user: CurrentUser,
  ) {
    if (!uid) {
      throw new BadRequestException("Identifier is required");
    }
    await this.storageService.deleteFileByIdentifier(uid, user);
    return {
      success: true,
      message: "File deleted successfully",
    };
  }

  /**
   * Access a file using a signed URL with access token
   * This endpoint is public and does not require authentication
   *
   * @param accessToken JWT token with file access information
   * @param response The Fastify response object
   */

  @Public()
  @ApiExcludeEndpoint()
  @Get("public/signed-url/document/:accessToken")
  @ApiOperation({
    summary: "Get signed URL for a file",
  })
  async getPublicUrlWithAccessToken(
    @Param("accessToken") accessToken: string,
    @Res() response: FastifyReply,
  ) {
    try {
      if (!accessToken) {
        throw new BadRequestException("Access token is required");
      }
      const file = await this.storageService.serveSignedUrl(accessToken);
      this.setCacheHeaders(response, file);
      response
        .header("Content-Type", file.contentType)
        .header("Content-Disposition", `inline; filename="${file.filename}"`)
        .send(file.data);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
