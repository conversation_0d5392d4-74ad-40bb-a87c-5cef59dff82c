import { BullModule } from "@nestjs/bullmq";
import { Global, Module } from "@nestjs/common";
import { ConfigModule } from "../config/config.module";
import { QueueNames } from "../constants/queue.constants";
import { BullBoardService } from "./bull-board.service";

@Global()
@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueNames.TICKET_SNS_PUBLISHER,
      defaultJobOptions: {
        removeOnComplete: {
          age: 24 * 3600,
          count: 1000,
        },
        removeOnFail: {
          age: 24 * 3600,
        },
      },
    }),
    BullModule.registerQueue({
      name: QueueNames.ACCOUNTS_SNS_PUBLISHER,
      defaultJobOptions: {
        removeOnComplete: {
          age: 24 * 3600,
          count: 1000,
        },
        removeOnFail: {
          age: 24 * 3600,
        },
      },
    }),
    BullModule.registerQueue({
      name: QueueNames.COMMENT_SNS_PUBLISHER,
      defaultJobOptions: {
        removeOnComplete: {
          age: 24 * 3600,
          count: 1000,
        },
        removeOnFail: {
          age: 24 * 3600,
        },
      },
    }),
    BullModule.registerQueue({
      name: QueueNames.ORGANIZATION_SNS_PUBLISHER,
      defaultJobOptions: {
        removeOnComplete: {
          age: 24 * 3600,
          count: 1000,
        },
        removeOnFail: {
          age: 24 * 3600,
        },
      },
    }),
    BullModule.registerQueue({
      name: QueueNames.REACTION_SNS_PUBLISHER,
      defaultJobOptions: {
        removeOnComplete: {
          age: 24 * 3600,
          count: 1000,
        },
        removeOnFail: {
          age: 24 * 3600,
        },
      },
    }),
    ConfigModule,
  ],
  providers: [BullBoardService],
  exports: [BullBoardService],
})
export class BullBoardModule {}
