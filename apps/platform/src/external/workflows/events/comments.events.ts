import { EntityType } from "@repo/thena-platform-entities";
import { AccountEvents, TicketEvents } from "@repo/thena-shared-interfaces";
import { AbstractWorkflowEvent } from "@repo/workflow-engine";
import {
  COMMON_MESSAGE_PROPERTIES,
  MESSAGE_ATTRIBUTES_SCHEMA,
} from "../constants";
import { EXTERNAL_STORAGE_RESPONSE_SCHEMA } from "../constants/common.schema";

const COMMENT_PAYLOAD_SCHEMA = {
  type: "object",
  properties: {
    id: { type: "string" },
    content: { type: ["string", "null"] },
    contentMarkdown: { type: ["string", "null"] },
    contentHtml: { type: ["string", "null"] },
    contentJson: { type: ["string", "null"] },
    teamId: { type: ["string", "null"] },
    accountId: { type: ["string", "null"] },
    commentType: { type: "string" },
    commentVisibility: { type: ["string", "null"] },
    parentCommentId: { type: ["string", "null"] },
    metadata: { type: ["object", "null"] },
    customerContact: {
      type: "object",
      properties: {
        id: { type: ["string", "null"] },
        email: { type: ["string", "null"] },
        avatarUrl: { type: ["string", "null"] },
      },
    },
    author: {
      type: "object",
      properties: {
        id: { type: ["string", "null"] },
        name: { type: ["string", "null"] },
        email: { type: ["string", "null"] },
        avatarUrl: { type: ["string", "null"] },
      },
    },
    attachments: EXTERNAL_STORAGE_RESPONSE_SCHEMA,
    createdWithTicket: { type: ["boolean", "null"] },
    createdAt: { type: ["string", "null"] },
    updatedAt: { type: ["string", "null"] },
    deletedAt: { type: ["string", "null"] },
  },
};

// Ticket Comment Events
export class TicketCommentCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Ticket comment created";

  static override eventType = TicketEvents.COMMENT_ADDED;

  static override description =
    "This event is triggered when a ticket comment is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: COMMENT_PAYLOAD_SCHEMA,
              ticket: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                },
                required: ["id", "title"],
              },
            },
            required: ["comment", "ticket"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    pathToTeamId: "context.event.message.payload.comment.teamId",

    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
  };
}

export class TicketCommentUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Ticket comment updated";

  static override eventType = TicketEvents.COMMENT_UPDATED;

  static override description =
    "This event is triggered when a ticket comment is updated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: {
                type: "object",
                properties: {
                  previous: COMMENT_PAYLOAD_SCHEMA,
                  updated: COMMENT_PAYLOAD_SCHEMA,
                },
                required: ["previous", "updated"],
              },
              ticket: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                },
                required: ["id", "title"],
              },
            },
            required: ["comment", "ticket"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    pathToTeamId: "context.event.message.payload.comment.updated.teamId",

    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
  };
}

export class TicketCommentDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Ticket comment deleted";
  static override eventType = TicketEvents.COMMENT_DELETED;
  static override description =
    "This event is triggered when a ticket comment is deleted";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: COMMENT_PAYLOAD_SCHEMA,
              ticket: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                },
                required: ["id", "title"],
              },
            },
            required: ["comment", "ticket"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
  static override accessibleToTeam = true;

  static override metadata = {
    pathToTeamId: "context.event.message.payload.comment.teamId",

    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
  };
}

// Account Activity Comment Events
export class AccountActivityCommentCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account activity comment created";
  static override eventType = AccountEvents.ACCOUNT_ACTIVITY_COMMENT_CREATED;
  static override description =
    "This event is triggered when an account activity comment is created";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: COMMENT_PAYLOAD_SCHEMA,
              accountActivity: {
                type: "object",
                properties: {
                  id: { type: "string" },
                },
                required: ["id"],
              },
            },
            required: ["comment", "accountActivity"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_ACTIVITY,
    requiredFields: {
      accountActivityId: "{{context.event.message.payload.accountActivity.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountActivity",
  };
}

// Account Activity Comment Events - Add Update and Delete
export class AccountActivityCommentUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account activity comment updated";
  static override eventType = AccountEvents.ACCOUNT_ACTIVITY_COMMENT_UPDATED;
  static override description =
    "This event is triggered when an account activity comment is updated";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: {
                type: "object",
                properties: {
                  previous: COMMENT_PAYLOAD_SCHEMA,
                  updated: COMMENT_PAYLOAD_SCHEMA,
                },
                required: ["previous", "updated"],
              },
              accountActivity: {
                type: "object",
                properties: {
                  id: { type: "string" },
                },
                required: ["id"],
              },
            },
            required: ["comment", "accountActivity"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_ACTIVITY,
    requiredFields: {
      accountActivityId: "{{context.event.message.payload.accountActivity.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountActivity",
  };
}

export class AccountActivityCommentDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account activity comment deleted";
  static override eventType = AccountEvents.ACCOUNT_ACTIVITY_COMMENT_DELETED;
  static override description =
    "This event is triggered when an account activity comment is deleted";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: COMMENT_PAYLOAD_SCHEMA,
              accountActivity: {
                type: "object",
                properties: {
                  id: { type: "string" },
                },
                required: ["id"],
              },
            },
            required: ["comment", "accountActivity"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_ACTIVITY,
    requiredFields: {
      accountActivityId: "{{context.event.message.payload.accountActivity.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountActivity",
  };
}

// Account Note Comment Events
export class AccountNoteCommentCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account note comment created";
  static override eventType = AccountEvents.ACCOUNT_NOTE_COMMENT_CREATED;
  static override description =
    "This event is triggered when an account note comment is created";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: COMMENT_PAYLOAD_SCHEMA,
              accountNote: {
                type: "object",
                properties: {
                  id: { type: "string" },
                },
                required: ["id"],
              },
            },
            required: ["comment", "accountNote"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_NOTE,
    requiredFields: {
      accountNoteId: "{{context.event.message.payload.accountNote.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountNote",
  };
}

// Account Note Comment Events - Add Update and Delete
export class AccountNoteCommentUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account note comment updated";
  static override eventType = AccountEvents.ACCOUNT_NOTE_COMMENT_UPDATED;
  static override description =
    "This event is triggered when an account note comment is updated";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: {
                type: "object",
                properties: {
                  previous: COMMENT_PAYLOAD_SCHEMA,
                  updated: COMMENT_PAYLOAD_SCHEMA,
                },
                required: ["previous", "updated"],
              },
              accountNote: {
                type: "object",
                properties: {
                  id: { type: "string" },
                },
                required: ["id"],
              },
            },
            required: ["comment", "accountNote"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_NOTE,
    requiredFields: {
      accountNoteId: "{{context.event.message.payload.accountNote.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountNote",
  };
}

export class AccountNoteCommentDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account note comment deleted";
  static override eventType = AccountEvents.ACCOUNT_NOTE_COMMENT_DELETED;
  static override description =
    "This event is triggered when an account note comment is deleted";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: COMMENT_PAYLOAD_SCHEMA,
              accountNote: {
                type: "object",
                properties: {
                  id: { type: "string" },
                },
                required: ["id"],
              },
            },
            required: ["comment", "accountNote"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_NOTE,
    requiredFields: {
      accountNoteId: "{{context.event.message.payload.accountNote.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountNote",
  };
}

// Account Task Comment Events
export class AccountTaskCommentCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account task comment created";
  static override eventType = AccountEvents.ACCOUNT_TASK_COMMENT_CREATED;
  static override description =
    "This event is triggered when an account task comment is created";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: COMMENT_PAYLOAD_SCHEMA,
              accountTask: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                },
                required: ["id", "title"],
              },
            },
            required: ["comment", "accountTask"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_TASK,
    requiredFields: {
      accountTaskId: "{{context.event.message.payload.accountTask.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountTask",
  };
}

// Account Task Comment Events - Add Update and Delete
export class AccountTaskCommentUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account task comment updated";
  static override eventType = AccountEvents.ACCOUNT_TASK_COMMENT_UPDATED;
  static override description =
    "This event is triggered when an account task comment is updated";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: {
                type: "object",
                properties: {
                  previous: COMMENT_PAYLOAD_SCHEMA,
                  updated: COMMENT_PAYLOAD_SCHEMA,
                },
                required: ["previous", "updated"],
              },
              accountTask: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                },
                required: ["id", "title"],
              },
            },
            required: ["comment", "accountTask"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_TASK,
    requiredFields: {
      accountTaskId: "{{context.event.message.payload.accountTask.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountTask",
  };
}

export class AccountTaskCommentDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account task comment deleted";
  static override eventType = AccountEvents.ACCOUNT_TASK_COMMENT_DELETED;
  static override description =
    "This event is triggered when an account task comment is deleted";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              comment: COMMENT_PAYLOAD_SCHEMA,
              accountTask: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                },
                required: ["id", "title"],
              },
            },
            required: ["comment", "accountTask"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_TASK,
    requiredFields: {
      accountTaskId: "{{context.event.message.payload.accountTask.id}}",
    },
    pathToAnnotate: "context.event.message.payload.accountTask",
  };
}
