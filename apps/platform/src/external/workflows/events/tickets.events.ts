import { EntityType } from "@repo/thena-platform-entities";
import { TicketEvents } from "@repo/thena-shared-interfaces";
import { AbstractWorkflowEvent } from "@repo/workflow-engine";
import {
  COMMON_MESSAGE_PROPERTIES,
  COMMON_TICKETS_EVENT_SCHEMA,
  MESSAGE_ATTRIBUTES_SCHEMA,
} from "../constants";

export class TicketCreatedEvent extends AbstractWorkflowEvent {
  private constructor() {
    super();
  }

  static override eventName = "Ticket created";

  static override eventType = TicketEvents.CREATED;

  static override description =
    "This event is triggered when a ticket is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              ticket: COMMON_TICKETS_EVENT_SCHEMA,
              comment: {
                type: ["object", "null"],
                properties: {
                  id: {
                    type: "string",
                  },
                  content: {
                    type: "string",
                  },
                  contentHtml: {
                    type: ["string", "null"],
                  },
                  contentJson: {
                    type: ["string", "null"],
                  },
                  customerContactId: {
                    type: ["string", "null"],
                  },
                  createdAt: {
                    type: "string",
                  },
                },
              },
            },
            required: ["ticket"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
    pathToTeamId: "context.event.message.payload.ticket.teamId",
  };

  static override accessibleToTeam = true;
}

export class TicketUpdatedEvent extends AbstractWorkflowEvent {
  private constructor() {
    super();
  }

  static override eventName = "Ticket updated";

  static override eventType = TicketEvents.UPDATED;

  static override description =
    "This event is triggered when a ticket is updated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousTicket: COMMON_TICKETS_EVENT_SCHEMA,
              ticket: COMMON_TICKETS_EVENT_SCHEMA,
            },
            required: ["previousTicket", "ticket"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
    pathToTeamId: "context.event.message.payload.ticket.teamId",
  };

  static override accessibleToTeam = true;
}

export class TicketDeletedEvent extends AbstractWorkflowEvent {
  private constructor() {
    super();
  }

  static override eventName = "Ticket deleted";

  static override eventType = TicketEvents.DELETED;

  static override description =
    "This event is triggered when a ticket is deleted";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              ticket: COMMON_TICKETS_EVENT_SCHEMA,
            },
            required: ["ticket"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
    pathToTeamId: "context.event.message.payload.ticket.teamId",
  };

  static override accessibleToTeam = true;
}

export class TicketEscalatedEvent extends AbstractWorkflowEvent {
  private constructor() {
    super();
  }

  static override eventName = "Ticket escalated";

  static override eventType = TicketEvents.ESCALATED;

  static override description =
    "This event is triggered when a ticket is escalated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              ticket: COMMON_TICKETS_EVENT_SCHEMA,
            },
            required: ["ticket"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
    pathToTeamId: "context.event.message.payload.ticket.teamId",
  };

  static override accessibleToTeam = true;
}

export class TicketArchivedEvent extends AbstractWorkflowEvent {
  private constructor() {
    super();
  }

  static override eventName = "Ticket archived";

  static override eventType = TicketEvents.ARCHIVED;

  static override description =
    "This event is triggered when a ticket is archived";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              ticket: COMMON_TICKETS_EVENT_SCHEMA,
            },
            required: ["ticket"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
    pathToTeamId: "context.event.message.payload.ticket.teamId",
  };

  static override accessibleToTeam = true;
}

export class TicketSLABreachedEvent extends AbstractWorkflowEvent {
  private constructor() {
    super();
  }

  static override eventName = "Ticket SLA Breached";

  static override eventType = TicketEvents.SLA_BREACHED;

  static override description =
    "This event is triggered when a ticket SLA is breached";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              ticket: {
                type: "object",
                properties: {
                  id: {
                    type: "string",
                  },
                  teamId: {
                    type: "string",
                  },
                },
              },
              sla: {
                type: "object",
                properties: {
                  policyId: {
                    type: "string",
                  },
                  policyName: {
                    type: "string",
                  },
                  metric: {
                    type: "string",
                  },
                },
              },
            },
            required: ["ticket", "sla"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.TICKET,
    requiredFields: {
      ticketId: "{{context.event.message.payload.ticket.id}}",
    },
    pathToAnnotate: "context.event.message.payload.ticket",
    pathToTeamId: "context.event.message.payload.ticket.teamId",
  };

  static override accessibleToTeam = true;
}
