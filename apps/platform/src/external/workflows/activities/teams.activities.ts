import { teams } from "@repo/shared-proto/dist/generated/src";
import { AbstractWorkflowActivity } from "@repo/workflow-engine";
import { ConfigKeys, ConfigService } from "../../../config/config.service";
import { ACTIVITY_THROTTLER_CONFIG } from "../constants/activity-throttler.config";

export class CheckTeamAvailabilityActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Check team availability";

  static override description =
    "This activity checks if team is available from team holidays and business hours";

  static override identifier = "teams:check-team-availability";

  static override requestSchema = {
    type: "object",
    properties: {
      teamId: { type: ["string", "null"] },
      specificHolidays: {
        type: ["array", "null"],
        items: { type: "string" },
      },
    },
    required: ["teamId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      isAvailable: { type: "boolean" },
      reason: {
        type: "string",
        enum: ["IN_BUSINESS_HOURS", "OUTSIDE_BUSINESS_HOURS", "HOLIDAY"],
      },
    },
    required: ["isAvailable", "reason"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: teams.GRPC_TEAMS_V1_PACKAGE_NAME,
      serviceName: teams.TEAMS_SERVICE_NAME,
      methodName: "CheckTeamAvailability",
      protoPath: "dist/proto/teams/teams.proto",
    },
  };

  static override isCompensable = false;

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["teamId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_5,
    },
  };
}
