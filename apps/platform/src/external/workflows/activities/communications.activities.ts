import { communication } from "@repo/shared-proto";
import { AbstractWorkflowActivity } from "@repo/workflow-engine";
import { ConfigKeys, ConfigService } from "../../../config/config.service";
import { COMMENT_RESPONSE_SCHEMA } from "../constants";
import { ACTIVITY_THROTTLER_CONFIG } from "../constants/activity-throttler.config";

/**
 * Create comment activity
 */
export class CreateCommentActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create comment";

  static override description = "This activity creates a comment on an entity";

  static override identifier = "communications:create-comment";

  static override requestSchema = {
    type: "object",
    properties: {
      content: { type: "string" },
      contentHtml: { type: ["string", "null"] },
      contentJson: { type: ["string", "object", "null"] },
      threadName: { type: ["string", "null"] },
      entityType: {
        type: "string",
      },
      entityId: { type: "string" },
      parentCommentId: { type: ["string", "null"] },
      commentVisibility: { type: ["string", "null"] },
      commentType: { type: ["string", "null"] },
      metadata: { type: ["string", "null"] },
      attachmentIds: {
        type: ["array", "null"],
        items: { type: "string" },
      },
      customerEmail: { type: ["string", "null"] },
      impersonatedUserEmail: { type: ["string", "null"] },
      impersonatedUserName: { type: ["string", "null"] },
      impersonatedUserAvatar: { type: ["string", "null"] },
      shouldSendEmail: { type: ["boolean", "null"] },
    },
    required: ["content", "entityType", "entityId"],
  };

  static override responseSchema = COMMENT_RESPONSE_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "CreateCommentOnAnEntity",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["entityType", "entityId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Get comments activity
 */
export class GetCommentsActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get comments";

  static override description = "This activity gets comments on an entity";

  static override identifier = "communications:get-comments";

  static override requestSchema = {
    type: "object",
    properties: {
      entityType: { type: "string" },
      entityId: { type: "string" },
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
    required: ["entityType", "entityId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      comments: {
        type: "array",
        items: COMMENT_RESPONSE_SCHEMA,
      },
    },
    required: ["comments"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "GetCommentsOnAnEntity",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["entityType", "entityId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Get comment activity
 */
export class GetCommentActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get comment";

  static override description = "This activity gets a comment by ID";

  static override identifier = "communications:get-comment";

  static override requestSchema = {
    type: "object",
    properties: {
      commentId: { type: "string" },
    },
    required: ["commentId"],
  };

  static override responseSchema = COMMENT_RESPONSE_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "GetComment",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["commentId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Get comment threads activity
 */
export class GetCommentThreadsActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get comment threads";

  static override description = "This activity gets comment threads";

  static override identifier = "communications:get-comment-threads";

  static override requestSchema = {
    type: "object",
    properties: {
      commentId: { type: "string" },
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
    required: ["commentId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      comments: {
        type: "array",
        items: COMMENT_RESPONSE_SCHEMA,
      },
    },
    required: ["comments"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "GetCommentThreads",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["commentId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Update comment activity
 */
export class UpdateCommentActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update comment";

  static override description = "This activity updates a comment";

  static override identifier = "communications:update-comment";

  static override requestSchema = {
    type: "object",
    properties: {
      commentId: { type: "string" },
      content: { type: ["string", "null"] },
      threadName: { type: ["string", "null"] },
      attachments: { type: ["array", "null"], items: { type: "string" } },
      metadata: { type: ["object", "null"] },
    },
    required: ["commentId"],
  };

  static override responseSchema = COMMENT_RESPONSE_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "UpdateComment",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["commentId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Delete comment activity
 */
export class DeleteCommentActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete comment";

  static override description = "This activity deletes a comment";

  static override identifier = "communications:delete-comment";

  static override requestSchema = {
    type: "object",
    properties: {
      commentId: { type: "string" },
    },
    required: ["commentId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {},
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "DeleteComment",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

/**
 * Add reaction activity
 */
export class AddReactionActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Add reaction";

  static override description = "This activity adds a reaction to a comment";

  static override identifier = "communications:add-reaction";

  static override requestSchema = {
    type: "object",
    properties: {
      commentId: { type: "string" },
      reactionName: { type: "string" },
    },
    required: ["commentId", "reactionName"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      success: { type: "boolean" },
    },
    required: ["success"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "AddReaction",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["commentId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Remove reaction activity
 */
export class RemoveReactionActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Remove reaction";

  static override description =
    "This activity removes a reaction from a comment";

  static override identifier = "communications:remove-reaction";

  static override requestSchema = {
    type: "object",
    properties: {
      commentId: { type: "string" },
      reactionName: { type: "string" },
    },
    required: ["commentId", "reactionName"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      success: { type: "boolean" },
    },
    required: ["success"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "RemoveReaction",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["commentId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Get emojis activity
 */
export class GetEmojisActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get emojis";

  static override description = "This activity gets all available emojis";

  static override identifier = "communications:get-emojis";

  static override requestSchema = {
    type: "object",
    properties: {},
    required: [],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      emojis: {
        type: "array",
        items: {
          type: "object",
          properties: {
            name: { type: "string" },
            unicode: { type: "string" },
            shortcode: { type: "string" },
            category: { type: "string" },
            keywords: {
              type: "array",
              items: { type: "string" },
            },
          },
          required: ["name", "unicode", "shortcode", "category", "keywords"],
        },
      },
    },
    required: ["emojis"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "GetEmojis",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;
}

/**
 * Has customer comment activity
 */
export class HasCustomerCommentActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Has customer comment";

  static override description =
    "This activity checks if a customer has a comment on an entity with provided parameters";

  static override identifier = "communications:has-customer-comment";

  static override requestSchema = {
    type: "object",
    properties: {
      entityType: { type: "string" },
      entityId: { type: "string" },
      since: { type: "string" },
      parentCommentId: { type: "string" },
    },
    required: ["entityType", "entityId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      hasComment: { type: "boolean" },
    },
    required: ["hasComment"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "HasCustomerComment",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["entityType", "entityId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Has internal member comment activity
 */
export class HasInternalMemberCommentActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Has internal member comment";

  static override description =
    "This activity checks if an internal member has a comment on an entity with provided parameters";

  static override identifier = "communications:has-internal-member-comment";

  static override requestSchema = {
    type: "object",
    properties: {
      entityType: { type: "string" },
      entityId: { type: "string" },
      since: { type: "string" },
      parentCommentId: { type: "string" },
      commentType: { type: "string" },
      commentVisibility: { type: "string" },
    },
    required: ["entityType", "entityId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      hasComment: { type: "boolean" },
    },
    required: ["hasComment"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: communication.GRPC_COMMUNICATION_V1_PACKAGE_NAME,
      serviceName: communication.COMMUNICATION_SERVICE_NAME,
      methodName: "HasInternalMemberComment",
      protoPath: "dist/proto/communication/communication.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["entityType", "entityId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}
