import {
  Body,
  Controller,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Post,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Throttle, SkipThrottle } from "@nestjs/throttler";
import {
  ApiCreateEndpoint,
  ApiGetEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { GENERIC_ERROR_MESSAGES } from "@repo/thena-shared-libs";
import { THROTTLER_TIER, THROTTLER_TIER_3, THROTTLER_TIER_1, THROTTLER_TIER_2, THROTTLER_SPECIAL_TIER, THROTTLER_TIER_4 } from "../../config/throttler.config";
import { CurrentUser } from "../../common/decorators";
import { ActivitiesService } from "../services/activities.service";
import { SharedService } from "../../shared/shared.service";
import { AuditLogResponseDto } from "../transformers/audit-log.response.dto";
import { CreateAuditLogDto, GetAuditLogsQueryDto } from "../dto/activities.dto";

@ApiTags("Activities")
@Controller("v1/activities")
@SkipThrottle({ [THROTTLER_TIER_1]: true })
@SkipThrottle({ [THROTTLER_TIER_2]: true })
@SkipThrottle({ [THROTTLER_TIER_4]: true })
@SkipThrottle({ [THROTTLER_SPECIAL_TIER]: true })
@Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
@UseInterceptors(ResponseTransformInterceptor)
export class ActivitiesController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly activitiesService: ActivitiesService,
    private readonly sharedService: SharedService,
  ) {}

  @Post("/audit-logs")
  @ApiCreateEndpoint({
    summary: "Create an audit log entry",

    responseType: AuditLogResponseDto,
  })
@SkipThrottle({ [THROTTLER_TIER_1]: true })
@SkipThrottle({ [THROTTLER_TIER_2]: true })
@SkipThrottle({ [THROTTLER_TIER_4]: true })
@SkipThrottle({ [THROTTLER_SPECIAL_TIER]: true })
@Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
  async createAuditLog(
    @Body() createAuditLogDto: CreateAuditLogDto,
    @CurrentUser() user: CurrentUser,
  ): Promise<AuditLogResponseDto> {
    try {
      const organization = await this.sharedService.getOrganization(user.orgUid);

      let team;
      if (createAuditLogDto.teamId) {
        team = await this.sharedService.getTeam(
          createAuditLogDto.teamId,
          organization.id,
        );
      }
      const log = {
        ...createAuditLogDto,
        organization: { id: organization.id },
        activityPerformedBy: { id: user.sub },
        team: team ? { id: team.id } : undefined,
      };
      const auditLog = await this.activitiesService.recordAuditLog(log);
      return AuditLogResponseDto.fromEntity(auditLog);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error("Error creating audit log", error);
      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.AUDIT_LOG_CREATION_FAILED,
      );
    }
  }
  @Get("/agent/audit-logs")
  @ApiGetEndpoint({
    summary: "Get agent audit logs",

    responseType: [AuditLogResponseDto],
  })
@SkipThrottle({ [THROTTLER_TIER_1]: true })
@SkipThrottle({ [THROTTLER_TIER_2]: true })
@SkipThrottle({ [THROTTLER_TIER_4]: true })
@SkipThrottle({ [THROTTLER_SPECIAL_TIER]: true })
@Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
  async getAuditLogs(
    @Query() query: GetAuditLogsQueryDto,
    @CurrentUser() user: CurrentUser,
  ): Promise<{ data: AuditLogResponseDto[]; total: number }> {
    try {
      const organization = await this.sharedService.getOrganization(user.orgUid);
      const result = await this.activitiesService.getAuditLogs({
        ...query,
        organizationId: organization.id,
      });
      
      return {
        data: result.data.map(AuditLogResponseDto.fromEntity),
        total: result.total,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error("Error fetching audit logs", error);
      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.AUDIT_LOG_NOT_FOUND,
      );
    }
  }
  @Get("/agent/audit-logs/:id")
  @ApiGetEndpoint({
    summary: "Get agent audit log by ID",

    responseType: AuditLogResponseDto,
  })
@SkipThrottle({ [THROTTLER_TIER_1]: true })
@SkipThrottle({ [THROTTLER_TIER_2]: true })
@SkipThrottle({ [THROTTLER_TIER_4]: true })
@SkipThrottle({ [THROTTLER_SPECIAL_TIER]: true })
@Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
  async getAuditLog(
    @Param("id") id: string,
    @CurrentUser() user: CurrentUser,
  ): Promise<AuditLogResponseDto> {
    try {
      const organization = await this.sharedService.getOrganization(user.orgUid);
      const log = await this.activitiesService.getAuditLogById(id, organization.id);
      
      if (!log) {
        throw new NotFoundException("Audit log not found");
      }

      return AuditLogResponseDto.fromEntity(log);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error("Error fetching audit log", error);
      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.AUDIT_LOG_NOT_FOUND,
      );
    }
  }
  @Post("/agent/audit-logs")
  @ApiCreateEndpoint({
    summary: "Create an audit log entry for agent-studio",
    responseType: AuditLogResponseDto,
  })
@SkipThrottle({ [THROTTLER_TIER_1]: true })
@SkipThrottle({ [THROTTLER_TIER_2]: true })
@SkipThrottle({ [THROTTLER_TIER_4]: true })
@SkipThrottle({ [THROTTLER_SPECIAL_TIER]: true })
@Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
  async createPublicAuditLog(
    @Body() createAuditLogDto: CreateAuditLogDto,
  ): Promise<AuditLogResponseDto> {
    try {
      const organization = await this.sharedService.getOrganization(
        createAuditLogDto.organization,
      );
      const activityPerformedBy = await this.sharedService.getUser(
        createAuditLogDto.activityPerformedBy,
        organization.id
      );

      let team;
      if (createAuditLogDto.teamId) {
        team = await this.sharedService.getTeam(
          createAuditLogDto.teamId,
          organization.id,
        );
      }

      const log = {
        ...createAuditLogDto,
        organization: organization,
        activityPerformedBy: activityPerformedBy,
        team: team,
      };
      const auditLog = await this.activitiesService.recordAuditLog(log);
      return AuditLogResponseDto.fromEntity(auditLog);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error("Error creating public audit log", error);
      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.AUDIT_LOG_CREATION_FAILED,
      );
    }
  }
}
