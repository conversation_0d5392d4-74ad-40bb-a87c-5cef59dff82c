import { Injectable } from "@nestjs/common";
import {
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogsRepository,
  TransactionContext,
  TransactionService,
} from "@repo/thena-platform-entities";
import { DeepPartial, FindOptionsWhere } from "typeorm";

@Injectable()
export class ActivitiesService {
  constructor(
    private readonly transactionService: TransactionService,

    // Repositories
    private readonly auditLogsRepository: AuditLogsRepository,
  ) {}

  /**
   * Records an audit log.
   * @param log The audit log data to record.
   * @returns The recorded audit log data.
   */
  async recordAuditLog(
    log: DeepPartial<AuditLog>,
    existingTxnContext?: TransactionContext,
  ) {
    const auditLog = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create the audit log data
        const auditLog = this.auditLogsRepository.create(log);

        // Save the audit log
        const savedAuditLog = await this.auditLogsRepository.saveWithTxn(
          txnContext,
          auditLog,
        );

        return savedAuditLog;
      },
      existingTxnContext,
    );

    return auditLog;
  }

  async recordBulkAuditLog(
    logs: DeepPartial<AuditLog>[],
    existingTxnContext?: TransactionContext,
  ) {
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create the audit logs
        const auditLogs = logs.map((log) =>
          this.auditLogsRepository.create(log),
        );

        // Save the audit logs
        await this.auditLogsRepository.saveManyWithTxn(txnContext, auditLogs);
      },
      existingTxnContext,
    );
  }

  /**
   * Get audit logs with optional filtering
   * @param options Query options for filtering audit logs
   * @returns Audit logs and total count
   */
  async getAuditLogs(options: {
    organizationId: string;
    entityType?: AuditLogEntityType;
    entityId?: string;
    op?: AuditLogOp;
    teamId?: string;
    page?: number;
    limit?: number;
  }) {
    const { organizationId, entityType, entityId, op, teamId, page = 1, limit = 10 } = options;
    
    // Build where conditions
    const where: FindOptionsWhere<AuditLog> = {
      organization: { id: organizationId },
    };

    if (entityType) {
      where.entityType = entityType;
    }

    if (entityId) {
      where.entityId = entityId;
    }

    if (op) {
      where.op = op;
    }

    if (teamId) {
      where.team = { id: teamId };
    }

    // Get total count
    const total = await this.auditLogsRepository.count({
      where,
    });

    // Get paginated results
    const data = await this.auditLogsRepository.findAll({
      where,
      relations: ['organization', 'activityPerformedBy', 'team'],
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    return { data, total };
  }

  /**
   * Get a specific audit log by ID
   * @param id Audit log ID
   * @param organizationId Organization ID for security check
   * @returns The audit log if found
   */
  async getAuditLogById(id: string, organizationId: string) {
    const auditLog = await this.auditLogsRepository.findByCondition({
      where: {
        id,
        organization: { id: organizationId },
      },
      relations: ['organization', 'activityPerformedBy', 'team'],
    });

    if (!auditLog) {
      return null;
    }

    return auditLog;
  }
}
