import { CurrentUser } from "../../common/decorators";

export const EmittableUserEvents = {
  USER_AVAILABILITY_CHANGED: "user.availability.changed",
};

export enum UserAvailabilityChangeType {
  BUSINESS_HOURS_UPDATED = "BUSINESS_HOURS_UPDATED",
  TIME_OFF_ADDED = "TIME_OFF_ADDED",
  TIME_OFF_UPDATED = "TIME_OFF_UPDATED",
  TIME_OFF_DELETED = "TIME_OFF_DELETED",
}

export class UserAvailabilityChangedEvent {
  user: CurrentUser;
  orgId: string;
  changeType: UserAvailabilityChangeType;
  metadata?: {
    timeOffId?: string;
    previousBusinessHours?: any;
    newBusinessHours?: any;
  };
}
