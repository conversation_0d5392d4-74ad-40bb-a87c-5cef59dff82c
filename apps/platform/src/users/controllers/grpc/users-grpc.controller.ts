import { <PERSON>ada<PERSON> } from "@grpc/grpc-js";
import {
  BadRequestException,
  Controller,
  Inject,
  UseGuards,
} from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { extractUserMetadata } from "@repo/nestjs-commons/utils";
import { users } from "@repo/shared-proto";
import { UsersGrpcService } from "../../services/grpc/users-grpc.service";
import { UserAnnotatorService } from "../../services/user-annotator.service";
import { UsersService } from "../../services/users.service";

@Controller("v1/users")
export class UsersGrpcController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly userService: UsersService,
    private readonly usersGrpcService: UsersGrpcService,
    private readonly userAnnotatorService: UserAnnotatorService,
  ) {}

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(
    users.BOT_INSTALLATION_USER_SERVICE_NAME,
    "CreateBotInstallationUser",
  )
  async createBotUser(
    data: users.CreateBotInstallationUserRequest,
    metadata: Metadata,
  ) {
    try {
      const userMetadata = extractUserMetadata(metadata);

      // Create the bot user
      const { user, teams, appKey, appSecretKey } =
        await this.usersGrpcService.createBotUser(
          { ...data, organizationId: userMetadata.orgUid },
          userMetadata.token,
        );

      // Create the response
      const response: users.CreateBotInstallationUserResponse = {
        id: user.id,
        uid: user.uid,
        name: user.name,
        transactionId: user.uid,
        userType: users.UserType.BOT_USER,
        organizationId: user.organizationId,
        teamIds: teams?.map((team) => team.uid),
        accessToken: "",
        refreshToken: "",
        appKey,
        appSecretKey,
      };

      return response;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error encountered while creating bot user: ${error.message}`,
          error?.stack,
        );
      }

      handleRpcError(error);
    }
  }

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(users.BOT_INSTALLATION_USER_SERVICE_NAME, "UninstallBot")
  async uninstallBotUser(data: users.UninstallBotRequest, _metadata: Metadata) {
    try {
      // Uninstall the bot user
      await this.usersGrpcService.uninstallBotUser(data);

      const response: users.UninstallBotResponse = {
        success: true,
        message: "Bot user uninstalled successfully!",
      };

      return response;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error encountered while creating bot user: ${error.message}`,
          error?.stack,
        );
      } else {
        console.error("Error encountered while uninstalling bot user", error);
      }

      handleRpcError(error);
    }
  }

  @GrpcMethod(
    users.BOT_INSTALLATION_USER_SERVICE_NAME,
    "RollbackBotInstallationUserCreation",
  )
  async rollbackBotInstallationUserCreation(
    data: users.RollbackBotInstallationUserRequest,
  ) {
    try {
      // Rollback the bot installation user creation
      await this.usersGrpcService.rollbackBotInstallation(data);

      // Create the response
      const response: users.RollbackBotInstallationUserResponse = {
        success: true,
        message: "Bot installation user creation rolled back successfully!",
        teamIds: data.teamIds,
        status: users.RollbackStatus.ROLLBACK_STATUS_SUCCESS,
      };

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.BOT_INSTALLATION_USER_SERVICE_NAME, "RollbackBotUninstall")
  async rollbackBotUninstall(data: users.RollbackBotUninstallRequest) {
    try {
      // Rollback the bot uninstall
      await this.usersGrpcService.rollbackBotUninstall(data);

      const response: users.RollbackBotUninstallResponse = {
        success: true,
        message: "Bot uninstall rolled back successfully!",
      };

      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_SERVICE_NAME, "GetOrganizationAdmin")
  async getOrganizationAdmin(
    data: users.GetOrganizationAdminRequest,
  ): Promise<users.GetOrganizationAdminResponse> {
    try {
      const orgAdmin = await this.userService.findOrganizationAdmin(
        data.organizationId,
      );

      return {
        userId: orgAdmin?.uid,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, "GetUserFieldMetadata")
  @UseGuards(GrpcAuthGuard)
  async getUserFieldMetadata(
    request: users.GetUserFieldMetadataRequest,
    metadata: Metadata,
  ): Promise<users.GetUserFieldMetadataResponse> {
    try {
      const currentUser = extractUserMetadata(metadata);
      return await this.userAnnotatorService.getUserFieldMetadata(
        request?.teamId,
        currentUser,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, "GetUserSkillsFieldMetadata")
  @UseGuards(GrpcAuthGuard)
  getUserSkillsFieldMetadata(): users.GetUserSkillsFieldMetadataResponse {
    try {
      return this.userAnnotatorService.getUserSkillsFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    users.USER_ANNOTATOR_SERVICE_NAME,
    "GetUserBusinessHoursConfigFieldMetadata",
  )
  @UseGuards(GrpcAuthGuard)
  getUserBusinessHoursConfigFieldMetadata(): users.GetUserBusinessHoursConfigFieldMetadataResponse {
    try {
      return this.userAnnotatorService.getUserBusinessHoursConfigFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, "GetTimeOffFieldMetadata")
  @UseGuards(GrpcAuthGuard)
  getTimeOffFieldMetadata(): users.GetTimeOffFieldMetadataResponse {
    try {
      return this.userAnnotatorService.getTimeOffFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, "GetUserData")
  @UseGuards(GrpcAuthGuard)
  async getUserData(
    request: users.GetUserDataRequest,
    metadata: Metadata,
  ): Promise<users.GetUserDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.userAnnotatorService.getUserData(
        request.userId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, "GetUserSkillsData")
  @UseGuards(GrpcAuthGuard)
  async getUserSkillsData(
    request: users.GetUserSkillsDataRequest,
    metadata: Metadata,
  ): Promise<users.GetUserSkillsDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.userAnnotatorService.getUserSkillsData(
        request.userId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    users.USER_ANNOTATOR_SERVICE_NAME,
    "GetUserBusinessHoursConfigData",
  )
  @UseGuards(GrpcAuthGuard)
  async getUserBusinessHoursConfigData(
    request: users.GetUserBusinessHoursConfigDataRequest,
    metadata: Metadata,
  ): Promise<users.GetUserBusinessHoursConfigDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.userAnnotatorService.getUserBusinessHoursConfigData(
        request.userId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_ANNOTATOR_SERVICE_NAME, "GetTimeOffData")
  @UseGuards(GrpcAuthGuard)
  async getTimeOffData(
    request: users.GetTimeOffDataRequest,
    metadata: Metadata,
  ): Promise<users.GetTimeOffDataResponse> {
    try {
      const { orgId } = extractUserMetadata(metadata);
      return await this.userAnnotatorService.getTimeOffData(
        request.userId,
        request.relations,
        orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(users.USER_SERVICE_NAME, "FetchWorkflowBot")
  async fetchWorkflowBot(request: users.Empty, metadata: Metadata) {
    try {
      const { orgId } = extractUserMetadata(metadata);

      const workflowBot = await this.userService.fetchWorkflowBot(orgId);

      return {
        id: workflowBot.uid,
        name: workflowBot.name,
        email: workflowBot.email,
        userType: users.UserType.BOT_USER,
        organizationId: workflowBot.organizationId,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(users.USER_SERVICE_NAME, "CheckUserAvailability")
  async checkUserAvailability(
    data: users.CheckUserAvailabilityRequest,
    metadata: Metadata,
  ): Promise<users.CheckUserAvailabilityResponse> {
    try {
      const user = extractUserMetadata(metadata);

      if (!data.userId) {
        throw new BadRequestException("User ID is required!");
      }

      return await this.userService.checkUserAvailability(user, data.userId);
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(users.USER_SERVICE_NAME, "GetUsersDetails")
  async getUsersDetails(
    data: users.GetUsersDetailsRequest,
  ): Promise<users.GetUsersDetailsResponse> {
    try {
      const { userIds } = data;

      // Return empty response if no userIds provided
      if (!userIds || userIds.length === 0) {
        return { users: [] };
      }

      // Fetch users from the database
      const userDetails = await this.userService.findUsersByIds(userIds);

      // Map to the response format
      return {
        users: userDetails.map((user) => ({
          name: user.name,
          avatar: user.avatarUrl || user.organization?.logoUrl,
        })),
      };
    } catch (error) {
      handleRpcError(error);
    }
  }
}
