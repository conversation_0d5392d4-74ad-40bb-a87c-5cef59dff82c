import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { RedisCacheProvider } from "@repo/nestjs-commons/cache";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  BusinessHoursConfigRepository,
  CachedBusinessHoursConfigRepository,
  CachedUserRepository,
  IDX_UNIQUE_USER_SKILL_ORG_CONSTRAINT,
  Organization,
  Tag,
  TimeOff,
  TimeOffRepository,
  TransactionContext,
  TransactionService,
  User,
  UserRepository,
  UserSkillsRepository,
  UserStatus,
  UserType,
} from "@repo/thena-platform-entities";
import { cloneDeep } from "lodash";
import {
  Between,
  DataSource,
  DeepPartial,
  FindOptionsRelations,
  FindOptionsSelect,
  FindOptionsWhere,
  In,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hanOrEqual,
  Like,
  MoreThan<PERSON>rEqual,
  Not,
  QueryFailedError,
  Raw,
} from "typeorm";
import { IdGeneratorUtils } from "../../common";
import { CACHE_TTL } from "../../common/constants/cache.constants";
import { POSTGRES_ERROR_CODES } from "../../common/constants/postgres-errors.constants";
import { CurrentUser } from "../../common/decorators";
import {
  BusinessDayDto,
  BusinessHoursConfigDto,
  UpdateTimezoneWorkingHoursDto,
} from "../../common/dto";
import { BusinessHoursValidatorService } from "../../common/services/business-hours-validation.service";
import { constructDailyConfigFromCommonSlots } from "../../common/utils/time-slots.utils";
import { SharedService } from "../../shared/shared.service";
import { SELECT_FROM_USERS } from "../constants";
import {
  CreateTimeOffDto,
  GetAllTimeOffsQuery,
  UpdateTimeOffDto,
} from "../dto/time-off.dto";
import {
  CreateOrganizationAdminDto,
  CreateUserDto,
  UpdateUserDto,
  UpdateUserWorkloadDto,
} from "../dto/users.dto";
import { EmittableUserEvents, UserAvailabilityChangedEvent, UserAvailabilityChangeType } from "../events/users.events";
import { UserAvailabilityResponseDto } from "../transformers";
import { AuthGrpcClient } from "../utils";
import { AuthHeaders } from "../utils/user-grpc-client.interface";

@Injectable()
export class UsersService {
  constructor(
    @Inject("CustomLogger")
    private readonly logger: ILogger,

    // User repositories
    private readonly userRepository: UserRepository,
    private readonly cachedUserRepository: CachedUserRepository,

    // gRPC client
    private readonly authGrpcClient: AuthGrpcClient,

    // Business hours configuration
    private readonly businessHoursValidationService: BusinessHoursValidatorService,
    private readonly businessHoursConfigRepository: BusinessHoursConfigRepository,
    private readonly cachedBusinessHoursConfigRepository: CachedBusinessHoursConfigRepository,

    // User skills repository
    private readonly userSkillsRepository: UserSkillsRepository,

    // Time off repository
    private readonly timeOffRepository: TimeOffRepository,

    // Injected services
    private readonly transactionService: TransactionService,
    private readonly cacheProvider: RedisCacheProvider,

    // Shared module
    private readonly sharedService: SharedService,

    // Database Utilities
    private readonly datasource: DataSource,

    // Event emitter
    private readonly eventEmitter: EventEmitter2,
  ) {}

  queryUsers(
    where: FindOptionsWhere<User>,
    relations?: FindOptionsRelations<User>,
  ) {
    return this.userRepository.findAll({
      where,
      relations: relations || {},
    });
  }

  /**
   * Finds a user by their ID.
   * @param id The ID of the user to find.
   * @returns A promise that resolves to the user object if found, or undefined if not found.
   */
  findOne(id: string): Promise<User | undefined> {
    this.logger.log(`Finding user by ID: ${id}`);
    return this.cachedUserRepository.findOneById(id);
  }

  /**
   * Finds a user by their user ID.
   * @param userId The ID of the user to find.
   * @returns A promise that resolves to the user object if found, or undefined if not found.
   */
  findOneByUserId(userId: string): Promise<User | undefined> {
    this.logger.log(`Finding user by user ID: ${userId}`);
    return this.userRepository.findByCondition({
      where: { uid: userId, userType: In([UserType.USER, UserType.ORG_ADMIN]) },
      relations: ["organization"],
    });
  }

  /**
   * Finds a user by their public ID.
   * @param id The public ID of the user to find.
   * @returns A promise that resolves to the user object if found, or undefined if not found.
   */
  findOneByPublicId(id: string, orgId?: string): Promise<User | undefined> {
    this.logger.log(
      `Finding user by public ID: ${id}${
        orgId ? ` in organization ${orgId}` : ""
      }`,
    );
    const whereClause: FindOptionsWhere<User> = { uid: id, isActive: true };

    // If the organization ID is provided, add it to the where clause
    if (orgId) {
      whereClause.organizationId = orgId;
    }

    return this.userRepository.findByCondition({
      where: whereClause,
      relations: ["organization"],
    });
  }

  /**
   * Finds a user by their email address.
   * @param email The email address of the user to find.
   * @returns A promise that resolves to the user object if found, or undefined if not found.
   */
  findOneByEmail(email: string, orgId?: string): Promise<User | undefined> {
    this.logger.log(
      `Finding user by email: ${email}${
        orgId ? ` in organization ${orgId}` : ""
      }`,
    );
    const whereClause: FindOptionsWhere<User> = { email };

    // If the organization ID is provided, add it to the where clause
    if (orgId) {
      whereClause.organizationId = orgId;
    }

    return this.userRepository.findByCondition({
      where: whereClause,
      relations: ["organization"],
      select: {
        id: true,
        authId: true,
        uid: true,
        email: true,
        organization: { uid: true, id: true, tier: true },
        userType: true,
        timezone: true,
      },
    });
  }

  /**
   * Finds all users by their email domain.
   * @param domain The email domain to find the users by.
   * @param organizationId The ID of the organization to find the users in.
   * @returns The users.
   */
  findAllUsersByEmailDomain(
    domain: string,
    organizationId: string,
  ): Promise<User[]> {
    this.logger.log(
      `Finding all users with email domain: ${domain} in organization ${organizationId}`,
    );
    return this.cachedUserRepository.findAll({
      where: { email: Like(`%@${domain}`), organizationId, isActive: true },
    });
  }

  /**
   * Checks if a user is part of an organization.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @returns True if the user is part of the organization, false otherwise.
   */
  isUserPartOfOrganization(
    details: { userId?: string; email?: string },
    orgId: string,
    options?: { exists?: boolean },
  ) {
    this.logger.log(
      `Checking if user ${
        details.userId || details.email
      } is part of organization ${orgId}`,
    );
    const whereClause: FindOptionsWhere<User> = { organizationId: orgId };

    // If the user ID is provided, add it to the where clause
    if (details.userId) {
      whereClause.id = details.userId;
    }

    // If the email is provided, add it to the where clause
    if (details.email) {
      whereClause.email = details.email;
    }

    if (options?.exists) {
      return this.cachedUserRepository.exists({
        where: whereClause,
      });
    }

    return this.cachedUserRepository.findByCondition({
      where: whereClause,
    });
  }

  /**
   * Finds all users by their public IDs.
   * @param ids The public IDs of the users to find.
   * @returns The users.
   */
  findManyByPublicIds(ids: string[]): Promise<User[]> {
    this.logger.log(`Finding users by public IDs: ${ids.join(", ")}`);
    return this.userRepository.findAll({
      where: { uid: In(ids), isActive: true },
    });
  }

  getBotDetails(botId: string) {
    this.logger.log(`Getting bot details for ID: ${botId}`);
    return this.userRepository.findByCondition({
      where: { uid: botId, userType: UserType.BOT_USER },
    });
  }

  async setSlackAuth(
    user: CurrentUser,
    userEmail: string,
    teamId: string,
    unset?: string,
  ) {
    this.logger.log(`Setting slack auth for user: ${userEmail}`);

    // If the user is not a bot user, throw an error, only bots can update this
    // TODO: We need to add a check if this is being done by a privileged bot
    if (user.userType !== UserType.BOT_USER) {
      throw new ForbiddenException(
        "You are not authorized to perform this operation",
      );
    }

    // Fetch the user
    const encodedEmail = userEmail.replace(" ", "+");
    const foundUser = await this.userRepository.findByCondition({
      where: { email: encodedEmail, organizationId: user.orgId },
    });

    // If the user is not found, throw an error
    if (!foundUser) {
      throw new NotFoundException(
        `User with email ${encodedEmail} not found in organization ${user.orgId}`,
      );
    }

    // Update the user's metadata
    await this.userRepository.update(foundUser.id, {
      metadata: {
        ...foundUser.metadata,
        slackAuthSink: {
          ...foundUser.metadata?.slackAuthSink,
          [teamId]: unset ? false : true,
        },
      },
    });

    this.logger.log(`Successfully set slack auth for user: ${encodedEmail}`);
  }

  async setUserApiKey(
    user: User,
    secretKey: string,
    txnContext?: TransactionContext,
  ) {
    this.logger.log(`Setting API key for user: ${user.uid}`);
    const updateUser = await this.userRepository.findByCondition({
      withDeleted: true,
      where: { id: user.id },
      select: ["id", "metadata"],
    });

    // If the user is not found, throw an error
    if (txnContext) {
      return this.userRepository.updateWithTxn(
        txnContext,
        { id: updateUser.id },
        { metadata: { ...updateUser.metadata, secretKey } },
      );
    }

    // Update the user's metadata
    return this.userRepository.update(updateUser.id, {
      metadata: { ...updateUser.metadata, secretKey },
    });
  }

  /**
   * Updates a user.
   * @param user The current user.
   * @param body The body of the user.
   * @returns The updated user.
   */
  async updateUser(
    user: { sub: string; uid: string; orgId: string },
    body: UpdateUserDto,
  ) {
    this.logger.log(`Updating user ${user.uid} with name: ${body.name}`);
    const { name, avatarUrl } = body;

    try {
      // Update the user
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.userRepository.updateWithTxn(
          txnContext,
          { id: user.sub, organizationId: user.orgId },
          { name, avatarUrl },
        );
      });

      // Return the updated user
      const updatedUser = await this.findOneByPublicId(user.uid, user.orgId);
      this.logger.log(`Successfully updated user ${user.uid}`);
      return updatedUser;
    } catch (error) {
      this.logger.error(`Failed to update user ${user.uid}:`, error);
      throw error;
    }
  }

  /**
   * Lists all users for a given organization.
   * @param user The current user.
   * @returns The users.
   */
  async listUsers(user: CurrentUser) {
    this.logger.log(`Listing all users for organization ${user.orgId}`);
    const users = await this.userRepository.findAll({
      where: { organizationId: user.orgId },
      select: SELECT_FROM_USERS,
    });

    this.logger.log(
      `Found ${users.length} users in organization ${user.orgId}`,
    );
    return users;
  }

  /**
   * Finds all organizations for a user.
   * @param user The current user.
   * @returns The organizations.
   */
  async findAllOrgsForUser(user: CurrentUser): Promise<Organization[]> {
    this.logger.log(`Finding all organizations for user ${user.uid}`);
    // Fetch the orgs the user is part of
    const orgs = await this.userRepository.findAll({
      where: { id: user.sub },
      relations: { organization: true },
    });

    this.logger.log(`Found ${orgs.length} organizations for user ${user.uid}`);
    return orgs.map((u) => u.organization);
  }

  /**
   * Finds all organizations for a user by their email.
   * @param email The email of the user.
   * @param select The select object.
   * @returns The organizations.
   */
  async findAllOrgsForUserEmail(
    email: string,
    select?: FindOptionsSelect<User>,
  ) {
    this.logger.log(`Finding all organizations for user email: ${email}`);
    const orgs = await this.userRepository.findAll({
      where: { email },
      relations: { organization: true },
      select,
    });

    this.logger.log(`Found ${orgs.length} organizations for email ${email}`);
    return orgs.map((u) => u.organization);
  }

  /**
   * Gets a user by their user ID.
   * @param userId The ID of the user.
   * @returns The user.
   */
  getUserByUserId(userId: string | Array<string>, organizationId: string) {
    this.logger.log(
      `Getting user(s) by ID: ${
        Array.isArray(userId) ? userId.join(", ") : userId
      } in organization ${organizationId}`,
    );
    return this.cachedUserRepository.findAll({
      where: {
        uid: In(Array.isArray(userId) ? userId : [userId]),
        organizationId,
      },
    });
  }

  /**
   * @internal
   * Checks if the user can attach skills.
   * @param user The current user.
   * @returns True if the user can attach skills, false otherwise.
   */
  private canManageSkills(user: CurrentUser) {
    this.logger.log(`Checking if user ${user.uid} can manage skills`);
    // If the user is not an organization admin, throw an error
    if (user.userType !== UserType.ORG_ADMIN) {
      this.logger.warn(
        `User ${user.uid} attempted to manage skills without admin privileges`,
      );
      throw new ForbiddenException(
        "You are not authorized to perform this operation",
      );
    }

    return true;
  }

  /**
   * Gets the supabase user.
   * @param token The token to use.
   * @returns The supabase user.
   */
  getSupabaseUser(token: string) {
    this.logger.log("Getting supabase user");
    return this.authGrpcClient.getSupabaseUser(token);
  }

  async createOrganizationAdmin(
    user: CreateOrganizationAdminDto,
  ): Promise<User> {
    this.logger.log(`Creating organization admin with email: ${user.email}`);
    try {
      const rpcUser = await this.authGrpcClient.createOrganizationAdmin({
        email: user.email,
        password: user.password,
        organizationUid: user.organizationUid,
      });

      // Fetch the created user
      const createdUser = await this.cachedUserRepository.findByCondition({
        where: { uid: rpcUser.id, organization: { uid: user.organizationUid } },
        relations: { organization: true },
        select: {
          id: true,
          uid: true,
          name: true,
          authId: true,
          organization: { uid: true },
          email: true,
          userType: true,
          isActive: true,
          status: true,
        },
      });

      this.logger.log(
        `Successfully created organization admin: ${createdUser.uid}`,
      );
      return createdUser;
    } catch (error) {
      this.logger.error(
        `Failed to create organization admin for email ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Creates a new user.
   * @param user The user object to create.
   * @returns A promise that resolves to the created user object.
   */
  async createBotUser(
    user: CreateUserDto,
    headers: AuthHeaders,
  ): Promise<User> {
    this.logger.log(`Creating bot user with email: ${user.email}`);
    try {
      // Create the user
      const rpcUser = await this.authGrpcClient.createBotUser(headers, {
        name: user.name,
        email: user.email,
        password: user.password,
        organizationUid: user.organizationUid,
        appId: user.appId,
        purpose: user.purpose,
        avatarUrl: user.avatarUrl,
      });

      // Fetch the created user
      const createdUser = await this.cachedUserRepository.findByCondition({
        where: { uid: rpcUser.id },
      });

      this.logger.log(`Successfully created bot user: ${createdUser.uid}`);
      return createdUser;
    } catch (error) {
      this.logger.error(
        `Failed to create bot user for email ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Creates a new user.
   * @param user The user object to create.
   * @returns A promise that resolves to the created user object.
   */
  async create(user: CreateUserDto, headers: AuthHeaders): Promise<User> {
    this.logger.log(`Creating user with email: ${user.email}`);
    try {
      // Create the user
      const rpcUser = await this.authGrpcClient.createUser(headers, {
        name: user.name,
        email: user.email,
        password: user.password,
        organizationUid: user.organizationUid,
        isCustomer: user.userType === UserType.CUSTOMER_USER,
        externalId: user.externalId,
      });

      // Fetch the created user
      const createdUser = await this.cachedUserRepository.findByCondition({
        where: { uid: rpcUser.id },
      });

      this.logger.log(`Successfully created user: ${createdUser.uid}`);
      return createdUser;
    } catch (error) {
      this.logger.error(
        `Failed to create user for email ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Creates a new user.
   * @param user The user object to create.
   * @returns A promise that resolves to the created user object.
   */
  async createUserWithoutAuth(user: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user without auth with email: ${user.email}`);
    try {
      // Create the user
      const rpcUser = await this.authGrpcClient.createUserWithoutAuth({
        name: user.name,
        email: user.email,
        password: user.password,
        organizationUid: user.organizationUid,
        isCustomer: user.userType === UserType.CUSTOMER_USER,
      });

      // Fetch the created user
      const createdUser = await this.cachedUserRepository.findByCondition({
        where: { uid: rpcUser.id },
      });

      this.logger.log(
        `Successfully created user without auth: ${createdUser.uid}`,
      );
      return createdUser;
    } catch (error) {
      this.logger.error(
        `Failed to create user without auth for email ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Creates a user persona.
   * @param user The user to create.
   * @returns The created user.
   */
  async createUserPersona(user: DeepPartial<User>) {
    this.logger.log(`Creating user persona with type: ${user.userType}`);
    try {
      const uid = this.generateUserIdentifier(user.userType);

      // Get the user metadata
      let userMetadata: Record<string, string> = {};
      try {
        // Fetch the auth user
        const qr = this.datasource.createQueryRunner();
        const res = await qr.query(`SELECT * FROM auth.users WHERE id = $1`, [
          user.authId,
        ]);

        // Release the query runner
        qr.release();

        const authUser = res[0];
        userMetadata = authUser?.raw_user_meta_data ?? {};
      } catch (qrError) {
        console.error("[CONSOLE_ERROR] Failed to fetch auth user:", qrError);
        this.logger.error(`Failed to fetch auth user:`, qrError);
      }

      // Get and set the user name
      const userName = userMetadata?.name ?? user.name;

      // Create the user
      const createdUser = this.userRepository.create({
        ...user,
        name: userName,
        uid,
      });

      const savedUser = await this.userRepository.save(createdUser);

      this.logger.log(`Successfully created user persona: ${savedUser.uid}`);
      return savedUser;
    } catch (error) {
      this.logger.error(`Failed to create user persona:`, error);
      throw error;
    }
  }

  async updateUserAvailability(user: CurrentUser, availability: UserStatus) {
    this.logger.log(
      `Updating availability for user ${user.uid} to ${availability}`,
    );
    try {
      await this.userRepository.update(
        { id: user.sub },
        { status: availability },
      );

      this.logger.log(`Successfully updated availability for user ${user.uid}`);
      return { ok: true };
    } catch (error) {
      this.logger.error(
        `Failed to update availability for user ${user.uid}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Updates a user's business hours.
   * @param user The current user.
   * @param body The updates to perform on the user.
   * @returns A promise that resolves to the updated user and business hours configuration.
   */
  async updateBusinessHours(
    user: CurrentUser,
    body: UpdateTimezoneWorkingHoursDto,
  ) {
    this.logger.log(`Updating business hours for user ${user.uid}`);
    const { timezone, dailyConfig, commonSlots, commonDailyConfig } = body;

    // Find the user's business hours configuration
    const currentUser = await this.cachedUserRepository.findOneById(user.sub);
    if (!currentUser) {
      this.logger.warn(`User ${user.uid} not found`);
      throw new NotFoundException("User not found!");
    }

    // If the common daily config is enabled, validate configurations
    if (commonDailyConfig) {
      // If the common slots are not provided, throw an error
      if (!commonSlots) {
        this.logger.warn(
          "Common slots required but not provided for common daily config",
        );
        throw new BadRequestException(
          "Common slots are required when common daily config is enabled!",
        );
      }

      // If the daily config is provided, throw an error
      if (dailyConfig) {
        this.logger.warn("Both common daily config and daily config provided");
        throw new BadRequestException(
          "Cannot update both common daily config and daily config!",
        );
      }
    }

    try {
      // If the business hours are being updated, validate them
      if (dailyConfig) {
        this.logger.log("Validating business hours configuration");
        const tz = timezone || currentUser.timezone || "UTC";
        const validationResult =
          this.businessHoursValidationService.validateBusinessHours(
            dailyConfig,
            tz,
          );

        // If the business hours are invalid, throw an error
        if (!validationResult.isValid) {
          this.logger.warn(
            `Invalid business hours configuration: ${validationResult.error}`,
          );
          throw new BadRequestException(validationResult.error);
        }
      }

      // Check if the user already has a business hours configuration
      const existingBusinessHoursConfig =
        await this.cachedBusinessHoursConfigRepository.findByCondition({
          where: { user: { id: user.sub } },
        });

      // Update the user's timezone and business hours in a transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Update the user's timezone
        if (timezone) {
          this.logger.log(`Updating timezone to: ${timezone}`);
          await this.userRepository.updateWithTxn(
            txnContext,
            { id: user.sub },
            { timezone },
          );
        }

        // Update the user's business hours
        if (dailyConfig) {
          if (!existingBusinessHoursConfig) {
            this.logger.log("Creating new business hours configuration");
            // Create a new business hours configuration
            const newBusinessHoursConfig =
              await this.businessHoursConfigRepository.saveWithTxn(txnContext, {
                ...dailyConfig,
                user: currentUser,
                organizationId: user.orgId,
              });

            // Update the user's business hours configuration
            await this.userRepository.updateWithTxn(
              txnContext,
              { id: user.sub },
              { businessHoursConfig: { id: newBusinessHoursConfig.id } },
            );
          } else {
            this.logger.log("Updating existing business hours configuration");
            // Update the existing business hours configuration
            await this.businessHoursConfigRepository.updateWithTxn(
              txnContext,
              { user: { id: user.sub } },
              { ...dailyConfig, commonDailyConfig: false },
            );
          }

          this.logger.log("Invalidating business hours cache");
          // Invalidate the user's business hours cache
          await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
            { userId: user.sub },
          );
        }

        // If the common daily config is enabled, update the configurations
        if (commonDailyConfig && commonSlots) {
          this.logger.log("Updating common daily config");
          const dailyConfig = constructDailyConfigFromCommonSlots(commonSlots);
          await this.businessHoursConfigRepository.updateWithTxn(
            txnContext,
            { user: { id: user.sub } },
            { ...dailyConfig, commonDailyConfig },
          );

          this.logger.log("Invalidating business hours cache");
          // Invalidate the user's business hours cache
          await this.cachedBusinessHoursConfigRepository.invalidateBusinessHoursCache(
            { userId: user.sub },
          );
        }
      });

      const availabilityEvent = new UserAvailabilityChangedEvent();
      availabilityEvent.user = user;
      availabilityEvent.orgId = user.orgId;
      availabilityEvent.changeType = UserAvailabilityChangeType.BUSINESS_HOURS_UPDATED;
      availabilityEvent.metadata = {
        newBusinessHours: dailyConfig || commonSlots,
      };

      this.eventEmitter.emit(
        EmittableUserEvents.USER_AVAILABILITY_CHANGED,
        availabilityEvent,
      );

      // Find the updated user and business hours configuration
      const [updatedUser, businessHoursConfig] = await Promise.all([
        this.findOneByUserId(currentUser.uid),
        this.cachedBusinessHoursConfigRepository.findByCondition({
          where: { user: { id: user.sub } },
        }),
      ]);

      this.logger.log(
        `Successfully updated business hours for user ${user.uid}`,
      );
      return {
        userId: currentUser.uid,
        user: updatedUser,
        businessHoursConfig,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update business hours for user ${user.uid}:`,
        error,
      );
      throw error;
    }
  }

  async getUserDetails(user: CurrentUser) {
    this.logger.log(`Getting details for user ${user.uid}`);
    const [updatedUser, businessHoursConfig] = await Promise.all([
      this.findOneByUserId(user.uid),
      this.cachedBusinessHoursConfigRepository.findByCondition({
        where: { user: { id: user.sub } },
      }),
    ]);

    return {
      userId: user.uid,
      user: { ...updatedUser, businessHoursConfig },
    };
  }

  /**
   * Updates a user's workload.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @param updatePayload The payload to update the user's workload with.
   */
  public async updateUserWorkload(
    userId: string,
    orgId: string,
    updatePayload: UpdateUserWorkloadDto,
  ) {
    this.logger.log(
      `Updating workload for user ${userId} in team ${updatePayload.forTeamId}`,
    );
    // Fetch the user
    const user = await this.cachedUserRepository.findByCondition({
      where: { id: userId, organizationId: orgId },
    });

    // If the user is not found, throw an error
    if (!user) {
      this.logger.warn(`User ${userId} not found`);
      throw new NotFoundException("User not found!");
    }

    try {
      await this.transactionService.runInTransaction(async (txnContext) => {
        const updatedPayload = cloneDeep(user.metadata?.workload || {});
        updatedPayload[updatePayload.forTeamId] = {
          count: updatePayload.newTotalTickets,
        };

        // Update the user's workload
        await this.userRepository.updateWithTxn(
          txnContext,
          { id: userId },
          { metadata: { workload: updatedPayload } },
        );
      });

      this.logger.log(`Successfully updated workload for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to update workload for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Gets all time offs for a user.
   * @param user The current user.
   * @param query The query object.
   * @returns All time offs for the user.
   */
  public async getAllTimeOffs(user: CurrentUser, query: GetAllTimeOffsQuery) {
    this.logger.log(`Getting all time offs for user ${user.uid}`);

    // Validate the query
    const limit = Math.min(Math.max(query.limit || 50, 1), 100);

    // Fetch the time offs
    const timeOffs = await this.timeOffRepository.fetchPaginatedResults(
      { page: query.page || 0, limit },
      {
        where: { user: { id: user.sub } },
        order: { createdAt: "DESC" },
      },
    );

    this.logger.log(
      `Found ${timeOffs.results.length} time offs for user ${user.uid}`,
    );

    return timeOffs;
  }

  /**
   * Updates a user's time off.
   * @param user The current user.
   * @param payload The payload to update the user's time off with.
   */
  public async addTimeOff(user: CurrentUser, timeOffData: CreateTimeOffDto) {
    this.logger.log(`Adding time off for user ${user.uid}`);
    try {
      // First check for any overlapping time off entries
      const existingTimeOff = await this.hasOverlappingTimeOffs(
        user.sub,
        new Date(timeOffData.startDate),
        new Date(timeOffData.endDate),
      );

      // If there is already an overlapping time off entry, throw an error
      if (existingTimeOff) {
        this.logger.warn(`Overlapping time off found for user ${user.uid}`);
        throw new BadRequestException(
          "There is already a time off entry that overlaps with the requested dates",
        );
      }

      // Create the time off entry
      const timeOff = await this.timeOffRepository.createTimeOff({
        user: { id: user.sub },
        startDate: new Date(timeOffData.startDate),
        endDate: new Date(timeOffData.endDate),
        description: timeOffData.description,
        type: timeOffData.type,
        organization: { id: user.orgId },
      });

      this.logger.log(`Successfully added time off for user ${user.uid}`);

      const availabilityEvent = new UserAvailabilityChangedEvent();
      availabilityEvent.user = user;
      availabilityEvent.orgId = user.orgId;
      availabilityEvent.changeType = UserAvailabilityChangeType.TIME_OFF_ADDED;
      availabilityEvent.metadata = {
        timeOffId: timeOff.id,
      };

      this.eventEmitter.emit(
        EmittableUserEvents.USER_AVAILABILITY_CHANGED,
        availabilityEvent,
      );

      return timeOff;
    } catch (error) {
      this.logger.error(`Failed to add time off for user ${user.uid}:`, error);
      throw error;
    }
  }

  /**
   * Gets all skills for a user.
   * @param user The current user.
   * @param userId The ID of the user to fetch the skills for.
   * @returns All skills for the user.
   */
  public async getUserSkills(user: CurrentUser, userId: string) {
    this.logger.log(`Getting skills for user ${userId}`);
    // Fetch user
    const requestedUser = await this.cachedUserRepository.exists({
      where: { uid: userId, organizationId: user.orgId },
    });

    // If the user does not exist, throw an error
    if (!requestedUser) {
      this.logger.warn(`User ${userId} not found`);
      throw new NotFoundException("User not found!");
    }

    const cacheKey = `user-skills:${userId}:${user.orgId}`;
    const cachedUserSkills = await this.cacheProvider.get<string>(cacheKey);

    // If the user skills are cached, return them
    if (cachedUserSkills) {
      this.logger.log(`Retrieved cached skills for user ${userId}`);
      return JSON.parse(cachedUserSkills) as Tag[];
    }

    // Fetch the user skills
    const junctionRecords = await this.userSkillsRepository.findAll({
      where: {
        user: { uid: userId },
        organization: { id: user.orgId },
      },
      relations: ["skill"],
    });

    // Map the junction records to the skill IDs
    const userSkills = junctionRecords.map((record) => record.skill);

    // Cache the user skills
    await this.cacheProvider.set(
      cacheKey,
      JSON.stringify(userSkills),
      CACHE_TTL.MONTH * 2,
    );

    this.logger.log(`Found ${userSkills.length} skills for user ${userId}`);
    return userSkills;
  }

  /**
   * Attaches a skill to a user.
   * @param user The current user.
   * @param skillId The ID of the skill to attach.
   * @param userId The ID of the user to attach the skill to.
   */
  public async attachSkillToUser(
    user: CurrentUser,
    skillId: string,
    userId: string,
  ) {
    this.logger.log(`Attaching skill ${skillId} to user ${userId}`);
    // Check if the user can manage skills
    this.canManageSkills(user);

    // Fetch user
    const requestedUser = await this.cachedUserRepository.findByCondition({
      where: { uid: userId, organizationId: user.orgId },
    });

    // If the user does not exist, throw an error
    if (!requestedUser) {
      this.logger.warn(`User ${userId} not found`);
      throw new NotFoundException("User not found!");
    }

    // Find the skill or throw an error if it's not found
    const skill = await this.sharedService.findOneTag(skillId, user);
    if (!skill) {
      this.logger.warn(`Skill ${skillId} not found`);
      throw new NotFoundException("Provided skill was not found!");
    }

    // Try and attach the skill to the user in a transaction
    try {
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Attach the skill to the user
        await this.userSkillsRepository.saveWithTxn(txnContext, {
          skill: { id: skill.id },
          user: { id: requestedUser.id },
          organization: { id: user.orgId },
        });

        this.logger.log("Invalidating user skills cache");
        // Delete the cached user skills
        await this.cacheProvider.del(`user-skills:${userId}:${user.orgId}`);
      });

      this.logger.log(
        `Successfully attached skill ${skillId} to user ${userId}`,
      );
    } catch (error) {
      // Handle errors thrown by the database
      if (error instanceof QueryFailedError) {
        if (error.code === POSTGRES_ERROR_CODES.DUPLICATE_KEY_VALUE) {
          const constraint = (error as any)?.constraint;
          // If the error is due to duplicate skill assignment for the user, throw an error
          if (constraint === IDX_UNIQUE_USER_SKILL_ORG_CONSTRAINT) {
            this.logger.warn(
              `Skill ${skillId} already attached to user ${userId}`,
            );
            throw new ConflictException("Skill already attached to user!");
          }
        }
      }

      this.logger.error(
        `Failed to attach skill ${skillId} to user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Detaches a skill from a user.
   * @param user The current user.
   * @param skillId The ID of the skill to detach.
   * @param userId The ID of the user to detach the skill from.
   */
  public async detachSkillFromUser(
    user: CurrentUser,
    skillId: string,
    userId: string,
  ) {
    this.logger.log(`Detaching skill ${skillId} from user ${userId}`);
    // Check if the user can manage skills
    this.canManageSkills(user);

    // Fetch user
    const requestedUser = await this.cachedUserRepository.exists({
      where: { uid: userId, organizationId: user.orgId },
    });

    // If the user does not exist, throw an error
    if (!requestedUser) {
      this.logger.warn(`User ${userId} not found`);
      throw new NotFoundException("User not found!");
    }

    // Find the user skills junction record
    const userSkillsJunction = await this.userSkillsRepository.findByCondition({
      where: {
        skill: { uid: skillId },
        user: { uid: userId },
        organization: { id: user.orgId },
      },
    });

    // If the user skills junction record does not exist, throw an error
    if (!userSkillsJunction) {
      this.logger.warn(`Skill ${skillId} not found on user ${userId}`);
      throw new NotFoundException("Provided skill not found on the user!");
    }

    try {
      // Remove the user skills junction record in a transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Delete the user skills junction record
        await this.userSkillsRepository.removeWithTxn(
          txnContext,
          userSkillsJunction,
        );

        this.logger.log("Invalidating user skills cache");
        // Delete the cached user skills
        await this.cacheProvider.del(`user-skills:${userId}:${user.orgId}`);
      });

      this.logger.log(
        `Successfully detached skill ${skillId} from user ${userId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to detach skill ${skillId} from user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Updates a time off entry.
   * @param user The current user.
   * @param timeOffId The ID of the time off entry to update.
   * @param timeOffData The updated time off data.
   */
  public async updateTimeOff(
    user: CurrentUser,
    timeOffId: string,
    timeOffData: UpdateTimeOffDto,
  ) {
    this.logger.log(`Updating time off ${timeOffId} for user ${user.uid}`);
    // Find the time off entry
    const existingTimeOff = await this.timeOffRepository.exists({
      where: {
        id: timeOffId,
        user: { id: user.sub },
        organization: { id: user.orgId },
        deletedAt: null,
      },
    });

    // If the time off entry is not found, throw an error
    if (!existingTimeOff) {
      this.logger.warn(`Time off ${timeOffId} not found for user ${user.uid}`);
      throw new NotFoundException("Time off entry not found");
    }

    try {
      // Check for overlapping time offs, excluding the current one
      const overlappingTimeOff = await this.hasOverlappingTimeOffs(
        user.sub,
        new Date(timeOffData.startDate),
        new Date(timeOffData.endDate),
        timeOffId,
      );

      // If there is an overlapping time off entry, throw an error
      if (overlappingTimeOff) {
        this.logger.warn(`Overlapping time off found for user ${user.uid}`);
        throw new BadRequestException(
          "There is already a time off entry that overlaps with the requested dates",
        );
      }

      // Update the time off entry
      await this.timeOffRepository.update(
        { id: timeOffId },
        {
          startDate: new Date(timeOffData.startDate),
          endDate: new Date(timeOffData.endDate),
          description: timeOffData.description,
          type: timeOffData.type,
        },
      );

      const updatedTimeOff = await this.timeOffRepository.findOneById(
        timeOffId,
      );
      this.logger.log(
        `Successfully updated time off ${timeOffId} for user ${user.uid}`,
      );

      const availabilityEvent = new UserAvailabilityChangedEvent();
      availabilityEvent.user = user;
      availabilityEvent.orgId = user.orgId;
      availabilityEvent.changeType = UserAvailabilityChangeType.TIME_OFF_UPDATED;
      availabilityEvent.metadata = {
        timeOffId: timeOffId,
      };

      this.eventEmitter.emit(
        EmittableUserEvents.USER_AVAILABILITY_CHANGED,
        availabilityEvent,
      );

      return updatedTimeOff;
    } catch (error) {
      this.logger.error(
        `Failed to update time off ${timeOffId} for user ${user.uid}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Deletes a time off entry.
   * @param user The current user.
   * @param timeOffId The ID of the time off entry to delete.
   */
  public async deleteTimeOff(user: CurrentUser, timeOffId: string) {
    this.logger.log(`Deleting time off ${timeOffId} for user ${user.uid}`);
    // Find the time off entry with startDate
    const timeOff = await this.timeOffRepository.findByCondition({
      where: {
        id: timeOffId,
        user: { id: user.sub },
        organization: { id: user.orgId },
        deletedAt: null,
      },
      select: ["id", "startDate"],
    });

    // If the time off entry is not found, throw an error
    if (!timeOff) {
      this.logger.warn(`Time off ${timeOffId} not found for user ${user.uid}`);
      throw new NotFoundException("Time off entry not found");
    }

    // Check if the time off has already started
    if (new Date(timeOff.startDate).getTime() <= Date.now()) {
      this.logger.warn(
        `Cannot delete time off ${timeOffId} as it has already started`,
      );
      throw new BadRequestException(
        "Cannot delete a time off entry that has already started",
      );
    }

    try {
      // Soft delete the time off entry
      await this.timeOffRepository.softDelete({
        id: timeOffId,
        organizationId: user.orgId,
      });
      this.logger.log(
        `Successfully deleted time off ${timeOffId} for user ${user.uid}`,
      );

      const availabilityEvent = new UserAvailabilityChangedEvent();
      availabilityEvent.user = user;
      availabilityEvent.orgId = user.orgId;
      availabilityEvent.changeType = UserAvailabilityChangeType.TIME_OFF_DELETED;
      availabilityEvent.metadata = {
        timeOffId: timeOffId,
      };

      this.eventEmitter.emit(
        EmittableUserEvents.USER_AVAILABILITY_CHANGED,
        availabilityEvent,
      );
    } catch (error) {
      this.logger.error(
        `Failed to delete time off ${timeOffId} for user ${user.uid}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Generates the business hours updates for a user.
   * @param businessHours The business hours configurations to update.
   * @returns The business hours updates.
   */
  private generateBusinessHoursUpdates(businessHours: BusinessHoursConfigDto) {
    this.logger.log("Generating business hours updates");
    const updateClause = {};

    for (const day in businessHours) {
      const dayProperty: BusinessDayDto = businessHours[day];

      // If the day is active, update the business hours
      if (dayProperty.isActive) {
        updateClause[day] = dayProperty.slots;
      } else {
        updateClause[day] = null;
      }
    }

    return updateClause;
  }

  /**
   * Checks if there are overlapping time offs for a user.
   * @param userId The ID of the user.
   * @param startDate The start date to check for overlapping time offs.
   * @param endDate The end date to check for overlapping time offs.
   * @param excludeTimeOffId The ID of the time off entry to exclude from the check.
   * @returns True if there are overlapping time offs, false otherwise.
   */
  private hasOverlappingTimeOffs(
    userId: string,
    startDate: Date,
    endDate: Date,
    excludeTimeOffId?: string,
  ) {
    this.logger.log(
      `Checking for overlapping time offs for user ${userId} between ${startDate} and ${endDate}`,
    );
    const whereConditions: FindOptionsWhere<TimeOff>[] = [
      {
        user: { id: userId },
        startDate: Between(new Date(startDate), new Date(endDate)),
        deletedAt: null,
      },
      {
        user: { id: userId },
        endDate: Between(new Date(startDate), new Date(endDate)),
        deletedAt: null,
      },
      {
        user: { id: userId },
        startDate: LessThanOrEqual(new Date(startDate)),
        endDate: MoreThanOrEqual(new Date(endDate)),
        deletedAt: null,
      },
    ];

    // If an excludeTimeOffId is provided, exclude it from the where conditions
    if (excludeTimeOffId) {
      whereConditions.forEach((condition) => {
        condition.id = Not(excludeTimeOffId);
      });
    }

    return this.timeOffRepository.exists({ where: whereConditions });
  }

  /**
   * Generates a team identifier.
   * @returns The generated team identifier.
   */
  private generateUserIdentifier(userType: UserType): string {
    this.logger.log(`Generating identifier for user type: ${userType}`);
    switch (userType) {
      case UserType.USER:
      case UserType.ORG_ADMIN: {
        return IdGeneratorUtils.generate("U");
      }

      case UserType.CUSTOMER_USER: {
        return IdGeneratorUtils.generate("C");
      }

      case UserType.APP_USER: {
        return IdGeneratorUtils.generate("A");
      }

      case UserType.BOT_USER: {
        return IdGeneratorUtils.generate("O");
      }

      default: {
        this.logger.error(`Invalid user type: ${userType}`);
        throw new Error("Invalid user type");
      }
    }
  }

  findOrganizationAdmin(organizationId: string) {
    return this.userRepository.findByCondition({
      where: {
        organization: { uid: organizationId },
        userType: UserType.ORG_ADMIN,
      },
      relations: ["organization"],
    });
  }

  findBotProfileByAppId(appId: string, organizationId: string): Promise<User> {
    return this.userRepository.findByCondition({
      where: {
        organization: { uid: organizationId },
        userType: UserType.BOT_USER,
        metadata: Raw(
          (alias) =>
            `"${alias.split(".")[0]}"."${
              alias.split(".")[1]
            }"->>'appId' = :appId`,
          { appId },
        ),
      },
      relations: ["organization"],
    });
  }

  fetchWorkflowBot(organizationId: string) {
    return this.userRepository.findByCondition({
      where: {
        organization: { uid: organizationId },
        userType: UserType.BOT_USER,
        metadata: Raw(
          (alias) =>
            `"${alias.split(".")[0]}"."${
              alias.split(".")[1]
            }"->>'purpose' = :purpose`,
          { purpose: "workflow" },
        ),
      },
    });
  }

  async checkUserAvailability(
    currentUser: CurrentUser,
    userId: string,
  ): Promise<UserAvailabilityResponseDto> {
    // Find the user and validate they exist
    const user = await this.findOneByPublicId(userId, currentUser.orgId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Get both business hours and time off information
    const [userBusinessHours, activeTimeOff] = await Promise.all([
      this.cachedBusinessHoursConfigRepository.findByCondition({
        where: { user: { id: user.id } },
      }),
      this.timeOffRepository.findByCondition({
        where: {
          user: { id: user.id },
          startDate: LessThanOrEqual(new Date()),
          endDate: MoreThanOrEqual(new Date()),
          deletedAt: IsNull(),
        },
      }),
    ]);

    // If user is on time off, they're not available
    if (activeTimeOff) {
      return {
        isAvailable: false,
        reason: "TIME_OFF",
      };
    }

    // Check if user is available based on business hours
    const isUserAvailable = this.sharedService.checkBusinessHoursAvailability(
      userBusinessHours,
      user.timezone,
    );

    if (!isUserAvailable) {
      return { isAvailable: false, reason: "OUTSIDE_BUSINESS_HOURS" };
    }
    return { isAvailable: true, reason: "IN_BUSINESS_HOURS" };
  }

  async findUsersByIds(ids: string[]): Promise<User[]> {
    this.logger.log(`Finding users by IDs: ${ids.join(", ")}`);

    const whereClause: FindOptionsWhere<User> = {
      uid: In(ids),
    };

    return await this.userRepository.findAll({
      where: whereClause,
      relations: { organization: true },
    });
  }
}
