import { Inject, Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { SentryService } from "@repo/nestjs-commons/filters/sentry-alerts.filter";
import { ILogger } from "@repo/nestjs-commons/logger";
import { UserType } from "@repo/thena-platform-entities";
import {
  EmittableOrganizationEvents,
  OrganizationCreatedEvent,
} from "../../organization/events/organization.events";
import { SharedService } from "../../shared/shared.service";
import {
  EmittableTeamEvents,
  TeamCreatedEvent,
} from "../../teams/events/teams.events";
import { UsersService } from "../services/users.service";

@Injectable()
export class UsersListeners {
  private readonly logSpanId = "[UsersListeners]";

  constructor(
    @Inject("CustomLogger") private readonly logger: ILogger,
    @Inject("Sentry") private readonly sentryService: SentryService,

    private readonly usersService: UsersService,
    private readonly sharedService: SharedService,
  ) {}

  @OnEvent(EmittableOrganizationEvents.ORGANIZATION_CREATED)
  async createWorkflowBotForOrganization(event: OrganizationCreatedEvent) {
    const { organizationUID } = event;

    this.logger.log(
      `${this.logSpanId} Organization created event received. OrgId: ${organizationUID}`,
    );

    try {
      const orgAdmin = await this.usersService.findOrganizationAdmin(
        organizationUID,
      );

      await this.usersService.createBotUser(
        {
          name: `Thena workflows`,
          email: `workflow-bot@${organizationUID}.thena.email`,
          password: `workflow-bot-${organizationUID}`,
          organizationUid: organizationUID,
          purpose: "workflow",
          userType: UserType.BOT_USER,
        },
        {
          user_id: orgAdmin.uid,
          org_id: organizationUID,
        },
      );

      this.logger.log(
        `${this.logSpanId} Workflow bot created for organization ${organizationUID}`,
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while creating workflow bot. > Error message: ${error.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "NotificationsListeners",
        fn: "handleNotificationChannelToggledEvent",
        organizationUID,
      });
    }
  }

  @OnEvent(EmittableTeamEvents.TEAM_CREATED)
  async createTeamMemberForTeam(event: TeamCreatedEvent) {
    const { teamId, teamUID, organizationId } = event;

    try {
      const workflowBot = await this.usersService.fetchWorkflowBot(
        organizationId,
      );

      if (!workflowBot) {
        this.logger.error(
          `${this.logSpanId} Workflow bot not found for organization ${organizationId}`,
        );

        return;
      }

      await this.sharedService.addTeamMemberAsBot(
        teamId,
        workflowBot.id,
        organizationId,
      );

      this.logger.log(
        `${this.logSpanId} Workflow bot added to team ${teamUID}`,
      );
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while creating team member. > Error message: ${error.message}`,
      );

      this.sentryService.captureException(error, {
        tag: "NotificationsListeners",
        fn: "handleNotificationChannelToggledEvent",
        organizationId,
      });
    }
  }
}
