import { ThrottlerStorageRedisService } from "@nest-lab/throttler-storage-redis";
import { BullModule } from "@nestjs/bullmq";
import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { ThrottlerModule } from "@nestjs/throttler";
import { TypeOrmModule } from "@nestjs/typeorm";
import { RedisCacheModule } from "@repo/nestjs-commons/cache";
import { RequestSourceMiddleware } from "@repo/nestjs-commons/middlewares";
import { NewRelicModule } from "@repo/nestjs-newrelic";
import { AccountsModule } from "./accounts/accounts.module";
import { ActivitiesModule } from "./activities/activities.module";
import { AuthModule } from "./auth/auth.module";
import { BullBoardModule } from "./bull/bull-board.module";
import { UtilsModule } from "./common";
import { CommonModule } from "./common/common.module";
import { CommunicationsModule } from "./communications/communications.module";
import { ConfigModule } from "./config/config.module";
import { ConfigKeys, ConfigService } from "./config/config.service";
import { getPlatformDBConfig } from "./config/db.config";
import { THROTTLER_CONFIG } from "./config/throttler.config";
import { CustomFieldModule } from "./custom-field/custom-field.module";
import { CustomObjectModule } from "./custom-object/custom-object.module";
import { SeedsModule } from "./database/seeds/seeds.module";
import { FormsModule } from "./forms/forms.module";
import { HealthModule } from "./health/health.module";
import { NotificationsModule } from "./notifications/notifications.module";
import { OrganizationModule } from "./organization/organization.module";
import { SettingsModule } from "./settings/settings.module";
import { SharedModule } from "./shared/shared.module";
import { StorageModule } from "./storage/storage.module";
import { SwaggerModule } from "./swagger/swagger.module";
import { TagModule } from "./tags/tags.module";
import { TeamsModule } from "./teams/teams.module";
import { TicketsModule } from "./tickets/tickets.module";
import { UsersModule } from "./users/users.module";
import { ViewsModule } from "./views/views.module";

@Module({
  imports: [
    ConfigModule,
    CommonModule,
    SwaggerModule,
    NewRelicModule,
    // BullMQ for Queue Management
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        connection: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          enableReadyCheck: false,
          lazyConnect: true,
          maxRetriesPerRequest: null, // Add this to prevent premature closing of connections
          retryStrategy: (times) => {
            if (times > 2) return null;
            return Math.min(times * 100, 2000);
          },
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep failed jobs for 24 hours
          },
          attempts: 3, // Add retry attempts
          backoff: {
            type: "exponential",
            delay: 1000,
          },
          timeout: 30000, // Add job timeout
          stalledInterval: 30000, // Check for stalled jobs every 30 seconds
          lockDuration: 30000, // Lock job for 30 seconds
        },
      }),
    }),

    // BullBoard for Queue Management
    BullBoardModule,

    // Configure throttler for Rate Limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        if (configService.get(ConfigKeys.NODE_ENV) === "test") {
          const tiers = THROTTLER_CONFIG.map((tier) => ({
            name: tier.name,
            ttl: 0,
            limit: 10_000_000,
          }));

          return {
            throttlers: tiers,
            storage: new ThrottlerStorageRedisService({
              host: configService.get(ConfigKeys.REDIS_HOST),
              port: Number(configService.get(ConfigKeys.REDIS_PORT)),
              username: configService.get(ConfigKeys.REDIS_USERNAME),
              password: configService.get(ConfigKeys.REDIS_PASSWORD),
            }),
          };
        }

        return {
          throttlers: THROTTLER_CONFIG,
          storage: new ThrottlerStorageRedisService({
            host: configService.get(ConfigKeys.REDIS_HOST),
            port: Number(configService.get(ConfigKeys.REDIS_PORT)),
            username: "default",
            password: configService.get(ConfigKeys.REDIS_PASSWORD),
          }),
        };
      },
    }),

    // Redis for Caching and internally used for Cached Repositories
    RedisCacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
        };
      },
    }),

    // Configure TypeORM for Platform DB, don't forget to add entities if you create one
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) =>
        getPlatformDBConfig(configService),
      inject: [ConfigService],
    }),

    // Configure EventEmitter module for Event Emission
    EventEmitterModule.forRoot({
      delimiter: ".",
      verboseMemoryLeak: true,
      wildcard: true,
    }),

    HealthModule,
    AuthModule,
    UtilsModule,
    AccountsModule,
    OrganizationModule,
    CustomFieldModule,
    CustomObjectModule,
    UsersModule,
    TicketsModule,
    TeamsModule,
    TagModule,
    ViewsModule,
    StorageModule,
    CommunicationsModule,
    ActivitiesModule,
    SharedModule,
    FormsModule,
    NotificationsModule,
    SeedsModule,
    SettingsModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestSourceMiddleware).forRoutes("*");
  }
}
