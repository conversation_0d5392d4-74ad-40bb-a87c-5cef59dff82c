import { AccountCollectionDto } from "../dto/account.collection.dto";
import { CommentCollectionDto } from "../dto/comment.collection.dto";
import { HelpCenterCollectionDto } from "../dto/help-center.collection.dto";
import { TicketCollectionDto } from "../dto/ticket.collection.dto";
import { SearchCollection } from "../enums/search-collections.enum";
// import { CustomerContactSearchResultDto } from '../dto/customer-contact.collection.dto'; // Uncomment if you have this DTO

export const ALLOWED_QUERY_BY_FIELDS: Record<SearchCollection, string[]> = {
  [SearchCollection.TICKETS]: TicketCollectionDto.ALLOWED_QUERY_FIELDS,
  [SearchCollection.ACCOUNTS]: AccountCollectionDto.ALLOWED_QUERY_FIELDS,
  [SearchCollection.COMMENTS]: CommentCollectionDto.ALLOWED_QUERY_FIELDS,
  [SearchCollection.CUSTOMER_CONTACTS]: [], // TODO: Add CustomerContactSearchResultDto.ALLOWED_QUERY_FIELDS when available
  [SearchCollection.HELP_CENTER]: HelpCenterCollectionDto.ALLOWED_QUERY_FIELDS,
};
