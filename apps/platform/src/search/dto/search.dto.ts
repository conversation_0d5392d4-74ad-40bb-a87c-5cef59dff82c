import { ApiExtraModels, ApiProperty, getSchemaPath } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsArray,
  IsBooleanString,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from "class-validator";
import { SearchCollection } from "../enums/search-collections.enum";
import { AccountSearchResponseDto } from "./response/account-search-response.dto";
import { CommentSearchResponseDto } from "./response/comment-search-response.dto";
import { TicketSearchResponseDto } from "./response/ticket-search-response.dto";

export type SearchDocumentDto =
  | TicketSearchResponseDto
  | AccountSearchResponseDto
  | CommentSearchResponseDto;

export class ThenaSearchDto {
  @ApiProperty({
    description: "The search query",
    required: false,
    type: String,
    default: "*",
  })
  @IsString()
  @IsOptional()
  @MaxLength(512)
  q: string = "*";

  @ApiProperty({
    description: "Fields to query by (comma separated)",
    required: true,
    type: String,
  })
  @IsString()
  query_by: string;

  @ApiProperty({
    description: "Streaming mode",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsBooleanString()
  streaming?: string;

  // @ApiProperty({
  //   description: "Query by weights (comma separated)",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  query_by_weights?: string;

  // @ApiProperty({
  //   description: "Prefix search (true/false or comma separated for each field)",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  prefix?: string;

  @ApiProperty({
    description: "Filter by expression",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  filter_by?: string;

  @ApiProperty({
    description: "Sort by expression",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  sort_by?: string;

  @ApiProperty({
    description: "Facet by fields (comma separated)",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  facet_by?: string;

  // @ApiProperty({
  //   description: "Max facet values",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  max_facet_values?: string;

  // @ApiProperty({ description: "Facet query", required: false, type: String })
  @IsOptional()
  @IsString()
  facet_query?: string;

  // @ApiProperty({
  //   description: "Number of typos allowed",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  num_typos?: string;

  @ApiProperty({ description: "Page number", required: false, type: String })
  @IsOptional()
  @IsString()
  page?: string;

  @ApiProperty({
    description: "Results per page",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  per_page?: string;

  @ApiProperty({
    description: "Group by fields (comma separated)",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  group_by?: string;

  @ApiProperty({ description: "Group limit", required: false, type: String })
  @IsOptional()
  @IsString()
  group_limit?: string;

  @ApiProperty({
    description: "Include fields (comma separated)",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  include_fields?: string;

  @ApiProperty({
    description: "Exclude fields (comma separated)",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  exclude_fields?: string;

  // @ApiProperty({
  //   description: "Highlight full fields (comma separated)",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  highlight_full_fields?: string;

  // @ApiProperty({
  //   description: "Highlight affix num tokens",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  highlight_affix_num_tokens?: string;

  // @ApiProperty({
  //   description: "Highlight start tag",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  highlight_start_tag?: string;

  // @ApiProperty({
  //   description: "Highlight end tag",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  highlight_end_tag?: string;

  // @ApiProperty({
  //   description: "Snippet threshold",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  snippet_threshold?: string;

  // @ApiProperty({
  //   description: "Drop tokens threshold",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  drop_tokens_threshold?: string;

  // @ApiProperty({
  //   description: "Typo tokens threshold",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  typo_tokens_threshold?: string;

  // @ApiProperty({
  //   description: "Pinned hits (JSON string)",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  pinned_hits?: string;

  // @ApiProperty({
  //   description: "Hidden hits (JSON string)",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  hidden_hits?: string;

  // @ApiProperty({ description: "Limit hits", required: false, type: String })
  @IsOptional()
  @IsString()
  limit_hits?: string;

  // @ApiProperty({
  //   description: "Exhaustive search",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  exhaustive_search?: string;

  // @ApiProperty({
  //   description: "Exhaustive facets",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  exhaustive_facets?: string;

  // @ApiProperty({
  //   description: "Exhaustive groups",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  exhaustive_groups?: string;

  // @ApiProperty({
  //   description: "Pre-segmented query",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  pre_segmented_query?: string;

  // @ApiProperty({ description: "Use cache", required: false, type: String })
  @IsOptional()
  @IsString()
  use_cache?: string;

  // @ApiProperty({
  //   description: "Min length for 1 typo",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  min_len_1typo?: string;

  // @ApiProperty({
  //   description: "Min length for 2 typos",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  min_len_2typo?: string;

  // @ApiProperty({ description: "Preset name", required: false, type: String })
  @IsOptional()
  @IsString()
  preset?: string;

  // @ApiProperty({
  //   description: "Prioritize exact match",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  prioritize_exact_match?: string;

  // @ApiProperty({
  //   description: "Prioritize token position",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  prioritize_token_position?: string;

  // @ApiProperty({
  //   description: "Enable overrides",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  enable_overrides?: string;

  // @ApiProperty({
  //   description: "Enable highlight v1",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  enable_highlight_v1?: string;

  // @ApiProperty({
  //   description: "Search cutoff in ms",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  search_cutoff_ms?: string;

  // @ApiProperty({
  //   description: "Cache TTL in seconds",
  //   required: false,
  //   type: String,
  // })
  @IsOptional()
  @IsString()
  cache_ttl?: string;
}

export class TypesenseMultiSearchItemDto {
  @ApiProperty({
    description: "The collection to search",
    required: true,
    enum: SearchCollection,
  })
  collection: SearchCollection;

  @IsOptional()
  @IsString()
  @MaxLength(512)
  @ApiProperty({ description: "The search query", required: false })
  q?: string;

  @IsOptional()
  @ApiProperty({ description: "Fields to query by", required: false })
  query_by?: string;

  // Allow extra params, but no decorator here
  [key: string]: any;
}

export class ThenaMultiSearchDto {
  @ApiProperty({
    type: [TypesenseMultiSearchItemDto],
    description: "Array of search objects for federated search",
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TypesenseMultiSearchItemDto)
  searches: TypesenseMultiSearchItemDto[];

  // Allow extra params, but no decorator here
  [key: string]: any;
}

@ApiExtraModels(
  TicketSearchResponseDto,
  AccountSearchResponseDto,
  CommentSearchResponseDto,
)
export class TypesenseSearchHitDto<T> {
  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(TicketSearchResponseDto) },
      { $ref: getSchemaPath(AccountSearchResponseDto) },
      { $ref: getSchemaPath(CommentSearchResponseDto) },
    ],
    description: "The search result document (ticket, account, or comment)",
  })
  document: T;
}

@ApiExtraModels(TypesenseSearchHitDto)
export class TypesenseSearchResultDto<T> {
  @ApiProperty({ type: [Object], description: "Facet counts" })
  facet_counts: any[];

  @ApiProperty({ type: Number, description: "Number of documents found" })
  found: number;

  @ApiProperty({
    type: "array",
    items: { $ref: getSchemaPath(TypesenseSearchHitDto) },
    description: "Array of search hits",
  })
  hits: TypesenseSearchHitDto<T>[];

  @ApiProperty({ type: Number, description: "Page number" })
  page: number;

  @ApiProperty({ type: Object, description: "Request parameters" })
  request_params: any;

  @ApiProperty({ type: Boolean, description: "Search cutoff" })
  search_cutoff: boolean;
}

export { AccountSearchResponseDto } from "./response/account-search-response.dto";
export { CommentSearchResponseDto } from "./response/comment-search-response.dto";
export { TicketSearchResponseDto } from "./response/ticket-search-response.dto";
