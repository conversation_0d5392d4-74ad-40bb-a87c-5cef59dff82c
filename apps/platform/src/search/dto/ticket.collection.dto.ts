import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsBoolean, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
export class TicketCollectionDto {
  static ALLOWED_QUERY_FIELDS = [
    "id",
    "uid",
    "ticket_id",
    "ticket_identifier",
    "title",
    "description",
    "ai_generated_summary",
    "ai_generated_title",
    "metadata",
    "organization_id",
    "organization_uid",
    "form_id",
    "form_uid",
    "requestor_email",
    "submitter_email",
    "account_id",
    "account_name",
    "team_id",
    "team_uid",
    "team_identifier",
    "sub_team_id",
    "assigned_agent_id",
    "assigned_agent_email",
    "assigned_agent_name",
    "status_id",
    "status_name",
    "status_uid",
    "priority_id",
    "priority_uid",
    "priority_name",
    "type_id",
    "type_name",
    "customer_contact_id",
    "customer_contact_uid",
    "customer_contact_name",
    "customer_contact_email",
    "is_escalated",
    "is_private",
    "is_draft",
    "sentiment_id",
    "source",
    "story_points",
    "created_at",
    "updated_at",
    "due_date",
    "archived_at",
    "deleted_at",
    "type_icon",
    "type_color",
    "type_uid",
    "form_name",
    "sentiment_uid",
    "sentiment_name",
    "sub_team_uid",
    "sla_total_resolution_time_status",
    "sla_total_resolution_time_scheduled_at",
    "sla_total_resolution_time_breached_at",
    "sla_total_resolution_time_achieved_at",
    "sla_total_resolution_time_paused_at",
    "sla_total_resolution_time_resumed_at",
    "sla_total_resolution_time_cancelled_at",
    "sla_total_resolution_time_duration_to_breach_minutes",
    "sla_total_resolution_time_paused_duration_minutes",
    "sla_total_resolution_time_next_attempt_at",
    "sla_first_time_response_status",
    "sla_first_time_response_scheduled_at",
    "sla_first_time_response_breached_at",
    "sla_first_time_response_achieved_at",
    "sla_first_time_response_paused_at",
    "sla_first_time_response_resumed_at",
    "sla_first_time_response_cancelled_at",
    "sla_first_time_response_duration_to_breach_minutes",
    "sla_first_time_response_paused_duration_minutes",
    "sla_first_time_response_next_attempt_at",
    "sla_next_time_response_status",
    "sla_next_time_response_scheduled_at",
    "sla_next_time_response_breached_at",
    "sla_next_time_response_achieved_at",
    "sla_next_time_response_paused_at",
    "sla_next_time_response_resumed_at",
    "sla_next_time_response_cancelled_at",
    "sla_next_time_response_duration_to_breach_minutes",
    "sla_next_time_response_paused_duration_minutes",
    "sla_next_time_response_next_attempt_at",
    "sla_update_time_status",
    "sla_update_time_scheduled_at",
    "sla_update_time_breached_at",
    "sla_update_time_achieved_at",
    "sla_update_time_paused_at",
    "sla_update_time_resumed_at",
    "sla_update_time_cancelled_at",
    "sla_update_time_duration_to_breach_minutes",
    "sla_update_time_paused_duration_minutes",
    "sla_update_time_next_attempt_at",
    "account_primary_domain",
    "account_website",
    "account_annual_revenue",
    "account_employees",
    "account_owner_id",
    "account_owner_email",
    "account_owner_name",
    "account_owner_user_type",
    "account_owner_status",
    "account_owner_timezone",
    "contact_email",
    "contact_name",
    "contact_phone",
    "account_health",
    "account_industry",
    "account_classification",
    "account_status",
    "account_uid",
    "account_logo",
    "tags",
    "assigned_agent_uid",
    "team_name",
    "sub_team_name",
    "account_health_uid",
    "account_industry_uid",
    "account_classification_uid",
    "account_status_uid",
    "account_health_value",
    "account_industry_value",
    "account_classification_value",
    "account_status_value",
  ];

  static SELECT_FIELDS = [
    "id",
    "uid",
    "ticket_id",
    "ticket_identifier",
    "title",
    "description",
    "ai_generated_summary",
    "ai_generated_title",
    "metadata",
    "organization_id",
    "organization_uid",
    "form_id",
    "form_uid",
    "requestor_email",
    "submitter_email",
    "account_id",
    "account_name",
    "team_id",
    "team_uid",
    "team_identifier",
    "sub_team_id",
    "assigned_agent_id",
    "assigned_agent_email",
    "assigned_agent_name",
    "status_id",
    "status_name",
    "status_uid",
    "priority_id",
    "priority_uid",
    "priority_name",
    "type_id",
    "type_name",
    "customer_contact_id",
    "customer_contact_uid",
    "customer_contact_name",
    "customer_contact_email",
    "is_escalated",
    "is_private",
    "is_draft",
    "sentiment_id",
    "source",
    "story_points",
    "created_at",
    "updated_at",
    "due_date",
    "archived_at",
    "deleted_at",
    "type_icon",
    "type_color",
    "type_uid",
    "form_name",
    "sentiment_uid",
    "sentiment_name",
    "sub_team_uid",
    "sla_total_resolution_time_status",
    "sla_total_resolution_time_scheduled_at",
    "sla_total_resolution_time_breached_at",
    "sla_total_resolution_time_achieved_at",
    "sla_total_resolution_time_paused_at",
    "sla_total_resolution_time_resumed_at",
    "sla_total_resolution_time_cancelled_at",
    "sla_total_resolution_time_duration_to_breach_minutes",
    "sla_total_resolution_time_paused_duration_minutes",
    "sla_total_resolution_time_next_attempt_at",
    "sla_first_time_response_status",
    "sla_first_time_response_scheduled_at",
    "sla_first_time_response_breached_at",
    "sla_first_time_response_achieved_at",
    "sla_first_time_response_paused_at",
    "sla_first_time_response_resumed_at",
    "sla_first_time_response_cancelled_at",
    "sla_first_time_response_duration_to_breach_minutes",
    "sla_first_time_response_paused_duration_minutes",
    "sla_first_time_response_next_attempt_at",
    "sla_next_time_response_status",
    "sla_next_time_response_scheduled_at",
    "sla_next_time_response_breached_at",
    "sla_next_time_response_achieved_at",
    "sla_next_time_response_paused_at",
    "sla_next_time_response_resumed_at",
    "sla_next_time_response_cancelled_at",
    "sla_next_time_response_duration_to_breach_minutes",
    "sla_next_time_response_paused_duration_minutes",
    "sla_next_time_response_next_attempt_at",
    "sla_update_time_status",
    "sla_update_time_scheduled_at",
    "sla_update_time_breached_at",
    "sla_update_time_achieved_at",
    "sla_update_time_paused_at",
    "sla_update_time_resumed_at",
    "sla_update_time_cancelled_at",
    "sla_update_time_duration_to_breach_minutes",
    "sla_update_time_paused_duration_minutes",
    "sla_update_time_next_attempt_at",
    "account_primary_domain",
    "account_website",
    "account_annual_revenue",
    "account_employees",
    "account_owner_id",
    "account_owner_email",
    "account_owner_name",
    "account_owner_user_type",
    "account_owner_status",
    "account_owner_timezone",
    "contact_email",
    "contact_name",
    "contact_phone",
    //  new account fields
    "account_health",
    "account_industry",
    "account_classification",
    "account_status",
    "account_uid",
    "account_logo",
    "tags",
    "assigned_agent_uid",
    "team_name",
    "sub_team_name",
    "account_health_uid",
    "account_industry_uid",
    "account_classification_uid",
    "account_status_uid",
    "account_health_value",
    "account_industry_value",
    "account_classification_value",
    "account_status_value",
  ];

  static FIELD_MAP: Record<string, string> = {
    uid: "uid",
    ticketId: "ticket_id",
    ticketIdentifier: "ticket_identifier",
    title: "title",
    description: "description",
    aiGeneratedSummary: "ai_generated_summary",
    aiGeneratedTitle: "ai_generated_title",
    metadata: "metadata",
    organizationId: "organization_id",
    organizationUid: "organization_uid",
    formId: "form_id",
    formUid: "form_uid",
    requestorEmail: "requestor_email",
    submitterEmail: "submitter_email",
    accountId: "account_id",
    accountName: "account_name",
    teamId: "team_id",
    teamUid: "team_uid",
    teamIdentifier: "team_identifier",
    subTeamId: "sub_team_id",
    assignedAgentId: "assigned_agent_id",
    assignedAgentEmail: "assigned_agent_email",
    assignedAgentName: "assigned_agent_name",
    statusId: "status_id",
    statusName: "status_name",
    statusUid: "status_uid",
    priorityId: "priority_id",
    priorityUid: "priority_uid",
    priorityName: "priority_name",
    typeId: "type_id",
    typeName: "type_name",
    customerContactId: "customer_contact_id",
    customerContactUid: "customer_contact_uid",
    customerContactName: "customer_contact_name",
    customerContactEmail: "customer_contact_email",
    isEscalated: "is_escalated",
    isPrivate: "is_private",
    isDraft: "is_draft",
    sentimentId: "sentiment_id",
    source: "source",
    storyPoints: "story_points",
    createdAt: "created_at",
    updatedAt: "updated_at",
    dueDate: "due_date",
    archivedAt: "archived_at",
    deletedAt: "deleted_at",
    typeIcon: "type_icon",
    typeColor: "type_color",
    typeUid: "type_uid",
    formName: "form_name",
    sentimentUid: "sentiment_uid",
    sentimentName: "sentiment_name",
    subTeamUid: "sub_team_uid",
    tags: "tags",
    fullText: "full_text",
    // SLA Total Resolution Time fields
    slaTotalResolutionTimeStatus: "sla_total_resolution_time_status",
    slaTotalResolutionTimeScheduledAt: "sla_total_resolution_time_scheduled_at",
    slaTotalResolutionTimeBreachedAt: "sla_total_resolution_time_breached_at",
    slaTotalResolutionTimeAchievedAt: "sla_total_resolution_time_achieved_at",
    slaTotalResolutionTimePausedAt: "sla_total_resolution_time_paused_at",
    slaTotalResolutionTimeResumedAt: "sla_total_resolution_time_resumed_at",
    slaTotalResolutionTimeCancelledAt: "sla_total_resolution_time_cancelled_at",
    slaTotalResolutionTimeDurationToBreachMinutes:
      "sla_total_resolution_time_duration_to_breach_minutes",
    slaTotalResolutionTimePausedDurationMinutes:
      "sla_total_resolution_time_paused_duration_minutes",
    slaTotalResolutionTimeNextAttemptAt:
      "sla_total_resolution_time_next_attempt_at",
    // SLA First Time Response fields
    slaFirstTimeResponseStatus: "sla_first_time_response_status",
    slaFirstTimeResponseScheduledAt: "sla_first_time_response_scheduled_at",
    slaFirstTimeResponseBreachedAt: "sla_first_time_response_breached_at",
    slaFirstTimeResponseAchievedAt: "sla_first_time_response_achieved_at",
    slaFirstTimeResponsePausedAt: "sla_first_time_response_paused_at",
    slaFirstTimeResponseResumedAt: "sla_first_time_response_resumed_at",
    slaFirstTimeResponseCancelledAt: "sla_first_time_response_cancelled_at",
    slaFirstTimeResponseDurationToBreachMinutes:
      "sla_first_time_response_duration_to_breach_minutes",
    slaFirstTimeResponsePausedDurationMinutes:
      "sla_first_time_response_paused_duration_minutes",
    slaFirstTimeResponseNextAttemptAt:
      "sla_first_time_response_next_attempt_at",
    // SLA Next Time Response fields
    slaNextTimeResponseStatus: "sla_next_time_response_status",
    slaNextTimeResponseScheduledAt: "sla_next_time_response_scheduled_at",
    slaNextTimeResponseBreachedAt: "sla_next_time_response_breached_at",
    slaNextTimeResponseAchievedAt: "sla_next_time_response_achieved_at",
    slaNextTimeResponsePausedAt: "sla_next_time_response_paused_at",
    slaNextTimeResponseResumedAt: "sla_next_time_response_resumed_at",
    slaNextTimeResponseCancelledAt: "sla_next_time_response_cancelled_at",
    slaNextTimeResponseDurationToBreachMinutes:
      "sla_next_time_response_duration_to_breach_minutes",
    slaNextTimeResponsePausedDurationMinutes:
      "sla_next_time_response_paused_duration_minutes",
    slaNextTimeResponseNextAttemptAt: "sla_next_time_response_next_attempt_at",
    // SLA Update Time fields
    slaUpdateTimeStatus: "sla_update_time_status",
    slaUpdateTimeScheduledAt: "sla_update_time_scheduled_at",
    slaUpdateTimeBreachedAt: "sla_update_time_breached_at",
    slaUpdateTimeAchievedAt: "sla_update_time_achieved_at",
    slaUpdateTimePausedAt: "sla_update_time_paused_at",
    slaUpdateTimeResumedAt: "sla_update_time_resumed_at",
    slaUpdateTimeCancelledAt: "sla_update_time_cancelled_at",
    slaUpdateTimeDurationToBreachMinutes:
      "sla_update_time_duration_to_breach_minutes",
    slaUpdateTimePausedDurationMinutes:
      "sla_update_time_paused_duration_minutes",
    slaUpdateTimeNextAttemptAt: "sla_update_time_next_attempt_at",

    // populated while flattening the account object
    accountPrimaryDomain: "account_primary_domain",
    accountWebsite: "account_website",
    accountAnnualRevenue: "account_annual_revenue",
    accountEmployees: "account_employees",
    accountOwnerId: "account_owner_id",
    accountOwnerEmail: "account_owner_email",
    accountOwnerName: "account_owner_name",
    accountOwnerUserType: "account_owner_user_type",
    accountOwnerStatus: "account_owner_status",
    accountOwnerTimezone: "account_owner_timezone",
    contactEmail: "contact_email",
    contactName: "contact_name",
    contactPhone: "contact_phone",
    //  new account fields
    accountHealth: "account_health",
    accountIndustry: "account_industry",
    accountClassification: "account_classification",
    accountStatus: "account_status",
    accountUid: "account_uid",
    accountLogo: "account_logo",
    assignedAgentUid: "assigned_agent_uid",
    // team fields
    teamName: "team_name",
    subTeamName: "sub_team_name",
    accountHealthUid: "account_health_uid",
    accountIndustryUid: "account_industry_uid",
    accountClassificationUid: "account_classification_uid",
    accountStatusUid: "account_status_uid",
    accountHealthValue: "account_health_value",
    accountIndustryValue: "account_industry_value",
    accountClassificationValue: "account_classification_value",
    accountStatusValue: "account_status_value",

  };

  static mapToTypesenseFields(fields: string[]): string[] {
    return fields.map((f) => this.FIELD_MAP[f] || f);
  }

  static transformValue = (field: string, value: string) => {
    const dateFields = [
      'created_at',
      'updated_at',
      'due_date',
      'archived_at',
      'deleted_at',
      'sla_total_resolution_time_scheduled_at',
      'sla_total_resolution_time_breached_at',
      'sla_total_resolution_time_achieved_at',
      'sla_total_resolution_time_paused_at',
      'sla_total_resolution_time_resumed_at',
      'sla_total_resolution_time_cancelled_at',
      'sla_total_resolution_time_next_attempt_at',
      'sla_first_time_response_scheduled_at',
      'sla_first_time_response_breached_at',
      'sla_first_time_response_achieved_at',
      'sla_first_time_response_paused_at',
      'sla_first_time_response_resumed_at',
      'sla_first_time_response_cancelled_at',
      'sla_first_time_response_next_attempt_at',
      'sla_next_time_response_scheduled_at',
      'sla_next_time_response_breached_at',
      'sla_next_time_response_achieved_at',
      'sla_next_time_response_paused_at',
      'sla_next_time_response_resumed_at',
      'sla_next_time_response_cancelled_at',
      'sla_next_time_response_next_attempt_at',
      'sla_update_time_scheduled_at',
      'sla_update_time_breached_at',
      'sla_update_time_achieved_at',
      'sla_update_time_paused_at',
      'sla_update_time_resumed_at',
      'sla_update_time_cancelled_at',
      'sla_update_time_next_attempt_at',
    ]

    if (dateFields.includes(field)) {
      const dateStr = value?.toString().replace(/"/g, "")
      return new Date(dateStr).getTime();
    }

    return value;
  }

  static joins = [
    {
      collection: "accounts",
      prefix: "account_",
      excludeFields: ["account_id", "account_name"],
    },
  ]

  @ApiProperty()
  @IsString()
  uid: string;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  ticket_id: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  organization_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  tk_form_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  requestor_email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  submitter_email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  account_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  title_metadata?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  metadata?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  ai_generated_title?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  ai_generated_summary?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sub_team_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  status_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  priority_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  type_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  assigned_agent_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  created_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  due_date?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  is_escalated?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  updated_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  is_private?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  story_points?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  is_draft?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  archived_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  deleted_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  customer_contact_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  customer_contact_uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  customer_contact_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  customer_contact_email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  team_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sentiment_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sentiment_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sentiment_uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  status_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  priority_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  priority_uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  type_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  type_uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  assigned_agent_email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  assigned_agent_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  account_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  account_primary_domain?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  account_website?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  account_annual_revenue?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  account_employees?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  account_owner_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  account_owner_email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  account_owner_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  account_owner_user_type?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  account_owner_status?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  account_owner_timezone?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contact_email?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contact_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contact_phone?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  team_identifier?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  ticket_identifier?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_total_resolution_time_status?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_total_resolution_time_scheduled_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_total_resolution_time_breached_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_total_resolution_time_achieved_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_total_resolution_time_paused_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_total_resolution_time_resumed_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_total_resolution_time_cancelled_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sla_total_resolution_time_duration_to_breach_minutes?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sla_total_resolution_time_paused_duration_minutes?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_total_resolution_time_next_attempt_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_first_time_response_status?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_first_time_response_scheduled_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_first_time_response_breached_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_first_time_response_achieved_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_first_time_response_paused_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_first_time_response_resumed_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_first_time_response_cancelled_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sla_first_time_response_duration_to_breach_minutes?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sla_first_time_response_paused_duration_minutes?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_first_time_response_next_attempt_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_next_time_response_status?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_next_time_response_scheduled_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_next_time_response_breached_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_next_time_response_achieved_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_next_time_response_paused_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_next_time_response_resumed_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_next_time_response_cancelled_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sla_next_time_response_duration_to_breach_minutes?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sla_next_time_response_paused_duration_minutes?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_next_time_response_next_attempt_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  organization_uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  team_uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  form_uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  form_name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  type_icon?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  type_color?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sub_team_uid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_update_time_status?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_update_time_scheduled_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_update_time_breached_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_update_time_achieved_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_update_time_paused_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_update_time_resumed_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_update_time_cancelled_at?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sla_update_time_duration_to_breach_minutes?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  sla_update_time_paused_duration_minutes?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sla_update_time_next_attempt_at?: string;

  /**
   * Maps a Typesense search result to this DTO, including inherited and extra fields.
   */
  static fromSearchResult(searchResult: any): TicketCollectionDto {
    const dto = new TicketCollectionDto();
    Object.assign(dto, searchResult);
    return dto;
  }
}
