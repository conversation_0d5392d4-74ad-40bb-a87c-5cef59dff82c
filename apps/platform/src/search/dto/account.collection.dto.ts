import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString } from "class-validator";

export class AccountCollectionDto {
  static ALLOWED_QUERY_FIELDS = [
    "uid",
    "name",
    "description",
    "is_active",
    "logo",
    "status",
    "classification",
    "health",
    "industry",
    "source",
    "primary_domain",
    "secondary_domain",
    "annual_revenue",
    "employees",
    "website",
    "billing_address",
    "shipping_address",
    "account_owner_id",
    "account_owner_email",
    "account_owner_name",
    "account_owner_user_type",
    "account_owner_status",
    "account_owner_timezone",
    "metadata",
    "organization_id",
    "created_at",
    "updated_at",
    "deleted_at",
    "organization_uid",
    "health_uid",
    "health_value",
    "classification_uid",
    "classification_value",
    "status_uid",
    "status_value",
    "industry_uid",
    "industry_value",
  ];

  static SELECT_FIELDS = [
    "uid",
    "name",
    "description",
    "is_active",
    "logo",
    "status",
    "classification",
    "health",
    "industry",
    "source",
    "primary_domain",
    "secondary_domain",
    "annual_revenue",
    "employees",
    "website",
    "billing_address",
    "shipping_address",
    "account_owner_id",
    "account_owner_email",
    "account_owner_name",
    "account_owner_user_type",
    "account_owner_status",
    "account_owner_timezone",
    "metadata",
    "organization_id",
    "created_at",
    "updated_at",
    "deleted_at",
    "organization_uid",
    "health_uid",
    "health_value",
    "classification_uid",
    "classification_value",
    "status_uid",
    "status_value",
    "industry_uid",
    "industry_value",
  ];

  static FIELD_MAP: Record<string, string> = {
    uid: "uid",
    name: "name",
    description: "description",
    isActive: "is_active",
    logo: "logo",
    status: "status",
    classification: "classification",
    health: "health",
    industry: "industry",
    source: "source",
    primaryDomain: "primary_domain",
    secondaryDomain: "secondary_domain",
    annualRevenue: "annual_revenue",
    employees: "employees",
    website: "website",
    billingAddress: "billing_address",
    shippingAddress: "shipping_address",
    accountOwnerId: "account_owner_id",
    accountOwnerEmail: "account_owner_email",
    accountOwnerName: "account_owner_name",
    accountOwnerUserType: "account_owner_user_type",
    accountOwnerStatus: "account_owner_status",
    accountOwnerTimezone: "account_owner_timezone",
    metadata: "metadata",
    organizationId: "organization_id",
    createdAt: "created_at",
    updatedAt: "updated_at",
    deletedAt: "deleted_at",
    organizationUid: "organization_uid",
    healthUid: "health_uid",
    healthValue: "health_value",
    classificationUid: "classification_uid",
    classificationValue: "classification_value",
    statusUid: "status_uid",
    statusValue: "status_value",
    industryUid: "industry_uid",
  };

  static mapToTypesenseFields(fields: string[]): string[] {
    return fields.map((f) => this.FIELD_MAP[f] || f);
  }

  @ApiProperty()
  @IsString()
  uid: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsBoolean()
  is_active: boolean;

  @ApiProperty()
  @IsString()
  logo: string;

  @ApiProperty()
  @IsNumber()
  status: number;

  @ApiProperty()
  @IsNumber()
  classification: number;

  @ApiProperty()
  @IsNumber()
  health: number;

  @ApiProperty()
  @IsNumber()
  industry: number;

  @ApiProperty()
  @IsString()
  source: string;

  @ApiProperty()
  @IsString()
  primary_domain: string;

  @ApiProperty()
  @IsString()
  secondary_domain: string;

  @ApiProperty()
  @IsNumber()
  annual_revenue: number;

  @ApiProperty()
  @IsNumber()
  employees: number;

  @ApiProperty()
  @IsString()
  website: string;

  @ApiProperty()
  @IsString()
  billing_address: string;

  @ApiProperty()
  @IsString()
  shipping_address: string;

  @ApiProperty()
  @IsNumber()
  account_owner_id: number;

  @ApiProperty()
  @IsString()
  account_owner_email: string;

  @ApiProperty()
  @IsString()
  account_owner_name: string;

  @ApiProperty()
  @IsString()
  account_owner_user_type: string;

  @ApiProperty()
  @IsString()
  account_owner_status: string;

  @ApiProperty()
  @IsString()
  account_owner_timezone: string;

  @ApiProperty()
  @IsString()
  metadata: string;

  @ApiProperty()
  @IsNumber()
  organization_id: number;

  @ApiProperty()
  @IsString()
  created_at: string;

  @ApiProperty()
  @IsString()
  updated_at: string;

  @ApiProperty()
  @IsString()
  deleted_at: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  account_owner_uid?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  health_uid?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  health_value?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  classification_uid?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  classification_value?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  status_uid?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  status_value?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  industry_uid?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  industry_value?: string;
}
