import { ApiProperty } from "@nestjs/swagger";

export class HelpCenterCollectionDto {
  static readonly ALLOWED_QUERY_FIELDS = [
    "uid",
    "title",
    "description",
    "body",
    "visibility",
    "tags",
    "help_center_ids",
    "organization_uid",
  ];

  static readonly SELECT_FIELDS = [
    "uid",
    "title",
    "body",
    "description",
    "tags",
    "help_center_ids",
    "organization_uid",
  ];

  static readonly FIELD_MAP: Record<string, string> = {
    uid: "uid",
    organizationUid: "organization_uid",
    title: "title",
    description: "description",
    body: "body",
    visibility: "visibility",
    tags: "tags",
    helpCenterIds: "help_center_ids",
  };

  static mapToTypesenseFields(fields: string[]): string[] {
    return fields.map((f) => HelpCenterCollectionDto.FIELD_MAP[f] || f);
  }

  @ApiProperty()
  uid: number;

  @ApiProperty()
  organization_uid: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  body: string;

  @ApiProperty()
  visibility: string;

  @ApiProperty({ type: [String] })
  tags: string[];

  @ApiProperty({ type: [String] })
  help_center_ids: string[];
}
