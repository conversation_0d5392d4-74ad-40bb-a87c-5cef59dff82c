import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { convertUnixToDate } from '@repo/thena-shared-libs';
import { IsBoolean, IsOptional, IsString } from "class-validator";
import { TicketResponseDto } from "../../../tickets/transformer/ticket-response.dto";

export class TicketSearchResponseDto extends TicketResponseDto {

  @ApiProperty()
  @IsString()
  uid: string;

  @ApiProperty()
  @IsString()
  ticketIdentifier: string;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  aiGeneratedSummary?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  aiGeneratedTitle?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  metadata?: string;

  @ApiProperty()
  @IsString()
  organizationId: string;

  @ApiProperty()
  @IsString()
  organizationUid: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  formId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  formUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  formName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountPrimaryDomain?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountWebsite?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountAnnualRevenue?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountEmployees?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountOwnerId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountOwnerEmail?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountOwnerName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountOwnerUserType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountOwnerStatus?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountOwnerTimezone?: string;

  @ApiProperty()
  @IsString()
  teamUid: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  teamIdentifier?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  subTeamId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  subTeamUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  assignedAgentId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  assignedAgentEmail?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  assignedAgentName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  statusId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  statusName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  statusUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  priorityId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  priorityUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  priorityName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  typeId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  typeName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  typeUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  typeIcon?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  typeColor?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  customerContactId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isEscalated?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isDraft?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sentimentId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sentimentUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  sentimentName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  dueDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  archivedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  deletedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contactEmail?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contactName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contactPhone?: string;

  // SLA Total Resolution Time fields
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeStatus?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeScheduledAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeBreachedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeAchievedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimePausedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeResumedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeCancelledAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeDurationToBreachMinutes?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimePausedDurationMinutes?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeNextAttemptAt?: string;

  // SLA First Time Response fields
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseStatus?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseScheduledAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseBreachedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseAchievedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponsePausedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseResumedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseCancelledAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseDurationToBreachMinutes?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponsePausedDurationMinutes?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseNextAttemptAt?: string;

  // SLA Next Time Response fields
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseStatus?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseScheduledAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseBreachedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseAchievedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponsePausedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseResumedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseCancelledAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseDurationToBreachMinutes?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponsePausedDurationMinutes?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseNextAttemptAt?: string;

  // SLA Update Time fields
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimeStatus?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimeScheduledAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimeBreachedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimeAchievedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimePausedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimeResumedAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimeCancelledAt?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimeDurationToBreachMinutes?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimePausedDurationMinutes?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateTimeNextAttemptAt?: string;

  // new account fields
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountHealth?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountIndustry?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountClassification?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountStatus?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountLogo?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountOwnerUid?: string;

  // tags
  @ApiPropertyOptional({
    type: 'object',
    properties: {
      values: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            color: { type: 'string' },
            created_at: { type: 'string' },
            deleted_at: { type: 'string' },
            description: { type: 'string' },
            id: { type: 'string' },
            is_active: { type: 'string' },
            name: { type: 'string' },
            organization_id: { type: 'string' },
            tagType: { type: 'string' },
            team_id: { type: 'string' },
            uid: { type: 'string' },
            updated_at: { type: 'string' },
          },
        },
      },
    },
  })
  @IsOptional()
  tags?: {
    values: {
      color: string;
      created_at: string;
      deleted_at: string;
      description: string;
      id: string;
      is_active: string;
      name: string;
      organization_id: string;
      tagType: string;
      team_id: string;
      uid: string;
      updated_at: string;
    }[];
  }

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  assignedAgentUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  customerContactUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  customerContactName?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  customerContactEmail?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountHealthUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountIndustryUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountClassificationUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountStatusUid?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountHealthValue?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountIndustryValue?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountClassificationValue?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountStatusValue?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaTotalResolutionTimeComplianceState?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaFirstTimeResponseComplianceState?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaNextTimeResponseComplianceState?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  slaUpdateComplianceState?: string;

  static getSlaComplianceState = (sla?: {
    status?: string,
    achievedAt?: string
  }) => {
    if (!sla) return null;

    const { status, achievedAt } = sla;

    switch (status) {
      case "breached":
        if (achievedAt) {
          return "DELAYED"
        }
        return "OVERDUE"
      case "achieved":
        return "ACHIEVED"
      case "paused":
        return "PAUSED"
      case "running":
        return "RUNNING"
      case "scheduled":
        return "DUE"
      default:
        return "NOT_SCHEDULED";
    }
  };

  static fromSearchResult(searchResult: any): TicketSearchResponseDto {
    const dto = new TicketSearchResponseDto();
    // Map snake_case fields from Typesense to camelCase fields in the DTO
    dto.uid = searchResult.uid;
    dto.ticketId = searchResult.ticket_id;
    dto.ticketIdentifier = searchResult.ticket_identifier;
    dto.title = searchResult.title;
    dto.description = searchResult.description;
    dto.aiGeneratedSummary = searchResult.ai_generated_summary;
    dto.aiGeneratedTitle = searchResult.ai_generated_title;
    dto.metadata = searchResult.metadata;
    dto.organizationId = searchResult.organization_id;
    dto.organizationUid = searchResult.organization_uid;
    dto.formId = searchResult.form_id;
    dto.formUid = searchResult.form_uid;
    dto.formName = searchResult.form_name;
    dto.requestorEmail = searchResult.requestor_email;
    dto.submitterEmail = searchResult.submitter_email;
    dto.accountId = searchResult.account_id;
    dto.accountName = searchResult.account_name;
    dto.teamId = searchResult.team_id;
    dto.teamUid = searchResult.team_uid;
    dto.teamIdentifier = searchResult.team_identifier;
    dto.subTeamId = searchResult.sub_team_id;
    dto.subTeamUid = searchResult.sub_team_uid;
    dto.assignedAgentId = searchResult.assigned_agent_id;
    dto.assignedAgentEmail = searchResult.assigned_agent_email;
    dto.assignedAgentName = searchResult.assigned_agent_name;
    dto.statusId = searchResult.status_id;
    dto.statusName = searchResult.status_name;
    dto.statusUid = searchResult.status_uid;
    dto.priorityId = searchResult.priority_id;
    dto.priorityName = searchResult.priority_name;
    dto.priorityUid = searchResult.priority_uid;
    dto.typeId = searchResult.type_id;
    dto.typeName = searchResult.type_name;
    dto.typeUid = searchResult.type_uid;
    dto.typeIcon = searchResult.type_icon;
    dto.typeColor = searchResult.type_color;
    dto.customerContactUid = searchResult.customer_contact_uid;
    dto.customerContactName = searchResult.customer_contact_name;
    dto.customerContactEmail = searchResult.customer_contact_email;
    dto.isEscalated = searchResult.is_escalated;
    dto.isPrivate = searchResult.is_private;
    dto.isDraft = searchResult.is_draft;
    dto.sentimentId = searchResult.sentiment_id;
    dto.sentimentUid = searchResult.sentiment_uid;
    dto.sentimentName = searchResult.sentiment_name;
    dto.source = searchResult.source;
    dto.storyPoints = searchResult.story_points;
    dto.createdAt = convertUnixToDate(searchResult.created_at);
    dto.updatedAt = convertUnixToDate(searchResult.updated_at);
    dto.dueDate = convertUnixToDate(searchResult.due_date);
    dto.archivedAt = convertUnixToDate(searchResult.archived_at);
    dto.deletedAt = convertUnixToDate(searchResult.deleted_at);

    // SLA Total Resolution Time fields
    dto.slaTotalResolutionTimeStatus = searchResult.sla_total_resolution_time_status;
    dto.slaTotalResolutionTimeScheduledAt = convertUnixToDate(searchResult.sla_total_resolution_time_scheduled_at);
    dto.slaTotalResolutionTimeBreachedAt = convertUnixToDate(searchResult.sla_total_resolution_time_breached_at);
    dto.slaTotalResolutionTimeAchievedAt = convertUnixToDate(searchResult.sla_total_resolution_time_achieved_at);
    dto.slaTotalResolutionTimePausedAt = convertUnixToDate(searchResult.sla_total_resolution_time_paused_at);
    dto.slaTotalResolutionTimeResumedAt = convertUnixToDate(searchResult.sla_total_resolution_time_resumed_at);
    dto.slaTotalResolutionTimeCancelledAt = convertUnixToDate(searchResult.sla_total_resolution_time_cancelled_at);
    dto.slaTotalResolutionTimeDurationToBreachMinutes = searchResult.sla_total_resolution_time_duration_to_breach_minutes?.toString();
    dto.slaTotalResolutionTimePausedDurationMinutes = searchResult.sla_total_resolution_time_paused_duration_minutes?.toString();
    dto.slaTotalResolutionTimeNextAttemptAt = convertUnixToDate(searchResult.sla_total_resolution_time_next_attempt_at);
    dto.slaTotalResolutionTimeComplianceState = TicketSearchResponseDto.getSlaComplianceState({
      status: dto.slaTotalResolutionTimeStatus,
      achievedAt: dto.slaTotalResolutionTimeAchievedAt,
    });

    // SLA First Time Response fields
    dto.slaFirstTimeResponseStatus = searchResult.sla_first_time_response_status;
    dto.slaFirstTimeResponseScheduledAt = convertUnixToDate(searchResult.sla_first_time_response_scheduled_at);
    dto.slaFirstTimeResponseBreachedAt = convertUnixToDate(searchResult.sla_first_time_response_breached_at);
    dto.slaFirstTimeResponseAchievedAt = convertUnixToDate(searchResult.sla_first_time_response_achieved_at);
    dto.slaFirstTimeResponsePausedAt = convertUnixToDate(searchResult.sla_first_time_response_paused_at);
    dto.slaFirstTimeResponseResumedAt = convertUnixToDate(searchResult.sla_first_time_response_resumed_at);
    dto.slaFirstTimeResponseCancelledAt = convertUnixToDate(searchResult.sla_first_time_response_cancelled_at);
    dto.slaFirstTimeResponseDurationToBreachMinutes = searchResult.sla_first_time_response_duration_to_breach_minutes?.toString();
    dto.slaFirstTimeResponsePausedDurationMinutes = searchResult.sla_first_time_response_paused_duration_minutes?.toString();
    dto.slaFirstTimeResponseNextAttemptAt = convertUnixToDate(searchResult.sla_first_time_response_next_attempt_at);
    dto.slaFirstTimeResponseComplianceState = TicketSearchResponseDto.getSlaComplianceState({
      status: dto.slaFirstTimeResponseStatus,
      achievedAt: dto.slaFirstTimeResponseAchievedAt,
    });

    // SLA Next Time Response fields
    dto.slaNextTimeResponseStatus = searchResult.sla_next_time_response_status;
    dto.slaNextTimeResponseScheduledAt = convertUnixToDate(searchResult.sla_next_time_response_scheduled_at);
    dto.slaNextTimeResponseBreachedAt = convertUnixToDate(searchResult.sla_next_time_response_breached_at);
    dto.slaNextTimeResponseAchievedAt = convertUnixToDate(searchResult.sla_next_time_response_achieved_at);
    dto.slaNextTimeResponsePausedAt = convertUnixToDate(searchResult.sla_next_time_response_paused_at);
    dto.slaNextTimeResponseResumedAt = convertUnixToDate(searchResult.sla_next_time_response_resumed_at);
    dto.slaNextTimeResponseCancelledAt = convertUnixToDate(searchResult.sla_next_time_response_cancelled_at);
    dto.slaNextTimeResponseDurationToBreachMinutes = searchResult.sla_next_time_response_duration_to_breach_minutes?.toString();
    dto.slaNextTimeResponsePausedDurationMinutes = searchResult.sla_next_time_response_paused_duration_minutes?.toString();
    dto.slaNextTimeResponseNextAttemptAt = convertUnixToDate(searchResult.sla_next_time_response_next_attempt_at);
    dto.slaNextTimeResponseComplianceState = TicketSearchResponseDto.getSlaComplianceState({
      status: dto.slaNextTimeResponseStatus,
      achievedAt: dto.slaNextTimeResponseAchievedAt,
    });

    // SLA Update Time fields
    dto.slaUpdateTimeStatus = searchResult.sla_update_time_status;
    dto.slaUpdateTimeScheduledAt = convertUnixToDate(searchResult.sla_update_time_scheduled_at);
    dto.slaUpdateTimeBreachedAt = convertUnixToDate(searchResult.sla_update_time_breached_at);
    dto.slaUpdateTimeAchievedAt = convertUnixToDate(searchResult.sla_update_time_achieved_at);
    dto.slaUpdateTimePausedAt = convertUnixToDate(searchResult.sla_update_time_paused_at);
    dto.slaUpdateTimeResumedAt = convertUnixToDate(searchResult.sla_update_time_resumed_at);
    dto.slaUpdateTimeCancelledAt = convertUnixToDate(searchResult.sla_update_time_cancelled_at);
    dto.slaUpdateTimeDurationToBreachMinutes = searchResult.sla_update_time_duration_to_breach_minutes?.toString();
    dto.slaUpdateTimePausedDurationMinutes = searchResult.sla_update_time_paused_duration_minutes?.toString();
    dto.slaUpdateTimeNextAttemptAt = convertUnixToDate(searchResult.sla_update_time_next_attempt_at);
    dto.slaUpdateComplianceState = TicketSearchResponseDto.getSlaComplianceState({
      status: dto.slaUpdateTimeStatus,
      achievedAt: dto.slaUpdateTimeAchievedAt,
    });

    // Account related fields
    dto.accountPrimaryDomain = searchResult?.accounts?.primary_domain;
    dto.accountWebsite = searchResult?.accounts?.website;
    dto.accountAnnualRevenue = searchResult?.accounts?.annual_revenue?.toString();
    dto.accountEmployees = searchResult?.accounts?.employees?.toString();
    dto.accountOwnerId = searchResult?.accounts?.account_owner_id?.toString();
    dto.accountOwnerEmail = searchResult?.accounts?.account_owner_email;
    dto.accountOwnerName = searchResult?.accounts?.account_owner_name;
    dto.accountOwnerUserType = searchResult?.accounts?.account_owner_user_type;
    dto.accountOwnerStatus = searchResult?.accounts?.account_owner_status;
    dto.accountOwnerTimezone = searchResult?.accounts?.account_owner_timezone;
    dto.accountHealth = searchResult?.accounts?.health;
    dto.accountIndustry = searchResult?.accounts?.industry;
    dto.accountClassification = searchResult?.accounts?.classification;
    dto.accountStatus = searchResult?.accounts?.status;
    dto.accountUid = searchResult?.accounts?.uid;
    dto.accountLogo = searchResult?.accounts?.logo;
    dto.accountOwnerUid = searchResult?.accounts?.account_owner_uid;
    dto.accountHealthUid = searchResult?.accounts?.health_uid;
    dto.accountIndustryUid = searchResult?.accounts?.industry_uid;
    dto.accountClassificationUid = searchResult?.accounts?.classification_uid;
    dto.accountStatusUid = searchResult?.accounts?.status_uid;
    dto.accountHealthValue = searchResult?.accounts?.health_value;
    dto.accountIndustryValue = searchResult?.accounts?.industry_value;
    dto.accountClassificationValue = searchResult?.accounts?.classification_value;
    dto.accountStatusValue = searchResult?.accounts?.status_value;
    // contacts
    dto.contactEmail = searchResult.contact_email;
    dto.contactName = searchResult.contact_name;
    dto.contactPhone = searchResult.contact_phone;

    // assigned agent
    dto.assignedAgentUid = searchResult?.assigned_agent_uid;

    // tags
    dto.tags = searchResult.tags || { values: [] };

    // team
    dto.teamName = searchResult?.team_name;
    dto.subTeamName = searchResult?.sub_team_name;

    return dto;
  }
}
