import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";
import { AccountCollectionDto } from "../account.collection.dto";

export class AccountSearchResponseDto extends AccountCollectionDto {

  @ApiProperty()
  @IsString()
  @IsOptional()
  health_uid?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  health_value?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  classification_uid?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  classification_value?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  status_uid?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  status_value?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  industry_uid?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  industry_value?: string;

  static fromSearchResult(searchResult: any): AccountSearchResponseDto {
    const dto = new AccountSearchResponseDto();
    // Map snake_case fields from Typesense to camelCase fields in the DTO
    dto.uid = searchResult.uid;
    dto.name = searchResult.name;
    dto.description = searchResult.description;
    dto.is_active = searchResult.is_active;
    dto.logo = searchResult.logo;
    dto.status = searchResult.status;
    dto.classification = searchResult.classification;
    dto.health = searchResult.health;
    dto.industry = searchResult.industry;
    dto.source = searchResult.source;
    dto.primary_domain = searchResult.primary_domain;
    dto.secondary_domain = searchResult.secondary_domain;
    dto.annual_revenue = searchResult.annual_revenue;
    dto.employees = searchResult.employees;
    dto.website = searchResult.website;
    dto.billing_address = searchResult.billing_address;
    dto.shipping_address = searchResult.shipping_address;
    dto.account_owner_id = searchResult.account_owner_id;
    dto.account_owner_email = searchResult.account_owner_email;
    dto.account_owner_name = searchResult.account_owner_name;
    dto.account_owner_user_type = searchResult.account_owner_user_type;
    dto.account_owner_status = searchResult.account_owner_status;
    dto.account_owner_timezone = searchResult.account_owner_timezone;
    dto.metadata = searchResult.metadata;
    dto.organization_id = searchResult.organization_id;
    dto.created_at = searchResult.created_at;
    dto.updated_at = searchResult.updated_at;
    dto.deleted_at = searchResult.deleted_at;
    // 
    dto.health_uid = searchResult.health_uid;
    dto.health_value = searchResult.health_value;
    dto.classification_uid = searchResult.classification_uid;
    dto.classification_value = searchResult.classification_value;
    dto.status_uid = searchResult.status_uid;
    dto.status_value = searchResult.status_value;
    dto.industry_uid = searchResult.industry_uid;
    dto.industry_value = searchResult.industry_value;
    // Add more mappings as needed for API response
    return dto;
  }
}
