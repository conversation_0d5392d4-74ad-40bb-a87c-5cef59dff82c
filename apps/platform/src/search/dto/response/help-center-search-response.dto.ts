import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsOptional, IsString } from "class-validator";

export class HelpCenterSearchResponseDto {
  @ApiProperty()
  @IsString()
  uid: string;

  @ApiProperty()
  @IsString()
  organizationUid: string;

  @ApiProperty()
  @IsString()
  title: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  body?: string;

  @ApiProperty()
  @IsString()
  visibility: string;

  @ApiProperty({ type: [String] })
  @IsArray()
  tags: string[] = [];

  @ApiProperty({ name: "helpCenterIds", type: [String] })
  @IsArray()
  helpCenterIds: string[] = [];

  static fromSearchResult(searchResult: any): HelpCenterSearchResponseDto {
    const dto = new HelpCenterSearchResponseDto();

    // Map snake_case fields from source to camelCase properties
    dto.uid = searchResult.uid?.toString() || "";
    dto.organizationUid =
      searchResult.organization_uid || searchResult.organizationUid || "";
    dto.title = searchResult.title || "";
    dto.description = searchResult.description || "";
    dto.body = searchResult.body || "";
    dto.visibility = searchResult.visibility || "";
    dto.tags = Array.isArray(searchResult.tags) ? searchResult.tags : [];
    dto.helpCenterIds = Array.isArray(searchResult.help_center_ids)
      ? searchResult.help_center_ids
      : [];

    return dto;
  }
}
