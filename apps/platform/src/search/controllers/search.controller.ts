import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Headers,
  Inject,
  Param,
  ParseEnumPipe,
  Post,
  Query,
  Res,
  UnauthorizedException,
} from "@nestjs/common";
import {
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  getSchemaPath,
} from "@nestjs/swagger";
import { ILogger } from "@repo/nestjs-commons/logger";
import { FastifyReply } from "fastify";
import { CurrentUser } from "../../common/decorators";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { TeamsService } from "../../teams/services/teams.service";
import { HelpCenterSearchResponseDto } from "../dto/response/help-center-search-response.dto";
import {
  AccountSearchResponseDto,
  CommentSearchResponseDto,
  ThenaMultiSearchDto,
  ThenaSearchDto,
  TicketSearchResponseDto,
} from "../dto/search.dto";
import { SearchCollection } from "../enums/search-collections.enum";
import { SearchService } from "../services/search.service";

function extractSearchKey(headers: Record<string, any>): string | undefined {
  return headers["x-thena-search-key"] || headers["X-Thena-Search-Key"];
}

@SkipAllThrottler()
@ApiTags("Search")
@Controller("v1/search")
@ApiExtraModels(
  TicketSearchResponseDto,
  AccountSearchResponseDto,
  CommentSearchResponseDto,
  HelpCenterSearchResponseDto
)
export class SearchController {
  constructor(
    private readonly searchService: SearchService,
    private readonly teamsService: TeamsService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) { }

  @Get(":collection")
  @ApiOperation({
    summary: "Entity search",
    description: `Search for tickets, accounts, comments and help center`,
  })
  @ApiParam({
    name: "collection",
    enum: SearchCollection,
    description:
      "The collection to search (tickets, comments, accounts, customer_contacts, help_center)",
    example: "tickets",
  })
  @ApiQuery({
    name: "query_by",
    required: true,
    type: String,
    example: "title,description",
    description: "Fields to query by (comma separated)",
  })
  @ApiQuery({
    name: "per_page",
    required: false,
    type: String,
    example: "10",
    description: "Results per page",
  })
  @ApiQuery({
    name: "q",
    required: false,
    type: String,
    example: "*",
    description: "The search query",
  })
  @ApiQuery({
    name: "include_fields",
    required: false,
    type: String,
    example:
      "uid,title,statusName,createdAt,updatedAt,priorityName,ticketIdentifier",
    description: "Fields to include in the response (comma separated)",
  })
  @ApiQuery({
    name: "filter_by",
    required: false,
    type: String,
    example: "priorityName:=medium&&statusName:=resolved",
    description: "Filter by expression",
  })
  @ApiQuery({
    name: "streaming",
    required: false,
    type: Boolean,
    example: false,
    description: "Enable streaming of paginated results",
  })
  @ApiOkResponse({
    schema: {
      type: "object",
      properties: {
        result: {
          type: "object",
          properties: {
            facet_counts: { type: "array", items: { type: "object" } },
            found: { type: "number" },
            hits: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  document: {
                    oneOf: [
                      { $ref: getSchemaPath(TicketSearchResponseDto) },
                      { $ref: getSchemaPath(AccountSearchResponseDto) },
                      { $ref: getSchemaPath(CommentSearchResponseDto) },
                      { $ref: getSchemaPath(HelpCenterSearchResponseDto) },
                    ],
                  },
                },
              },
            },
            page: { type: "number" },
            request_params: { type: "object" },
            search_cutoff: { type: "boolean" },
          },
        },
      },
    },
    description: "Search result",
  })
  async search(
    @Param("collection", new ParseEnumPipe(SearchCollection))
    collection: SearchCollection,
    @Query() query: ThenaSearchDto,
    @Headers() headers: Record<string, any>,
    @CurrentUser() user: CurrentUser,
    @Res({ passthrough: true }) reply: FastifyReply,
  ) {
    const searchKey = extractSearchKey(headers);
    if (!user) {
      throw new UnauthorizedException();
    }
    //lets get the UIDs the user is a part of
    const teams = await this.teamsService.getTeamsByUser(
      user,
      { teamId: true },
      { team: true },
    );

    if (teams.length === 0) {
      throw new BadRequestException("User is not a member of any teams");
    }
    const teamIds = teams.filter(team => team.team).map((team) => team.team.uid);
    this.logger.log(`Team IDs: ${teamIds}`);

    // Parse streaming param (query params are always strings)
    const isStreaming = query.streaming === "true" || query.streaming === "1";

    if (!isStreaming) {
      const result = await this.searchService.search(
        { ...query, collection },
        searchKey,
        teamIds,
        user.orgUid,
      );
      return reply.status(result.status).send(result.data);
    }

    // Streaming logic
    reply.raw.setHeader("Content-Type", "application/json; charset=utf-8");
    reply.raw.setHeader("Transfer-Encoding", "chunked");
    reply.raw.setHeader("X-Accel-Buffering", "no");

    const writeChunk = (data: any) => {
      reply.raw.write(`\n${JSON.stringify(data)}`);
    };

    // Heartbeat mechanism: send a newline every 5 seconds to keep the connection alive
    const heartbeat = setInterval(() => {
      reply.raw.write("\n");
    }, 5000);

    await this.searchService.streamSearch(
      { ...query, collection },
      searchKey,
      teamIds,
      user.orgUid,
      writeChunk,
    );
    clearInterval(heartbeat);
    reply.raw.end();
    return undefined;
  }

  @Post("multi")
  @ApiOperation({
    summary: "Federated search",
    description: "Federated search across all entity collections",
  })
  async multiSearch(
    @Body() body: ThenaMultiSearchDto,
    @Headers() headers: Record<string, any>,
    @CurrentUser() user: CurrentUser,
    @Res({ passthrough: true }) reply: FastifyReply,
  ) {
    const searchKey = extractSearchKey(headers);

    if (!user) {
      throw new UnauthorizedException();
    }
    // lets get the UIDs the user is a part of
    const teams = await this.teamsService.getTeamsByUser(user);
    const teamIds = teams.map((team) => team.team.uid);

    const result = await this.searchService.multiSearch(
      body,
      searchKey,
      teamIds,
      user.orgUid,
    );
    return reply.status(result.status).send(result.data);
  }
}
