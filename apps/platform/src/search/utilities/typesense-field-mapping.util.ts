import { AccountCollectionDto } from "../dto/account.collection.dto";
import { CommentCollectionDto } from "../dto/comment.collection.dto";
import { HelpCenterCollectionDto } from "../dto/help-center.collection.dto";
import { TicketCollectionDto } from "../dto/ticket.collection.dto";
import { SearchCollection } from "../enums/search-collections.enum";

/**
 * Maps filter_by string by splitting on '&&', mapping field names using the provided mapFieldsFn,
 * and rejoining the conditions.
 * @param filterBy The filter_by string (e.g. 'priorityName:=medium&&statusName:=resolved')
 * @param mapFieldsFn Function to map an array of field names to Typesense field names
 */
function mapFilterByFields(
  filterBy: string,
  mapFieldsFn: (fields: string[]) => string[],
): string {
  if (!filterBy) return filterBy;
  const conditions = filterBy.split("&&").map((cond) => cond.trim());
  const mappedConditions = conditions.map((cond) => {
    // Match field name and operator+value
    const match = cond.match(
      /^([^:><!]+)(:?[><!]?=|:?[><]=|:>|:<|:!=|:=)(.+)$/,
    );
    if (match) {
      const [, field, operator, value] = match;
      const mappedField = mapFieldsFn([field.trim()])[0];
      return `${mappedField}${operator}${value.trim()}`;
    }
    // If no match, return as is
    return cond;
  });
  return mappedConditions.join("&&");
}

export function mapFieldsForTypesense(
  collection: SearchCollection,
  fields: {
    query_by?: string;
    include_fields?: string;
    filter_by?: string;
    sort_by?: string;
  },
): {
  query_by?: string;
  include_fields?: string;
  filter_by?: string;
  sort_by?: string;
} {
  let typesenseQueryBy = fields.query_by;
  let typesenseIncludeFields = fields.include_fields;
  let typesenseFilterBy = fields.filter_by;
  let typesenseSortBy = fields.sort_by;
  switch (collection) {
    case SearchCollection.TICKETS:
      if (fields.query_by) {
        typesenseQueryBy = TicketCollectionDto.mapToTypesenseFields(
          fields.query_by.split(",").map((f) => f.trim()),
        ).join(",");
      }
      if (fields.include_fields) {
        typesenseIncludeFields = TicketCollectionDto.mapToTypesenseFields(
          fields.include_fields.split(",").map((f) => f.trim()),
        ).join(",");
      }
      if (fields.filter_by) {
        typesenseFilterBy = mapFilterByFields(
          fields.filter_by,
          TicketCollectionDto.mapToTypesenseFields.bind(TicketCollectionDto),
        );
      }
      if (fields.sort_by) {
        typesenseSortBy = TicketCollectionDto.mapToTypesenseFields(
          fields.sort_by.split(",").map((f) => f.trim()),
        ).join(",");
      }
      break;
    case SearchCollection.ACCOUNTS:
      if (fields.query_by) {
        typesenseQueryBy = AccountCollectionDto.mapToTypesenseFields(
          fields.query_by.split(",").map((f) => f.trim()),
        ).join(",");
      }
      if (fields.include_fields) {
        typesenseIncludeFields = AccountCollectionDto.mapToTypesenseFields(
          fields.include_fields.split(",").map((f) => f.trim()),
        ).join(",");
      }
      if (fields.filter_by) {
        typesenseFilterBy = mapFilterByFields(
          fields.filter_by,
          AccountCollectionDto.mapToTypesenseFields.bind(AccountCollectionDto),
        );
      }
      if (fields.sort_by) {
        typesenseSortBy = AccountCollectionDto.mapToTypesenseFields(
          fields.sort_by.split(",").map((f) => f.trim()),
        ).join(",");
      }
      break;
    case SearchCollection.COMMENTS:
      if (fields.query_by) {
        typesenseQueryBy = CommentCollectionDto.mapToTypesenseFields(
          fields.query_by.split(",").map((f) => f.trim()),
        ).join(",");
      }
      if (fields.include_fields) {
        typesenseIncludeFields = CommentCollectionDto.mapToTypesenseFields(
          fields.include_fields.split(",").map((f) => f.trim()),
        ).join(",");
      }
      if (fields.filter_by) {
        typesenseFilterBy = mapFilterByFields(
          fields.filter_by,
          CommentCollectionDto.mapToTypesenseFields.bind(CommentCollectionDto),
        );
      }
      if (fields.sort_by) {
        typesenseSortBy = CommentCollectionDto.mapToTypesenseFields(
          fields.sort_by.split(",").map((f) => f.trim()),
        ).join(",");
      }
      break;
    case SearchCollection.HELP_CENTER:
      if (fields.query_by) {
        typesenseQueryBy = HelpCenterCollectionDto.mapToTypesenseFields(
          fields.query_by.split(",").map((f) => f.trim()),
        ).join(",");
      }
      if (fields.include_fields) {
        typesenseIncludeFields = HelpCenterCollectionDto.mapToTypesenseFields(
          fields.include_fields.split(",").map((f) => f.trim()),
        ).join(",");
      }
      if (fields.filter_by) {
        typesenseFilterBy = mapFilterByFields(
          fields.filter_by,
          HelpCenterCollectionDto.mapToTypesenseFields.bind(
            HelpCenterCollectionDto,
          ),
        );
      }
      if (fields.sort_by) {
        typesenseSortBy = HelpCenterCollectionDto.mapToTypesenseFields(
          fields.sort_by.split(",").map((f) => f.trim()),
        ).join(",");
      }
      break;
  }
  return {
    query_by: typesenseQueryBy,
    include_fields: typesenseIncludeFields,
    filter_by: typesenseFilterBy,
    sort_by: typesenseSortBy,
  };
}
