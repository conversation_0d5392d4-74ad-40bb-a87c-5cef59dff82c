import { TeamUserRoutingStrategy } from "@repo/thena-platform-entities";
import { z } from "zod";

export const createTeamSchema = z.object({
  name: z.string().min(6),
  description: z.string().optional(),
  identifier: z.string().optional(),
  parentTeamId: z.string().optional(),
  isPrivate: z.boolean().optional(),
});

export const updateTeamSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  isPrivate: z.boolean().optional(),
  icon: z.string().optional(),
});

export const addTeamMemberSchema = z.object({
  teamId: z.string(),
  email: z.string().email().optional(),
  userId: z.string().optional(),
  isAdmin: z.boolean().optional(),
});

// Helper function to validate time format (HH:mm)
const isValidTimeFormat = (time: string) => {
  const timeRegex = /^([0-1][0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
};

// Business Slot Schema
const businessSlotSchema = z.object({
  start: z.string().refine(isValidTimeFormat, {
    message: "Start time must be in 24-hour format (HH:mm)",
  }),
  end: z.string().refine(isValidTimeFormat, {
    message: "End time must be in 24-hour format (HH:mm)",
  }),
});

const businessDaySchema = z
  .object({
    isActive: z.boolean({
      required_error: "The business day must be active or inactive!",
    }),
    slots: z.array(businessSlotSchema).optional(),
  })
  .superRefine((obj, ctx) => {
    // If inactive, slots can be undefined or empty
    if (!obj.isActive) {
      return;
    }

    // If active, slots must be present and not empty
    if (!obj.slots || obj.slots.length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["slots"],
        message: "Active business days must have at least one time slot",
      });
      return;
    }

    // Validate that slots don't overlap and are in chronological order
    for (let i = 1; i < obj.slots.length; i++) {
      const prevEnd = obj.slots[i - 1].end;
      const currentStart = obj.slots[i].start;
      if (prevEnd >= currentStart) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["slots"],
          message:
            "Time slots must not overlap and must be in chronological order",
        });
        return;
      }
    }
  });

// Business Hours Config Schema
const businessHoursConfigSchema = z.object({
  monday: businessDaySchema.optional(),
  tuesday: businessDaySchema.optional(),
  wednesday: businessDaySchema.optional(),
  thursday: businessDaySchema.optional(),
  friday: businessDaySchema.optional(),
  saturday: businessDaySchema.optional(),
  sunday: businessDaySchema.optional(),
});

// Main Schema
export const updateTimezoneWorkingHoursSchema = z.object({
  timezone: z
    .string()
    .refine((tz) => {
      try {
        Intl.DateTimeFormat(undefined, { timeZone: tz });
        return true;
      } catch (_error) {
        return false;
      }
    }, "Invalid timezone")
    .optional(),
  routingRespectsTimezone: z.boolean().optional(),
  routingRespectsUserTimezone: z.boolean().optional(),
  routingRespectsUserAvailability: z.boolean().optional(),
  userRoutingStrategy: z.nativeEnum(TeamUserRoutingStrategy).optional(),
  dailyConfig: businessHoursConfigSchema.optional(),
});

// Enum for rule operators
const RuleOperatorEnum = z.enum([
  "equals",
  "not_equals",
  "contains",
  "not_contains",
  "greater_than",
  "less_than",
  "in",
  "not_in",
  "matches",
  "is_null",
  "is_not_null",
]);

// Schema for a single rule
const RuleSchema = z.object({
  field: z.string().min(1, "Field is required"),
  operator: RuleOperatorEnum,
  value: z.string().min(1, "Value is required"),
  precedence: z.number().optional(),
});

// Schema for creating a routing rule
export const CreateRoutingRuleSchema = z
  .object({
    name: z.string().min(1, "Name is required"),
    description: z.string().optional(),
    teamId: z.string().min(1, "Team ID is required"),
    evaluationOrder: z.number().optional(),
    resultTeamId: z.string().min(1, "Result team ID is required"),
    andRules: z
      .array(RuleSchema)
      .min(1, "At least one AND rule is required")
      .optional(),
    orRules: z
      .array(RuleSchema)
      .min(1, "At least one OR rule is required")
      .optional(),
  })
  .refine((data) => data.andRules?.length > 0 || data.orRules?.length > 0, {
    message: "At least one of andRules or orRules must be provided",
    path: ["rules"], // This will show the error at the rules level
  });

// Schema for updating a routing rule
export const UpdateRoutingRuleSchema = z
  .object({
    id: z.string().min(1, "Rule ID is required"),
    name: z.string().min(1, "Name is required").optional(),
    teamId: z.string().min(1, "Team ID is required").optional(),
    description: z.string().optional(),
    evaluationOrder: z.number().optional(),
    resultTeamId: z.string().min(1, "Result team ID is required").optional(),
    andRules: z
      .array(RuleSchema)
      .min(1, "At least one AND rule is required")
      .optional(),
    orRules: z
      .array(RuleSchema)
      .min(1, "At least one OR rule is required")
      .optional(),
  })
  .refine(
    (data) => {
      // Only validate rules if they are being updated
      if (data.andRules || data.orRules) {
        return data.andRules?.length > 0 || data.orRules?.length > 0;
      }
      return true;
    },
    {
      message:
        "If updating rules, at least one of andRules or orRules must be provided",
      path: ["rules"],
    },
  );
