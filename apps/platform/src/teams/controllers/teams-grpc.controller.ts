import { <PERSON>ada<PERSON> } from "@grpc/grpc-js";
import { BadRequestEx<PERSON>, Controller, UseGuards } from "@nestjs/common";
import { GrpcMethod } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import { extractUserMetadata } from "@repo/nestjs-commons/utils";
import { teams } from "@repo/shared-proto";
import { UpdateTimezoneWorkingHoursDto } from "../../common/dto";
import { AddTeamMemberDto } from "../dto";
import {
  CreateRoutingRuleGroupDto,
  UpdateRoutingRuleGroupDto,
} from "../dto/team-routing.dto";
import { CreateTeamDto, UpdateTeamDto } from "../dto/teams.dto";
import { TeamAnnotatorService } from "../services/team-annotator.service";
import { TeamsService } from "../services/teams.service";
import {
  addTeamMemberSchema,
  CreateRoutingRuleSchema,
  createTeamSchema,
  UpdateRoutingRuleSchema,
  updateTeamSchema,
  updateTimezoneWorkingHoursSchema,
} from "../validators";

@Controller("v1/teams")
@UseGuards(GrpcAuthGuard)
export class TeamsGrpcController {
  constructor(
    private readonly teamsService: TeamsService,
    private readonly teamAnnotatorService: TeamAnnotatorService,
  ) {}

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "CreateTeam")
  async createTeam(data: teams.CreateTeamRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Validate the request
      const parsedData = createTeamSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException("Invalid request!");
      }

      // Create the team payload
      const payload = {
        ...parsedData.data,
        name: parsedData.data.name,
      } as CreateTeamDto;

      // Create the team
      const team = await this.teamsService.createTeam(payload, user);

      // Format the response
      const response: teams.CommonTeamResponse = {
        id: team.uid,
        name: team.name,
        description: team.description,
        identifier: team.identifier,
        parentTeamId: team.parentTeam?.uid,
        isPrivate: team.isPrivate,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "UpdateTeam")
  async updateTeam(data: teams.UpdateTeamRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Validate the request
      const parsedData = updateTeamSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException("Invalid request!");
      }

      // Create the update team payload
      const payload = {
        ...parsedData.data,
        name: parsedData.data.name,
      } as UpdateTeamDto;

      // Update the team
      const team = await this.teamsService.updateTeam(data.id, payload, user);

      // Format the response
      const response: teams.CommonTeamResponse = {
        id: team.uid,
        name: team.name,
        description: team.description,
        identifier: team.identifier,
        parentTeamId: team.parentTeam?.uid,
        isPrivate: team.isPrivate,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "DeleteTeam")
  async deleteTeam(data: teams.DeleteTeamRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.id) {
        throw new BadRequestException("Team ID is required!");
      }

      // Delete the team
      await this.teamsService.deleteOneTeam(data.id, user);

      // Return the response
      return {};
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "GetTeamById")
  async getTeamById(data: teams.GetTeamByIdRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.id) {
        throw new BadRequestException("Team ID is required!");
      }

      // Get the team by its ID
      const team = await this.teamsService.findOneTeamById(data.id, user);

      // Format the response
      const response: teams.CommonTeamResponse = {
        id: team.uid,
        name: team.name,
        description: team.description,
        identifier: team.identifier,
        parentTeamId: team.parentTeam?.uid,
        isPrivate: team.isPrivate,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "GetAllTeams")
  async getAllTeams(_data: teams.GetAllTeamsRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Get all teams
      const teams = await this.teamsService.findAllTeams(user);

      // Format the response
      const response: teams.GetAllTeamsResponse = {
        teams: teams.map((team) => ({
          id: team.uid,
          name: team.name,
          description: team.description,
          identifier: team.identifier,
          parentTeamId: team.parentTeam?.uid,
          isPrivate: team.isPrivate,
        })),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "GetAllPublicTeams")
  async getAllPublicTeams(
    _data: teams.GetAllPublicTeamsRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Get all public teams
      const teams = await this.teamsService.getAllPublicTeams(user);

      // Format the response
      const response: teams.GetAllPublicTeamsResponse = {
        teams: teams.map((team) => ({
          id: team.uid,
          name: team.name,
          description: team.description,
          identifier: team.identifier,
          parentTeamId: team.parentTeam?.uid,
          isPrivate: team.isPrivate,
        })),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "AddTeamMember")
  async addTeamMember(data: teams.AddTeamMemberRequest, metadata: Metadata) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Validate the request
      const parsedData = addTeamMemberSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException("Invalid request!");
      }

      // Check if the email or user ID is provided
      if (!parsedData.data.email && !parsedData.data.userId) {
        throw new BadRequestException(
          "Either email or user ID is required to add a member to a team!",
        );
      }

      // Create the payload
      const payload = {
        email: parsedData.data.email,
        userId: parsedData.data.userId,
        isAdmin: parsedData.data.isAdmin,
      } as AddTeamMemberDto;

      // Add the team member
      const teamMember = await this.teamsService.addMemberToTeam(
        parsedData.data.teamId,
        payload,
        user,
      );

      // Format the response
      const response: teams.TeamMemberResponse = {
        id: teamMember.user.uid,
        name: teamMember.user.name,
        email: teamMember.user.email,
        invitedBy: teamMember.invitedBy?.name,
        teamId: teamMember.team.uid,
        teamName: teamMember.team.name,
        isActive: teamMember.isActive,
        role: teamMember.role,
        isOwner: teamMember.team.teamOwnerId === teamMember.user.id,
        joinedAt: teamMember.joinedAt.toString(),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "RemoveTeamMember")
  async removeTeamMember(
    data: teams.RemoveTeamMemberRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Check if the team ID and member ID are provided
      if (!data.teamId || !data.memberId) {
        throw new BadRequestException("Team ID and member ID are required!");
      }

      // Remove the team member
      await this.teamsService.removeMemberFromTeam(
        data.teamId,
        data.memberId,
        user,
      );

      // Return the response
      return {};
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "GetAllTeamMembers")
  async getAllTeamMembers(
    data: teams.GetAllTeamMembersRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.teamId) {
        throw new BadRequestException("Team ID is required!");
      }

      // Get all team members
      const teamMembers = await this.teamsService.findTeamMembers(
        data.teamId,
        user,
      );

      // Format the response
      const response: teams.GetAllTeamMembersResponse = {
        members: teamMembers.map((member) => ({
          id: member.user.uid,
          name: member.user.name,
          email: member.user.email,
          invitedBy: member.invitedBy?.name,
          teamId: member.team.uid,
          teamName: member.team.name,
          isActive: member.isActive,
          role: member.role,
          isOwner: member.team.teamOwnerId === member.user.id,
          joinedAt: member.joinedAt.toString(),
        })),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "GetTeamConfigurations")
  async getTeamConfigurations(
    data: teams.GetTeamConfigurationsRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.teamId) {
        throw new BadRequestException("Team ID is required!");
      }

      // Get the team configurations
      const teamConfigurations = await this.teamsService.getTeamConfigurations(
        data.teamId,
        user,
      );

      // Format the response
      const response: teams.TeamConfigurationsResponse = {
        teamId: teamConfigurations.teamId,
        timezone: teamConfigurations.teamConfig.timezone,
        holidays: teamConfigurations.teamConfig.holidays,
        routingRespectsTimezone:
          teamConfigurations.teamConfig.routingRespectsTimezone,
        routingRespectsUserTimezone:
          teamConfigurations.teamConfig.routingRespectsUserTimezone,
        routingRespectsUserAvailability:
          teamConfigurations.teamConfig.routingRespectsUserAvailability,
        routingRespectsUserBusinessHours:
          teamConfigurations.teamConfig.routingRespectsUserBusinessHours,
        userRoutingStrategy: teamConfigurations.teamConfig.userRoutingStrategy,
        dailyConfig: teamConfigurations.businessHoursConfig,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "UpdateTeamConfigurations")
  async updateTeamConfigurations(
    data: teams.UpdateTeamConfigurationsRequest,
    metadata: Metadata,
  ): Promise<teams.TeamConfigurationsResponse> {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Check if the team ID is provided
      if (!data.teamId) {
        throw new BadRequestException("Team ID is required!");
      }

      // Validate the request
      const parsedData = updateTimezoneWorkingHoursSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException("Invalid request!");
      }

      // Create the payload
      const payload = {
        timezone: parsedData.data.timezone,
        routingRespectsTimezone: parsedData.data.routingRespectsTimezone,
        routingRespectsUserTimezone:
          parsedData.data.routingRespectsUserTimezone,
        routingRespectsUserAvailability:
          parsedData.data.routingRespectsUserAvailability,
        routingRespectsUserBusinessHours:
          parsedData.data.routingRespectsUserBusinessHours,
        userRoutingStrategy: parsedData.data.userRoutingStrategy,
        businessHours: parsedData.data.dailyConfig,
      } as UpdateTimezoneWorkingHoursDto;

      // Update the team configurations
      const updatedTeamConfigurations =
        await this.teamsService.updateTeamConfigurations(
          data.teamId,
          payload,
          user,
        );

      // Format the response
      const response: teams.TeamConfigurationsResponse = {
        teamId: updatedTeamConfigurations.teamId,
        timezone: updatedTeamConfigurations.teamConfig.timezone,
        holidays: updatedTeamConfigurations.teamConfig.holidays,
        routingRespectsTimezone:
          updatedTeamConfigurations.teamConfig.routingRespectsTimezone,
        routingRespectsUserTimezone:
          updatedTeamConfigurations.teamConfig.routingRespectsUserTimezone,
        routingRespectsUserAvailability:
          updatedTeamConfigurations.teamConfig.routingRespectsUserAvailability,
        routingRespectsUserBusinessHours:
          updatedTeamConfigurations.teamConfig.routingRespectsUserBusinessHours,
        userRoutingStrategy:
          updatedTeamConfigurations.teamConfig.userRoutingStrategy,
        dailyConfig: updatedTeamConfigurations.businessHoursConfig,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "CreateRoutingRule")
  async createRoutingRule(
    data: teams.CreateRoutingRuleRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Validate the request
      const parsedData = CreateRoutingRuleSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException("Invalid request!");
      }

      // Create the payload
      const payload = {
        name: parsedData.data.name,
        description: parsedData.data.description,
        teamId: parsedData.data.teamId,
        evaluationOrder: parsedData.data.evaluationOrder,
        resultTeamId: parsedData.data.resultTeamId,
        andRules: parsedData.data.andRules,
        orRules: parsedData.data.orRules,
      } as CreateRoutingRuleGroupDto;

      // Create the routing rule
      const routingRule = await this.teamsService.createRoutingRule(
        parsedData.data.teamId,
        user,
        payload,
      );

      // Format the response
      const response: teams.CommonRoutingRuleResponse = {
        id: routingRule.uid,
        name: routingRule.name,
        description: routingRule.description,
        teamId: routingRule.team.uid,
        evaluationOrder: routingRule.priority,
        resultTeamId: routingRule.resultTeamId,
        andRules: routingRule.andRules,
        orRules: routingRule.orRules,
        createdBy: routingRule.createdBy.uid,
        createdAt: routingRule.createdAt.toString(),
        updatedAt: routingRule.updatedAt.toString(),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "UpdateRoutingRule")
  async updateRoutingRule(
    data: teams.UpdateRoutingRuleRequest,
    metadata: Metadata,
  ) {
    try {
      // Extract the user metadata from the metadata object
      const user = extractUserMetadata(metadata);

      // Validate the request
      const parsedData = UpdateRoutingRuleSchema.safeParse(data);
      if (!parsedData.success) {
        throw new BadRequestException("Invalid request!");
      }

      // Create the payload
      const payload = {
        name: parsedData.data.name,
        description: parsedData.data.description,
        evaluationOrder: parsedData.data.evaluationOrder,
        resultTeamId: parsedData.data.resultTeamId,
        andRules: parsedData.data.andRules,
        orRules: parsedData.data.orRules,
      } as UpdateRoutingRuleGroupDto;

      // Update the routing rule
      const routingRule = await this.teamsService.updateRoutingRule(
        parsedData.data.teamId,
        parsedData.data.id,
        user,
        payload,
      );

      // Format the response
      const response: teams.CommonRoutingRuleResponse = {
        id: routingRule.uid,
        name: routingRule.name,
        description: routingRule.description,
        teamId: routingRule.team.uid,
        evaluationOrder: routingRule.priority,
        resultTeamId: routingRule.resultTeamId,
        andRules: routingRule.andRules,
        orRules: routingRule.orRules,
        createdBy: routingRule.createdBy.uid,
        createdAt: routingRule.createdAt.toString(),
        updatedAt: routingRule.updatedAt.toString(),
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, "GetTeamFieldMetadata")
  getTeamFieldMetadata(): teams.GetTeamFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getTeamFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, "GetTeamMemberFieldMetadata")
  getTeamMemberFieldMetadata(): teams.GetTeamMemberFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getTeamMemberFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    teams.TEAM_ANNOTATOR_SERVICE_NAME,
    "GetTeamConfigurationFieldMetadata",
  )
  getTeamConfigurationFieldMetadata(): teams.GetTeamConfigurationFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getTeamConfigurationFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, "GetTeamCapacityFieldMetadata")
  getTeamCapacityFieldMetadata(): teams.GetTeamCapacityFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getTeamCapacityFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    teams.TEAM_ANNOTATOR_SERVICE_NAME,
    "GetBusinessHoursConfigFieldMetadata",
  )
  getBusinessHoursConfigFieldMetadata(): teams.GetBusinessHoursConfigFieldMetadataResponse {
    try {
      return this.teamAnnotatorService.getBusinessHoursConfigFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, "GetTeamData")
  async getTeamData(
    data: teams.GetTeamDataRequest,
    metadata: Metadata,
  ): Promise<teams.GetTeamDataResponse> {
    try {
      // Extract the user
      const user = extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getTeamData(
        data.teamId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, "GetTeamMemberData")
  async getTeamMemberData(
    data: teams.GetTeamMemberDataRequest,
    metadata: Metadata,
  ): Promise<teams.GetTeamMemberDataResponse> {
    try {
      // Extract the user
      const user = extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getTeamMemberData(
        data.teamId,
        data.memberId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, "GetTeamConfigurationData")
  async getTeamConfigurationData(
    data: teams.GetTeamConfigurationDataRequest,
    metadata: Metadata,
  ): Promise<teams.GetTeamConfigurationDataResponse> {
    try {
      // Extract the user
      const user = extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getTeamConfigurationData(
        data.teamId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, "GetTeamCapacityData")
  async getTeamCapacityData(
    data: teams.GetTeamCapacityDataRequest,
    metadata: Metadata,
  ): Promise<teams.GetTeamCapacityDataResponse> {
    try {
      // Extract the user
      const user = extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getTeamCapacityData(
        data.teamId,
        data.userId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAM_ANNOTATOR_SERVICE_NAME, "GetBusinessHoursConfigData")
  async getBusinessHoursConfigData(
    data: teams.GetBusinessHoursConfigDataRequest,
    metadata: Metadata,
  ): Promise<teams.GetBusinessHoursConfigDataResponse> {
    try {
      // Extract the user
      const user = extractUserMetadata(metadata);

      return await this.teamAnnotatorService.getBusinessHoursConfigData(
        data.teamId,
        data.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(teams.TEAMS_SERVICE_NAME, "CheckTeamAvailability")
  async checkTeamAvailability(
    data: teams.CheckTeamAvailabilityRequest,
    metadata: Metadata,
  ): Promise<teams.CheckTeamAvailabilityResponse> {
    try {
      const user = extractUserMetadata(metadata);

      if (!data.teamId) {
        throw new BadRequestException("Team ID is required!");
      }

      return await this.teamsService.checkTeamAvailability(
        user,
        data.teamId,
        data.specificHolidays ?? [],
      );
    } catch (error) {
      handleRpcError(error);
    }
  }
}
