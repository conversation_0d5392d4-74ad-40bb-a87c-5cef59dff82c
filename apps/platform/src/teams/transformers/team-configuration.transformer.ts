import { ApiProperty } from "@nestjs/swagger";
import {
  BusinessHoursConfig,
  TeamConfiguration,
  TeamUserRoutingStrategy,
} from "@repo/thena-platform-entities";
import { BusinessDayDto } from "../../common/dto";

export class BusinessDays {
  @ApiProperty({ description: "The business hours for Monday" })
  monday: BusinessDayDto;

  @ApiProperty({ description: "The business hours for Tuesday" })
  tuesday: BusinessDayDto;

  @ApiProperty({ description: "The business hours for Wednesday" })
  wednesday: BusinessDayDto;

  @ApiProperty({ description: "The business hours for Thursday" })
  thursday: BusinessDayDto;

  @ApiProperty({ description: "The business hours for Friday" })
  friday: BusinessDayDto;

  @ApiProperty({ description: "The business hours for Saturday" })
  saturday: BusinessDayDto;

  @ApiProperty({ description: "The business hours for Sunday" })
  sunday: BusinessDayDto;
}

export class TeamConfigurationsResponseDto {
  @ApiProperty({ description: "The team timezone" })
  timezone: string;

  @ApiProperty({ description: "Unique identifier for the team" })
  teamId: string;

  @ApiProperty({ description: "The fallback sub team" })
  fallbackSubTeam: string;

  @ApiProperty({ description: "The team holidays" })
  holidays: string[];

  @ApiProperty({ description: "Whether routing respects timezone" })
  routingRespectsTimezone: boolean;

  @ApiProperty({ description: "Whether routing respects user timezone" })
  routingRespectsUserTimezone: boolean;

  @ApiProperty({ description: "Whether routing respects user availability" })
  routingRespectsUserAvailability: boolean;

  @ApiProperty({ description: "The user routing strategy" })
  userRoutingStrategy: TeamUserRoutingStrategy;

  @ApiProperty({
    description: "Whether the common daily business hours config is enabled",
  })
  commonDailyConfig: boolean;

  @ApiProperty({ description: "The team daily business hours config" })
  dailyConfig: BusinessDays;

  @ApiProperty({ description: "The created date of the team configuration" })
  createdAt: string;

  @ApiProperty({ description: "The updated date of the team configuration" })
  updatedAt: string;

  static fromEntity(entity: {
    teamId: string;
    teamConfig: TeamConfiguration;
    businessHoursConfig: BusinessHoursConfig;
  }): TeamConfigurationsResponseDto {
    // Initialize the response dto
    const dto = new TeamConfigurationsResponseDto();

    // Assign the team configuration
    dto.timezone = entity.teamConfig.timezone;
    dto.teamId = entity.teamId;
    dto.fallbackSubTeam = entity.teamConfig.fallbackSubTeam?.uid;
    dto.holidays = entity.teamConfig.holidays;
    dto.routingRespectsTimezone = entity.teamConfig.routingRespectsTimezone;
    dto.routingRespectsUserTimezone =
      entity.teamConfig.routingRespectsUserTimezone;
    dto.routingRespectsUserAvailability =
      entity.teamConfig.routingRespectsUserAvailability;
    dto.userRoutingStrategy = entity.teamConfig.userRoutingStrategy;
    dto.createdAt = entity.teamConfig.createdAt
      ? new Date(entity.teamConfig.createdAt).toISOString()
      : undefined;
    dto.updatedAt = entity.teamConfig.updatedAt
      ? new Date(entity.teamConfig.updatedAt).toISOString()
      : undefined;

    // Initialize the dailyConfig
    dto.dailyConfig = new BusinessDays();
    dto.commonDailyConfig = entity.businessHoursConfig.commonDailyConfig;

    // Assign the business hours config to the dailyConfig
    dto.dailyConfig.monday = entity.businessHoursConfig.monday;
    dto.dailyConfig.tuesday = entity.businessHoursConfig.tuesday;
    dto.dailyConfig.wednesday = entity.businessHoursConfig.wednesday;
    dto.dailyConfig.thursday = entity.businessHoursConfig.thursday;
    dto.dailyConfig.friday = entity.businessHoursConfig.friday;
    dto.dailyConfig.saturday = entity.businessHoursConfig.saturday;
    dto.dailyConfig.sunday = entity.businessHoursConfig.sunday;

    return dto;
  }
}

export class TeamAvailabilityResponseDto {
  @ApiProperty({ description: "The team availability" })
  isAvailable: boolean;

  @ApiProperty({
    description: "Reason for the team's availability",
    example: "HOLIDAY",
    enum: ["HOLIDAY", "IN_BUSINESS_HOURS", "OUTSIDE_BUSINESS_HOURS"],
  })
  reason: "HOLIDAY" | "IN_BUSINESS_HOURS" | "OUTSIDE_BUSINESS_HOURS";
}
