import { Injectable } from "@nestjs/common";
import { Organization } from "@repo/thena-platform-entities";
import { OrganizationCreatedEvent } from "./organization.events";

@Injectable()
export class OrganizationEventsFactory {
  private createBaseOrganizationEvent<T extends OrganizationCreatedEvent>(
    EventClass: new () => T,
    organization: Organization,
  ): T {
    if (!organization || !organization?.id) {
      throw new Error(
        "Missing required fields for organization event creation!",
      );
    }

    const event = new EventClass();
    event.organizationId = organization.id;
    event.organizationUID = organization.uid;

    return event;
  }

  createOrganizationCreatedEvent(
    organization: Organization,
  ): OrganizationCreatedEvent {
    return this.createBaseOrganizationEvent(
      OrganizationCreatedEvent,
      organization,
    );
  }
}
