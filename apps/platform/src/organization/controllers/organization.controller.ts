import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  NotFoundException,
  Param,
  Patch,
  Post,
  Req,
  UseInterceptors,
} from "@nestjs/common";
import { <PERSON>pi<PERSON><PERSON>, ApiExcludeController, ApiTags } from "@nestjs/swagger";
import { Throttle } from "@nestjs/throttler";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { GENERIC_ERROR_MESSAGES } from "@repo/thena-shared-libs";
import { FastifyRequest } from "fastify";
import { Public } from "../../auth/decorators/auth.decorator";
import { CurrentUser } from "../../common/decorators";
import { ThrottleTierFour } from "../../common/decorators/throttler.decorator";
import {
  THROTTLER_TIER,
  THROTTLER_TIER_2,
  THROTTLER_TIER_3,
} from "../../config/throttler.config";
import {
  CreateOrganizationDto,
  InviteUserDto,
  JoinOrganizationDto,
  UpdateOrganizationDto,
} from "../dto/organization.dto";
import { OrganizationService } from "../services/organization.service";
import { OrganizationResponseDto } from "../transformers";

@ApiTags("Organizations")
@Controller("v1/organizations")
@ApiExcludeController()
@UseInterceptors(ResponseTransformInterceptor)
export class OrganizationController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly organizationService: OrganizationService,
  ) {}

  @Public()
  @Post()
  @ApiCreateEndpoint({
    summary: "Create an organization",
    responseType: OrganizationResponseDto,
    operationId: "createOrganization",
  })
  @Throttle({ [THROTTLER_TIER_2]: THROTTLER_TIER.TIER_2 }) // This can even go lower since organizations cannot be created by an IP too often
  async create(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Req() request: FastifyRequest,
  ): Promise<OrganizationResponseDto> {
    try {
      // Extract the auth token from the request headers
      const authToken = request.headers.authorization;

      // Create the organization
      const org = await this.organizationService.create(
        createOrganizationDto,
        authToken,
      );

      // Return the organization
      return OrganizationResponseDto.fromEntity(org);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Public()
  @Post("/join")
  @ApiCreateEndpoint({
    summary: "Join an organization",
  })
  @ApiBody({ type: JoinOrganizationDto })
  @ThrottleTierFour()
  async join(
    @Body() joinOrgDto: JoinOrganizationDto,
    @Req() request: FastifyRequest,
  ) {
    try {
      // Extract the auth token from the request headers
      const authToken = request.headers.authorization;

      // Join the organization
      const { userPersona, organization } =
        await this.organizationService.joinOrganization(joinOrgDto, authToken);

      // Return the user persona
      return {
        userId: userPersona.uid,
        organizationId: organization.id,
        organizationUid: organization.uid,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException(
        GENERIC_ERROR_MESSAGES.SOMETHING_WENT_WRONG,
      );
    }
  }

  @Post("/invite")
  @ApiCreateEndpoint({
    summary: "Create an invitation for a user to join an organization",
  })
  @ThrottleTierFour()
  async invite(
    @Body() inviteDto: InviteUserDto,
    @CurrentUser() user: CurrentUser,
  ) {
    const newInvite = await this.organizationService.createInvite(
      inviteDto,
      user,
    );

    return newInvite;
  }

  @Get()
  @ApiGetEndpoint({
    summary: "Find all organizations",
    responseType: OrganizationResponseDto,
  })
  @Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
  async findAll(
    @CurrentUser() user: CurrentUser,
  ): Promise<OrganizationResponseDto[]> {
    const orgs = await this.organizationService.findAll(user);
    return orgs.map(OrganizationResponseDto.fromEntity);
  }

  @Get("/:id")
  @ApiGetEndpoint({
    summary: "Find an organization by identifier",
    responseType: OrganizationResponseDto,
  })
  @Throttle({ [THROTTLER_TIER_3]: THROTTLER_TIER.TIER_3 })
  async findOne(
    @Param("id") id: string,
    @CurrentUser() user: CurrentUser,
  ): Promise<OrganizationResponseDto> {
    const org = await this.organizationService.findOneByIdentifier(user.orgUid);
    if (!org) {
      throw new NotFoundException("Organization not found");
    }

    // Check if the user is authorized to access this organization
    if (id !== org.uid) {
      throw new NotFoundException("Organization not found");
    }

    return OrganizationResponseDto.fromEntity(org);
  }

  @Patch(":id")
  @ApiUpdateEndpoint({
    summary: "Update an organization",
    responseType: OrganizationResponseDto,
  })
  @Throttle({ [THROTTLER_TIER_2]: THROTTLER_TIER.TIER_2 })
  async update(
    @Param("id") id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @CurrentUser() user: CurrentUser,
  ): Promise<OrganizationResponseDto> {
    const org = await this.organizationService.update(
      id,
      updateOrganizationDto,
      user,
    );

    return OrganizationResponseDto.fromEntity(org);
  }

  @Delete(":id")
  @ApiDeleteEndpoint({
    summary: "Delete an organization",
  })
  @Throttle({ [THROTTLER_TIER_2]: THROTTLER_TIER.TIER_2 }) // Same with delete
  async remove(
    @Param("id") id: string,
    @CurrentUser() user: CurrentUser,
  ): Promise<void> {
    await this.organizationService.remove(id, user);
  }
}
