import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { ContextUserType, SNSPublisherService } from "@repo/thena-eventbridge";
import {
  CommentEntityTypes,
  CustomerContact,
  ReactionsRepository,
  User,
} from "@repo/thena-platform-entities";
import { Job } from "bullmq";
import * as rTracer from "cls-rtracer";
import { DeepPartial } from "typeorm";
import { v4 as uuidv4 } from "uuid";
import { ConfigKeys, ConfigService } from "../../config/config.service";
import { QueueNames } from "../../constants/queue.constants";
import {
  REACTION_SNS_PUBLISHER,
  REACTIONS_RELATIONS,
} from "../constants/comments.constants";
import { SnsReactionPayload } from "../interfaces/sns-reaction-payload.interface";
import { CommentOp, getCommentEventType } from "../utils";

@Injectable()
@Processor(QueueNames.REACTION_SNS_PUBLISHER)
export class ReactionSnsConsumer extends WorkerHost {
  constructor(
    @Inject(REACTION_SNS_PUBLISHER)
    private snsPublisherService: SNSPublisherService,

    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly configService: ConfigService,

    private readonly reactionsRepository: ReactionsRepository,
  ) {
    super();
  }

  async process(
    job: Job<{
      name: string;
      emojiId: string;
      previousReaction: any;
      orgId: string;
      commentId: string;
      author: User;
      customerContact: CustomerContact;
      eventType: CommentOp;
      entityType: CommentEntityTypes;
      reqId: unknown;
      metadata: Record<string, any>;
    }>,
  ) {
    try {
      const { data } = job;
      const {
        emojiId,
        previousReaction,
        commentId,
        author,
        customerContact,
        eventType,
        entityType,
        reqId,
        orgId,
      } = data;

      await rTracer.runWithId(async () => {
        this.logger.log(
          `Processing SNS message for job ${job.id}, event type: ${eventType}`,
        );

        if (!emojiId || !commentId || !author) {
          throw new Error("Invalid payload");
        }

        const reaction =
          eventType === CommentOp.REACTION_REMOVED
            ? previousReaction
            : await this.reactionsRepository.findByCondition({
                where: {
                  commentId: commentId,
                  emojiId: emojiId,
                  organizationId: orgId,
                  reactionByUserId: author ? author.id : null,
                  reactionByCustomerContactId: customerContact
                    ? customerContact.id
                    : null,
                },
                relations: REACTIONS_RELATIONS,
              });

        if (!reaction) {
          throw new Error(
            `Reaction not found for commentId=${commentId}, emojiId=${emojiId}`,
          );
        }

        const snsPayload = this.constructSnsPayload(
          reaction,
          eventType,
          entityType,
          author,
        );

        await Promise.race([
          this.publishToSNS(snsPayload as SnsReactionPayload),
          new Promise((_, reject) =>
            setTimeout(() => {
              const error = new Error("SNS publish timeout");
              error.name = "SNSTimeoutError";
              reject(error);
            }, 5000),
          ),
        ]);

        return {
          success: true,
          processedAt: new Date(),
          emojiId: emojiId,
        };
      }, reqId);
    } catch (error) {
      this.logger.error(
        `Error processing SNS message for job ${job.id}: ${error.message}`,
        error?.stack,
      );

      // Determine if we should retry based on error type
      const maxAttempts = job.opts.attempts ?? 1;
      if (job.attemptsMade < maxAttempts - 1) {
        const retryError = new Error(
          `SNS publishing failed (attempt ${job.attemptsMade + 1}): ${
            error.message
          }`,
        );
        retryError.name = "RetryableError";
        throw retryError;
      }

      // On final attempt, mark as permanent failure
      throw error;
    }
  }

  private async publishToSNS(snsPayload: SnsReactionPayload) {
    await this.snsPublisherService.publishSNSMessage({
      subject: snsPayload.eventType,
      message: JSON.stringify(snsPayload),
      topicArn: this.configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
      messageAttributes: {
        event_name: snsPayload.eventType,
        event_id: snsPayload.eventId, // Unique identifier for the event
        event_timestamp: snsPayload.timestamp,
        context_user_id: snsPayload.actor.id,
        context_user_type: snsPayload.actor.type as unknown as ContextUserType,
        context_organization_id: snsPayload.orgId,
      },
      messageGroupId: snsPayload.orgId,
    });
  }

  private constructSnsPayload(
    reaction: {
      emoji: { name: string };
      reactionBy: {
        uid: string;
        name: string;
        avatarUrl: string;
        email: string;
      };
      comment: { uid: string };
      metadata: Record<string, any>;
    },
    eventType: CommentOp,
    entityType: CommentEntityTypes,
    author: User,
  ) {
    const payload: DeepPartial<SnsReactionPayload> = {
      eventId: uuidv4(),
      eventType: getCommentEventType(entityType, eventType),
      timestamp: new Date().getTime().toString(),
      orgId: author.organization.uid,
      actor: {
        id: author.uid,
        type: author.userType,
        email: author.email,
      },
      payload: {
        reaction: {
          name: reaction.emoji.name,
          author: {
            id: author.uid,
            name: author.name,
            avatarUrl: author.avatarUrl,
            email: author.email,
          },
          metadata: reaction.metadata,
        },
        comment: {
          id: reaction.comment.uid,
        },
      },
    };

    return payload;
  }
}
