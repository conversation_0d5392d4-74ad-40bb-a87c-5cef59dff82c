import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { ContextUserType, SNSPublisherService } from "@repo/thena-eventbridge";
import {
  Comment,
  CommentEntityTypes,
  CommentRepository,
} from "@repo/thena-platform-entities";
import { Job } from "bullmq";
import * as rTracer from "cls-rtracer";
import { DeepPartial, FindOptionsRelations } from "typeorm";
import { v4 as uuidv4 } from "uuid";
import { CurrentUser } from "../../common/decorators";
import { ConfigKeys, ConfigService } from "../../config/config.service";
import { QueueNames } from "../../constants/queue.constants";
import { COMMENT_SNS_PUBLISHER } from "../constants/comments.constants";
import { SnsCommentCreatedPayload } from "../interfaces/sns-comment-created-payload.interface";
import { CommentOp, getCommentEventType } from "../utils";

@Injectable()
@Processor(QueueNames.COMMENT_SNS_PUBLISHER)
export class CommentSnsConsumer extends WorkerHost {
  constructor(
    @Inject(COMMENT_SNS_PUBLISHER)
    private snsPublisherService: SNSPublisherService,

    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly configService: ConfigService,

    private readonly commentRepository: CommentRepository,
  ) {
    super();
  }

  // TODO: @SUDHEER FIX THIS- this is a temporary fix to convert the storage url to a public url
  private convertToPublicUrl(storage: any) {
    return `${this.configService.get(
      ConfigKeys.BASE_URL,
    )}/storage/public/signed-url/document/${storage.accessTokenHash}`;
  }

  private async publishToSNS(snsPayload: SnsCommentCreatedPayload) {
    await this.snsPublisherService.publishSNSMessage({
      subject: snsPayload.eventType,
      message: JSON.stringify(snsPayload),
      topicArn: this.configService.get(ConfigKeys.AWS_SNS_TICKET_TOPIC_ARN),
      messageAttributes: {
        event_name: snsPayload.eventType,
        event_id: snsPayload.eventId, // Unique identifier for the event
        event_timestamp: snsPayload.timestamp,
        context_user_id: snsPayload.actor.id,
        context_user_type: snsPayload.actor.type as unknown as ContextUserType,
        context_organization_id: snsPayload.orgId,
      },
      messageGroupId: snsPayload.orgId,
    });
  }

  /**
   * Process the SNS message for the comment created event
   * @param job The job object
   */
  async process(
    job: Job<{
      comment: string;
      user: CurrentUser;
      eventType: CommentOp;
      entityType: CommentEntityTypes;
      previousComment?: Comment;
      reqId: unknown;
      shouldSendEmail?: boolean;
      createdWithTicket?: boolean;
    }>,
  ) {
    try {
      const {
        comment,
        user,
        eventType,
        entityType,
        previousComment,
        reqId,
        shouldSendEmail,
        createdWithTicket,
      } = job.data;

      await rTracer.runWithId(async () => {
        this.logger.log(
          `Processing SNS message for job ${job.id}, event type: ${job.data.eventType}`,
        );

        // For delete events, use the previousComment data
        const commentData =
          eventType === CommentOp.DELETED
            ? previousComment
            : await this.constructForEntityType({ comment, user, eventType });

        // If no comment data found for non-delete events, throw error
        if (!commentData && eventType !== CommentOp.DELETED) {
          const error = new Error(`Comment not found with id ${comment}`);
          error.name = "CommentNotFoundError";
          throw error;
        }

        // Construct the SNS payload
        const snsPayload = this.constructSnsPayload(
          commentData,
          eventType,
          user,
          entityType,
          previousComment,
          shouldSendEmail,
          createdWithTicket,
        );

        // Add timeout to SNS publish operation
        await Promise.race([
          this.publishToSNS(snsPayload as SnsCommentCreatedPayload),
          new Promise((_, reject) =>
            setTimeout(() => {
              const error = new Error("SNS publish timeout");
              error.name = "SNSTimeoutError";
              reject(error);
            }, 5000),
          ),
        ]);

        return {
          success: true,
          processedAt: new Date(),
          commentId: comment,
        };
      }, reqId);
    } catch (error) {
      this.logger.error(
        `Error processing SNS message for job ${job.id}: ${error.message}`,
        error?.stack,
      );

      // Determine if we should retry based on error type
      if (job.attemptsMade < job.opts.attempts - 1) {
        const retryError = new Error(
          `SNS publishing failed (attempt ${job.attemptsMade + 1}): ${
            error.message
          }`,
        );
        retryError.name = "RetryableError";
        throw retryError;
      }

      // On final attempt, mark as permanent failure
      throw error;
    }
  }

  /**
   * Construct the SNS payload for the given entity type
   * @template T The type of the data object
   * @param data The data object
   * @param entityType The entity type
   * @returns The SNS payload
   */
  private async constructForEntityType(data: any) {
    const { comment, user, eventType } = data;
    const relations: FindOptionsRelations<Comment> = {
      account: true,
      accountTask: true,
      accountActivity: true,
      accountNote: true,
      author: true,
      attachments: true,
      ticket: true,
      team: true,
      parentComment: true,
      customerContact: true,
    };

    // Get the comment data
    const commentData = await this.commentRepository.findByCondition({
      where: { uid: comment, organizationId: user.orgId },
      relations,
    });

    // If the comment is not found and the event type is not a delete event, throw an error
    if (!commentData && eventType !== CommentOp.DELETED) {
      const error = new Error(`Comment not found with id ${comment}`);
      error.name = "CommentNotFoundError";
      throw error;
    }

    return commentData;
  }

  private constructSnsPayload(
    comment: Comment,
    eventType: CommentOp,
    user: CurrentUser,
    entityType: CommentEntityTypes,
    previousComment?: Comment,
    shouldSendEmail?: boolean,
    createdWithTicket?: boolean,
  ) {
    this.logger.log(`Processing comment with entity type: ${entityType}`);
    const payload: DeepPartial<SnsCommentCreatedPayload> = {
      eventId: uuidv4(),
      eventType: getCommentEventType(entityType, eventType),
      timestamp: new Date().getTime().toString(),
      orgId: user.orgUid,
      actor: {
        id: user.uid,
        type: user.userType,
        email: user.email,
      },
      payload: {
        comment:
          eventType === CommentOp.UPDATED
            ? {
                // For update events, include both previous and updated comment
                previous: {
                  id: previousComment.uid,
                  content: previousComment.content,
                  contentMarkdown: previousComment.contentMarkdown,
                  parentCommentId: previousComment.parentComment?.uid,
                  contentHtml: previousComment.contentHtml,
                  contentJson: previousComment.contentJson,
                  metadata: previousComment.metadata as unknown as Record<
                    string,
                    unknown
                  >,
                  commentType: previousComment.commentType,
                  author: {
                    id: previousComment.author.uid,
                    name: previousComment.author.name,
                    email: previousComment.author.email,
                    avatarUrl: previousComment.author.avatarUrl,
                  },
                  customerContact: {
                    id: previousComment.customerContact?.uid,
                    email: previousComment.customerContact?.email,
                    avatarUrl: previousComment.customerContact?.avatarUrl,
                  },
                  attachments: previousComment.attachments.map(
                    (attachment) => ({
                      id: attachment.uid,
                      name: attachment.originalName,
                      url: this.convertToPublicUrl(attachment),
                      size: attachment.size,
                      createdAt: attachment.createdAt,
                      updatedAt: attachment.updatedAt,
                      deletedAt: attachment.deletedAt,
                    }),
                  ),
                  commentVisibility: previousComment.commentVisibility,
                  createdAt: previousComment.createdAt,
                  updatedAt: previousComment.updatedAt,
                  deletedAt: previousComment.deletedAt,
                },
                updated: {
                  id: comment.uid,
                  content: comment.content,
                  parentCommentId: comment.parentComment?.uid,
                  contentMarkdown: comment.contentMarkdown,
                  contentHtml: comment.contentHtml,
                  contentJson: comment.contentJson,
                  metadata: comment.metadata as unknown as Record<
                    string,
                    unknown
                  >,
                  commentType: comment.commentType,
                  author: {
                    id: comment?.author?.uid,
                    name: comment?.author?.name,
                    email: comment?.author?.email,
                    avatarUrl: comment?.author?.avatarUrl,
                  },
                  customerContact: {
                    id: comment?.customerContact?.uid,
                    email: comment?.customerContact?.email,
                    avatarUrl: comment?.customerContact?.avatarUrl,
                  },
                  attachments: comment.attachments?.map((attachment) => ({
                    id: attachment.uid,
                    name: attachment.originalName,
                    url: this.convertToPublicUrl(attachment),
                    size: attachment.size,
                    contentType: attachment.contentType,
                    createdAt: attachment.createdAt,
                    updatedAt: attachment.updatedAt,
                    deletedAt: attachment.deletedAt,
                  })),
                  commentVisibility: comment.commentVisibility,
                  createdAt: comment.createdAt,
                  updatedAt: comment.updatedAt,
                  deletedAt: comment.deletedAt,
                },
              }
            : {
                // For non-update events, keep the existing structure
                id: comment.uid,
                content: comment.content,
                contentMarkdown: comment.contentMarkdown,
                contentHtml: comment.contentHtml,
                contentJson: comment.contentJson,
                commentType: comment.commentType,
                parentCommentId: comment.parentComment?.uid,
                metadata: comment.metadata as unknown as Record<
                  string,
                  unknown
                >,
                author: {
                  id: comment?.author?.uid,
                  name: comment?.author?.name,
                  email: comment?.author?.email,
                  avatarUrl: comment?.author?.avatarUrl,
                },
                customerContact: {
                  id: comment.customerContact?.uid,
                  email: comment.customerContact?.email,
                  avatarUrl: comment.customerContact?.avatarUrl,
                },
                attachments: comment.attachments?.map((attachment) => ({
                  id: attachment.uid,
                  name: attachment.originalName,
                  url: this.convertToPublicUrl(attachment),
                  size: attachment.size,
                  contentType: attachment.contentType,
                  createdAt: attachment.createdAt,
                  updatedAt: attachment.updatedAt,
                  deletedAt: attachment.deletedAt,
                })),
                commentVisibility: comment.commentVisibility,
                createdAt: comment.createdAt,
                updatedAt: comment.updatedAt,
                deletedAt: comment.deletedAt,
                shouldSendEmail,
                createdWithTicket: createdWithTicket ?? false,
              },
      },
    };

    // Add the entity type to the payload
    if (entityType === CommentEntityTypes.TICKET) {
      if (eventType === CommentOp.UPDATED) {
        payload.payload.comment.previous.teamId = comment.team.uid;
        payload.payload.comment.updated.teamId = comment.team.uid;
      } else {
        payload.payload.comment.teamId = comment.team.uid;
      }
      payload.payload.ticket = {
        id: comment.ticket.uid,
        title: comment.ticket.title,
        source: comment.ticket.source,
      };
    }

    // Add the account task to the payload
    if (entityType === CommentEntityTypes.ACCOUNT_TASK) {
      if (eventType === CommentOp.UPDATED) {
        payload.payload.comment.previous.accountId = comment.account.uid;
        payload.payload.comment.updated.accountId = comment.account.uid;
      } else {
        payload.payload.comment.accountId = comment.account.uid;
      }
      payload.payload.accountTask = {
        id: comment.accountTask.uid,
        title: comment.accountTask.title,
      };
    }

    // Add the account activity to the payload
    if (entityType === CommentEntityTypes.ACCOUNT_ACTIVITY) {
      if (eventType === CommentOp.UPDATED) {
        payload.payload.comment.previous.accountId = comment.account.uid;
        payload.payload.comment.updated.accountId = comment.account.uid;
      } else {
        payload.payload.comment.accountId = comment.account.uid;
      }
      payload.payload.accountActivity = {
        id: comment.accountActivity.uid,
      };
    }

    // Add the account note to the payload
    if (entityType === CommentEntityTypes.ACCOUNT_NOTE) {
      if (eventType === CommentOp.UPDATED) {
        payload.payload.comment.previous.accountId = comment.account.uid;
        payload.payload.comment.updated.accountId = comment.account.uid;
      } else {
        payload.payload.comment.accountId = comment.account.uid;
      }
      payload.payload.accountNote = {
        id: comment.accountNote.uid,
      };
    }

    return payload;
  }
}
