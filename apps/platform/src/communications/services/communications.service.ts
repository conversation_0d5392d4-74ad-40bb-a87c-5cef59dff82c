import { InjectQueue } from "@nestjs/bullmq";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  AccountActivity,
  AccountNote,
  AccountTask,
  AuditLog,
  AuditLogEntityType,
  AuditLogOp,
  AuditLogVisibility,
  Comment,
  CommentEntityTypes,
  CustomerContact,
  Ticket,
  TransactionService,
  UserType,
} from "@repo/thena-platform-entities";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { DeepPartial } from "typeorm";
import { AccountActivityActionService } from "../../accounts/services/account-activity.action.service";
import { AccountNoteActionService } from "../../accounts/services/account-note.action.service";
import { AccountTaskActionService } from "../../accounts/services/account-task.action.service";
import { CustomerContactActionService } from "../../accounts/services/customer-contact.action.service";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators";
import { CreateCommentDto } from "../../common/dto/comments.dto";
import { extractNameFromEmail } from "../../common/utils/extract-email-details";
import { generateRandomPassword } from "../../common/utils/random-password-generator.utils";
import { QueueNames } from "../../constants/queue.constants";
import { OrganizationService } from "../../organization/services/organization.service";
import { StorageService } from "../../storage/services/storage-service";
import { TicketsService } from "../../tickets/services/tickets.service";
import { UsersService } from "../../users/services/users.service";
import {
  GetCommentByUserTypeQuery,
  GetCommentQuery,
} from "../dto/comment.queries";
import { CommentOp } from "../utils";
import { CommentsUtilsService } from "./comments-utils.service";
import { CommentsActionService } from "./comments.action.service";

@Injectable()
export class CommunicationsService {
  constructor(
    @Inject(forwardRef(() => TicketsService))
    private readonly ticketService: TicketsService,

    private readonly accountActivityActionService: AccountActivityActionService,
    private readonly accountNoteActionService: AccountNoteActionService,
    private readonly accountTaskActionService: AccountTaskActionService,
    private readonly customerContactActionService: CustomerContactActionService,

    @InjectQueue(QueueNames.COMMENT_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,

    private readonly commentsActionService: CommentsActionService,
    private readonly transactionService: TransactionService,
    private readonly activitiesService: ActivitiesService,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly storageService: StorageService,
    private readonly usersService: UsersService,
    private readonly organizationService: OrganizationService,

    private readonly commentsUtilsService: CommentsUtilsService,
  ) {}

  async getEntity(
    currentUser: CurrentUser,
    entityType: CommentEntityTypes,
    entityId: string,
  ) {
    this.logger.log(`Fetching ${entityType} entity with ID ${entityId}`);

    switch (entityType) {
      case CommentEntityTypes.TICKET: {
        return await this.ticketService.getTicketById(
          entityId,
          currentUser.orgId,
        );
      }
      case CommentEntityTypes.ACCOUNT_NOTE: {
        return await this.accountNoteActionService.findAccountNote(
          entityId,
          currentUser.sub,
        );
      }
      case CommentEntityTypes.ACCOUNT_TASK: {
        return await this.accountTaskActionService.findAccountTask(entityId);
      }
      case CommentEntityTypes.ACCOUNT_ACTIVITY: {
        return await this.accountActivityActionService.findAccountActivity(
          entityId,
        );
      }
    }
  }

  /**
   * Helper function to get the properties of an entity
   *
   * @param entity The entity
   * @returns The properties of the entity
   */
  private getEntityProperties<T extends { id: string; uid: string }>(
    entity: T,
    entityType: CommentEntityTypes,
  ) {
    this.logger.log(
      `Getting properties for ${entityType} entity ${entity.uid}`,
    );

    switch (entityType) {
      case CommentEntityTypes.TICKET: {
        const ticket = entity as unknown as Ticket;
        return {
          ticketId: ticket.id,
          teamId: ticket.team.id,
        };
      }
      case CommentEntityTypes.ACCOUNT_ACTIVITY: {
        const accountActivity = entity as unknown as AccountActivity;
        return {
          accountActivityId: accountActivity.id,
          accountId: accountActivity.accountId,
        };
      }
      case CommentEntityTypes.ACCOUNT_NOTE: {
        const accountNote = entity as unknown as AccountNote;
        return {
          accountNoteId: accountNote.id,
          accountId: accountNote.accountId,
        };
      }
      case CommentEntityTypes.ACCOUNT_TASK: {
        const accountTask = entity as unknown as AccountTask;
        return {
          accountTaskId: accountTask.id,
          accountId: accountTask.accountId,
        };
      }
    }
  }

  /**
   * Helper function to check if a parent comment is valid
   *
   * @param parentComment The parent comment
   * @param entity The entity
   * @returns Whether the parent comment is valid
   */
  private isValidParentComment<T extends { id: string; uid: string }>(
    parentComment: Comment,
    entity: T,
    entityType: CommentEntityTypes,
  ): boolean {
    this.logger.log(
      `Validating parent comment for ${entityType} entity ${entity.uid}`,
    );

    // If the entity is a ticket, check if the parent comment is on the same ticket
    if (entityType === CommentEntityTypes.TICKET) {
      const isSameTicketId =
        Number(parentComment.ticketId) ===
        Number((entity as unknown as Ticket).id);
      const isSameParentTeam =
        parentComment.teamId === (entity as unknown as Ticket).team.id;
      return isSameParentTeam && isSameTicketId;
    }

    const entityProperties = this.getEntityProperties(entity, entityType);
    const isValid = Object.entries(entityProperties).every(
      ([key, value]) => parentComment?.[key] && parentComment[key] === value,
    );

    if (!isValid) {
      this.logger.warn(
        `Invalid parent comment for ${entityType} entity ${entity.uid}`,
      );
    }

    return isValid;
  }

  /**
   * Create a comment on an entity
   * @param entity The ticket or account note
   * @param createCommentDto The comment data
   * @param currentUser The current user
   * @returns The newly created comment
   */
  async createCommentOnAnEntity<T extends { id: string; uid: string }>(
    entity: T,
    entityType: CommentEntityTypes,
    createCommentDto: CreateCommentDto,
    currentUser: CurrentUser,
  ) {
    this.logger.log(
      `Creating comment on ${entityType} entity ${entity.uid} by user ${currentUser.uid}`,
    );

    // Check if the entity exists
    if (!entity) {
      this.logger.warn(`${entityType} entity not found`);
      throw new NotFoundException("Resource not found");
    }
    const orgId = currentUser.orgId;

    const entityProperties = this.getEntityProperties(entity, entityType);

    // Get the parent comment if it exists
    let parentComment: Comment | null;
    if (createCommentDto.parentCommentId) {
      this.logger.log(
        `Fetching parent comment ${createCommentDto.parentCommentId}`,
      );
      const parentCommentId = createCommentDto.parentCommentId;
      parentComment = await this.commentsActionService.getPopulatedCommentById(
        parentCommentId,
        orgId,
      );

      // If the parent comment is not found, throw an error
      if (!parentComment) {
        this.logger.warn(`Parent comment ${parentCommentId} not found`);
        throw new NotFoundException("Parent comment not found!");
      }

      // If the parent comment is deleted, throw an error
      if (parentComment.deletedAt) {
        this.logger.warn(`Parent comment ${parentCommentId} is deleted`);
        throw new BadRequestException(
          "This comment was deleted and therefore can no longer be replied to!",
        );
      }

      // Check if parent comment belongs to the same entity
      const isValidParent = this.isValidParentComment(
        parentComment,
        entity,
        entityType,
      );

      if (!isValidParent) {
        this.logger.warn(
          `Parent comment ${parentCommentId} belongs to different resource`,
        );
        throw new BadRequestException(
          `This comment is not on the same resource as the parent comment!`,
        );
      }

      // If the parent comment already has a parent comment, throw an error
      if (parentComment.parentCommentId) {
        this.logger.warn(`Nested comment attempt on parent ${parentCommentId}`);
        throw new BadRequestException("Nested comments are not supported!");
      }
    }

    // If the customer email is provided, create a customer contact
    let customerContact: CustomerContact | null;
    if (createCommentDto.impersonatedUserEmail) {
      const { firstName, lastName } = extractNameFromEmail(
        createCommentDto.impersonatedUserEmail,
      );

      this.logger.log(
        `Creating customer contact for impersonated user ${createCommentDto.impersonatedUserEmail}`,
      );

      // Get the user type for the impersonated user
      const userType = await this.organizationService.getUserType(
        createCommentDto.impersonatedUserEmail,
        currentUser.orgId,
      );

      // If the user type is customer, create a customer contact
      if (userType === "CUSTOMER") {
        customerContact =
          await this.customerContactActionService.createCustomerContact(
            currentUser,
            {
              email: createCommentDto.impersonatedUserEmail,
              firstName,
              lastName,
            },
            { returnIfExists: true },
          );
      }
    }

    try {
      // Get all the mentioned users
      const mentionedUsers =
        this.commentsUtilsService.getMentionedUsersFromHTML(
          createCommentDto.contentHtml,
        );

      // Create the mentioned users
      await this.commentsUtilsService.createUsers(mentionedUsers, currentUser);
    } catch (error) {
      this.logger.error(
        `Error encountered while creating mentioned users: ${error.message}`,
        error?.stack,
      );
    }

    try {
      // Create the comment
      const newComment = await this.transactionService.runInTransaction(
        async (txnContext) => {
          this.logger.log("Creating new comment record");
          // Create the comment
          let impersonatedUserName = createCommentDto.impersonatedUserName;
          if (!impersonatedUserName && customerContact?.firstName) {
            this.logger.log(`Using customer contact's first_name as fallback for impersonatedUserName: ${customerContact.firstName}`);
            impersonatedUserName = customerContact.firstName;
          }

          const comment = await this.commentsActionService.createComment({
            ...entityProperties,
            teamId: entityProperties.teamId,
            organizationId: orgId,
            source: createCommentDto.metadata?.source,
            content: createCommentDto.content ?? "",
            contentHtml: createCommentDto.contentHtml ?? "",
            commentVisibility: createCommentDto.commentVisibility,
            contentJson: createCommentDto.contentJson ?? "",
            commentType: createCommentDto.commentType,
            authorId: currentUser.sub,
            parentCommentId: parentComment?.id,
            commentThreadName: createCommentDto.threadName,
            metadata: createCommentDto.metadata,
            customerId: customerContact?.id,
            impersonatedUserEmail: createCommentDto.impersonatedUserEmail,
            impersonatedUserName: impersonatedUserName,
            impersonatedUserAvatar: createCommentDto.impersonatedUserAvatar,
          });

          if (createCommentDto.attachmentIds?.length > 0) {
            this.logger.log(
              `Processing ${createCommentDto.attachmentIds.length} attachments`,
            );
            const attachments = await this.storageService.attachFilesToEntity(
              createCommentDto.attachmentIds,
              currentUser.orgId,
            );

            await this.commentsActionService.updateCommentAttachments(
              comment,
              attachments,
            );
          }

          // Create the audit log
          const auditLog: DeepPartial<AuditLog> = {
            organization: { id: currentUser.orgId },
            activityPerformedBy: { id: currentUser.sub },
            entityId: entity.id,
            entityUid: entity.uid,
            entityType: entityType as string as AuditLogEntityType,
            op: AuditLogOp.CREATED,
            visibility: AuditLogVisibility.SYSTEM,
            activity: `A new comment was created on ${entityType} ${entity.id}`,
            description: `A new comment was created by user ${currentUser.email} on ${entityType} ${entity.id}`,
            ...(entityProperties.teamId && {
              team: { id: entityProperties.teamId },
            }),
          };

          this.logger.log("Adding comment to SNS publish queue");
          // Add this to the Bull Queue
          await this.snsPublishQueue.add(
            QueueNames.COMMENT_SNS_PUBLISHER,
            {
              comment: comment.uid,
              user: currentUser,
              eventType: CommentOp.CREATED,
              entityType: entityType,
              reqId: rTracer.id(),
            },
            {
              attempts: 3,
              backoff: { type: "exponential", delay: 1000 },
            },
          );

          // Record the audit log
          await this.activitiesService.recordAuditLog(auditLog, txnContext);

          return comment;
        },
      );

      this.logger.log(
        `Successfully created comment on ${entityType} ${entity.uid}`,
      );
      return newComment;
    } catch (error) {
      this.logger.error(
        `Failed to create comment on ${entityType} ${entity.uid}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get comments for an entity
   * @param entity The ticket or account note
   * @param getCommentsQuery The query for getting comments
   * @param currentUser The current user
   * @returns The comments and the total number of comments
   */
  async getCommentsForAnEntity<T extends { id: string; uid: string }>(
    entity: T,
    entityType: CommentEntityTypes,
    getCommentsQuery: GetCommentQuery,
    currentUser: CurrentUser,
  ) {
    this.logger.log(`Fetching comments for ${entityType} entity ${entity.uid}`);

    // Check if the entity exists
    if (!entity) {
      this.logger.warn(`${entityType} entity not found`);
      throw new NotFoundException("Resource not found");
    }

    const { page, limit } = getCommentsQuery;

    const queryPage = page ?? 0;
    const queryLimit = Math.min(limit ?? 10, 100);

    const entityProperties = this.getEntityProperties(entity, entityType);

    // Get the comments
    const comments = await this.commentsActionService.getComments(
      {
        ...entityProperties,
        page: queryPage,
        limit: queryLimit,
      },
      currentUser,
    );

    this.logger.log(
      `Retrieved ${comments.results.length} comments for ${entityType} ${entity.uid}`,
    );
    return comments;
  }

  /**
   * Get comments for an entity
   * @param entity The ticket or account note
   * @param getCommentsQuery The query for getting comments
   * @param currentUser The current user
   * @returns The comments and the total number of comments
   */
  async getAllCommentsForAnEntity<T extends { id: string; uid: string }>(
    entities: T[],
    entityType: CommentEntityTypes,
    currentUser: CurrentUser,
  ) {
    this.logger.log(`Fetching comments for ${entityType} entities`);

    // Check if the entity exists
    if (!entities) {
      this.logger.warn(`${entityType} entities not found`);
      throw new NotFoundException("Resource not found");
    }

    const entityProperties = entities.map((entity) =>
      this.getEntityProperties(entity, entityType),
    );

    // Get the comments
    const comments = await this.commentsActionService.getAllComments(
      {
        ticketIds: entityProperties
          .map((entityProperty) => entityProperty.ticketId)
          .filter(Boolean),
        accountNoteIds: entityProperties
          .map((entityProperty) => entityProperty.accountNoteId)
          .filter(Boolean),
        accountActivityIds: entityProperties
          .map((entityProperty) => entityProperty.accountActivityId)
          .filter(Boolean),
        accountTaskIds: entityProperties
          .map((entityProperty) => entityProperty.accountTaskId)
          .filter(Boolean),
      },
      currentUser,
    );

    this.logger.log(
      `Retrieved ${comments.results.length} comments for ${entityType} entities`,
    );
    return comments;
  }

  /**
   * Get comments for a ticket
   * @param ticket The ticket
   * @param getCommentsQuery The query for getting comments
   * @param currentUser The current user
   * @returns The comments and the total number of comments
   */
  async getCommentForAnEntityByUserType<T extends { id: string; uid: string }>(
    entity: T,
    entityType: CommentEntityTypes,
    getCommentsQuery: GetCommentByUserTypeQuery,
    currentUser: CurrentUser,
  ) {
    this.logger.log(
      `Fetching comments by user type for ${entityType} entity ${entity.uid}`,
    );

    // Check if the ticket exists
    if (!entity) {
      this.logger.warn(`${entityType} entity not found`);
      throw new NotFoundException("Ticket not found");
    }

    const entityProperties = this.getEntityProperties(entity, entityType);

    const { userType } = getCommentsQuery;

    // Get the comments on the ticket
    const comment = await this.commentsActionService.getCommentByUserType(
      {
        ...entityProperties,
        organizationId: currentUser.orgId,
        userType,
        firstComment: getCommentsQuery.firstComment ?? false,
      } as GetCommentByUserTypeQuery & {
        organizationId: string;
        teamId?: string;
        ticketId?: string;
        accountId?: string;
        accountNoteId?: string;
        accountActivityId?: string;
        accountTaskId?: string;
      },
      currentUser,
    );

    return comment;
  }

  private async getAuthor(
    userType: "USER" | "CUSTOMER",
    email: string,
    currentUser: CurrentUser,
  ) {
    this.logger.log(`Getting author for ${userType} with email ${email}`);

    if (userType === "USER") {
      let user = await this.usersService.findOneByEmail(
        email,
        currentUser.orgId,
      );

      // If the user does not exist, create a new user
      if (!user) {
        this.logger.log(`Creating new user for email ${email}`);
        user = await this.usersService.create(
          {
            email,
            name: email.split("@")?.[0],
            userType: UserType.ORG_ADMIN,
            password: generateRandomPassword(16),
            organizationUid: currentUser.orgUid,
          },
          { user_id: currentUser.uid, org_id: currentUser.orgUid },
        );
      }

      return {
        ...currentUser,
        sub: user.id,
        uid: user.uid,
        email: user.email,
        userType: user.userType,
        timezone: user.timezone,
      };
    }
  }
}
