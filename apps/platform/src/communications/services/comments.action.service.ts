import { InjectQueue } from "@nestjs/bullmq";
import {
    BadRequestException,
    Injectable,
    NotFoundException,
    UnauthorizedException,
} from "@nestjs/common";
import {
    AuditLog,
    AuditLogEntityType,
    AuditLogOp,
    AuditLogVisibility,
    Comment,
    CommentEntityTypes,
    CommentProcessorService,
    CommentRepository,
    CommentType,
    CommentVisibility,
    CustomerContact,
    MentionExtractor,
    Reactions,
    ReactionsRepository,
    Storage,
    TransactionContext,
    TransactionService,
    User,
    UserType,
} from "@repo/thena-platform-entities";
import { Queue } from "bullmq";
import * as rTracer from "cls-rtracer";
import { cloneDeep, isArray, mergeWith } from "lodash";
import { DeepPartial, In, IsNull, Not } from "typeorm";
import { CustomerContactActionService } from "../../accounts/services/customer-contact.action.service";
import { ActivitiesService } from "../../activities/services/activities.service";
import { CurrentUser } from "../../common/decorators";
import { QueueNames } from "../../constants/queue.constants";
import { StorageService } from "../../storage/services/storage-service";
import { UsersService } from "../../users/services/users.service";
import { EAGERLY_LOAD_COMMENTS_RELATIONS } from "../constants/comments.constants";
import {
    CreateComment,
    GetCommentByUserTypeQuery,
    GetCommentQuery,
    GetCommentThreadsQuery,
} from "../dto/comment.queries";
import { UpdateCommentDto } from "../dto/comments.dto";
import { CommentOp } from "../utils";

@Injectable()
export class CommentsActionService {
  constructor(
    private readonly transactionService: TransactionService,
    private readonly commentProcessorService: CommentProcessorService,
    private readonly activitiesService: ActivitiesService,
    private readonly usersService: UsersService,
    private readonly customerContactActionService: CustomerContactActionService,
    private readonly storageService: StorageService,

    // Repositories
    private readonly reactionsRepository: ReactionsRepository,
    private readonly commentRepository: CommentRepository,

    @InjectQueue(QueueNames.COMMENT_SNS_PUBLISHER)
    private readonly snsPublishQueue: Queue,
  ) { }

  /**
   * Get a comment by its unique identifier and eagerly load the relations
   * @param commentId - The unique identifier of the comment
   * @param orgId - The unique identifier of the organization
   * @returns The comment with the relations eagerly loaded
   */
  async getPopulatedCommentById(commentId: string, orgId: string) {
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: orgId },
      relations: EAGERLY_LOAD_COMMENTS_RELATIONS,
    });

    return comment;
  }

  /**
   * Get the allowed visibilities for a comment based on the user type
   * @param currentUser The currently logged in user
   * @param teamId The unique identifier of the team
   * @param organizationId The unique identifier of the organization
   * @returns The allowed visibilities
   */
  private getAllowedVisibilities(
    currentUser: CurrentUser,
    _organizationId: string,
    _teamId: string,
    accountId: string,
  ) {
    if (accountId) {
      // Account notes are always internal comments
      return [CommentVisibility.PRIVATE];
    }

    // If the user is a customer user, only get public comments
    switch (currentUser.userType) {
      // Customer users can only see public comments
      case UserType.CUSTOMER_USER: {
        return [CommentVisibility.PUBLIC];
      }

      // Internal users can see all comments
      case UserType.ORG_ADMIN:
      case UserType.BOT_USER:
      case UserType.USER: {
        // If the user is not in the team, they can only see public and internal comments
        return [CommentVisibility.PUBLIC];
      }

      default: {
        throw new UnauthorizedException("Invalid user type!");
      }
    }
  }

  /**
   * Create a new comment
   * @param comment The comment data
   * @returns The newly created comment
   */
  async createComment(comment: CreateComment, txnContext?: TransactionContext) {
    const newComment = await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Extract the user and customer IDs from the comment content
        const { userIds } = MentionExtractor.extractMentions(
          comment.contentHtml ?? comment.content,
        );

        const { plainText, html, markdown } =
          this.commentProcessorService.processComment(comment.content);

        const commentData: DeepPartial<Comment> = {
          organizationId: comment.organizationId,
          content: plainText,
          contentHtml: comment.contentHtml ?? html,
          contentJson: comment.contentJson ?? null,
          contentMarkdown: markdown,
          commentVisibility:
            comment.commentVisibility ?? CommentVisibility.PUBLIC,
          commentType: comment.commentType ?? CommentType.COMMENT,
          parentCommentId: comment.parentCommentId,
          metadata: {
            mentions: userIds,
            source: comment.source,
            ...comment.metadata,
          },
          authorId: comment.authorId,
          ticketId: comment.ticketId ?? null,
          teamId: comment.teamId ?? null,
          accountNoteId: comment.accountNoteId ?? null,
          accountActivityId: comment.accountActivityId ?? null,
          accountTaskId: comment.accountTaskId ?? null,
          accountId: comment.accountId ?? null,
          commentThreadName: comment.commentThreadName ?? null,
          impersonatedUserEmail: comment.impersonatedUserEmail ?? null,
          impersonatedUserName: comment.impersonatedUserName ?? null,
          impersonatedUserAvatar: comment.impersonatedUserAvatar ?? null,
        };

        // If the comment is associated with a customer contact, set the customer contact
        if (comment.customerId) {
          commentData.customerContact = { id: comment.customerId };
        }

        // Create the comment
        const createdComment = await this.commentRepository.createComment(
          commentData,
          txnContext,
        );

        // Create audit logs for comment
        const auditLog: DeepPartial<AuditLog> = {
          ...(createdComment.teamId && { team: { id: createdComment.teamId } }),
          organization: { id: createdComment.organizationId },
          activityPerformedBy: { id: createdComment.authorId },
          entityId: createdComment.id,
          entityUid: createdComment.uid,
          entityType: AuditLogEntityType.COMMENTS,
          op: AuditLogOp.CREATED,
          visibility: AuditLogVisibility.SYSTEM,
          activity: `A new comment ${createdComment.id} was created`,
          description: `A new comment ${createdComment.id} was created by ${createdComment.authorId} `,
        };

        // Record the audit log
        await this.activitiesService.recordAuditLog(auditLog, txnContext);

        // Update the parent comment's metadata
        if (comment.parentCommentId) {
          const parentId = comment.parentCommentId;
          const orgId = comment.organizationId;
          const parentCommentCriteria = { id: parentId, organizationId: orgId };

          // Update the parent comment's metadata
          await this.commentRepository.updateWithTxn(
            txnContext,
            parentCommentCriteria,
            {
              metadata: () => `
              jsonb_set(
                COALESCE(metadata, '{"replies": []}'::jsonb),
                '{replies}',
                COALESCE(metadata->'replies', '[]'::jsonb) || '["${createdComment.uid}"]'::jsonb
              )`,
            },
          );
        }

        return createdComment;
      },
      txnContext,
    );

    // Fetch the comment with populated relations
    const populatedComment = await this.getPopulatedCommentById(
      newComment.uid,
      comment.organizationId,
    );

    return populatedComment;
  }

  /**
   * Update a comment
   * @param commentId The unique identifier of the comment
   * @param updateCommentDto The update comment data
   * @returns The updated comment
   */
  async updateComment(
    commentId: string,
    updateCommentDto: UpdateCommentDto,
    currentUser: CurrentUser,
  ): Promise<Comment> {
    // Check if the comment exists
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
      relations: [
        "team",
        "author",
        "ticket",
        "attachments",
        "accountNote",
        "accountActivity",
        "accountTask",
        "customerContact",
      ],
    });

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException("Comment not found!");
    if (comment.deletedAt)
      throw new BadRequestException(
        "This comment was deleted and therefore can no longer be edited/updated!",
      );

    const previousComment = cloneDeep(comment);

    // Check if the current user is a bot user
    const isBotUser = currentUser.userType === UserType.BOT_USER;
    const isAuthorSetAs = updateCommentDto.commentAs === comment.author?.email;
    const isCustomerContactSetAs =
      updateCommentDto.commentAs === comment.customerContact?.email;
    const canBotUserUpdate =
      isBotUser && (isAuthorSetAs || isCustomerContactSetAs);

    // Is user's comment comment
    const isUserComment = comment.authorId === currentUser.sub;

    // If the current user is not the author of the comment, throw an error
    if (!isUserComment && !canBotUserUpdate) {
      throw new UnauthorizedException(
        "You are not authorized to update this comment!",
      );
    }

    // Run the update transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Process the updated content
      const { content, metadata: newMetadata } = updateCommentDto;

      // Update criteria
      const updateCriteria = {
        id: comment.id,
        organizationId: comment.organizationId,
      };

      // If the content is being updated, update the comment
      if (content) {
        // Extract the user IDs from the updated content
        // Always use contentHtml for extraction if available, as it preserves the mention structure
        const { userIds } = MentionExtractor.extractMentions(
          updateCommentDto.contentHtml || content
        );
        const refArray = userIds?.map((id) => `"${id}"`).join(",");

        // Process the content, but don't override the contentHtml
        const { plainText, html, markdown } =
          this.commentProcessorService.processComment(content);

        // Update the comment
        await this.commentRepository.updateWithTxn(txnContext, updateCriteria, {
          content: plainText,
          contentJson: updateCommentDto.contentJson ?? null,
          // Always use the provided contentHtml if available, don't override with processed HTML
          contentHtml: updateCommentDto.contentHtml ?? html,
          contentMarkdown: markdown,
          isEdited: true,
          commentThreadName: updateCommentDto.threadName ?? null,
          metadata: () => `
          jsonb_set(
            jsonb_set(
              COALESCE(metadata, '{}'::jsonb) ${newMetadata ? `|| '${JSON.stringify(newMetadata)}'::jsonb` : ""
            },
              '{mentions}',
              CASE 
                WHEN '${refArray}' = '' THEN '[]'::jsonb
                ELSE '[${refArray}]'::jsonb
              END
            ),
            '{lastEditedAt}',
            '"${new Date().toISOString()}"'::jsonb
          )
      `,
        });
      } else if (newMetadata) {
        await this.commentRepository
          .createQueryBuilder()
          .update(Comment)
          .set({
            isEdited: true,
            commentThreadName: updateCommentDto.threadName ?? null,
            metadata: () => `
            jsonb_set(
              COALESCE(metadata, '{}'::jsonb) || :metadata::jsonb,
              '{lastEditedAt}',
              :lastEditedAt::jsonb
            )
          `,
          })
          .where(updateCriteria)
          .setParameters({
            metadata: JSON.stringify(newMetadata),
            lastEditedAt: JSON.stringify(new Date().toISOString()),
          })
          .execute();
        // await this.commentRepository.updateWithTxn(txnContext, updateCriteria, {
        //   isEdited: true,
        //   commentThreadName: updateCommentDto.threadName ?? null,
        //   metadata: () => `
        //   jsonb_set(
        //     COALESCE(metadata, '{}'::jsonb) || $1::jsonb,
        //     '{lastEditedAt}',
        //     '"${new Date().toISOString()}"'::jsonb
        //   )
        // `,
        // });
      }

      if (updateCommentDto.attachments !== undefined) {
        const attachments = updateCommentDto.attachments.length === 0
          ? []
          : await this.storageService.attachFilesToEntity(
            updateCommentDto.attachments,
            comment.organizationId,
          );

        await txnContext.manager.getRepository(Comment)
          .createQueryBuilder()
          .relation("attachments")
          .of(comment)
          .addAndRemove(attachments, comment.attachments || []);
      }

      await this.commentRepository.updateWithTxn(txnContext, updateCriteria, {
        isEdited: true,
        ...(updateCommentDto.threadName !== undefined && {
          commentThreadName: updateCommentDto.threadName,
        }),
        ...(updateCommentDto.commentVisibility !== undefined && {
          commentVisibility: updateCommentDto.commentVisibility,
        }),
        ...(updateCommentDto.commentType !== undefined && {
          commentType: updateCommentDto.commentType,
        }),
        ...(updateCommentDto.isPinned !== undefined && {
          isPinned: updateCommentDto.isPinned,
        }),
      });
    });

    // Emit comment updated event
    await this.snsPublishQueue.add(
      QueueNames.COMMENT_SNS_PUBLISHER,
      {
        comment: comment.uid,
        user: currentUser,
        eventType: CommentOp.UPDATED,
        entityType: CommentEntityTypes.TICKET,
        previousComment,
        reqId: rTracer.id(),
      },
      {
        attempts: 3,
        backoff: { type: "exponential", delay: 1000 },
      },
    );

    // Fetch the updated comment with populated relations
    const populatedComment = await this.getPopulatedCommentById(
      comment.uid,
      currentUser.orgId,
    );

    return populatedComment;
  }

  /**
   * Deletes a comment.
   * @param commentId The unique identifier of the comment
   * @param currentUser The currently logged in user
   */
  async deleteComment(
    commentId: string,
    currentUser: CurrentUser,
    metadata?: Record<string, any>,
  ) {
    // Check if the comment exists with full relations
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
      relations: [
        "team",
        "author",
        "ticket",
        "attachments",
        "accountNote",
        "accountActivity",
        "accountTask",
        "parentComment",
        "customerContact",
      ],
    });

    const fullCommentData = cloneDeep(comment);

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException("Comment not found!");

    // If the current user is not the author of the comment, throw an error
    if (comment.authorId !== currentUser.sub) {
      throw new UnauthorizedException(
        "You are not authorized to delete this comment!",
      );
    }

    // Run the delete transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      if (metadata) {
        const updatedMetadata = mergeWith(
          fullCommentData.metadata,
          metadata,
          (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          },
        );

        await this.commentRepository.updateWithTxn(
          txnContext,
          {
            id: comment.id,
            organizationId: currentUser.orgId,
          },
          {
            metadata: updatedMetadata,
          },
        );
      }

      // Delete the comment
      await this.commentRepository.softDeleteWithTxn(txnContext, {
        id: comment.id,
        uid: comment.uid,
        organizationId: currentUser.orgId,
      });

      fullCommentData.deletedAt = new Date();

      // Add to SNS publish queue
      await this.snsPublishQueue.add(
        QueueNames.COMMENT_SNS_PUBLISHER,
        {
          comment: comment.uid,
          user: currentUser,
          eventType: CommentOp.DELETED,
          entityType: CommentEntityTypes.TICKET,
          previousComment: fullCommentData,
          reqId: rTracer.id(),
        },
        {
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 1000,
          },
        },
      );
    });
  }

  /**
   * Get comments for a ticket
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The comments and the total number of comments
   */
  async getComments(
    getCommentQuery: GetCommentQuery & {
      teamId?: string;
      ticketId?: string;
      accountId?: string;
      accountNoteId?: string;
      accountActivityId?: string;
      accountTaskId?: string;
    },
    currentUser: CurrentUser,
  ) {
    const {
      page,
      limit,
      teamId,
      ticketId,
      accountId,
      accountNoteId,
      accountActivityId,
      accountTaskId,
    } = getCommentQuery;

    // Get the allowed visibilities
    const allowedVisibilities = this.getAllowedVisibilities(
      currentUser,
      currentUser.orgId,
      teamId,
      accountId,
    );

    const comments = await this.commentRepository.fetchPaginatedResults(
      { page, limit },
      {
        where: [
          // Get all the comments including private and public comments for the user
          // in the team
          {
            ...(ticketId && { ticketId }),
            ...(teamId && { teamId }),
            ...(accountId && { accountId }),
            ...(accountNoteId && { accountNoteId }),
            ...(accountActivityId && { accountActivityId }),
            ...(accountTaskId && { accountTaskId }),
            organizationId: currentUser.orgId,
            commentType: CommentType.COMMENT,
            commentVisibility: In([
              ...allowedVisibilities,
              CommentVisibility.PRIVATE,
            ]),
            parentCommentId: IsNull(),
          },

          // Only fetch public notes for the user in the team
          {
            ...(ticketId && { ticketId }),
            ...(teamId && { teamId }),
            ...(accountId && { accountId }),
            ...(accountNoteId && { accountNoteId }),
            ...(accountActivityId && { accountActivityId }),
            ...(accountTaskId && { accountTaskId }),
            organizationId: currentUser.orgId,
            commentType: CommentType.NOTE,
            commentVisibility: CommentVisibility.PUBLIC,
            parentCommentId: IsNull(),
          },

          // Get all private comments for the current user
          {
            ...(ticketId && { ticketId }),
            ...(teamId && { teamId }),
            ...(accountId && { accountId }),
            ...(accountNoteId && { accountNoteId }),
            ...(accountActivityId && { accountActivityId }),
            ...(accountTaskId && { accountTaskId }),
            organizationId: currentUser.orgId,
            commentType: CommentType.NOTE,
            commentVisibility: CommentVisibility.PRIVATE,
            parentCommentId: IsNull(),
            author: { id: currentUser.sub },
          },
        ],
        relations: ["author", "attachments", "customerContact"],
      },
    );

    // Enhance the comments with user reactions
    const enhancedComments = await this.enhanceCommentsWithUserReactions(
      comments.results,
      currentUser,
    );

    return {
      results: enhancedComments,
      total: comments.total,
    };
  }

  /**
   * Get comments for a ticket
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The comments and the total number of comments
   */
  async getAllComments(
    entityProperties: {
      ticketIds?: string[];
      accountNoteIds?: string[];
      accountActivityIds?: string[];
      accountTaskIds?: string[];
    },
    currentUser: CurrentUser,
  ) {
    const { ticketIds, accountNoteIds, accountActivityIds, accountTaskIds } =
      entityProperties;

    const comments = await this.commentRepository.findAll({
      where: [
        // Get all the comments including private and public comments for the user
        {
          ...(ticketIds?.length > 0 && { ticketId: In(ticketIds) }),
          ...(accountNoteIds?.length > 0 && {
            accountNoteId: In(accountNoteIds),
          }),
          ...(accountActivityIds?.length > 0 && {
            accountActivityId: In(accountActivityIds),
          }),
          ...(accountTaskIds?.length > 0 && {
            accountTaskId: In(accountTaskIds),
          }),
          organizationId: currentUser.orgId,
          commentType: CommentType.COMMENT,
        },

        // Only fetch public notes for the user in the team
        {
          ...(ticketIds?.length > 0 && { ticketId: In(ticketIds) }),
          ...(accountNoteIds?.length > 0 && {
            accountNoteId: In(accountNoteIds),
          }),
          ...(accountActivityIds?.length > 0 && {
            accountActivityId: In(accountActivityIds),
          }),
          ...(accountTaskIds?.length > 0 && {
            accountTaskId: In(accountTaskIds),
          }),
          organizationId: currentUser.orgId,
          commentType: CommentType.NOTE,
          commentVisibility: CommentVisibility.PUBLIC,
        },

        // Get all private comments for the current user
        {
          ...(ticketIds?.length > 0 && { ticketId: In(ticketIds) }),
          ...(accountNoteIds?.length > 0 && {
            accountNoteId: In(accountNoteIds),
          }),
          ...(accountActivityIds?.length > 0 && {
            accountActivityId: In(accountActivityIds),
          }),
          ...(accountTaskIds?.length > 0 && {
            accountTaskId: In(accountTaskIds),
          }),
          organizationId: currentUser.orgId,
          commentType: CommentType.NOTE,
          commentVisibility: CommentVisibility.PRIVATE,
          author: { id: currentUser.sub },
        },
      ],
      relations: [
        "team",
        "author",
        "ticket",
        "attachments",
        "accountNote",
        "accountActivity",
        "accountTask",
        "parentComment",
        "customerContact",
      ],
    });

    // Enhance the comments with user reactions
    const enhancedComments = await this.enhanceCommentsWithUserReactions(
      comments,
      currentUser,
    );

    return {
      results: enhancedComments,
    };
  }

  /**
   * Get comments for a ticket
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The comments and the total number of comments
   */
  async getCommentByUserType(
    getCommentByUserTypeQuery: GetCommentByUserTypeQuery & {
      organizationId: string;
      teamId?: string;
      ticketId?: string;
      accountId?: string;
      accountNoteId?: string;
      accountActivityId?: string;
      accountTaskId?: string;
    },
    currentUser: CurrentUser,
  ) {
    const {
      teamId,
      ticketId,
      organizationId,
      userType,
      accountId,
      accountNoteId,
      accountActivityId,
      accountTaskId,
      firstComment,
    } = getCommentByUserTypeQuery;

    // Get the allowed visibilities
    const allowedVisibilities = await this.getAllowedVisibilities(
      currentUser,
      organizationId,
      teamId,
      accountId,
    );
    const comments = await this.commentRepository.findWithRelations({
      where: {
        ...(ticketId && { ticketId }),
        ...(teamId && { teamId }),
        ...(accountId && { accountId }),
        ...(accountNoteId && { accountNoteId }),
        ...(accountActivityId && { accountActivityId }),
        ...(accountTaskId && { accountTaskId }),
        organizationId,
        commentVisibility: In(allowedVisibilities),
        commentType: CommentType.COMMENT,
        ...(userType === "customer" && {
          customerContact: Not(IsNull()),
        }),
        ...(userType === "agent" && {
          customerContact: IsNull(),
        }),
      },
      relations: ["author", "team", "customerContact"],
      order: firstComment ? { id: "ASC" } : { id: "DESC" },
      take: 1,
    });

    // Since we're expecting only one result, return the first item or null
    return comments[0] || null;
  }

  /**
   * Get the threads for a comment
   * @param commentId The unique identifier of the comment
   * @param getCommentQuery The query for getting comments
   * @param currentUser The currently logged in user
   * @returns The threads for the comment
   */
  async getCommentThreads(
    commentId: string,
    getCommentQuery: GetCommentThreadsQuery,
    currentUser: CurrentUser,
  ) {
    const { page, limit } = getCommentQuery;

    // Check if the comment exists
    const comment = await this.commentRepository.findByCondition({
      where: { uid: commentId, organizationId: currentUser.orgId },
    });

    // If the comment is not found, throw an error
    if (!comment) throw new NotFoundException("Comment not found!");

    // Get the allowed visibilities
    const allowedVisibilities = this.getAllowedVisibilities(
      currentUser,
      currentUser.orgId,
      comment.teamId,
      comment.accountId,
    );

    // Get all the visibilities
    const allVisibilities = [...allowedVisibilities, comment.commentVisibility];

    // Get the thread comments
    const threadComments = await this.commentRepository.fetchPaginatedResults(
      { page: page ?? 0, limit: Math.max(limit ?? 10, 100) },
      {
        where: [
          {
            organizationId: currentUser.orgId,
            commentVisibility: In(allVisibilities),
            parentCommentId: comment.id,
          },
          {
            organizationId: currentUser.orgId,
            commentVisibility: CommentVisibility.PRIVATE,
            parentCommentId: comment.id,
            author: { id: currentUser.sub },
          },
        ],
        relations: { attachments: true },
        order: { createdAt: "ASC" },
      },
    );

    // Create a set of unique values for the authors and customer contacts
    const authorSet = new Set<string>();
    const customerContactSet = new Set<string>();

    for (const comment of threadComments.results) {
      authorSet.add(comment.authorId);
      customerContactSet.add(comment.customerContactId);
    }

    const [authors, customerContacts] = await Promise.all([
      this.usersService.queryUsers({ id: In(Array.from(authorSet)) }),
      this.customerContactActionService.queryCustomerContacts({
        id: In(Array.from(customerContactSet)),
      }),
    ]);

    const authorMap = new Map<string, User>();
    const customerContactMap = new Map<string, CustomerContact>();

    for (const author of authors) {
      authorMap.set(author.id, author);
    }

    for (const customerContact of customerContacts) {
      customerContactMap.set(customerContact.id, customerContact);
    }

    // Join the comments with the authors and customer contacts
    const joinedComments = threadComments.results.map((comment) => {
      return {
        ...comment,
        author: authorMap.get(comment.authorId),
        customerContact: customerContactMap.get(comment.customerContactId),
      };
    }) as Array<Comment & { author: User; customerContact: CustomerContact }>;

    // Enhance the thread comments with user reactions
    const enhancedThreadComments = await this.enhanceCommentsWithUserReactions(
      joinedComments,
      currentUser,
    );

    return {
      results: enhancedThreadComments,
      total: threadComments.total,
    };
  }

  async updateCommentAttachments(comment: Comment, attachments: Storage[]) {
    comment.attachments = attachments;
    await this.commentRepository.save(comment);
  }

  /**
   * Enhance comments with user reactions
   * @param comments The comments to enhance
   * @param currentUser The currently logged in user
   * @returns The comments with user reactions
   */
  async enhanceCommentsWithUserReactions(
    comments: Comment[],
    currentUser: CurrentUser,
  ) {
    if (!comments.length) {
      return comments;
    }

    // Get all comment ids
    const commentIds = comments.map((comment) => comment.id);

    // Get reactions for the above comments by the current user
    const userReactions = await this.reactionsRepository.findAll({
      where: {
        commentId: In(commentIds),
        reactionBy: { id: currentUser.sub },
        organization: { id: currentUser.orgId },
      },
      relations: { emoji: true },
    });

    // Map for a faster lookup
    const reactionMap = new Map<string, Reactions[]>();
    userReactions.forEach((reaction) => {
      // Initialize the array for the comment id if it doesn't exist
      if (!reactionMap.has(reaction.commentId)) {
        reactionMap.set(reaction.commentId, []);
      }

      // Add the reaction to the map
      reactionMap.get(reaction.commentId)?.push(reaction);
    });

    return comments.map((comment) => {
      const userReactionsForComment = reactionMap.get(comment.id) || [];

      // Initialize the metadata if it doesn't exist
      if (!comment.metadata) {
        comment.metadata = {
          reactions: {},
          replies: [],
          mentions: [],
          source: comment?.metadata?.source,
          ignoreSelf: false,
          integrationMetadata: [],
          external_metadata: {},
          external_sinks: {},
        };
      }

      // Add the user reactions to the metadata
      comment.metadata.userReactions = userReactionsForComment.map(
        (reaction) => ({
          emojiId: reaction.emojiId,
          emojiName: reaction.emoji.name,
          emojiUnicode: reaction.emoji.unicode,
          emojiUrl: reaction.emoji.url,
        }),
      );

      return comment;
    });
  }
}
