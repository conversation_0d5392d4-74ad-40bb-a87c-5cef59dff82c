interface CommentPayload {
  id: string;
  content: string;
  teamId?: string;
  accountId?: string;
  commentType: string;
  parentCommentId?: string;
  metadata: Record<string, unknown>;
  customerContact?: {
    id: string;
    email: string;
    avatarUrl: string;
  };
  author: {
    id: string;
    name: string;
    email: string;
    avatarUrl: string;
  };
  contentMarkdown: string;
  contentHtml: string;
  attachments: Array<{
    id: string;
    url: string;
    size: number;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
  }>;
  commentVisibility: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
  contentJson: string;
}

export interface SnsCommentCreatedPayload {
  eventId: string;
  eventType: string;
  timestamp: string;
  orgId: string;
  actor: {
    id: string;
    type: string;
    email: string;
  };
  payload: {
    comment: {
      previous?: CommentPayload;
      updated?: CommentPayload;
    } & CommentPayload;
    ticket?: {
      id: string;
      title: string;
    };
    accountTask?: {
      id: string;
      title: string;
    };
    accountActivity?: {
      id: string;
    };
    accountNote?: {
      id: string;
    };
  };
}
