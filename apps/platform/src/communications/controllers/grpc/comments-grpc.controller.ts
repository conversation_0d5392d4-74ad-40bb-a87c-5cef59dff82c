import { <PERSON>ada<PERSON> } from "@grpc/grpc-js";
import { Controller, Inject, UseGuards } from "@nestjs/common";
import { GrpcMethod, RpcException } from "@nestjs/microservices";
import { handleRpcError } from "@repo/nestjs-commons/errors";
import { GrpcAuthGuard } from "@repo/nestjs-commons/guards";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import {
  extractUserMetadata,
  extractWorkflowContextFromMetadata,
} from "@repo/nestjs-commons/utils";
import { communication } from "@repo/shared-proto";
import {
  CommentEntityTypes,
  CommentType,
  CommentVisibility,
} from "@repo/thena-platform-entities";
import { isArray, mergeWith } from "lodash";
import { CreateCommentDto } from "../../../common/dto/comments.dto";
import { CommentsAnnotatorService } from "../../services/comments-annotator.service";
import { CommentsActionService } from "../../services/comments.action.service";
import { CommunicationsService } from "../../services/communications.service";
import { ReactionsActionService } from "../../services/reactions.action.service";
import { CommentResponseDto } from "../../transformers/comment-response.transformer";

@Controller("v1/comments")
@UseGuards(GrpcAuthGuard)
export class CommentsGrpcController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly communicationsService: CommunicationsService,
    private readonly commentsService: CommentsActionService,
    private readonly commentsAnnotatorService: CommentsAnnotatorService,
    private readonly reactionsActionService: ReactionsActionService,
  ) {}

  @GrpcMethod(
    communication.COMMUNICATION_SERVICE_NAME,
    "CreateCommentOnAnEntity",
  )
  async createComment(
    request: communication.CreateCommentRequest,
    metadata: Metadata,
  ): Promise<communication.CommentResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const requestMetadata = JSON.parse(request.metadata || "{}");

      const commentMetadata = mergeWith(
        requestMetadata,
        workflowContext.workflowId
          ? { workflowExecutions: [workflowContext] }
          : {},
        (a, b) => {
          if (isArray(a)) {
            return a.concat(b);
          }
        },
      );

      const entity = await this.communicationsService.getEntity(
        user,
        request.entityType as CommentEntityTypes,
        request.entityId,
      );

      // Form create comment dto
      const createCommentDto: CreateCommentDto = {
        content: request.content,
        contentHtml: request.contentHtml,
        parentCommentId: request.parentCommentId,
        commentVisibility: request.commentVisibility as CommentVisibility,
        commentType: request.commentType as CommentType,
        threadName: request.threadName,
        metadata: commentMetadata,
        attachmentIds: request.attachmentIds,
        customerEmail: request.customerEmail,
        impersonatedUserEmail: request.impersonatedUserEmail,
        impersonatedUserName: request.impersonatedUserName,
        impersonatedUserAvatar: request.impersonatedUserAvatar,
      };

      // Create the comment
      const comment = await this.communicationsService.createCommentOnAnEntity(
        entity,
        request.entityType as CommentEntityTypes,
        createCommentDto,
        user,
      );

      const commentResponse = CommentResponseDto.fromEntity(comment);

      return commentResponse;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "GetCommentsOnAnEntity")
  async getCommentsOnAnEntity(
    request: communication.GetCommentsRequest,
    metadata: Metadata,
  ): Promise<communication.GetCommentsResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const entity = await this.communicationsService.getEntity(
        user,
        request.entityType as CommentEntityTypes,
        request.entityId,
      );

      const comments = await this.communicationsService.getCommentsForAnEntity(
        entity,
        request.entityType as CommentEntityTypes,
        request,
        user,
      );

      const commentsResponses = comments.results.map(
        CommentResponseDto.fromEntity,
      );

      return {
        comments: commentsResponses,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "GetComment")
  async getComment(
    request: communication.GetCommentRequest,
    metadata: Metadata,
  ): Promise<communication.CommentResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const comment = await this.commentsService.getPopulatedCommentById(
        request.commentId,
        user.orgId,
      );

      if (!comment) {
        throw new RpcException("Comment not found");
      }

      const commentResponse = CommentResponseDto.fromEntity(comment);

      return commentResponse;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "GetCommentThreads")
  async getCommentThreads(
    request: communication.GetCommentThreadsRequest,
    metadata: Metadata,
  ): Promise<communication.GetCommentThreadsResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const comments = await this.commentsService.getCommentThreads(
        request.commentId,
        request,
        user,
      );

      const commentsResponses = comments.results.map(
        CommentResponseDto.fromEntity,
      );

      return {
        comments: commentsResponses,
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "UpdateComment")
  async updateComment(
    request: communication.UpdateCommentRequest,
    metadata: Metadata,
  ): Promise<communication.CommentResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      let parsedMetadata: Record<string, any> = {};

      try {
        const requestMetadata = JSON.parse(request.metadata || "{}");

        parsedMetadata = mergeWith(
          requestMetadata || {},
          workflowContext.workflowId
            ? { workflowExecutions: [workflowContext] }
            : {},
          (a, b) => {
            if (isArray(a)) {
              return a.concat(b);
            }
          },
        );
      } catch (error) {
        if (error instanceof Error) {
          this.logger.error(
            `[CommentsGrpcController] Error parsing metadata: ${error.message}`,
            error.stack,
          );
        } else {
          console.error("Failed to parse metadata", error);
        }

        throw new RpcException("Invalid metadata");
      }

      const comment = await this.commentsService.updateComment(
        request.commentId,
        {
          content: request.content,
          threadName: request.threadName,
          attachments: request.attachments,
          metadata: parsedMetadata,
        },
        user,
      );

      const commentResponse = CommentResponseDto.fromEntity(comment);

      return commentResponse;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "DeleteComment")
  async deleteComment(
    request: communication.DeleteCommentRequest,
    metadata: Metadata,
  ): Promise<communication.Empty> {
    try {
      const user = extractUserMetadata(metadata);

      const workflowContext = extractWorkflowContextFromMetadata(metadata);

      const commentMetadata = workflowContext.workflowId
        ? { workflowExecutions: [workflowContext] }
        : {};

      await this.commentsService.deleteComment(
        request.commentId,
        user,
        commentMetadata,
      );

      return {};
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "GetCommentByUserType")
  async getCommentByUserType(
    request: communication.GetCommentByUserTypeRequest,
    metadata: Metadata,
  ): Promise<communication.CommentResponse> {
    try {
      const user = extractUserMetadata(metadata);

      const entity = await this.communicationsService.getEntity(
        user,
        request.entityType as CommentEntityTypes,
        request.entityId,
      );

      const comment =
        await this.communicationsService.getCommentForAnEntityByUserType(
          entity,
          request.entityType as CommentEntityTypes,
          {
            entityType: request.entityType as CommentEntityTypes,
            entityId: request.entityId,
            userType: request.userType as "agent" | "customer" | "all",
            firstComment: request.firstComment ?? false,
          },
          user,
        );

      if (!comment) {
        throw new RpcException("Comment not found");
      }

      const commentResponse = CommentResponseDto.fromEntity(comment);

      return commentResponse;
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(communication.COMMUNICATION_SERVICE_NAME, "GetEmojis")
  async getEmojis(
    _request: communication.Empty,
    metadata: Metadata,
  ): Promise<communication.GetEmojisResponse> {
    try {
      const user = extractUserMetadata(metadata);
      const emojis = await this.reactionsActionService.listAllEmojis(user);

      return {
        emojis: emojis.map((emoji) => ({
          name: emoji.name,
          unicode: emoji.unicode,
          shortcode: emoji.aliases || emoji.name,
          category: "emoji",
          keywords: emoji.keywords || [],
        })),
      };
    } catch (error) {
      handleRpcError(error);
    }
  }

  // Field metadata APIs ========================================================================================
  @GrpcMethod(
    communication.COMMUNICATION_ANNOTATOR_SERVICE_NAME,
    "GetCommentFieldMetadata",
  )
  getCommentFieldMetadata(): communication.GetCommentFieldMetadataResponse {
    try {
      return this.commentsAnnotatorService.getCommentFieldMetadata();
    } catch (error) {
      handleRpcError(error);
    }
  }

  @GrpcMethod(
    communication.COMMUNICATION_ANNOTATOR_SERVICE_NAME,
    "GetCommentData",
  )
  getCommentData(
    request: communication.GetCommentDataRequest,
    metadata: Metadata,
  ): Promise<communication.GetCommentDataResponse> {
    try {
      const user = extractUserMetadata(metadata);

      return this.commentsAnnotatorService.getCommentData(
        request.commentId,
        request.relations,
        user.orgId,
      );
    } catch (error) {
      handleRpcError(error);
    }
  }
}
