import {
  Body,
  Controller,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  NotFoundException,
  Post,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiExcludeEndpoint, ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { SentryService } from "@repo/nestjs-commons/filters";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  CommentType,
  CommentVisibility,
  OrganizationTier,
} from "@repo/thena-platform-entities";
import { RequireThenaAppImpersonation } from "../../auth/decorators";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { CurrentUser } from "../../common/decorators/user.decorator";
import { CreateCommentDto } from "../../common/dto/comments.dto";
import {
  GetAllCommentsForAnEntityQuery,
  GetCommentByUserTypeQuery,
  GetCommentsForAnEntityQuery,
} from "../dto/comment.queries";
import { CreateCommentOnAnEntityDto } from "../dto/comments.dto";
import { GetAllCommentsResponse } from "../dto/response/comment-response.dto";
import { CommunicationsService } from "../services/communications.service";
import {
  BulkCommentResponseDto,
  CommentResponseDto,
} from "../transformers/comment-response.transformer";

@ApiTags("Comments")
@Controller("v1/comments")
@SkipAllThrottler()
@UseInterceptors(ResponseTransformInterceptor)
export class CommunicationsController {
  private readonly logSpanId = "[CommunicationsController]";

  constructor(
    private readonly communicationsService: CommunicationsService,

    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  @Post("")
  @RequireThenaAppImpersonation()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comment created successfully!")
  @ApiCreateEndpoint({
    summary: "Comment on an entity",
    responseType: CommentResponseDto,
  })
  async commentOnAnEntity(
    @CurrentUser() currentUser: CurrentUser,
    @Body() body: CreateCommentOnAnEntityDto,
  ): Promise<CommentResponseDto> {
    try {
      // Log user context for impersonation verification
      this.logger.log(
        `Creating comment - User context: ${JSON.stringify({
          uid: currentUser.uid,
          email: currentUser.email,
          userType: currentUser.userType,
          isImpersonated: !!(currentUser as any)._impersonation,
          impersonationData: (currentUser as any)._impersonation || null,
        })}`,
        "CommunicationsController",
      );

      // Get the entity
      const entity = await this.communicationsService.getEntity(
        currentUser,
        body.entityType,
        body.entityId,
      );

      // Form create comment dto
      const createCommentDto: CreateCommentDto = {
        content: body.content ?? "",
        contentHtml: body.contentHtml ?? "",
        contentJson: body.contentJson ?? "",
        parentCommentId: body.parentCommentId,
        commentVisibility: body.commentVisibility ?? CommentVisibility.PRIVATE,
        commentType: body.commentType ?? CommentType.NOTE,
        threadName: body.threadName,
        metadata: body.metadata,
        attachmentIds: body.attachmentIds,
        customerEmail: body.customerEmail,
        impersonatedUserEmail: body?.impersonatedUserEmail,
        impersonatedUserName: body?.impersonatedUserName,
        impersonatedUserAvatar: body?.impersonatedUserAvatar,
        shouldSendEmail: true, // True by default through HTTP
      };

      // Create the comment
      const comment = await this.communicationsService.createCommentOnAnEntity(
        entity,
        body.entityType,
        createCommentDto,
        currentUser,
      );

      // Log the created comment details for verification
      this.logger.log(
        `Comment created successfully - Details: ${JSON.stringify({
          commentId: comment.uid,
          authorId: comment.authorId, // This is the user who created the comment
          entityType: body.entityType,
          entityId: body.entityId,
          content: comment.content?.substring(0, 50) + "...", // First 50 chars
          wasImpersonated: !!(currentUser as any)._impersonation,
          originalBotUser:
            (currentUser as any)._impersonation?.originalBotUserUid || null,
        })}`,
        "CommunicationsController",
      );

      return CommentResponseDto.fromEntity(comment);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while creating comment on an entity. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "COMMUNICATIONS_CONTROLLER",
        fn: "commentOnAnEntity",
        organizationId: currentUser.orgUid,
        body,
        user: currentUser,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comments fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get comments on an entity",
    responseType: GetAllCommentsResponse,
  })
  async getCommentsOnAnEntity(
    @CurrentUser() currentUser: CurrentUser,
    @Query() query: GetCommentsForAnEntityQuery,
  ): Promise<CommentResponseDto[]> {
    try {
      // Get the entity
      const entity = await this.communicationsService.getEntity(
        currentUser,
        query.entityType,
        query.entityId,
      );

      // Get the comments
      const { results: comments } =
        await this.communicationsService.getCommentsForAnEntity(
          entity,
          query.entityType,
          query,
          currentUser,
        );

      return comments.map(CommentResponseDto.fromEntity);
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching comments on an entity. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "COMMUNICATIONS_CONTROLLER",
        fn: "getCommentsOnAnEntity",
        organizationId: currentUser.orgUid,
        query,
        user: currentUser,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }

  @Get("/all")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comments fetched successfully!")
  @ApiExcludeEndpoint()
  async getAllCommentsOnAnEntity(
    @Query() getAllCommentsQuery: GetAllCommentsForAnEntityQuery,
    @CurrentUser() currentUser: CurrentUser,
  ) {
    const entityIds = getAllCommentsQuery.entityId.split(",");

    // Get the entities
    const entityResults = await Promise.allSettled(
      entityIds.map((entityId) =>
        this.communicationsService.getEntity(
          currentUser,
          getAllCommentsQuery.entityType,
          entityId,
        ),
      ),
    );

    const entities = entityResults
      .filter(
        (result): result is PromiseFulfilledResult<any> =>
          result.status === "fulfilled",
      )
      .map((result) => result.value)
      .filter(Boolean);

    if (!entities || entities.length === 0) {
      throw new NotFoundException("Entity not found");
    }

    const { results: comments } =
      await this.communicationsService.getAllCommentsForAnEntity(
        entities,
        getAllCommentsQuery.entityType,
        currentUser,
      );

    return comments.map(BulkCommentResponseDto.fromEntity);
  }

  @Get("/user-type")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comments fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get comments for an entity by user type",
    responseType: GetAllCommentsResponse,
  })
  async getCommentsForAnEntityByUserType(
    @CurrentUser() currentUser: CurrentUser,
    @Query() query: GetCommentByUserTypeQuery,
  ): Promise<CommentResponseDto> {
    try {
      // Get the entity
      const entity = await this.communicationsService.getEntity(
        currentUser,
        query.entityType,
        query.entityId,
      );

      // Get the comment
      const comment =
        await this.communicationsService.getCommentForAnEntityByUserType(
          entity,
          query.entityType,
          query,
          currentUser,
        );

      return comment ? CommentResponseDto.fromEntity(comment) : null;
    } catch (error) {
      this.logger.error(
        `${this.logSpanId} Error encountered while fetching comments on an entity. > Error message: ${error.message}`,
        error.stack,
      );

      if (error instanceof HttpException) {
        throw error;
      }

      this.sentryService.captureException(error, {
        tag: "COMMUNICATIONS_CONTROLLER",
        fn: "getCommentsForAnEntityByUserType",
        organizationId: currentUser.orgUid,
        query,
        user: currentUser,
      });

      throw new InternalServerErrorException("Something went wrong!");
    }
  }
}
