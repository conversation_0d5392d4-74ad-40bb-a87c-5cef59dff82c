import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Patch,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiBody, ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  ApiDeleteEndpoint,
  ApiGetEndpoint,
  ApiResponseMessage,
  ApiUpdateEndpoint,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { OrganizationTier } from "@repo/thena-platform-entities";
import { RequireThenaAppImpersonation } from "../../auth/decorators";
import { CurrentUser } from "../../common/decorators";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import {
  GetAllCommentsResponse,
  GetCommentThreadsQuery,
  UpdateCommentDto,
} from "../dto";
import { CommentsActionService } from "../services/comments.action.service";
import { CommentResponseDto } from "../transformers/comment-response.transformer";

@Controller("v1/comments")
@SkipAllThrottler()
@ApiTags("Comments")
@UseInterceptors(ResponseTransformInterceptor)
export class CommentsActionController {
  constructor(private readonly commentsActionService: CommentsActionService) {}

  @Get("/:commentId/threads")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comment threads fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get comment threads",
    responseType: GetAllCommentsResponse,
  })
  async getCommentThreads(
    @Param("commentId") commentId: string,
    @Query() getCommentQuery: GetCommentThreadsQuery,
    @CurrentUser() currentUser: CurrentUser,
  ) {
    if (!commentId) throw new BadRequestException("Comment ID is required!");

    const { results: threadComments } =
      await this.commentsActionService.getCommentThreads(
        commentId,
        getCommentQuery,
        currentUser,
      );

    // If no threaded comments are found, return an empty array
    if (!threadComments || threadComments.length === 0) {
      return [];
    }

    return threadComments.map(CommentResponseDto.fromEntity);
  }

  @Get("/:commentId")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comment fetched successfully!")
  @ApiGetEndpoint({
    summary: "Get a comment",
    responseType: CommentResponseDto,
  })
  async getComment(
    @Param("commentId") commentId: string,
    @CurrentUser() currentUser: CurrentUser,
  ) {
    if (!commentId) throw new BadRequestException("Comment ID is required!");

    const comment = await this.commentsActionService.getPopulatedCommentById(
      commentId,
      currentUser.orgId,
    );

    if (!comment) throw new NotFoundException("Comment not found!");

    return CommentResponseDto.fromEntity(comment);
  }

  @Patch("/:commentId")
  @RequireThenaAppImpersonation()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiBody({ type: UpdateCommentDto })
  @ApiResponseMessage("Comment updated successfully!")
  @ApiUpdateEndpoint({
    summary: "Update a comment",
    responseType: CommentResponseDto,
  })
  async updateComment(
    @Param("commentId") commentId: string,
    @Body() updateCommentDto: UpdateCommentDto,
    @CurrentUser() currentUser: CurrentUser,
  ) {
    if (!commentId) throw new BadRequestException("Comment ID is required!");

    // Update the provided comment
    const comment = await this.commentsActionService.updateComment(
      commentId,
      updateCommentDto,
      currentUser,
    );

    return CommentResponseDto.fromEntity(comment);
  }

  @Delete(":commentId")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Comment deleted successfully!")
  @ApiDeleteEndpoint({ summary: "Delete a comment" })
  async deleteComment(
    @Param("commentId") commentId: string,
    @CurrentUser() currentUser: CurrentUser,
  ) {
    if (!commentId) throw new BadRequestException("Comment ID is required!");
    await this.commentsActionService.deleteComment(commentId, currentUser);
  }
}
