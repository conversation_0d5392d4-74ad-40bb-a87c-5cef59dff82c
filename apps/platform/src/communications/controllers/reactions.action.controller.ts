import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Query,
  UseInterceptors,
} from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  ApiCreateEndpoint,
  ApiDeleteEndpoint,
  ApiResponseMessage,
  ResponseTransformInterceptor,
} from "@repo/nestjs-commons/decorators";
import { RequiredTier } from "@repo/nestjs-commons/guards/organization-tier/index";
import { CUSTOM_LOGGER_TOKEN, ILogger } from "@repo/nestjs-commons/logger";
import { OrganizationTier } from "@repo/thena-platform-entities";
import { RequireThenaAppImpersonation } from "../../auth/decorators";
import { CurrentUser } from "../../common/decorators";
import { SkipAllThrottler } from "../../common/decorators/throttler.decorator";
import { AddReactionDto, CommonReactionResponse } from "../dto";
import { ReactionsActionService } from "../services/reactions.action.service";
import { EmojiResponseDto } from "../transformers/emoji-response.transformer";

@Controller("v1/reactions")
@ApiTags("Reactions")
@UseInterceptors(ResponseTransformInterceptor)
export class ReactionsActionController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly reactionsActionService: ReactionsActionService,
  ) {}

  @Get("emojis")
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Emojis fetched successfully!")
  async getEmojis(@CurrentUser() currentUser: CurrentUser) {
    try {
      const emojis = await this.reactionsActionService.listAllEmojis(
        currentUser,
      );

      return emojis.map(EmojiResponseDto.fromEntity);
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to fetch emojis: ${error.message}`,
          error.stack,
        );
      } else {
        console.error("Failed to fetch emojis", error);
      }

      throw new InternalServerErrorException("Failed to fetch emojis", error);
    }
  }

  @Post(":commentId")
  @RequireThenaAppImpersonation()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Reaction added successfully!")
  @ApiCreateEndpoint({
    summary: "Add a reaction to a comment",
    responseType: CommonReactionResponse,
  })
  @SkipAllThrottler()
  async addReactionToComment(
    @Param("commentId") commentId: string,
    @Body() addReactionDto: AddReactionDto,
    @CurrentUser() currentUser: CurrentUser,
  ) {
    // If no comment ID is provided, throw an error
    if (!commentId) throw new BadRequestException("Comment ID is required!");

    // Add the reaction to the comment
    const reactionAdded = await this.reactionsActionService.addReaction(
      commentId,
      addReactionDto,
      currentUser,
    );

    // Return the result
    return reactionAdded;
  }

  @Delete("/remove/:commentId/:reactionName")
  @RequireThenaAppImpersonation()
  @RequiredTier(OrganizationTier.STANDARD, OrganizationTier.ENTERPRISE)
  @ApiOperation({
    description:
      "This endpoint is only available for standard and enterprise tier organizations.",
  })
  @ApiResponseMessage("Reaction removed successfully!")
  @ApiDeleteEndpoint({ summary: "Remove a reaction from a comment" })
  @SkipAllThrottler()
  async removeReactionFromComment(
    @Param("commentId") commentId: string,
    @Param("reactionName") reactionName: string,
    @CurrentUser() currentUser: CurrentUser,
    @Query("impersonatedUserEmail") impersonatedUserEmail?: string,
  ) {
    // If no comment ID is provided, throw an error
    if (!commentId) throw new BadRequestException("Comment ID is required!");

    // If no reaction name is provided, throw an error
    if (!reactionName) {
      throw new BadRequestException("Reaction name is required!");
    }

    // Remove the reaction from the comment
    await this.reactionsActionService.removeReaction(
      commentId,
      reactionName,
      currentUser,
      {},
      impersonatedUserEmail,
    );
  }
}
