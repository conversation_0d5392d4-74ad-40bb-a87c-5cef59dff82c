import cookie from "@fastify/cookie";
import helmet from "@fastify/helmet";
import multipart from "@fastify/multipart";
import {
  HttpStatus,
  UnprocessableEntityException,
  ValidationPipe,
} from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { MicroserviceOptions } from "@nestjs/microservices";
import type { NestFastifyApplication } from "@nestjs/platform-fastify";
import { FastifyAdapter } from "@nestjs/platform-fastify";
import { SwaggerModule } from "@nestjs/swagger";
import { shouldExcludeFromSentry } from "@repo/nestjs-commons/errors";
import {
  SENTRY_SERVICE_TOKEN,
  SentryExceptionFilter,
} from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import { NewRelicInterceptor, NewRelicService } from "@repo/nestjs-newrelic";
import * as Sentry from "@sentry/node";
import * as rTracer from "cls-rtracer";
import cors from "cors";
import dotenv from "dotenv";
import "newrelic";
import pino from "pino";
import { v4 as uuidv4 } from "uuid";
import { AppModule } from "./app.module";
import bullBoardPlugin from "./bull/bull-board.plugin";
import { BullBoardService } from "./bull/bull-board.service";
import { DatabaseExceptionFilter } from "./filters/database-exception.filter";
import { HttpExceptionFilter } from "./filters/http-exception.filter";
import { grpcServerConfig } from "./grpc.server.config";
import { SwaggerService } from "./swagger/swagger.service";
dotenv.config({ path: ".env" });

async function bootstrap() {
  try {
    const fastifyAdapter = new FastifyAdapter({
      logger: {
        level: "info",
        base: { app: process.env.APP_TAG, service: process.env.SERVICE_TAG },
        timestamp: pino.stdTimeFunctions.isoTime,
      },
      trustProxy: ["loopback"],
      genReqId: () => uuidv4(),
    });

    // Initialize Sentry
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
      tracesSampleRate: 1.0,
      attachStacktrace: true,
      autoSessionTracking: true,
      beforeSend(event, hint) {
        const exception = hint.originalException;
        if (exception instanceof Error && shouldExcludeFromSentry(exception)) {
          return null; // Don't send to Sentry
        }
        return event;
      },
    });

    // Register multipart support
    await fastifyAdapter.register(multipart as any, {
      limits: {
        fieldNameSize: 100, // Max field name size in bytes
        fieldSize: 100 * 1024, // Max field value size in bytes (100KB)
        fields: 10, // Max number of non-file fields
        fileSize: 50 * 1024 * 1024, // For multipart forms, the max file size in bytes (50MB)
        files: 1, // Max number of file fields
        headerPairs: 2000, // Max number of header key=>value pairs
      },
    });

    // Create the NestJS application exposing the REST APIs
    const app = await NestFactory.create<NestFastifyApplication>(
      AppModule,
      fastifyAdapter,
      { bufferLogs: true },
    );

    // Get BullBoardService first
    const bullBoardService = app.get(BullBoardService);

    // Register Bull Board plugin
    await fastifyAdapter.register(bullBoardPlugin as any, {
      queues: [
        bullBoardService.ticketsSNSPublishQueue,
        bullBoardService.accountsSNSPublishQueue,
        bullBoardService.commentSNSPublishQueue,
        bullBoardService.organizationSNSPublishQueue,
        bullBoardService.reactionsSNSPublishQueue,
      ],
      basePath: "/admin/queues",
      logLevel: "silent",
    });
    // Register helmet
    await app.register(helmet as any, {
      crossOriginResourcePolicy: { policy: "same-site" },
      crossOriginEmbedderPolicy: false,
    });

    // Register Cookie Middleware
    await app.register(cookie as any);

    // Connect the gRPC microservice for gRPC routes
    app.connectMicroservice<MicroserviceOptions>(grpcServerConfig);

    const logger = app.get<ILogger>("CustomLogger");

    app.useLogger(logger);

    // Add validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        exceptionFactory: (errors) => {
          const processErrors = (error): any[] => {
            const result = [];
            if (error.constraints) {
              // Extract human-readable constraints
              const constraintValues = Object.values(error.constraints);
              result.push(`${error.property}: ${constraintValues[0]}`);
            }
            if (error.children && error.children.length > 0) {
              error.children.forEach((child) => {
                result.push(
                  ...processErrors(child).map((msg) =>
                    typeof msg === "string"
                      ? `${error.property}.${msg}`
                      : {
                          ...msg,
                          property: `${error.property}.${msg.property}`,
                        },
                  ),
                );
              });
            }
            return result;
          };

          const messages = errors.flatMap(processErrors);

          return new UnprocessableEntityException({
            message: "Validation failed",
            error: "Validation Error",
            statusCode: 422,
            details: messages.map((msg) => {
              // Handle both string messages and object format
              if (typeof msg === "string") {
                const [property, errorMessage] = msg.split(": ");
                return {
                  field: property,
                  errors: [errorMessage || msg],
                };
              }
              return {
                field: msg.property,
                value: msg.value,
                errors: msg.constraints
                  ? Object.values(msg.constraints)
                  : ["Invalid value"],
              };
            }),
          }).getResponse();
        },
      }),
    );

    // Register Sentry Exception Filter
    const sentryService = app.get(SENTRY_SERVICE_TOKEN);

    // Register all exception filters
    app.useGlobalFilters(
      new HttpExceptionFilter(),
      new DatabaseExceptionFilter(),
      new SentryExceptionFilter(sentryService),
    );

    app.use(
      rTracer.fastifyMiddleware({
        echoHeader: true,
        requestIdFactory: (req) => ({
          reqId: req.id,
          context: {},
        }),
      }),
    );

    // Enable CORS with default options
    app.use(cors());

    // Start listening to server termination signals
    app.enableShutdownHooks();

    // Alternatively, customize CORS options
    app.enableCors({
      origin: ["http://localhost:3000", "http://localhost:3000/", "*"], // your frontend URLs
      methods: ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE"],
      allowedHeaders: "*",
      credentials: true,
    });

    // Get SwaggerService from app container
    const swaggerService = app.get(SwaggerService);

    // Initialize swagger document
    swaggerService.setApp(app);

    // Setup swagger UI
    const document = swaggerService.getDocument();
    SwaggerModule.setup("api", app, document);

    // Get NewRelicService from the app
    const newRelicService = app.get(NewRelicService);

    // Only add the interceptor if New Relic is enabled
    if (newRelicService.isEnabled() && process.env.NODE_ENV !== "development") {
      const interceptor = new NewRelicInterceptor(newRelicService);
      app.useGlobalInterceptors(interceptor as any);
      newRelicService.setServiceName(
        `${process.env.APP_TAG} | ${process.env.SERVICE_TAG} | ${process.env.NODE_ENV}`,
      );
    }

    await app.startAllMicroservices();
    await app.listen({ port: 8000, host: "0.0.0.0" }, (err, address) => {
      if (err) {
        process.exit(1);
      }

      logger.log(`Server listening at ${address}`);
    });

    [
      "beforeExit",
      "uncaughtException",
      "unhandledRejection",
      "rejectionHandled",
      "exit",
    ].forEach((event) => {
      process.on(event, (error) => {
        logger.error(
          `🚨🚨🚨 Mayday mayday! Received a ${event} signal! Initiating graceful shutdown`,
        );
        logger.error(
          `Shutting down due to Error message: ${error.message}, Error stack: ${error.stack}`,
        );
      });
    });

    const signals = ["SIGTERM", "SIGINT"];
    signals.forEach((signal) => {
      process.on(signal, async () => {
        logger.log(
          `🚨🚨🚨 Mayday mayday! Received ${signal}. Starting graceful shutdown...`,
        );

        try {
          await app.close();
          logger.log("Application gracefully closed.");
          process.exit(0);
        } catch (error) {
          if (error instanceof Error) {
            logger.error(
              `Error during graceful shutdown: ${error.message}, Error stack: ${error.stack}`,
            );
          }

          process.exit(1);
        }
      });
    });
  } catch (error) {
    if (error instanceof Error) {
      console.error(
        "Failed to start the application:",
        error.message,
        error.stack,
      );
    }

    process.exit(1);
  }
}

bootstrap();
