import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger,
  UnauthorizedException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { AuthenticationGrpcClient } from "@repo/nestjs-commons/guards";
import { FastifyRequest } from "fastify";
import { AppsGrpcClient } from "../../common/grpc/apps.grpc-client";
import { REQUIRE_THENA_APP_IMPERSONATION_KEY } from "../decorators/thena-app-impersonation.decorator";

@Injectable()
export class ThenaAppImpersonationGuard implements CanActivate {
  private readonly logger = new Logger(ThenaAppImpersonationGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly authClient: AuthenticationGrpcClient,
    private readonly appsGrpcClient: AppsGrpcClient,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if this endpoint requires Thena app impersonation support
    const requiresThenaAppImpersonation = this.reflector.getAllAndOverride<boolean>(
      REQUIRE_THENA_APP_IMPERSONATION_KEY,
      [context.getHandler(), context.getClass()],
    );

    // If decorator is not present, allow normal flow
    if (!requiresThenaAppImpersonation) {
      return true;
    }

    const request = context.switchToHttp().getRequest<FastifyRequest>();
    const apiKey = request.headers["x-api-key"] as string;
    const userIdToImpersonate = request.headers["x-user-id"] as string;

    // If no impersonation headers, allow normal flow (bot user context)
    if (!apiKey || !userIdToImpersonate) {
      return true;
    }

    // Both headers present - validate Thena app impersonation
    try {
      this.logger.log(
        `Validating Thena app impersonation for user: ${userIdToImpersonate}`,
        "ThenaAppImpersonationGuard",
      );

      // Step 1: Validate API key and get bot user info
      const botUser = await this.authClient.validateKey(apiKey);
      if (!botUser) {
        throw new UnauthorizedException("Invalid API key");
      }

      // Step 2: Check if bot belongs to Thena-owned app
      const appCheckResult = await this.appsGrpcClient.isThenaOwnedApp(botUser.uid);
      if (!appCheckResult.isThenaOwned) {
        throw new ForbiddenException(
          "Only Thena-owned apps can use user impersonation on this endpoint",
        );
      }

      // Step 3: Create impersonated user context
      // TODO: Implement proper user lookup via gRPC
      // For now, create a mock user object for the impersonated user
      const impersonatedUser = {
        uid: userIdToImpersonate,
        id: `impersonated-${userIdToImpersonate}`,
        email: `${userIdToImpersonate}@example.com`,
        orgId: botUser.orgId,
        orgUid: botUser.orgUid,
        userType: "USER",
        timezone: "UTC",
        token: "",
        sub: userIdToImpersonate,
        // Add impersonation metadata
        _impersonation: {
          isImpersonated: true,
          originalBotUserUid: botUser.uid,
          impersonatedUserUid: userIdToImpersonate,
          appUid: appCheckResult.appUid || "unknown",
          organizationUid: botUser.orgUid,
          impersonatedAt: new Date().toISOString(),
        },
      };

      // Replace the user in the request with the impersonated user
      request.user = impersonatedUser as any;

      this.logger.log(
        `Thena app impersonation successful: ${botUser.uid} → ${userIdToImpersonate}`,
        "ThenaAppImpersonationGuard",
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Thena app impersonation failed: ${error.message}`,
        error.stack,
        "ThenaAppImpersonationGuard",
      );

      if (error instanceof ForbiddenException || error instanceof UnauthorizedException) {
        throw error;
      }

      throw new UnauthorizedException("Failed to validate Thena app impersonation");
    }
  }
}
