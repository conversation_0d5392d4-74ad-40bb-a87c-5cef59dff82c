import { SetMetadata } from "@nestjs/common";

export const REQUIRE_THENA_APP_IMPERSONATION_KEY = "require_thena_app_impersonation";

/**
 * Decorator to mark endpoints that support Thena app impersonation.
 * When this decorator is applied to an endpoint, it allows Thena-owned apps
 * to impersonate users by providing both X-api-key and X-user-id headers.
 * 
 * @example
 * ```typescript
 * @Post('comments')
 * @RequireThenaAppImpersonation()
 * async createComment(@Body() dto: CreateCommentDto) {
 *   // This endpoint supports X-user-id impersonation for Thena apps
 * }
 * ```
 */
export const RequireThenaAppImpersonation = () => 
  SetMetadata(REQUIRE_THENA_APP_IMPERSONATION_KEY, true);
