import { Injectable, Logger } from "@nestjs/common";
import { AuthenticationGrpcClient } from "@repo/nestjs-commons/guards";
import { AppsGrpcClient } from "../../common/grpc/apps.grpc-client";
import { UsersGrpcClient } from "../../common/grpc/users.grpc-client";

export interface ThenaAppValidationResult {
  isValid: boolean;
  user?: any;
  botUser?: any;
  error?: string;
  metadata?: {
    originalBotUserUid: string;
    impersonatedUserUid: string;
    appUid: string;
    organizationUid: string;
  };
}

@Injectable()
export class ThenaAppImpersonationService {
  private readonly logger = new Logger(ThenaAppImpersonationService.name);

  constructor(
    private readonly authClient: AuthenticationGrpcClient,
    private readonly appsGrpcClient: AppsGrpcClient,
    private readonly usersGrpcClient: UsersGrpcClient,
  ) {}

  /**
   * Validates user impersonation for Thena-owned apps
   * @param apiKey The full API key
   * @param userId The user ID to impersonate
   * @returns Promise<ThenaAppValidationResult>
   */
  async validateUserImpersonation(
    apiKey: string,
    userId: string,
  ): Promise<ThenaAppValidationResult> {
    try {
      // Step 1: Validate the API key and get bot user info
      const botUser = await this.authClient.validateKey(apiKey);
      if (!botUser) {
        return {
          isValid: false,
          error: "Invalid API key",
        };
      }

      // Step 2: Check if the bot belongs to a Thena-owned app
      const appCheckResult = await this.appsGrpcClient.isThenaOwnedApp(
        botUser.uid,
      );

      if (!appCheckResult.isThenaOwned) {
        return {
          isValid: false,
          error: "API key does not belong to a Thena-owned app",
        };
      }

      // Step 3: Get the target user to impersonate
      const targetUserResult = await this.usersGrpcClient.getUserByUid(
        userId,
        botUser.orgId,
      );

      if (!targetUserResult.user) {
        return {
          isValid: false,
          error: "Invalid user ID or user not found in organization",
        };
      }

      const targetUser = targetUserResult.user;

      return {
        isValid: true,
        user: targetUser,
        botUser: botUser,
        metadata: {
          originalBotUserUid: botUser.uid,
          impersonatedUserUid: targetUser.uid,
          appUid: appCheckResult.appUid || "unknown",
          organizationUid: targetUser.orgUid || botUser.orgUid,
        },
      };
    } catch (error) {
      this.logger.error("Error validating user impersonation", {
        error: error.message,
        userId,
      });

      return {
        isValid: false,
        error: "Internal server error during validation",
      };
    }
  }

  // Removed placeholder methods - now using real gRPC calls
}
