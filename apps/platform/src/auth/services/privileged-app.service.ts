import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { AuthenticationGrpcClient } from "@repo/nestjs-commons/guards";
import {
  ApiKey,
  AppInstallation,
  InstallationStatus,
  User,
} from "@repo/thena-platform-entities";
import { Repository } from "typeorm";

export interface PrivilegedAppValidationResult {
  isValid: boolean;
  user?: User;
  botUser?: any;
  error?: string;
  metadata?: {
    originalBotUserUid: string;
    impersonatedUserUid: string;
    appUid: string;
    organizationUid: string;
  };
}

@Injectable()
export class PrivilegedAppService {
  private readonly logger = new Logger(PrivilegedAppService.name);

  constructor(
    @InjectRepository(ApiKey)
    private readonly apiKeyRepository: Repository<ApiKey>,
    @InjectRepository(AppInstallation)
    private readonly appInstallationRepository: Repository<AppInstallation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly authClient: AuthenticationGrpcClient,
  ) {}

  /**
   * Checks if an API key belongs to a privileged app
   * @param apiKey The full API key (keyId.keySecret)
   * @returns Promise<boolean> indicating if the app is privileged
   */
  async isPrivilegedApp(apiKey: string): Promise<boolean> {
    const result = await this.getPrivilegedAppInstallation(apiKey);
    return result.isPrivileged;
  }

  /**
   * Gets the privileged app installation for an API key
   * @param apiKey The full API key (keyId.keySecret)
   * @returns Promise<{isPrivileged: boolean, appInstallation?: AppInstallation}>
   */
  private async getPrivilegedAppInstallation(apiKey: string): Promise<{
    isPrivileged: boolean;
    appInstallation?: AppInstallation;
  }> {
    try {
      const [keyId] = apiKey.split('.');
      if (!keyId) {
        return { isPrivileged: false };
      }

      // First, find the API key and user
      const apiKeyRecord = await this.apiKeyRepository
        .createQueryBuilder('apiKey')
        .leftJoinAndSelect('apiKey.user', 'user')
        .where('apiKey.keyId = :keyId', { keyId })
        .andWhere('apiKey.isActive = :isActive', { isActive: true })
        .getOne();

      if (!apiKeyRecord) {
        return { isPrivileged: false };
      }

      return this.getPrivilegedAppInstallationFromRecord(apiKeyRecord);
    } catch (error) {
      this.logger.error('Error checking if app is privileged', {
        error: error.message,
      });
      return { isPrivileged: false };
    }
  }

  /**
   * Gets the privileged app installation from an already-fetched API key record
   * @param apiKeyRecord The API key record with user data
   * @returns Promise<{isPrivileged: boolean, appInstallation?: AppInstallation}>
   */
  private async getPrivilegedAppInstallationFromRecord(apiKeyRecord: ApiKey & { user: User }): Promise<{
    isPrivileged: boolean;
    appInstallation?: AppInstallation;
  }> {
    try {
      // Check if this user's app is privileged
      const appInstallation = await this.appInstallationRepository
        .createQueryBuilder('appInstallation')
        .leftJoinAndSelect('appInstallation.app', 'app')
        .where('appInstallation.botUserId = :botUserId', { botUserId: apiKeyRecord.user.uid })
        .andWhere('appInstallation.status = :status', { status: InstallationStatus.ACTIVE })
        .andWhere('app.isThenaPrivileged = :isPrivileged', { isPrivileged: true })
        .getOne();

      return {
        isPrivileged: !!appInstallation,
        appInstallation: appInstallation || undefined,
      };
    } catch (error) {
      this.logger.error('Error checking if app is privileged from record', {
        error: error.message,
      });
      return { isPrivileged: false };
    }
  }

  /**
   * Validates user impersonation for a privileged app
   * @param apiKey The full API key
   * @param userId The user ID to impersonate
   * @returns Promise<PrivilegedAppValidationResult>
   */
  async getUserInSameOrganization(
    apiKey: string,
    userId: string,
  ): Promise<PrivilegedAppValidationResult> {
    try {
      const [keyId, keySecret] = apiKey.split('.');

      if (!keyId || !keySecret) {
        return {
          isValid: false,
          error: 'Invalid API key format',
        };
      }

      // First, get the API key and bot user information
      const apiKeyRecord = await this.apiKeyRepository
        .createQueryBuilder('apiKey')
        .leftJoinAndSelect('apiKey.user', 'botUser')
        .leftJoinAndSelect('botUser.organization', 'botOrganization')
        .where('apiKey.keyId = :keyId', { keyId })
        .andWhere('apiKey.isActive = :isActive', { isActive: true })
        .getOne();

      if (!apiKeyRecord) {
        return {
          isValid: false,
          error: 'Invalid API key',
        };
      }

      // Validate the API key using gRPC to get full bot user information
      let botUser: any;
      try {
        botUser = await this.authClient.validateKey(apiKey);
      } catch (error) {
        this.logger.error('gRPC error during API key validation', {
          error: error.message,
          apiKeyPrefix: apiKey.substring(0, 8) + '...',
        });
        return {
          isValid: false,
          error: 'Authentication failed while validating API key',
        };
      }

      if (!botUser) {
        return {
          isValid: false,
          error: 'Invalid API key',
        };
      }

      // Check if the API key belongs to a privileged app and get the installation
      // Use the already-fetched apiKeyRecord to avoid redundant database query
      const privilegedResult = await this.getPrivilegedAppInstallationFromRecord(apiKeyRecord);
      if (!privilegedResult.isPrivileged || !privilegedResult.appInstallation) {
        return {
          isValid: false,
          error: 'API key does not belong to a privileged app',
        };
      }

      // Find the target user to impersonate
      const targetUser = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.organization', 'organization')
        .where('user.uid = :userId', { userId })
        .getOne();

      if (!targetUser) {
        return {
          isValid: false,
          error: 'Invalid user ID or user not found in organization',
        };
      }

      // Verify both users are in the same organization
      if (targetUser.organizationId !== apiKeyRecord.user.organizationId) {
        this.logger.warn('Organization mismatch in privileged app impersonation', {
          targetUserOrgId: targetUser.organizationId,
          botOrgId: apiKeyRecord.user.organizationId,
        });
        return {
          isValid: false,
          error: 'Invalid user ID or user not found in organization',
        };
      }

      // Reuse the appInstallation from the privileged check
      const appInstallation = privilegedResult.appInstallation;

      return {
        isValid: true,
        user: targetUser,
        botUser: botUser,
        metadata: {
          originalBotUserUid: apiKeyRecord.user.uid,
          impersonatedUserUid: targetUser.uid,
          appUid: appInstallation.app.uid,
          organizationUid: targetUser.organization?.uid || '',
        },
      };
    } catch (error) {
      this.logger.error('Error validating user impersonation', {
        error: error.message,
        userId,
      });

      return {
        isValid: false,
        error: 'Internal server error during validation',
      };
    }
  }
}
