import { EXTERNAL_STORAGE_RESPONSE_SCHEMA } from "./common.schema";

export const COMMENT_RESPONSE_SCHEMA = {
  type: "object",
  properties: {
    id: { type: "string" },
    content: { type: ["string", "null"] },
    contentHtml: { type: ["string", "null"] },
    contentMarkdown: { type: ["string", "null"] },
    contentJson: { type: ["string", "null"] },
    isEdited: { type: ["boolean", "null"] },
    threadName: { type: ["string", "null"] },
    commentVisibility: {
      type: ["string", "null"],
    },
    commentType: {
      type: ["string", "null"],
    },
    isPinned: { type: ["boolean", "null"] },
    sourceEmailId: { type: ["string", "null"] },
    metadata: { type: ["object", "null"] },
    parentCommentId: { type: ["string", "null"] },
    author: { type: ["string", "null"] },
    authorId: { type: ["string", "null"] },
    authorAvatarUrl: { type: ["string", "null"] },
    authorUserType: { type: ["string", "null"] },
    attachments: EXTERNAL_STORAGE_RESPONSE_SCHEMA,
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
    customerContactId: { type: ["string", "null"] },
    customerContactEmail: { type: ["string", "null"] },
    customerContactAvatarUrl: { type: ["string", "null"] },
    customerContactFirstName: { type: ["string", "null"] },
    customerContactLastName: { type: ["string", "null"] },
    impersonatedUserEmail: { type: ["string", "null"] },
    impersonatedUserName: { type: ["string", "null"] },
    impersonatedUserAvatar: { type: ["string", "null"] },
  },
};
