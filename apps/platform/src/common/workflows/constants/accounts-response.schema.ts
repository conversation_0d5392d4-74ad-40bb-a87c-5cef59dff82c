import {
  EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
  EXTERNAL_STORAGE_RESPONSE_SCHEMA,
} from "./common.schema";

export const ACCOUNT_PAYLOAD_SCHEMA = {
  type: "object",
  required: ["id", "name", "primaryDomain", "createdAt", "updatedAt"],
  properties: {
    id: { type: "string" },
    name: { type: "string" },
    description: { type: ["string", "null"] },
    source: { type: ["string", "null"] },
    logo: { type: ["string", "null"] },
    statusId: { type: ["string", "null"] },
    status: { type: ["string", "null"] },
    statusConfiguration: { type: ["object", "null"] },
    classificationId: { type: ["string", "null"] },
    classification: { type: ["string", "null"] },
    classificationConfiguration: { type: ["object", "null"] },
    healthId: { type: ["string", "null"] },
    health: { type: ["string", "null"] },
    healthConfiguration: { type: ["object", "null"] },
    industryId: { type: ["string", "null"] },
    industry: { type: ["string", "null"] },
    industryConfiguration: { type: ["object", "null"] },
    primaryDomain: { type: "string" },
    secondaryDomain: { type: ["string", "null"] },
    accountOwnerId: { type: ["string", "null"] },
    accountOwner: { type: ["string", "null"] },
    accountOwnerEmail: { type: ["string", "null"] },
    accountOwnerAvatarUrl: { type: ["string", "null"] },
    annualRevenue: { type: ["number", "null"] },
    employees: { type: ["number", "null"] },
    website: { type: ["string", "null"] },
    billingAddress: { type: ["string", "null"] },
    shippingAddress: { type: ["string", "null"] },
    customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
    metadata: { type: ["object", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
};

export const ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA = {
  type: "object",
  required: ["id", "name", "createdAt", "updatedAt"],
  properties: {
    id: { type: "string" },
    name: { type: "string" },
    inverseRelationshipId: { type: ["string", "null"] },
    inverseRelationship: { type: ["string", "null"] },
    metadata: { type: ["object", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
};

export const ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA = {
  type: "object",
  required: [
    "id",
    "accountId",
    "account",
    "relatedAccountId",
    "relatedAccount",
    "relationshipType",
    "createdAt",
    "updatedAt",
  ],
  properties: {
    id: { type: "string" },
    accountId: { type: "string" },
    account: { type: "string" },
    relatedAccountId: { type: "string" },
    relatedAccount: { type: "string" },
    relationshipType: ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA,
    metadata: { type: ["object", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
};

export const ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA = {
  type: "object",
  required: ["id", "accountId", "account", "createdAt", "updatedAt"],
  properties: {
    id: { type: "string" },
    accountId: { type: "string" },
    account: { type: "string" },
    title: { type: ["string", "null"] },
    description: { type: ["string", "null"] },
    activityTimestamp: { type: "string" },
    duration: { type: ["number", "null"] },
    location: { type: ["string", "null"] },
    type: { type: ["string", "null"] },
    typeId: { type: ["string", "null"] },
    typeConfiguration: { type: ["object", "null"] },
    status: { type: ["string", "null"] },
    statusId: { type: ["string", "null"] },
    statusConfiguration: { type: ["object", "null"] },
    participants: { type: ["array", "null"], items: { type: "string" } },
    creator: { type: "string" },
    creatorId: { type: "string" },
    creatorEmail: { type: "string" },
    attachments: EXTERNAL_STORAGE_RESPONSE_SCHEMA,
    metadata: { type: ["object", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
};

export const ACCOUNT_NOTE_PAYLOAD_SCHEMA = {
  type: "object",
  required: [
    "id",
    "accountId",
    "account",
    "content",
    "visibility",
    "author",
    "authorId",
    "authorEmail",
    "createdAt",
    "updatedAt",
  ],
  properties: {
    id: { type: "string" },
    accountId: { type: "string" },
    account: { type: "string" },
    content: { type: "string" },
    type: { type: ["string", "null"] },
    typeId: { type: ["string", "null"] },
    typeConfiguration: { type: ["object", "null"] },
    visibility: { type: "string" },
    attachments: EXTERNAL_STORAGE_RESPONSE_SCHEMA,
    author: { type: "string" },
    authorId: { type: "string" },
    authorEmail: { type: "string" },
    metadata: { type: ["object", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
};

export const ACCOUNT_TASK_PAYLOAD_SCHEMA = {
  type: "object",
  required: ["id", "accountId", "account", "title", "createdAt", "updatedAt"],
  properties: {
    id: { type: "string" },
    accountId: { type: "string" },
    account: { type: "string" },
    activityId: { type: "string" },
    title: { type: "string" },
    description: { type: ["string", "null"] },
    assigneeId: { type: ["string", "null"] },
    assignee: { type: ["string", "null"] },
    creator: { type: "string" },
    creatorId: { type: "string" },
    creatorEmail: { type: "string" },
    type: { type: ["string", "null"] },
    typeId: { type: ["string", "null"] },
    typeConfiguration: { type: ["object", "null"] },
    status: { type: ["string", "null"] },
    statusId: { type: ["string", "null"] },
    statusConfiguration: { type: ["object", "null"] },
    priority: { type: ["string", "null"] },
    priorityId: { type: ["string", "null"] },
    priorityConfiguration: { type: ["object", "null"] },
    attachments: EXTERNAL_STORAGE_RESPONSE_SCHEMA,
    metadata: { type: ["object", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
};

export const CUSTOMER_CONTACT_PAYLOAD_SCHEMA = {
  type: "object",
  required: ["id", "firstName", "email", "createdAt", "updatedAt"],
  properties: {
    id: { type: "string" },
    firstName: { type: "string" },
    lastName: { type: ["string", "null"] },
    email: { type: "string" },
    phoneNumber: { type: ["string", "null"] },
    avatarUrl: { type: ["string", "null"] },
    accounts: {
      type: ["array", "null"],
      items: {
        type: "object",
        properties: { id: { type: "string" }, name: { type: "string" } },
      },
    },
    contactTypeId: { type: ["string", "null"] },
    contactType: { type: ["string", "null"] },
    customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
    metadata: { type: ["object", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
};

export const ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA = {
  type: "object",
  required: ["id", "attribute", "value", "isDefault", "createdAt", "updatedAt"],
  properties: {
    id: { type: "string" },
    attribute: { type: "string" },
    value: { type: "string" },
    isDefault: { type: ["boolean", "null"] },
    configuration: { type: ["object", "null"] },
    metadata: { type: ["object", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
};
