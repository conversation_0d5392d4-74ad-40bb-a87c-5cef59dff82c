export const MESSAGE_ATTRIBUTES_SCHEMA = {
  type: "object",
  required: [
    "event_name",
    "event_id",
    "event_timestamp",
    "context_user_id",
    "context_user_type",
    "context_organization_id",
  ],
  properties: {
    event_id: {
      type: "string",
    },
    event_name: {
      type: "string",
    },
    context_user_id: {
      type: "string",
    },
    event_timestamp: {
      type: "string",
    },
    context_user_type: {
      type: "string",
    },
    context_organization_id: {
      type: "string",
    },
  },
};

export const COMMON_MESSAGE_PROPERTIES = {
  actor: {
    type: "object",
    required: ["email", "id", "type"],
    properties: {
      id: {
        type: "string",
      },
      type: {
        type: "string",
      },
      email: {
        type: "string",
      },
    },
  },
  orgId: {
    type: "string",
  },
  teamId: {
    type: "string",
  },
  eventId: {
    type: "string",
  },
  eventType: {
    type: "string",
  },
  timestamp: {
    type: "string",
  },
};

export const EXTERNAL_STORAGE_RESPONSE_SCHEMA = {
  type: ["array", "null"],
  items: {
    type: "object",
    properties: {
      id: { type: ["string", "null"] },
      url: { type: ["string", "null"] },
      name: { type: ["string", "null"] },
      size: { type: ["number", "string", "null"] }, // originally a number but, while sending to SNS, we do a JSON.stringify on the entire event payload.
      contentType: { type: ["string", "null"] },
      createdAt: { type: ["string", "null"] },
    },
  },
};

export const EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA = {
  type: ["array", "null"],
  items: {
    type: "object",
    required: ["customFieldId", "data"],
    properties: {
      customFieldId: { type: ["string", "null"] },
      data: {
        type: ["array", "null"],
        items: { type: ["object", "null"] },
      },
    },
  },
};

export const COMMON_USER_RESPONSE_SCHEMA = {
  type: "object",
  properties: {
    id: { type: "string" },
    name: { type: "string" },
    email: { type: "string" },
    userType: { type: "string" },
    status: { type: "string" },
    isActive: { type: "boolean" },
    lastLoginAt: { type: ["string", "null"] },
    avatarUrl: { type: ["string", "null"] },
    timezone: { type: ["string", "null"] },
    createdAt: { type: "string" },
    updatedAt: { type: "string" },
  },
  required: [
    "id",
    "name",
    "email",
    "userType",
    "status",
    "isActive",
    "createdAt",
    "updatedAt",
  ],
};
