import { EntityType } from "@repo/thena-platform-entities";
import { AccountEvents } from "@repo/thena-shared-interfaces";
import { AbstractWorkflowEvent } from "@repo/workflow-engine";
import {
  ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
  ACCOUNT_NOTE_PAYLOAD_SCHEMA,
  ACCOUNT_PAYLOAD_SCHEMA,
  ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
  ACCOUNT_TASK_PAYLOAD_SCHEMA,
  COMMON_MESSAGE_PROPERTIES,
  CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
  MESSAGE_ATTRIBUTES_SCHEMA,
} from "../constants";

// Account Events ================================================================

export class AccountCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account created";

  static override eventType = AccountEvents.ACCOUNT_CREATED;

  static override description =
    "This event is triggered when an account is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              account: ACCOUNT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT,
    requiredFields: {
      accountId: "{{context.event.message.payload.account.id}}",
    },
    pathToAnnotate: "context.event.message.payload.account",
  };
}

export class AccountUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account updated";

  static override eventType = AccountEvents.ACCOUNT_UPDATED;

  static override description =
    "This event is triggered when an account is updated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              account: ACCOUNT_PAYLOAD_SCHEMA,
              previousAccount: ACCOUNT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT,
    requiredFields: {
      accountId: "{{context.event.message.payload.account.id}}",
    },
    pathToAnnotate: "context.event.message.payload.account",
  };
}

export class AccountDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account deleted";
  static override eventType = AccountEvents.ACCOUNT_DELETED;
  static override description =
    "This event is triggered when an account is deleted";
  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousAccount: ACCOUNT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT,
    requiredFields: {
      accountId: "{{context.event.message.payload.previousAccount.id}}",
    },
    pathToAnnotate: "context.event.message.payload.previousAccount",
  };
}

// Account Relationship Events ================================================================

export class AccountRelationshipCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account relationship created";

  static override eventType = AccountEvents.ACCOUNT_RELATIONSHIP_CREATED;

  static override description =
    "This event is triggered when an account relationship is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              relationship: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountRelationshipUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account relationship updated";

  static override eventType = AccountEvents.ACCOUNT_RELATIONSHIP_UPDATED;

  static override description =
    "This event is triggered when an account relationship is updated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousRelationship: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
              relationship: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class AccountRelationshipDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account relationship deleted";

  static override eventType = AccountEvents.ACCOUNT_RELATIONSHIP_DELETED;

  static override description =
    "This event is triggered when an account relationship is deleted";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousRelationship: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

// Account Activity Events ================================================================

export class AccountActivityCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account activity created";

  static override eventType = AccountEvents.ACCOUNT_ACTIVITY_CREATED;

  static override description =
    "This event is triggered when an account activity is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              activity: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_ACTIVITY,
    requiredFields: {
      accountActivityId: "{{context.event.message.payload.activity.id}}",
    },
    pathToAnnotate: "context.event.message.payload.activity",
  };
}

export class AccountActivityUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account activity updated";

  static override eventType = AccountEvents.ACCOUNT_ACTIVITY_UPDATED;

  static override description =
    "This event is triggered when an account activity is updated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousActivity: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
              activity: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_ACTIVITY,
    requiredFields: {
      accountActivityId: "{{context.event.message.payload.activity.id}}",
    },
    pathToAnnotate: "context.event.message.payload.activity",
  };
}

export class AccountActivityDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account activity deleted";

  static override eventType = AccountEvents.ACCOUNT_ACTIVITY_DELETED;

  static override description =
    "This event is triggered when an account activity is deleted";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousActivity: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_ACTIVITY,
    requiredFields: {
      accountActivityId:
        "{{context.event.message.payload.previousActivity.id}}",
    },
    pathToAnnotate: "context.event.message.payload.previousActivity",
  };
}

// Account Note Events ================================================================

export class AccountNoteCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account note created";

  static override eventType = AccountEvents.ACCOUNT_NOTE_CREATED;

  static override description =
    "This event is triggered when an account note is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              note: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_NOTE,
    requiredFields: {
      accountNoteId: "{{context.event.message.payload.note.id}}",
    },
    pathToAnnotate: "context.event.message.payload.note",
  };
}

export class AccountNoteUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account note updated";

  static override eventType = AccountEvents.ACCOUNT_NOTE_UPDATED;

  static override description =
    "This event is triggered when an account note is updated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousNote: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
              note: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_NOTE,
    requiredFields: {
      accountNoteId: "{{context.event.message.payload.note.id}}",
    },
    pathToAnnotate: "context.event.message.payload.note",
  };
}

export class AccountNoteDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account note deleted";

  static override eventType = AccountEvents.ACCOUNT_NOTE_DELETED;

  static override description =
    "This event is triggered when an account note is deleted";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousNote: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_NOTE,
    requiredFields: {
      accountNoteId: "{{context.event.message.payload.previousNote.id}}",
    },
    pathToAnnotate: "context.event.message.payload.previousNote",
  };
}

// Account Task Events ================================================================

export class AccountTaskCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account task created";

  static override eventType = AccountEvents.ACCOUNT_TASK_CREATED;

  static override description =
    "This event is triggered when an account task is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              task: ACCOUNT_TASK_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_TASK,
    requiredFields: {
      accountTaskId: "{{context.event.message.payload.task.id}}",
    },
    pathToAnnotate: "context.event.message.payload.task",
  };
}

export class AccountTaskUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account task updated";

  static override eventType = AccountEvents.ACCOUNT_TASK_UPDATED;

  static override description =
    "This event is triggered when an account task is updated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousTask: ACCOUNT_TASK_PAYLOAD_SCHEMA,
              task: ACCOUNT_TASK_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_TASK,
    requiredFields: {
      accountTaskId: "{{context.event.message.payload.task.id}}",
    },
    pathToAnnotate: "context.event.message.payload.task",
  };
}

export class AccountTaskDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Account task deleted";

  static override eventType = AccountEvents.ACCOUNT_TASK_DELETED;

  static override description =
    "This event is triggered when an account task is deleted";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousTask: ACCOUNT_TASK_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.ACCOUNT_TASK,
    requiredFields: {
      accountTaskId: "{{context.event.message.payload.previousTask.id}}",
    },
    pathToAnnotate: "context.event.message.payload.previousTask",
  };
}

// Customer Contact Events ================================================================

export class CustomerContactCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Customer contact created";

  static override eventType = AccountEvents.CUSTOMER_CONTACT_CREATED;

  static override description =
    "This event is triggered when a customer contact is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              customerContact: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.CUSTOMER_CONTACT,
    requiredFields: {
      customerContactId: "{{context.event.message.payload.customerContact.id}}",
    },
    pathToAnnotate: "context.event.message.payload.customerContact",
  };
}

export class CustomerContactUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Customer contact updated";

  static override eventType = AccountEvents.CUSTOMER_CONTACT_UPDATED;

  static override description =
    "This event is triggered when a customer contact is updated";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousCustomerContact: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
              customerContact: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.CUSTOMER_CONTACT,
    requiredFields: {
      customerContactId: "{{context.event.message.payload.customerContact.id}}",
    },
    pathToAnnotate: "context.event.message.payload.customerContact",
  };
}

export class CustomerContactDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Customer contact deleted";

  static override eventType = AccountEvents.CUSTOMER_CONTACT_DELETED;

  static override description =
    "This event is triggered when a customer contact is deleted";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousCustomerContact: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
            },
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };

  static override metadata = {
    entityType: EntityType.CUSTOMER_CONTACT,
    requiredFields: {
      customerContactId:
        "{{context.event.message.payload.previousCustomerContact.id}}",
    },
    pathToAnnotate: "context.event.message.payload.previousCustomerContact",
  };
}
