import { OrganizationEvents } from "@repo/thena-shared-interfaces";
import { AbstractWorkflowEvent } from "@repo/workflow-engine";
import {
  COMMON_MESSAGE_PROPERTIES,
  COMMON_USER_RESPONSE_SCHEMA,
  MESSAGE_ATTRIBUTES_SCHEMA,
  ORGANIZATION_RESPONSE_SCHEMA,
} from "../constants";

export class OrganizationCreatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Organization created";

  static override eventType = OrganizationEvents.CREATED;

  static override description =
    "This event is triggered when an organization is created";

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              organization: ORGANIZATION_RESPONSE_SCHEMA,
            },
            required: ["organization"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class OrganizationUpdatedEvent extends AbstractWorkflowEvent {
  static override eventName = "Organization updated";

  static override description =
    "This event is triggered when an organization is updated";

  static override eventType = OrganizationEvents.UPDATED;

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              organization: ORGANIZATION_RESPONSE_SCHEMA,
              previousOrganization: ORGANIZATION_RESPONSE_SCHEMA,
            },
            required: ["organization", "previousOrganization"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class OrganizationDeletedEvent extends AbstractWorkflowEvent {
  static override eventName = "Organization deleted";

  static override description =
    "This event is triggered when an organization is deleted";

  static override eventType = OrganizationEvents.DELETED;

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              previousOrganization: ORGANIZATION_RESPONSE_SCHEMA,
            },
            required: ["previousOrganization"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}

export class OrganizationMemberJoinedEvent extends AbstractWorkflowEvent {
  static override eventName = "Organization member joined";

  static override description =
    "This event is triggered when a member joins an organization";

  static override eventType = OrganizationEvents.MEMBER_JOINED;

  static override schema = {
    type: "object",
    required: ["message", "messageAttributes"],
    properties: {
      message: {
        type: "object",
        required: [
          "actor",
          "orgId",
          "eventId",
          "payload",
          "eventType",
          "timestamp",
        ],
        properties: {
          ...COMMON_MESSAGE_PROPERTIES,
          payload: {
            type: "object",
            properties: {
              organization: ORGANIZATION_RESPONSE_SCHEMA,
              user: COMMON_USER_RESPONSE_SCHEMA,
            },
            required: ["organization", "user"],
          },
        },
      },
      messageAttributes: MESSAGE_ATTRIBUTES_SCHEMA,
    },
  };
}
