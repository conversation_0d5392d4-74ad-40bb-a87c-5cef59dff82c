import { Inject, Injectable, OnModuleInit } from "@nestjs/common";
import { SentryService } from "@repo/nestjs-commons/filters/sentry-alerts.filter";
import { ILogger } from "@repo/nestjs-commons/logger";
import { workflows } from "@repo/shared-proto";
import {
  AbstractWorkflowActivity,
  AbstractWorkflowEvent,
} from "@repo/workflow-engine";
import { ConfigService } from "../../config/config.service";
import { WorkflowsGrpcClient } from "../grpc/workflows.client.grpc";
import * as activities from "./activities";
import * as events from "./events";

@Injectable()
export class WorkflowsRegistrySyncService implements OnModuleInit {
  constructor(
    private readonly workflowsGrpcClient: WorkflowsGrpcClient,
    private readonly configService: ConfigService,
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    try {
      await Promise.all([this.registerActivities(), this.registerEvents()]);
    } catch (error) {
      this.logger.error(
        `[RegistrySyncService] An error occurred during posting activities and events: ${error.message}`,
        error.stack,
      );
      this.sentryService.captureException(error, {
        tag: "RegistrySyncToWorkflows",
        fn: "TicketsRegistrySyncService.onModuleInit",
      });
    }
  }

  private async registerActivities() {
    const activitiesList: workflows.Activity[] = [];

    for (const [_key, Activity] of Object.entries(activities)) {
      if (
        typeof Activity === "function" &&
        Activity.prototype instanceof AbstractWorkflowActivity
      ) {
        const instance = new Activity(this.configService);
        const signature = {
          ...Activity.getSignature(),
          connectionDetails: instance.connectionDetails,
        } as workflows.Activity;

        activitiesList.push(signature);
      }
    }

    await this.workflowsGrpcClient.registerActivities({
      source: workflows.Source.PLATFORM_APP,
      activities: activitiesList,
    });
  }

  private async registerEvents() {
    const eventsList: workflows.Event[] = [];

    for (const [_key, Event] of Object.entries(events)) {
      if (
        typeof Event === "function" &&
        Event.prototype instanceof AbstractWorkflowEvent
      ) {
        const signature = Event.getSignature() as workflows.Event;
        eventsList.push(signature);
      }
    }

    await this.workflowsGrpcClient.registerEvents({
      source: workflows.Source.PLATFORM_APP,
      events: eventsList,
    });
  }
}
