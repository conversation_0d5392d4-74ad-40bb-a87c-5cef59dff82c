import { tickets } from "@repo/shared-proto";
import { AbstractWorkflowActivity } from "@repo/workflow-engine";
import { ConfigKeys, ConfigService } from "../../../config/config.service";
import {
  COMMON_TICKET_RESPONSE_SCHEMA,
  EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
} from "../constants";
import { ACTIVITY_THROTTLER_CONFIG } from "../constants/activity-throttler.config";

/**
 * Assign ticket activity
 */
export class AssignTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Assign ticket";

  static override description = "This activity assigns a ticket to an agent";

  static override identifier = "tickets:assign-ticket";

  static override requestSchema = {
    type: "object",
    properties: {
      ticketId: { type: "string" },
      agentId: { type: "string" },
      unassign: { type: "boolean" },
    },
    required: ["ticketId", "agentId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      success: { type: "boolean" },
      ticket: COMMON_TICKET_RESPONSE_SCHEMA,
    },
    required: ["success", "ticket"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: "AssignTicket",
      protoPath: "dist/proto/tickets/tickets.proto",
    },
  };

  static override isCompensable = true;

  static override compensationActivity = {
    identifier: "tickets:compensate-assign-ticket",
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["ticketId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class CompensateAssignTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Compensate assign ticket";

  static override description =
    "This activity compensates the assign ticket activity";

  static override identifier = "tickets:compensate-assign-ticket";

  static override requestSchema = {
    type: "object",
    properties: {
      executionId: { type: "string" },
    },
    required: ["executionId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      success: { type: "boolean" },
      ticket: COMMON_TICKET_RESPONSE_SCHEMA,
    },
    required: ["success", "ticket"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: "CompensateAssignTicket",
      protoPath: "dist/proto/tickets/tickets.proto",
    },
  };

  static override isCompensable = false;

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: false,
    },
  };
}

/**
 * Create ticket activity
 */
export class CreateTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create ticket";

  static override description = "This activity creates a ticket";

  static override identifier = "tickets:create-ticket";

  static override requestSchema = {
    type: "object",
    properties: {
      title: { type: "string" },
      requestorEmail: { type: "string" },
      teamId: { type: "string" },
      accountId: { type: ["string", "null"] },
      assignedAgentId: { type: ["string", "null"] },
      description: { type: ["string", "null"] },
      dueDate: { type: ["string", "null"] },
      statusId: { type: ["string", "null"] },
      statusName: { type: ["string", "null"] },
      priorityId: { type: ["string", "null"] },
      priorityName: { type: ["string", "null"] },
      typeId: { type: ["string", "null"] },
      submitterEmail: { type: ["string", "null"] },
      isPrivate: { type: ["boolean", "null"] },
      attachmentUrls: { type: ["array", "null"], items: { type: "string" } },
      source: { type: ["string", "null"] },
      aiGeneratedTitle: { type: ["string", "null"] },
      aiGeneratedSummary: { type: ["string", "null"] },
      performRouting: { type: ["boolean", "null"] },
      formId: { type: ["string", "null"] },
      metadata: { type: ["object", "null"] },
      customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
    },
    required: ["title", "requestorEmail", "teamId"],
  };

  static override responseSchema = COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: "CreateTicket",
      protoPath: "dist/proto/tickets/tickets.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["teamId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

/**
 * Update ticket activity
 */
export class UpdateTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update ticket";

  static override description = "This activity updates a ticket";

  static override identifier = "tickets:update-ticket";

  static override requestSchema = {
    type: "object",
    properties: {
      id: { type: "string" },
      title: { type: ["string", "null"] },
      subTeamId: { type: ["string", "null"] },
      accountId: { type: ["string", "null"] },
      assignedAgentId: { type: ["string", "null"] },
      description: { type: ["string", "null"] },
      dueDate: { type: ["string", "null"] },
      statusId: { type: ["string", "null"] },
      statusName: { type: ["string", "null"] },
      priorityId: { type: ["string", "null"] },
      priorityName: { type: ["string", "null"] },
      typeId: { type: ["string", "null"] },
      submitterEmail: { type: ["string", "null"] },
      isPrivate: { type: ["boolean", "null"] },
      attachmentUrls: { type: ["array", "null"], items: { type: "string" } },
      aiGeneratedTitle: { type: ["string", "null"] },
      aiGeneratedSummary: { type: ["string", "null"] },
      metadata: { type: ["object", "null"] },
      customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
    },
    required: ["id"],
  };

  static override responseSchema = COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: "UpdateTicket",
      protoPath: "dist/proto/tickets/tickets.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["id"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_2,
    },
  };
}

/**
 * Get ticket activity
 */
export class GetTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get ticket";

  static override description = "This activity gets a ticket by ID";

  static override identifier = "tickets:get-ticket";

  static override requestSchema = {
    type: "object",
    properties: {
      id: { type: "string" },
    },
    required: ["id"],
  };

  static override responseSchema = COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: "GetTicket",
      protoPath: "dist/proto/tickets/tickets.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["id"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Get tickets with cursor activity
 */
export class GetTicketsWithCursorActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get tickets with cursor";

  static override description =
    "This activity gets tickets with cursor pagination";

  static override identifier = "tickets:get-tickets-with-cursor";

  static override requestSchema = {
    type: "object",
    properties: {
      limit: { type: ["number", "null"] },
      teamId: { type: ["string", "null"] },
      afterCursor: { type: ["string", "null"] },
    },
    required: ["limit"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      cursor: { type: ["string", "null"] },
      tickets: {
        type: "array",
        items: COMMON_TICKET_RESPONSE_SCHEMA,
      },
    },
    required: ["cursor", "tickets"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: "GetTicketsWithCursor",
      protoPath: "dist/proto/tickets/tickets.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["teamId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

/**
 * Archive ticket activity
 */
export class ArchiveTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Archive ticket";

  static override description = "This activity archives a ticket";

  static override identifier = "tickets:archive-ticket";

  static override requestSchema = {
    type: "object",
    properties: {
      id: { type: "string" },
    },
    required: ["id"],
  };

  static override responseSchema = COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: "ArchiveTicket",
      protoPath: "dist/proto/tickets/tickets.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["id"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

/**
 * Escalate ticket activity
 */
export class EscalateTicketActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Escalate ticket";

  static override description = "This activity escalates a ticket";

  static override identifier = "tickets:escalate-ticket";

  static override requestSchema = {
    type: "object",
    properties: {
      id: { type: "string" },
      reason: { type: "string" },
      details: { type: "string" },
      impact: { type: "string" },
    },
    required: ["id", "reason", "details", "impact"],
  };

  static override responseSchema = COMMON_TICKET_RESPONSE_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: tickets.GRPC_TICKETS_V1_PACKAGE_NAME,
      serviceName: tickets.TICKETS_SERVICE_NAME,
      methodName: "EscalateTicket",
      protoPath: "dist/proto/tickets/tickets.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["id"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}
