import { accounts } from "@repo/shared-proto";
import { AbstractWorkflowActivity } from "@repo/workflow-engine";
import { ConfigKeys, ConfigService } from "../../../config/config.service";
import { EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA } from "../constants";
import {
  ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
  ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA,
  ACCOUNT_NOTE_PAYLOAD_SCHEMA,
  ACCOUNT_PAYLOAD_SCHEMA,
  ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
  ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA,
  ACCOUNT_TASK_PAYLOAD_SCHEMA,
  CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
} from "../constants/accounts-response.schema";
import { ACTIVITY_THROTTLER_CONFIG } from "../constants/activity-throttler.config";

// Account Activities ================================================================

export class GetAccounts extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get accounts";

  static override description = "This activity gets accounts";

  static override identifier = "accounts:get-accounts";

  static override requestSchema = {
    type: "object",
    properties: {
      source: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      classification: { type: ["string", "null"] },
      health: { type: ["string", "null"] },
      industry: { type: ["string", "null"] },
      accountOwnerId: { type: ["string", "null"] },
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: "GetAccounts",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_3,
    },
  };
}

export class GetAccountDetails extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get account details";

  static override description = "This activity gets account details";

  static override identifier = "accounts:get-account-details";

  static override requestSchema = {
    type: "object",
    required: ["id"],
    properties: {
      id: { type: "string" },
    },
  };

  static override responseSchema = ACCOUNT_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: "GetAccountDetails",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["id"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_2,
    },
  };
}

export class CreateAccount extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create account";

  static override description = "This activity creates an account";

  static override identifier = "accounts:create-account";

  static override requestSchema = {
    type: "object",
    required: ["name", "primaryDomain"],
    properties: {
      name: { type: "string" },
      description: { type: ["string", "null"] },
      source: { type: ["string", "null"] },
      accountOwnerId: { type: ["string", "null"] },
      logo: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      classification: { type: ["string", "null"] },
      health: { type: ["string", "null"] },
      industry: { type: ["string", "null"] },
      primaryDomain: { type: "string" },
      secondaryDomain: { type: ["string", "null"] },
      annualRevenue: { type: ["number", "null"] },
      employees: { type: ["number", "null"] },
      website: { type: ["string", "null"] },
      billingAddress: { type: ["string", "null"] },
      shippingAddress: { type: ["string", "null"] },
      customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
      addExistingUsersToAccountContacts: { type: ["boolean", "null"] },
      metadata: {
        type: ["object", "null"],
      },
    },
  };

  static override responseSchema = ACCOUNT_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: "CreateAccount",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

export class UpdateAccount extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update account";

  static override description = "This activity updates an account";

  static override identifier = "accounts:update-account";

  static override requestSchema = {
    type: "object",
    required: ["id"],
    properties: {
      id: { type: "string" },
      name: { type: ["string", "null"] },
      description: { type: ["string", "null"] },
      source: { type: ["string", "null"] },
      accountOwnerId: { type: ["string", "null"] },
      logo: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      classification: { type: ["string", "null"] },
      health: { type: ["string", "null"] },
      industry: { type: ["string", "null"] },
      primaryDomain: { type: ["string", "null"] },
      secondaryDomain: { type: ["string", "null"] },
      annualRevenue: { type: ["number", "null"] },
      employees: { type: ["number", "null"] },
      website: { type: ["string", "null"] },
      billingAddress: { type: ["string", "null"] },
      shippingAddress: { type: ["string", "null"] },
      customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
      addExistingUsersToAccountContacts: { type: ["boolean", "null"] },
      metadata: {
        type: ["object", "null"],
      },
    },
  };

  static override responseSchema = ACCOUNT_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: "UpdateAccount",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["id"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class DeleteAccount extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete account";

  static override description = "This activity deletes an account";

  static override identifier = "accounts:delete-account";

  static override requestSchema = {
    type: "object",
    required: ["id"],
    properties: {
      id: { type: "string" },
    },
  };

  static override responseSchema = {};

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: "DeleteAccount",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

// Account Attribute Value Activities ================================================================

export class GetAccountAttributeValues extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get account attribute values";

  static override description = "This activity gets account attribute values";

  static override identifier = "accounts:get-account-attribute-values";

  static override requestSchema = {
    type: "object",
    required: ["attribute"],
    properties: {
      attribute: { type: "string" },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
      methodName: "GetAccountAttributeValues",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

export class CreateAccountAttributeValue extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create account attribute value";

  static override description =
    "This activity creates an account attribute value";

  static override identifier = "accounts:create-account-attribute-value";

  static override requestSchema = {
    type: "object",
    required: ["attribute", "value"],
    properties: {
      attribute: { type: "string" },
      value: { type: "string" },
      isDefault: { type: ["boolean", "null"] },
      icon: { type: ["string", "null"] },
      color: { type: ["string", "null"] },
      isClosed: { type: ["boolean", "null"] },
    },
  };

  static override responseSchema = ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
      methodName: "CreateAccountAttributeValue",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_5,
    },
  };
}

export class UpdateAccountAttributeValue extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update account attribute value";

  static override description =
    "This activity updates an account attribute value";

  static override identifier = "accounts:update-account-attribute-value";

  static override requestSchema = {
    type: "object",
    required: ["id"],
    properties: {
      id: { type: "string" },
      value: { type: ["string", "null"] },
      isDefault: { type: ["boolean", "null"] },
      icon: { type: ["string", "null"] },
      color: { type: ["string", "null"] },
      isClosed: { type: ["boolean", "null"] },
    },
  };

  static override responseSchema = ACCOUNT_ATTRIBUTE_VALUE_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
      methodName: "UpdateAccountAttributeValue",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["id"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class DeleteAccountAttributeValue extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete account attribute value";

  static override description =
    "This activity deletes an account attribute value";

  static override identifier = "accounts:delete-account-attribute-value";

  static override requestSchema = {
    type: "object",
    required: ["id"],
    properties: {
      id: { type: "string" },
      forceDelete: { type: ["boolean", "null"] },
    },
  };

  static override responseSchema = {};

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME,
      methodName: "DeleteAccountAttributeValue",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

// Account Relationship Type Activities ================================================================

export class GetAccountRelationshipTypes extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get account relationship types";

  static override description = "This activity gets account relationship types";

  static override identifier = "accounts:get-account-relationship-types";

  static override requestSchema = {
    type: "object",
    properties: {
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: "GetAccountRelationshipTypes",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class CreateAccountRelationshipType extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create account relationship type";

  static override description =
    "This activity creates an account relationship type";

  static override identifier = "accounts:create-account-relationship-type";

  static override requestSchema = {
    type: "object",
    required: ["name"],
    properties: {
      name: { type: "string" },
      inverseRelationshipId: { type: ["string", "null"] },
    },
  };

  static override responseSchema = ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: "CreateAccountRelationshipType",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

export class UpdateAccountRelationshipType extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update account relationship type";

  static override description =
    "This activity updates an account relationship type";

  static override identifier = "accounts:update-account-relationship-type";

  static override requestSchema = {
    type: "object",
    required: ["id"],
    properties: {
      id: { type: "string" },
      name: { type: ["string", "null"] },
      inverseRelationshipId: { type: ["string", "null"] },
    },
  };

  static override responseSchema = ACCOUNT_RELATIONSHIP_TYPE_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: "UpdateAccountRelationshipType",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["id"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class DeleteAccountRelationshipType extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete account relationship type";

  static override description =
    "This activity deletes an account relationship type";

  static override identifier = "accounts:delete-account-relationship-type";

  static override requestSchema = {
    type: "object",
    required: ["id"],
    properties: {
      id: { type: "string" },
    },
  };

  static override responseSchema = {};

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: "DeleteAccountRelationshipType",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

// Account Relationships Activities ================================================================

export class GetAccountRelationships extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get account relationships";

  static override description = "This activity gets account relationships";

  static override identifier = "accounts:get-account-relationships";

  static override requestSchema = {
    type: "object",
    required: ["accountId"],
    properties: {
      accountId: { type: ["string", "null"] },
      relationshipType: { type: ["string", "null"] },
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: "GetAccountRelationships",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["accountId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class CreateAccountRelationship extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create account relationship";

  static override description = "This activity creates an account relationship";

  static override identifier = "accounts:create-account-relationship";

  static override requestSchema = {
    type: "object",
    required: ["accountId", "relatedAccountId", "relationshipType"],
    properties: {
      accountId: { type: "string" },
      relatedAccountId: { type: "string" },
      relationshipType: { type: "string" },
    },
  };

  static override responseSchema = ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: "CreateAccountRelationship",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

export class UpdateAccountRelationship extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update account relationship";

  static override description = "This activity updates an account relationship";

  static override identifier = "accounts:update-account-relationship";

  static override requestSchema = {
    type: "object",
    required: ["relationshipId"],
    properties: {
      relationshipId: { type: "string" },
      relationshipType: { type: "string" },
    },
  };

  static override responseSchema = ACCOUNT_RELATIONSHIP_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: "UpdateAccountRelationship",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["relationshipId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class DeleteAccountRelationship extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete account relationship";

  static override description = "This activity deletes an account relationship";

  static override identifier = "accounts:delete-account-relationship";

  static override requestSchema = {
    type: "object",
    required: ["relationshipId"],
    properties: {
      relationshipId: { type: "string" },
    },
  };

  static override responseSchema = {};

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_RELATIONSHIPS_SERVICE_NAME,
      methodName: "DeleteAccountRelationship",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

// Account Activity Activities ================================================================

export class GetAccountActivities extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get account activities";

  static override description = "This activity gets account activities";

  static override identifier = "accounts:get-account-activities";

  static override requestSchema = {
    type: "object",
    properties: {
      accountId: { type: ["string", "null"] },
      type: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
      methodName: "GetAccountActivities",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["accountId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class CreateAccountActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create account activity";

  static override description = "This activity creates an account activity";

  static override identifier = "accounts:create-account-activity";

  static override requestSchema = {
    type: "object",
    required: ["accountId", "activityTimestamp"],
    properties: {
      accountId: { type: "string" },
      activityTimestamp: { type: "string" },
      title: { type: ["string", "null"] },
      description: { type: ["string", "null"] },
      duration: { type: ["number", "null"] },
      location: { type: ["string", "null"] },
      type: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      participants: { type: ["array", "null"] },
      attachmentUrls: { type: ["array", "null"] },
    },
  };

  static override responseSchema = ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
      methodName: "CreateAccountActivity",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["accountId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_2,
    },
  };
}

export class UpdateAccountActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update account activity";

  static override description = "This activity updates an account activity";

  static override identifier = "accounts:update-account-activity";

  static override requestSchema = {
    type: "object",
    required: ["activityId"],
    properties: {
      activityId: { type: "string" },
      title: { type: ["string", "null"] },
      description: { type: ["string", "null"] },
      activityTimestamp: { type: ["string", "null"] },
      duration: { type: ["number", "null"] },
      location: { type: ["string", "null"] },
      type: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      participants: { type: ["array", "null"] },
      attachmentUrls: { type: ["array", "null"] },
    },
  };

  static override responseSchema = ACCOUNT_ACTIVITY_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
      methodName: "UpdateAccountActivity",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["activityId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class DeleteAccountActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete account activity";

  static override description = "This activity deletes an account activity";

  static override identifier = "accounts:delete-account-activity";

  static override requestSchema = {
    type: "object",
    required: ["activityId"],
    properties: {
      activityId: { type: "string" },
    },
  };

  static override responseSchema = {};

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_ACTIVITIES_SERVICE_NAME,
      methodName: "DeleteAccountActivity",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

// Account Notes Activities ================================================================

export class GetAccountNotes extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get account notes";

  static override description = "This activity gets account notes";

  static override identifier = "accounts:get-account-notes";

  static override requestSchema = {
    type: "object",
    required: ["accountId"],
    properties: {
      accountId: { type: ["string", "null"] },
      type: { type: ["string", "null"] },
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_NOTE_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_NOTES_SERVICE_NAME,
      methodName: "GetAccountNotes",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["accountId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class CreateAccountNote extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create account note";

  static override description = "This activity creates an account note";

  static override identifier = "accounts:create-account-note";

  static override requestSchema = {
    type: "object",
    required: ["accountId", "content"],
    properties: {
      accountId: { type: "string" },
      content: { type: "string" },
      type: { type: ["string", "null"] },
      visibility: { type: ["string", "null"] },
      attachmentUrls: {
        type: ["array", "null"],
        items: {
          type: "string",
        },
      },
    },
  };

  static override responseSchema = ACCOUNT_NOTE_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_NOTES_SERVICE_NAME,
      methodName: "CreateAccountNote",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["accountId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_2,
    },
  };
}

export class UpdateAccountNote extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update account note";

  static override description = "This activity updates an account note";

  static override identifier = "accounts:update-account-note";

  static override requestSchema = {
    type: "object",
    required: ["noteId"],
    properties: {
      noteId: { type: "string" },
      content: { type: ["string", "null"] },
      type: { type: ["string", "null"] },
      visibility: { type: ["string", "null"] },
      attachmentUrls: {
        type: ["array", "null"],
        items: {
          type: "string",
        },
      },
    },
  };

  static override responseSchema = ACCOUNT_NOTE_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_NOTES_SERVICE_NAME,
      methodName: "UpdateAccountNote",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["noteId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class DeleteAccountNote extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete account note";

  static override description = "This activity deletes an account note";

  static override identifier = "accounts:delete-account-note";

  static override requestSchema = {
    type: "object",
    required: ["noteId"],
    properties: {
      noteId: { type: "string" },
    },
  };

  static override responseSchema = {};

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_NOTES_SERVICE_NAME,
      methodName: "DeleteAccountNote",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

// Account Task Activities ================================================================

export class GetAccountTasks extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get account tasks";

  static override description = "This activity gets account tasks";

  static override identifier = "accounts:get-account-tasks";

  static override requestSchema = {
    type: "object",
    required: ["accountId"],
    properties: {
      accountId: { type: ["string", "null"] },
      activityId: { type: ["string", "null"] },
      type: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      priority: { type: ["string", "null"] },
      assigneeId: { type: ["string", "null"] },
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_TASK_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_TASKS_SERVICE_NAME,
      methodName: "GetAccountTasks",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["accountId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class CreateAccountTask extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create account task";

  static override description = "This activity creates an account task";

  static override identifier = "accounts:create-account-task";

  static override requestSchema = {
    type: "object",
    required: ["accountId", "title", "assigneeId"],
    properties: {
      accountId: { type: "string" },
      title: { type: "string" },
      assigneeId: { type: "string" },
      activityId: { type: ["string", "null"] },
      description: { type: ["string", "null"] },
      type: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      priority: { type: ["string", "null"] },
      attachmentUrls: {
        type: ["array", "null"],
        items: {
          type: "string",
        },
      },
    },
  };

  static override responseSchema = ACCOUNT_TASK_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_TASKS_SERVICE_NAME,
      methodName: "CreateAccountTask",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["accountId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_2,
    },
  };
}

export class UpdateAccountTask extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update account task";

  static override description = "This activity updates an account task";

  static override identifier = "accounts:update-account-task";

  static override requestSchema = {
    type: "object",
    required: ["taskId"],
    properties: {
      taskId: { type: "string" },
      title: { type: ["string", "null"] },
      assigneeId: { type: ["string", "null"] },
      activityId: { type: ["string", "null"] },
      description: { type: ["string", "null"] },
      type: { type: ["string", "null"] },
      status: { type: ["string", "null"] },
      priority: { type: ["string", "null"] },
      attachmentUrls: {
        type: ["array", "null"],
        items: {
          type: "string",
        },
      },
    },
  };

  static override responseSchema = ACCOUNT_TASK_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_TASKS_SERVICE_NAME,
      methodName: "UpdateAccountTask",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["taskId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class DeleteAccountTask extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete account task";

  static override description = "This activity deletes an account task";

  static override identifier = "accounts:delete-account-task";

  static override requestSchema = {
    type: "object",
    required: ["taskId"],
    properties: {
      taskId: { type: "string" },
    },
  };

  static override responseSchema = {};

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNT_TASKS_SERVICE_NAME,
      methodName: "DeleteAccountTask",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

// Customer Contacts Activities ================================================================

export class GetCustomerContacts extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Get customer contacts";

  static override description = "This activity gets customer contacts";

  static override identifier = "accounts:get-customer-contacts";

  static override requestSchema = {
    type: "object",
    properties: {
      accountId: { type: ["string", "null"] },
      contactType: { type: ["string", "null"] },
      page: { type: ["number", "null"] },
      limit: { type: ["number", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "array",
        items: CUSTOMER_CONTACT_PAYLOAD_SCHEMA,
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: "GetCustomerContacts",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

export class CreateCustomerContact extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Create customer contact";

  static override description = "This activity creates a customer contact";

  static override identifier = "accounts:create-customer-contact";

  static override requestSchema = {
    type: "object",
    required: ["firstName", "email"],
    properties: {
      firstName: { type: "string" },
      lastName: { type: ["string", "null"] },
      email: { type: "string" },
      phoneNumber: { type: ["string", "null"] },
      contactType: { type: ["string", "null"] },
      avatarUrl: { type: ["string", "null"] },
      accountIds: {
        type: ["array", "null"],
        items: { type: "string" },
      },
      customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
    },
  };

  static override responseSchema = CUSTOMER_CONTACT_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: "CreateCustomerContact",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_5,
    },
  };
}

export class BulkCreateCustomerContacts extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Bulk create customer contacts";

  static override description = "This activity bulk creates customer contacts";

  static override identifier = "accounts:bulk-create-customer-contacts";

  static override requestSchema = {
    type: "object",
    required: ["contacts"],
    properties: {
      contacts: {
        type: "array",
        items: {
          type: "object",
          required: ["firstName", "email"],
          properties: {
            firstName: { type: "string" },
            lastName: { type: ["string", "null"] },
            email: { type: "string" },
            phoneNumber: { type: ["string", "null"] },
            avatarUrl: { type: ["string", "null"] },
          },
        },
      },
      accountIds: {
        type: ["array", "null"],
        items: { type: "string" },
      },
      contactType: { type: ["string", "null"] },
    },
  };

  static override responseSchema = {
    type: "object",
    required: ["data"],
    properties: {
      data: {
        type: "object",
        properties: {
          total: { type: "number" },
          created: { type: "number" },
          skipped: { type: "number" },
        },
      },
    },
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: "BulkCreateCustomerContacts",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class UpdateCustomerContact extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Update customer contact";

  static override description = "This activity updates a customer contact";

  static override identifier = "accounts:update-customer-contact";

  static override requestSchema = {
    type: "object",
    required: ["contactId"],
    properties: {
      contactId: { type: "string" },
      firstName: { type: ["string", "null"] },
      lastName: { type: ["string", "null"] },
      email: { type: ["string", "null"] },
      avatarUrl: { type: ["string", "null"] },
      phoneNumber: { type: ["string", "null"] },
      accountIds: {
        type: ["array", "null"],
        items: { type: "string" },
      },
      contactType: { type: ["string", "null"] },
      customFieldValues: EXTERNAL_CUSTOM_FIELDS_RESPONSE_SCHEMA,
    },
  };

  static override responseSchema = CUSTOMER_CONTACT_PAYLOAD_SCHEMA;

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: "UpdateCustomerContact",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["contactId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}

export class DeleteCustomerContact extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Delete customer contact";

  static override description = "This activity deletes a customer contact";

  static override identifier = "accounts:delete-customer-contact";

  static override requestSchema = {
    type: "object",
    required: ["contactId"],
    properties: {
      contactId: { type: "string" },
    },
  };

  static override responseSchema = {};

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.CUSTOMER_CONTACTS_SERVICE_NAME,
      methodName: "DeleteCustomerContact",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_4,
    },
  };
}

export class FilterAccountsByPrimaryDomainsActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Filter accounts by primary domains";

  static override description =
    "This activity filters accounts by primary domains";

  static override identifier = "accounts:filter-accounts-by-primary-domains";

  static override requestSchema = {
    type: "object",
    properties: {
      primaryDomains: {
        type: "array",
        items: { type: "string" },
      },
    },
    required: ["primaryDomains"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_PAYLOAD_SCHEMA,
      },
    },
    required: ["data"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: "FilterAccountsByPrimaryDomains",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_5,
    },
  };
}

export class FilterAccountsByIdsActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Filter accounts by IDs";

  static override description = "This activity filters accounts by IDs";

  static override identifier = "accounts:filter-accounts-by-ids";

  static override requestSchema = {
    type: "object",
    properties: {
      ids: {
        type: "array",
        items: { type: "string" },
      },
    },
    required: ["ids"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      data: {
        type: "array",
        items: ACCOUNT_PAYLOAD_SCHEMA,
      },
    },
    required: ["data"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: accounts.GRPC_ACCOUNTS_V1_PACKAGE_NAME,
      serviceName: accounts.ACCOUNTS_SERVICE_NAME,
      methodName: "FilterAccountsByIds",
      protoPath: "dist/proto/accounts/accounts.proto",
    },
  };

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      ...ACTIVITY_THROTTLER_CONFIG.TIER_5,
    },
  };
}
