import { users } from "@repo/shared-proto/dist/generated/src";
import { AbstractWorkflowActivity } from "@repo/workflow-engine";
import { ConfigKeys, ConfigService } from "../../../config/config.service";
import { ACTIVITY_THROTTLER_CONFIG } from "../constants/activity-throttler.config";

export class CheckUserAvailabilityActivity extends AbstractWorkflowActivity {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  static override activityName = "Check user availability";

  static override description =
    "This activity checks if user is available from user time off and business hours";

  static override identifier = "users:check-user-availability";

  static override requestSchema = {
    type: "object",
    properties: {
      userId: { type: "string" },
    },
    required: ["userId"],
  };

  static override responseSchema = {
    type: "object",
    properties: {
      isAvailable: { type: "boolean" },
      reason: {
        type: "string",
        enum: ["IN_BUSINESS_HOURS", "OUTSIDE_BUSINESS_HOURS", "TIME_OFF"],
      },
    },
    required: ["isAvailable", "reason"],
  };

  override connectionDetails = {
    transport: "GRPC" as const,
    grpcConfig: {
      url: this.configService.get(ConfigKeys.PLATFORM_GRPC_URL),
      packageName: users.GRPC_USERS_V1_PACKAGE_NAME,
      serviceName: users.USER_SERVICE_NAME,
      methodName: "CheckUserAvailability",
      protoPath: "dist/proto/users/users.proto",
    },
  };

  static override isCompensable = false;

  static override accessibleToTeam = true;

  static override metadata = {
    throttler: {
      enabled: true,
      argsToUseForKey: ["userId"],
      ...ACTIVITY_THROTTLER_CONFIG.TIER_1,
    },
  };
}
