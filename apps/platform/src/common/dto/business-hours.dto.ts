import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { TeamUserRoutingStrategy } from "@repo/thena-platform-entities";
import { Type } from "class-transformer";
import {
  <PERSON>Array,
  IsBoolean,
  IsDefined,
  IsEnum,
  IsOptional,
  IsString,
  IsTimeZone,
  Validate,
  ValidateNested,
} from "class-validator";
import { IsDateDDMMOrDDMMYYYY } from "../validators/date.validators";
import {
  IsTimeFormatConstraint,
  IsValidTimeSlotConstraint,
} from "../validators/time.validators";

// Business Slot Class 1
export class BusinessSlotDto {
  @IsString()
  @Validate(IsTimeFormatConstraint)
  @ApiProperty({
    description: "Start time in 24-hour format (HH:mm)",
    example: "10:00",
  })
  start: string;

  @IsString()
  @Validate(IsTimeFormatConstraint)
  @ApiProperty({
    description: "End time in 24-hour format (HH:mm)",
    example: "18:00",
  })
  end: string;
}

// Business Day Class
export class BusinessDayDto {
  @IsBoolean()
  @IsDefined({ message: "The business day must be active or inactive!" })
  @ApiProperty({ description: "Whether the business day is active" })
  isActive: boolean;

  @Validate(IsValidTimeSlotConstraint)
  @Type(() => BusinessSlotDto)
  @ValidateNested({ each: true })
  @ApiProperty({
    description: "The time slots for the business day",
    type: [BusinessSlotDto],
  })
  slots: BusinessSlotDto[];
}

// Business Hours Config Class
export class BusinessHoursConfigDto {
  @ValidateNested()
  @Type(() => BusinessDayDto)
  @ApiProperty({ description: "The business hours for Monday" })
  monday: BusinessDayDto;

  @ValidateNested()
  @Type(() => BusinessDayDto)
  @ApiProperty({ description: "The business hours for Tuesday" })
  tuesday: BusinessDayDto;

  @ValidateNested()
  @Type(() => BusinessDayDto)
  @ApiProperty({ description: "The business hours for Wednesday" })
  wednesday: BusinessDayDto;

  @ValidateNested()
  @Type(() => BusinessDayDto)
  @ApiProperty({ description: "The business hours for Thursday" })
  thursday: BusinessDayDto;

  @ValidateNested()
  @Type(() => BusinessDayDto)
  @ApiProperty({ description: "The business hours for Friday" })
  friday: BusinessDayDto;

  @ValidateNested()
  @Type(() => BusinessDayDto)
  @ApiProperty({ description: "The business hours for Saturday" })
  saturday: BusinessDayDto;

  @ValidateNested()
  @Type(() => BusinessDayDto)
  @ApiProperty({ description: "The business hours for Sunday" })
  sunday: BusinessDayDto;
}

// Update Team Configurations Class
export class UpdateTimezoneWorkingHoursDto {
  @IsTimeZone()
  @IsOptional()
  @ApiPropertyOptional({ description: "The timezone of the team" })
  timezone?: string;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the routing rules respect the timezone for the teams",
  })
  routingRespectsTimezone?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      "Whether the routing rules respect the user capacity for the teams",
  })
  routingRespectsUserCapacity?: boolean;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: "The fallback sub team of the team",
  })
  fallbackSubTeam?: string;

  @IsOptional()
  @IsArray()
  @IsDateDDMMOrDDMMYYYY({ each: true })
  @ApiPropertyOptional({
    description: "The dates of the team's holidays",
    example: ["25-12", "25-12-2024"],
    isArray: true,
    type: [String],
  })
  holidays?: Array<string>;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      "Whether the routing rules respect the user timezone for the teams",
  })
  routingRespectsUserTimezone?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description:
      "Whether the routing rules respect the user availability for the teams",
  })
  routingRespectsUserAvailability?: boolean;

  @IsOptional()
  @IsEnum(TeamUserRoutingStrategy)
  @ApiPropertyOptional({
    description: "The user routing strategy for the team",
    enum: TeamUserRoutingStrategy,
  })
  userRoutingStrategy?: TeamUserRoutingStrategy;

  @IsBoolean()
  @IsOptional()
  @ApiPropertyOptional({
    description: "Whether the team uses common daily config",
  })
  commonDailyConfig?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessSlotDto)
  @ApiPropertyOptional({
    description: "The common slots for the team",
    example: [{ start: "09:00", end: "17:00" }],
  })
  commonSlots?: BusinessSlotDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessHoursConfigDto)
  @ApiPropertyOptional({
    description: "The business hours of the team",
    example: {
      monday: {
        isActive: true,
        slots: [{ start: "09:00", end: "17:00" }],
      },
    },
  })
  dailyConfig?: BusinessHoursConfigDto;
}
