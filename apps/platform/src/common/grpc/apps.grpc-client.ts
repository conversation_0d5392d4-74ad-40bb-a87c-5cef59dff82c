import { Injectable, OnModuleInit } from "@nestjs/common";
import { BaseGrpcClient } from "@repo/nestjs-commons/grpc";
import { ILogger } from "@repo/nestjs-commons/logger";
import { apps } from "@repo/shared-proto";
import { ConfigKeys, ConfigService } from "../../config/config.service";

@Injectable()
export class AppsGrpcClient extends BaseGrpcClient implements OnModuleInit {
  constructor(
    logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    super(logger, "apps");
  }

  onModuleInit() {
    this.initializeClient("apps", "Apps");
  }

  protected getServiceUrl(): string {
    return this.configService.get(ConfigKeys.APPS_GRPC_URL) || "0.0.0.0:50053";
  }

  /**
   * Check if a bot user belongs to a Thena-owned app
   * @param botUserUid The UID of the bot user
   * @returns Promise<apps.IsThenaOwnedAppResponse>
   */
  async isThenaOwnedApp(
    botUserUid: string,
  ): Promise<apps.IsThenaOwnedAppResponse> {
    return await this.makeGrpcRequest<
      apps.IsThenaOwnedAppRequest,
      apps.IsThenaOwnedAppResponse
    >("IsThenaOwnedApp", { botUserUid }, {});
  }
}
