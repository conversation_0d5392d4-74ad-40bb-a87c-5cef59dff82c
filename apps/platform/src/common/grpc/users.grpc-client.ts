import { Injectable, OnModuleInit } from "@nestjs/common";
import { BaseGrpcClient } from "@repo/nestjs-commons/grpc";
import { ILogger } from "@repo/nestjs-commons/logger";
import { users } from "@repo/shared-proto";
import { ConfigKeys, ConfigService } from "../../config/config.service";

@Injectable()
export class UsersGrpcClient extends BaseGrpcClient implements OnModuleInit {
  constructor(
    logger: ILogger,
    private readonly configService: ConfigService,
  ) {
    super(logger, "users");
  }

  onModuleInit() {
    this.initializeClient("users", users.USER_SERVICE_NAME);
  }

  protected getServiceUrl(): string {
    return this.configService.get(ConfigKeys.PLATFORM_GRPC_URL);
  }

  /**
   * Get user by UID
   * @param userUid The UID of the user
   * @param orgId The organization ID for context
   * @returns Promise<users.GetUserByUidResponse>
   */
  async getUserByUid(
    userUid: string,
    orgId: string,
  ): Promise<users.GetUserByUidResponse> {
    return await this.makeGrpcRequest<
      users.GetUserByUidRequest,
      users.GetUserByUidResponse
    >("GetUserByUid", { userUid }, { user_id: userUid, org_id: orgId });
  }
}
