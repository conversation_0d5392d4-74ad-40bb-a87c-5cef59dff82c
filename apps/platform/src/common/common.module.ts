import { Module } from "@nestjs/common";
import { APP_INTERCEPTOR } from "@nestjs/core";
import {
  SENTRY_SERVICE_TOKEN,
  SentryService,
} from "@repo/nestjs-commons/filters";
import {
  CUSTOM_LOGGER_TOKEN,
  ILogger,
  PinoLoggerService,
} from "@repo/nestjs-commons/logger";
import { ConfigModule } from "../config/config.module";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { RateLimitHeadersInterceptor } from "../interceptors/rate-limit-headers.interceptor";
import { WorkflowsGrpcClient } from "./grpc/workflows.client.grpc";
import { FieldMetadataService } from "./services/field-metadata.service";
import { ValidationService } from "./services/validation.service";
import { WorkflowsRegistrySyncService } from "./workflows/registry-sync.service";

@Module({
  imports: [ConfigModule],
  providers: [
    // Rate limit headers interceptor
    {
      provide: APP_INTERCEPTOR,
      useClass: RateLimitHeadersInterceptor,
    },
    // gRPC Clients
    WorkflowsGrpcClient,

    // Services
    FieldMetadataService,
    WorkflowsRegistrySyncService,

    // System services
    {
      provide: CUSTOM_LOGGER_TOKEN,
      useFactory: (configService: ConfigService) => {
        const appTag = configService.get(ConfigKeys.APP_TAG);
        const serviceTag = configService.get(ConfigKeys.SERVICE_TAG);
        return new PinoLoggerService(appTag, serviceTag);
      },
      inject: [ConfigService],
    },
    {
      provide: "ValidationService",
      useClass: ValidationService,
    },
    {
      provide: SENTRY_SERVICE_TOKEN,
      useFactory: (configService: ConfigService, logger: ILogger) => {
        const sentryDsn = configService.get(ConfigKeys.SENTRY_DSN);
        const appEnv = configService.get(ConfigKeys.NODE_ENV);
        const appTag = configService.get(ConfigKeys.APP_TAG);
        const serviceTag = configService.get(ConfigKeys.SERVICE_TAG);
        return new SentryService(logger, sentryDsn, appEnv, appTag, serviceTag);
      },
      inject: [ConfigService, CUSTOM_LOGGER_TOKEN],
    },
  ],
  exports: [
    CUSTOM_LOGGER_TOKEN,
    "ValidationService",
    SENTRY_SERVICE_TOKEN,

    // gRPC Clients
    WorkflowsGrpcClient,

    // Services
    FieldMetadataService,
  ],
})
export class CommonModule {}
