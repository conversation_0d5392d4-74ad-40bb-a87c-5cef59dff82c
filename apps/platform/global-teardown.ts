import { TestingModule } from "@nestjs/testing";
import {
  Account,
  AccountActivity,
  AccountAttributeValue,
  AccountNote,
  AccountRelationship,
  AccountRelationshipType,
  AccountTask,
  AuditLog,
  BusinessHoursConfig,
  Comment,
  CustomerContact,
  CustomField,
  CustomFieldValues,
  CustomObject,
  CustomObjectFields,
  CustomObjectRecords,
  Draft,
  EmojiActions,
  Emojis,
  Form,
  FormFieldEvents,
  Mentions,
  NotificationChannel,
  NotificationSubscription,
  NotificationSubscriptionPreference,
  Organization,
  OrganizationDomains,
  OrganizationInvitations,
  PlatformSettings,
  PlatformSettingsSchema,
  Reactions,
  Storage,
  Tag,
  Team,
  TeamCapacity,
  TeamConfiguration,
  TeamMember,
  TeamRoutingRules,
  ThenaRestrictedField,
  Ticket,
  TicketPriority,
  TicketRelationships,
  TicketSentiment,
  TicketStatus,
  TicketTimeLog,
  TicketType,
  TimeOff,
  User,
  UserNotificationPreference,
  UserNotificationPreferenceProfile,
  UserSkills,
  Views,
  ViewsType,
} from "@repo/thena-platform-entities";
import { Redis } from "ioredis";
import { DataSource } from "typeorm";

interface UserWithPassword extends User {
  password: string;
  organization_id?: string;
}

declare global {
  // eslint-disable-next-line no-var
  var testModule: TestingModule;
  // eslint-disable-next-line no-var
  var testUser: UserWithPassword;
  // eslint-disable-next-line no-var
  var testOrganization: Organization;
  // eslint-disable-next-line no-var
  var testTeam: Team;
  // eslint-disable-next-line no-var
  var testTag: Tag;
}

export default async function globalTeardown() {
  // Create TypeORM connection
  const dataSource = new DataSource({
    type: "postgres",
    host: "localhost",
    port: 54332,
    username: "postgres",
    password: "postgres",
    database: "postgres",
    entities: [
      Account,
      AccountActivity,
      AccountAttributeValue,
      AccountNote,
      AccountRelationship,
      AccountRelationshipType,
      AccountTask,
      AuditLog,
      BusinessHoursConfig,
      Comment,
      CustomerContact,
      CustomField,
      CustomFieldValues,
      CustomObject,
      CustomObjectFields,
      CustomObjectRecords,
      Draft,
      EmojiActions,
      Emojis,
      Form,
      FormFieldEvents,
      Mentions,
      NotificationChannel,
      NotificationSubscription,
      NotificationSubscriptionPreference,
      Organization,
      OrganizationDomains,
      OrganizationInvitations,
      PlatformSettings,
      PlatformSettingsSchema,
      Reactions,
      Storage,
      Tag,
      Team,
      TeamCapacity,
      TeamConfiguration,
      TeamMember,
      TeamRoutingRules,
      ThenaRestrictedField,
      Ticket,
      TicketPriority,
      TicketRelationships,
      TicketSentiment,
      TicketStatus,
      TicketTimeLog,
      TicketType,
      TimeOff,
      User,
      UserNotificationPreference,
      UserNotificationPreferenceProfile,
      UserSkills,
      Views,
      ViewsType,
    ],
    synchronize: false,
  });

  // Create Redis client
  const redis = new Redis({
    host: "localhost",
    port: 23011,
  });

  // Remove all data from the database
  await dataSource.initialize();
  await dataSource.query(`TRUNCATE TABLE "organization" CASCADE;`); // Org id is present everywhere
  await dataSource.query(`TRUNCATE TABLE "auth"."users" CASCADE;`);
  await dataSource.query(`TRUNCATE TABLE "views_types" CASCADE;`);
  await dataSource.query(`TRUNCATE TABLE "emojis" CASCADE;`);
  await dataSource.destroy();

  // Remove cache
  await redis.flushall();
  await redis.quit();
}
