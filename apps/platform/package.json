{"name": "@thena-backend/platform", "description": "", "version": "1.0.0", "dependencies": {"@aws-sdk/client-s3": "^3.677.0", "@aws-sdk/s3-request-presigner": "^3.686.0", "@bull-board/api": "^5.9.1", "@bull-board/fastify": "^5.9.1", "@bull-board/ui": "^5.9.1", "@fastify/cookie": "^7.0.0", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "7.7.3", "@fastify/static": "7.0.4", "@grpc/grpc-js": "^1.12.2", "@grpc/reflection": "^1.0.4", "@knocklabs/node": "^0.6.17", "@nest-lab/throttler-storage-redis": "^1.0.0", "@nestjs/bullmq": "^11.0.1", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.4", "@nestjs/core": "^10.4.4", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.7", "@nestjs/platform-fastify": "^10.4.5", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.2.1", "@nestjs/typeorm": "^11.0.0", "@repo/may-i": "workspace:*", "@repo/nestjs-commons": "workspace:*", "@repo/shared-proto": "workspace:*", "@repo/thena-eventbridge": "workspace:*", "@repo/thena-platform-entities": "workspace:*", "@repo/thena-shared-interfaces": "workspace:*", "@repo/nestjs-newrelic": "workspace:*", "@repo/thena-shared-libs": "workspace:*", "@repo/workflow-engine": "workspace:*", "@supabase/supabase-js": "^2.47.5", "axios": "^1.7.8", "bullmq": "^5.37.0", "cache-manager": "5.7.6", "cls-rtracer": "^2.6.3", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "fastify": "^5.0.0", "fastify-plugin": "^5.0.1", "glob": "^11.0.1", "install": "^0.13.0", "ioredis": "^5.4.1", "istanbul-badges-readme": "^1.9.0", "joi": "^17.13.3", "js-yaml": "^4.1.0", "jsdom": "^26.0.0", "lodash": "^4.17.21", "luxon": "^3.5.0", "nanoid": "3", "nest-commander": "^3.16.0", "node-emoji": "^2.2.0", "pg": "^8.13.0", "pino": "^9.5.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "tldts": "^6.1.55", "typesense": "^1.8.2", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@compodoc/compodoc": "^1.1.26", "@faker-js/faker": "^9.0.3", "@jest/globals": "^29.7.0", "@nestjs/axios": "^3.0.3", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.1.3", "@nestjs/testing": "^10.4.4", "@repo/eslint-config": "workspace:*", "@repo/thena-shared-interfaces": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/cors": "^2.8.17", "@types/jest": "^29.5.2", "@types/js-yaml": "^4.0.9", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.9", "@types/luxon": "^3.4.2", "@types/node": "^22.9.0", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "pino-pretty": "^11.3.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.20", "typescript": "^5.6.3"}, "main": "index.js", "scripts": {"build": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "clean:modules": "rimraf node_modules", "consumer": "nest start --config nest-cli-consumer.json", "consumer:debug": "nest start --debug --watch --config nest-cli-consumer.json", "consumer:dev": "nest start --watch --config nest-cli-consumer.json", "consumer:prod": "node dist/workflow.consumer", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "producer": "nest start --config nest-cli-producer.json", "producer:debug": "nest start --debug --watch --config nest-cli-producer.json", "producer:dev": "nest start --watch --config nest-cli-producer.json", "producer:prod": "node dist/workflow.producer", "start": "nest start", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch | pino-pretty --singleLine --colorize", "start:prod": "node dist/src/main", "test": "jest --verbose --detectOpenHandles --forceExit", "test:cov": "jest --config ./jest.config.js --no-cache --coverage --verbose --detectOpenHandles --forceExit", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:file": "jest --runInBand --verbose", "test:watch": "jest --watch", "make-badges": "jest --coverage --coverageReporters=\"json-summary\"  && istanbul-badges-readme"}}