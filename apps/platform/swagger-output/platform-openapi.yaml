openapi: 3.0.0
paths:
  /v1/auth/login:
    post:
      operationId: AuthController_signIn
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignInDto'
      responses:
        '201':
          description: ''
  /v1/auth/signup:
    post:
      operationId: AuthController_signUp
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignUpDto'
      responses:
        '201':
          description: ''
  /v1/users/list:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonUserConfigurationsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/metadata/slack-auth:
    patch:
      operationId: UsersController_setUserMetadata
      summary: ''
      description: This endpoint is only available for enterprise organizations.
      parameters:
        - name: userEmail
          required: true
          in: query
          schema:
            type: string
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: unset
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
      tags:
        - Users
  /v1/users/details:
    get:
      operationId: ''
      summary: Fetch current user's details
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users:
    patch:
      operationId: ''
      summary: Update your user details
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/business-hours:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTimezoneWorkingHoursDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonUserConfigurationsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/availability:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserAvailabilityDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateUserAvailabilityResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/settings:
    patch:
      operationId: UserSettingsController_updateUserSettings
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserSettingsDto'
      responses:
        '200':
          description: ''
      tags:
        - Users
  /v1/users/time-off:
    get:
      operationId: ''
      summary: Get all your time off
      parameters:
        - name: page
          required: false
          in: query
          description: The page number to fetch time offs by
          schema:
            minimum: 0
            default: 0
            example: 1
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of time offs to fetch
          schema:
            minimum: 1
            maximum: 100
            default: 10
            example: 10
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTimeOffsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
    post:
      operationId: ''
      summary: Create your time off
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTimeOffDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTimeOffResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/time-off/{id}:
    patch:
      operationId: ''
      summary: Update your time off
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTimeOffDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTimeOffResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
    delete:
      operationId: ''
      summary: Delete your time off
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: number
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/skills:
    get:
      operationId: ''
      summary: Get all skills
      parameters:
        - name: page
          required: false
          in: query
          description: The page number to fetch skills by
          schema:
            minimum: 0
            default: 1
            example: 1
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of skills to fetch
          schema:
            minimum: 1
            maximum: 100
            default: 10
            example: 10
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTagsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/skills/{userId}:
    get:
      operationId: ''
      summary: Get all skills for a user
      parameters:
        - name: userId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTagsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/skills/{skillId}/detach/{userId}:
    patch:
      operationId: ''
      summary: Detach a skill from a user
      parameters:
        - name: skillId
          required: true
          in: path
          schema:
            type: string
        - name: userId
          required: true
          in: path
          schema:
            type: string
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/users/skills/{skillId}/attach/{userId}:
    patch:
      operationId: ''
      summary: Attach a skill to a user
      parameters:
        - name: skillId
          required: true
          in: path
          schema:
            type: string
        - name: userId
          required: true
          in: path
          schema:
            type: string
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Users
  /v1/teams:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTeamsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTeamDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTeamResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/teams/public:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTeamsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/teams/{teamId}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTeamResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTeamDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTeamResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
    delete:
      operationId: ''
      summary: Delete a team
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/teams/{teamId}/sub-teams:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTeamsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/teams/{teamId}/members:
    get:
      operationId: ''
      summary: Get all team members
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: searchQuery
          required: false
          in: query
          description: The search query to filter team members by.
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTeamMembersResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
    post:
      operationId: ''
      summary: Add a team member
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddTeamMemberDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamMemberResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/teams/{teamId}/members/{memberId}:
    delete:
      operationId: ''
      summary: Remove a team member
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: memberId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/teams/{teamId}/configurations:
    get:
      operationId: ''
      summary: Get team configurations
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamConfigurationsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
    patch:
      operationId: ''
      summary: Update team configurations
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTimezoneWorkingHoursDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTeamConfigurationsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/teams/{teamId}/routing:
    post:
      operationId: ''
      summary: Create a team routing rule
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRoutingRuleGroupDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTeamRoutingRuleResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
    get:
      operationId: ''
      summary: Get team routing
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTeamRoutingRulesResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/teams/{teamId}/routing/{ruleId}:
    patch:
      operationId: ''
      summary: Update team routing
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: ruleId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRoutingRuleGroupDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTeamRoutingRuleResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
    delete:
      operationId: ''
      summary: Delete a team routing rule
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: ruleId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Teams
  /v1/accounts:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: source
          required: false
          in: query
          description: The source of the accounts to find
          schema:
            example: manual
            type: string
        - name: status
          required: false
          in: query
          description: The identifier / value of the status attribute
          schema:
            example: STATUS123
            type: string
        - name: classification
          required: false
          in: query
          description: The identifier / value of the classification attribute
          schema:
            example: CLASSIFICATION123
            type: string
        - name: health
          required: false
          in: query
          description: The identifier / value of the health attribute
          schema:
            example: HEALTH123
            type: string
        - name: industry
          required: false
          in: query
          description: The identifier / value of the industry attribute
          schema:
            example: INDUSTRY123
            type: string
        - name: accountOwnerId
          required: false
          in: query
          description: The identifier of the account owner
          schema:
            example: USER123
            type: string
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of accounts to fetch
          schema:
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountWithCustomerContactsPaginatedResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/filter/primary-domains:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterAccountsByPrimaryDomainsDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/filter/ids:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterAccountsByIdsDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/{id}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    put:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/bulk:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: string
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AccountResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/account-custom-fields:
    get:
      operationId: ''
      summary: Get custom fields for an account
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomFieldResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/attributes:
    post:
      operationId: ''
      summary: Creates an account attribute value
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountAttributeValueDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountAttributeValueResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    get:
      operationId: ''
      summary: Gets all account attribute values
      parameters:
        - name: attribute
          required: true
          in: query
          description: Attribute type
          schema:
            example: account_status
            enum:
              - account_status
              - account_classification
              - account_health
              - account_industry
              - contact_type
              - activity_type
              - activity_status
              - note_type
              - task_type
              - task_status
              - task_priority
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountAttributeValueResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/attributes/{id}:
    put:
      operationId: ''
      summary: Updates an account attribute value
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountAttributeValueDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountAttributeValueResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    delete:
      operationId: ''
      summary: Deletes an account attribute value
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: forceDelete
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/relationships/types:
    post:
      operationId: ''
      summary: Create an account relationship type
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountRelationshipTypeDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountRelationshipTypeResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    get:
      operationId: ''
      summary: Get all account relationship types
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AccountRelationshipTypeResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/relationships/types/{id}:
    put:
      operationId: ''
      summary: Update an account relationship type
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountRelationshipTypeDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountRelationshipTypeResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    delete:
      operationId: ''
      summary: Delete an account relationship type
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/relationships:
    post:
      operationId: ''
      summary: Create an account relationship
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountRelationshipDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountRelationshipResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    get:
      operationId: ''
      summary: Get all account relationships
      parameters:
        - name: accountId
          required: false
          in: query
          description: Account ID
          schema:
            example: A123
            type: string
        - name: relationshipTypeId
          required: false
          in: query
          description: The identifier of the relationship type attribute
          schema:
            example: RELATIONSHIP_TYPE_PARENT
            type: string
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of relationships to fetch
          schema:
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountRelationshipPaginatedResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/relationships/{id}:
    put:
      operationId: ''
      summary: Update an account relationship
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountRelationshipDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountRelationshipResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    delete:
      operationId: ''
      summary: Delete an account relationship
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/contacts:
    post:
      operationId: ''
      summary: Create a customer contact
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerContactDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerContactResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    get:
      operationId: ''
      summary: Get all customer contacts
      parameters:
        - name: accountId
          required: false
          in: query
          description: Account ID of the customer contact
          schema:
            example: A123
            type: string
        - name: contactType
          required: false
          in: query
          description: Contact type of the customer contact to find. (Fetches all contacts satisfying other conditions if not provided)
          schema:
            example: CT123
            type: string
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of contacts to fetch
          schema:
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerContactPaginatedResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/contacts/bulk:
    post:
      operationId: ''
      summary: Bulk create customer contacts
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkCreateCustomerContactsDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerContactBulkResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/contacts/filter/ids:
    post:
      operationId: ''
      summary: Filter customer contacts by ids
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterCustomerContactsByIdsDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CustomerContactResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/contacts/{contactId}:
    put:
      operationId: ''
      summary: Update a customer contact
      parameters:
        - name: contactId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerContactDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerContactResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    delete:
      operationId: ''
      summary: Delete a customer contact
      parameters:
        - name: contactId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/contacts/search:
    get:
      operationId: ''
      summary: Search customer contacts
      parameters:
        - name: email
          required: true
          in: query
          description: The email of the customer contact to search
          schema:
            example: <EMAIL>
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CustomerContactResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/activities:
    get:
      operationId: ''
      summary: Fetches all account activities
      parameters:
        - name: accountId
          required: false
          in: query
          description: The identifier of the account to find activities for
          schema:
            example: A123
            type: string
        - name: type
          required: false
          in: query
          description: The identifier / value of the type of the activity
          schema:
            type: string
        - name: status
          required: false
          in: query
          description: The identifier / value of the status of the activity
          schema:
            type: string
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of activities to fetch
          schema:
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountActivityPaginatedResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    post:
      operationId: ''
      summary: Creates an account activity
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountActivityDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountActivityResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/activities/{activityId}:
    put:
      operationId: ''
      summary: Updates an account activity
      parameters:
        - name: activityId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountActivityDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountActivityResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    delete:
      operationId: ''
      summary: Deletes an account activity
      parameters:
        - name: activityId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/activities/{activityId}/attachments/{attachmentId}:
    delete:
      operationId: ''
      summary: Removes an attachment from an account activity
      parameters:
        - name: activityId
          required: true
          in: path
          schema:
            type: string
        - name: attachmentId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/notes:
    get:
      operationId: ''
      summary: Fetches all account notes by account ID or by note ID
      parameters:
        - name: accountId
          required: false
          in: query
          description: The identifier of the account to find notes for
          schema:
            example: A123
            type: string
        - name: type
          required: false
          in: query
          description: The identifier / value of the type attribute of the note
          schema:
            example: T123
            type: string
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of notes to fetch
          schema:
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountNotePaginatedResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    post:
      operationId: ''
      summary: Creates an account note
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountNoteDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountNoteResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/notes/{noteId}:
    put:
      operationId: ''
      summary: Updates an account note
      parameters:
        - name: noteId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountNoteDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountNoteResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    delete:
      operationId: ''
      summary: Deletes an account note
      parameters:
        - name: noteId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/notes/{noteId}/attachments/{attachmentId}:
    delete:
      operationId: ''
      summary: Removes an attachment from an account note
      parameters:
        - name: noteId
          required: true
          in: path
          schema:
            type: string
        - name: attachmentId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/tasks:
    get:
      operationId: ''
      summary: Fetches all account tasks by account ID or by task ID
      parameters:
        - name: accountId
          required: false
          in: query
          description: The identifier of the account to find tasks for
          schema:
            example: A123
            type: string
        - name: activityId
          required: false
          in: query
          description: The identifier of the activity to find tasks for
          schema:
            example: A123
            type: string
        - name: assigneeId
          required: false
          in: query
          description: The identifier of the assignee to find tasks for
          schema:
            example: U123
            type: string
        - name: type
          required: false
          in: query
          description: The identifier / value of the type attribute of the task
          schema:
            example: TASK_TYPE_TODO
            type: string
        - name: status
          required: false
          in: query
          description: The identifier / value of the status attribute of the task
          schema:
            example: TASK_STATUS_TODO
            type: string
        - name: priority
          required: false
          in: query
          description: The identifier / value of the priority attribute of the task
          schema:
            example: TASK_PRIORITY_HIGH
            type: string
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of tasks to fetch
          schema:
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountTaskPaginatedResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    post:
      operationId: ''
      summary: Creates an account task
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountTaskDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountTaskResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/tasks/{taskId}:
    put:
      operationId: ''
      summary: Updates an account task
      parameters:
        - name: taskId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountTaskDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountTaskResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
    delete:
      operationId: ''
      summary: Deletes an account task
      parameters:
        - name: taskId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/tasks/{taskId}/attachments/{attachmentId}:
    delete:
      operationId: ''
      summary: Removes an attachment from an account task
      parameters:
        - name: taskId
          required: true
          in: path
          schema:
            type: string
        - name: attachmentId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/accounts/contacts/ingest:
    post:
      operationId: ''
      summary: Ingest users
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IngestCustomerContactDTO'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Accounts
  /v1/activities/audit-logs:
    post:
      operationId: ''
      summary: Create an audit log entry
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAuditLogDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLogResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Activities
  /v1/activities/agent/audit-logs:
    get:
      operationId: ''
      summary: Get agent audit logs
      parameters:
        - name: entityType
          required: false
          in: query
          description: Entity type filter
          schema:
            enum:
              - ticket
              - user
              - organization
              - team
              - business_hours
              - comments
              - reactions
              - custom_fields
              - drafts
              - storage
              - tags
              - views
              - account
              - account_configuration
              - account_attribute_value
              - customer_contact
              - account_relationship_type
              - account_relationship
              - account_activity
              - account_note
              - account_task
            type: string
        - name: entityId
          required: false
          in: query
          description: Entity ID filter
          schema:
            type: string
        - name: op
          required: false
          in: query
          description: Operation type filter
          schema:
            enum:
              - info
              - created
              - updated
              - deleted
              - archived
              - restored
            type: string
        - name: teamId
          required: false
          in: query
          description: Team ID filter
          schema:
            type: string
        - name: page
          required: false
          in: query
          description: Page number for pagination
          schema:
            default: 1
            type: number
        - name: limit
          required: false
          in: query
          description: Number of items per page
          schema:
            default: 10
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuditLogResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Activities
    post:
      operationId: ''
      summary: Create an audit log entry for agent-studio
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAuditLogDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLogResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Activities
  /v1/activities/agent/audit-logs/{id}:
    get:
      operationId: ''
      summary: Get agent audit log by ID
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLogResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Activities
  /v1/custom-field:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomFieldDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomFieldResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom fields
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: limit
          required: true
          in: query
          schema:
            type: number
        - name: page
          required: true
          in: query
          schema:
            type: number
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: source
          required: true
          in: query
          schema:
            type: string
        - name: onlyTeamFields
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCustomFieldsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom fields
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomFieldDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchCustomFieldResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom fields
  /v1/custom-field/fetchByIds:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCustomFieldsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom fields
  /v1/custom-field/search:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: term
          required: true
          in: query
          schema:
            type: string
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: onlyTeamFields
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCustomFieldsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom fields
  /v1/custom-field/delete:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteCustomFieldDto'
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom fields
  /v1/custom-field/types:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCustomFieldTypesResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom fields
  /v1/custom-field/thena-restricted-fields:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: types
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom fields
  /storage/upload-file:
    post:
      operationId: uploadFile
      summary: Upload a single file
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: expiry
          required: false
          in: query
          description: Expiry format for the file if shouldExpire is true
          schema:
            enum:
              - 1d
              - 1w
              - 1m
              - 1y
            type: string
      responses:
        '201':
          description: ''
      tags:
        - Storage
  /storage/fetchDocumentByIdentifier:
    get:
      operationId: fetchDocumentByIdentifier
      summary: Get a file by its identifier
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: uid
          required: true
          in: query
          description: The unique identifier of the file
          schema:
            type: string
      responses:
        '200':
          description: ''
      tags:
        - Storage
  /storage/private/document/{fileName}/{storageId}/{userToken}:
    get:
      operationId: StorageController_getPrivateUrl
      summary: Get private URL for a file
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: fileName
          required: true
          in: path
          schema:
            type: string
        - name: storageId
          required: true
          in: path
          schema:
            type: string
        - name: userToken
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
      tags:
        - Storage
  /storage/deleteDocumentByIdentifier:
    delete:
      operationId: StorageController_deleteDocumentByIdentifier
      summary: Delete a file by its identifier
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: uid
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
      tags:
        - Storage
  /v1/custom-object:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomObjectDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomObjectResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom objects
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomObjectDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCustomObjectsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom objects
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: limit
          required: true
          in: query
          schema:
            type: number
        - name: page
          required: true
          in: query
          schema:
            type: number
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: onlyTeamObjects
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCustomObjectsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom objects
  /v1/custom-object/fetchByIds:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCustomObjectsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom objects
  /v1/custom-object/search:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: term
          required: true
          in: query
          schema:
            type: string
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: onlyTeamObjects
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCustomObjectsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom objects
  /v1/custom-object/{customObjectId}/fields:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: customObjectId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFieldsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom objects
  /v1/custom-object/{customObjectId}/fields/{fieldId}:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: customObjectId
          required: true
          in: path
          schema:
            type: string
        - name: fieldId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddFieldDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddFieldResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom objects
  /v1/custom-object/{customObjectId}/records:
    post:
      operationId: CustomObjectRecordsController_addRecord
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: customObjectId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateObjectRecordDto'
      responses:
        '201':
          description: ''
      tags:
        - Custom objects
    get:
      operationId: CustomObjectRecordsController_getAllRecords
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: customObjectId
          required: true
          in: path
          schema:
            type: string
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: limit
          required: true
          in: query
          schema:
            type: number
        - name: page
          required: true
          in: query
          schema:
            type: number
      responses:
        '200':
          description: ''
      tags:
        - Custom objects
  /v1/custom-object/{customObjectId}/records/{id}:
    patch:
      operationId: CustomObjectRecordsController_updateRecord
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: customObjectId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateObjectRecordDto'
      responses:
        '200':
          description: ''
      tags:
        - Custom objects
    delete:
      operationId: CustomObjectRecordsController_deleteRecord
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: customObjectId
          required: true
          in: path
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
      tags:
        - Custom objects
  /v1/custom-object/{customObjectId}/records/fetchByIds:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: customObjectId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllObjectRecordsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Custom objects
  /v1/tickets:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: false
          in: query
          description: The ID of the team to filter tickets by
          schema:
            example: T00M677SAK
            type: string
        - name: page
          required: false
          in: query
          description: The page number to fetch tickets by
          schema:
            minimum: 0
            default: 0
            example: 0
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of tickets to fetch
          schema:
            minimum: 1
            maximum: 100
            default: 10
            example: 10
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTicketsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    post:
      operationId: create
      summary: Create a new ticket
      description: |-
        Creates a new ticket in the system. You must provide a title, requestorEmail, and teamId. Optionally, you can specify fields such as description, accountId, assignedAgentId, priority, status, formId, customFieldValues, and more.

        - If a formId and customFieldValues are provided, the ticket will be associated with a form and its custom fields.
        - If assignedAgentId is provided, the ticket will be assigned to that agent.
        - If performRouting is true, the system will attempt to auto-assign the ticket based on routing rules.
        - Additional metadata, attachments, and AI-generated fields can also be included.

        Returns the created ticket object.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - description: Minimal required fields
                  example:
                    title: Need help with my account!
                    requestorEmail: <EMAIL>
                    teamId: team_12345
                - description: All possible fields
                  example:
                    title: Cannot access dashboard
                    requestorEmail: <EMAIL>
                    teamId: team_67890
                    description: I am unable to access the dashboard after login.
                    accountId: account_abcde
                    assignedAgentId: agent_123
                    assignedAgentEmail: <EMAIL>
                    dueDate: '2024-07-01T12:00:00.000Z'
                    submitterEmail: <EMAIL>
                    statusId: status_open
                    statusName: Open
                    priorityId: priority_high
                    priorityName: High
                    sentimentId: sentiment_positive
                    metadata:
                      source: web
                      campaign: summer2024
                    typeId: type_bug
                    isPrivate: false
                    source: web
                    aiGeneratedTitle: Login Issue
                    aiGeneratedSummary: User cannot access dashboard after login.
                    attachmentUrls:
                      - https://example.com/attachment1.png
                      - https://example.com/attachment2.pdf
                    customFieldValues:
                      - customFieldId: cf_001
                        data:
                          - value: Sample text
                        metadata:
                          required: true
                      - customFieldId: cf_002
                        data:
                          - value: Option A
                            id: opt_a
                          - value: Option B
                            id: opt_b
                    performRouting: true
                    formId: form_12345
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}:
    get:
      operationId: ''
      summary: Get a ticket
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    patch:
      operationId: ''
      summary: Update a ticket
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTicketBody'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/related:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: linked
          required: false
          in: query
          description: Whether to fetch linked tickets
          schema:
            type: boolean
        - name: subtickets
          required: false
          in: query
          description: Whether to fetch subtickets
          schema:
            type: boolean
        - name: duplicate
          required: false
          in: query
          description: Whether to fetch duplicate tickets
          schema:
            type: boolean
        - name: page
          required: false
          in: query
          description: The page number to fetch tickets by
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit of tickets to fetch
          schema:
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTicketsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/escalate:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EscalateTicketBody'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/assign:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignTicketBody'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/comment:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommentDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonCommentResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/comments:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of comments to fetch
          schema:
            type: number
        - name: commentType
          required: true
          in: query
          description: The type of comments to fetch
          schema:
            enum:
              - comment
              - note
            type: string
        - name: visibility
          required: true
          in: query
          description: The visibility of the comments to fetch
          schema:
            enum:
              - private
              - public
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCommentsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/sub-ticket:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarkOrCreateSubTicketBody'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/mark-duplicate:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarkDuplicateBody'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/link:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LinkTicketsBody'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/reassign-team:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignTeamToTicketBody'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/archive:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/unarchive:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/log:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TicketTimeLogDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketTimeLogResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/{id}/time-logs:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTicketTimeLogsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/ticket-types:
    get:
      operationId: TicketsController_getTicketFieldTypes
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: slaEnabled
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
      tags:
        - Tickets
  /v1/tickets/bulk:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTicketsBulkDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTicketsBulkResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTicketsBulkDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateTicketsBulkResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/bulk/archive:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ArchiveTicketsBulkDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTicketsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/bulk/bulk-delete:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteTicketsBulkDto'
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/type:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTicketTypesResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTicketTypeDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketTypeResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/type/{id}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketTypeResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTicketTypeDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketTypeResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/status:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTicketStatusesResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTicketStatusDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketStatusResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/status/{id}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TicketStatusResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    patch:
      operationId: ''
      summary: Update a ticket status
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTicketStatusDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketStatusResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: fallback
          required: true
          in: query
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/priority:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTicketPrioritiesResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTicketPriorityDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketPriorityResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/priority/{id}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketPriorityResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTicketPriorityDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTicketPriorityResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    delete:
      operationId: ''
      summary: Delete a custom ticket priority
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/draft:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDraftTicketDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftTicketResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Draft Tickets
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: page
          required: false
          in: query
          description: The page number to fetch tickets by
          schema:
            minimum: 1
            default: 1
            example: 1
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of tickets to fetch
          schema:
            minimum: 1
            maximum: 100
            default: 10
            example: 10
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftTicketResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Draft Tickets
  /v1/tickets/draft/{uid}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftTicketResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Draft Tickets
    put:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDraftTicketDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftTicketResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Draft Tickets
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftTicketResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Draft Tickets
  /v1/tickets/draft/{uid}/submit:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: uid
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DraftTicketResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Draft Tickets
  /v1/tickets/sentiment:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: query
          schema:
            type: string
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTicketSentimentDto'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/tickets/sentiment/{id}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTicketSentimentDto'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Ticket sentiment deleted successfully!
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tickets
  /v1/comments:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommentOnAnEntityDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommentResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Comments
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of comments to fetch
          schema:
            type: number
        - name: entityType
          required: true
          in: query
          description: The type of the entity
          schema:
            type: string
        - name: entityId
          required: true
          in: query
          description: The identifier of the entity
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCommentsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Comments
  /v1/comments/user-type:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCommentsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Comments
  /v1/comments/{commentId}/threads:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: commentId
          required: true
          in: path
          schema:
            type: string
        - name: page
          required: false
          in: query
          description: The page number
          schema:
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of comments to fetch
          schema:
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllCommentsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Comments
  /v1/comments/{commentId}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: commentId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommentResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Comments
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: commentId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCommentDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommentResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Comments
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: commentId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Comments
  /v1/reactions/emojis:
    get:
      operationId: ReactionsActionController_getEmojis
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: ''
      tags:
        - Reactions
  /v1/reactions/{commentId}:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: commentId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddReactionDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonReactionResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Reactions
  /v1/reactions/remove/{commentId}/{reactionName}:
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: commentId
          required: true
          in: path
          schema:
            type: string
        - name: reactionName
          required: true
          in: path
          schema:
            type: string
        - name: impersonatedUserEmail
          required: true
          in: query
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Reactions
  /v1/emojis/actions:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Emoji Actions
  /v1/emojis/actions/{teamId}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Emoji Actions
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmojiActionDTO'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Emoji Actions
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MapEmojisActionDTO'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Emoji Actions
  /v1/emojis/actions/{teamId}/{emojiActionId}:
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: emojiActionId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Emoji Actions
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
        - name: emojiActionId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmojiActionIndividual'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Emoji Actions
  /v1/emojis/actions/{teamId}/unmap:
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UnmapEmojiActionDTO'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Emoji Actions
  /v1/forms:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFormDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Forms
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: limit
          required: true
          in: query
          schema:
            type: number
        - name: page
          required: true
          in: query
          schema:
            type: number
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: onlyTeamForms
          required: true
          in: query
          schema:
            type: boolean
        - name: escalationForms
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllFormsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Forms
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFormDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Forms
  /v1/forms/fetchByIds:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: ids
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllFormsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Forms
  /v1/forms/search:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: term
          required: true
          in: query
          schema:
            type: string
        - name: teamId
          required: true
          in: query
          schema:
            type: string
        - name: onlyTeamForms
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllFormsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Forms
  /v1/forms/delete:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteFormsDto'
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Forms
  /v1/forms/order:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderFormsDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FormResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Forms
  /v1/teams/{teamUuid}/tags:
    get:
      operationId: ''
      summary: Get all tags for a particular team
      parameters:
        - name: teamUuid
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTagsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
    post:
      operationId: ''
      summary: Create a new tag for a particular team
      parameters:
        - name: teamUuid
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTagDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTagsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
  /v1/teams/{teamUuid}/tags/{tagUuid}:
    put:
      operationId: ''
      summary: Update a tag for a particular team
      parameters:
        - name: teamUuid
          required: true
          in: path
          schema:
            type: string
        - name: tagUuid
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTagDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateTagsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
    delete:
      operationId: ''
      summary: Remove a tag from a particular team
      parameters:
        - name: teamUuid
          required: true
          in: path
          schema:
            type: string
        - name: tagUuid
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteTagResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
  /v1/tickets/{ticketId}/tags:
    get:
      operationId: ''
      summary: Get all tags for a ticket
      parameters:
        - name: ticketId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTicketTagsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Ticket Tags
    post:
      operationId: ''
      summary: Tags successfully added to ticket
      parameters:
        - name: ticketId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddTagsDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTicketTagsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Ticket Tags
  /v1/tickets/{ticketId}/tags/{tagId}:
    delete:
      operationId: ''
      summary: Remove a tag from a ticket
      parameters:
        - name: ticketId
          required: true
          in: path
          schema:
            type: string
        - name: tagId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteTicketTagResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Ticket Tags
  /v1/tags:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTagDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTagResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: page
          required: false
          in: query
          description: The page number to fetch tickets by
          schema:
            minimum: 0
            default: 1
            example: 1
            type: number
        - name: limit
          required: false
          in: query
          description: The limit number of tickets to fetch
          schema:
            minimum: 1
            maximum: 100
            default: 10
            example: 10
            type: number
        - name: type
          required: false
          in: query
          description: The type of the tag
          schema:
            enum:
              - ticket
              - skills
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllTagsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
  /v1/tags/{tagId}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: tagId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTagResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: tagId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTagDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonTagResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: tagId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Tags
  /v1/views:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: teamId
          required: true
          in: query
          description: The unique identifier of the team to fetch views from
          schema:
            example: T00M677SAK
            type: string
        - name: page
          required: true
          in: query
          description: The page number to fetch views from
          schema:
            minimum: 1
            default: 1
            example: 1
            type: number
        - name: limit
          required: true
          in: query
          description: The limit number of views to fetch
          schema:
            minimum: 1
            maximum: 100
            default: 10
            example: 10
            type: number
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllViewsResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Views
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateViewBody'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonViewResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Views
  /v1/views/{id}:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonViewResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Views
    delete:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Views
    patch:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateViewBody'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonViewResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Views
  /v1/views/{id}/duplicate:
    post:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommonViewResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Views
  /v1/view-types:
    get:
      operationId: ''
      summary: ''
      description: This endpoint is only available for standard and enterprise tier organizations.
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllViewsTypesResponse'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Views
  /v1/notifications/subscriptions:
    post:
      operationId: ''
      summary: Subscribe to an entity
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscribeDto'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
    delete:
      operationId: ''
      summary: Unsubscribe from an entity
      parameters:
        - name: entityId
          required: true
          in: query
          description: The ID of the entity to subscribe to
          schema:
            example: '123'
            type: string
        - name: eventCategory
          required: true
          in: query
          description: The category of event to subscribe to
          schema:
            example: Ticket
            enum:
              - ticket
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
    get:
      operationId: ''
      summary: Get subscribers of an entity
      parameters:
        - name: entityId
          required: true
          in: query
          description: The ID of the entity
          schema:
            example: '123'
            type: string
        - name: eventCategory
          required: true
          in: query
          description: The type of entity to subscribe to
          schema:
            example: TICKET
            enum:
              - ticket
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SubscriberResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
  /v1/notifications/subscriptions/bulk:
    post:
      operationId: ''
      summary: Bulk subscribe users to an entity
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkSubscribeDto'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
    patch:
      operationId: ''
      summary: Bulk unsubscribe users from an entity
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkSubscribeDto'
      responses:
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
  /v1/notifications/subscriptions/preferences:
    post:
      operationId: ''
      summary: Update the subscription preferences
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationSubscriptionPreferenceRequestDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationSubscriptionPreferenceResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
    get:
      operationId: ''
      summary: Get all subscription preferences
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/NotificationSubscriptionPreferenceResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
  /v1/notifications/preferences/profiles:
    post:
      operationId: ''
      summary: Create notification preference profile
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserNotificationPreferenceProfileDto'
      responses:
        '201':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserNotificationPreferenceProfileResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
    get:
      operationId: ''
      summary: List notification preference profiles
      parameters: []
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserNotificationPreferenceProfileResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
  /v1/notifications/preferences/profiles/{profileId}:
    put:
      operationId: ''
      summary: Update notification preference profile
      parameters:
        - name: profileId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserNotificationPreferenceProfileDto'
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserNotificationPreferenceProfileResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
    delete:
      operationId: ''
      summary: Delete notification preference profile
      parameters:
        - name: profileId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: Resource deleted successfully
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
    get:
      operationId: ''
      summary: Get notification preference profile
      parameters:
        - name: profileId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Operation successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserNotificationPreferenceProfileResponseDto'
        '401':
          description: User is not authenticated!
        '403':
          description: User does not have access to this resource!
        '404':
          description: Resource not found!
        '429':
          description: Too many requests!
        '500':
          description: Something went wrong!
        '503':
          description: This service/resource is currently unavailable.
      tags:
        - Notifications
info:
  title: Thena Platform
  description: The Thena Platform API description
  version: 1.0.0
  contact: {}
tags: []
servers:
  - url: https://platform.thena.ai
    description: Platform
components:
  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: x-api-key
      description: 'Enter your API key '
  schemas:
    SignInDto:
      type: object
      properties: {}
    SignUpDto:
      type: object
      properties: {}
    BusinessSlotDto:
      type: object
      properties:
        start:
          type: string
          description: Start time in 24-hour format (HH:mm)
          example: '10:00'
        end:
          type: string
          description: End time in 24-hour format (HH:mm)
          example: '18:00'
      required:
        - start
        - end
    BusinessDayDto:
      type: object
      properties:
        isActive:
          type: boolean
          description: Whether the business day is active
        slots:
          description: The time slots for the business day
          type: array
          items:
            $ref: '#/components/schemas/BusinessSlotDto'
      required:
        - isActive
        - slots
    BusinessDays:
      type: object
      properties:
        monday:
          description: The business hours for Monday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        tuesday:
          description: The business hours for Tuesday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        wednesday:
          description: The business hours for Wednesday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        thursday:
          description: The business hours for Thursday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        friday:
          description: The business hours for Friday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        saturday:
          description: The business hours for Saturday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        sunday:
          description: The business hours for Sunday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
      required:
        - monday
        - tuesday
        - wednesday
        - thursday
        - friday
        - saturday
        - sunday
    UserConfigurationsResponseDto:
      type: object
      properties:
        timezone:
          type: string
          description: The team timezone
        userId:
          type: string
          description: Unique identifier for the user
        dailyConfig:
          description: The user's daily business hours config
          allOf:
            - $ref: '#/components/schemas/BusinessDays'
        commonDailyConfig:
          type: boolean
          description: Whether the common daily config is enabled
        createdAt:
          type: string
          description: The created date of the user configuration
        updatedAt:
          type: string
          description: The updated date of the user configuration
      required:
        - timezone
        - userId
        - dailyConfig
        - commonDailyConfig
        - createdAt
        - updatedAt
    CommonUserConfigurationsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete user configurations
          allOf:
            - $ref: '#/components/schemas/UserConfigurationsResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    UserResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The id of the time off
        name:
          type: string
          description: The name of the user
        email:
          type: string
          description: The email of the user
        userType:
          type: string
          description: The user type of the user
        status:
          type: string
          description: The status of the user
        isActive:
          type: boolean
          description: The is active of the user
        lastLoginAt:
          type: string
          description: The last login date of the user
        avatarUrl:
          type: string
          description: The avatar url of the user
        timezone:
          type: string
          description: The timezone of the user
        createdAt:
          type: string
          description: The created date of the time off
        updatedAt:
          type: string
          description: The updated date of the time off
      required:
        - id
        - name
        - email
        - userType
        - status
        - isActive
        - lastLoginAt
        - avatarUrl
        - timezone
        - createdAt
        - updatedAt
    UpdateUserDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the user
          example: John Doe
        avatarUrl:
          type: string
          description: The avatar url of the user
          example: https://example.com/avatar.png
    BusinessHoursConfigDto:
      type: object
      properties:
        monday:
          description: The business hours for Monday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        tuesday:
          description: The business hours for Tuesday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        wednesday:
          description: The business hours for Wednesday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        thursday:
          description: The business hours for Thursday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        friday:
          description: The business hours for Friday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        saturday:
          description: The business hours for Saturday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
        sunday:
          description: The business hours for Sunday
          allOf:
            - $ref: '#/components/schemas/BusinessDayDto'
      required:
        - monday
        - tuesday
        - wednesday
        - thursday
        - friday
        - saturday
        - sunday
    UpdateTimezoneWorkingHoursDto:
      type: object
      properties:
        timezone:
          type: string
          description: The timezone of the team
        routingRespectsTimezone:
          type: boolean
          description: Whether the routing rules respect the timezone for the teams
        routingRespectsUserCapacity:
          type: boolean
          description: Whether the routing rules respect the user capacity for the teams
        fallbackSubTeam:
          type: string
          description: The fallback sub team of the team
        holidays:
          description: The dates of the team's holidays
          example:
            - 25-12
            - 25-12-2024
          items:
            type: array
          type: array
        routingRespectsUserTimezone:
          type: boolean
          description: Whether the routing rules respect the user timezone for the teams
        routingRespectsUserAvailability:
          type: boolean
          description: Whether the routing rules respect the user availability for the teams
        userRoutingStrategy:
          type: string
          description: The user routing strategy for the team
          enum:
            - manual
            - round_robin
        commonDailyConfig:
          type: boolean
          description: Whether the team uses common daily config
        commonSlots:
          description: The common slots for the team
          example:
            - start: '09:00'
              end: '17:00'
          type: array
          items:
            type: string
        dailyConfig:
          description: The business hours of the team
          example:
            monday:
              isActive: true
              slots:
                - start: '09:00'
                  end: '17:00'
          allOf:
            - $ref: '#/components/schemas/BusinessHoursConfigDto'
    UpdateUserAvailabilityDto:
      type: object
      properties:
        availability:
          type: string
          description: The availability of the user
          example: ACTIVE
      required:
        - availability
    UpdateUserAvailabilityResponseDto:
      type: object
      properties:
        availability:
          type: string
          description: The availability of the user
          example: ACTIVE
      required:
        - availability
    UpdateUserAvailabilityResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for update user availability
          allOf:
            - $ref: '#/components/schemas/UpdateUserAvailabilityResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    UpdateUserSettingsDto:
      type: object
      properties: {}
    TimeOffResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The id of the time off
        startDate:
          type: string
          description: The start date of the time off in Zulu time
        endDate:
          type: string
          description: The end date of the time off in Zulu time
        description:
          type: string
          description: The description of the time off
        type:
          type: string
          description: The type of the time off
        createdAt:
          type: string
          description: The created date of the time off
        updatedAt:
          type: string
          description: The updated date of the time off
      required:
        - id
        - startDate
        - endDate
        - description
        - type
        - createdAt
        - updatedAt
    GetAllTimeOffsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The time offs fetched
          type: array
          items:
            $ref: '#/components/schemas/TimeOffResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CreateTimeOffDto:
      type: object
      properties:
        startDate:
          type: string
          description: The start date of the time off in Zulu time
          example: '2024-01-01T00:00:00.000Z'
        endDate:
          type: string
          description: The end date of the time off in Zulu time
          example: '2024-01-02T23:59:59.999Z'
        description:
          type: string
          description: The description of the time off
          example: I'm going to be on vacation
        type:
          type: string
          description: The type of the time off
          example: VACATION
      required:
        - startDate
        - endDate
        - type
    CommonTimeOffResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete time off operations
          allOf:
            - $ref: '#/components/schemas/TimeOffResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    UpdateTimeOffDto:
      type: object
      properties:
        startDate:
          type: string
          description: The start date of the time off in Zulu time
          example: '2024-01-01T00:00:00.000Z'
        endDate:
          type: string
          description: The end date of the time off in Zulu time
          example: '2024-01-02T23:59:59.999Z'
        description:
          type: string
          description: The description of the time off
          example: I'm going to be on vacation
        type:
          type: string
          description: The type of the time off
          example: VACATION
      required:
        - startDate
        - endDate
        - type
    TagsResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The public id of the tag
        name:
          type: string
          description: The name of the tag
        description:
          type: string
          description: The description of the tag
        color:
          type: string
          description: The color of the tag
        createdAt:
          type: string
          description: The creation date of the tag
        updatedAt:
          type: string
          description: The update date of the tag
      required:
        - id
        - name
        - color
        - createdAt
        - updatedAt
    GetAllTagsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The tags fetched
          type: array
          items:
            $ref: '#/components/schemas/TagsResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    TeamResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The ID of the team
        name:
          type: string
          description: The name of the team
        icon:
          type: string
          description: The icon of the team
        color:
          type: string
          description: The color of the team
        parentTeamId:
          type: string
          description: The parent team ID of the team
        parentTeamName:
          type: string
          description: The name of the parent team
        teamId:
          type: string
          description: The team ID of the team
        identifier:
          type: string
          description: The identifier of the team
        description:
          type: string
          description: The description of the team
        teamOwner:
          type: string
          description: The name of the team owner
        teamOwnerId:
          type: string
          description: The ID of the team owner
        fallbackSubTeam:
          type: string
          description: The fallback sub team
        createdAt:
          type: string
          description: The creation date of the team
        isActive:
          type: boolean
          description: Whether the team is active
        isPrivate:
          type: boolean
          description: Whether the team is private
        archivedAt:
          type: string
          description: The archived date of the team
        updatedAt:
          type: string
          description: The updated date of the team
      required:
        - id
        - name
        - teamId
        - identifier
        - teamOwner
        - teamOwnerId
        - fallbackSubTeam
        - createdAt
        - isActive
        - isPrivate
    GetAllTeamsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The teams fetched
          type: array
          items:
            $ref: '#/components/schemas/TeamResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CreateTeamDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the team
        description:
          type: string
          description: The description of the team
        icon:
          type: string
          description: The icon of the team
        color:
          type: string
          description: The color of the team
        identifier:
          type: string
          description: The identifier of the team
        parentTeamId:
          type: string
          description: The parent team ID of the team
        isPrivate:
          type: boolean
          description: Whether the team is private
      required:
        - name
    CommonTeamResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete team operations
          allOf:
            - $ref: '#/components/schemas/TeamResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    UpdateTeamDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the team
        identifier:
          type: string
          description: The identifier of the team
        icon:
          type: string
          description: The icon of the team
        color:
          type: string
          description: The color of the team
        description:
          type: string
          description: The description of the team
        isPrivate:
          type: boolean
          description: Whether the team is private
    TeamMemberResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The ID of the team member
        name:
          type: string
          description: The name of the team member
        email:
          type: string
          description: The email of the team member
        invitedBy:
          type: string
          description: The name of the user who invited the team member
        teamId:
          type: string
          description: The ID of the team
        teamName:
          type: string
          description: The name of the team
        isActive:
          type: boolean
          description: Whether the team member is active
        role:
          type: string
          description: The role of the team member
        isOwner:
          type: boolean
          description: Whether the team member is the owner of the team
        avatarUrl:
          type: string
          description: The avatar url of the team member
        joinedAt:
          type: string
          description: The joined date of the team member
        createdAt:
          type: string
          description: The created date of the team member
      required:
        - id
        - name
        - email
        - teamId
        - teamName
        - isActive
        - role
        - isOwner
        - avatarUrl
        - joinedAt
        - createdAt
    GetAllTeamMembersResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The team members fetched
          type: array
          items:
            $ref: '#/components/schemas/TeamMemberResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    AddTeamMemberDto:
      type: object
      properties:
        email:
          type: string
          description: The email address of the user to add to the team.
        userId:
          type: string
          description: The user ID of the user to add to the team.
        isAdmin:
          type: boolean
          description: Whether the user should be added as an admin to the team.
    TeamConfigurationsResponseDto:
      type: object
      properties:
        timezone:
          type: string
          description: The team timezone
        teamId:
          type: string
          description: Unique identifier for the team
        fallbackSubTeam:
          type: string
          description: The fallback sub team
        holidays:
          description: The team holidays
          type: array
          items:
            type: string
        routingRespectsTimezone:
          type: boolean
          description: Whether routing respects timezone
        routingRespectsUserTimezone:
          type: boolean
          description: Whether routing respects user timezone
        routingRespectsUserAvailability:
          type: boolean
          description: Whether routing respects user availability
        userRoutingStrategy:
          type: string
          description: The user routing strategy
        commonDailyConfig:
          type: boolean
          description: Whether the common daily business hours config is enabled
        dailyConfig:
          description: The team daily business hours config
          allOf:
            - $ref: '#/components/schemas/BusinessDays'
        createdAt:
          type: string
          description: The created date of the team configuration
        updatedAt:
          type: string
          description: The updated date of the team configuration
      required:
        - timezone
        - teamId
        - fallbackSubTeam
        - holidays
        - routingRespectsTimezone
        - routingRespectsUserTimezone
        - routingRespectsUserAvailability
        - userRoutingStrategy
        - commonDailyConfig
        - dailyConfig
        - createdAt
        - updatedAt
    TeamConfigurationsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The team configurations fetched
          type: array
          items:
            $ref: '#/components/schemas/TeamConfigurationsResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CommonTeamConfigurationsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete team configurations
          allOf:
            - $ref: '#/components/schemas/TeamConfigurationsResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CreateRoutingRuleGroupDto:
      type: object
      properties:
        description:
          type: string
          description: The description of the routing rule group
        evaluationOrder:
          type: number
          description: The evaluation order of the routing rule group
        name:
          type: string
          description: The name of the routing rule group
        resultTeamId:
          type: string
          description: The result team ID of the routing rule group
        andRules:
          description: The rules of the routing rule group
          type: array
          items:
            type: string
        orRules:
          description: The rules of the routing rule group
          type: array
          items:
            type: string
      required:
        - name
        - resultTeamId
    TeamRoutingRuleResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the team routing rule
        name:
          type: string
          description: The name of the team routing rule
        description:
          type: string
          description: The description of the team routing rule
        teamId:
          type: string
          description: The team ID of the team routing rule
        evaluationOrder:
          type: number
          description: The evaluation order of the team routing rule
        resultTeamId:
          type: string
          description: The result team ID of the team routing rule
        fallbackTeamId:
          type: string
          description: The fallback team ID of the team routing rule
        andRules:
          description: The AND rules of the team routing rule
          type: array
          items:
            type: string
        orRules:
          description: The OR rules of the team routing rule
          type: array
          items:
            type: string
        createdBy:
          type: string
          description: The created by of the team routing rule
        createdAt:
          type: string
          description: The created date of the team routing rule
        createdById:
          type: string
          description: The created by ID of the team routing rule
        updatedAt:
          type: string
          description: The updated date of the team routing rule
      required:
        - id
        - name
        - teamId
        - evaluationOrder
        - resultTeamId
        - fallbackTeamId
        - andRules
        - orRules
        - createdBy
        - createdAt
        - createdById
        - updatedAt
    CommonTeamRoutingRuleResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete team routing rule operations
          allOf:
            - $ref: '#/components/schemas/TeamRoutingRuleResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    UpdateRoutingRuleGroupDto:
      type: object
      properties:
        description:
          type: string
          description: The description of the routing rule group
        evaluationOrder:
          type: number
          description: The evaluation order of the routing rule group
        name:
          type: string
          description: The name of the routing rule group
        resultTeamId:
          type: string
          description: The result team ID of the routing rule group
        andRules:
          description: The rules of the routing rule group
          type: array
          items:
            type: string
        orRules:
          description: The rules of the routing rule group
          type: array
          items:
            type: string
    GetAllTeamRoutingRulesResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The team routing rules fetched
          type: array
          items:
            $ref: '#/components/schemas/TeamRoutingRuleResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    AccountWithCustomerContactsPaginatedResponseDto:
      type: object
      properties: {}
    FilterAccountsByPrimaryDomainsDto:
      type: object
      properties:
        primaryDomains:
          type: array
          items:
            type: string
      required:
        - primaryDomains
    AccountResponseDto:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier of the account
        name:
          type: string
          description: Name of the account
        description:
          type: string
          description: The description of the account
        source:
          type: string
          description: The source of the account
          example: hubspot
        logo:
          type: string
          description: The URL of the account logo
        statusId:
          type: string
          description: The identifier of the status of the account
        status:
          type: string
          description: The status of the account
        statusConfiguration:
          type: object
          description: The configuration of the status
        classificationId:
          type: string
          description: The identifier of the classification of the account
        classification:
          type: string
          description: The classification of the account
        classificationConfiguration:
          type: object
          description: The configuration of the classification
        healthId:
          type: string
          description: The identifier of the health attribute
        health:
          type: string
          description: The health of the account
        healthConfiguration:
          type: object
          description: The configuration of the health
        industryId:
          type: string
          description: The identifier of the industry attribute
        industry:
          type: string
          description: The industry of the account
        industryConfiguration:
          type: object
          description: The configuration of the industry
        primaryDomain:
          type: string
          description: Primary domain of the account
        secondaryDomain:
          type: string
          description: Secondary domain of the account
        accountOwner:
          type: string
          description: Name of the account owner
        accountOwnerId:
          type: string
          description: Unique identifier of the account owner
        accountOwnerEmail:
          type: string
          description: Email of the account owner
        accountOwnerAvatarUrl:
          type: string
          description: The avatar URL of the account owner
        annualRevenue:
          type: number
          description: Annual revenue of the account
        employees:
          type: number
          description: Number of employees of the account
        website:
          type: string
          description: Website of the account
        billingAddress:
          type: string
          description: Billing address of the account
        shippingAddress:
          type: string
          description: Shipping address of the account
        customFieldValues:
          description: The custom field values
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the account
        createdAt:
          type: string
          description: Creation date of the account
        updatedAt:
          type: string
          description: Last update date of the account
      required:
        - id
        - name
        - source
        - statusId
        - status
        - statusConfiguration
        - classificationId
        - classification
        - classificationConfiguration
        - healthId
        - health
        - healthConfiguration
        - industryId
        - industry
        - industryConfiguration
        - primaryDomain
        - customFieldValues
        - metadata
        - createdAt
        - updatedAt
    FilterAccountsByIdsDto:
      type: object
      properties:
        ids:
          type: array
          items:
            type: string
      required:
        - ids
    ExternalCustomFieldValuesDto:
      type: object
      properties: {}
    CreateAccountDto:
      type: object
      properties:
        description:
          type: string
          description: The description of the account
        source:
          type: string
          description: The source of the account
          example: hubspot
        accountOwnerId:
          type: string
          description: The user identifier of the account owner
          example: USER123
        logo:
          type: string
          description: The URL of the account logo
          example: https://example.com/logo.png
        status:
          type: string
          description: The identifier / value of the status attribute
          example: STATUS123
        classification:
          type: string
          description: The identifier / value of the classification attribute
          example: CLASSIFICATION123
        health:
          type: string
          description: The identifier / value of the health attribute
          example: HEALTH123
        industry:
          type: string
          description: The identifier / value of the industry attribute
          example: INDUSTRY123
        secondaryDomain:
          type: string
          description: The secondary domain of the account
          example: example.com
        annualRevenue:
          type: number
          description: The annual revenue of the account
          example: 1000000
        employees:
          type: number
          description: The number of employees of the account
          example: 100
        website:
          type: string
          description: The website of the account
          example: https://example.com
        billingAddress:
          type: string
          description: The billing address of the account
          example: 123 Main St, Town, State, USA 12345
        shippingAddress:
          type: string
          description: The shipping address of the account
          example: 123 Main St, Town, State, USA 12345
        customFieldValues:
          description: The custom field values of the account
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
        addExistingUsersToAccountContacts:
          type: boolean
          description: Whether to add existing users matching the email domain to account contacts (false by default)
          example: true
        metadata:
          type: object
          description: The metadata of the account
        name:
          type: string
          description: The name of the account
          example: Example
        primaryDomain:
          type: string
          description: The primary domain of the account
          example: example.com
      required:
        - name
        - primaryDomain
    UpdateAccountDto:
      type: object
      properties:
        description:
          type: string
          description: The description of the account
        source:
          type: string
          description: The source of the account
          example: hubspot
        accountOwnerId:
          type: string
          description: The user identifier of the account owner
          example: USER123
        logo:
          type: string
          description: The URL of the account logo
          example: https://example.com/logo.png
        status:
          type: string
          description: The identifier / value of the status attribute
          example: STATUS123
        classification:
          type: string
          description: The identifier / value of the classification attribute
          example: CLASSIFICATION123
        health:
          type: string
          description: The identifier / value of the health attribute
          example: HEALTH123
        industry:
          type: string
          description: The identifier / value of the industry attribute
          example: INDUSTRY123
        secondaryDomain:
          type: string
          description: The secondary domain of the account
          example: example.com
        annualRevenue:
          type: number
          description: The annual revenue of the account
          example: 1000000
        employees:
          type: number
          description: The number of employees of the account
          example: 100
        website:
          type: string
          description: The website of the account
          example: https://example.com
        billingAddress:
          type: string
          description: The billing address of the account
          example: 123 Main St, Town, State, USA 12345
        shippingAddress:
          type: string
          description: The shipping address of the account
          example: 123 Main St, Town, State, USA 12345
        customFieldValues:
          description: The custom field values of the account
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
        addExistingUsersToAccountContacts:
          type: boolean
          description: Whether to add existing users matching the email domain to account contacts (false by default)
          example: true
        metadata:
          type: object
          description: The metadata of the account
        name:
          type: string
          description: The name of the account
          example: Example
        primaryDomain:
          type: string
          description: The primary domain of the account
          example: example.com
    CustomFieldData:
      type: object
      properties:
        name:
          type: string
        uid:
          type: string
        organizationId:
          type: string
        source:
          type: string
          enum:
            - ticket
            - account
            - custom_object
            - customer_contact
        fieldType:
          type: string
          enum:
            - single_line
            - multi_line
            - rich_text
            - integer
            - decimal
            - currency
            - date
            - date_time
            - time
            - single_choice
            - multi_choice
            - radio_button
            - checkbox
            - email
            - phone_number
            - url
            - ip_address
            - regex
            - password
            - file_upload
            - calculated
            - lookup
            - address
            - coordinates
            - rating
            - toggle
            - boolean
        options:
          nullable: true
          type: array
          items:
            type: string
        metadata:
          type: object
          nullable: true
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        isActive:
          type: boolean
        teamId:
          type: string
        autoAddToAllForms:
          type: boolean
        hintText:
          type: string
        placeholderText:
          type: string
        mandatoryOnCreation:
          type: boolean
        mandatoryOnClose:
          type: boolean
        visibleToCustomer:
          type: boolean
        editableByCustomer:
          type: boolean
        defaultValue:
          type: string
      required:
        - name
        - uid
        - organizationId
        - source
        - fieldType
        - options
        - metadata
        - createdAt
        - updatedAt
        - isActive
        - teamId
        - autoAddToAllForms
        - hintText
        - placeholderText
        - mandatoryOnCreation
        - mandatoryOnClose
        - visibleToCustomer
        - editableByCustomer
        - defaultValue
    CustomFieldResponseDto:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CustomFieldData'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    CreateAccountAttributeValueDto:
      type: object
      properties:
        icon:
          type: string
          description: Hero icon name of the account attribute value
          example: camera
        color:
          type: string
          description: HEX color of the account attribute value
          example: '#000000'
        isClosed:
          type: boolean
          description: Whether the account attribute value is closed. Applicable only for task_status attribute type
          example: false
        metadata:
          type: object
          description: The metadata of the account attribute value
          example: {}
        attribute:
          type: string
          description: Attribute type
          example: account_status
          enum:
            - account_status
            - account_classification
            - account_health
            - account_industry
            - contact_type
            - activity_type
            - activity_status
            - note_type
            - task_type
            - task_status
            - task_priority
        value:
          type: string
          description: Attribute value
          example: PROSPECT
        isDefault:
          type: boolean
          description: Whether the value is default for the attribute
          example: true
      required:
        - attribute
        - value
    AccountAttributeValueResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the attribute value
        attribute:
          type: string
          description: Attribute type
          example: account_status
          enum:
            - account_status
            - account_classification
            - account_health
            - account_industry
            - contact_type
            - activity_type
            - activity_status
            - note_type
            - task_type
            - task_status
            - task_priority
        value:
          type: string
          description: Attribute value
          example: PROSPECT
        isDefault:
          type: boolean
          description: Whether this value is default for the attribute
        configuration:
          type: object
          description: Configuration of the attribute value
        createdAt:
          type: string
          description: The creation date of the attribute value
        updatedAt:
          type: string
          description: The update date of the attribute value
        metadata:
          type: object
          description: The metadata of the attribute value
      required:
        - id
        - attribute
        - value
        - isDefault
        - configuration
        - createdAt
        - updatedAt
        - metadata
    UpdateAccountAttributeValueDto:
      type: object
      properties:
        icon:
          type: string
          description: Hero icon name of the account attribute value
          example: camera
        color:
          type: string
          description: HEX color of the account attribute value
          example: '#000000'
        isClosed:
          type: boolean
          description: Whether the account attribute value is closed. Applicable only for task_status attribute type
          example: false
        metadata:
          type: object
          description: The metadata of the account attribute value
          example: {}
        value:
          type: string
          description: Attribute value
          example: PROSPECT
        isDefault:
          type: boolean
          description: Whether the attribute is default
          example: false
    CreateAccountRelationshipTypeDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the relationship type
          example: Parent
        inverseRelationshipId:
          type: string
          description: The inverse relationship type ID
          example: A123
        metadata:
          type: object
          description: The metadata of the relationship type
          example: {}
      required:
        - name
    AccountRelationshipTypeResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The identifier of the relationship type
          example: ART123
        name:
          type: string
          description: The name of the relationship type
          example: Parent
        inverseRelationshipId:
          type: string
          description: The identifier of the inverse relationship type
          example: ART124
        inverseRelationship:
          type: string
          description: The name of the inverse relationship type
          example: Subsidiary
        createdAt:
          type: string
          description: The creation date of the relationship type
        updatedAt:
          type: string
          description: Last update date of the relationship type
        metadata:
          type: object
          description: The metadata of the relationship type
      required:
        - id
        - name
        - inverseRelationshipId
        - inverseRelationship
        - createdAt
        - updatedAt
        - metadata
    UpdateAccountRelationshipTypeDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the relationship type
          example: Parent
        inverseRelationshipId:
          type: string
          description: The inverse relationship type ID
          example: A123
        metadata:
          type: object
          description: The metadata of the relationship type
          example: {}
    CreateAccountRelationshipDto:
      type: object
      properties:
        accountId:
          type: string
          description: Account ID
          example: A123
        relatedAccountId:
          type: string
          description: Related account ID
          example: A124
        relationshipType:
          type: string
          description: The identifier of the relationship type attribute
          example: RELATIONSHIP_TYPE_PARENT
        metadata:
          type: object
          description: The metadata of the relationship
          example: {}
      required:
        - accountId
        - relatedAccountId
        - relationshipType
    AccountRelationshipResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The identifier of the relationship
          example: AR123
        accountId:
          type: string
          description: The identifier of the primary account of the relationship
        account:
          type: string
          description: The name of the primary account of the relationship
        relatedAccountId:
          type: string
          description: The identifier of the related account of the relationship
        relatedAccount:
          type: string
          description: The name of the related account of the relationship
        relationshipType:
          description: Relationship type
          allOf:
            - $ref: '#/components/schemas/AccountRelationshipTypeResponseDto'
        createdAt:
          type: string
          description: The creation date of the relationship
        updatedAt:
          type: string
          description: Last update date of the relationship
        metadata:
          type: object
          description: The metadata of the relationship
      required:
        - id
        - accountId
        - account
        - relatedAccountId
        - relatedAccount
        - relationshipType
        - createdAt
        - updatedAt
        - metadata
    AccountRelationshipPaginatedResponseDto:
      type: object
      properties: {}
    UpdateAccountRelationshipDto:
      type: object
      properties:
        relationshipType:
          type: string
          description: The identifier of the relationship type attribute
          example: RELATIONSHIP_TYPE_PARENT
        metadata:
          type: object
          description: The metadata of the relationship
          example: {}
      required:
        - relationshipType
    CreateCustomerContactDto:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the customer contact
          example: John
        lastName:
          type: string
          description: Last name of the customer contact
          example: Doe
        email:
          type: string
          description: Email of the customer contact
          example: <EMAIL>
        phoneNumber:
          type: string
          description: Phone number of the customer contact with country code
          example: '+**********'
        contactType:
          type: string
          description: Contact type (UID of the contact type attribute value) of the customer contact to create. (Uses default contact type if not provided)
          example: CT123
        avatarUrl:
          type: string
          description: Avatar URL of the customer contact
          example: https://example.com/avatar.png
        accountIds:
          description: Account IDs of the customer contact
          example:
            - A123
            - A456
          type: array
          items:
            type: string
        customFieldValues:
          description: The custom field values of the customer contact
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
        metadata:
          type: object
          description: The metadata of the customer contact
      required:
        - firstName
        - email
    CustomerContactResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The identifier of the customer contact
        firstName:
          type: string
          description: The first name of the customer contact
        lastName:
          type: string
          description: The last name of the customer contact
        email:
          type: string
          description: The email of the customer contact
        phoneNumber:
          type: string
          description: The phone number of the customer contact
        avatarUrl:
          type: string
          description: The avatar URL of the customer contact
        accounts:
          description: The name of the account
          type: array
          items:
            type: string
        contactTypeId:
          type: string
          description: The identifier of the contact type
        contactType:
          type: string
          description: The name of the contact type
        customFieldValues:
          description: The custom field values
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the contact
        createdAt:
          type: string
          description: The creation date of the contact
        updatedAt:
          type: string
          description: The last update date of the contact
      required:
        - id
        - firstName
        - lastName
        - email
        - phoneNumber
        - avatarUrl
        - accounts
        - contactTypeId
        - contactType
        - customFieldValues
        - metadata
        - createdAt
        - updatedAt
    BulkCreateCustomerContactsDto:
      type: object
      properties:
        contacts:
          description: Details of customer contacts to create
          type: array
          items:
            type: string
        accountIds:
          description: Account ID of the customer contacts
          example: A123
          type: array
          items:
            type: string
        contactType:
          type: string
          description: Contact type of the customer contacts
          example: CT123
      required:
        - contacts
    CustomerContactBulkResponseDto:
      type: object
      properties:
        total:
          type: number
          description: The total number of contacts provided
        created:
          type: number
          description: The number of contacts created
        skipped:
          type: number
          description: The number of contacts skipped due to existing contacts with the same email.
      required:
        - total
        - created
        - skipped
    CustomerContactPaginatedResponseDto:
      type: object
      properties: {}
    FilterCustomerContactsByIdsDto:
      type: object
      properties:
        ids:
          description: The IDs of the customer contacts to filter
          example:
            - C123
            - C456
          type: array
          items:
            type: string
      required:
        - ids
    UpdateCustomerContactDto:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the customer contact
          example: John
        lastName:
          type: string
          description: Last name of the customer contact
          example: Doe
        avatarUrl:
          type: string
          description: Avatar URL of the customer contact
          example: https://example.com/avatar.png
        email:
          type: string
          description: Email of the customer contact
          example: <EMAIL>
        phoneNumber:
          type: string
          description: Phone number of the customer contact with country code
          example: '+**********'
        contactType:
          type: string
          description: Contact type of the customer contact
          example: CT123
        accountIds:
          description: Account IDs of the customer contact. This will replace the existing account IDs of the customer contact
          example:
            - A123
            - A456
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the customer contact
        customFieldValues:
          description: The custom field values of the customer contact
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
    AccountActivityPaginatedResponseDto:
      type: object
      properties: {}
    CreateAccountActivityDto:
      type: object
      properties:
        accountId:
          type: string
          description: The identifier of the account to create the activity for
          example: A123
        activityTimestamp:
          type: string
          description: The timestamp of the activity
          example: '2024-01-01T00:00:00Z'
        title:
          type: string
          description: The title of the activity
          example: Meeting with John Doe
        description:
          type: string
          description: The description of the activity
          example: Meeting with John Doe
        duration:
          type: number
          description: The duration of the activity in minutes
          example: 60
        location:
          type: string
          description: The location of the activity
          example: New York, NY
        type:
          type: string
          description: The identifier / value of the Type attribute of the activity
          example: T123
        status:
          type: string
          description: The identifier / value of the Status attribute of the activity
          example: S123
        participants:
          description: The identifiers of the participants of the activity
          example:
            - U123
            - U124
          type: array
          items:
            type: string
        attachmentUrls:
          description: The URLs of the attachments to attach to the note
          example:
            - https://example.com/attachment1.jpg
            - https://example.com/attachment2.jpg
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the activity
          example: {}
      required:
        - accountId
        - activityTimestamp
        - title
    AccountActivityResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The identifier of the activity
        accountId:
          type: string
          description: The identifier of the account
        account:
          type: string
          description: The name of the account
        activityTimestamp:
          type: string
          description: The timestamp of the activity
        duration:
          type: number
          description: The duration of the activity in minutes
        location:
          type: string
          description: The location of the activity
        title:
          type: string
          description: The title of the activity
        description:
          type: string
          description: The description of the activity
        type:
          type: string
          description: The type of the activity
        typeId:
          type: string
          description: The identifier of the type attribute
        typeConfiguration:
          type: object
          description: The configuration of the type attribute
        status:
          type: string
          description: The status of the activity
        statusId:
          type: string
          description: The identifier of the status attribute
        statusConfiguration:
          type: object
          description: The configuration of the status attribute
        participants:
          description: The participants of the activity
          type: array
          items:
            type: string
        creator:
          type: string
          description: The creator of the activity
        creatorId:
          type: string
          description: The identifier of the creator
        creatorEmail:
          type: string
          description: The email of the creator
        attachments:
          description: The attachments of the activity
          type: array
          items:
            type: string
        createdAt:
          type: string
          description: The creation date of the activity
        updatedAt:
          type: string
          description: The update date of the activity
        metadata:
          type: object
          description: The metadata of the activity
      required:
        - id
        - accountId
        - account
        - activityTimestamp
        - duration
        - location
        - title
        - description
        - type
        - typeId
        - typeConfiguration
        - status
        - statusId
        - statusConfiguration
        - participants
        - creator
        - creatorId
        - creatorEmail
        - attachments
        - createdAt
        - updatedAt
        - metadata
    UpdateAccountActivityDto:
      type: object
      properties:
        title:
          type: string
          description: The title of the activity
          example: Meeting with John Doe
        description:
          type: string
          description: The description of the activity
          example: Meeting with John Doe
        activityTimestamp:
          type: string
          description: The timestamp of the activity
          example: '2024-01-01T00:00:00Z'
        duration:
          type: number
          description: The duration of the activity in minutes
          example: 60
        location:
          type: string
          description: The location of the activity
          example: New York, NY
        type:
          type: string
          description: The identifier / value of the Type attribute of the activity
          example: T123
        status:
          type: string
          description: The identifier / value of the Status attribute of the activity
          example: S123
        participants:
          description: The identifiers of the participants of the activity
          example:
            - U123
            - U124
          type: array
          items:
            type: string
        attachmentUrls:
          description: The URLs of the attachments to attach to the note
          example:
            - https://example.com/attachment1.jpg
            - https://example.com/attachment2.jpg
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the activity
          example: {}
    AccountNotePaginatedResponseDto:
      type: object
      properties: {}
    CreateAccountNoteDto:
      type: object
      properties:
        accountId:
          type: string
          description: The identifier of the account to create the note for
          example: A123
        content:
          type: string
          description: The content of the note
          example: This is a note
        type:
          type: string
          description: The identifier / value of the Type attribute of the activity
          example: T123
        visibility:
          type: string
          description: The visibility of the note
          example: private
        attachmentUrls:
          description: The URLs of the attachments to attach to the note
          example:
            - https://example.com/attachment1.jpg
            - https://example.com/attachment2.jpg
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the note
      required:
        - accountId
        - content
    AccountNoteResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The identifier of the activity
        accountId:
          type: string
          description: The identifier of the account
        account:
          type: string
          description: The name of the account
        content:
          type: string
          description: The content of the note
        type:
          type: string
          description: The type of the note
        typeId:
          type: string
          description: The identifier of the type attribute
        typeConfiguration:
          type: object
          description: The configuration of the type
        visibility:
          type: string
          description: The visibility of the note
        attachments:
          description: The attachments of the note
          type: array
          items:
            type: string
        author:
          type: string
          description: The name of the author
        authorId:
          type: string
          description: The identifier of the author
        authorEmail:
          type: string
          description: The email of the author
        createdAt:
          type: string
          description: The timestamp of the note
        updatedAt:
          type: string
          description: The last updated timestamp of the note
        metadata:
          type: object
          description: The metadata of the note
      required:
        - id
        - accountId
        - account
        - content
        - type
        - typeId
        - typeConfiguration
        - visibility
        - attachments
        - author
        - authorId
        - authorEmail
        - createdAt
        - updatedAt
        - metadata
    UpdateAccountNoteDto:
      type: object
      properties:
        content:
          type: string
          description: The content of the note
          example: This is a note
        type:
          type: string
          description: The identifier / value of the type attribute of the note
          example: T123
        visibility:
          type: string
          description: The visibility of the note
          example: private
        attachmentUrls:
          description: The URLs of the attachments to attach to the note
          example:
            - https://example.com/attachment1.jpg
            - https://example.com/attachment2.jpg
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the note
          example: {}
    AccountTaskPaginatedResponseDto:
      type: object
      properties: {}
    CreateAccountTaskDto:
      type: object
      properties:
        accountId:
          type: string
          description: The identifier of the account to create the task for
          example: A123
        title:
          type: string
          description: The title of the task
          example: Task 1
        assigneeId:
          type: string
          description: The identifier of the assignee
          example: U123
        activityId:
          type: string
          description: The identifier of the activity to create the task for
          example: A123
        description:
          type: string
          description: The description of the task
          example: This is a task
        type:
          type: string
          description: The identifier / value of the type attribute of the task
          example: T123
        status:
          type: string
          description: "The identifier / value of the status attribute of the task\t"
          example: S123
        priority:
          type: string
          description: The identifier / value of the priority attribute of the task
          example: P123
        attachmentUrls:
          description: The URLs of the attachments to attach to the note
          example:
            - https://example.com/attachment1.jpg
            - https://example.com/attachment2.jpg
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the task
          example: {}
      required:
        - accountId
        - title
        - assigneeId
    AccountTaskResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The identifier of the task
        accountId:
          type: string
          description: The identifier of the account
        account:
          type: string
          description: The name of the account
        activityId:
          type: string
          description: The identifier of the activity
        title:
          type: string
          description: The title of the task
        description:
          type: string
          description: The description of the task
        assigneeId:
          type: string
          description: The identifier of the assignee
        type:
          type: string
          description: The type of the task
        typeId:
          type: string
          description: The identifier of the type
        typeConfiguration:
          type: object
          description: The configuration of the type
        status:
          type: string
          description: The status of the task
        statusId:
          type: string
          description: The identifier of the status
        statusConfiguration:
          type: object
          description: The configuration of the status
        priority:
          type: string
          description: The priority of the task
        priorityId:
          type: string
          description: The identifier of the priority
        priorityConfiguration:
          type: object
          description: The configuration of the priority
        attachments:
          description: The attachments of the task
          type: array
          items:
            type: string
        isActive:
          type: boolean
          description: Whether the task is active
        creator:
          type: string
          description: The creator of the task
        creatorId:
          type: string
          description: The identifier of the creator
        creatorEmail:
          type: string
          description: The email of the creator
        createdAt:
          type: string
          description: The creation date of the task
        updatedAt:
          type: string
          description: The update date of the task
        metadata:
          type: object
          description: The metadata of the task
      required:
        - id
        - accountId
        - account
        - activityId
        - title
        - description
        - assigneeId
        - type
        - typeId
        - typeConfiguration
        - status
        - statusId
        - statusConfiguration
        - priority
        - priorityId
        - priorityConfiguration
        - attachments
        - isActive
        - creator
        - creatorId
        - creatorEmail
        - createdAt
        - updatedAt
        - metadata
    UpdateAccountTaskDto:
      type: object
      properties:
        title:
          type: string
          description: The title of the task
          example: Task 1
        assigneeId:
          type: string
          description: The identifier of the assignee
          example: U123
        activityId:
          type: string
          description: Change the activity associated with the task
          example: A123
        description:
          type: string
          description: The description of the task
          example: This is a task
        type:
          type: string
          description: The identifier / value of the type attribute of the task
          example: T123
        status:
          type: string
          description: "The identifier / value of the status attribute of the task\t"
          example: S123
        priority:
          type: string
          description: The identifier / value of the priority attribute of the task
          example: P123
        attachmentUrls:
          description: The URLs of the attachments to attach to the note
          example:
            - https://example.com/attachment1.jpg
            - https://example.com/attachment2.jpg
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the task
          example: {}
    IngestCustomerContactDTO:
      type: object
      properties: {}
    CreateAuditLogDto:
      type: object
      properties:
        entityType:
          type: string
          description: Type of entity being audited
          enum:
            - ticket
            - user
            - organization
            - team
            - business_hours
            - comments
            - reactions
            - custom_fields
            - drafts
            - storage
            - tags
            - views
            - account
            - account_configuration
            - account_attribute_value
            - customer_contact
            - account_relationship_type
            - account_relationship
            - account_activity
            - account_note
            - account_task
        entityUid:
          type: string
          description: UID of the entity being audited
        op:
          type: string
          description: Operation performed on the entity
          enum:
            - info
            - created
            - updated
            - deleted
            - archived
            - restored
        visibility:
          type: string
          description: Visibility level of the audit log
          enum:
            - system
            - team
            - organization
        teamId:
          type: string
          description: Team ID associated with the audit log
        metadata:
          type: object
          description: Additional metadata for the audit log
        activity:
          type: string
          description: Main activity text
        description:
          type: string
          description: Detailed description of the activity
        source:
          type: string
          description: Source of the activity
        isAutomated:
          type: boolean
          description: Whether the activity was automated
          default: false
        organization:
          type: string
          description: Organization performing the activity
        activityPerformedBy:
          type: string
          description: User performing the activity
      required:
        - entityType
        - entityUid
        - op
        - visibility
        - activity
        - organization
        - activityPerformedBy
    AuditLogResponseDto:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the audit log
        entityType:
          type: string
          description: Type of entity being audited
          enum:
            - ticket
            - user
            - organization
            - team
            - business_hours
            - comments
            - reactions
            - custom_fields
            - drafts
            - storage
            - tags
            - views
            - account
            - account_configuration
            - account_attribute_value
            - customer_contact
            - account_relationship_type
            - account_relationship
            - account_activity
            - account_note
            - account_task
        entityId:
          type: string
          description: ID of the entity being audited
        op:
          type: string
          description: Operation performed on the entity
          enum:
            - info
            - created
            - updated
            - deleted
            - archived
            - restored
        visibility:
          type: string
          description: Visibility level of the audit log
          enum:
            - system
            - team
            - organization
        description:
          type: string
          description: Description of the activity
        activity:
          type: string
          description: Activity performed
        activityPerformedById:
          type: string
          description: ID of the user who performed the activity
        activityPerformedByName:
          type: string
          description: Name of the user who performed the activity
        teamId:
          type: string
          description: ID of the team associated with the audit log
        teamName:
          type: string
          description: Name of the team associated with the audit log
        organizationId:
          type: string
          description: ID of the organization associated with the audit log
        metadata:
          type: object
          description: Additional metadata for the audit log
        createdAt:
          format: date-time
          type: string
          description: Timestamp when the audit log was created
        updatedAt:
          format: date-time
          type: string
          description: Timestamp when the audit log was last updated
      required:
        - id
        - entityType
        - entityId
        - op
        - visibility
        - description
        - activity
        - activityPerformedById
        - activityPerformedByName
        - teamId
        - teamName
        - organizationId
        - metadata
        - createdAt
        - updatedAt
    CreateCustomFieldDto:
      type: object
      properties:
        isActive:
          type: boolean
          description: Whether the custom field is active
        description:
          type: string
          description: The description of the custom field
        name:
          type: string
          description: The name of the custom field
        source:
          type: string
          description: The source of the custom field
        fieldType:
          type: string
          description: The type of the custom field
        options:
          description: The options of the custom field
          type: array
          items:
            type: string
        metadata:
          type: object
          description: The metadata of the custom field
        placeholderText:
          type: string
          description: The placeholder text of the custom field
        hintText:
          type: string
          description: The hint text of the custom field
        mandatoryOnClose:
          type: boolean
          description: Whether the custom field is mandatory on close
        mandatoryOnCreation:
          type: boolean
          description: Whether the custom field is mandatory on creation
        visibleToCustomer:
          type: boolean
          description: Whether the custom field is visible to customer
        editableByCustomer:
          type: boolean
          description: Whether the custom field is editable by customer
        autoAddToAllForms:
          type: boolean
          description: Whether the custom field is auto added to all forms
        defaultValue:
          type: string
          description: The default value of the custom field
        teamId:
          type: string
          description: The team id of the custom field
        lookup:
          type: string
          description: The lookup object id or thena entity name
      required:
        - name
        - source
        - fieldType
    GetAllCustomFieldsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldData'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    UpdateFieldsDto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        isActive:
          type: boolean
        options:
          type: array
          items:
            type: string
        placeholderText:
          type: string
        hintText:
          type: string
        defaultValue:
          type: string
        regexForValidation:
          type: string
        mandatoryOnCreation:
          type: boolean
        mandatoryOnClose:
          type: boolean
        visibleToCustomer:
          type: boolean
        editableByCustomer:
          type: boolean
        autoAddToAllForms:
          type: boolean
    CustomFieldUpdateData:
      type: object
      properties:
        fieldId:
          type: string
        version:
          type: number
        updates:
          $ref: '#/components/schemas/UpdateFieldsDto'
      required:
        - fieldId
        - version
    UpdateCustomFieldDto:
      type: object
      properties:
        fields:
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldUpdateData'
      required:
        - fields
    BatchCustomFieldResponseDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CustomFieldData'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    DeleteFieldItemDto:
      type: object
      properties:
        fieldId:
          type: string
        version:
          type: number
      required:
        - fieldId
        - version
    DeleteCustomFieldDto:
      type: object
      properties:
        fields:
          type: array
          items:
            $ref: '#/components/schemas/DeleteFieldItemDto'
      required:
        - fields
    CustomFieldTypesData:
      type: object
      properties:
        text:
          type: array
          items:
            type: string
        numeric:
          type: array
          items:
            type: string
        choice:
          type: array
          items:
            type: string
        date:
          type: array
          items:
            type: string
        user:
          type: array
          items:
            type: string
        specialized:
          type: array
          items:
            type: string
        file:
          type: array
          items:
            type: string
        calculated:
          type: array
          items:
            type: string
        lookup:
          type: array
          items:
            type: string
        geographic:
          type: array
          items:
            type: string
        rating:
          type: array
          items:
            type: string
        toggle:
          type: array
          items:
            type: string
      required:
        - text
        - numeric
        - choice
        - date
        - user
        - specialized
        - file
        - calculated
        - lookup
        - geographic
        - rating
        - toggle
    GetAllCustomFieldTypesResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CustomFieldTypesData'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    CreateCustomObjectDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the custom object
        description:
          type: string
          description: The description of the custom object
        teamId:
          type: string
          description: The team id of the custom object
      required:
        - name
    CustomObjectResponseDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        organizationId:
          type: string
        teamId:
          type: string
      required:
        - id
        - name
        - description
        - organizationId
        - teamId
    CustomObjectUpdateData:
      type: object
      properties:
        name:
          type: string
          description: The updated name of the custom object
        description:
          type: string
          description: The updated description of the custom object
    UpdateCustomObjectMetaDTO:
      type: object
      properties:
        id:
          type: string
        version:
          type: number
        updates:
          $ref: '#/components/schemas/CustomObjectUpdateData'
      required:
        - id
        - version
        - updates
    UpdateCustomObjectDto:
      type: object
      properties:
        objects:
          type: array
          items:
            $ref: '#/components/schemas/UpdateCustomObjectMetaDTO'
      required:
        - objects
    GetAllCustomObjectsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CustomObjectResponseDto'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    CustomObjectFieldResponseDto:
      type: object
      properties:
        id:
          type: string
        relationshipType:
          type: string
        childObjectId:
          type: string
        childEntityId:
          type: string
        defaultValue:
          type: string
        createdOn:
          format: date-time
          type: string
        updatedOn:
          format: date-time
          type: string
      required:
        - id
        - relationshipType
        - childObjectId
        - childEntityId
        - defaultValue
        - createdOn
        - updatedOn
    GetFieldsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CustomObjectFieldResponseDto'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    AddFieldDto:
      type: object
      properties:
        fieldId:
          type: string
          description: The id of the field to add
        parentObjectId:
          type: string
          description: The id of the parent object to add
        relationshipType:
          type: string
          description: The relationship type of the field to add
        defaultValue:
          type: string
          description: The default value of the field to add
    AddFieldResponse:
      type: object
      properties:
        status:
          type: boolean
        message:
          type: string
      required:
        - status
        - message
    CreateObjectRecordDto:
      type: object
      properties:
        teamId:
          type: string
          description: The ID of the team
        customFieldValues:
          description: The custom field values
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
      required:
        - teamId
        - customFieldValues
    UpdateObjectRecordDto:
      type: object
      properties:
        customFieldValues:
          description: The custom field values
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
      required:
        - customFieldValues
    CustomObjectRecordResponseDto:
      type: object
      properties:
        id:
          type: string
        teamId:
          type: string
        version:
          type: number
        customFieldValues:
          type: array
          items:
            type: string
      required:
        - id
        - teamId
        - version
        - customFieldValues
    GetAllObjectRecordsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CustomObjectRecordResponseDto'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    TicketResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the ticket
        title:
          type: string
          description: The title of the ticket
        ticketId:
          type: number
          description: The ticket ID of the ticket
        description:
          type: string
          description: The description of the ticket
        source:
          type: string
          description: The source of the ticket
        accountId:
          type: string
          description: The account ID of the ticket
        status:
          type: string
          description: The status of the ticket
        statusId:
          type: string
          description: The status ID of the ticket
        priority:
          type: string
          description: The priority of the ticket
        priorityId:
          type: string
          description: The priority ID of the ticket
        storyPoints:
          type: number
          description: The story points of the ticket
        account:
          type: string
          description: The account of the ticket
        teamId:
          type: string
          description: The team ID of the ticket
        teamName:
          type: string
          description: The name of the team of the ticket
        teamIdentifier:
          type: string
          description: The identifier of the team of the ticket
        subTeamId:
          type: string
          description: The sub team ID of the ticket
        subTeamName:
          type: string
          description: The name of the sub team of the ticket
        subTeamIdentifier:
          type: string
          description: The identifier of the sub team of the ticket
        isPrivate:
          type: boolean
          description: Whether the ticket is private
        typeId:
          type: string
          description: The type ID of the ticket
        type:
          type: string
          description: The name of the type of the ticket
        assignedAgent:
          type: string
          description: The assigned agent of the ticket
        assignedAgentId:
          type: string
          description: The assigned agent ID of the ticket
        assignedAgentEmail:
          type: string
          description: The assigned agent email of the ticket
        requestorEmail:
          type: string
          description: The requestor email of the ticket
        customerContactId:
          type: string
          description: The customer contact ID of the ticket
        customerContactFirstName:
          type: string
          description: The customer contact first name of the ticket
        customerContactLastName:
          type: string
          description: The customer contact last name of the ticket
        customerContactEmail:
          type: string
          description: The customer contact email of the ticket
        submitterEmail:
          type: string
          description: The submitter email of the ticket
        customFieldValues:
          description: The custom field values
          type: array
          items:
            type: string
        deletedAt:
          type: string
          description: The deleted at date of the ticket
        archivedAt:
          type: string
          description: The archived at date of the ticket
        createdAt:
          type: string
          description: The created at date of the ticket
        updatedAt:
          type: string
          description: The updated at date of the ticket
        formId:
          type: string
          description: The form ID of the ticket
        aiGeneratedTitle:
          type: string
          description: The AI generated title of the ticket
        aiGeneratedSummary:
          type: string
          description: The AI generated summary of the ticket
        sentiment:
          type: string
          description: The sentiment of the ticket
        sentimentId:
          type: string
          description: The sentiment ID of the ticket
      required:
        - id
        - title
        - ticketId
        - description
        - source
        - accountId
        - status
        - statusId
        - priority
        - priorityId
        - storyPoints
        - account
        - teamId
        - teamName
        - teamIdentifier
        - subTeamId
        - subTeamName
        - subTeamIdentifier
        - isPrivate
        - typeId
        - type
        - assignedAgent
        - assignedAgentId
        - assignedAgentEmail
        - requestorEmail
        - customerContactId
        - customerContactFirstName
        - customerContactLastName
        - customerContactEmail
        - submitterEmail
        - customFieldValues
        - deletedAt
        - archivedAt
        - createdAt
        - updatedAt
        - formId
        - aiGeneratedTitle
        - aiGeneratedSummary
        - sentiment
        - sentimentId
    GetAllTicketsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The tickets fetched
          type: array
          items:
            $ref: '#/components/schemas/TicketResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CommonTicketResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete ticket operations
          allOf:
            - $ref: '#/components/schemas/TicketResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    UpdateTicketBody:
      type: object
      properties:
        assignedAgentId:
          type: string
          description: The ID of the assigned agent
        accountId:
          type: string
          description: The ID of the account
        assignedAgentEmail:
          type: string
          description: The email of the assigned agent
        description:
          type: string
          description: The description of the ticket
        dueDate:
          format: date-time
          type: string
          description: The due date of the ticket
        submitterEmail:
          type: string
          description: The email of the submitter
        statusId:
          type: string
          description: The ID of the status, status id if provided is used over status name
        statusName:
          type: string
          description: The name of the status to match against
        priorityId:
          type: string
          description: The ID of the priority, priority id if provided is used over priority name
        priorityName:
          type: string
          description: The name of the priority to match against
        sentimentId:
          type: string
          description: The ID of the sentiment
        metadata:
          type: object
          description: The metadata of the ticket
        typeId:
          type: string
          description: The ID of the type
        isPrivate:
          type: boolean
          description: Whether the ticket is private
        source:
          type: string
          description: The source of the ticket
        aiGeneratedTitle:
          type: string
          description: The AI generated title of the ticket
        aiGeneratedSummary:
          type: string
          description: The AI generated summary of the ticket
        attachmentUrls:
          description: The attachment URLs
          type: array
          items:
            type: string
        customFieldValues:
          description: The custom field values
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
        title:
          type: string
          description: The title of the ticket
        subTeamId:
          type: string
          description: The ID of the sub-team
        formId:
          type: string
          description: The ID of the form
    EscalateTicketBody:
      type: object
      properties:
        reason:
          type: string
          description: The reason for the escalation
        details:
          type: string
          description: The details of the escalation
        impact:
          type: string
          description: The impact of the escalation
      required:
        - reason
        - details
        - impact
    AssignTicketBody:
      type: object
      properties:
        assignedAgentId:
          type: string
          description: The ID of the agent to assign
      required:
        - assignedAgentId
    CreateCommentDto:
      type: object
      properties:
        content:
          type: string
          description: The content of the comment
        contentHtml:
          type: string
          description: The HTML content of the comment
        contentJson:
          type: string
          description: The JSON content of the comment
        threadName:
          type: string
          description: The name of the comment thread
        parentCommentId:
          type: string
          description: The parent comment ID
        commentVisibility:
          type: string
          description: The visibility of the comment
        commentType:
          type: string
          description: The type of the comment
        customerEmail:
          type: string
          description: The customer email of the comment
        metadata:
          type: object
          description: The metadata of the comment
        attachmentIds:
          description: The attachment IDs
          type: array
          items:
            type: string
        impersonatedUserEmail:
          type: string
          description: The impersonated user email
        impersonatedUserName:
          type: string
          description: The impersonated user name
        impersonatedUserAvatar:
          type: string
          description: The impersonated user avatar
    CommentResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the comment
        content:
          type: string
          description: The content of the comment
        contentHtml:
          type: string
          description: The HTML content of the comment
        contentJson:
          type: string
          description: The JSON content of the comment
        contentMarkdown:
          type: string
          description: The markdown content of the comment
        isEdited:
          type: boolean
          description: Indicates if the comment is edited
        threadName:
          type: string
          description: The name of the comment thread
        commentVisibility:
          type: string
          description: The visibility of the comment
        commentType:
          type: string
          description: The type of the comment
        isPinned:
          type: boolean
          description: Indicates if the comment is pinned
        sourceEmailId:
          type: string
          description: The source email ID of the comment
        metadata:
          type: object
          description: The metadata of the comment
        parentCommentId:
          type: string
          description: The parent comment ID of the comment
        createdAt:
          type: string
          description: The creation date of the comment
        updatedAt:
          type: string
          description: The update date of the comment
        author:
          type: string
          description: The author of the comment
        authorId:
          type: string
          description: The unique identifier of the author of the comment
        authorUserType:
          type: string
          description: The user type of the author of the comment
        customerContactId:
          type: string
          description: The customer contact ID of the author of the comment
        customerContactEmail:
          type: string
          description: The email of the customer contact of the author of the comment
        customerContactFirstName:
          type: string
          description: The first name of the customer contact of the author of the comment
        customerContactLastName:
          type: string
          description: The last name of the customer contact of the author of the comment
        customerContactAvatarUrl:
          type: string
          description: The avatar url of the customer contact of the author of the comment
        authorAvatarUrl:
          type: string
          description: The avatar url of the author of the comment
        impersonatedUserEmail:
          type: string
          description: The impersonated user email
        impersonatedUserName:
          type: string
          description: The impersonated user name
        impersonatedUserAvatar:
          type: string
          description: The impersonated user avatar
        attachments:
          description: The attachments of the comment
          type: array
          items:
            type: string
      required:
        - id
        - content
        - contentHtml
        - contentJson
        - contentMarkdown
        - isEdited
        - threadName
        - commentVisibility
        - commentType
        - isPinned
        - sourceEmailId
        - metadata
        - parentCommentId
        - createdAt
        - updatedAt
        - author
        - authorId
        - authorUserType
        - customerContactId
        - customerContactEmail
        - customerContactFirstName
        - customerContactLastName
        - customerContactAvatarUrl
        - authorAvatarUrl
        - impersonatedUserEmail
        - impersonatedUserName
        - impersonatedUserAvatar
        - attachments
    CommonCommentResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete comment operations
          allOf:
            - $ref: '#/components/schemas/CommentResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    GetAllCommentsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The comments fetched
          type: array
          items:
            $ref: '#/components/schemas/CommentResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    MarkOrCreateSubTicketBody:
      type: object
      properties:
        assignedAgentId:
          type: string
          description: The ID of the assigned agent
        accountId:
          type: string
          description: The ID of the account
        assignedAgentEmail:
          type: string
          description: The email of the assigned agent
        description:
          type: string
          description: The description of the ticket
        dueDate:
          format: date-time
          type: string
          description: The due date of the ticket
        submitterEmail:
          type: string
          description: The email of the submitter
        statusId:
          type: string
          description: The ID of the status, status id if provided is used over status name
        statusName:
          type: string
          description: The name of the status to match against
        priorityId:
          type: string
          description: The ID of the priority, priority id if provided is used over priority name
        priorityName:
          type: string
          description: The name of the priority to match against
        sentimentId:
          type: string
          description: The ID of the sentiment
        metadata:
          type: object
          description: The metadata of the ticket
        typeId:
          type: string
          description: The ID of the type
        isPrivate:
          type: boolean
          description: Whether the ticket is private
        source:
          type: string
          description: The source of the ticket
        aiGeneratedTitle:
          type: string
          description: The AI generated title of the ticket
        aiGeneratedSummary:
          type: string
          description: The AI generated summary of the ticket
        attachmentUrls:
          description: The attachment URLs
          type: array
          items:
            type: string
        customFieldValues:
          description: The custom field values
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
        parentTicketId:
          type: string
          description: The ID of the parent ticket
        subTicketId:
          type: string
          description: The ID of the ticket to mark as a sub-ticket
        title:
          type: string
          description: The title of the ticket
      required:
        - parentTicketId
    MarkDuplicateBody:
      type: object
      properties:
        duplicateTicketId:
          type: string
          description: The ID of the ticket to mark as duplicate
        duplicateOfTicketId:
          type: string
          description: The ID of the ticket that is being duplicated
      required:
        - duplicateTicketId
        - duplicateOfTicketId
    LinkTicketsBody:
      type: object
      properties:
        sourceTicketId:
          type: string
          description: The ID of the source ticket
        linkedTicketId:
          type: string
          description: The ID of the target ticket to link to
      required:
        - sourceTicketId
        - linkedTicketId
    AssignTeamToTicketBody:
      type: object
      properties:
        teamId:
          type: string
          description: The ID of the team to assign
      required:
        - teamId
    TicketTimeLogDto:
      type: object
      properties:
        description:
          type: string
          description: The description of the time log
          example: Worked on the ticket for 2 hours
        timeSpentMinutes:
          type: number
          description: The time spent in minutes
          example: 120
      required:
        - description
        - timeSpentMinutes
    TicketTimeLogResponseDto:
      type: object
      properties:
        ticketId:
          type: string
          description: Unique identifier of the ticket for which time is logged
        userId:
          type: string
          description: Unique identifier of the user who logged the time
        userName:
          type: string
          description: Name of the user who logged the time
        description:
          type: string
          description: Description of the time logged
        timeSpentMinutes:
          type: number
          description: Time spent in minutes
        createdAt:
          type: string
          description: The created date of the ticket time log
        updatedAt:
          type: string
          description: The updated date of the ticket time log
      required:
        - ticketId
        - userId
        - userName
        - description
        - timeSpentMinutes
        - createdAt
        - updatedAt
    CommonTicketTimeLogResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete ticket time log operations
          allOf:
            - $ref: '#/components/schemas/TicketTimeLogResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    GetAllTicketTimeLogsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The ticket time logs fetched
          type: array
          items:
            $ref: '#/components/schemas/TicketTimeLogResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CreateTicketBody:
      type: object
      properties:
        assignedAgentId:
          type: string
          description: The ID of the assigned agent
        accountId:
          type: string
          description: The ID of the account
        assignedAgentEmail:
          type: string
          description: The email of the assigned agent
        description:
          type: string
          description: The description of the ticket
        dueDate:
          format: date-time
          type: string
          description: The due date of the ticket
        submitterEmail:
          type: string
          description: The email of the submitter
        statusId:
          type: string
          description: The ID of the status, status id if provided is used over status name
        statusName:
          type: string
          description: The name of the status to match against
        priorityId:
          type: string
          description: The ID of the priority, priority id if provided is used over priority name
        priorityName:
          type: string
          description: The name of the priority to match against
        sentimentId:
          type: string
          description: The ID of the sentiment
        metadata:
          type: object
          description: The metadata of the ticket
        typeId:
          type: string
          description: The ID of the type
        isPrivate:
          type: boolean
          description: Whether the ticket is private
        source:
          type: string
          description: The source of the ticket
        aiGeneratedTitle:
          type: string
          description: The AI generated title of the ticket
        aiGeneratedSummary:
          type: string
          description: The AI generated summary of the ticket
        attachmentUrls:
          description: The attachment URLs
          type: array
          items:
            type: string
        customFieldValues:
          description: The custom field values
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
        title:
          type: string
          description: The title of the ticket
        requestorEmail:
          type: string
          description: The email of the requestor
        performRouting:
          type: boolean
          description: Whether to perform routing
        teamId:
          type: string
          description: The ID of the team
        formId:
          type: string
          description: The ID of the form
        commentContentHtml:
          type: string
          description: The content of the initial comment (HTML format)
        commentContent:
          type: string
          description: The content of the initial comment (plain text format)
        commentContentJson:
          type: string
          description: The content of the initial comment (JSON format)
        commentAttachmentIds:
          description: The attachment IDs for the initial comment
          type: array
          items:
            type: string
        commentMetadata:
          type: object
          description: The metadata for the initial comment
        commentImpersonatedUserEmail:
          type: string
          description: The email of the impersonated user for the initial comment
        commentImpersonatedUserName:
          type: string
          description: The name of the impersonated user for the initial comment
        commentImpersonatedUserAvatar:
          type: string
          description: The avatar URL of the impersonated user for the initial comment
      required:
        - title
        - requestorEmail
        - teamId
    CreateBulkTicketsOptions:
      type: object
      properties:
        stopOnError:
          type: boolean
          description: Whether to stop on error
    CreateTicketsBulkDto:
      type: object
      properties:
        tickets:
          description: The tickets to create
          type: array
          items:
            $ref: '#/components/schemas/CreateTicketBody'
        options:
          description: The options for creating tickets in bulk
          allOf:
            - $ref: '#/components/schemas/CreateBulkTicketsOptions'
      required:
        - tickets
    TicketBulkResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The ticket id
        title:
          type: string
          description: The ticket title
      required:
        - id
        - title
    GetAllTicketsBulkResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The tickets fetched
          type: array
          items:
            $ref: '#/components/schemas/TicketBulkResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    UpdateTicketsBulkDto:
      type: object
      properties:
        statusId:
          type: string
          description: The ID of the status
        typeId:
          type: string
          description: The ID of the type
        priorityId:
          type: string
          description: The ID of the priority
        assignedAgentId:
          type: string
          description: The ID of the assigned agent
        isPrivate:
          type: boolean
          description: Whether the ticket is private
        ticketIds:
          description: The IDs of the tickets to update
          type: array
          items:
            type: string
      required:
        - ticketIds
    SkippedTicketBulkResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The ticket id
        reason:
          type: string
          description: The reason for skipping the ticket
      required:
        - id
        - reason
    UpdateTicketsBulkResponseDto:
      type: object
      properties:
        updated:
          description: The tickets updated
          type: array
          items:
            $ref: '#/components/schemas/TicketBulkResponseDto'
        skipped:
          description: The tickets skipped
          type: array
          items:
            $ref: '#/components/schemas/SkippedTicketBulkResponseDto'
      required:
        - updated
        - skipped
    UpdateTicketsBulkResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The tickets updated
          allOf:
            - $ref: '#/components/schemas/UpdateTicketsBulkResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    ArchiveTicketsBulkDto:
      type: object
      properties:
        ticketIds:
          description: The IDs of the tickets to archive
          type: array
          items:
            type: string
      required:
        - ticketIds
    DeleteTicketsBulkDto:
      type: object
      properties:
        ticketIds:
          description: The IDs of the tickets to delete
          type: array
          items:
            type: string
      required:
        - ticketIds
    TicketTypeResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the ticket
        name:
          type: string
          description: The name of the ticket type
        color:
          type: string
          description: The color of the ticket type
        icon:
          type: string
          description: The icon of the ticket type
        isActive:
          type: boolean
          description: Whether the ticket type is active
        teamId:
          type: string
          description: The team ID of the ticket type
        organizationId:
          type: string
          description: The organization ID of the ticket type
        autoAssign:
          type: boolean
          description: Whether the ticket type is auto-assigned
        createdAt:
          type: string
          description: The created date of the ticket type
        updatedAt:
          type: string
          description: The updated date of the ticket type
      required:
        - id
        - name
        - color
        - icon
        - isActive
        - teamId
        - organizationId
        - autoAssign
        - createdAt
        - updatedAt
    GetAllTicketTypesResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The ticket types fetched
          type: array
          items:
            $ref: '#/components/schemas/TicketTypeResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CommonTicketTypeResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete ticket type operations
          allOf:
            - $ref: '#/components/schemas/TicketTypeResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CreateTicketTypeDto:
      type: object
      properties:
        color:
          type: string
          description: The color of the ticket type
          example: '#000000'
        icon:
          type: string
          description: Link to the icon of the ticket type
        autoAssign:
          type: boolean
          description: Whether to auto assign the ticket type
          example: true
        isActive:
          type: boolean
          description: Whether the ticket type is active
          example: true
        name:
          type: string
          description: The name of the ticket type
          example: Bug
        teamId:
          type: string
          description: The team id of the ticket type
          example: team_123
      required:
        - name
        - teamId
    UpdateTicketTypeDto:
      type: object
      properties:
        color:
          type: string
          description: The color of the ticket type
          example: '#000000'
        icon:
          type: string
          description: Link to the icon of the ticket type
        autoAssign:
          type: boolean
          description: Whether to auto assign the ticket type
          example: true
        isActive:
          type: boolean
          description: Whether the ticket type is active
          example: true
        name:
          type: string
          description: The name of the ticket type
          example: Bug
    TicketStatusResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the ticket
        name:
          type: string
          description: The name of the ticket status
        displayName:
          type: string
          description: The display name of the ticket status
        description:
          type: string
          description: The description of the ticket status
        isDefault:
          type: boolean
          description: Whether the ticket status is default
        teamId:
          type: string
          description: The team ID of the ticket status
        organizationId:
          type: string
          description: The organization ID of the ticket status
        parentStatusId:
          type: string
          description: The parent status ID of the ticket status
        createdAt:
          type: string
          description: The created date of the ticket status
        updatedAt:
          type: string
          description: The updated date of the ticket status
      required:
        - id
        - name
        - displayName
        - description
        - isDefault
        - teamId
        - organizationId
        - createdAt
        - updatedAt
    GetAllTicketStatusesResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The ticket statuses fetched
          type: array
          items:
            $ref: '#/components/schemas/TicketStatusResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CreateTicketStatusDto:
      type: object
      properties:
        displayName:
          type: string
          description: The display name of the ticket status
        description:
          type: string
          description: The description of the ticket status
        isDefault:
          type: boolean
          description: Whether the ticket status is default
        name:
          type: string
          description: The name of the ticket status
        teamId:
          type: string
          description: The team ID of the ticket status
        parentStatusId:
          type: string
          description: The parent status ID of the ticket status
        moveParentTickets:
          type: boolean
          description: Whether to move the parent tickets to the new status
      required:
        - name
        - teamId
        - parentStatusId
    CommonTicketStatusResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete ticket status operations
          allOf:
            - $ref: '#/components/schemas/TicketStatusResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    UpdateTicketStatusDto:
      type: object
      properties:
        displayName:
          type: string
          description: The display name of the ticket status
        description:
          type: string
          description: The description of the ticket status
        isDefault:
          type: boolean
          description: Whether the ticket status is default
        name:
          type: string
          description: The name of the ticket status
    TicketPriorityResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the ticket priority
        name:
          type: string
          description: The name of the ticket priority
        displayName:
          type: string
          description: The display name of the ticket priority
        description:
          type: string
          description: The description of the ticket priority
        teamId:
          type: string
          description: The team ID of the ticket priority
        organizationId:
          type: string
          description: The organization ID of the ticket priority
        isDefault:
          type: boolean
          description: Whether the ticket priority is default
        createdAt:
          type: string
          description: The created date of the ticket priority
        updatedAt:
          type: string
          description: The updated date of the ticket priority
      required:
        - id
        - name
        - displayName
        - description
        - teamId
        - organizationId
        - isDefault
        - createdAt
        - updatedAt
    GetAllTicketPrioritiesResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The tickets priorities fetched
          type: array
          items:
            $ref: '#/components/schemas/TicketPriorityResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CommonTicketPriorityResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete ticket priority operations
          allOf:
            - $ref: '#/components/schemas/TicketPriorityResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CreateTicketPriorityDto:
      type: object
      properties:
        displayName:
          type: string
          description: The display name of the ticket priority
          example: High
        description:
          type: string
          description: The description of the ticket priority
          example: This is a high priority ticket
        isDefault:
          type: boolean
          description: Whether the ticket priority is the default one
          example: true
        name:
          type: string
          description: The name of the ticket priority
          example: High
        teamId:
          type: string
          description: The ID of the team to which the ticket priority belongs
          example: 123e4567-e89b-12d3-a456-426614174000
      required:
        - name
        - teamId
    UpdateTicketPriorityDto:
      type: object
      properties:
        displayName:
          type: string
          description: The display name of the ticket priority
          example: High
        description:
          type: string
          description: The description of the ticket priority
          example: This is a high priority ticket
        isDefault:
          type: boolean
          description: Whether the ticket priority is the default one
          example: true
        name:
          type: string
          description: The name of the ticket priority
          example: High
    CreateDraftTicketDto:
      type: object
      properties:
        assignedAgentId:
          type: string
          description: The ID of the assigned agent
        description:
          type: string
          description: The description of the ticket
        submitterEmail:
          type: string
          description: The email of the submitter
        statusId:
          type: string
          description: The ID of the status
        priorityId:
          type: string
          description: The ID of the priority
        typeId:
          type: string
          description: The ID of the type
        isPrivate:
          type: boolean
          description: Whether the ticket is private
        attachmentUrls:
          description: The attachment URLs
          type: array
          items:
            type: string
        customFieldValues:
          description: The custom field values
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
        metadata:
          type: object
          description: Additional metadata for the draft
        title:
          type: string
          description: The title of the ticket
        requestorEmail:
          type: string
          description: The email of the requestor
        accountId:
          type: string
          description: The ID of the account
        teamId:
          type: string
          description: The ID of the team
        draftScope:
          type: string
          description: The scope of the draft
          enum:
            - personal
            - team
          default: personal
      required:
        - title
        - requestorEmail
        - teamId
        - draftScope
    DraftTicketResponseDto:
      type: object
      properties:
        uid:
          type: string
        status:
          type: string
        draftScope:
          type: string
        entityType:
          type: string
        content:
          type: object
        metadata:
          type: object
        createdBy:
          type: string
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        lastModifiedBy:
          type: string
      required:
        - uid
        - status
        - draftScope
        - entityType
        - content
        - metadata
        - createdBy
        - createdAt
        - updatedAt
    UpdateDraftTicketDto:
      type: object
      properties:
        assignedAgentId:
          type: string
          description: The ID of the assigned agent
        description:
          type: string
          description: The description of the ticket
        submitterEmail:
          type: string
          description: The email of the submitter
        statusId:
          type: string
          description: The ID of the status
        priorityId:
          type: string
          description: The ID of the priority
        typeId:
          type: string
          description: The ID of the type
        isPrivate:
          type: boolean
          description: Whether the ticket is private
        attachmentUrls:
          description: The attachment URLs
          type: array
          items:
            type: string
        customFieldValues:
          description: The custom field values
          type: array
          items:
            $ref: '#/components/schemas/ExternalCustomFieldValuesDto'
        metadata:
          type: object
          description: Additional metadata for the draft
        title:
          type: string
          description: The title of the ticket
        requestorEmail:
          type: string
          description: The email of the requestor
        status:
          type: string
          description: The status of the draft
          enum:
            - in_progress
            - ready_to_publish
            - published
            - discarded
    CreateTicketSentimentDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the ticket sentiment
        icon:
          type: string
          description: The icon of the ticket sentiment
        description:
          type: string
          description: The description of the ticket sentiment
        isDefault:
          type: boolean
          description: Whether the ticket sentiment is default
        teamId:
          type: string
          description: The team ID of the ticket sentiment
      required:
        - teamId
    UpdateTicketSentimentDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the ticket sentiment
        icon:
          type: string
          description: The icon of the ticket sentiment
        description:
          type: string
          description: The description of the ticket sentiment
        isDefault:
          type: boolean
          description: Whether the ticket sentiment is default
    CreateCommentOnAnEntityDto:
      type: object
      properties:
        content:
          type: string
          description: The content of the comment
        contentHtml:
          type: string
          description: The HTML content of the comment
        contentJson:
          type: string
          description: The JSON content of the comment
        threadName:
          type: string
          description: The name of the comment thread
        entityType:
          type: string
          description: The type of the entity
        entityId:
          type: string
          description: The identifier of the entity
        parentCommentId:
          type: string
          description: The parent comment ID
        commentVisibility:
          type: string
          description: The visibility of the comment
        commentType:
          type: string
          description: The type of the comment
        metadata:
          type: object
          description: The metadata of the comment
        attachmentIds:
          description: The attachment IDs
          type: array
          items:
            type: string
        commentAs:
          type: string
          description: The comment as a string
        customerEmail:
          type: string
          description: The customer contact's email address
      required:
        - entityType
        - entityId
    UpdateCommentDto:
      type: object
      properties:
        content:
          type: string
          description: The content of the comment
          example: This is a comment
          minLength: 1
          maxLength: 5000
        contentHtml:
          type: string
          description: The HTML content of the comment
        contentJson:
          type: string
          description: The JSON content of the comment
        threadName:
          type: string
          description: The name of the comment thread
        commentVisibility:
          type: string
          description: The visibility of the comment
        commentType:
          type: string
          description: The type of the comment
        isPinned:
          type: boolean
          description: Whether the comment is pinned
        preserveMentions:
          type: boolean
          description: Whether to preserve mentions when updating content
        attachments:
          description: The attachments of the comment
          example:
            - attachment1
            - attachment2
          type: array
          items:
            type: string
        commentAs:
          type: string
          description: The comment as a string
        metadata:
          type: object
          description: The metadata of the comment
      required:
        - content
        - attachments
    AddReactionDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the reaction
          example: heart
        impersonatedUserEmail:
          type: string
          description: The impersonated user email
        impersonatedUserName:
          type: string
          description: The impersonated user name
        impersonatedUserAvatar:
          type: string
          description: The impersonated user avatar
        metadata:
          type: object
          description: The metadata for the reaction
      required:
        - name
    CommonReactionResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          type: boolean
          description: The response for create/update/delete reaction operations
      required:
        - status
        - message
        - timestamp
        - data
    UpdateEmojiActionIndividual:
      type: object
      properties:
        emoji:
          type: string
          description: Emoji to map against the action
          example: white_check_mark
        action:
          type: string
          description: Action to map against the emoji
          example: create_ticket
      required:
        - emoji
        - action
    UpdateEmojiActionDTO:
      type: object
      properties:
        action:
          type: string
          description: Action to map against the emojis
          example: create_ticket
        emojis:
          description: Emojis to map against the action
          example:
            - white_check_mark
          type: array
          items:
            type: string
      required:
        - action
        - emojis
    UnmapEmojiActionDTO:
      type: object
      properties:
        emoji:
          type: string
          description: Emoji to unmap from the action
          example: white_check_mark
        flow:
          type: string
          description: Flow to unmap from the emoji
          example: OUTBOUND
        action:
          type: string
          description: Action to unmap from the emoji
          example: create_ticket
      required:
        - emoji
        - flow
        - action
    MapEmojisActionDTO:
      type: object
      properties:
        emojis:
          description: Emojis to map against the action
          example:
            - white_check_mark
          type: array
          items:
            type: string
        flow:
          type: string
          description: Flow to map against the emojis
          example: OUTBOUND
        action:
          type: string
          description: Action to map against the emojis
          example: create_ticket
      required:
        - emojis
        - flow
        - action
    TargetFieldOption:
      type: object
      properties: {}
    TargetField:
      type: object
      properties:
        id:
          type: string
          description: The ID of the target field
        type:
          type: string
          description: The type of the target field
        value:
          type: object
          description: The value of the target field
        options:
          description: The options of the target field
          type: array
          items:
            $ref: '#/components/schemas/TargetFieldOption'
    Condition:
      type: object
      properties:
        triggerFieldId:
          type: string
          description: The ID of the trigger field
        triggerFieldValue:
          type: object
          description: The value of the trigger field
        conditionType:
          type: string
          description: The type of the condition
        targetFields:
          description: The target fields
          type: array
          items:
            $ref: '#/components/schemas/TargetField'
        forVendor:
          type: boolean
          description: Indicates if the form is for vendor
        forUser:
          type: boolean
          description: Indicates if the form is for user
        teamId:
          type: string
          description: The ID of the team
      required:
        - triggerFieldId
        - triggerFieldValue
        - conditionType
        - targetFields
    FormFieldDto:
      type: object
      properties:
        field:
          type: string
          description: The field
        defaultValue:
          type: object
          description: The default value
        mandatoryOnCreation:
          type: boolean
          description: Indicates if the field is mandatory on creation
        mandatoryOnClose:
          type: boolean
          description: Indicates if the field is mandatory on close
        visibleToCustomer:
          type: boolean
          description: Indicates if the field is visible to customer
        editableByCustomer:
          type: boolean
          description: Indicates if the field is editable by customer
      required:
        - field
        - editableByCustomer
    CreateFormDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the form
        description:
          type: string
          description: The description of the form
        conditions:
          description: Conditions of the form
          type: array
          items:
            $ref: '#/components/schemas/Condition'
        fields:
          description: Fields of the form
          type: array
          items:
            $ref: '#/components/schemas/FormFieldDto'
        isActive:
          type: boolean
          description: Indicates if the form is active
        channels:
          description: Channels of the form
          type: array
          items:
            type: string
        teamId:
          type: string
          description: The ID of the team
      required:
        - name
    FormResponse:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        fields:
          type: array
          items:
            $ref: '#/components/schemas/FormFieldDto'
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Condition'
        version:
          type: number
        isActive:
          type: boolean
        default:
          type: boolean
        createdAt:
          format: date-time
          type: string
        updatedAt:
          format: date-time
          type: string
        channels:
          type: array
          items:
            type: string
        teamId:
          type: string
        order:
          type: number
      required:
        - id
        - name
        - description
        - fields
        - conditions
        - version
        - isActive
        - default
        - createdAt
        - updatedAt
        - channels
        - teamId
        - order
    FormResponseDto:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/FormResponse'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    FormData:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        fields:
          type: array
          items:
            $ref: '#/components/schemas/FormFieldDto'
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Condition'
        version:
          type: number
        isActive:
          type: boolean
        default:
          type: boolean
        channels:
          type: array
          items:
            type: string
        order:
          type: number
      required:
        - id
        - name
        - description
        - fields
        - conditions
        - version
        - isActive
        - default
        - channels
    GetAllFormsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/FormData'
        status:
          type: boolean
        message:
          type: string
        timestamp:
          format: date-time
          type: string
      required:
        - data
        - status
        - message
        - timestamp
    UpdateFormItemDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the form
        description:
          type: string
          description: The description of the form
        default:
          type: boolean
          description: Indicates if the form is default
        fields:
          type: array
          items:
            $ref: '#/components/schemas/FormFieldDto'
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Condition'
        channels:
          description: Channels of the form
          type: array
          items:
            type: string
        isActive:
          type: boolean
          description: Indicates if the form is active
    UpdateFormDto:
      type: object
      properties:
        formId:
          type: string
          description: The ID of the form
        version:
          type: number
          description: The version of the form
        updates:
          description: The updates to the form
          allOf:
            - $ref: '#/components/schemas/UpdateFormItemDto'
      required:
        - formId
        - version
        - updates
    DeleteFormItemDto:
      type: object
      properties:
        formId:
          type: string
          description: The ID of the form
        version:
          type: number
          description: The version of the form
      required:
        - formId
        - version
    DeleteFormsDto:
      type: object
      properties:
        forms:
          type: array
          items:
            $ref: '#/components/schemas/DeleteFormItemDto'
      required:
        - forms
    OrderFormItemDto:
      type: object
      properties:
        formId:
          type: string
          description: The ID of the form
        order:
          type: number
          description: The order of the form
        version:
          type: number
          description: The version of the form
      required:
        - formId
        - order
        - version
    OrderFormsDto:
      type: object
      properties:
        forms:
          type: array
          items:
            $ref: '#/components/schemas/OrderFormItemDto'
      required:
        - forms
    Tag:
      type: object
      properties: {}
    TagListDto:
      type: object
      properties:
        count:
          type: number
          description: Number of tags
        items:
          description: List of tags
          type: array
          items:
            $ref: '#/components/schemas/Tag'
      required:
        - count
        - items
    GetTagsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        teamUuid:
          type: string
          description: The team ID
        data:
          description: Fetches all tags created by a team.
          allOf:
            - $ref: '#/components/schemas/TagListDto'
      required:
        - status
        - message
        - timestamp
        - teamUuid
        - data
    CreateTagDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the tag
          example: Cat
          minLength: 1
          maxLength: 50
        color:
          type: string
          description: Color in hexadecimal format
          example: '#FF0000'
          pattern: ^#([A-Fa-f0-9]{6})$
        tagType:
          type: string
          description: The type of the tag
          enum:
            - ticket
            - skills
        description:
          type: string
          description: Optional description of the tag
          nullable: true
      required:
        - name
        - color
        - tagType
    CreateTagsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        teamUuid:
          type: string
          description: The team ID
        data:
          description: Creates tags for a particular team.
          allOf:
            - $ref: '#/components/schemas/TagListDto'
      required:
        - status
        - message
        - timestamp
        - teamUuid
        - data
    UpdateTagDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the tag
          example: Urgent
          minLength: 1
          maxLength: 50
          nullable: true
        color:
          type: string
          description: Color in hexadecimal format
          example: '#FF0000'
          pattern: ^#([A-Fa-f0-9]{6})$
          nullable: true
        description:
          type: string
          description: Optional description of the tag
          example: Used for urgent tickets that need immediate attention
          nullable: true
          maxLength: 255
    UpdateTagsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        teamUuid:
          type: string
          description: The team ID
        data:
          description: Updates tags for a particular team.
          allOf:
            - $ref: '#/components/schemas/TagListDto'
      required:
        - status
        - message
        - timestamp
        - teamUuid
        - data
    DeleteTagResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        teamUuid:
          type: string
          description: The team ID
        removedTagId:
          type: string
          description: Removes a tag from a particular team.
      required:
        - status
        - message
        - timestamp
        - teamUuid
        - removedTagId
    GetTicketTagsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        ticketId:
          type: string
          description: The ticket ID
        data:
          description: The tags associated with the ticket
          allOf:
            - $ref: '#/components/schemas/TagListDto'
      required:
        - status
        - message
        - timestamp
        - ticketId
        - data
    AddTagsDto:
      type: object
      properties:
        tagIds:
          description: Array of tag IDs to add to the ticket
          example: '[''ids'', ''ids'']'
          type: array
          items:
            type: string
      required:
        - tagIds
    CreateTicketTagsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        ticketId:
          type: string
          description: The ticket ID
        data:
          description: The tags added to the ticket
          allOf:
            - $ref: '#/components/schemas/TagListDto'
      required:
        - status
        - message
        - timestamp
        - ticketId
        - data
    DeleteTicketTagResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        ticketId:
          type: string
          description: The ticket ID
        removedTagId:
          type: string
          description: The ID of the removed tag
      required:
        - status
        - message
        - timestamp
        - ticketId
        - removedTagId
    CommonTagResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete tag operations
          allOf:
            - $ref: '#/components/schemas/TagsResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    ViewsResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the view
        teamId:
          type: string
          description: The unique identifier of the team of the view
        viewId:
          type: number
          description: The view ID of the view
        name:
          type: string
          description: The name of the view
        description:
          type: string
          description: The description of the view
        configuration:
          type: object
          description: The JSON configuration containing view settings and filters
        viewsTypeId:
          type: string
          description: The views type ID of the view
        viewsType:
          type: string
          description: The views type of the view
        owner:
          type: string
          description: The name of the owner of the view
        ownerId:
          type: string
          description: The unique identifier of the owner of the view
        isShared:
          type: boolean
          description: Indicates if the view is shared
        isPersonal:
          type: boolean
          description: Indicates if the view is personal
        createdAt:
          format: date-time
          type: string
          description: The creation date of the view
        updatedAt:
          format: date-time
          type: string
          description: The update date of the view
      required:
        - id
        - teamId
        - viewId
        - name
        - description
        - configuration
        - viewsTypeId
        - viewsType
        - owner
        - ownerId
        - isShared
        - isPersonal
        - createdAt
        - updatedAt
    GetAllViewsResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The views fetched
          type: array
          items:
            $ref: '#/components/schemas/ViewsResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CommonViewResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The response for create/update/delete view operations
          allOf:
            - $ref: '#/components/schemas/ViewsResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    CreateViewBody:
      type: object
      properties:
        name:
          type: string
          description: The name of the view
          example: My View
        description:
          type: string
          description: The description of the view
          example: My View Description
        teamId:
          type: string
          description: The team id of the view
          example: T00M677SAK
        viewsTypeId:
          type: string
          description: The views type id of the view
          example: 3ENSBTAJ10QG1HAFYYNHADB58PKM5
        isShared:
          type: boolean
          description: The shared flag of the view
          example: true
        configuration:
          type: object
          description: The configuration of the view
      required:
        - name
        - description
        - teamId
        - viewsTypeId
        - isShared
        - configuration
    UpdateViewBody:
      type: object
      properties:
        name:
          type: string
          description: The name of the view
          example: My View
        description:
          type: string
          description: The description of the view
          example: My View Description
        configuration:
          type: object
          description: The configuration of the view
        isShared:
          type: boolean
          description: The shared flag of the view
          example: true
      required:
        - name
        - description
        - configuration
        - isShared
    ViewsTypesResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the view type
        name:
          type: string
          description: The name of the view
        description:
          type: string
          description: The description of the view
        icon:
          type: string
          description: The icon of the view type
        createdAt:
          format: date-time
          type: string
          description: The created at of the view
        updatedAt:
          format: date-time
          type: string
          description: The updated at of the view
      required:
        - id
        - name
        - description
        - icon
        - createdAt
        - updatedAt
    GetAllViewsTypesResponse:
      type: object
      properties:
        status:
          type: boolean
          default: true
          description: The status of the response
        message:
          type: string
          default: Success
          description: The message of the response
        timestamp:
          format: date-time
          type: string
          default: '2024-01-01T00:00:00.000Z'
          description: The timestamp of the response
        data:
          description: The views types fetched
          type: array
          items:
            $ref: '#/components/schemas/ViewsTypesResponseDto'
      required:
        - status
        - message
        - timestamp
        - data
    SubscribeDto:
      type: object
      properties:
        entityId:
          type: string
          description: The ID of the entity to subscribe to
          example: '123'
        eventCategory:
          type: string
          description: The category of event to subscribe to
          example: Ticket
          enum:
            - ticket
      required:
        - entityId
        - eventCategory
    SubscriberResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The ID of the user
          example: '123'
        name:
          type: string
          description: The name of the user
          example: John Doe
        email:
          type: string
          description: The email of the user
          example: <EMAIL>
        avatarUrl:
          type: string
          description: The avatar of the user
          example: https://example.com/avatar.png
        subscriptionType:
          type: string
          description: The type of subscription
          example: manual
          enum:
            - manual
            - auto
        subscribedAt:
          type: string
          description: The date when the user subscribed
          example: '2024-02-13T12:00:00Z'
      required:
        - id
        - name
        - email
        - avatarUrl
        - subscriptionType
        - subscribedAt
    BulkSubscribeDto:
      type: object
      properties:
        userIds:
          description: List of user IDs to subscribe
          example:
            - user1
            - user2
          type: array
          items:
            type: string
        entityId:
          type: string
          description: The ID of the entity to subscribe to
          example: '123'
        eventCategory:
          type: string
          description: The category of event to subscribe to
          example: Ticket
          enum:
            - ticket
      required:
        - userIds
        - entityId
        - eventCategory
    NotificationSubscriptionPreferenceRequestDto:
      type: object
      properties:
        eventType:
          type: string
          description: The event type of the notification
          example: ticket_created
        isEnabled:
          type: boolean
          description: Whether the notification is enabled
          example: true
      required:
        - eventType
        - isEnabled
    NotificationSubscriptionPreferenceResponseDto:
      type: object
      properties:
        eventType:
          type: string
          description: The event type of the notification
          example: ticket_created
        isEnabled:
          type: boolean
          description: Whether the notification is enabled
          example: true
        createdAt:
          type: string
          description: The created at date of the notification subscription preference
          example: '2021-01-01'
        updatedAt:
          type: string
          description: The updated at date of the notification subscription preference
          example: '2021-01-01'
      required:
        - eventType
        - isEnabled
        - createdAt
        - updatedAt
    UserNotificationPreferenceConfigurationDto:
      type: object
      properties:
        eventType:
          type: string
          description: The type of the notification event
          enum:
            - ticket_created
            - ticket_assigned
            - ticket_status_changed
            - ticket_priority_changed
            - ticket_escalated
            - ticket_archived
            - ticket_customer_thread_reply
            - ticket_customer_thread_mention
            - ticket_internal_thread_reply
            - ticket_internal_thread_mention
            - ticket_note_reply
            - ticket_note_mention
            - ticket_custom_field_updated
            - ticket_tag_updated
            - ticket_sla_breach_warning
            - ticket_sla_breached
            - ticket_csat_received
            - ticket_csat_updated
        channelType:
          type: string
          description: The type of the notification channel
          enum:
            - push
            - toast
            - email
            - slack
        isEnabled:
          type: boolean
          description: Whether the notification channel is enabled
      required:
        - eventType
        - channelType
        - isEnabled
    CreateUserNotificationPreferenceProfileDto:
      type: object
      properties:
        description:
          type: string
          description: The description of the user notification preference profile
          example: Out of office profile
        isCurrent:
          type: boolean
          description: Whether the user notification preference profile is current
          default: false
        isDefault:
          type: boolean
          description: Whether the user notification preference profile is default
          default: true
        groupingType:
          type: string
          description: The grouping type of the user notification preference profile
          enum:
            - no_grouping
            - group_by_event
            - group_by_entity
            - group_by_entity_and_event
        batchingType:
          type: string
          description: The batching type of the user notification preference profile
          enum:
            - no_batching
            - batch_by_entity
        batchCount:
          type: number
          description: The maximum number of notifications to send in a batch
        batchDuration:
          type: number
          description: The maximum duration of a batch in minutes
        title:
          type: string
          description: The title of the user notification preference profile
          example: Out of office
        configuration:
          description: The configuration of the user notification preference profile
          type: array
          items:
            $ref: '#/components/schemas/UserNotificationPreferenceConfigurationDto'
      required:
        - title
        - configuration
    UserNotificationPreferenceProfileResponseDto:
      type: object
      properties:
        id:
          type: string
          description: The id of the user notification preference profile
        title:
          type: string
          description: The title of the user notification preference profile
        description:
          type: string
          description: The description of the user notification preference profile
        isCurrent:
          type: boolean
          description: Whether the user notification preference profile is current
        isDefault:
          type: boolean
          description: Whether the user notification preference profile is default
        configuration:
          description: The configuration of the user notification preference profile
          type: array
          items:
            $ref: '#/components/schemas/UserNotificationPreferenceConfigurationDto'
        groupingType:
          type: string
          description: The grouping type of the user notification preference profile
          enum:
            - no_grouping
            - group_by_event
            - group_by_entity
            - group_by_entity_and_event
        batchingType:
          type: string
          description: The batching type of the user notification preference profile
          enum:
            - no_batching
            - batch_by_entity
        batchCount:
          type: number
          description: The maximum number of notifications to send in a batch
        batchDuration:
          type: number
          description: The maximum duration of a batch in minutes
        createdAt:
          type: string
          description: The created at date of the user notification preference profile
        updatedAt:
          type: string
          description: The updated at date of the user notification preference profile
      required:
        - id
        - title
        - description
        - isCurrent
        - isDefault
        - configuration
        - groupingType
        - batchingType
        - batchCount
        - batchDuration
        - createdAt
        - updatedAt
    UpdateUserNotificationPreferenceProfileDto:
      type: object
      properties:
        description:
          type: string
          description: The description of the user notification preference profile
          example: Out of office profile
        isCurrent:
          type: boolean
          description: Whether the user notification preference profile is current
          default: false
        isDefault:
          type: boolean
          description: Whether the user notification preference profile is default
          default: true
        groupingType:
          type: string
          description: The grouping type of the user notification preference profile
          enum:
            - no_grouping
            - group_by_event
            - group_by_entity
            - group_by_entity_and_event
        batchingType:
          type: string
          description: The batching type of the user notification preference profile
          enum:
            - no_batching
            - batch_by_entity
        batchCount:
          type: number
          description: The maximum number of notifications to send in a batch
        batchDuration:
          type: number
          description: The maximum duration of a batch in minutes
        title:
          type: string
          description: The title of the user notification preference profile
          example: Out of office
        configuration:
          description: The configuration of the user notification preference profile
          type: array
          items:
            $ref: '#/components/schemas/UserNotificationPreferenceConfigurationDto'
security:
  - ApiKey: []
