import { TestingModule } from "@nestjs/testing";
import {
  Account,
  AccountActivity,
  AccountAttributeType,
  AccountAttributeValue,
  AccountNote,
  AccountRelationship,
  AccountRelationshipType,
  AccountTask,
  AuditLog,
  BusinessHoursConfig,
  Comment,
  CustomerContact,
  CustomField,
  CustomFieldValues,
  CustomObject,
  CustomObjectFields,
  CustomObjectRecords,
  Draft,
  EmojiActions,
  Emojis,
  Form,
  FormFieldEvents,
  Mentions,
  NotificationChannel,
  NotificationSubscription,
  NotificationSubscriptionPreference,
  Organization,
  OrganizationDomains,
  OrganizationInvitations,
  PlatformSettings,
  PlatformSettingsSchema,
  Reactions,
  Storage,
  Tag,
  TagsType,
  Team,
  TeamCapacity,
  TeamConfiguration,
  TeamMember,
  TeamMemberRole,
  TeamRoutingRules,
  ThenaRestrictedField,
  Ticket,
  TicketPriority,
  TicketRelationships,
  TicketSentiment,
  TicketStatus,
  TicketTimeLog,
  TicketType,
  TimeOff,
  User,
  UserNotificationPreference,
  UserNotificationPreferenceProfile,
  UserSkills,
  UserType,
  Views,
  ViewsType,
} from "@repo/thena-platform-entities";
import { DataSource } from "typeorm";

interface UserWithPassword extends User {
  password: string;
  organization_id?: string;
}

declare global {
  // eslint-disable-next-line no-var
  var testModule: TestingModule;
  // eslint-disable-next-line no-var
  var testUser: UserWithPassword;
  // eslint-disable-next-line no-var
  var testUser1: UserWithPassword;
  // eslint-disable-next-line no-var
  var testUser2: UserWithPassword;
  // eslint-disable-next-line no-var
  var testUser3: UserWithPassword;
  // eslint-disable-next-line no-var
  var testUser4: UserWithPassword;
  // eslint-disable-next-line no-var
  var testUser5: UserWithPassword;
  // eslint-disable-next-line no-var
  var testOrganization: Organization;
  // eslint-disable-next-line no-var
  var testTeam: Team;
  // eslint-disable-next-line no-var
  var testTeam2: Team;
  // eslint-disable-next-line no-var
  var testTag: Tag;
  // eslint-disable-next-line no-var
  var testTicketType: TicketType;
  // eslint-disable-next-line no-var
  var testTicketStatus: TicketStatus;
  // eslint-disable-next-line no-var
  var testTicketPriority: TicketPriority;
  // eslint-disable-next-line no-var
  var testAdminAccount: Account;
  // eslint-disable-next-line no-var
  var testAccount: Account;
  // eslint-disable-next-line no-var
  var testAccount2: Account;
  // eslint-disable-next-line no-var
  var testAccount3: Account;
  // eslint-disable-next-line no-var
  var testViewsType: ViewsType;
  // eslint-disable-next-line no-var
  var globalEmojiHeart: Emojis;
  // eslint-disable-next-line no-var
  var globalEmojiThumbsUp: Emojis;
  // eslint-disable-next-line no-var
  var testAccountStatus: AccountAttributeValue;
  // eslint-disable-next-line no-var
  var testAccountClassification: AccountAttributeValue;
  // eslint-disable-next-line no-var
  var testContactType: AccountAttributeValue;
}

const usersDataRaw = [
  {
    email: "<EMAIL>",
    password: "Test@123",
    uuid: "914a26f2-535c-4849-8747-54c924a76474",
    uid: "UTESTUSER1",
    name: "Test User",
    userType: "ORG_ADMIN",
  },
  {
    email: "<EMAIL>",
    password: "Test@123",
    uuid: "40bc61d4-0fd1-45a4-a6d2-3234fba5d2c2",
    uid: "UTESTADMIN1",
    name: "Test Admin",
    userType: "ORG_ADMIN",
  },
  {
    email: "<EMAIL>",
    password: "Test@123",
    uuid: "e64a7d5f-eb2f-41b5-8e9a-fccbd2fc477c",
    uid: "UTESTUSER2",
    name: "Test User 1",
    userType: "ORG_ADMIN",
  },
  {
    email: "<EMAIL>",
    password: "Test@123",
    uuid: "f297a368-f29c-4707-9644-bd09d49d9ab4",
    uid: "UTESTUSER3",
    name: "Test User 2",
    userType: "ORG_ADMIN",
  },
  {
    email: "<EMAIL>",
    password: "Test@123",
    uuid: "8ffec6b7-4539-4425-8c48-4e7c79e3786e",
    uid: "UTESTUSER4",
    name: "Test User 3",
    userType: "ORG_ADMIN",
  },
  {
    email: "<EMAIL>",
    password: "Test@123",
    uuid: "e4d91e0b-44ec-48be-92ae-aedc2e28ab77",
    uid: "UTESTUSER5",
    name: "Test User 4",
    userType: "ORG_ADMIN",
  },
  {
    email: "<EMAIL>",
    password: "Test@123",
    uuid: "65e4b10c-aef6-41ec-8c9b-8ec74d14f542",
    uid: "UTESTUSER6",
    name: "Test User 5",
    userType: "USER",
  },
];

export default async function globalSetup() {
  // Create TypeORM connection
  const dataSource = new DataSource({
    type: "postgres",
    host: "localhost",
    port: 54332,
    username: "postgres",
    password: "postgres",
    database: "postgres",
    entities: [
      Account,
      AccountActivity,
      AccountAttributeValue,
      AccountNote,
      AccountRelationship,
      AccountRelationshipType,
      AccountTask,
      AuditLog,
      BusinessHoursConfig,
      Comment,
      CustomerContact,
      CustomField,
      CustomFieldValues,
      CustomObject,
      CustomObjectFields,
      CustomObjectRecords,
      Draft,
      EmojiActions,
      Emojis,
      Form,
      FormFieldEvents,
      Mentions,
      NotificationChannel,
      NotificationSubscription,
      NotificationSubscriptionPreference,
      Organization,
      OrganizationDomains,
      OrganizationInvitations,
      PlatformSettings,
      PlatformSettingsSchema,
      Reactions,
      Storage,
      Tag,
      Team,
      TeamCapacity,
      TeamConfiguration,
      TeamMember,
      TeamRoutingRules,
      ThenaRestrictedField,
      Ticket,
      TicketPriority,
      TicketRelationships,
      TicketSentiment,
      TicketStatus,
      TicketTimeLog,
      TicketType,
      TimeOff,
      User,
      UserNotificationPreference,
      UserNotificationPreferenceProfile,
      UserSkills,
      Views,
      ViewsType,
    ],
    synchronize: false,
  });

  await dataSource.initialize();

  const testOrganization = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Organization)
    .values({ name: "Test Organization", uid: "EORGTEST12" })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const testViewsType = await dataSource
    .createQueryBuilder()
    .insert()
    .into(ViewsType)
    .values({
      name: "List View",
      icon: "https://example.com/icon.png",
      description: "This is a test views type",
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  // Create a factory function to create users
  const createUserFactory = (
    email: string,
    password: string,
    uuid: string,
    uid: string,
    name: string,
    userType: UserType,
  ) => {
    return createUser(
      dataSource,
      testOrganization.id,
      email,
      password,
      uuid,
      uid,
      name,
      userType,
    );
  };

  const userPromises = usersDataRaw.map((userData) =>
    createUserFactory(
      userData.email,
      userData.password,
      userData.uuid,
      userData.uid,
      userData.name,
      userData.userType as UserType,
    ),
  );

  const [
    testUser,
    testAdminUser,
    testUser1,
    testUser2,
    testUser3,
    testUser4,
    testUser5,
  ] = await Promise.all(userPromises);

  const testTeam = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Team)
    .values({
      name: "Test Team",
      uid: "THGG97JHGH",
      organizationId: testOrganization.id,
      identifier: "TTM",
      teamOwnerId: testUser.id,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const testTeam2 = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Team)
    .values({
      name: "Test Team 2",
      uid: "THGG97JHGH2",
      organizationId: testOrganization.id,
      identifier: "TTM2",
      teamOwnerId: testUser.id,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  await dataSource
    .createQueryBuilder()
    .insert()
    .into(TeamMember)
    .values({
      organizationId: testOrganization.id,
      teamId: testTeam.id,
      userId: testUser.id,
      invitedById: testUser.id,
      isActive: true,
      role: TeamMemberRole.ADMIN,
      isBot: false,
    })
    .execute();

  await dataSource
    .createQueryBuilder()
    .insert()
    .into(TeamMember)
    .values({
      organizationId: testOrganization.id,
      teamId: testTeam2.id,
      userId: testUser.id,
      invitedById: testUser.id,
      isActive: true,
      role: TeamMemberRole.ADMIN,
      isBot: false,
    })
    .execute();

  const testTag = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Tag)
    .values({
      name: "Global Test Tag",
      organizationId: testOrganization.id,
      color: "#FFFFFF",
      teamId: testTeam.id,
      tagType: TagsType.TICKET,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const testTicketType = await dataSource
    .createQueryBuilder()
    .insert()
    .into(TicketType)
    .values({
      name: "Test Type",
      organizationId: testOrganization.id,
      color: "#FF5733", // Example color
      icon: "bug", // Example icon
      autoAssign: true,
      isActive: true,
      teamId: testTeam.id, // Optional, you can set this if needed
      metadata: { key: "value" }, // Example metadata
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0] as TicketType);

  // Create test ticket status
  const testTicketStatus = await dataSource
    .createQueryBuilder()
    .insert()
    .into(TicketStatus)
    .values({
      name: "Test Ticket Status",
      organizationId: testOrganization.id,
      teamId: testTeam.id,
      displayName: "Test Display Name",
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0] as TicketStatus);

  // Create test ticket priority
  const testTicketPriority = await dataSource
    .createQueryBuilder()
    .insert()
    .into(TicketPriority)
    .values({
      name: "Test Priority",
      isDefault: false,
      organizationId: testOrganization.id,
      teamId: testTeam.id,
      displayName: "Test Priority Display Name",
      description: "This is a test ticket priority", // Optional
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0] as TicketPriority);

  const testAccountStatus = await dataSource
    .createQueryBuilder()
    .insert()
    .into(AccountAttributeValue)
    .values({
      organizationId: testOrganization.id,
      attribute: AccountAttributeType.ACCOUNT_STATUS,
      value: "ACTIVE",
      isDefault: true,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const testAccountClassification = await dataSource
    .createQueryBuilder()
    .insert()
    .into(AccountAttributeValue)
    .values({
      organizationId: testOrganization.id,
      attribute: AccountAttributeType.ACCOUNT_CLASSIFICATION,
      value: "ENTERPRISE",
      isDefault: true,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const testAccountHealth = await dataSource
    .createQueryBuilder()
    .insert()
    .into(AccountAttributeValue)
    .values({
      organizationId: testOrganization.id,
      attribute: AccountAttributeType.ACCOUNT_HEALTH,
      value: "HEALTHY",
      isDefault: true,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const testAccountIndustry = await dataSource
    .createQueryBuilder()
    .insert()
    .into(AccountAttributeValue)
    .values({
      organizationId: testOrganization.id,
      attribute: AccountAttributeType.ACCOUNT_INDUSTRY,
      value: "FINANCE",
      isDefault: true,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  // Create default contact type
  const testContactType = await dataSource
    .createQueryBuilder()
    .insert()
    .into(AccountAttributeValue)
    .values({
      organizationId: testOrganization.id,
      attribute: AccountAttributeType.CONTACT_TYPE,
      value: "OTHER",
      isDefault: true,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  // Create test account
  const testAccount = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Account)
    .values({
      organizationId: testOrganization.id,
      status: testAccountStatus.id,
      classification: testAccountClassification.id,
      health: testAccountHealth.id,
      name: "Test Account",
      accountOwnerId: testUser.id, // Assuming testUser is the account owner
      isActive: true,
      primaryDomain: "testaccount.com",
      source: "manual",
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0] as Account);

  const testAccount2 = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Account)
    .values({
      organizationId: testOrganization.id,
      status: testAccountStatus.id,
      classification: testAccountClassification.id,
      health: testAccountHealth.id,
      name: "Test Account 2",
      accountOwnerId: testUser.id, // Assuming testUser is the account owner
      isActive: true,
      primaryDomain: "testaccount2.com",
      source: "manual",
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0] as Account);

  const testAccount3 = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Account)
    .values({
      organizationId: testOrganization.id,
      status: testAccountStatus.id,
      classification: testAccountClassification.id,
      health: testAccountHealth.id,
      name: "Test Account 3",
      accountOwnerId: testUser.id, // Assuming testUser is the account owner
      isActive: true,
      primaryDomain: "testaccount3.com",
      source: "manual",
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0] as Account);

  // Create default form for organization
  const defaultForm = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Form)
    .values({
      name: "Default Form",
      organizationId: testOrganization.id,
      default: true,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  // Create test ticket
  const testTicket = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Ticket)
    .values({
      organizationId: testOrganization.id,
      requestorEmail: testUser.email,
      submitterEmail: testUser.email,
      accountId: testAccount.id,
      title: "Test Ticket Title",
      description: "This is a test ticket description",
      subTeamId: testTeam.id,
      statusId: testTicketStatus.id,
      priorityId: testTicketPriority.id,
      typeId: testTicketType.id,
      assignedAgentId: testUser.id,
      isPrivate: false,
      isDraft: false,
      ticketId: 1000, // This will be unique per team due to the composite unique index
      titleMetadata: { source: "test" },
      customFieldValues: [],
      formId: defaultForm.id,
      team: testTeam,
      teamId: testTeam.id,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const globalEmojiHeart = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Emojis)
    .values({
      name: "heart",
      unicode: "❤️",
      aliases: "love,hearts",
      keywords: ["love", "like", "affection", "valentines"],
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const globalEmojiThumbsUp = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Emojis)
    .values({
      name: "thumbs_up",
      unicode: "👍",
      aliases: "thumbsup,+1,yes",
      keywords: ["agree", "approve", "ok", "positive", "like"],
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  const testAdminAccount = await dataSource
    .createQueryBuilder()
    .insert()
    .into(Account)
    .values({
      organizationId: testOrganization.id,
      name: "Test Admin Account",
      accountOwnerId: testAdminUser.id, // Assuming testUser is the account owner
      status: testAccountStatus.id,
      classification: testAccountClassification.id,
      health: testAccountHealth.id,
      isActive: true,
      primaryDomain: "testadminaccount.com",
      source: "manual",
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  // Store in global scope for access in teardown
  global.testAdminAccount = testAdminAccount;
  global.testUser = { ...testUser, password: "Test@123" };
  global.testAdminUser = { ...testAdminUser, password: "Test@123" };
  global.testUser1 = { ...testUser1, password: "Test@123" };
  global.testUser2 = { ...testUser2, password: "Test@123" };
  global.testUser3 = { ...testUser3, password: "Test@123" };
  global.testUser4 = { ...testUser4, password: "Test@123" };
  global.testUser5 = { ...testUser5, password: "Test@123" };
  global.testTeam = { ...testTeam, teamId: testTeam.team_id };
  global.testTeam2 = { ...testTeam2, teamId: testTeam2.team_id };
  global.testOrganization = testOrganization;
  global.testTag = testTag;
  global.testTicketType = testTicketType;
  global.testTicketStatus = testTicketStatus;
  global.testTicketPriority = testTicketPriority;
  global.testAccount = testAccount;
  global.testAccount2 = testAccount2;
  global.testAccount3 = testAccount3;
  global.testTicket = testTicket;
  global.testViewsType = testViewsType;
  global.globalEmojiHeart = globalEmojiHeart;
  global.globalEmojiThumbsUp = globalEmojiThumbsUp;
  global.testAccountStatus = testAccountStatus;
  global.testAccountClassification = testAccountClassification;
  global.testAccountHealth = testAccountHealth;
  global.testAccountIndustry = testAccountIndustry;
  global.testContactType = testContactType;
  await dataSource.destroy();
}

async function createUser(
  dataSource: DataSource,
  orgId: string,
  email: string,
  password: string,
  uuid: string,
  uid: string,
  name: string,
  userType: UserType,
) {
  // Create auth.users
  await dataSource.query(`
    INSERT INTO
    auth.users (
      instance_id,
      id,
      aud,
      role,
      email,
      encrypted_password,
      email_confirmed_at,
      recovery_sent_at,
      last_sign_in_at,
      raw_app_meta_data,
      raw_user_meta_data,
      created_at,
      updated_at,
      confirmation_token,
      email_change,
      email_change_token_new,
      recovery_token
    ) VALUES (
      '********-0000-0000-0000-************',
      '${uuid}',
      'authenticated',
      'authenticated',
      '${email}',
      crypt ('${password}', gen_salt ('bf')),
      current_timestamp,
      current_timestamp,
      current_timestamp,
      '{"provider":"email","providers":["email"]}',
      '{}',
      current_timestamp,
      current_timestamp,
      '',
      '',
      '',
      ''
    );`);

  // Create test data using raw queries
  const testUser = await dataSource
    .createQueryBuilder()
    .insert()
    .into(User)
    .values({
      uid,
      authId: uuid,
      email,
      name,
      organizationId: orgId,
      userType,
    })
    .returning("*")
    .execute()
    .then((result) => result.raw[0]);

  return testUser;
}
