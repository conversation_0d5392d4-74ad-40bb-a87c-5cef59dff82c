{"name": "@repo/thena-platform-entities", "description": "", "version": "1.0.0", "author": "", "dependencies": {"@babel/runtime": "^7.27.3", "@repo/thena-shared-interfaces": "workspace:*", "@repo/workflow-engine": "workspace:*", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cls-hooked": "^4.2.2", "cls-rtracer": "^2.6.3", "dotenv": "^16.4.5", "ioredis": "^5.4.1", "typesense": "1.8.2", "winston": "^3.14.2"}, "devDependencies": {"@nestjs/core": "^10.4.4", "@nestjs/testing": "^10.4.4", "@repo/eslint-config": "workspace:*", "@repo/nestjs-commons": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/jest": "^29.5.2", "@types/node": "^22.9.0", "chalk": "^5.3.0", "jest": "^29.5.0", "node": "link:@types/sentry/node", "ora": "^8.1.1", "ts-node": "^10.9.1", "typescript": "5.6.3"}, "peerDependencies": {"@nestjs/common": "^10.4.4", "@nestjs/core": "^10.4.4", "@nestjs/typeorm": "^11.0.0", "pg": "^8.13.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "exports": {"./entities/*": "./dist/entities/*.js", "./repositories/*": "./dist/repositories/*.js", "./common/*": "./dist/common/*.js", ".": {"import": "./dist/index.js", "require": "./dist/index.js"}}, "files": ["dist"], "jest": {"moduleFileExtensions": ["js", "json", "ts"], "maxWorkers": 1, "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "testEnvironment": "node"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "scripts": {"build": "pnpm clean && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "clean:modules": "rimraf node_modules", "dev": "tsc --watch", "lint": "eslint \"src/**/*.ts\"", "migration:create": "typeorm migration:create ./src/migrations/new-migration", "migration:revert": "typeorm migration:revert -d ./src/typeorm-config.ts", "migration:run": "pnpx typeorm-ts-node-commonjs migration:run -d ./src/typeorm-config.ts", "migration:test:run": "pnpm typeorm migration:run -d ./src/typeorm-tests-config.ts", "migrations": "-------------------------- MIGRATIONS --------------------------", "seed": "ts-node src/seeders/index.ts", "seeding": "-------------------------- SEEDING --------------------------", "start": "ts-node src/index.ts", "test": "jest --runInBand --verbose", "type-check": "tsc --noEmit", "typeorm": "typeorm-ts-node-commonjs", "typesense:up": "ts-node src/type-sense/index.ts up", "typesense:down": "ts-node src/type-sense/index.ts down"}, "types": "dist/index.d.ts"}