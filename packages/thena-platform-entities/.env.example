# Staging Database Configuration
THENA_PLATFORM_DB_HOST_STAGING=<insert-staging-host-here>
THENA_PLATFORM_DB_PORT_STAGING=<insert-staging-port-here>
THENA_PLATFORM_DB_USER_STAGING=<insert-staging-user-here>
THENA_PLATFORM_DB_PASSWORD_STAGING=<insert-staging-password-here>
THENA_PLATFORM_DB_NAME_STAGING=<insert-staging-db-name-here>

# Production Database Configuration
THENA_PLATFORM_DB_HOST_PRODUCTION=<insert-production-host-here>
THENA_PLATFORM_DB_PORT_PRODUCTION=<insert-production-port-here>
THENA_PLATFORM_DB_USER_PRODUCTION=<insert-production-user-here>
THENA_PLATFORM_DB_PASSWORD_PRODUCTION=<insert-production-password-here>
THENA_PLATFORM_DB_NAME_PRODUCTION=<insert-production-db-name-here>

# Development Database Configuration
THENA_PLATFORM_DB_HOST_DEVELOPMENT=<insert-development-host-here>
THENA_PLATFORM_DB_PORT_DEVELOPMENT=<insert-development-port-here>
THENA_PLATFORM_DB_USER_DEVELOPMENT=<insert-development-user-here>
THENA_PLATFORM_DB_PASSWORD_DEVELOPMENT=<insert-development-password-here>
THENA_PLATFORM_DB_NAME_DEVELOPMENT=<insert-development-db-name-here>

# Environment
# If your running platform locally, set this to development
# if your runnning any other app, set this to staging
NODE_ENV=<development|staging|production>

# needed only if you use the typesense migrate script
TYPESENSE_HOST=<your typesense host>
TYPESENSE_API_KEY=<your typesense admin key>