import { RequestSource } from "@repo/nestjs-commons/middlewares";
import "reflect-metadata";
import {
  Check,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Account } from "../../accounts/entities/account.entity";
import { CustomerContact } from "../../accounts/entities/customer-contact.entity";
import {
  EntityType,
  SUPPORTED_TYPES,
} from "../../common/constants/annotator.constants";
import { FieldMetadata } from "../../common/decorators/field-metadata.decorator";
import { Comment } from "../../communications/entities/comments.entity";
import { OPERATORS } from "../../constants";
import { CustomFieldValues } from "../../custom-fields/entities/custom-field-values.entity";
import { Form } from "../../forms/entities/form.entity";
import { Organization } from "../../organizations/entities/organization.entity";
import { Storage } from "../../storage/entities/storage.entity";
import { Tag } from "../../tags/entities/tag.entity";
import { Team } from "../../teams/entities/team.entity";
import { User } from "../../users/entities/user.entity";
import { TicketPriority } from "./ticket-priority.entity";
import { TicketSentiment } from "./ticket-sentiment.entity";
import { TicketStatus } from "./ticket-status.entity";
import { TicketTimeLog } from "./ticket-time-log.entity";
import { TicketType } from "./ticket-type.entity";

export interface TicketMetadata {
  /**
   * The comments on the ticket
   */
  // comments: Record<string, string>;

  /**
   * The external metadata on the ticket
   */
  external_metadata: Record<string, unknown>;

  /**
   * The escalation details on the ticket
   */
  escalation_details?: {
    /**
     * The reason for the escalation
     */
    reason: string;

    /**
     * The time the ticket was escalated
     */
    escalation_time: number;

    /**
     * The details of the escalation
     */
    details: string;

    /**
     * The user who escalated the ticket
     */
    escalated_by: {
      /**
       * The email of the user who escalated the ticket
       */
      email?: string;

      /**
       * The ID of the user who escalated the ticket
       */
      userId?: string;
    };

    /**
     * The impact of the escalation
     */
    impact: string;
  };
}

@Entity("ticket")
@Index("idx_ticket_metadata", { synchronize: false }) // TypeORM doesn't support GIN indexes on jsonb created this manually in migrations
@Index("unique_ticket_uid", ["uid", "organizationId"], { unique: true })
@Index("unique_ticket_id", ["ticketId", "teamId", "organizationId"], {
  unique: true,
})
// TODO: Remove this check once we have a proper source enum @amit
@Check(
  `"source" IN ('unknown', 'web', 'api', 'mobile', 'email', 'slack', 'integration', 'grpc', 'workflow', 'vendor-portal', 'customer-portal', 'ai-agent', 'ms-teams', 'web-chat', 'manual')`,
)
export class Ticket {
  @PrimaryGeneratedColumn({ type: "bigint" })
  id: string;

  @Index()
  @Column({ type: "text", default: () => "generate_ulid()" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Id",
    description: "Identifier of the ticket",
    expression: "id",
    standard: true,
  })
  uid: string;

  @ManyToOne(() => Organization, { onDelete: "RESTRICT", onUpdate: "CASCADE" })
  @JoinColumn({ name: "organization_id" })
  organization: Organization;

  @Index()
  @Column({ name: "organization_id" })
  organizationId: string;

  @ManyToOne(() => Form, { onDelete: "RESTRICT", onUpdate: "CASCADE" })
  @JoinColumn({ name: "form_id" })
  form: Form;

  @Index()
  @Column({ name: "form_id", nullable: true })
  formId: string;

  @OneToMany(() => Comment, (comment) => comment.ticket, {
    nullable: true,
  })
  @JoinTable({
    name: "ticket_comments",
    joinColumn: {
      name: "ticket_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "comment_id",
      referencedColumnName: "id",
    },
  })
  comments: Comment[];

  @OneToMany(() => TicketTimeLog, (timeLog) => timeLog.ticket)
  timeLogs: TicketTimeLog[];

  @Index()
  @FieldMetadata({
    type: SUPPORTED_TYPES.NUMBER,
    label: "Ticket ID",
    description: "Numeric ID of the ticket",
    expression: "ticketId",
    standard: true,
  })
  @Column({ type: "integer", name: "ticket_id", default: 0 })
  ticketId: number;

  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Requestor Email",
    description: "Requestor email associated with the ticket",
    expression: "requestorEmail",
    standard: true,
  })
  @Index()
  @Column({ name: "requestor_email" })
  requestorEmail: string;

  @Index()
  @Column({ type: "bigint", name: "customer_contact_id", nullable: true })
  customerContactId: string;

  @Index()
  @ManyToOne(() => CustomerContact, {
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
    nullable: true,
  })
  @JoinColumn({ name: "customer_contact_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Customer Contact",
    description: "Customer contact associated with the ticket",
    expression: "customerContact",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.CUSTOMER_CONTACT,
    },
  })
  customerContact: CustomerContact;

  @Index()
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Submitter Email",
    description: "Submitter email associated with the ticket",
    expression: "submitterEmail",
    standard: true,
  })
  @Column({ name: "submitter_email", nullable: true })
  submitterEmail: string;

  @ManyToOne(() => Account, { onDelete: "SET NULL", onUpdate: "CASCADE" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Account",
    description: "Account associated with the ticket",
    expression: "account",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.ACCOUNT,
    },
  })
  @JoinColumn({ name: "account_id" })
  account: Account;

  @Index()
  @Column({ name: "account_id", nullable: true })
  accountId: string;

  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Title",
    description: "Title of the ticket",
    expression: "title",
    standard: true,
  })
  @Column({ type: "text" })
  title: string;

  @Column({ type: "jsonb", nullable: true, name: "title_metadata" })
  titleMetadata: Record<string, unknown>;

  @Column({ nullable: true, type: "jsonb" })
  metadata: TicketMetadata;

  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "AI Generated Title",
    description: "AI Generated Title of the ticket",
    expression: "aiGeneratedTitle",
    standard: true,
  })
  @Column({ nullable: true, name: "ai_generated_title" })
  aiGeneratedTitle: string;

  @FieldMetadata({
    type: SUPPORTED_TYPES.TEXT,
    label: "Description",
    description: "Detailed description of the ticket",
    expression: "description",
    standard: true,
  })
  @Column({ type: "text", nullable: true })
  description: string;

  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "AI Generated Summary",
    description: "AI Generated Summary of the ticket",
    expression: "aiGeneratedSummary",
    standard: true,
  })
  @Column({ nullable: true, name: "ai_generated_summary" })
  aiGeneratedSummary: string;

  @ManyToOne(() => Team, {
    nullable: false,
    onDelete: "RESTRICT",
    onUpdate: "CASCADE",
  })
  @JoinColumn({ name: "team_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Team",
    description: "Team associated with the ticket",
    expression: "team",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TEAM,
      relationshipValue: "team",
    },
  })
  team: Team;

  @Index()
  @Column({ name: "team_id", nullable: false })
  teamId: string;

  @ManyToOne(() => Team, {
    nullable: true,
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Sub Team",
    description: "Sub Team associated with the ticket",
    expression: "subTeam",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TEAM,
      relationshipValue: "subTeam",
    },
  })
  @JoinColumn({ name: "sub_team_id" })
  subTeam: Team;

  @Column({ name: "sub_team_id", nullable: true })
  subTeamId: string;

  @ManyToOne(() => TicketStatus, (status) => status.tickets, {
    onDelete: "RESTRICT",
    onUpdate: "CASCADE",
  })
  @JoinColumn({ name: "status_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Status",
    description: "Current status of the ticket",
    expression: "status",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TICKET_STATUS,
    },
  })
  status: TicketStatus;

  @Index()
  @Column({ name: "status_id" })
  statusId: string;

  @Index()
  @Column({ name: "sentiment_id", nullable: true })
  sentimentId: string;

  @ManyToOne(() => TicketSentiment, (sentiment) => sentiment.tickets, {
    onDelete: "RESTRICT",
    onUpdate: "CASCADE",
    nullable: true,
  })
  @JoinColumn({ name: "sentiment_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Sentiment",
    description: "Sentiment of the ticket",
    expression: "sentiment",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TICKET_SENTIMENT,
    },
  })
  sentiment: TicketSentiment;

  @ManyToOne(() => TicketPriority, (priority) => priority.tickets, {
    onDelete: "RESTRICT",
    onUpdate: "CASCADE",
    nullable: true,
  })
  @JoinColumn({ name: "priority_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Priority",
    description: "Priority level of the ticket",
    expression: "priority",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TICKET_PRIORITY,
    },
    slaMetadata: {
      required: true,
      system: true,
      slaEnabled: true,
      expression: "priority.id",
      endPoint: "/v1/tickets/priority",
      queryParams: {
        required: {
          teamId: "teamId",
        },
      },
      supportedOperators: [
        {
          name: OPERATORS.EQUALS.name,
          value: OPERATORS.EQUALS.value,
        },
        {
          name: OPERATORS.NOT_EQUALS.name,
          value: OPERATORS.NOT_EQUALS.value,
        },
        {
          name: OPERATORS.IN.name,
          value: OPERATORS.IN.value,
        },
        {
          name: OPERATORS.NOT_IN.name,
          value: OPERATORS.NOT_IN.value,
        },
      ],
    },
  })
  priority: TicketPriority;

  @Index()
  @Column({ name: "priority_id", nullable: true })
  priorityId: string;

  @ManyToOne(() => TicketType, (type) => type.tickets, {
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  })
  @JoinColumn({ name: "type_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Type",
    description: "Type of the ticket",
    expression: "type",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TICKET_TYPE,
    },
  })
  type: TicketType;

  @Index()
  @Column({ name: "type_id", nullable: true })
  typeId: string;

  @ManyToMany(() => Tag, (tag) => tag.tickets, {
    onDelete: "CASCADE",
    onUpdate: "CASCADE",
  })
  @JoinTable({
    name: "ticket_tags",
    joinColumn: {
      name: "ticket_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "tag_id",
      referencedColumnName: "id",
    },
  })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Tags",
    description: "Tags associated with the ticket",
    expression: "tags",
    standard: true,
    constraints: {
      lookupType: "manyToMany",
      relatedEntityType: EntityType.TAGS,
    },
  })
  tags: Tag[];

  @ManyToOne(() => User, { onDelete: "SET NULL", onUpdate: "CASCADE" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Assigned Agent",
    description: "Agent assigned to the ticket",
    expression: "assignedAgent",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.USER,
    },
  })
  @JoinColumn({ name: "assigned_agent_id" })
  assignedAgent: User;

  @Index()
  @Column({ name: "assigned_agent_id", nullable: true })
  assignedAgentId: string;

  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Created At",
    description: "Date and time the ticket was created",
    expression: "createdAt",
    standard: true,
  })
  @CreateDateColumn({ type: "timestamptz", name: "created_at" })
  createdAt: Date;

  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Due Date",
    description: "Due date of the ticket",
    expression: "dueDate",
    standard: true,
  })
  @Column({ nullable: true, name: "due_date" })
  dueDate: Date;

  @FieldMetadata({
    type: SUPPORTED_TYPES.BOOLEAN,
    label: "Is Escalated",
    description: "Whether the ticket is escalated",
    expression: "isEscalated",
    standard: true,
  })
  @Column({ default: false, name: "is_escalated" })
  isEscalated: boolean;

  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Updated At",
    description: "Date and time the ticket was last updated",
    expression: "updatedAt",
    standard: true,
  })
  @UpdateDateColumn({ name: "updated_at", type: "timestamptz" })
  updatedAt: Date;

  @FieldMetadata({
    type: SUPPORTED_TYPES.BOOLEAN,
    label: "Private",
    description: "Whether the ticket is private",
    expression: "isPrivate",
    standard: true,
  })
  @Column({ default: false, name: "is_private" })
  isPrivate: boolean;

  @FieldMetadata({
    type: SUPPORTED_TYPES.NUMBER,
    label: "Story Points",
    description: "Story points associated with the ticket",
    expression: "storyPoints",
    standard: true,
  })
  @Column({ nullable: true, name: "story_points" })
  storyPoints: number;

  @Column({ default: false, name: "is_draft" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.BOOLEAN,
    label: "Is Draft",
    description: "Whether the ticket is a draft",
    expression: "isDraft",
    standard: true,
  })
  isDraft: boolean;

  @Index()
  @Column({ nullable: true, name: "archived_at" })
  archivedAt: Date;

  @ManyToMany(() => Storage, (storage) => storage)
  @JoinTable({
    name: "ticket_attachments",
    joinColumn: {
      name: "ticket_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "storage_id",
      referencedColumnName: "id",
    },
  })
  attachments: Storage[];

  @ManyToMany(() => CustomFieldValues, (customFieldValue) => customFieldValue, {
    nullable: true,
  })
  @JoinTable({
    name: "ticket_custom_field_values",
    joinColumn: {
      name: "ticket_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "custom_field_value_id",
      referencedColumnName: "id",
    },
  })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Custom Fields",
    description: "Custom fields associated with the ticket",
    expression: "customFieldValues",
    standard: true,
    constraints: {
      lookupType: "manyToMany",
      relatedEntityType: EntityType.CUSTOM_FIELD_VALUES,
    },
  })
  customFieldValues: CustomFieldValues[] | null;

  @Column({ type: "varchar", name: "source", default: "unknown" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.CHOICE,
    label: "Source",
    description: "Source of the ticket",
    expression: "source",
    standard: true,
    constraints: {
      options: Object.entries(RequestSource).map(([key, value]) => ({
        label: key.toLowerCase().replace("-", " "),
        value: value,
      })),
    },
  })
  source: RequestSource;

  @Index()
  @DeleteDateColumn({ type: "timestamptz", name: "deleted_at", nullable: true })
  deletedAt?: Date;
}
