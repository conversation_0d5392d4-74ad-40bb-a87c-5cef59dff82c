import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import {
  EntityType,
  SUPPORTED_TYPES,
} from "../../common/constants/annotator.constants";
import { FieldMetadata } from "../../common/decorators/field-metadata.decorator";
import { Organization } from "../../organizations/entities/organization.entity";
import { Team } from "../../teams/entities/team.entity";
import { Ticket } from "./ticket.entity";

@Entity("ticket_sentiment")
@Index("idx_uniq_ticket_sentiment_uid", ["uid", "organization"], {
  unique: true,
})
export class TicketSentiment {
  @PrimaryGeneratedColumn({ type: "bigint" })
  id: string;

  @Index()
  @Column({ type: "text", default: () => "generate_ulid()" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Id",
    description: "Identifier of the ticket sentiment",
    expression: "id",
    standard: true,
  })
  uid: string;

  @Column({ type: "text", nullable: true })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Icon",
    description: "Icon of the ticket sentiment",
    expression: "icon",
    standard: true,
  })
  icon: string;

  @ManyToOne(() => Team)
  @JoinColumn({ name: "team_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Team",
    description: "Team of the ticket sentiment",
    expression: "team",
    standard: true,
    constraints: {
      lookupType: "manyToOne",
      relatedEntityType: EntityType.TEAM,
    },
  })
  team: Team;

  @Column()
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Name",
    description: "Name of the ticket sentiment",
    expression: "name",
    standard: true,
  })
  name: string;

  @Column({ type: "text", nullable: true })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Description",
    description: "Description of the ticket sentiment",
    expression: "description",
    standard: true,
  })
  description: string;

  @Column({ default: false, name: "is_default" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.BOOLEAN,
    label: "Is Default",
    description: "Whether the ticket sentiment is the default sentiment",
    expression: "isDefault",
    standard: true,
  })
  isDefault: boolean;

  @Index()
  @ManyToOne(() => Organization)
  @JoinColumn({ name: "organization_id" })
  organization: Organization;

  @OneToMany(() => Ticket, (ticket) => ticket.sentiment)
  tickets: Ticket[];

  @DeleteDateColumn({ type: "timestamptz", name: "deleted_at", nullable: true })
  deletedAt: Date;

  @CreateDateColumn({ name: "created_at", type: "timestamptz" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Created At",
    description: "Date and time when the ticket sentiment was created",
    expression: "createdAt",
    standard: true,
  })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamptz" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Updated At",
    description: "Date and time when the ticket sentiment was last updated",
    expression: "updatedAt",
    standard: true,
  })
  updatedAt: Date;
}
