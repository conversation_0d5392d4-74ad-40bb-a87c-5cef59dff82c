{"created_at": **********, "default_sorting_field": "updated_at", "enable_nested_fields": false, "fields": [{"name": "assigned_agent_uid", "type": "string", "optional": true, "facet": true, "index": true}, {"name": "account_owner_uid", "type": "string", "optional": true, "facet": true, "index": true}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "uid", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "name", "optional": false, "sort": true, "stem": false, "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "description", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "logo", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "is_active", "optional": false, "sort": true, "stem": false, "store": true, "type": "bool"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "status", "optional": false, "sort": true, "stem": false, "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "classification", "optional": false, "sort": true, "stem": false, "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "health", "optional": false, "sort": true, "stem": false, "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "industry", "optional": false, "sort": true, "stem": false, "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "source", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "primary_domain", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "secondary_domain", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "website", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "billing_address", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "shipping_address", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "annual_revenue", "optional": false, "sort": true, "stem": false, "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "employees", "optional": false, "sort": true, "stem": false, "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "account_owner_id", "optional": false, "sort": true, "stem": false, "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "account_owner_email", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "account_owner_name", "optional": false, "sort": true, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "account_owner_user_type", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "account_owner_status", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "account_owner_timezone", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "metadata", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "organization_id", "optional": false, "sort": true, "stem": false, "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "created_at", "optional": false, "sort": true, "stem": false, "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "updated_at", "optional": false, "sort": true, "stem": false, "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "deleted_at", "optional": true, "sort": true, "stem": false, "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "id_search", "optional": true, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "health_uid", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "health_value", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "classification_uid", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "classification_value", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "status_uid", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "status_value", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "industry_uid", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "industry_value", "optional": false, "sort": false, "stem": false, "store": true, "type": "string"}], "name": "accounts", "num_documents": 156, "symbols_to_index": ["/", "-", "_", ".", "@"], "token_separators": [",", ";"]}