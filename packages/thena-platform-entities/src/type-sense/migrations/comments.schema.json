{"name": "comments", "fields": [{"name": "uid", "type": "string", "facet": false, "index": true, "store": true, "sort": false}, {"name": "team_id", "type": "string", "facet": true, "index": true, "store": true, "sort": false}, {"name": "ticket_id", "type": "string", "facet": true, "index": true, "store": true, "sort": false}, {"name": "content", "type": "string", "facet": false, "index": true, "store": true, "sort": false}, {"name": "content_markdown", "type": "string", "facet": false, "index": true, "store": true, "sort": false}, {"name": "content_html", "type": "string", "facet": false, "index": false, "store": true, "sort": false}, {"name": "created_at", "type": "int64", "facet": true, "index": true, "store": true, "sort": true}, {"name": "updated_at", "type": "int64", "facet": true, "index": true, "store": true, "sort": true}, {"name": "created_by_id", "type": "string", "facet": true, "index": true, "store": true, "sort": false}, {"name": "created_by_email", "type": "string", "facet": false, "index": true, "store": true, "sort": false, "optional": true}, {"name": "created_by_name", "type": "string", "facet": true, "index": true, "store": true, "sort": true, "optional": true}, {"name": "organization_id", "type": "string", "facet": true, "index": true, "store": true, "sort": false}, {"name": "organization_uid", "type": "string", "facet": false, "index": true, "store": true, "sort": false, "optional": true}, {"name": "team_uid", "type": "string", "facet": false, "index": true, "store": true, "sort": false}, {"name": "team_identifier", "type": "string", "facet": true, "index": true, "store": true, "sort": false}, {"name": "ticket_ticket_id", "type": "string", "facet": false, "index": true, "store": true, "sort": false}, {"name": "ticket_identifier", "type": "string", "facet": true, "index": true, "store": true, "sort": false, "optional": true}, {"name": "ticket_uid", "type": "string", "facet": false, "index": true, "store": true, "sort": false, "optional": true, "reference": "tickets.uid", "async_reference": true}, {"name": "comment_type", "type": "string", "facet": true, "index": true, "store": true, "sort": false}, {"name": "comment_visibility", "type": "string", "facet": true, "index": true, "store": true, "sort": false}, {"name": "is_edited", "type": "bool", "facet": true, "index": true, "store": true, "sort": true}, {"name": "is_pinned", "type": "bool", "facet": true, "index": true, "store": true, "sort": true}], "default_sorting_field": "created_at", "symbols_to_index": ["/", "-", "_", ".", "@"], "token_separators": [",", ";"]}