{"created_at": 1748271193, "default_sorting_field": "updated_at", "enable_nested_fields": true, "fields": [{"facet": false, "index": true, "infix": false, "locale": "", "name": "uid", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "ticket_id", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "title", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "description", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "ai_generated_summary", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "ai_generated_title", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "organization_id", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "organization_uid", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "form_id", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "form_uid", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "form_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "requestor_email", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "submitter_email", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "account_id", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "string", "reference": "accounts.id", "async_reference": true}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "account_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "team_id", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "team_uid", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string", "reference": "teams.uid", "async_reference": true}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "team_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "team_identifier", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sub_team_id", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sub_team_uid", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "string", "reference": "teams.uid", "async_reference": true}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "sub_team_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "assigned_agent_id", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "assigned_agent_uid", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "assigned_agent_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "assigned_agent_email", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "status_id", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "status_uid", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "status_name", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "priority_id", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "priority_uid", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "priority_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "type_id", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "type_uid", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "type_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "type_icon", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "type_color", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "customer_contact_id", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "string", "reference": "customer_contacts.id", "async_reference": true}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "is_escalated", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "bool"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "is_private", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "bool"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "is_draft", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "bool"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sentiment_id", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sentiment_uid", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sentiment_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "source", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "story_points", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "created_at", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "updated_at", "optional": false, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "due_date", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "archived_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "deleted_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "tags", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "object"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_status", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_scheduled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_breached_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_achieved_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_paused_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_resumed_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_cancelled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_duration_to_breach_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_paused_duration_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_response_next_attempt_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_status", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_scheduled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_breached_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_achieved_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_paused_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_resumed_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_cancelled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_duration_to_breach_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_paused_duration_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_resolution_next_attempt_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_status", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_scheduled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_breached_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_achieved_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_paused_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_resumed_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_cancelled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_duration_to_breach_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_paused_duration_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_total_resolution_time_next_attempt_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_status", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_scheduled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_breached_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_achieved_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_paused_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_resumed_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_cancelled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_duration_to_breach_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_paused_duration_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_first_time_response_next_attempt_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_status", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_scheduled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_breached_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_achieved_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_paused_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_resumed_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_cancelled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_duration_to_breach_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_paused_duration_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_next_time_response_next_attempt_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_status", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_scheduled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_breached_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_achieved_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_paused_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_resumed_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_cancelled_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_duration_to_breach_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_paused_duration_minutes", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int32"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "sla_update_time_next_attempt_at", "optional": true, "sort": true, "stem": false, "stem_dictionary": "", "store": true, "type": "int64"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "full_text", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "ticket_identifier", "optional": false, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": false, "index": true, "infix": false, "locale": "", "name": "metadata", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "customer_contact_uid", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "customer_contact_name", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}, {"facet": true, "index": true, "infix": false, "locale": "", "name": "customer_contact_email", "optional": true, "sort": false, "stem": false, "stem_dictionary": "", "store": true, "type": "string"}], "name": "tickets", "num_documents": 1, "symbols_to_index": ["/", "-", "_", ".", "@"], "token_separators": ["-"]}