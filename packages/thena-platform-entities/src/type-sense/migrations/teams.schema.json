{"name": "teams", "fields": [{"name": "id", "type": "int64", "facet": true, "index": true}, {"name": "uid", "type": "string", "facet": true, "index": true}, {"name": "identifier", "type": "string", "facet": true, "index": true}, {"name": "organization_id", "type": "int64", "facet": true, "index": true}, {"name": "parent_team_id", "type": "int64", "facet": true, "index": true, "optional": true}, {"name": "name", "type": "string", "facet": false, "index": true}, {"name": "description", "type": "string", "facet": false, "index": true, "optional": true}, {"name": "icon", "type": "string", "facet": true, "index": true, "optional": true}, {"name": "color", "type": "string", "facet": true, "index": true, "optional": true}, {"name": "configuration_id", "type": "int64", "facet": true, "index": true, "optional": true}, {"name": "team_owner_id", "type": "int64", "facet": true, "index": true}, {"name": "is_active", "type": "bool", "facet": true, "index": true}, {"name": "is_private", "type": "bool", "facet": true, "index": true}, {"name": "created_at", "type": "int64", "facet": true, "index": true, "sort": true}, {"name": "updated_at", "type": "int64", "facet": true, "index": true, "sort": true}, {"name": "deleted_at", "type": "int64", "facet": true, "index": true, "sort": true, "optional": true}, {"name": "archived_at", "type": "int64", "facet": true, "index": true, "sort": true, "optional": true}, {"name": "full_text", "type": "string", "facet": false, "index": true}], "default_sorting_field": "updated_at", "symbols_to_index": ["-", "_", ".", "@", "/"]}