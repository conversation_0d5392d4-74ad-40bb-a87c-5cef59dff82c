import * as fs from 'fs/promises';
import * as path from 'path';
import { argv } from 'process';
import { TypesenseMigration } from './common';

class SchemaMigration extends TypesenseMigration {
  constructor(schemaPath: string) {
    super(schemaPath);
  }
}

export const runMigration = async () => {
  const args = argv.slice(2);
  const command = args?.[0];

  if (!command) {
    console.log('Usage: node index.js <command>');
    return;
  }

  if (command !== 'up' && command !== 'down') {
    console.log('Command must be either "up" or "down"');
    return;
  }

  try {
    const migrationsDir = path.join(__dirname, 'migrations');
    const files = await fs.readdir(migrationsDir);
    const jsonFiles = files.filter(file => file.endsWith('.json'));

    for (const file of jsonFiles) {
      const filePath = path.join(migrationsDir, file);
      console.log(`Processing ${file}...`);

      const migration = new SchemaMigration(filePath);
      await migration[command]();
    }

    console.log('All migrations completed successfully');
  } catch (err) {
    console.error('Error during migration:', err);
  }
};

runMigration();
