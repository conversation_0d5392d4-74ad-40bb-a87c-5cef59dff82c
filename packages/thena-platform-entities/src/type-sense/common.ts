import dotenv from 'dotenv';
import fs from 'fs';
import { Client } from 'typesense';
import { CollectionCreateSchema } from 'typesense/lib/Typesense/Collections';

dotenv.config();

export interface TypesenseCollectionSchema {
  name: string;
  fields: {
    name: string;
    type: string;
    facet?: boolean;
    index?: boolean;
    infix?: boolean;
    locale?: string;
    optional?: boolean;
    reference?: string;
    sort?: boolean;
    stem?: boolean;
    store?: boolean;
    default?: any;
    async_reference?: boolean;
  }[];
}

export abstract class TypesenseMigration {
  protected client: Client;
  protected collectionName: string;
  protected schema: CollectionCreateSchema;

  constructor(schemaPath: string) {
    const schemaContent = fs.readFileSync(schemaPath, 'utf8');
    if (!schemaContent || schemaContent.trim() === '') {
      this.schema = { name: '', fields: [] };
    } else {
      this.schema = JSON.parse(schemaContent);
    }
    this.collectionName = this.schema.name;
    this.client = new Client({
      nodes: [
        {
          host: process.env.TYPESENSE_HOST || 'localhost',
          port: parseInt(process.env.TYPESENSE_PORT || '443'),
          protocol: process.env.TYPESENSE_PROTOCOL || 'https',
        },
      ],
      apiKey: process.env.TYPESENSE_API_KEY || '',
      connectionTimeoutSeconds: 30,
    });
  }

  /**
   * Creates a new collection in Typesense
   * @param schema - Collection schema definition
   * @returns Promise with the created collection
   */
  protected async createCollection(schema: CollectionCreateSchema) {
    try {
      return await this.client.collections().create(schema);
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to create Typesense collection: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Deletes a collection from Typesense
   * @returns Promise with the deletion result
   */
  protected async deleteCollection() {
    try {
      return await this.client.collections(this.collectionName).delete();
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to delete Typesense collection: ${error.message}`);
      }
      throw error;
    }
  }

  public async up(): Promise<void> {
    try {
      if (!this.schema?.name || this.schema?.fields?.length === 0) {
        console.log('Skipping collection creation - schema is empty');
        return;
      }
      console.log('Creating collection', this.collectionName);
      await this.createCollection(this.schema);
      console.log('Collection created', this.collectionName);
    } catch (error) {
      console.log('Error creating collection', this.collectionName, error);
    }
  }

  public async down(): Promise<void> {
    try {
      if (!this.schema?.name || this.schema?.fields?.length === 0) {
        console.log('Skipping collection creation - schema is empty');
        return;
      }

      if (await this.client.collections(this.collectionName).retrieve()) {
        console.log('Deleting collection', this.collectionName);
        await this.deleteCollection();
        console.log('Collection deleted', this.collectionName);
      } else {
        console.log(`${this.collectionName} Collection does not exist`);
      }
    } catch (error) {
      console.log(`${this.collectionName} Error deleting collection`, error);
    }
  }
}
