import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { BusinessHoursConfig } from "../../business-hours/entities/business-hours-config.entity";
import {
  EntityType,
  SUPPORTED_TYPES,
} from "../../common/constants/annotator.constants";
import { FieldMetadata } from "../../common/decorators/field-metadata.decorator";
import { Organization } from "../../organizations/entities/organization.entity";
import { TeamUserRoutingStrategy } from "../constants/teams.constants";
import { Team } from "./team.entity";

/**
 * Flat object that contains all the explicit team configurations. This does not
 * include any metadata that is included in the team configuration entity.
 */
export interface ExplicitTeamConfigurations {
  /**
   * The timezone of the team.
   */
  timezone?: string;

  /**
   * The holidays of the team.
   */
  holidays?: string[];

  /**
   * The business hours configuration of the team.
   */
  businessHoursConfig: BusinessHoursConfig;

  /**
   * Whether the routing respects the timezone.
   */
  routingRespectsTimezone?: boolean;

  /**
   * Whether the routing respects the user timezone.
   */
  routingRespectsUserTimezone?: boolean;

  /**
   * Whether the routing respects the user availability.
   */
  routingRespectsUserAvailability?: boolean;

  /**
   * The user routing strategy of the team.
   */
  userRoutingStrategy?: TeamUserRoutingStrategy;
}

@Entity("team_configurations")
export class TeamConfiguration {
  @PrimaryGeneratedColumn({ type: "bigint" })
  id: string;

  @OneToOne(() => Team, (team) => team.configuration, { onDelete: "CASCADE" })
  @JoinColumn({ name: "team_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Team",
    description: "The team that this configuration belongs to",
    expression: "team",
    standard: true,
    constraints: {
      lookupType: "oneToOne",
      relatedEntityType: EntityType.TEAM,
    },
  })
  team: Team;

  @Index({ unique: true })
  @Column({ name: "team_id" })
  teamId: string;

  @ManyToOne(() => Team, { nullable: true })
  @JoinColumn({ name: "fallback_sub_team_id" })
  fallbackSubTeam: Team;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: "organization_id" })
  organization: Organization;

  @Index()
  @Column({ name: "organization_id" })
  organizationId: string;

  @Index()
  @Column({ type: "varchar", length: 100, default: "UTC" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.STRING,
    label: "Timezone",
    description: "The timezone of the team",
    expression: "timezone",
    standard: true,
  })
  timezone?: string;

  @OneToOne(() => BusinessHoursConfig, (config) => config.team)
  @JoinColumn({ name: "business_hours_config_id" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "Business Hours Config",
    description: "The business hours configuration of the team",
    expression: "businessHoursConfig",
    standard: true,
    constraints: {
      lookupType: "oneToOne",
      relatedEntityType: EntityType.BUSINESS_HOURS_CONFIG,
    },
  })
  businessHoursConfig: BusinessHoursConfig;

  @Index()
  @Column({ name: "business_hours_config_id" })
  businessHoursConfigId: string;

  @Column({ type: "boolean", default: true, name: "routing_respects_timezone" })
  routingRespectsTimezone: boolean;

  @Column({
    type: "boolean",
    default: true,
    name: "routing_respects_user_timezone",
  })
  routingRespectsUserTimezone: boolean;

  @Column({
    type: "boolean",
    default: true,
    name: "routing_respects_user_availability",
  })
  routingRespectsUserAvailability: boolean;

  @Column({
    type: "boolean",
    default: true,
    name: "routing_respects_user_capacity",
  })
  routingRespectsUserCapacity: boolean;

  @Column({
    type: "varchar",
    length: 60,
    nullable: true,
    name: "user_routing_strategy",
  })
  @FieldMetadata({
    type: SUPPORTED_TYPES.LOOKUP,
    label: "User Routing Strategy",
    description: "The user routing strategy of the team",
    expression: "userRoutingStrategy",
    standard: true,
    constraints: {
      options: Object.entries(TeamUserRoutingStrategy).map(([key, value]) => ({
        label: key.charAt(0).toUpperCase() + key.slice(1).toLowerCase(),
        value,
      })),
    },
  })
  userRoutingStrategy: TeamUserRoutingStrategy;

  @Column({ type: "varchar", length: 10, array: true, nullable: true })
  @FieldMetadata({
    type: SUPPORTED_TYPES.ARRAY,
    label: "Holidays",
    description: "The holidays of the team",
    expression: "holidays",
    standard: true,
    constraints: {
      itemsType: "string",
    },
  })
  holidays?: string[];

  @DeleteDateColumn({ name: "deleted_at", type: "timestamptz", nullable: true })
  deletedAt?: Date;

  @CreateDateColumn({ name: "created_at", type: "timestamptz" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Created At",
    description: "The created at date of the team configuration",
    expression: "createdAt",
    standard: true,
  })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamptz" })
  @FieldMetadata({
    type: SUPPORTED_TYPES.DATE,
    label: "Updated At",
    description: "The updated at date of the team configuration",
    expression: "updatedAt",
    standard: true,
  })
  updatedAt: Date;

  /**
   * Returns the team configuration object
   * @returns The team configuration object
   */
  public get configuration(): ExplicitTeamConfigurations {
    return {
      timezone: this.timezone,
      holidays: this.holidays,
      businessHoursConfig: this.businessHoursConfig,
      userRoutingStrategy: this.userRoutingStrategy,
      routingRespectsTimezone: this.routingRespectsTimezone,
      routingRespectsUserTimezone: this.routingRespectsUserTimezone,
      routingRespectsUserAvailability: this.routingRespectsUserAvailability,
    };
  }
}
