import { MigrationInterface, QueryRunner } from "typeorm";

export class AddBillingEntities1748966237174 implements MigrationInterface {
  name = "AddBillingEntities1748966237174";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "subscriptions" ("id" BIGSERIAL NOT NULL, "organization_id" bigint NOT NULL, "started_at" TIMESTAMP WITH TIME ZONE NOT NULL, "ends_at" TIMESTAMP WITH TIME ZONE, "plan_type" character varying NOT NULL DEFAULT 'free', "billing_cycle" character varying NOT NULL DEFAULT 'monthly', "stripe_customer_id" character varying, "stripe_subscription_id" character varying, "stripe_subscription_status" character varying, "stripe_subscription_item_id" character varying, "stripe_subscription_item_price" character varying, "metadata" jsonb NOT NULL DEFAULT '{}', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_a87248d73155605cf782be9ee5e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9ea1509175fa294fc64d43a9fe" ON "subscriptions" ("organization_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_9ea1509175fa294fc64d43a9fe6" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_9ea1509175fa294fc64d43a9fe6"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9ea1509175fa294fc64d43a9fe"`,
    );
    await queryRunner.query(`DROP TABLE "subscriptions"`);
  }
}
