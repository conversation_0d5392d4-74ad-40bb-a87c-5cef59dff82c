import { MigrationInterface, QueryRunner } from "typeorm";

export class AlterRequestSourceAddManual1748537093362
  implements MigrationInterface
{
  name = "AlterRequestSourceAddManual1748537093362";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "ticket" DROP CONSTRAINT "CHK_a43d0ca77da459bb93edbd9da9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ticket" ADD CONSTRAINT "CHK_0d814364214aec72f8652c1a10" CHECK ("source" IN ('unknown', 'web', 'api', 'mobile', 'email', 'slack', 'integration', 'grpc', 'workflow', 'vendor-portal', 'customer-portal', 'ai-agent', 'ms-teams', 'web-chat', 'manual'))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "ticket" DROP CONSTRAINT "CHK_0d814364214aec72f8652c1a10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ticket" ADD CONSTRAINT "CHK_a43d0ca77da459bb93edbd9da9" CHECK (((source)::text = ANY ((ARRAY['unknown'::character varying, 'web'::character varying, 'api'::character varying, 'mobile'::character varying, 'email'::character varying, 'slack'::character varying, 'integration'::character varying, 'grpc'::character varying, 'workflow'::character varying, 'vendor-portal'::character varying, 'customer-portal'::character varying, 'ai-agent'::character varying, 'ms-teams'::character varying, 'web-chat'::character varying])::text[])))`,
    );
  }
}
