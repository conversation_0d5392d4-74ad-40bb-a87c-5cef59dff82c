import { EntityType } from "../../common";

export interface WorkflowAnnotation {
  entityType?: EntityType;
  requiredFields?: Record<string, unknown>;
  pathToAnnotate?: string;
  relations: string[];
}

export enum WorkflowType {
  /** Workflows that are manually initiated by users */
  MANUAL = "MANUAL",
  /** Workflows that are created by the system */
  AUTOMATED = "AUTOMATED",
}

export enum WorkflowSubType {
  /** Standard workflow processes. Manually created by users */
  WORKFLOW = "WORKFLOW",
  /** Workflows created by Auto Responder service */
  AUTO_RESPONDER = "AUTO_RESPONDER",
  /** Workflows created by agent-studio service */
  AI_AGENT = "AI_AGENT",
  /** Workflows created by the registered apps */
  REGISTERED_APP = "REGISTERED_APP",
}
