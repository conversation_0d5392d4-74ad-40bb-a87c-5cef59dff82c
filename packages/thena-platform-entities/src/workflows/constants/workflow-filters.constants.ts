/**
 * Array of supported filter operators for workflow conditions.
 * Each operator defines a specific comparison operation.
 */
export const FILTER_OPERATORS = [
  "~eq", // Equal to
  "~neq", // Not equal to
  "~in", // In array
  "~nin", // Not in array
  "~gte", // Greater than or equal to
  "~gt", // Greater than
  "~lte", // Less than or equal to
  "~lt", // Less than
  "~regex", // Regular expression match
  "~nregex", // Does not match regular expression
  "~starts", // Starts with
  "~nstarts", // Does not start with
  "~ends", // Ends with
  "~nends", // Does not end with
  "~contains", // Contains substring
  "~ncontains", // Does not contain substring
  "~isnull", // Is null
  "~isempty", // Is empty
] as const;

/**
 * Array of supported logical operators for combining filter conditions.
 */
export const LOGICAL_OPERATORS = ["~and", "~or"] as const;

/**
 * Type representing a valid filter operator.
 * Derived from the FILTER_OPERATORS array.
 */
export type FilterOperator = (typeof FILTER_OPERATORS)[number];

/**
 * Type representing a valid logical operator.
 * Derived from the LOGICAL_OPERATORS array.
 */
export type LogicalOperator = (typeof LOGICAL_OPERATORS)[number];

/**
 * Type representing a filter condition.
 * Can be either a comparison using a filter operator or a (nested) logical combination of conditions.
 */
export type FilterCondition =
  | {
      [K in FilterOperator]?: unknown;
    }
  | {
      [K in LogicalOperator]?: FilterCondition[];
    };

/**
 * Type representing a set of event filters.
 * Maps event field names to their corresponding filter conditions.
 */
export type EventFilters = Record<string, FilterCondition> | FilterCondition;
