import { Column, Entity, Index, JoinColumn, ManyToOne } from "typeorm";

import {
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Organization } from "../../organizations/entities/organization.entity";

export enum SubscriptionPlanType {
  FREE = "free",
  STANDARD = "standard",
  ENTERPRISE = "enterprise",
}

export enum SubscriptionBillingCycle {
  MONTHLY = "monthly",
  YEARLY = "yearly",
}

@Entity({ name: "subscriptions" })
export class Subscription {
  @PrimaryGeneratedColumn("increment", { type: "bigint" })
  id: string;

  @ManyToOne(() => Organization, { onDelete: "CASCADE" })
  @JoinColumn({ name: "organization_id" })
  organization: Organization;

  @Column({ type: "bigint", name: "organization_id" })
  @Index()
  organizationId: string;

  @Column({ type: "timestamptz", name: "started_at" })
  startedAt: Date;

  @Column({ type: "timestamptz", name: "ends_at", nullable: true })
  endsAt: Date;

  @Column({
    type: "varchar",
    name: "plan_type",
    enum: SubscriptionPlanType,
    default: SubscriptionPlanType.FREE,
  })
  planType: SubscriptionPlanType;

  @Column({
    type: "varchar",
    name: "billing_cycle",
    enum: SubscriptionBillingCycle,
    default: SubscriptionBillingCycle.MONTHLY,
  })
  billingCycle: SubscriptionBillingCycle;

  @Column({ type: "varchar", name: "stripe_customer_id", nullable: true })
  stripeCustomerId: string;

  @Column({ type: "varchar", name: "stripe_subscription_id", nullable: true })
  stripeSubscriptionId: string;

  @Column({
    type: "varchar",
    name: "stripe_subscription_status",
    nullable: true,
  })
  stripeSubscriptionStatus: string;

  @Column({
    type: "varchar",
    name: "stripe_subscription_item_id",
    nullable: true,
  })
  stripeSubscriptionItemId: string;

  @Column({
    type: "varchar",
    name: "stripe_subscription_item_price",
    nullable: true,
  })
  stripeSubscriptionItemPrice: string;

  @Column({ type: "jsonb", name: "metadata", default: {} })
  metadata: Record<string, unknown>;

  @CreateDateColumn({
    name: "created_at",
    type: "timestamptz",
  })
  createdAt: Date;

  @UpdateDateColumn({
    name: "updated_at",
    type: "timestamptz",
  })
  updatedAt: Date;

  @DeleteDateColumn({
    name: "deleted_at",
    type: "timestamptz",
    nullable: true,
  })
  deletedAt: Date;
}
