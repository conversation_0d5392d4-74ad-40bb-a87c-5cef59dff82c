export interface AcquireLockOptions {
  /** The unique key for the lock */
  key: string;
  /** Time-to-live for the lock in seconds */
  ttl: number;
  /** Maximum number of retry attempts (default: 3) */
  maxRetries?: number;
  /** Delay between retry attempts in milliseconds (default: 100) */
  retryDelay?: number;
}

export interface ReleaseLockOptions {
  /** The unique key for the lock */
  key: string;
  /** The lock value/token returned when acquiring the lock */
  lockValue?: string;
}

export interface LockAcquisitionResult {
  /** Whether the lock was successfully acquired */
  acquired: boolean;
  /** The lock value/token if acquired (used for safe release) */
  lockValue?: string;
  /** Error message if acquisition failed */
  error?: string;
}

export interface ILockProvider {
  /**
   * Attempts to acquire a distributed lock with retry logic
   * @param options - Lock acquisition configuration
   * @returns Promise resolving to lock acquisition result
   */
  acquireLock(options: AcquireLockOptions): Promise<LockAcquisitionResult>;

  /**
   * Releases a distributed lock
   * @param options - Lock release configuration
   * @returns Promise resolving to true if lock was released, false otherwise
   */
  releaseLock(options: ReleaseLockOptions): Promise<boolean>;
}
