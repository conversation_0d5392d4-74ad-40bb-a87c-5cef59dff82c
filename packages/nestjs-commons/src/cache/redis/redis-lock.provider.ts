import {
  Inject,
  Injectable,
  OnM<PERSON>ule<PERSON><PERSON><PERSON>,
  OnModuleInit,
} from "@nestjs/common";
import { Cluster, Redis, RedisOptions } from "ioredis";
import {
  AcquireLockOptions,
  ILockProvider,
  LockAcquisitionResult,
  ReleaseLockOptions,
} from "../lock.interface";
import { REDIS_CONFIG_TOKEN } from "./redis-cache.constants";

@Injectable()
export class RedisLockProvider
  implements ILockProvider, OnModuleInit, OnModuleDestroy
{
  private static instance: Redis | Cluster;
  private redis: Redis | Cluster;

  constructor(
    @Inject(REDIS_CONFIG_TOKEN)
    private readonly options: RedisOptions,
  ) {}

  async onModuleInit(): Promise<void> {
    if (!RedisLockProvider.instance) {
      const config: RedisOptions = {
        ...this.options,
        maxRetriesPerRequest: 1,
        enableReadyCheck: false,
        lazyConnect: true,
        retryStrategy: (times) => {
          if (times > 2) return null;
          return Math.min(times * 100, 2000);
        },
      };

      RedisLockProvider.instance = new Redis(config);

      RedisLockProvider.instance.on("error", (error) => {
        console.error("Redis connection error:", error);
      });

      RedisLockProvider.instance.on("connect", () => {
        console.log("Successfully connected to Redis");
      });

      RedisLockProvider.instance.on("ready", () => {
        console.log("Redis client ready");
      });

      RedisLockProvider.instance.on("end", () => {
        console.log("Redis connection closed");
        RedisLockProvider.instance = null;
      });

      try {
        await RedisLockProvider.instance.connect();
      } catch (error) {
        console.error("Failed to connect to Redis:", error);
        throw error;
      }
    }

    this.redis = RedisLockProvider.instance;
  }

  async onModuleDestroy(): Promise<void> {
    if (RedisLockProvider.instance) {
      await RedisLockProvider.instance.quit();
      RedisLockProvider.instance = null;
    }
  }

  /**
   * Attempts to acquire a lock with retries.
   *
   * @param options {@link AcquireLockOptions} - Lock acquisition configuration
   * @returns {@link LockAcquisitionResult} lock acquisition result
   */
  async acquireLock(
    options: AcquireLockOptions,
  ): Promise<LockAcquisitionResult> {
    const { key, ttl, maxRetries = 5, retryDelay = 100 } = options;
    const lockValue = `${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    let lastError: string | undefined;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await this.redis.set(key, lockValue, "EX", ttl, "NX"); // NX - Only if not exists

        if (result === "OK") {
          return {
            acquired: true,
            lockValue,
          };
        }

        if (attempt < maxRetries) {
          // Exponential backoff
          await this.sleep(retryDelay * (attempt + 1));
        }

        lastError = `Lock already exists for key: ${key}`;
      } catch (error) {
        lastError = `Redis error during lock acquisition: ${error.message}`;

        if (attempt < maxRetries) {
          // Exponential backoff
          await this.sleep(retryDelay * (attempt + 1));
        }
      }
    }

    return {
      acquired: false,
      error:
        lastError || `Failed to acquire lock after ${maxRetries + 1} attempts`,
    };
  }

  /**
   * Releases a lock
   * Uses Lua script to ensure only the lock owner can release it.
   *
   * @param options {@link ReleaseLockOptions} - Lock release configuration
   * @returns {@link boolean} true if lock was released, false otherwise
   */
  async releaseLock(options: ReleaseLockOptions): Promise<boolean> {
    const { key, lockValue } = options;

    // Verifies lock ownership and releases if successful
    const releaseLockScript = `
      if redis.call('GET', KEYS[1]) == ARGV[1] then
        return redis.call('DEL', KEYS[1])
      else
        return 0
      end
    `;

    try {
      const result = (await this.redis.eval(
        releaseLockScript,
        1,
        key,
        lockValue || "",
      )) as number;

      return result === 1;
    } catch (error) {
      console.error(`Error releasing lock for key ${key}:`, error.message);
      return false;
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
