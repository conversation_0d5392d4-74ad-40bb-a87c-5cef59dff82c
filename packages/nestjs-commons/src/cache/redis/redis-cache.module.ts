import { DynamicModule, Module } from "@nestjs/common";
import { RedisOptions } from "ioredis";
import { REDIS_CONFIG_TOKEN } from "./redis-cache.constants";
import { RedisCacheProvider } from "./redis-cache.provider";
import { RedisLockProvider } from "./redis-lock.provider";

@Module({})
export class RedisCacheModule {
  static register(config: RedisOptions): DynamicModule {
    return {
      module: RedisCacheModule,
      providers: [
        {
          provide: REDIS_CONFIG_TOKEN,
          useValue: config,
        },
        RedisCacheProvider,
        RedisLockProvider,
      ],
      exports: [REDIS_CONFIG_TOKEN, RedisCacheProvider, RedisLockProvider],
      global: true,
    };
  }

  static registerAsync(options: {
    imports?: any[];
    useFactory: (...args: any[]) => RedisOptions;
    inject?: any[];
  }): DynamicModule {
    return {
      module: RedisCacheModule,
      imports: options.imports || [],
      providers: [
        {
          provide: REDIS_CONFIG_TOKEN,
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        RedisCacheProvider,
        RedisLockProvider,
      ],
      exports: [REDIS_CONFIG_TOKEN, RedisCacheProvider, RedisLockProvider],
      global: true,
    };
  }
}
