import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Inject,
} from "@nestjs/common";
import { FastifyRequest } from "fastify";
import { shouldExcludeFromSentry } from "../errors";
import { SENTRY_SERVICE_TOKEN, SentryService } from "./sentry-alerts.filter";

@Catch()
export class SentryExceptionFilter implements ExceptionFilter {
  constructor(
    @Inject(SENTRY_SERVICE_TOKEN) private readonly sentryService: SentryService,
  ) {}

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<FastifyRequest>();

    const isNotFoundException =
      exception instanceof HttpException &&
      exception?.getStatus() === HttpStatus.NOT_FOUND;

    // If the exception is a 404, throw it
    if (isNotFoundException) {
      throw exception;
    }

    // Only capture exceptions that shouldn't be excluded from Sentry
    if (!(exception instanceof Error) || !shouldExcludeFromSentry(exception)) {
      this.sentryService.captureException(exception, {
        url: request.url,
        method: request.method,
        body: request.body,
        query: request.query,
        params: request.params,
        errorType: exception instanceof HttpException ? "handled" : "unhandled",
      });
    }

    throw exception;
  }
}
