import { Inject, Injectable } from "@nestjs/common";
import * as Sen<PERSON> from "@sentry/node";
import * as rTracer from "cls-rtracer";
import { shouldExcludeFromSentry } from "../errors";
import { ILogger } from "../logger";

export const SENTRY_SERVICE_TOKEN = "Sentry";

@Injectable()
export class SentryService {
  private readonly sentryDsn: string;
  private readonly appEnv: string;
  private readonly appTag: string;
  private readonly serviceTag: string;

  constructor(
    @Inject() private readonly logger: ILogger,
    sentryDsn: string,
    appEnv: string,
    appTag: string,
    serviceTag: string,
  ) {
    this.sentryDsn = sentryDsn;
    this.appEnv = appEnv || "development";
    this.appTag = appTag;
    this.serviceTag = serviceTag;

    Sentry.init({
      dsn: this.sentryDsn,
      environment: this.appEnv,
    });
  }

  captureException(error: Error, meta: Record<string, any> = {}) {
    if (shouldExcludeFromSentry(error)) {
      return;
    }

    this.logger.error(
      `[SentryExceptionCaptured] Error: ${
        error.message
      } metadata: ${JSON.stringify(meta)}`,
    );

    try {
      Sentry.withScope((scope) => {
        scope.setLevel("error");
        scope.setTag("app", this.appTag);
        scope.setTag("service", this.serviceTag);
        scope.setTag("deployment", this.appEnv);

        if (meta?.tag) {
          scope.setTransactionName(meta.tag);
          scope.setTag(meta.tag, true);
        }

        if (meta?.errorSubType) {
          scope.setTag("errorSubType", meta.errorSubType);
        }

        const rid = rTracer.id() as {
          reqId: string;
          context: Record<string, string>;
        };
        if (rid) {
          scope.setTag("requestId", rid.reqId || "");
          if (rid.context) {
            Object.keys(rid.context).forEach((key) => {
              scope.setTag(`context.${key}`, rid.context[key]);
            });
          }
        }

        Object.keys(meta).forEach((key) => {
          scope.setExtra(key, meta[key]);
        });

        Sentry.captureException(error);
      });
    } catch (captureError) {
      this.logger.error(
        `[Sentry] Failed to capture exception: ${captureError.message}`,
      );
    }
  }
}
