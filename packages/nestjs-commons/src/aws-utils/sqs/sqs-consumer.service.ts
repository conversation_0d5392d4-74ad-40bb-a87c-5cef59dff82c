import {
  DeleteMessageCommand,
  ReceiveMessageCommand,
  SQSClient,
  SQSClientConfig,
} from "@aws-sdk/client-sqs";
import {
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from "@nestjs/common";
import * as rTracer from "cls-rtracer";
import { v4 as uuidv4 } from "uuid";
import { shouldExcludeFromSentry } from "../../errors";
import { SentryService } from "../../filters/sentry-alerts.filter";
import { ILogger } from "../../logger";
import { transformSQSMessage } from "./helper";
import { MessageHandler, SQSConfig, SQSMessage } from "./sqs.interfaces";

@Injectable()
export class SQSConsumerService implements OnModuleInit, OnModuleDestroy {
  private sqsClient: SQSClient;
  private isPolling: boolean = false;
  private handler: MessageHandler;

  constructor(
    @Inject("SQSConfig") private readonly config: SQSConfig,
    @Inject("sentry") private readonly sentry: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  onModuleInit() {
    this.initializeSQSClient();
  }

  onModuleDestroy() {
    this.stopConsumer();
  }

  private initializeSQSClient() {
    const clientConfig: SQSClientConfig = {
      region: this.config.region,
      credentials: this.config.credentials,
    };
    this.sqsClient = new SQSClient(clientConfig);
    this.logger.log(
      `[SQS][CONSUMER] SQS consumer client initialized for queueUrl: ${this.config.queueUrl}`,
    );
  }

  async receiveMessages(): Promise<SQSMessage[]> {
    const spanId = "[SQS][CONSUMER][RECEIVE_MESSAGES]";
    const command = new ReceiveMessageCommand({
      QueueUrl: this.config.queueUrl,
      MaxNumberOfMessages: 10,
      WaitTimeSeconds: 20,
      MessageAttributeNames: ["All"],
    });

    try {
      const response = await this.sqsClient.send(command);
      if (response.Messages && response.Messages.length > 0) {
        return response.Messages.map((message) => transformSQSMessage(message));
      }
      return [];
    } catch (error) {
      this.logger.error(
        `${spanId} Error receiving messages from SQS > Error message: ${error.message},
         Error stack: ${error.stack}`,
      );
      this.sentry.captureException(error, {
        tag: "SQS_CONSUMER",
        fn: "receiveMessages",
        queueUrl: this.config.queueUrl,
        error: error,
        stack: error.stack,
      });
    }
  }

  async deleteMessage(messageId: string): Promise<void> {
    const spanId = "[SQS][CONSUMER][DELETE_MESSAGE]";
    const command = new DeleteMessageCommand({
      QueueUrl: this.config.queueUrl,
      ReceiptHandle: messageId,
    });

    try {
      await this.sqsClient.send(command);
    } catch (error) {
      this.logger.error(
        `${spanId} Error deleting message from SQS > Error message: ${error.message}, 
        Error stack: ${error.stack}`,
      );
      this.sentry.captureException(error, {
        tag: "SQS_CONSUMER",
        fn: "deleteMessage",
        queueUrl: this.config.queueUrl,
        error: error,
        messageId: messageId,
        stack: error.stack,
      });
    }
  }

  startConsumer(handler: MessageHandler): void {
    this.handler = handler;
    this.isPolling = true;
    this.logger.log(
      `[SQS][CONSUMER] Starting consumer for queueUrl: ${this.config.queueUrl}`,
    );
    this.poll();
  }

  private async poll(): Promise<void> {
    while (this.isPolling) {
      try {
        const messages = (await this.receiveMessages()) || [];
        if (messages.length > 0) {
          messages.forEach((message) => this.processMessage(message));
        }
      } catch (error) {
        this.logger.error(
          `[SQS][CONSUMER][POLL] Error polling messages, queueUrl: ${this.config.queueUrl} >
           Error message: ${error.message} > Error stack: ${error.stack}`,
        );
        this.sentry.captureException(error, {
          tag: "SQS_CONSUMER",
          fn: "poll",
          queueUrl: this.config.queueUrl,
          error: error,
          stack: error.stack,
        });
      }
    }
  }

  private async processMessage(message: SQSMessage): Promise<void> {
    const spanId = "[SQS][CONSUMER][PROCESS_MESSAGE]";
    try {
      await rTracer.runWithId(
        async () => {
          await this.handler(message);
          await this.deleteMessage(message.id);
        },
        {
          reqId: message.messageAttributes?.event_id ?? uuidv4(),
          context: {
            eventType: message.messageAttributes.event_name,
            userId: message.messageAttributes?.context_user_id,
            orgId: message.messageAttributes?.context_organization_id,
          },
        },
      );
    } catch (error) {
      this.logger.error(
        `${spanId} Error processing message, messageId: ${message.id}, messageBody: ${message.message}
         > Error message: ${error.message} > Error stack: ${error.stack}`,
      );

      if (!(error instanceof Error) || !shouldExcludeFromSentry(error)) {
        this.sentry.captureException(error, {
          tag: "SQS_CONSUMER",
          fn: "processMessage",
          queueUrl: this.config.queueUrl,
          error: error,
          messageId: message.id,
          messageBody: message.message,
          stack: error.stack,
        });
      }
    }
  }

  stopConsumer(): void {
    this.isPolling = false;
    this.logger.log(
      `[SQS][CONSUMER] Stopping consumer for queueUrl: ${this.config.queueUrl}`,
    );
  }
}
