import {
    CanActivate,
    ExecutionContext,
    Injectable,
    UnauthorizedException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import * as rTracer from "cls-rtracer";
import { FastifyRequest } from "fastify";
import { IS_PUBLIC_KEY } from "../../decorators/public.decorator";
import { ApiKeyAuthStrategy } from "./strategies/http/api-key.http.strategy";
import { BearerTokenHttpStrategy } from "./strategies/http/bearer.token.http.strategy";

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly apiKeyStrategy: ApiKeyAuthStrategy,
    private readonly bearerTokenStrategy: BearerTokenHttpStrategy,
    private readonly reflector: Reflector,
  ) {}

  /**
   * Authenticates the request using the API key or Bearer token strategy.
   * @param context The execution context.
   * @returns A promise that resolves to a boolean.
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>();

    try {
      // Check if route is public
      const isPublic = this.reflector.getAllAndOverride(IS_PUBLIC_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);

      // If the route is public, allow access
      // User Object is not returned as its not needed.
      if (isPublic) return true;

      const user = await this.authenticatedRequests(request);
      request.user = user;

      // This is for logger context.
      const traceId = rTracer.id() as {
        reqId: string;
        context: Record<string, any>;
      };
      if (traceId && traceId.context) {
        traceId.context.userId = user?.uid;
        traceId.context.orgId = user?.orgUid;
      }

      return true;
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  /**
   * Authenticates the request using the API key or Bearer token strategy.
   * @param request The request object.
   * @returns A promise that resolves to a User object.
   */
  private async authenticatedRequests(request: FastifyRequest) {
    if (request.headers["x-api-key"]) {
      return await this.apiKeyStrategy.authenticate(request);
    }

    if (request.headers["authorization"]) {
      return await this.bearerTokenStrategy.authenticate(request);
    }

    throw new UnauthorizedException(
      "Authentication failed | No API key or Bearer token provided",
    );
  }
}
