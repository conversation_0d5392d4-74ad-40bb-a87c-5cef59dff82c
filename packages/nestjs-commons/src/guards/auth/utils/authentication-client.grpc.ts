import { Inject, Optional } from "@nestjs/common";
import { authentication } from "@repo/shared-proto";
import { BaseGrpcClient, LOGGER_TOKEN } from "../../../grpc";
import { ILogger } from "../../../logger";
import { User } from "../interface/user.interface";

export const AUTH_GRPC_SERVICE_URL_TOKEN = "AUTH_GRPC_SERVICE_URL_TOKEN";

export class AuthenticationGrpcClient extends BaseGrpcClient {
  constructor(
    // Inject the logger
    @Optional() @Inject(LOGGER_TOKEN) logger: ILogger | undefined,

    // Inject the service url
    @Optional()
    @Inject(AUTH_GRPC_SERVICE_URL_TOKEN)
    private readonly serviceUrl: string | undefined,
  ) {
    super(logger, "authentication");
    this.initializeClient("authentication", "AuthenticationService");
  }

  getServiceUrl() {
    return this.serviceUrl ?? "0.0.0.0:50052";
  }

  async getUser(orgId: string, token?: string): Promise<User> {
    const response = await this.makeGrpcRequest<
      authentication.ValidateAndGetUserRequest,
      authentication.ValidateAndGetUserResponse
    >("ValidateAndGetUser", {}, { authorization: token, org_id: orgId });

    return {
      authId: response.authId,
      sub: response.sub,
      uid: response.uid,
      email: response.email,
      userName: response.email.split("@")[0],
      userType: response.userType,
      scopes: [],
      orgId: response.orgId,
      orgUid: response.orgUid,
      orgTier: authentication.OrganizationTier[response.orgTier].toString(),
      metadata: {
        userType: response.userType,
        timezone: response.timezone,
      },
    };
  }

  async getUserByInternalIds(userId: string, orgId: string): Promise<User> {
    const response = await this.makeGrpcRequest<
      authentication.ValidateAndGetUserByInternalIdsRequest,
      authentication.ValidateAndGetUserByInternalIdsResponse
    >(
      "ValidateAndGetUserByInternalIds",
      { userId, orgId },
      { user_id: userId, org_id: orgId },
    );

    return {
      authId: response.authId,
      sub: response.sub,
      uid: response.uid,
      email: response.email,
      userName: response.email.split("@")[0],
      userType: response.userType,
      scopes: [],
      orgId: response.orgId,
      orgUid: response.orgUid,
      orgTier: authentication.OrganizationTier[response.orgTier].toString(),
      metadata: {
        userType: response.userType,
        timezone: response.timezone,
      },
    };
  }

  async validateKey(apiKey: string): Promise<User> {
    const response = await this.makeGrpcRequest<
      authentication.ValidateAndGetUserByApiKeyRequest,
      authentication.ValidateAndGetUserByApiKeyResponse
    >("ValidateAndGetUserByApiKey", { apiKey }, {});

    return {
      authId: response.authId,
      sub: response.sub,
      uid: response.uid,
      email: response.email,
      userName: response.email.split("@")[0],
      userType: response.userType,
      scopes: [],
      orgId: response.orgId,
      orgUid: response.orgUid,
      orgTier: authentication.OrganizationTier[response.orgTier].toString(),
      metadata: {
        userType: response.userType,
        timezone: response.timezone,
      },
      // Removed isPrivilegedApp field - platform handles this via gRPC when needed
    };
  }
}
