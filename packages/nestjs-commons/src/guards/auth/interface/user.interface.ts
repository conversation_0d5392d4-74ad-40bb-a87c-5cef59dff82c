/**
 * The user interface
 */
export interface User {
  /**
   * The primary key of the user in the auth.users table
   */
  authId: string;

  /**
   * The primary key of the user in the public.users table
   */
  sub: string;

  /**
   * The uid of the user in the public.users table
   */
  uid: string;

  /**
   * The email of the user
   */
  email: string;

  /**
   * The name of the user
   */
  userName: string;

  /**
   * The type of the user
   */
  userType: string;

  /**
   * The scopes of the user
   */
  scopes: string[];

  /**
   * The primary key of the organization the user belongs to
   */
  orgId: string;

  /**
   * The uid of the organization the user belongs to
   */
  orgUid: string;

  /**
   * The tier of the organization the user belongs to
   */
  orgTier: string;

  /**
   * The metadata of the user
   */
  metadata?: Record<string, any>;

  /**
   * The token of the user
   */
  token?: string;

  // Removed isPrivilegedApp field - platform handles this via gRPC when needed
}
