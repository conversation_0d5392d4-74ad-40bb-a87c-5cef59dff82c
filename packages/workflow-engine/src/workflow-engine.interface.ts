export interface WorkflowEngine {
  initializeClient(): Promise<void>;
  initializeWorker(): Promise<void>;
  startWorker(): Promise<void>;
  executeWorkflow(
    workflowName: string,
    workflowId: string,
    args: any[],
  ): Promise<any>;
  registerWorkflow(
    name: string,
    workflowFn: (...args: any[]) => Promise<any>,
  ): void;
  registerActivity(
    name: string,
    activityFn: (...args: any[]) => Promise<any>,
  ): void;
  shutdown(): Promise<void>;
}
