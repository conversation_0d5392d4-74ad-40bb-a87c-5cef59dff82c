import { Inject } from "@nestjs/common";
import {
  Connection,
  IllegalStateError,
  WorkflowClient,
} from "@temporalio/client";
import { NativeConnection, Worker, WorkflowBundle } from "@temporalio/worker";

import { ILogger } from "@repo/nestjs-commons/logger";
import { WorkflowEngine } from "../../workflow-engine.interface";

/**
 * TemporalEngine is an implementation of the WorkflowEngine interface that uses Temporal.
 */
class TemporalEngine implements WorkflowEngine {
  private client: WorkflowClient;
  private worker: Worker;
  private workflows: Map<string, (...args: any[]) => Promise<any>> = new Map();
  private activities: Map<string, (...args: any[]) => Promise<any>> = new Map();

  constructor(
    private connectionAddress: string,
    private accountId: string,
    private namespace: string,
    private apiKey: string,
    private taskQueue: string,
    @Inject("CustomLogger") private readonly logger: ILogger,
    private workflowBundle?: WorkflowBundle,
  ) {}

  /**
   * Initialize the Temporal client.
   * @returns {Promise<void>}
   */
  async initializeClient(): Promise<void> {
    this.logger.log(
      "[TemporalEngine][initializeClient] Initializing Temporal engine client...",
    );
    try {
      const connection = await Connection.connect({
        address: this.connectionAddress,
        tls: {},
        apiKey: this.apiKey,
        metadata: {
          "temporal-namespace": `${this.namespace}.${this.accountId}`,
        },
      });

      this.client = new WorkflowClient({
        connection,
        namespace: `${this.namespace}.${this.accountId}`,
      });

      this.logger.log(
        "[TemporalEngine][initializeClient] Temporal engine client initialized successfully",
      );
    } catch (error) {
      this.logger.error(
        `[TemporalEngine][initializeClient] Error initializing Temporal engine: ${error}`,
      );
      throw error;
    }
  }

  /**
   * Initialize the Temporal worker.
   * Requires registerWorkflows and registerActivities before initialization
   * @returns {Promise<void>}
   */
  async initializeWorker(): Promise<void> {
    this.logger.log(
      "[TemporalEngine][initializeWorker] Initializing Temporal engine worker...",
    );
    try {
      const nativeConnection = await NativeConnection.connect({
        address: this.connectionAddress,
        tls: {},
        apiKey: this.apiKey,
        metadata: {
          "temporal-namespace": `${this.namespace}.${this.accountId}`,
        },
      });

      this.worker = await Worker.create({
        connection: nativeConnection,
        workflowBundle: this.workflowBundle,
        activities: Object.fromEntries(this.activities),
        taskQueue: this.taskQueue,
        namespace: `${this.namespace}.${this.accountId}`,
      });

      this.logger.log(
        "[TemporalEngine][initializeWorker] Temporal engine initialized successfully",
      );
    } catch (error) {
      this.logger.error(
        `[TemporalEngine][initializeWorker] Error initializing Temporal engine: ${error}`,
      );
      throw error;
    }
  }

  async startWorker(): Promise<void> {
    if (!this.worker) {
      this.logger.error("[TemporalEngine][startWorker] Worker not initialized");
      throw new Error("Worker not initialized");
    }
    this.logger.log(
      "[TemporalEngine][startWorker] Starting Temporal worker...",
    );
    try {
      await this.worker.run();
    } catch (error) {
      this.logger.error(
        `[TemporalEngine][startWorker] Error starting Temporal worker: ${error}`,
      );
      throw error;
    }
  }

  /**
   * Execute a workflow.
   * @param workflowName The name of the workflow to execute.
   * @param workflowId The workflow ID.
   * @param args The arguments to pass to the workflow.
   * @returns The result of the workflow.
   */
  async executeWorkflow(workflowName: string, workflowId: string, args: any[]) {
    this.logger.log(
      `[TemporalEngine][executeWorkflow] Executing workflow ${workflowName} with args ${JSON.stringify(
        args,
      )}`,
    );
    const handle = await this.client.start(workflowName, {
      args,
      taskQueue: this.taskQueue,
      workflowId,
    });
    this.logger.log(
      `[TemporalEngine][executeWorkflow] Workflow ${workflowName} started with handle ${handle}`,
    );
    return handle;
  }

  /**
   * Register a workflow.
   * @param workflowName The name of the workflow to register.
   * @param workflow The workflow to register.
   */
  registerWorkflow(
    workflowName: string,
    workflow: (...args: any[]) => Promise<any>,
  ): void {
    this.logger.log(
      `[TemporalEngine][registerWorkflow] Registering workflow ${workflowName}`,
    );
    this.workflows.set(workflowName, workflow);
    this.logger.log(
      `[TemporalEngine][registerWorkflow] Workflow ${workflowName} registered`,
    );
  }

  /**
   * Register an activity.
   * @param activityName The name of the activity to register.
   * @param activity The activity to register.
   */
  registerActivity(
    activityName: string,
    activity: (...args: any[]) => Promise<any>,
  ): void {
    this.logger.log(
      `[TemporalEngine][registerActivity] Registering activity ${activityName}`,
    );
    this.activities.set(activityName, activity);
    this.logger.log(
      `[TemporalEngine][registerActivity] Activity ${activityName} registered`,
    );
  }

  async shutdown(): Promise<void> {
    try {
      this.worker.shutdown();
    } catch (error) {
      if (error instanceof IllegalStateError) {
        this.logger.log(
          "[TemporalEngine][shutdown] Shutting down Temporal engine...",
        );
        while (this.worker.getState().toString() !== "STOPPED") {
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
        return;
      }
      this.logger.error(
        `[TemporalEngine][shutdown] Error shutting down Temporal engine: ${error}`,
      );
      throw error;
    }
  }
}

export { TemporalEngine };
