syntax = "proto3";

package grpc.tickets.v1;

// Tickets service for tickets app.
service Tickets {
  // Assigns a ticket to an agent
  rpc AssignTicket (AssignTicketRequest) returns (AssignTicketResponse);

  // Compensates for assigning a ticket to an agent
  rpc CompensateAssignTicket (CompensateAssignTicketRequest) returns (AssignTicketResponse);

  // Create/Update a ticket
  rpc CreateTicket (CreateTicketRequest) returns (CommonTicketResponse);
  rpc UpdateTicket (UpdateTicketRequest) returns (CommonTicketResponse);
  rpc GetTicket (GetTicketRequest) returns (CommonTicketResponse);
  rpc GetTicketsWithCursor (GetTicketsWithCursorRequest) returns (GetTicketsWithCursorResponse);
  rpc ArchiveTicket (ArchiveTicketRequest) returns (CommonTicketResponse);
  rpc EscalateTicket (EscalateTicketRequest) returns (CommonTicketResponse);
}

// Ticket Annotator service
service TicketAnnotator {
  // Field Metadata APIs
  rpc GetTicketFieldMetadata (GetTicketFieldMetadataRequest) returns (GetTicketFieldMetadataResponse);
  rpc GetTicketStatusFieldMetadata (GetTicketStatusFieldMetadataRequest) returns (GetTicketStatusFieldMetadataResponse);
  rpc GetTicketTypeFieldMetadata (GetTicketTypeFieldMetadataRequest) returns (GetTicketTypeFieldMetadataResponse);
  rpc GetTicketPriorityFieldMetadata (GetTicketPriorityFieldMetadataRequest) returns (GetTicketPriorityFieldMetadataResponse);
  rpc GetTicketSentimentFieldMetadata (GetTicketSentimentFieldMetadataRequest) returns (GetTicketSentimentFieldMetadataResponse);

  // Entity Data APIs
  rpc GetTicketData (GetTicketDataRequest) returns (GetTicketDataResponse);
  rpc FilterTicketData (FilterTicketDataRequest) returns (GetTicketDataResponse);
  rpc GetTicketStatusData (GetTicketStatusDataRequest) returns (GetTicketStatusDataResponse);
  rpc GetTicketTypeData (GetTicketTypeDataRequest) returns (GetTicketTypeDataResponse);
  rpc GetTicketPriorityData (GetTicketPriorityDataRequest) returns (GetTicketPriorityDataResponse);
  rpc GetTicketSentimentData (GetTicketSentimentDataRequest) returns (GetTicketSentimentDataResponse);
}

// Request for assigning a ticket to an agent
message AssignTicketRequest {
  string executionId = 1;
  string ticketId = 2;
  string agentId = 3;
  string userId = 4;
  // Whether to unassign the ticket
  optional bool unassign = 5;
}

// Response for assigning a ticket to an agent
message AssignTicketResponse {
  bool success = 1;
  CommonTicketResponse ticket = 2;
}

message CompensateAssignTicketRequest {
  string executionId = 1;
}

// Request for getting a ticket
message GetTicketRequest {
  string id = 1;
}

// Request for creating a ticket
message CreateTicketRequest {
  // The title of the ticket
  string title = 1;

  // The email of the requestor
  string requestorEmail = 2;

  // The ID of the team
  string teamId = 3;

  // The ID of the account
  optional string accountId = 4;

  // The ID of the assigned agent
  optional string assignedAgentId = 5;

  // The description of the ticket
  optional string description = 6;

  // The due date of the ticket
  optional string dueDate = 7;

  // The ID of the status
  optional string statusId = 8;

  // The ID of the priority
  optional string priorityId = 9;

  // The ID of the type
  optional string typeId = 10;

  // The email of the submitter
  optional string submitterEmail = 11;

  // Whether the ticket is private
  optional bool isPrivate = 12;

  // The attachment URLs
  repeated string attachmentUrls = 13;

  // The source of the request
  optional string source = 14;

  // The AI generated title of the ticket
  optional string aiGeneratedTitle = 15;

  // The AI generated summary of the ticket
  optional string aiGeneratedSummary = 16;

  // Whether to perform routing
  optional bool performRouting = 17;

  // The ID of the form
  optional string formId = 18;

  // The metadata of the ticket
  optional string metadata = 19;

  // The status name to match against
  optional string statusName = 20;

  // The priority name to match against
  optional string priorityName = 21;

  // The custom field values
  repeated CustomFieldValue customFieldValues = 22;
}

message UpdateTicketRequest {
  string id = 1;

  // The title of the ticket
  optional string title = 2;

  // The ID of the sub-team
  optional string subTeamId = 3;

  // The ID of the account
  optional string accountId = 4;

  // The ID of the assigned agent
  optional string assignedAgentId = 5;

  // The description of the ticket
  optional string description = 6;

  // The due date of the ticket
  optional string dueDate = 7;

  // The ID of the status
  optional string statusId = 8;

  // The ID of the priority
  optional string priorityId = 9;

  // The ID of the type
  optional string typeId = 10;

  // The email of the submitter
  optional string submitterEmail = 11;

  // Whether the ticket is private
  optional bool isPrivate = 12;

  // The attachment URLs
  repeated string attachmentUrls = 13;

  // The AI generated title of the ticket
  optional string aiGeneratedTitle = 14;

  // The AI generated summary of the ticket
  optional string aiGeneratedSummary = 15;

  // The metadata of the ticket
  optional string metadata = 16;

  // The status name to match against
  optional string statusName = 17;

  // The priority name to match against
  optional string priorityName = 18;

  // The custom field values
  repeated CustomFieldValue customFieldValues = 19;
}

// Response for creating a ticket
message CommonTicketResponse {
  // The ID of the ticket
  string id = 1;

  // The title of the ticket
  string title = 2;

  // The ticket ID
  int32 ticketId = 3;

  // The description of the ticket
  optional string description = 4;

  // The account ID
  optional string accountId = 5;

  // The account Name
  optional string account = 6;

  // The status of the ticket
  optional string status = 7;

  // The status ID
  optional string statusId = 8;

  // The priority of the ticket
  optional string priority = 9;

  // The priority ID
  optional string priorityId = 10;

  // The team ID
  string teamId = 11;

  // The name of the team
  optional string teamName = 12;

  // Whether the ticket is private
  bool isPrivate = 13;

  // The type ID
  optional string typeId = 14;

  // The type
  optional string type = 15;

  // The assigned agent
  optional string assignedAgent = 16;

  // The assigned agent ID
  optional string assignedAgentId = 17;

  // The requestor email
  string requestorEmail = 18;

  // The submitter email
  optional string submitterEmail = 19;

  // The deleted at date
  optional string deletedAt = 20;

  // The archived at date
  optional string archivedAt = 21;

  // The created at date
  string createdAt = 22;

  // The updated at date
  string updatedAt = 23;

  // The AI generated title of the ticket
  optional string aiGeneratedTitle = 24;

  // The AI generated summary of the ticket
  optional string aiGeneratedSummary = 25;

  // The metadata of the ticket
  optional string metadata = 26;

  // The form ID
  optional string formId = 27;

  // The custom field values
  repeated CustomFieldValue customFieldValues = 28;

  // The organization ID
  optional string organizationId = 29;
}

message GetTicketsWithCursorRequest {
  int32 limit = 1;
  optional string teamId = 2;
  optional string afterCursor = 3;
}

message GetTicketsWithCursorResponse {
  string cursor = 1;
  repeated CommonTicketResponse tickets = 2;
}

message ArchiveTicketRequest {
  string id = 1;
}

message EscalateTicketRequest {
  string id = 1;
  string reason = 2;
  string details = 3;
  string impact = 4;
}


message CustomFieldValueData {
  optional string value = 1;
  optional string id = 2;
}

message CustomFieldValue {
  string customFieldId = 1;
  repeated CustomFieldValueData data = 2;
  optional string metadata = 3;
}

// Field metadata messages
message GetTicketFieldMetadataRequest {}

message GetTicketFieldMetadataResponse {
  map<string, TicketFieldMetadata> fields = 1;
}

message GetTicketStatusFieldMetadataRequest {
 optional string teamId = 1;
}

message GetTicketStatusFieldMetadataResponse {
  map<string, TicketFieldMetadata> fields = 1;
}

message GetTicketTypeFieldMetadataRequest {
 optional string teamId = 1;
}

message GetTicketTypeFieldMetadataResponse {
  map<string, TicketFieldMetadata> fields = 1;
}

message GetTicketPriorityFieldMetadataRequest {
 optional string teamId = 1;
}

message GetTicketPriorityFieldMetadataResponse {
  map<string, TicketFieldMetadata> fields = 1;
}

message GetTicketSentimentFieldMetadataRequest {
 optional string teamId = 1;
}

message GetTicketSentimentFieldMetadataResponse {
  map<string, TicketFieldMetadata> fields = 1;
}

// Entity data messages
message GetTicketDataRequest {
  string ticketId = 1;
  repeated string relations = 2;
}

// Entity data messages
message FilterTicketDataRequest {
  string conditions = 1;
  repeated string relations = 2;
}

message GetTicketDataResponse {
  string data = 1;
}

message GetTicketStatusDataRequest {
  string statusId = 1;
  repeated string relations = 2;
}

message GetTicketStatusDataResponse {
  string data = 1;
}

message GetTicketTypeDataRequest {
  string typeId = 1;
  repeated string relations = 2;
}

message GetTicketTypeDataResponse {
  string data = 1;
}

message GetTicketPriorityDataRequest {
  string priorityId = 1;
  repeated string relations = 2;
}

message GetTicketPriorityDataResponse {
  string data = 1;
}

message GetTicketSentimentDataRequest {
  string sentimentId = 1;
  repeated string relations = 2;
}

message GetTicketSentimentDataResponse {
  string data = 1;
}

// Field metadata structure for tickets
message TicketFieldMetadata {
  // Type of the field
  string type = 1;
  
  // Label for displaying
  string label = 2;
  
  // Description for displaying
  optional string description = 3;
  
  // Key of the field in findEntity api response
  string expression = 4;
  
  // If the field is a standard field
  bool standard = 5;
  
  // If the field is enabled for SLA
  optional bool slaEnabled = 6;
  
  // Constraints for the field
  optional TicketFieldConstraints constraints = 7;
  
  // Supported operators for the field
  repeated TicketFieldOperator supportedOperators = 8;
}

// Field constraints for tickets
message TicketFieldConstraints {
  // For dynamic choices
  repeated TicketFieldOption dynamicChoices = 1;
  
  // Fixed options for choice type
  repeated TicketFieldOption options = 2;
  
  // For lookup type
  optional string lookupType = 3;
  
  // Related entity type for lookup
  optional string relatedEntityType = 4;
  
  // For number type
  optional int32 min = 5;
  optional int32 max = 6;
  
  // For string/text type
  optional int32 minLength = 7;
  optional int32 maxLength = 8;
  
  // For array type
  optional string itemsType = 9;

  // Relationship value for lookup type
  optional string relationshipValue = 10;
}

// Option for ticket choice fields
message TicketFieldOption {
  string label = 1;
  string value = 2;
}

// Operator definition for ticket fields
message TicketFieldOperator {
  string name = 1;
  string value = 2;
}
