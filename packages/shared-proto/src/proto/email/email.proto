syntax = "proto3";

package grpc.email.v1;

// Request message for which service to check health for
message SendEmailRequest {
  string to = 1;
  string subject = 2;
  string body = 3;
}

message SendEmailResponse {
  enum SendEmailStatus {
    UNKNOWN = 0;
    SUCCESS = 1;
    FAILED = 2;
  }

  SendEmailStatus status = 1;
}

// EmailProvider service for email app.
service EmailProvider {
  // Send an email
  rpc SendEmail(SendEmailRequest) returns (SendEmailResponse) {}
}
