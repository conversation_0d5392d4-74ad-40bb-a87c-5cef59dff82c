syntax = "proto3";

package grpc.communication.v1;

message Empty {}

service CommunicationService {
  rpc CreateCommentOnAnEntity (CreateCommentRequest) returns (CommentResponse);
	rpc GetCommentsOnAnEntity (GetCommentsRequest) returns (GetCommentsResponse);

	rpc GetComment (GetCommentRequest) returns (CommentResponse);
  rpc GetCommentByUserType (GetCommentByUserTypeRequest) returns (CommentResponse);
  rpc GetCommentThreads (GetCommentThreadsRequest) returns (GetCommentThreadsResponse);
	rpc UpdateComment (UpdateCommentRequest) returns (CommentResponse);
	rpc DeleteComment (DeleteCommentRequest) returns (Empty);

	rpc AddReaction (AddReactionRequest) returns (ReactionResponse);
	rpc RemoveReaction (RemoveReactionRequest) returns (ReactionResponse);
  rpc GetEmojis (Empty) returns (GetEmojisResponse);
}

message CreateCommentRequest {
	string content = 1;
	optional string contentHtml = 2;
	optional string threadName = 3;
	string entityType = 4;
	string entityId = 5;
	optional string parentCommentId = 6;
	optional string commentVisibility = 7;
	optional string commentType = 8;
	optional string metadata = 9;
	repeated string attachmentIds = 10;
	optional string customerEmail = 11;
	optional string impersonatedUserEmail = 12;
	optional string impersonatedUserName = 13;
	optional string impersonatedUserAvatar = 14;
}

message ExternalStorageResponseDto {
  string id = 1;
  string url = 2;
  string name = 3;
  int32 size = 4;
  string contentType = 5;
  string createdAt = 6;
}

message CommentMetadata {
	map<string, ReactionMetadata> reactions = 1;
	repeated string replies = 2;
	repeated string mentions = 3;
	optional string source = 4;
	repeated IntegrationMetadata integrationMetadata = 5;
}

message ReactionMetadata {
	int32 count = 1;
	repeated string users = 2;
}

message IntegrationMetadata {
	string source = 1;
	string externalId = 2;
	optional string channelId = 3;
	optional string threadId = 4;
}

message CommentResponse {
	string id = 1;
	string content = 2;
	string contentHtml = 3;
	string contentMarkdown = 4;
	bool isEdited = 5;
	string threadName = 6;
	string commentVisibility = 7;
	string commentType = 8;
	bool isPinned = 9;
	string sourceEmailId = 10;
	CommentMetadata metadata = 11;
	string parentCommentId = 12;
	string author = 13;
	string authorId = 14;
	string authorUserType = 15;
	repeated ExternalStorageResponseDto attachments = 16;
	string createdAt = 17;
	string updatedAt = 18;
  optional string customerContactId = 19;
  optional string customerContactEmail = 20;
  optional string impersonatedUserEmail = 21;
  optional string impersonatedUserName = 22;
  optional string impersonatedUserAvatar = 23;
}

message GetCommentsRequest {
	string entityType = 1;
	string entityId = 2;
	optional int32 page = 4;
	optional int32 limit = 5;
}

message GetCommentsResponse {
	repeated CommentResponse comments = 1;
}

message GetCommentByUserTypeRequest {
  string entityType = 1;
  string entityId = 2;
  string userType = 3;
  optional bool firstComment = 4;
}

message GetCommentRequest {
	string commentId = 1;
}

message UpdateCommentRequest {
	string commentId = 1;
	optional string content = 2;
	optional string threadName = 3;
	repeated string attachments = 4;
  optional string metadata = 5;
}

message DeleteCommentRequest {
	string commentId = 1;
}

message GetCommentThreadsRequest {
	string commentId = 1;
	optional int32 page = 2;
	optional int32 limit = 3;
}

message GetCommentThreadsResponse {
	repeated CommentResponse comments = 1;
}

message AddReactionRequest {
	string commentId = 1;
	string reactionName = 2;
}

message RemoveReactionRequest {
	string commentId = 1;
	string reactionName = 2;
}

message ReactionResponse {
	bool success = 1;
}

message GetEmojisResponse {
  repeated EmojiResponse emojis = 1;
}

message EmojiResponse {
  string name = 1;
  string unicode = 2;
  string shortcode = 3;
  string category = 4;
  repeated string keywords = 5;
}

service CommunicationAnnotatorService {
	rpc GetCommentFieldMetadata (GetCommentFieldMetadataRequest) returns (GetCommentFieldMetadataResponse);
	rpc GetCommentData (GetCommentDataRequest) returns (GetCommentDataResponse);
}


message GetCommentFieldMetadataRequest {}

message GetCommentFieldMetadataResponse {
	map<string, CommentFieldMetadata> fields = 1;
}

message GetCommentDataRequest {
	string commentId = 1;
	repeated string relations = 2;
}


message GetCommentDataResponse {
	string data = 1;
}

// Field metadata structure for comments
message CommentFieldMetadata {
  // Type of the field
  string type = 1;
  
  // Label for displaying
  string label = 2;
  
  // Description for displaying
  optional string description = 3;
  
  // Key of the field in findEntity api response
  string expression = 4;
  
  // If the field is a standard field
  bool standard = 5;
  
  // If the field is enabled for SLA
  optional bool slaEnabled = 6;
  
  // Constraints for the field
  optional CommentFieldConstraints constraints = 7;
  
  // Supported operators for the field
  repeated CommentFieldOperator supportedOperators = 8;
}

// Field constraints for comments
message CommentFieldConstraints {
  // For choice type
  optional bool dynamic_options = 1;
  
  // Fixed options for choice type
  repeated CommentFieldOption options = 2;
  
  // For lookup type
  optional string lookupType = 3;
  
  // Related entity type for lookup
  optional string relatedEntityType = 4;
  
  // For number type
  optional int32 min = 5;
  optional int32 max = 6;
  
  // For string/text type
  optional int32 minLength = 7;
  optional int32 maxLength = 8;
  
  // For array type
  optional string itemsType = 9;

	// Relationship value for lookup type
	optional string relationshipValue = 10;
}

// Option for comment choice fields
message CommentFieldOption {
  string label = 1;
  string value = 2;
}

// Operator definition for comment fields
message CommentFieldOperator {
  string name = 1;
  string value = 2;
}
