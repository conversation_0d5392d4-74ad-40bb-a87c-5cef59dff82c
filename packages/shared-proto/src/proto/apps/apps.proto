syntax = "proto3";

package grpc.apps.v1;

// Apps service for apps platform
service Apps {
  // Check if a bot user belongs to a Thena-owned app
  rpc IsThenaOwnedApp(IsThenaOwnedAppRequest) returns (IsThenaOwnedAppResponse);
}

message IsThenaOwnedAppRequest {
  string bot_user_uid = 1; // The UID of the bot user
}

message IsThenaOwnedAppResponse {
  bool is_thena_owned = 1; // Whether the bot belongs to a Thena-owned app
  string app_uid = 2; // The UID of the app (if found)
  string app_name = 3; // The name of the app (if found)
}
