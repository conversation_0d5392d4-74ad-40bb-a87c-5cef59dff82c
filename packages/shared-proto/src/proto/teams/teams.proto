syntax = "proto3";

package grpc.teams.v1;

// Teams service for teams app.
service Teams {
  // Create a team
  rpc CreateTeam (CreateTeamRequest) returns (CommonTeamResponse);

  // Update a team
  rpc UpdateTeam (UpdateTeamRequest) returns (CommonTeamResponse);

  // Delete a team
  rpc DeleteTeam (DeleteTeamRequest) returns (EmptyResponse);

  // Get a team by ID
  rpc GetTeamById (GetTeamByIdRequest) returns (CommonTeamResponse);

  // Get all teams
  rpc GetAllTeams (GetAllTeamsRequest) returns (GetAllTeamsResponse);

  // Get all public teams
  rpc GetAllPublicTeams (GetAllPublicTeamsRequest) returns (GetAllPublicTeamsResponse);

  // Add a team member
  rpc AddTeamMember (AddTeamMemberRequest) returns (TeamMemberResponse);

  // Remove a team member
  rpc RemoveTeamMember (RemoveTeamMemberRequest) returns (EmptyResponse);

  // Get all team members
  rpc GetAllTeamMembers (GetAllTeamMembersRequest) returns (GetAllTeamMembersResponse);

  // Get team configurations
  rpc GetTeamConfigurations (GetTeamConfigurationsRequest) returns (TeamConfigurationsResponse);

  // Update team configurations
  rpc UpdateTeamConfigurations (UpdateTeamConfigurationsRequest) returns (TeamConfigurationsResponse);

  // Create a routing rule
  rpc CreateRoutingRule (CreateRoutingRuleRequest) returns (CommonRoutingRuleResponse);

  // Update a routing rule
  rpc UpdateRoutingRule (UpdateRoutingRuleRequest) returns (CommonRoutingRuleResponse);

  // Check team availability
  rpc CheckTeamAvailability (CheckTeamAvailabilityRequest) returns (CheckTeamAvailabilityResponse);
}

// Team Annotator service
service TeamAnnotator {
  // Field Metadata APIs
  rpc GetTeamFieldMetadata (GetTeamFieldMetadataRequest) returns (GetTeamFieldMetadataResponse);
  rpc GetTeamMemberFieldMetadata (GetTeamMemberFieldMetadataRequest) returns (GetTeamMemberFieldMetadataResponse);
  rpc GetTeamConfigurationFieldMetadata (GetTeamConfigurationFieldMetadataRequest) returns (GetTeamConfigurationFieldMetadataResponse);
  rpc GetTeamCapacityFieldMetadata (GetTeamCapacityFieldMetadataRequest) returns (GetTeamCapacityFieldMetadataResponse);
  rpc GetBusinessHoursConfigFieldMetadata (GetBusinessHoursConfigFieldMetadataRequest) returns (GetBusinessHoursConfigFieldMetadataResponse);


  // Entity Data APIs
  rpc GetTeamData (GetTeamDataRequest) returns (GetTeamDataResponse);
  rpc GetTeamMemberData (GetTeamMemberDataRequest) returns (GetTeamMemberDataResponse);
  rpc GetTeamConfigurationData (GetTeamConfigurationDataRequest) returns (GetTeamConfigurationDataResponse);
  rpc GetTeamCapacityData (GetTeamCapacityDataRequest) returns (GetTeamCapacityDataResponse);
  rpc GetBusinessHoursConfigData (GetBusinessHoursConfigDataRequest) returns (GetBusinessHoursConfigDataResponse);
}


// Single rule definition
message Rule {
  string field = 1;
  string operator = 2;
  string value = 3;
  optional int32 precedence = 4;
}

// Request to create a new routing rule
message CreateRoutingRuleRequest {
  string name = 1;
  optional string description = 2;
  string team_id = 3;
  optional int32 evaluation_order = 4;
  string result_team_id = 5;
  repeated Rule and_rules = 6;
  repeated Rule or_rules = 7;
}

// Request to update an existing routing rule
message UpdateRoutingRuleRequest {
  string id = 1;
  optional string name = 2;
  optional string description = 3;
  optional int32 evaluation_order = 4;
  optional string result_team_id = 5;
  repeated Rule and_rules = 6;
  repeated Rule or_rules = 7;
}

// Response for routing rule operations
message CommonRoutingRuleResponse {
  string id = 1;
  string name = 2;
  optional string description = 3;
  string team_id = 4;
  int32 evaluation_order = 5;
  string result_team_id = 6;
  optional string fallback_team_id = 7;
  repeated Rule and_rules = 8;
  repeated Rule or_rules = 9;
  string created_by = 10;
  string created_at = 11;
  string updated_at = 12;
}

message EmptyResponse {}

message GetAllTeamsRequest {}

message GetAllPublicTeamsRequest {}

message GetTeamByIdRequest {
  string id = 1;
}

message AddTeamMemberRequest {
  string team_id = 1;
  optional string user_id = 2;
  optional string email = 3;
  optional bool is_admin = 4;
}

message RemoveTeamMemberRequest {
  string team_id = 1;
  string member_id = 2;
}

message TeamMemberResponse {
  string id = 1;
  string name = 2;
  string email = 3;
  string invited_by = 4;
  string team_id = 5;
  string team_name = 6;
  bool is_active = 7;
  string role = 8;
  bool is_owner = 9;
  string joined_at = 10;
}

message GetAllTeamMembersRequest {
  string team_id = 1;
}

message GetAllTeamMembersResponse {
  repeated TeamMemberResponse members = 1;
}

message CreateTeamRequest {
  string name = 1;
  optional string description = 2;
  optional string identifier = 3;
  optional string parent_team_id = 4;
  optional bool is_private = 5;
  optional string icon = 6;
}

message UpdateTeamRequest {
  string id = 1;
  optional string name = 2;
  optional string description = 3;
  optional bool is_private = 4;
  optional string icon = 5;
}

message DeleteTeamRequest {
  string id = 1;
}

message CommonTeamResponse {
  string id = 1;
  string name = 2;
  optional string parent_team_id = 3;
  optional string parent_team_name = 4;
  optional string team_id = 5;
  optional string identifier = 6;
  optional string description = 7;
  optional string team_owner = 8;
  optional string team_owner_id = 9;
  optional string created_at = 10;
  optional bool is_active = 11;
  optional bool is_private = 12;
  optional string archived_at = 13;
}

message GetAllTeamsResponse {
  repeated CommonTeamResponse teams = 1;
}

message GetAllPublicTeamsResponse {
  repeated CommonTeamResponse teams = 1;
}

// Business time slot representing a start and end time
message BusinessSlot {
  // Start time in 24-hour format (HH:mm)
  string start = 1;
  // End time in 24-hour format (HH:mm)
  string end = 2;
}

// Business day configuration
message BusinessDay {
  // Whether the business day is active
  bool is_active = 1;
  // The time slots for the business day
  repeated BusinessSlot slots = 2;
}

// Business days configuration
message BusinessDays {
  // The business hours for Monday
  BusinessDay monday = 1;
  // The business hours for Tuesday
  BusinessDay tuesday = 2;
  // The business hours for Wednesday
  BusinessDay wednesday = 3;
  // The business hours for Thursday
  BusinessDay thursday = 4;
  // The business hours for Friday
  BusinessDay friday = 5;
  // The business hours for Saturday
  BusinessDay saturday = 6;
  // The business hours for Sunday
  BusinessDay sunday = 7;
}

// Business days configuration
message ResponseBusinessDays {
  // The business hours for Monday
  BusinessDay monday = 1;
  // The business hours for Tuesday
  BusinessDay tuesday = 2;
  // The business hours for Wednesday
  BusinessDay wednesday = 3;
  // The business hours for Thursday
  BusinessDay thursday = 4;
  // The business hours for Friday
  BusinessDay friday = 5;
  // The business hours for Saturday
  BusinessDay saturday = 6;
  // The business hours for Sunday
  BusinessDay sunday = 7;
}

message GetTeamConfigurationsRequest {
  string team_id = 1;
}

message TeamConfigurationsResponse {
  string team_id = 1;
  string timezone = 2;
  repeated string holidays = 3;
  bool routing_respects_timezone = 4;
  bool routing_respects_user_timezone = 5;
  bool routing_respects_user_availability = 6;
  bool routing_respects_user_business_hours = 7;
  string user_routing_strategy = 8;
  ResponseBusinessDays daily_config = 9;
}

message UpdateTeamConfigurationsRequest {
  string team_id = 1;
  optional string timezone = 2;
  optional bool routing_respects_timezone = 3;
  optional bool routing_respects_user_timezone = 4;
  optional bool routing_respects_user_availability = 5;
  optional bool routing_respects_user_business_hours = 6;
  optional string user_routing_strategy = 7;
  optional BusinessDays daily_config = 8;
}

message CheckTeamAvailabilityRequest {
  string teamId = 1;
  repeated string specificHolidays = 2;
}

message CheckTeamAvailabilityResponse {
  bool isAvailable = 1;
  string reason = 2;
}

// Field metadata messages
message GetTeamFieldMetadataRequest {}

message GetTeamFieldMetadataResponse {
  map<string, TeamFieldMetadata> fields = 1;
}

message GetTeamMemberFieldMetadataRequest {}

message GetTeamMemberFieldMetadataResponse {
  map<string, TeamFieldMetadata> fields = 1;
}

message GetTeamConfigurationFieldMetadataRequest {}

message GetTeamConfigurationFieldMetadataResponse {
  map<string, TeamFieldMetadata> fields = 1;
}

message GetTeamCapacityFieldMetadataRequest {}

message GetTeamCapacityFieldMetadataResponse {
  map<string, TeamFieldMetadata> fields = 1;
}

message GetBusinessHoursConfigFieldMetadataRequest {}

message GetBusinessHoursConfigFieldMetadataResponse {
  map<string, TeamFieldMetadata> fields = 1;
}

// Entity data messages
message GetTeamDataRequest {
  string teamId = 1;
  repeated string relations = 2;
}

message GetTeamDataResponse {
  string data = 1;
}

message GetTeamMemberDataRequest {
  string teamId = 1;
  string memberId = 2;
  repeated string relations = 3;
}

message GetTeamMemberDataResponse {
  string data = 1;
}

message GetTeamConfigurationDataRequest {
  string teamId = 1;
  repeated string relations = 2;
}

message GetTeamConfigurationDataResponse {
  string data = 1;
}

message GetTeamCapacityDataRequest {
  string teamId = 1;
  string userId = 2;
  repeated string relations = 3;
}

message GetTeamCapacityDataResponse {
  string data = 1;
}

message GetBusinessHoursConfigDataRequest {
  string teamId = 1;
  repeated string relations = 2;
}

message GetBusinessHoursConfigDataResponse {
  string data = 1;
}

// Field metadata structure for teams
message TeamFieldMetadata {
  // Type of the field
  string type = 1;
  
  // Label for displaying
  string label = 2;
  
  // Description for displaying
  optional string description = 3;
  
  // Key of the field in findEntity api response
  string expression = 4;
  
  // If the field is a standard field
  bool standard = 5;
  
  // If the field is enabled for SLA
  optional bool slaEnabled = 6;
  
  // Constraints for the field
  optional TeamFieldConstraints constraints = 7;
  
  // Supported operators for the field
  repeated TeamFieldOperator supportedOperators = 8;
}

// Field constraints for teams
message TeamFieldConstraints {
  // For choice type
  optional bool dynamicOptions = 1;
  
  // Fixed options for choice type
  repeated TeamFieldOption options = 2;
  
  // For lookup type
  optional string lookupType = 3;
  
  // Related entity type for lookup
  optional string relatedEntityType = 4;
  
  // For number type
  optional int32 min = 5;
  optional int32 max = 6;
  
  // For string/text type
  optional int32 minLength = 7;
  optional int32 maxLength = 8;
  
  // For array type
  optional string itemsType = 9;

	// Relationship value for lookup type
	optional string relationshipValue = 10;
}

// Option for team choice fields
message TeamFieldOption {
  string label = 1;
  string value = 2;
}

// Operator definition for team fields
message TeamFieldOperator {
  string name = 1;
  string value = 2;
}
