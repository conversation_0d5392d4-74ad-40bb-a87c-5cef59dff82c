syntax = "proto3";

package grpc.users.v1;

message Empty {}

// User type enum matching your TypeORM enum
enum UserType {
  USER_TYPE_UNSPECIFIED = 0;
  USER = 1;
  CUSTOMER_USER = 2;
  APP_USER = 3;
  ORG_ADMIN = 4;
  BOT_USER = 5;
}

enum RollbackStatus {
  ROLLBACK_STATUS_UNSPECIFIED = 0;
  ROLLBACK_STATUS_SUCCESS = 1;
  ROLLBACK_STATUS_FAILED = 2;
  ROLLBACK_STATUS_USER_NOT_FOUND = 3;
  ROLLBACK_STATUS_INVALID_TRANSACTION = 4;
}

// Bot installation user service definition
service BotInstallationUserService {
  // Create a new bot user for installation
  rpc CreateBotInstallationUser(CreateBotInstallationUserRequest) returns (CreateBotInstallationUserResponse);

  // Uninstall a bot
  rpc UninstallBot(UninstallBotRequest) returns (UninstallBotResponse);
  
  // Compensation/rollback for bot installation user creation
  rpc RollbackBotInstallationUserCreation(RollbackBotInstallationUserRequest) returns (RollbackBotInstallationUserResponse);

  // Compensation/rollback for bot uninstall
  rpc RollbackBotUninstall(RollbackBotUninstallRequest) returns (RollbackBotUninstallResponse);
}

// Compensation/rollback for bot uninstall response
message RollbackBotUninstallResponse {
  bool success = 1;
  string message = 2;
}

// Compensation/rollback for bot uninstall request
message RollbackBotUninstallRequest {
  string app_id = 1;
  string organization_id = 2;
  repeated string team_ids = 3;
}

// Uninstall bot request
message UninstallBotRequest {
  string app_id = 1;
  string organization_id = 2;
}

// Uninstall bot response
message UninstallBotResponse {
  bool success = 1;
  string message = 2;
}

// Request message for creating a bot user
message CreateBotInstallationUserRequest {
  string name = 1;
  string app_id = 2;
  string organization_id = 3;
  repeated string team_ids = 4;
  UserType user_type = 5;
  optional string avatar_url = 6;
}

// Create bot user response
message CreateBotInstallationUserResponse {
  string id = 1;
  string uid = 2;
  string name = 3;
  string organization_id = 4;
  repeated string team_ids = 5;
  UserType user_type = 6;
  string transaction_id = 7;  // For compensation/rollback
  string access_token = 8;
  string refresh_token = 9;
  string app_key = 10;
  string app_secret_key = 11;
}

// Compensation/rollback request
message RollbackBotInstallationUserRequest {
  string id = 1;
  string transaction_id = 2;
  repeated string team_ids = 3;
  string reason = 4;
  string organization_id = 5;
}

// Rollback response
message RollbackBotInstallationUserResponse {
  bool success = 1;
  string message = 2;
  repeated string team_ids = 3;
  RollbackStatus status = 4;
}

// Field metadata messages
message GetUserFieldMetadataRequest {
  optional string teamId = 1;
}

message GetUserFieldMetadataResponse {
  map<string, UserFieldMetadata> fields = 1;
}

message GetUserSkillsFieldMetadataRequest {}

message GetUserSkillsFieldMetadataResponse {
  map<string, UserFieldMetadata> fields = 1;
}

message GetUserBusinessHoursConfigFieldMetadataRequest {}

message GetUserBusinessHoursConfigFieldMetadataResponse {
  map<string, UserFieldMetadata> fields = 1;
}

message GetTimeOffFieldMetadataRequest {}

message GetTimeOffFieldMetadataResponse {
  map<string, UserFieldMetadata> fields = 1;
}

// Entity data messages
message GetUserDataRequest {
  string userId = 1;
  repeated string relations = 2;
}

message GetUserDataResponse {
  string data = 1;
}

message GetUserSkillsDataRequest {
  string userId = 1;
  repeated string relations = 2;
}

message GetUserSkillsDataResponse {
  string data = 1;
}

message GetUserBusinessHoursConfigDataRequest {
  string userId = 1;
  repeated string relations = 2;
}

message GetUserBusinessHoursConfigDataResponse {
  string data = 1;
}

message GetTimeOffDataRequest {
  string userId = 1;
  repeated string relations = 2;
}

message GetTimeOffDataResponse {
  string data = 1;
}

// Field metadata structure for users
message UserFieldMetadata {
  // Type of the field
  string type = 1;
  
  // Label for displaying
  string label = 2;
  
  // Description for displaying
  optional string description = 3;
  
  // Key of the field in findEntity api response
  string expression = 4;
  
  // If the field is a standard field
  bool standard = 5;
  
  // If the field is enabled for SLA
  optional bool slaEnabled = 6;
  
  // Constraints for the field
  optional UserFieldConstraints constraints = 7;
  
  // Supported operators for the field
  repeated UserFieldOperator supportedOperators = 8;
}

// Field constraints for users
message UserFieldConstraints {
  // For dynamic choices
  repeated UserFieldOption dynamicChoices = 1;	
  
  // Fixed options for choice type
  repeated UserFieldOption options = 2;
  
  // For lookup type
  optional string lookupType = 3;
  
  // Related entity type for lookup
  optional string relatedEntityType = 4;
  
  // For number type
  optional int32 min = 5;
  optional int32 max = 6;
  
  // For string/text type
  optional int32 minLength = 7;
  optional int32 maxLength = 8;
  
  // For array type
  optional string itemsType = 9;

	// Relationship value for lookup type
	optional string relationshipValue = 10;
}

// Option for user choice fields
message UserFieldOption {
  string label = 1;
  string value = 2;
}

// Operator definition for user fields
message UserFieldOperator {
  string name = 1;
  string value = 2;
}

// Request to get user details
message GetUsersDetailsRequest {  
  repeated string userIds = 1;  
}  
  
message UserDetails {  
  string name = 1;  
  string avatar = 2;  
  optional string logoUrl = 3;  
}  
  
message GetUsersDetailsResponse {  
  repeated UserDetails users = 1;  
}  

// User Annotator service
service UserAnnotator {
  // Field Metadata APIs
  rpc GetUserFieldMetadata (GetUserFieldMetadataRequest) returns (GetUserFieldMetadataResponse);
  rpc GetUserSkillsFieldMetadata (GetUserSkillsFieldMetadataRequest) returns (GetUserSkillsFieldMetadataResponse);
  rpc GetUserBusinessHoursConfigFieldMetadata (GetUserBusinessHoursConfigFieldMetadataRequest) returns (GetUserBusinessHoursConfigFieldMetadataResponse);
  rpc GetTimeOffFieldMetadata (GetTimeOffFieldMetadataRequest) returns (GetTimeOffFieldMetadataResponse);

  // Entity Data APIs
  rpc GetUserData (GetUserDataRequest) returns (GetUserDataResponse);
  rpc GetUserSkillsData (GetUserSkillsDataRequest) returns (GetUserSkillsDataResponse);
  rpc GetUserBusinessHoursConfigData (GetUserBusinessHoursConfigDataRequest) returns (GetUserBusinessHoursConfigDataResponse);
  rpc GetTimeOffData (GetTimeOffDataRequest) returns (GetTimeOffDataResponse);
}


service UserService {
  rpc GetOrganizationAdmin (GetOrganizationAdminRequest) returns (GetOrganizationAdminResponse);
  rpc FetchWorkflowBot (Empty) returns (FetchWorkflowBotResponse);
  rpc CheckUserAvailability (CheckUserAvailabilityRequest) returns (CheckUserAvailabilityResponse);
  // Get details for multiple users
  rpc GetUsersDetails(GetUsersDetailsRequest) returns (GetUsersDetailsResponse);
}

message GetOrganizationAdminRequest {
  string organization_id = 1;
}

message GetOrganizationAdminResponse {
  string user_id = 1;
}

message FetchWorkflowBotResponse {
  string id = 1;
  string name = 2;
  string email = 3;
  UserType userType = 4;
  string organizationId = 5;
}

message CheckUserAvailabilityRequest {
  string userId = 1;
}

message CheckUserAvailabilityResponse {
  bool isAvailable = 1;
  string reason = 2;
}
