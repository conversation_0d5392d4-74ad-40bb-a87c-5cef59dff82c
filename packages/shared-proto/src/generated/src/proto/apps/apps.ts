// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.1
// source: apps/apps.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.apps.v1";

export interface IsThenaOwnedAppRequest {
  /** The UID of the bot user */
  botUserUid: string;
}

export interface IsThenaOwnedAppResponse {
  /** Whether the bot belongs to a Thena-owned app */
  isThenaOwned: boolean;
  /** The UID of the app (if found) */
  appUid: string;
  /** The name of the app (if found) */
  appName: string;
}

export const GRPC_APPS_V1_PACKAGE_NAME = "grpc.apps.v1";

/** Apps service for apps platform */

export interface AppsClient {
  /** Check if a bot user belongs to a Thena-owned app */

  isThenaOwnedApp(request: IsThenaOwnedAppRequest): Observable<IsThenaOwnedAppResponse>;
}

/** Apps service for apps platform */

export interface AppsController {
  /** Check if a bot user belongs to a Thena-owned app */

  isThenaOwnedApp(
    request: IsThenaOwnedAppRequest,
  ): Promise<IsThenaOwnedAppResponse> | Observable<IsThenaOwnedAppResponse> | IsThenaOwnedAppResponse;
}

export function AppsControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["isThenaOwnedApp"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("Apps", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("Apps", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const APPS_SERVICE_NAME = "Apps";
