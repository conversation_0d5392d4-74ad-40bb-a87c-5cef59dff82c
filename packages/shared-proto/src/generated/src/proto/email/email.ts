// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.0
// source: email/email.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.email.v1";

/** Request message for which service to check health for */
export interface SendEmailRequest {
  to: string;
  subject: string;
  body: string;
}

export interface SendEmailResponse {
  status: SendEmailResponse_SendEmailStatus;
}

export enum SendEmailResponse_SendEmailStatus {
  UNKNOWN = 0,
  SUCCESS = 1,
  FAILED = 2,
  UNRECOGNIZED = -1,
}

export const GRPC_EMAIL_V1_PACKAGE_NAME = "grpc.email.v1";

/** EmailProvider service for email app. */

export interface EmailProviderClient {
  /** Send an email */

  sendEmail(request: SendEmailRequest): Observable<SendEmailResponse>;
}

/** EmailProvider service for email app. */

export interface EmailProviderController {
  /** Send an email */

  sendEmail(request: SendEmailRequest): Promise<SendEmailResponse> | Observable<SendEmailResponse> | SendEmailResponse;
}

export function EmailProviderControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["sendEmail"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("EmailProvider", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("EmailProvider", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const EMAIL_PROVIDER_SERVICE_NAME = "EmailProvider";
