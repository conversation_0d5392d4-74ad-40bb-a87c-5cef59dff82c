// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.1
// source: accounts/accounts.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.accounts.v1";

export interface Empty {}

/** Field metadata messages */
export interface GetAccountFieldMetadataRequest {}

export interface GetAccountFieldMetadataResponse {
  fields: { [key: string]: AccountFieldMetadata };
}

export interface GetAccountFieldMetadataResponse_FieldsEntry {
  key: string;
  value: AccountFieldMetadata | undefined;
}

export interface GetAccountAttributeValueFieldMetadataRequest {
  attributeType?: string | undefined;
}

export interface GetAccountAttributeValueFieldMetadataResponse {
  fields: { [key: string]: AccountFieldMetadata };
}

export interface GetAccountAttributeValueFieldMetadataResponse_FieldsEntry {
  key: string;
  value: AccountFieldMetadata | undefined;
}

export interface GetCustomerContactFieldMetadataRequest {}

export interface GetCustomerContactFieldMetadataResponse {
  fields: { [key: string]: AccountFieldMetadata };
}

export interface GetCustomerContactFieldMetadataResponse_FieldsEntry {
  key: string;
  value: AccountFieldMetadata | undefined;
}

/** Entity data messages */
export interface GetAccountDataRequest {
  accountId: string;
  relations: string[];
}

export interface GetAccountDataResponse {
  data: string;
}

export interface GetAccountAttributeValueDataRequest {
  attributeValueId: string;
  relations: string[];
}

export interface GetAccountAttributeValueDataResponse {
  data: string;
}

export interface GetCustomerContactDataRequest {
  customerContactId: string;
  relations: string[];
}

export interface GetCustomerContactDataResponse {
  data: string;
}

/** Field metadata structure for accounts */
export interface AccountFieldMetadata {
  /** Type of the field */
  type: string;
  /** Label for displaying */
  label: string;
  /** Description for displaying */
  description?: string | undefined;
  /** Key of the field in findEntity api response */
  expression: string;
  /** If the field is a standard field */
  standard: boolean;
  /** If the field is enabled for SLA */
  slaEnabled?: boolean | undefined;
  /** Constraints for the field */
  constraints?: AccountFieldConstraints | undefined;
  /** Supported operators for the field */
  supportedOperators: AccountFieldOperator[];
}

/** Field constraints for accounts */
export interface AccountFieldConstraints {
  /** For dynamic choices */
  dynamicChoices: AccountFieldOption[];
  /** Fixed options for choice type */
  options: AccountFieldOption[];
  /** For lookup type */
  lookupType?: string | undefined;
  /** Related entity type for lookup */
  relatedEntityType?: string | undefined;
  /** For number type */
  min?: number | undefined;
  max?: number | undefined;
  /** For string/text type */
  minLength?: number | undefined;
  maxLength?: number | undefined;
  /** For array type */
  itemsType?: string | undefined;
  /** Relationship value for lookup type */
  relationshipValue?: string | undefined;
}

/** Option for account choice fields */
export interface AccountFieldOption {
  label: string;
  value: string;
}

/** Operator definition for account fields */
export interface AccountFieldOperator {
  name: string;
  value: string;
}

export interface AccountAttributeValueConfiguration {
  icon?: string | undefined;
  color?: string | undefined;
  isClosed?: boolean | undefined;
}

export interface Account {
  id: string;
  name: string;
  description?: string | undefined;
  source: string;
  logo?: string | undefined;
  statusId: string;
  status: string;
  statusConfiguration: AccountAttributeValueConfiguration | undefined;
  classificationId: string;
  classification: string;
  classificationConfiguration: AccountAttributeValueConfiguration | undefined;
  healthId: string;
  health: string;
  healthConfiguration: AccountAttributeValueConfiguration | undefined;
  industryId: string;
  industry: string;
  industryConfiguration: AccountAttributeValueConfiguration | undefined;
  primaryDomain: string;
  secondaryDomain?: string | undefined;
  accountOwner?: string | undefined;
  accountOwnerId?: string | undefined;
  accountOwnerEmail?: string | undefined;
  accountOwnerAvatarUrl?: string | undefined;
  annualRevenue?: number | undefined;
  employees?: number | undefined;
  website?: string | undefined;
  billingAddress?: string | undefined;
  shippingAddress?: string | undefined;
  customFieldValues: CustomFieldValue[];
  createdAt: string;
  updatedAt: string;
}

export interface CustomFieldValueData {
  value?: string | undefined;
  id?: string | undefined;
}

export interface CustomFieldValue {
  customFieldId: string;
  data: CustomFieldValueData[];
  metadata?: string | undefined;
}

export interface GetAccountsRequest {
  source?: string | undefined;
  status?: string | undefined;
  classification?: string | undefined;
  health?: string | undefined;
  industry?: string | undefined;
  accountOwnerId?: string | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

export interface GetAccountsResponse {
  data: Account[];
}

export interface GetAccountDetailsRequest {
  id: string;
}

export interface CreateAccountRequest {
  name: string;
  description?: string | undefined;
  source?: string | undefined;
  accountOwnerId?: string | undefined;
  logo?: string | undefined;
  status?: string | undefined;
  classification?: string | undefined;
  health?: string | undefined;
  industry?: string | undefined;
  primaryDomain: string;
  secondaryDomain?: string | undefined;
  annualRevenue?: number | undefined;
  employees?: number | undefined;
  website?: string | undefined;
  billingAddress?: string | undefined;
  shippingAddress?: string | undefined;
  customFieldValues: CustomFieldValue[];
  addExistingUsersToAccountContacts?: boolean | undefined;
  metadata: { [key: string]: string };
}

export interface CreateAccountRequest_MetadataEntry {
  key: string;
  value: string;
}

export interface UpdateAccountRequest {
  id: string;
  name?: string | undefined;
  description?: string | undefined;
  source?: string | undefined;
  logo?: string | undefined;
  status?: string | undefined;
  classification?: string | undefined;
  health?: string | undefined;
  industry?: string | undefined;
  primaryDomain?: string | undefined;
  secondaryDomain?: string | undefined;
  accountOwnerId?: string | undefined;
  annualRevenue?: number | undefined;
  employees?: number | undefined;
  website?: string | undefined;
  billingAddress?: string | undefined;
  shippingAddress?: string | undefined;
  customFieldValues: CustomFieldValue[];
  addExistingUsersToAccountContacts?: boolean | undefined;
  metadata: { [key: string]: string };
}

export interface UpdateAccountRequest_MetadataEntry {
  key: string;
  value: string;
}

export interface DeleteAccountRequest {
  id: string;
}

/** Filter accounts by primary domains */
export interface FilterAccountsByPrimaryDomainsRequest {
  primaryDomains: string[];
}

export interface FilterAccountsByPrimaryDomainsResponse {
  data: Account[];
}

/** Filter accounts by IDs */
export interface FilterAccountsByIdsRequest {
  ids: string[];
}

export interface FilterAccountsByIdsResponse {
  data: Account[];
}

export interface AccountActivity {
  id: string;
  accountId: string;
  account: string;
  title: string;
  description?: string | undefined;
  activityTimestamp: string;
  duration?: number | undefined;
  location?: string | undefined;
  type?: string | undefined;
  typeId?: string | undefined;
  typeConfiguration: { [key: string]: string };
  status?: string | undefined;
  statusId?: string | undefined;
  statusConfiguration: { [key: string]: string };
  participants: string[];
  attachments: AccountActivityAttachment[];
  creator: string;
  creatorId: string;
  creatorEmail: string;
  createdAt: string;
  updatedAt: string;
}

export interface AccountActivity_TypeConfigurationEntry {
  key: string;
  value: string;
}

export interface AccountActivity_StatusConfigurationEntry {
  key: string;
  value: string;
}

export interface AccountActivityAttachment {
  id: string;
  url: string;
  name: string;
  size: number;
  contentType: string;
  createdAt: string;
}

export interface GetAccountActivitiesRequest {
  accountId?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

export interface GetAccountActivitiesResponse {
  data: AccountActivity[];
}

export interface CreateAccountActivityRequest {
  accountId: string;
  title: string;
  activityTimestamp: string;
  description?: string | undefined;
  duration?: number | undefined;
  location?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  participants: string[];
  attachmentUrls: string[];
}

export interface UpdateAccountActivityRequest {
  activityId: string;
  title?: string | undefined;
  description?: string | undefined;
  activityTimestamp?: string | undefined;
  duration?: number | undefined;
  location?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  participants: string[];
  attachmentUrls: string[];
}

export interface DeleteAccountActivityRequest {
  activityId: string;
}

export interface RemoveActivityAttachmentRequest {
  activityId: string;
  attachmentId: string;
}

export interface AccountNote {
  id: string;
  accountId: string;
  account: string;
  content: string;
  type?: string | undefined;
  typeId?: string | undefined;
  typeConfiguration: { [key: string]: string };
  visibility?: string | undefined;
  attachments: AccountNoteAttachment[];
  author: string;
  authorId: string;
  authorEmail: string;
  createdAt: string;
  updatedAt: string;
}

export interface AccountNote_TypeConfigurationEntry {
  key: string;
  value: string;
}

export interface AccountNoteAttachment {
  id: string;
  url: string;
  name: string;
  size: number;
  contentType: string;
  createdAt: string;
}

export interface GetAccountNotesRequest {
  accountId?: string | undefined;
  type?: string | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

export interface GetAccountNotesResponse {
  data: AccountNote[];
}

export interface CreateAccountNoteRequest {
  accountId: string;
  content: string;
  type?: string | undefined;
  visibility?: string | undefined;
  attachmentUrls: string[];
}

export interface UpdateAccountNoteRequest {
  noteId: string;
  content?: string | undefined;
  type?: string | undefined;
  visibility?: string | undefined;
  attachmentUrls: string[];
}

export interface DeleteAccountNoteRequest {
  noteId: string;
}

export interface AccountRelationshipType {
  id: string;
  name: string;
  inverseRelationshipId?: string | undefined;
  inverseRelationship?: string | undefined;
  createdAt: string;
  updatedAt: string;
}

export interface GetAccountRelationshipTypesResponse {
  data: AccountRelationshipType[];
}

export interface CreateAccountRelationshipTypeRequest {
  name: string;
  inverseRelationshipId?: string | undefined;
}

export interface UpdateAccountRelationshipTypeRequest {
  id: string;
  name?: string | undefined;
  inverseRelationshipId?: string | undefined;
}

export interface DeleteAccountRelationshipTypeRequest {
  id: string;
}

export interface AccountRelationship {
  id: string;
  accountId: string;
  account: string;
  relatedAccountId: string;
  relatedAccount: string;
  relationshipType: AccountRelationshipType | undefined;
  createdAt: string;
  updatedAt: string;
}

export interface GetAccountRelationshipsRequest {
  accountId?: string | undefined;
  relationshipType?: string | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

export interface GetAccountRelationshipsResponse {
  data: AccountRelationship[];
}

export interface CreateAccountRelationshipRequest {
  accountId: string;
  relatedAccountId: string;
  relationshipType: string;
}

export interface UpdateAccountRelationshipRequest {
  relationshipId: string;
  relationshipType?: string | undefined;
}

export interface DeleteAccountRelationshipRequest {
  relationshipId: string;
}

export interface AccountTask {
  id: string;
  accountId: string;
  account: string;
  activityId?: string | undefined;
  title: string;
  description?: string | undefined;
  assigneeId?: string | undefined;
  typeId?: string | undefined;
  type?: string | undefined;
  typeConfiguration: { [key: string]: string };
  statusId?: string | undefined;
  status?: string | undefined;
  statusConfiguration: { [key: string]: string };
  priorityId?: string | undefined;
  priority?: string | undefined;
  priorityConfiguration: { [key: string]: string };
  attachments: AccountTaskAttachment[];
  creator: string;
  creatorId: string;
  creatorEmail: string;
  createdAt: string;
  updatedAt: string;
}

export interface AccountTask_TypeConfigurationEntry {
  key: string;
  value: string;
}

export interface AccountTask_StatusConfigurationEntry {
  key: string;
  value: string;
}

export interface AccountTask_PriorityConfigurationEntry {
  key: string;
  value: string;
}

export interface AccountTaskAttachment {
  id: string;
  url: string;
  name: string;
  size: number;
  contentType: string;
  createdAt: string;
}

export interface GetAccountTasksRequest {
  accountId?: string | undefined;
  activityId?: string | undefined;
  assigneeId?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  priority?: string | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

export interface GetAccountTasksResponse {
  data: AccountTask[];
}

export interface CreateAccountTaskRequest {
  accountId: string;
  title: string;
  assigneeId: string;
  activityId?: string | undefined;
  description?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  priority?: string | undefined;
  attachmentUrls: string[];
}

export interface UpdateAccountTaskRequest {
  taskId: string;
  title?: string | undefined;
  assigneeId?: string | undefined;
  activityId?: string | undefined;
  description?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  priority?: string | undefined;
  attachmentUrls: string[];
}

export interface DeleteAccountTaskRequest {
  taskId: string;
}

export interface CustomerContactAccount {
  id: string;
  name: string;
}

export interface CustomerContact {
  id: string;
  firstName: string;
  lastName?: string | undefined;
  email: string;
  phoneNumber?: string | undefined;
  avatarUrl?: string | undefined;
  accounts: CustomerContactAccount[];
  contactTypeId?: string | undefined;
  contactType?: string | undefined;
  customFieldValues: CustomFieldValue[];
  metadata: { [key: string]: string };
  createdAt: string;
  updatedAt: string;
}

export interface CustomerContact_MetadataEntry {
  key: string;
  value: string;
}

export interface GetCustomerContactsRequest {
  accountId?: string | undefined;
  contactType?: string | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

export interface GetCustomerContactsResponse {
  data: CustomerContact[];
}

export interface CreateCustomerContactRequest {
  firstName: string;
  lastName?: string | undefined;
  email: string;
  phoneNumber?: string | undefined;
  contactType?: string | undefined;
  accountIds: string[];
  customFieldValues: CustomFieldValue[];
  avatarUrl?: string | undefined;
}

export interface UpdateCustomerContactRequest {
  contactId: string;
  firstName?: string | undefined;
  lastName?: string | undefined;
  email?: string | undefined;
  phoneNumber?: string | undefined;
  contactType?: string | undefined;
  accountIds: string[];
  avatarUrl?: string | undefined;
  customFieldValues: CustomFieldValue[];
}

export interface DeleteCustomerContactRequest {
  contactId: string;
}

export interface CustomerContactDetails {
  firstName: string;
  lastName?: string | undefined;
  email: string;
  phoneNumber?: string | undefined;
  avatarUrl?: string | undefined;
}

export interface BulkCreateCustomerContactsRequest {
  contacts: CustomerContactDetails[];
  accountIds: string[];
  contactType?: string | undefined;
}

export interface BulkCreateCustomerContactsResponse {
  total: number;
  created: number;
  skipped: number;
}

export interface AccountAttributeValue {
  id: string;
  attribute: string;
  value: string;
  isDefault: boolean;
  configuration: { [key: string]: string };
  createdAt: string;
  updatedAt: string;
}

export interface AccountAttributeValue_ConfigurationEntry {
  key: string;
  value: string;
}

export interface GetAccountAttributeValuesRequest {
  attribute: string;
}

export interface GetAccountAttributeValuesResponse {
  data: AccountAttributeValue[];
}

export interface CreateAccountAttributeValueRequest {
  attribute: string;
  value: string;
  isDefault: boolean;
  icon?: string | undefined;
  color?: string | undefined;
  isClosed?: boolean | undefined;
}

export interface UpdateAccountAttributeValueRequest {
  id: string;
  value?: string | undefined;
  isDefault?: boolean | undefined;
  icon?: string | undefined;
  color?: string | undefined;
  isClosed?: boolean | undefined;
}

export interface DeleteAccountAttributeValueRequest {
  id: string;
  forceDelete: boolean;
}

export const GRPC_ACCOUNTS_V1_PACKAGE_NAME = "grpc.accounts.v1";

export interface AccountAnnotatorClient {
  /** Field Metadata APIs */

  getAccountFieldMetadata(
    request: GetAccountFieldMetadataRequest,
  ): Observable<GetAccountFieldMetadataResponse>;

  getAccountAttributeValueFieldMetadata(
    request: GetAccountAttributeValueFieldMetadataRequest,
  ): Observable<GetAccountAttributeValueFieldMetadataResponse>;

  getCustomerContactFieldMetadata(
    request: GetCustomerContactFieldMetadataRequest,
  ): Observable<GetCustomerContactFieldMetadataResponse>;

  /** Entity Data APIs */

  getAccountData(
    request: GetAccountDataRequest,
  ): Observable<GetAccountDataResponse>;

  getAccountAttributeValueData(
    request: GetAccountAttributeValueDataRequest,
  ): Observable<GetAccountAttributeValueDataResponse>;

  getCustomerContactData(
    request: GetCustomerContactDataRequest,
  ): Observable<GetCustomerContactDataResponse>;
}

export interface AccountAnnotatorController {
  /** Field Metadata APIs */

  getAccountFieldMetadata(
    request: GetAccountFieldMetadataRequest,
  ):
    | Promise<GetAccountFieldMetadataResponse>
    | Observable<GetAccountFieldMetadataResponse>
    | GetAccountFieldMetadataResponse;

  getAccountAttributeValueFieldMetadata(
    request: GetAccountAttributeValueFieldMetadataRequest,
  ):
    | Promise<GetAccountAttributeValueFieldMetadataResponse>
    | Observable<GetAccountAttributeValueFieldMetadataResponse>
    | GetAccountAttributeValueFieldMetadataResponse;

  getCustomerContactFieldMetadata(
    request: GetCustomerContactFieldMetadataRequest,
  ):
    | Promise<GetCustomerContactFieldMetadataResponse>
    | Observable<GetCustomerContactFieldMetadataResponse>
    | GetCustomerContactFieldMetadataResponse;

  /** Entity Data APIs */

  getAccountData(
    request: GetAccountDataRequest,
  ):
    | Promise<GetAccountDataResponse>
    | Observable<GetAccountDataResponse>
    | GetAccountDataResponse;

  getAccountAttributeValueData(
    request: GetAccountAttributeValueDataRequest,
  ):
    | Promise<GetAccountAttributeValueDataResponse>
    | Observable<GetAccountAttributeValueDataResponse>
    | GetAccountAttributeValueDataResponse;

  getCustomerContactData(
    request: GetCustomerContactDataRequest,
  ):
    | Promise<GetCustomerContactDataResponse>
    | Observable<GetCustomerContactDataResponse>
    | GetCustomerContactDataResponse;
}

export function AccountAnnotatorControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getAccountFieldMetadata",
      "getAccountAttributeValueFieldMetadata",
      "getCustomerContactFieldMetadata",
      "getAccountData",
      "getAccountAttributeValueData",
      "getCustomerContactData",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("AccountAnnotator", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("AccountAnnotator", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const ACCOUNT_ANNOTATOR_SERVICE_NAME = "AccountAnnotator";

export interface AccountsServiceClient {
  /** Get all accounts */

  getAccounts(request: GetAccountsRequest): Observable<GetAccountsResponse>;

  /** Get account details */

  getAccountDetails(request: GetAccountDetailsRequest): Observable<Account>;

  /** Create an account */

  createAccount(request: CreateAccountRequest): Observable<Account>;

  /** Update an account */

  updateAccount(request: UpdateAccountRequest): Observable<Account>;

  /** Delete an account */

  deleteAccount(request: DeleteAccountRequest): Observable<Empty>;

  /** Filter accounts by primary domains */

  filterAccountsByPrimaryDomains(
    request: FilterAccountsByPrimaryDomainsRequest,
  ): Observable<FilterAccountsByPrimaryDomainsResponse>;

  /** Filter accounts by IDs */

  filterAccountsByIds(
    request: FilterAccountsByIdsRequest,
  ): Observable<FilterAccountsByIdsResponse>;
}

export interface AccountsServiceController {
  /** Get all accounts */

  getAccounts(
    request: GetAccountsRequest,
  ):
    | Promise<GetAccountsResponse>
    | Observable<GetAccountsResponse>
    | GetAccountsResponse;

  /** Get account details */

  getAccountDetails(
    request: GetAccountDetailsRequest,
  ): Promise<Account> | Observable<Account> | Account;

  /** Create an account */

  createAccount(
    request: CreateAccountRequest,
  ): Promise<Account> | Observable<Account> | Account;

  /** Update an account */

  updateAccount(
    request: UpdateAccountRequest,
  ): Promise<Account> | Observable<Account> | Account;

  /** Delete an account */

  deleteAccount(
    request: DeleteAccountRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;

  /** Filter accounts by primary domains */

  filterAccountsByPrimaryDomains(
    request: FilterAccountsByPrimaryDomainsRequest,
  ):
    | Promise<FilterAccountsByPrimaryDomainsResponse>
    | Observable<FilterAccountsByPrimaryDomainsResponse>
    | FilterAccountsByPrimaryDomainsResponse;

  /** Filter accounts by IDs */

  filterAccountsByIds(
    request: FilterAccountsByIdsRequest,
  ):
    | Promise<FilterAccountsByIdsResponse>
    | Observable<FilterAccountsByIdsResponse>
    | FilterAccountsByIdsResponse;
}

export function AccountsServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getAccounts",
      "getAccountDetails",
      "createAccount",
      "updateAccount",
      "deleteAccount",
      "filterAccountsByPrimaryDomains",
      "filterAccountsByIds",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("AccountsService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("AccountsService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const ACCOUNTS_SERVICE_NAME = "AccountsService";

export interface AccountActivitiesServiceClient {
  /** Get account activities */

  getAccountActivities(
    request: GetAccountActivitiesRequest,
  ): Observable<GetAccountActivitiesResponse>;

  /** Create account activity */

  createAccountActivity(
    request: CreateAccountActivityRequest,
  ): Observable<AccountActivity>;

  /** Update account activity */

  updateAccountActivity(
    request: UpdateAccountActivityRequest,
  ): Observable<AccountActivity>;

  /** Delete account activity */

  deleteAccountActivity(
    request: DeleteAccountActivityRequest,
  ): Observable<Empty>;

  /** Remove activity attachment */

  removeActivityAttachment(
    request: RemoveActivityAttachmentRequest,
  ): Observable<Empty>;
}

export interface AccountActivitiesServiceController {
  /** Get account activities */

  getAccountActivities(
    request: GetAccountActivitiesRequest,
  ):
    | Promise<GetAccountActivitiesResponse>
    | Observable<GetAccountActivitiesResponse>
    | GetAccountActivitiesResponse;

  /** Create account activity */

  createAccountActivity(
    request: CreateAccountActivityRequest,
  ): Promise<AccountActivity> | Observable<AccountActivity> | AccountActivity;

  /** Update account activity */

  updateAccountActivity(
    request: UpdateAccountActivityRequest,
  ): Promise<AccountActivity> | Observable<AccountActivity> | AccountActivity;

  /** Delete account activity */

  deleteAccountActivity(
    request: DeleteAccountActivityRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;

  /** Remove activity attachment */

  removeActivityAttachment(
    request: RemoveActivityAttachmentRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;
}

export function AccountActivitiesServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getAccountActivities",
      "createAccountActivity",
      "updateAccountActivity",
      "deleteAccountActivity",
      "removeActivityAttachment",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("AccountActivitiesService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("AccountActivitiesService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const ACCOUNT_ACTIVITIES_SERVICE_NAME = "AccountActivitiesService";

export interface AccountNotesServiceClient {
  /** Get account notes */

  getAccountNotes(
    request: GetAccountNotesRequest,
  ): Observable<GetAccountNotesResponse>;

  /** Create account note */

  createAccountNote(request: CreateAccountNoteRequest): Observable<AccountNote>;

  /** Update account note */

  updateAccountNote(request: UpdateAccountNoteRequest): Observable<AccountNote>;

  /** Delete account note */

  deleteAccountNote(request: DeleteAccountNoteRequest): Observable<Empty>;
}

export interface AccountNotesServiceController {
  /** Get account notes */

  getAccountNotes(
    request: GetAccountNotesRequest,
  ):
    | Promise<GetAccountNotesResponse>
    | Observable<GetAccountNotesResponse>
    | GetAccountNotesResponse;

  /** Create account note */

  createAccountNote(
    request: CreateAccountNoteRequest,
  ): Promise<AccountNote> | Observable<AccountNote> | AccountNote;

  /** Update account note */

  updateAccountNote(
    request: UpdateAccountNoteRequest,
  ): Promise<AccountNote> | Observable<AccountNote> | AccountNote;

  /** Delete account note */

  deleteAccountNote(
    request: DeleteAccountNoteRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;
}

export function AccountNotesServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getAccountNotes",
      "createAccountNote",
      "updateAccountNote",
      "deleteAccountNote",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("AccountNotesService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("AccountNotesService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const ACCOUNT_NOTES_SERVICE_NAME = "AccountNotesService";

export interface AccountRelationshipsServiceClient {
  /** Create an account relationship type */

  createAccountRelationshipType(
    request: CreateAccountRelationshipTypeRequest,
  ): Observable<AccountRelationshipType>;

  /** Get all account relationship types */

  getAccountRelationshipTypes(
    request: Empty,
  ): Observable<GetAccountRelationshipTypesResponse>;

  /** Update an account relationship type */

  updateAccountRelationshipType(
    request: UpdateAccountRelationshipTypeRequest,
  ): Observable<AccountRelationshipType>;

  /** Delete an account relationship type */

  deleteAccountRelationshipType(
    request: DeleteAccountRelationshipTypeRequest,
  ): Observable<Empty>;

  /** Get account relationships */

  getAccountRelationships(
    request: GetAccountRelationshipsRequest,
  ): Observable<GetAccountRelationshipsResponse>;

  /** Create account relationship */

  createAccountRelationship(
    request: CreateAccountRelationshipRequest,
  ): Observable<AccountRelationship>;

  /** Update account relationship */

  updateAccountRelationship(
    request: UpdateAccountRelationshipRequest,
  ): Observable<AccountRelationship>;

  /** Delete account relationship */

  deleteAccountRelationship(
    request: DeleteAccountRelationshipRequest,
  ): Observable<Empty>;
}

export interface AccountRelationshipsServiceController {
  /** Create an account relationship type */

  createAccountRelationshipType(
    request: CreateAccountRelationshipTypeRequest,
  ):
    | Promise<AccountRelationshipType>
    | Observable<AccountRelationshipType>
    | AccountRelationshipType;

  /** Get all account relationship types */

  getAccountRelationshipTypes(
    request: Empty,
  ):
    | Promise<GetAccountRelationshipTypesResponse>
    | Observable<GetAccountRelationshipTypesResponse>
    | GetAccountRelationshipTypesResponse;

  /** Update an account relationship type */

  updateAccountRelationshipType(
    request: UpdateAccountRelationshipTypeRequest,
  ):
    | Promise<AccountRelationshipType>
    | Observable<AccountRelationshipType>
    | AccountRelationshipType;

  /** Delete an account relationship type */

  deleteAccountRelationshipType(
    request: DeleteAccountRelationshipTypeRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;

  /** Get account relationships */

  getAccountRelationships(
    request: GetAccountRelationshipsRequest,
  ):
    | Promise<GetAccountRelationshipsResponse>
    | Observable<GetAccountRelationshipsResponse>
    | GetAccountRelationshipsResponse;

  /** Create account relationship */

  createAccountRelationship(
    request: CreateAccountRelationshipRequest,
  ):
    | Promise<AccountRelationship>
    | Observable<AccountRelationship>
    | AccountRelationship;

  /** Update account relationship */

  updateAccountRelationship(
    request: UpdateAccountRelationshipRequest,
  ):
    | Promise<AccountRelationship>
    | Observable<AccountRelationship>
    | AccountRelationship;

  /** Delete account relationship */

  deleteAccountRelationship(
    request: DeleteAccountRelationshipRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;
}

export function AccountRelationshipsServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createAccountRelationshipType",
      "getAccountRelationshipTypes",
      "updateAccountRelationshipType",
      "deleteAccountRelationshipType",
      "getAccountRelationships",
      "createAccountRelationship",
      "updateAccountRelationship",
      "deleteAccountRelationship",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("AccountRelationshipsService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("AccountRelationshipsService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const ACCOUNT_RELATIONSHIPS_SERVICE_NAME = "AccountRelationshipsService";

export interface AccountTasksServiceClient {
  /** Get account tasks */

  getAccountTasks(
    request: GetAccountTasksRequest,
  ): Observable<GetAccountTasksResponse>;

  /** Create account task */

  createAccountTask(request: CreateAccountTaskRequest): Observable<AccountTask>;

  /** Update account task */

  updateAccountTask(request: UpdateAccountTaskRequest): Observable<AccountTask>;

  /** Delete account task */

  deleteAccountTask(request: DeleteAccountTaskRequest): Observable<Empty>;
}

export interface AccountTasksServiceController {
  /** Get account tasks */

  getAccountTasks(
    request: GetAccountTasksRequest,
  ):
    | Promise<GetAccountTasksResponse>
    | Observable<GetAccountTasksResponse>
    | GetAccountTasksResponse;

  /** Create account task */

  createAccountTask(
    request: CreateAccountTaskRequest,
  ): Promise<AccountTask> | Observable<AccountTask> | AccountTask;

  /** Update account task */

  updateAccountTask(
    request: UpdateAccountTaskRequest,
  ): Promise<AccountTask> | Observable<AccountTask> | AccountTask;

  /** Delete account task */

  deleteAccountTask(
    request: DeleteAccountTaskRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;
}

export function AccountTasksServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getAccountTasks",
      "createAccountTask",
      "updateAccountTask",
      "deleteAccountTask",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("AccountTasksService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("AccountTasksService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const ACCOUNT_TASKS_SERVICE_NAME = "AccountTasksService";

export interface CustomerContactsServiceClient {
  /** Get customer contacts */

  getCustomerContacts(
    request: GetCustomerContactsRequest,
  ): Observable<GetCustomerContactsResponse>;

  /** Create customer contact */

  createCustomerContact(
    request: CreateCustomerContactRequest,
  ): Observable<CustomerContact>;

  /** Update customer contact */

  updateCustomerContact(
    request: UpdateCustomerContactRequest,
  ): Observable<CustomerContact>;

  /** Delete customer contact */

  deleteCustomerContact(
    request: DeleteCustomerContactRequest,
  ): Observable<Empty>;

  /** Bulk create customer contacts */

  bulkCreateCustomerContacts(
    request: BulkCreateCustomerContactsRequest,
  ): Observable<BulkCreateCustomerContactsResponse>;
}

export interface CustomerContactsServiceController {
  /** Get customer contacts */

  getCustomerContacts(
    request: GetCustomerContactsRequest,
  ):
    | Promise<GetCustomerContactsResponse>
    | Observable<GetCustomerContactsResponse>
    | GetCustomerContactsResponse;

  /** Create customer contact */

  createCustomerContact(
    request: CreateCustomerContactRequest,
  ): Promise<CustomerContact> | Observable<CustomerContact> | CustomerContact;

  /** Update customer contact */

  updateCustomerContact(
    request: UpdateCustomerContactRequest,
  ): Promise<CustomerContact> | Observable<CustomerContact> | CustomerContact;

  /** Delete customer contact */

  deleteCustomerContact(
    request: DeleteCustomerContactRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;

  /** Bulk create customer contacts */

  bulkCreateCustomerContacts(
    request: BulkCreateCustomerContactsRequest,
  ):
    | Promise<BulkCreateCustomerContactsResponse>
    | Observable<BulkCreateCustomerContactsResponse>
    | BulkCreateCustomerContactsResponse;
}

export function CustomerContactsServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getCustomerContacts",
      "createCustomerContact",
      "updateCustomerContact",
      "deleteCustomerContact",
      "bulkCreateCustomerContacts",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("CustomerContactsService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("CustomerContactsService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const CUSTOMER_CONTACTS_SERVICE_NAME = "CustomerContactsService";

export interface AccountAttributeValuesServiceClient {
  /** Get account attribute values */

  getAccountAttributeValues(
    request: GetAccountAttributeValuesRequest,
  ): Observable<GetAccountAttributeValuesResponse>;

  /** Create account attribute value */

  createAccountAttributeValue(
    request: CreateAccountAttributeValueRequest,
  ): Observable<AccountAttributeValue>;

  /** Update account attribute value */

  updateAccountAttributeValue(
    request: UpdateAccountAttributeValueRequest,
  ): Observable<AccountAttributeValue>;

  /** Delete account attribute value */

  deleteAccountAttributeValue(
    request: DeleteAccountAttributeValueRequest,
  ): Observable<Empty>;
}

export interface AccountAttributeValuesServiceController {
  /** Get account attribute values */

  getAccountAttributeValues(
    request: GetAccountAttributeValuesRequest,
  ):
    | Promise<GetAccountAttributeValuesResponse>
    | Observable<GetAccountAttributeValuesResponse>
    | GetAccountAttributeValuesResponse;

  /** Create account attribute value */

  createAccountAttributeValue(
    request: CreateAccountAttributeValueRequest,
  ):
    | Promise<AccountAttributeValue>
    | Observable<AccountAttributeValue>
    | AccountAttributeValue;

  /** Update account attribute value */

  updateAccountAttributeValue(
    request: UpdateAccountAttributeValueRequest,
  ):
    | Promise<AccountAttributeValue>
    | Observable<AccountAttributeValue>
    | AccountAttributeValue;

  /** Delete account attribute value */

  deleteAccountAttributeValue(
    request: DeleteAccountAttributeValueRequest,
  ): Promise<Empty> | Observable<Empty> | Empty;
}

export function AccountAttributeValuesServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getAccountAttributeValues",
      "createAccountAttributeValue",
      "updateAccountAttributeValue",
      "deleteAccountAttributeValue",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("AccountAttributeValuesService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("AccountAttributeValuesService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const ACCOUNT_ATTRIBUTE_VALUES_SERVICE_NAME =
  "AccountAttributeValuesService";
