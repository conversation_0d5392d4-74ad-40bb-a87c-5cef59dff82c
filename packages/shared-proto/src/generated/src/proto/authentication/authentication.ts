// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.1
// source: authentication/authentication.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.authentication.v1";

export enum UserType {
  ORG_ADMIN = 0,
  USER = 1,
  CUSTOMER_USER = 2,
  APP_USER = 3,
  BOT_USER = 4,
  UNRECOGNIZED = -1,
}

export enum OrganizationTier {
  FREE = 0,
  STANDARD = 1,
  ENTERPRISE = 2,
  UNRECOGNIZED = -1,
}

export interface FetchApiKeyForBotUserRequest {
  botUserId: string;
}

export interface FetchApiKeyForBotUserResponse {
  botToken: string;
}

export interface CreateNewAPIKeyRequest {
  name: string;
  description: string;
  expiresAt?: string | undefined;
}

export interface CreateNewAPIKeyResponse {
  apiKey: string;
  secretKey: string;
}

/** The validate and get user by api key request */
export interface ValidateAndGetUserByApiKeyRequest {
  apiKey: string;
}

/** The validate and get user by api key response */
export interface ValidateAndGetUserByApiKeyResponse {
  /** The user id */
  sub: string;
  /** The auth id */
  authId: string;
  /** The user id same as the sub */
  uid: string;
  /** The user email */
  email: string;
  /** The organization id */
  orgId: string;
  /** The organization uid */
  orgUid: string;
  /** The user type */
  userType: string;
  /** The user timezone */
  timezone: string;
  /** The user scopes */
  scopes: string[];
  /** Removed is_privileged_app field - platform handles this via gRPC when needed */
  orgTier: OrganizationTier;
}

export interface SignInRequest {
  email: string;
  password: string;
  organizationId?: string | undefined;
}

export interface SignInResponse {
  token: string;
  refreshToken: string;
}

export interface GetSupabaseAuthUserRequest {
}

export interface GetSupabaseAuthUserResponse {
  id: string;
  email: string;
  phone: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateOrganizationAdminRequest {
  email: string;
  password: string;
  organizationUid: string;
}

export interface CreateOrganizationAdminResponse {
  id: string;
  name: string;
  email: string;
  userType: string;
}

export interface CreateBotUserRequest {
  name: string;
  email: string;
  password: string;
  organizationUid: string;
  appId?: string | undefined;
  purpose?: string | undefined;
  avatarUrl?: string | undefined;
}

export interface CreateBotUserResponse {
  id: string;
  name: string;
  email: string;
  userType: string;
  token: string;
  refreshToken: string;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  password: string;
  organizationUid: string;
  isCustomer?: boolean | undefined;
  externalId?: string | undefined;
}

export interface CreateUserWithoutAuthRequest {
  name: string;
  email: string;
  password: string;
  organizationUid: string;
  isCustomer?: boolean | undefined;
  externalId?: string | undefined;
}

export interface CreateUserWithoutAuthResponse {
  id: string;
  name: string;
  email: string;
  userType: string;
}

export interface CreateUserResponse {
  id: string;
  name: string;
  email: string;
  userType: string;
}

export interface ValidateAndGetUserRequest {
}

export interface UserMetadata {
  isAgent: boolean;
  purpose: string;
  externalId: string;
  slackAuthSink: { [key: string]: boolean };
}

export interface UserMetadata_SlackAuthSinkEntry {
  key: string;
  value: boolean;
}

export interface ValidateAndGetUserResponse {
  sub: string;
  authId: string;
  uid: string;
  email: string;
  orgId: string;
  orgUid: string;
  userType: string;
  timezone: string;
  metadata: UserMetadata | undefined;
  scopes: string[];
  orgTier: OrganizationTier;
}

/** Add these new message types to your proto file */
export interface ValidateAndGetUserByInternalIdsRequest {
  userId: string;
  orgId: string;
}

export interface ValidateAndGetUserByInternalIdsResponse {
  sub: string;
  authId: string;
  uid: string;
  email: string;
  orgId: string;
  orgUid: string;
  userType: string;
  timezone: string;
  scopes: string[];
  orgTier: OrganizationTier;
}

export const GRPC_AUTHENTICATION_V1_PACKAGE_NAME = "grpc.authentication.v1";

/** Authentication service */

export interface AuthenticationServiceClient {
  /** Sign in */

  signIn(request: SignInRequest): Observable<SignInResponse>;

  /** Validate and get user */

  validateAndGetUser(request: ValidateAndGetUserRequest): Observable<ValidateAndGetUserResponse>;

  /** Validate and get user by api key */

  validateAndGetUserByApiKey(
    request: ValidateAndGetUserByApiKeyRequest,
  ): Observable<ValidateAndGetUserByApiKeyResponse>;

  /** Create new api key */

  createNewApiKey(request: CreateNewAPIKeyRequest): Observable<CreateNewAPIKeyResponse>;

  /** Create user */

  createUser(request: CreateUserRequest): Observable<CreateUserResponse>;

  /** Create organization admin */

  createOrganizationAdmin(request: CreateOrganizationAdminRequest): Observable<CreateOrganizationAdminResponse>;

  /** Create organization admin */

  createBotUser(request: CreateBotUserRequest): Observable<CreateBotUserResponse>;

  /** Get supabase auth user */

  getSupabaseAuthUser(request: GetSupabaseAuthUserRequest): Observable<GetSupabaseAuthUserResponse>;

  /** Create user without auth */

  createUserWithoutAuth(request: CreateUserWithoutAuthRequest): Observable<CreateUserWithoutAuthResponse>;

  /** Validate and get user by internal IDs */

  validateAndGetUserByInternalIds(
    request: ValidateAndGetUserByInternalIdsRequest,
  ): Observable<ValidateAndGetUserByInternalIdsResponse>;

  /** Fetch api key */

  fetchApiKeyForBotUser(request: FetchApiKeyForBotUserRequest): Observable<FetchApiKeyForBotUserResponse>;
}

/** Authentication service */

export interface AuthenticationServiceController {
  /** Sign in */

  signIn(request: SignInRequest): Promise<SignInResponse> | Observable<SignInResponse> | SignInResponse;

  /** Validate and get user */

  validateAndGetUser(
    request: ValidateAndGetUserRequest,
  ): Promise<ValidateAndGetUserResponse> | Observable<ValidateAndGetUserResponse> | ValidateAndGetUserResponse;

  /** Validate and get user by api key */

  validateAndGetUserByApiKey(
    request: ValidateAndGetUserByApiKeyRequest,
  ):
    | Promise<ValidateAndGetUserByApiKeyResponse>
    | Observable<ValidateAndGetUserByApiKeyResponse>
    | ValidateAndGetUserByApiKeyResponse;

  /** Create new api key */

  createNewApiKey(
    request: CreateNewAPIKeyRequest,
  ): Promise<CreateNewAPIKeyResponse> | Observable<CreateNewAPIKeyResponse> | CreateNewAPIKeyResponse;

  /** Create user */

  createUser(
    request: CreateUserRequest,
  ): Promise<CreateUserResponse> | Observable<CreateUserResponse> | CreateUserResponse;

  /** Create organization admin */

  createOrganizationAdmin(
    request: CreateOrganizationAdminRequest,
  ):
    | Promise<CreateOrganizationAdminResponse>
    | Observable<CreateOrganizationAdminResponse>
    | CreateOrganizationAdminResponse;

  /** Create organization admin */

  createBotUser(
    request: CreateBotUserRequest,
  ): Promise<CreateBotUserResponse> | Observable<CreateBotUserResponse> | CreateBotUserResponse;

  /** Get supabase auth user */

  getSupabaseAuthUser(
    request: GetSupabaseAuthUserRequest,
  ): Promise<GetSupabaseAuthUserResponse> | Observable<GetSupabaseAuthUserResponse> | GetSupabaseAuthUserResponse;

  /** Create user without auth */

  createUserWithoutAuth(
    request: CreateUserWithoutAuthRequest,
  ): Promise<CreateUserWithoutAuthResponse> | Observable<CreateUserWithoutAuthResponse> | CreateUserWithoutAuthResponse;

  /** Validate and get user by internal IDs */

  validateAndGetUserByInternalIds(
    request: ValidateAndGetUserByInternalIdsRequest,
  ):
    | Promise<ValidateAndGetUserByInternalIdsResponse>
    | Observable<ValidateAndGetUserByInternalIdsResponse>
    | ValidateAndGetUserByInternalIdsResponse;

  /** Fetch api key */

  fetchApiKeyForBotUser(
    request: FetchApiKeyForBotUserRequest,
  ): Promise<FetchApiKeyForBotUserResponse> | Observable<FetchApiKeyForBotUserResponse> | FetchApiKeyForBotUserResponse;
}

export function AuthenticationServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "signIn",
      "validateAndGetUser",
      "validateAndGetUserByApiKey",
      "createNewApiKey",
      "createUser",
      "createOrganizationAdmin",
      "createBotUser",
      "getSupabaseAuthUser",
      "createUserWithoutAuth",
      "validateAndGetUserByInternalIds",
      "fetchApiKeyForBotUser",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("AuthenticationService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("AuthenticationService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const AUTHENTICATION_SERVICE_NAME = "AuthenticationService";
