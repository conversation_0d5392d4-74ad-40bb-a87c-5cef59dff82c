// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.1
// source: tickets/tickets.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.tickets.v1";

/** Request for assigning a ticket to an agent */
export interface AssignTicketRequest {
  executionId: string;
  ticketId: string;
  agentId: string;
  userId: string;
  /** Whether to unassign the ticket */
  unassign?: boolean | undefined;
}

/** Response for assigning a ticket to an agent */
export interface AssignTicketResponse {
  success: boolean;
  ticket: CommonTicketResponse | undefined;
}

export interface CompensateAssignTicketRequest {
  executionId: string;
}

/** Request for getting a ticket */
export interface GetTicketRequest {
  id: string;
}

/** Request for creating a ticket */
export interface CreateTicketRequest {
  /** The title of the ticket */
  title: string;
  /** The email of the requestor */
  requestorEmail: string;
  /** The ID of the team */
  teamId: string;
  /** The ID of the account */
  accountId?:
    | string
    | undefined;
  /** The ID of the assigned agent */
  assignedAgentId?:
    | string
    | undefined;
  /** The description of the ticket */
  description?:
    | string
    | undefined;
  /** The due date of the ticket */
  dueDate?:
    | string
    | undefined;
  /** The ID of the status */
  statusId?:
    | string
    | undefined;
  /** The ID of the priority */
  priorityId?:
    | string
    | undefined;
  /** The ID of the type */
  typeId?:
    | string
    | undefined;
  /** The email of the submitter */
  submitterEmail?:
    | string
    | undefined;
  /** Whether the ticket is private */
  isPrivate?:
    | boolean
    | undefined;
  /** The attachment URLs */
  attachmentUrls: string[];
  /** The source of the request */
  source?:
    | string
    | undefined;
  /** The AI generated title of the ticket */
  aiGeneratedTitle?:
    | string
    | undefined;
  /** The AI generated summary of the ticket */
  aiGeneratedSummary?:
    | string
    | undefined;
  /** Whether to perform routing */
  performRouting?:
    | boolean
    | undefined;
  /** The ID of the form */
  formId?:
    | string
    | undefined;
  /** The metadata of the ticket */
  metadata?:
    | string
    | undefined;
  /** The status name to match against */
  statusName?:
    | string
    | undefined;
  /** The priority name to match against */
  priorityName?:
    | string
    | undefined;
  /** The custom field values */
  customFieldValues: CustomFieldValue[];
  /** The content of the customer comment */
  commentContent?:
    | string
    | undefined;
  /** The HTML content of the customer comment */
  commentContentHtml?:
    | string
    | undefined;
  /** The JSON content of the customer comment */
  commentContentJson?:
    | string
    | undefined;
  /** The attachment IDs for the customer comment */
  commentAttachmentIds: string[];
  /** The metadata for the customer comment */
  commentMetadata?:
    | string
    | undefined;
  /** The impersonated user name for the customer comment */
  commentImpersonatedUserName?:
    | string
    | undefined;
  /** The impersonated user email for the customer comment */
  commentImpersonatedUserEmail?:
    | string
    | undefined;
  /** The impersonated user avatar for the customer comment */
  commentImpersonatedUserAvatar?: string | undefined;
}

export interface UpdateTicketRequest {
  id: string;
  /** The title of the ticket */
  title?:
    | string
    | undefined;
  /** The ID of the sub-team */
  subTeamId?:
    | string
    | undefined;
  /** The ID of the account */
  accountId?:
    | string
    | undefined;
  /** The ID of the assigned agent */
  assignedAgentId?:
    | string
    | undefined;
  /** The email of the assigned agent */
  assignedAgentEmail?:
    | string
    | undefined;
  /** The description of the ticket */
  description?:
    | string
    | undefined;
  /** The due date of the ticket */
  dueDate?:
    | string
    | undefined;
  /** The ID of the status */
  statusId?:
    | string
    | undefined;
  /** The name of the status */
  priorityId?:
    | string
    | undefined;
  /** The ID of the type */
  typeId?:
    | string
    | undefined;
  /** The email of the submitter */
  submitterEmail?:
    | string
    | undefined;
  /** Whether the ticket is private */
  isPrivate?:
    | boolean
    | undefined;
  /** The attachment URLs */
  attachmentUrls: string[];
  /** The AI generated title of the ticket */
  aiGeneratedTitle?:
    | string
    | undefined;
  /** The AI generated summary of the ticket */
  aiGeneratedSummary?:
    | string
    | undefined;
  /** The metadata of the ticket */
  metadata?:
    | string
    | undefined;
  /** The status name to match against */
  statusName?:
    | string
    | undefined;
  /** The priority name to match against */
  priorityName?:
    | string
    | undefined;
  /** The custom field values */
  customFieldValues: CustomFieldValue[];
}

/** Response for creating a ticket */
export interface CommonTicketResponse {
  /** The ID of the ticket */
  id: string;
  /** The title of the ticket */
  title: string;
  /** The ticket ID */
  ticketId: number;
  /** The description of the ticket */
  description?:
    | string
    | undefined;
  /** The account ID */
  accountId?:
    | string
    | undefined;
  /** The account Name */
  account?:
    | string
    | undefined;
  /** The status of the ticket */
  status?:
    | string
    | undefined;
  /** The status ID */
  statusId?:
    | string
    | undefined;
  /** The priority of the ticket */
  priority?:
    | string
    | undefined;
  /** The priority ID */
  priorityId?:
    | string
    | undefined;
  /** The team ID */
  teamId: string;
  /** The name of the team */
  teamName?:
    | string
    | undefined;
  /** Whether the ticket is private */
  isPrivate: boolean;
  /** The type ID */
  typeId?:
    | string
    | undefined;
  /** The type */
  type?:
    | string
    | undefined;
  /** The assigned agent */
  assignedAgent?:
    | string
    | undefined;
  /** The assigned agent ID */
  assignedAgentId?:
    | string
    | undefined;
  /** The requestor email */
  requestorEmail: string;
  /** The submitter email */
  submitterEmail?:
    | string
    | undefined;
  /** The deleted at date */
  deletedAt?:
    | string
    | undefined;
  /** The archived at date */
  archivedAt?:
    | string
    | undefined;
  /** The created at date */
  createdAt: string;
  /** The updated at date */
  updatedAt: string;
  /** The AI generated title of the ticket */
  aiGeneratedTitle?:
    | string
    | undefined;
  /** The AI generated summary of the ticket */
  aiGeneratedSummary?:
    | string
    | undefined;
  /** The metadata of the ticket */
  metadata?:
    | string
    | undefined;
  /** The form ID */
  formId?:
    | string
    | undefined;
  /** The custom field values */
  customFieldValues: CustomFieldValue[];
  /** The organization ID */
  organizationId?:
    | string
    | undefined;
  /** The comment object */
  comment?: Comment | undefined;
}

export interface Comment {
  id: string;
  content: string;
  contentHtml: string;
  contentMarkdown: string;
  isEdited: boolean;
  threadName: string;
  commentVisibility: string;
  commentType: string;
  isPinned: boolean;
  sourceEmailId: string;
  metadata: CommentMetadata | undefined;
  parentCommentId: string;
  author: string;
  authorId: string;
  authorUserType: string;
  attachments: ExternalStorageResponseDto[];
  createdAt: string;
  updatedAt: string;
  customerContactId?: string | undefined;
  customerContactEmail?: string | undefined;
  impersonatedUserEmail?: string | undefined;
  impersonatedUserName?: string | undefined;
  impersonatedUserAvatar?: string | undefined;
}

/** For ticket + comments */
export interface CommentMetadata {
  reactions: { [key: string]: ReactionMetadata };
  replies: string[];
  mentions: string[];
  source?: string | undefined;
  integrationMetadata: IntegrationMetadata[];
  email: EmailData | undefined;
}

export interface CommentMetadata_ReactionsEntry {
  key: string;
  value: ReactionMetadata | undefined;
}

/** For ticket + comments */
export interface ReactionMetadata {
  count: number;
  users: string[];
}

/** For ticket + comments */
export interface IntegrationMetadata {
  source: string;
  externalId: string;
  channelId?: string | undefined;
  threadId?: string | undefined;
}

/** For ticket + comments */
export interface EmailData {
  to: EmailAddress[];
  cc: EmailAddress[];
  bcc: EmailAddress[];
  from: EmailAddress | undefined;
  headers: EmailHeader[];
}

/** For ticket + comments */
export interface EmailAddress {
  name: string;
  email: string;
  mailboxHash: string;
}

/** For ticket + comments */
export interface EmailHeader {
  name: string;
  value: string;
}

/** For ticket + comments */
export interface ExternalStorageResponseDto {
  id: string;
  url: string;
  name: string;
  size: number;
  contentType: string;
  createdAt: string;
}

export interface GetTicketsWithCursorRequest {
  limit: number;
  teamId?: string | undefined;
  afterCursor?: string | undefined;
}

export interface GetTicketsWithCursorResponse {
  cursor: string;
  tickets: CommonTicketResponse[];
}

export interface ArchiveTicketRequest {
  id: string;
}

export interface EscalateTicketRequest {
  id: string;
  reason: string;
  details: string;
  impact: string;
}

export interface CustomFieldValueData {
  value?: string | undefined;
  id?: string | undefined;
}

export interface CustomFieldValue {
  customFieldId: string;
  data: CustomFieldValueData[];
  metadata?: string | undefined;
}

export interface GetTicketsByFiltersRequest {
  accountId?: string | undefined;
  limit?: number | undefined;
  page?: number | undefined;
}

export interface GetTicketsByFiltersResponse {
  tickets: CommonTicketResponse[];
  total: number;
  currentPage: number;
  totalPages: number;
}

export interface CheckIfTicketStatusRemainedSameRequest {
  ticketId: string;
  /** epoch */
  duration: number;
}

export interface CheckIfTicketStatusRemainedSameResponse {
  statusRemainedSame: boolean;
}

/** Field metadata messages */
export interface GetTicketFieldMetadataRequest {
}

export interface GetTicketFieldMetadataResponse {
  fields: { [key: string]: TicketFieldMetadata };
}

export interface GetTicketFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TicketFieldMetadata | undefined;
}

export interface GetTicketStatusFieldMetadataRequest {
  teamId?: string | undefined;
}

export interface GetTicketStatusFieldMetadataResponse {
  fields: { [key: string]: TicketFieldMetadata };
}

export interface GetTicketStatusFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TicketFieldMetadata | undefined;
}

export interface GetTicketTypeFieldMetadataRequest {
  teamId?: string | undefined;
}

export interface GetTicketTypeFieldMetadataResponse {
  fields: { [key: string]: TicketFieldMetadata };
}

export interface GetTicketTypeFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TicketFieldMetadata | undefined;
}

export interface GetTicketPriorityFieldMetadataRequest {
  teamId?: string | undefined;
}

export interface GetTicketPriorityFieldMetadataResponse {
  fields: { [key: string]: TicketFieldMetadata };
}

export interface GetTicketPriorityFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TicketFieldMetadata | undefined;
}

export interface GetTicketSentimentFieldMetadataRequest {
  teamId?: string | undefined;
}

export interface GetTicketSentimentFieldMetadataResponse {
  fields: { [key: string]: TicketFieldMetadata };
}

export interface GetTicketSentimentFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TicketFieldMetadata | undefined;
}

/** Entity data messages */
export interface GetTicketDataRequest {
  ticketId: string;
  relations: string[];
}

/** Entity data messages */
export interface FilterTicketDataRequest {
  conditions: string;
  relations: string[];
}

export interface GetTicketDataResponse {
  data: string;
}

export interface GetTicketStatusDataRequest {
  statusId: string;
  relations: string[];
}

export interface GetTicketStatusDataResponse {
  data: string;
}

export interface GetTicketTypeDataRequest {
  typeId: string;
  relations: string[];
}

export interface GetTicketTypeDataResponse {
  data: string;
}

export interface GetTicketPriorityDataRequest {
  priorityId: string;
  relations: string[];
}

export interface GetTicketPriorityDataResponse {
  data: string;
}

export interface GetTicketSentimentDataRequest {
  sentimentId: string;
  relations: string[];
}

export interface GetTicketSentimentDataResponse {
  data: string;
}

/** Field metadata structure for tickets */
export interface TicketFieldMetadata {
  /** Type of the field */
  type: string;
  /** Label for displaying */
  label: string;
  /** Description for displaying */
  description?:
    | string
    | undefined;
  /** Key of the field in findEntity api response */
  expression: string;
  /** If the field is a standard field */
  standard: boolean;
  /** If the field is enabled for SLA */
  slaEnabled?:
    | boolean
    | undefined;
  /** Constraints for the field */
  constraints?:
    | TicketFieldConstraints
    | undefined;
  /** Supported operators for the field */
  supportedOperators: TicketFieldOperator[];
}

/** Field constraints for tickets */
export interface TicketFieldConstraints {
  /** For dynamic choices */
  dynamicChoices: TicketFieldOption[];
  /** Fixed options for choice type */
  options: TicketFieldOption[];
  /** For lookup type */
  lookupType?:
    | string
    | undefined;
  /** Related entity type for lookup */
  relatedEntityType?:
    | string
    | undefined;
  /** For number type */
  min?: number | undefined;
  max?:
    | number
    | undefined;
  /** For string/text type */
  minLength?: number | undefined;
  maxLength?:
    | number
    | undefined;
  /** For array type */
  itemsType?:
    | string
    | undefined;
  /** Relationship value for lookup type */
  relationshipValue?: string | undefined;
}

/** Option for ticket choice fields */
export interface TicketFieldOption {
  label: string;
  value: string;
}

/** Operator definition for ticket fields */
export interface TicketFieldOperator {
  name: string;
  value: string;
}

export const GRPC_TICKETS_V1_PACKAGE_NAME = "grpc.tickets.v1";

/** Tickets service for tickets app. */

export interface TicketsClient {
  /** Assigns a ticket to an agent */

  assignTicket(request: AssignTicketRequest): Observable<AssignTicketResponse>;

  /** Compensates for assigning a ticket to an agent */

  compensateAssignTicket(request: CompensateAssignTicketRequest): Observable<AssignTicketResponse>;

  /** Create/Update a ticket */

  createTicket(request: CreateTicketRequest): Observable<CommonTicketResponse>;

  updateTicket(request: UpdateTicketRequest): Observable<CommonTicketResponse>;

  getTicket(request: GetTicketRequest): Observable<CommonTicketResponse>;

  getTicketsWithCursor(request: GetTicketsWithCursorRequest): Observable<GetTicketsWithCursorResponse>;

  archiveTicket(request: ArchiveTicketRequest): Observable<CommonTicketResponse>;

  escalateTicket(request: EscalateTicketRequest): Observable<CommonTicketResponse>;

  getTicketsByFilters(request: GetTicketsByFiltersRequest): Observable<GetTicketsByFiltersResponse>;

  checkIfTicketStatusRemainedSame(
    request: CheckIfTicketStatusRemainedSameRequest,
  ): Observable<CheckIfTicketStatusRemainedSameResponse>;
}

/** Tickets service for tickets app. */

export interface TicketsController {
  /** Assigns a ticket to an agent */

  assignTicket(
    request: AssignTicketRequest,
  ): Promise<AssignTicketResponse> | Observable<AssignTicketResponse> | AssignTicketResponse;

  /** Compensates for assigning a ticket to an agent */

  compensateAssignTicket(
    request: CompensateAssignTicketRequest,
  ): Promise<AssignTicketResponse> | Observable<AssignTicketResponse> | AssignTicketResponse;

  /** Create/Update a ticket */

  createTicket(
    request: CreateTicketRequest,
  ): Promise<CommonTicketResponse> | Observable<CommonTicketResponse> | CommonTicketResponse;

  updateTicket(
    request: UpdateTicketRequest,
  ): Promise<CommonTicketResponse> | Observable<CommonTicketResponse> | CommonTicketResponse;

  getTicket(
    request: GetTicketRequest,
  ): Promise<CommonTicketResponse> | Observable<CommonTicketResponse> | CommonTicketResponse;

  getTicketsWithCursor(
    request: GetTicketsWithCursorRequest,
  ): Promise<GetTicketsWithCursorResponse> | Observable<GetTicketsWithCursorResponse> | GetTicketsWithCursorResponse;

  archiveTicket(
    request: ArchiveTicketRequest,
  ): Promise<CommonTicketResponse> | Observable<CommonTicketResponse> | CommonTicketResponse;

  escalateTicket(
    request: EscalateTicketRequest,
  ): Promise<CommonTicketResponse> | Observable<CommonTicketResponse> | CommonTicketResponse;

  getTicketsByFilters(
    request: GetTicketsByFiltersRequest,
  ): Promise<GetTicketsByFiltersResponse> | Observable<GetTicketsByFiltersResponse> | GetTicketsByFiltersResponse;

  checkIfTicketStatusRemainedSame(
    request: CheckIfTicketStatusRemainedSameRequest,
  ):
    | Promise<CheckIfTicketStatusRemainedSameResponse>
    | Observable<CheckIfTicketStatusRemainedSameResponse>
    | CheckIfTicketStatusRemainedSameResponse;
}

export function TicketsControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "assignTicket",
      "compensateAssignTicket",
      "createTicket",
      "updateTicket",
      "getTicket",
      "getTicketsWithCursor",
      "archiveTicket",
      "escalateTicket",
      "getTicketsByFilters",
      "checkIfTicketStatusRemainedSame",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("Tickets", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("Tickets", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const TICKETS_SERVICE_NAME = "Tickets";

/** Ticket Annotator service */

export interface TicketAnnotatorClient {
  /** Field Metadata APIs */

  getTicketFieldMetadata(request: GetTicketFieldMetadataRequest): Observable<GetTicketFieldMetadataResponse>;

  getTicketStatusFieldMetadata(
    request: GetTicketStatusFieldMetadataRequest,
  ): Observable<GetTicketStatusFieldMetadataResponse>;

  getTicketTypeFieldMetadata(
    request: GetTicketTypeFieldMetadataRequest,
  ): Observable<GetTicketTypeFieldMetadataResponse>;

  getTicketPriorityFieldMetadata(
    request: GetTicketPriorityFieldMetadataRequest,
  ): Observable<GetTicketPriorityFieldMetadataResponse>;

  getTicketSentimentFieldMetadata(
    request: GetTicketSentimentFieldMetadataRequest,
  ): Observable<GetTicketSentimentFieldMetadataResponse>;

  /** Entity Data APIs */

  getTicketData(request: GetTicketDataRequest): Observable<GetTicketDataResponse>;

  filterTicketData(request: FilterTicketDataRequest): Observable<GetTicketDataResponse>;

  getTicketStatusData(request: GetTicketStatusDataRequest): Observable<GetTicketStatusDataResponse>;

  getTicketTypeData(request: GetTicketTypeDataRequest): Observable<GetTicketTypeDataResponse>;

  getTicketPriorityData(request: GetTicketPriorityDataRequest): Observable<GetTicketPriorityDataResponse>;

  getTicketSentimentData(request: GetTicketSentimentDataRequest): Observable<GetTicketSentimentDataResponse>;
}

/** Ticket Annotator service */

export interface TicketAnnotatorController {
  /** Field Metadata APIs */

  getTicketFieldMetadata(
    request: GetTicketFieldMetadataRequest,
  ):
    | Promise<GetTicketFieldMetadataResponse>
    | Observable<GetTicketFieldMetadataResponse>
    | GetTicketFieldMetadataResponse;

  getTicketStatusFieldMetadata(
    request: GetTicketStatusFieldMetadataRequest,
  ):
    | Promise<GetTicketStatusFieldMetadataResponse>
    | Observable<GetTicketStatusFieldMetadataResponse>
    | GetTicketStatusFieldMetadataResponse;

  getTicketTypeFieldMetadata(
    request: GetTicketTypeFieldMetadataRequest,
  ):
    | Promise<GetTicketTypeFieldMetadataResponse>
    | Observable<GetTicketTypeFieldMetadataResponse>
    | GetTicketTypeFieldMetadataResponse;

  getTicketPriorityFieldMetadata(
    request: GetTicketPriorityFieldMetadataRequest,
  ):
    | Promise<GetTicketPriorityFieldMetadataResponse>
    | Observable<GetTicketPriorityFieldMetadataResponse>
    | GetTicketPriorityFieldMetadataResponse;

  getTicketSentimentFieldMetadata(
    request: GetTicketSentimentFieldMetadataRequest,
  ):
    | Promise<GetTicketSentimentFieldMetadataResponse>
    | Observable<GetTicketSentimentFieldMetadataResponse>
    | GetTicketSentimentFieldMetadataResponse;

  /** Entity Data APIs */

  getTicketData(
    request: GetTicketDataRequest,
  ): Promise<GetTicketDataResponse> | Observable<GetTicketDataResponse> | GetTicketDataResponse;

  filterTicketData(
    request: FilterTicketDataRequest,
  ): Promise<GetTicketDataResponse> | Observable<GetTicketDataResponse> | GetTicketDataResponse;

  getTicketStatusData(
    request: GetTicketStatusDataRequest,
  ): Promise<GetTicketStatusDataResponse> | Observable<GetTicketStatusDataResponse> | GetTicketStatusDataResponse;

  getTicketTypeData(
    request: GetTicketTypeDataRequest,
  ): Promise<GetTicketTypeDataResponse> | Observable<GetTicketTypeDataResponse> | GetTicketTypeDataResponse;

  getTicketPriorityData(
    request: GetTicketPriorityDataRequest,
  ): Promise<GetTicketPriorityDataResponse> | Observable<GetTicketPriorityDataResponse> | GetTicketPriorityDataResponse;

  getTicketSentimentData(
    request: GetTicketSentimentDataRequest,
  ):
    | Promise<GetTicketSentimentDataResponse>
    | Observable<GetTicketSentimentDataResponse>
    | GetTicketSentimentDataResponse;
}

export function TicketAnnotatorControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getTicketFieldMetadata",
      "getTicketStatusFieldMetadata",
      "getTicketTypeFieldMetadata",
      "getTicketPriorityFieldMetadata",
      "getTicketSentimentFieldMetadata",
      "getTicketData",
      "filterTicketData",
      "getTicketStatusData",
      "getTicketTypeData",
      "getTicketPriorityData",
      "getTicketSentimentData",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("TicketAnnotator", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("TicketAnnotator", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const TICKET_ANNOTATOR_SERVICE_NAME = "TicketAnnotator";
