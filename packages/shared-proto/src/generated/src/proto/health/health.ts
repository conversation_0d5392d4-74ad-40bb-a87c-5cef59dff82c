// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.1
// source: health/health.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.health.v1";

/** Request message for which service to check health for */
export interface HealthCheckRequest {
}

export interface HealthCheckResponse {
  status: HealthCheckResponse_HealthStatus;
}

export enum HealthCheckResponse_HealthStatus {
  UNKNOWN = 0,
  SERVING = 1,
  UNRECOGNIZED = -1,
}

export const GRPC_HEALTH_V1_PACKAGE_NAME = "grpc.health.v1";

/** HealthCheck service for tickets app. */

export interface HealthCheckClient {
  /** Check the health status of a service */

  ping(request: HealthCheckRequest): Observable<HealthCheckResponse>;
}

/** HealthCheck service for tickets app. */

export interface HealthCheckController {
  /** Check the health status of a service */

  ping(
    request: HealthCheckRequest,
  ): Promise<HealthCheckResponse> | Observable<HealthCheckResponse> | HealthCheckResponse;
}

export function HealthCheckControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["ping"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("HealthCheck", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("HealthCheck", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const HEALTH_CHECK_SERVICE_NAME = "HealthCheck";
