// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.0
// source: tags/tags.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.tags.v1";

/** Field metadata messages */
export interface GetTagFieldMetadataRequest {
  teamId?: string | undefined;
}

export interface GetTagFieldMetadataResponse {
  fields: { [key: string]: TagFieldMetadata };
}

export interface GetTagFieldMetadataResponse_FieldsEntry {
  key: string;
  value: TagFieldMetadata | undefined;
}

/** Entity data messages */
export interface GetTagDataRequest {
  tagId: string;
  relations: string[];
}

export interface GetTagDataResponse {
  data: string;
}

/** Field metadata structure for tags */
export interface TagFieldMetadata {
  /** Type of the field */
  type: string;
  /** Label for displaying */
  label: string;
  /** Description for displaying */
  description?:
    | string
    | undefined;
  /** Key of the field in findEntity api response */
  expression: string;
  /** If the field is a standard field */
  standard: boolean;
  /** If the field is enabled for SLA */
  slaEnabled?:
    | boolean
    | undefined;
  /** Constraints for the field */
  constraints?:
    | TagFieldConstraints
    | undefined;
  /** Supported operators for the field */
  supportedOperators: TagFieldOperator[];
}

/** Field constraints for tags */
export interface TagFieldConstraints {
  /** For dynamic choices */
  dynamicChoices: TagFieldOption[];
  /** Fixed options for choice type */
  options: TagFieldOption[];
  /** For lookup type */
  lookupType?:
    | string
    | undefined;
  /** Related entity type for lookup */
  relatedEntityType?:
    | string
    | undefined;
  /** For number type */
  min?: number | undefined;
  max?:
    | number
    | undefined;
  /** For string/text type */
  minLength?: number | undefined;
  maxLength?:
    | number
    | undefined;
  /** For array type */
  itemsType?:
    | string
    | undefined;
  /** Relationship value for lookup type */
  relationshipValue?: string | undefined;
}

/** Option for tag choice fields */
export interface TagFieldOption {
  label: string;
  value: string;
}

/** Operator definition for tag fields */
export interface TagFieldOperator {
  name: string;
  value: string;
}

export const GRPC_TAGS_V1_PACKAGE_NAME = "grpc.tags.v1";

/** Tag Annotator service */

export interface TagAnnotatorClient {
  /** Field Metadata APIs */

  getTagFieldMetadata(request: GetTagFieldMetadataRequest): Observable<GetTagFieldMetadataResponse>;

  /** Entity Data APIs */

  getTagData(request: GetTagDataRequest): Observable<GetTagDataResponse>;
}

/** Tag Annotator service */

export interface TagAnnotatorController {
  /** Field Metadata APIs */

  getTagFieldMetadata(
    request: GetTagFieldMetadataRequest,
  ): Promise<GetTagFieldMetadataResponse> | Observable<GetTagFieldMetadataResponse> | GetTagFieldMetadataResponse;

  /** Entity Data APIs */

  getTagData(
    request: GetTagDataRequest,
  ): Promise<GetTagDataResponse> | Observable<GetTagDataResponse> | GetTagDataResponse;
}

export function TagAnnotatorControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getTagFieldMetadata", "getTagData"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("TagAnnotator", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("TagAnnotator", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const TAG_ANNOTATOR_SERVICE_NAME = "TagAnnotator";
