// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.1
// source: workflows/workflows.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.workflows.v1";

export enum Source {
  PLATFORM_APP = 0,
  REGISTERED_APP = 1,
  UNRECOGNIZED = -1,
}

export interface CompensationActivity {
  identifier: string;
}

export interface HTTPActivityConfig {
  method: string;
  url: string;
  headers: { [key: string]: string };
}

export interface HTTPActivityConfig_HeadersEntry {
  key: string;
  value: string;
}

export interface GRPCActivityConfig {
  url: string;
  packageName: string;
  serviceName: string;
  methodName: string;
  protoPath: string;
}

export interface ActivityConnectionDetails {
  transport: string;
  httpConfig?: HTTPActivityConfig | undefined;
  grpcConfig?: GRPCActivityConfig | undefined;
}

export interface Activity {
  activityName: string;
  description: string;
  /** TODO - @AMit make this identifier as a composite key of activityName and appId. */
  identifier: string;
  /** stringified JSON */
  requestSchema: string;
  /** stringified JSON */
  responseSchema: string;
  connectionDetails: ActivityConnectionDetails | undefined;
  isCompensable: boolean;
  /** stringified JSON */
  metadata: string;
  compensationActivity?: CompensationActivity | undefined;
  accessibleToTeam: boolean;
  teamIds: string[];
}

export interface RegisterActivitiesRequest {
  source: Source;
  activities: Activity[];
  organizationId?: string | undefined;
}

export interface RegisterActivitiesResponse {
  success: boolean;
  transactionId: string;
}

export interface Event {
  eventName: string;
  description: string;
  eventType: string;
  /** stringified JSON */
  schema: string;
  /** stringified JSON */
  metadata: string;
  accessibleToTeam: boolean;
  teamIds: string[];
}

export interface RegisterEventsRequest {
  source: Source;
  events: Event[];
  organizationId?: string | undefined;
}

export interface RegisterEventsResponse {
  success: boolean;
  transactionId: string;
}

export interface CompensateRegisterActivitiesRequest {
  transactionId: string;
}

export interface CompensateRegisterActivitiesResponse {
  success: boolean;
}

export interface CompensateRegisterEventsRequest {
  transactionId: string;
}

export interface CompensateRegisterEventsResponse {
  success: boolean;
}

export interface GetActivitiesRequest {
  source: Source;
  organizationId?: string | undefined;
}

export interface GetActivitiesResponse {
  activities: Activity[];
}

export interface GetEventsRequest {
  source: Source;
  organizationId?: string | undefined;
}

export interface GetEventsResponse {
  events: Event[];
}

export interface DeleteActivitiesRequest {
  identifier: string;
  /** Delete operation is only applicable for registered activities. Has to be provided. */
  organizationId: string;
}

export interface DeleteActivitiesResponse {
  success: boolean;
  transactionId: string;
}

export interface DeleteEventsRequest {
  eventType: string;
  /** Delete operation is only applicable for registered events. Has to be provided. */
  organizationId: string;
}

export interface DeleteEventsResponse {
  success: boolean;
  transactionId: string;
}

export interface CompensateDeleteActivitiesRequest {
  transactionId: string;
}

export interface CompensateDeleteActivitiesResponse {
  success: boolean;
}

export interface CompensateDeleteEventsRequest {
  transactionId: string;
}

export interface CompensateDeleteEventsResponse {
  success: boolean;
}

export interface UpdateAccessibleTeamsForActivitiesRequest {
  activityIdentifiers: string[];
  teamIds: string[];
}

export interface UpdateAccessibleTeamsForActivitiesResponse {
  success: boolean;
}

export interface UpdateAccessibleTeamsForEventsRequest {
  eventTypes: string[];
  teamIds: string[];
}

export interface UpdateAccessibleTeamsForEventsResponse {
  success: boolean;
}

export interface SeedWorkflowsRequest {
  organizationId: string;
  teamId: string;
}

export interface SeedWorkflowsResponse {
  success: boolean;
}

export const GRPC_WORKFLOWS_V1_PACKAGE_NAME = "grpc.workflows.v1";

/** Workflows service for workflows app. */

export interface WorkflowsServiceClient {
  /** Register activities */

  registerActivities(
    request: RegisterActivitiesRequest,
  ): Observable<RegisterActivitiesResponse>;

  /** Register events */

  registerEvents(
    request: RegisterEventsRequest,
  ): Observable<RegisterEventsResponse>;

  /** Compensate register activities */

  compensateRegisterActivities(
    request: CompensateRegisterActivitiesRequest,
  ): Observable<CompensateRegisterActivitiesResponse>;

  /** Compensate register events */

  compensateRegisterEvents(
    request: CompensateRegisterEventsRequest,
  ): Observable<CompensateRegisterEventsResponse>;

  /** Get activities */

  getActivities(
    request: GetActivitiesRequest,
  ): Observable<GetActivitiesResponse>;

  /** Get events */

  getEvents(request: GetEventsRequest): Observable<GetEventsResponse>;

  /** Delete activities */

  deleteActivities(
    request: DeleteActivitiesRequest,
  ): Observable<DeleteActivitiesResponse>;

  /** Delete events */

  deleteEvents(request: DeleteEventsRequest): Observable<DeleteEventsResponse>;

  /** Compensate delete activities */

  compensateDeleteActivities(
    request: CompensateDeleteActivitiesRequest,
  ): Observable<CompensateDeleteActivitiesResponse>;

  /** Compensate delete events */

  compensateDeleteEvents(
    request: CompensateDeleteEventsRequest,
  ): Observable<CompensateDeleteEventsResponse>;

  /** Update accessible teams for activities */

  updateAccessibleTeamsForActivities(
    request: UpdateAccessibleTeamsForActivitiesRequest,
  ): Observable<UpdateAccessibleTeamsForActivitiesResponse>;

  /** Update accessible teams for events */

  updateAccessibleTeamsForEvents(
    request: UpdateAccessibleTeamsForEventsRequest,
  ): Observable<UpdateAccessibleTeamsForEventsResponse>;

  /** Seed workflows */

  seedWorkflows(
    request: SeedWorkflowsRequest,
  ): Observable<SeedWorkflowsResponse>;
}

/** Workflows service for workflows app. */

export interface WorkflowsServiceController {
  /** Register activities */

  registerActivities(
    request: RegisterActivitiesRequest,
  ):
    | Promise<RegisterActivitiesResponse>
    | Observable<RegisterActivitiesResponse>
    | RegisterActivitiesResponse;

  /** Register events */

  registerEvents(
    request: RegisterEventsRequest,
  ):
    | Promise<RegisterEventsResponse>
    | Observable<RegisterEventsResponse>
    | RegisterEventsResponse;

  /** Compensate register activities */

  compensateRegisterActivities(
    request: CompensateRegisterActivitiesRequest,
  ):
    | Promise<CompensateRegisterActivitiesResponse>
    | Observable<CompensateRegisterActivitiesResponse>
    | CompensateRegisterActivitiesResponse;

  /** Compensate register events */

  compensateRegisterEvents(
    request: CompensateRegisterEventsRequest,
  ):
    | Promise<CompensateRegisterEventsResponse>
    | Observable<CompensateRegisterEventsResponse>
    | CompensateRegisterEventsResponse;

  /** Get activities */

  getActivities(
    request: GetActivitiesRequest,
  ):
    | Promise<GetActivitiesResponse>
    | Observable<GetActivitiesResponse>
    | GetActivitiesResponse;

  /** Get events */

  getEvents(
    request: GetEventsRequest,
  ):
    | Promise<GetEventsResponse>
    | Observable<GetEventsResponse>
    | GetEventsResponse;

  /** Delete activities */

  deleteActivities(
    request: DeleteActivitiesRequest,
  ):
    | Promise<DeleteActivitiesResponse>
    | Observable<DeleteActivitiesResponse>
    | DeleteActivitiesResponse;

  /** Delete events */

  deleteEvents(
    request: DeleteEventsRequest,
  ):
    | Promise<DeleteEventsResponse>
    | Observable<DeleteEventsResponse>
    | DeleteEventsResponse;

  /** Compensate delete activities */

  compensateDeleteActivities(
    request: CompensateDeleteActivitiesRequest,
  ):
    | Promise<CompensateDeleteActivitiesResponse>
    | Observable<CompensateDeleteActivitiesResponse>
    | CompensateDeleteActivitiesResponse;

  /** Compensate delete events */

  compensateDeleteEvents(
    request: CompensateDeleteEventsRequest,
  ):
    | Promise<CompensateDeleteEventsResponse>
    | Observable<CompensateDeleteEventsResponse>
    | CompensateDeleteEventsResponse;

  /** Update accessible teams for activities */

  updateAccessibleTeamsForActivities(
    request: UpdateAccessibleTeamsForActivitiesRequest,
  ):
    | Promise<UpdateAccessibleTeamsForActivitiesResponse>
    | Observable<UpdateAccessibleTeamsForActivitiesResponse>
    | UpdateAccessibleTeamsForActivitiesResponse;

  /** Update accessible teams for events */

  updateAccessibleTeamsForEvents(
    request: UpdateAccessibleTeamsForEventsRequest,
  ):
    | Promise<UpdateAccessibleTeamsForEventsResponse>
    | Observable<UpdateAccessibleTeamsForEventsResponse>
    | UpdateAccessibleTeamsForEventsResponse;

  /** Seed workflows */

  seedWorkflows(
    request: SeedWorkflowsRequest,
  ):
    | Promise<SeedWorkflowsResponse>
    | Observable<SeedWorkflowsResponse>
    | SeedWorkflowsResponse;
}

export function WorkflowsServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "registerActivities",
      "registerEvents",
      "compensateRegisterActivities",
      "compensateRegisterEvents",
      "getActivities",
      "getEvents",
      "deleteActivities",
      "deleteEvents",
      "compensateDeleteActivities",
      "compensateDeleteEvents",
      "updateAccessibleTeamsForActivities",
      "updateAccessibleTeamsForEvents",
      "seedWorkflows",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcMethod("WorkflowsService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(
        constructor.prototype,
        method,
      );
      GrpcStreamMethod("WorkflowsService", method)(
        constructor.prototype[method],
        method,
        descriptor,
      );
    }
  };
}

export const WORKFLOWS_SERVICE_NAME = "WorkflowsService";
