// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.1
// source: users/users.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.users.v1";

/** User type enum matching your TypeORM enum */
export enum UserType {
  USER_TYPE_UNSPECIFIED = 0,
  USER = 1,
  CUSTOMER_USER = 2,
  APP_USER = 3,
  ORG_ADMIN = 4,
  BOT_USER = 5,
  UNRECOGNIZED = -1,
}

export enum RollbackStatus {
  ROLLBACK_STATUS_UNSPECIFIED = 0,
  ROLLBACK_STATUS_SUCCESS = 1,
  ROLLBACK_STATUS_FAILED = 2,
  ROLLBACK_STATUS_USER_NOT_FOUND = 3,
  ROLLBACK_STATUS_INVALID_TRANSACTION = 4,
  UNRECOGNIZED = -1,
}

export interface Empty {
}

/** Compensation/rollback for bot uninstall response */
export interface RollbackBotUninstallResponse {
  success: boolean;
  message: string;
}

/** Compensation/rollback for bot uninstall request */
export interface RollbackBotUninstallRequest {
  appId: string;
  organizationId: string;
  teamIds: string[];
}

/** Uninstall bot request */
export interface UninstallBotRequest {
  appId: string;
  organizationId: string;
}

/** Uninstall bot response */
export interface UninstallBotResponse {
  success: boolean;
  message: string;
}

/** Request message for creating a bot user */
export interface CreateBotInstallationUserRequest {
  name: string;
  appId: string;
  organizationId: string;
  teamIds: string[];
  userType: UserType;
  avatarUrl?: string | undefined;
}

/** Create bot user response */
export interface CreateBotInstallationUserResponse {
  id: string;
  uid: string;
  name: string;
  organizationId: string;
  teamIds: string[];
  userType: UserType;
  /** For compensation/rollback */
  transactionId: string;
  accessToken: string;
  refreshToken: string;
  appKey: string;
  appSecretKey: string;
}

/** Compensation/rollback request */
export interface RollbackBotInstallationUserRequest {
  id: string;
  transactionId: string;
  teamIds: string[];
  reason: string;
  organizationId: string;
}

/** Rollback response */
export interface RollbackBotInstallationUserResponse {
  success: boolean;
  message: string;
  teamIds: string[];
  status: RollbackStatus;
}

/** Field metadata messages */
export interface GetUserFieldMetadataRequest {
  teamId?: string | undefined;
}

export interface GetUserFieldMetadataResponse {
  fields: { [key: string]: UserFieldMetadata };
}

export interface GetUserFieldMetadataResponse_FieldsEntry {
  key: string;
  value: UserFieldMetadata | undefined;
}

export interface GetUserSkillsFieldMetadataRequest {
}

export interface GetUserSkillsFieldMetadataResponse {
  fields: { [key: string]: UserFieldMetadata };
}

export interface GetUserSkillsFieldMetadataResponse_FieldsEntry {
  key: string;
  value: UserFieldMetadata | undefined;
}

export interface GetUserBusinessHoursConfigFieldMetadataRequest {
}

export interface GetUserBusinessHoursConfigFieldMetadataResponse {
  fields: { [key: string]: UserFieldMetadata };
}

export interface GetUserBusinessHoursConfigFieldMetadataResponse_FieldsEntry {
  key: string;
  value: UserFieldMetadata | undefined;
}

export interface GetTimeOffFieldMetadataRequest {
}

export interface GetTimeOffFieldMetadataResponse {
  fields: { [key: string]: UserFieldMetadata };
}

export interface GetTimeOffFieldMetadataResponse_FieldsEntry {
  key: string;
  value: UserFieldMetadata | undefined;
}

/** Entity data messages */
export interface GetUserDataRequest {
  userId: string;
  relations: string[];
}

export interface GetUserDataResponse {
  data: string;
}

export interface GetUserSkillsDataRequest {
  userId: string;
  relations: string[];
}

export interface GetUserSkillsDataResponse {
  data: string;
}

export interface GetUserBusinessHoursConfigDataRequest {
  userId: string;
  relations: string[];
}

export interface GetUserBusinessHoursConfigDataResponse {
  data: string;
}

export interface GetTimeOffDataRequest {
  userId: string;
  relations: string[];
}

export interface GetTimeOffDataResponse {
  data: string;
}

/** Field metadata structure for users */
export interface UserFieldMetadata {
  /** Type of the field */
  type: string;
  /** Label for displaying */
  label: string;
  /** Description for displaying */
  description?:
    | string
    | undefined;
  /** Key of the field in findEntity api response */
  expression: string;
  /** If the field is a standard field */
  standard: boolean;
  /** If the field is enabled for SLA */
  slaEnabled?:
    | boolean
    | undefined;
  /** Constraints for the field */
  constraints?:
    | UserFieldConstraints
    | undefined;
  /** Supported operators for the field */
  supportedOperators: UserFieldOperator[];
}

/** Field constraints for users */
export interface UserFieldConstraints {
  /** For dynamic choices */
  dynamicChoices: UserFieldOption[];
  /** Fixed options for choice type */
  options: UserFieldOption[];
  /** For lookup type */
  lookupType?:
    | string
    | undefined;
  /** Related entity type for lookup */
  relatedEntityType?:
    | string
    | undefined;
  /** For number type */
  min?: number | undefined;
  max?:
    | number
    | undefined;
  /** For string/text type */
  minLength?: number | undefined;
  maxLength?:
    | number
    | undefined;
  /** For array type */
  itemsType?:
    | string
    | undefined;
  /** Relationship value for lookup type */
  relationshipValue?: string | undefined;
}

/** Option for user choice fields */
export interface UserFieldOption {
  label: string;
  value: string;
}

/** Operator definition for user fields */
export interface UserFieldOperator {
  name: string;
  value: string;
}

/** Request to get user details */
export interface GetUsersDetailsRequest {
  userIds: string[];
}

export interface UserDetails {
  name: string;
  avatar: string;
  logoUrl?: string | undefined;
}

export interface GetUsersDetailsResponse {
  users: UserDetails[];
}

export interface GetOrganizationAdminRequest {
  organizationId: string;
}

export interface GetOrganizationAdminResponse {
  userId: string;
}

export interface FetchWorkflowBotRequest {
  organizationId: string;
}

export interface FetchWorkflowBotResponse {
  id: string;
  name: string;
  email: string;
  userType: UserType;
  organizationId: string;
}

export interface CheckUserAvailabilityRequest {
  userId: string;
}

export interface CheckUserAvailabilityResponse {
  isAvailable: boolean;
  reason: string;
}

export const GRPC_USERS_V1_PACKAGE_NAME = "grpc.users.v1";

/** Bot installation user service definition */

export interface BotInstallationUserServiceClient {
  /** Create a new bot user for installation */

  createBotInstallationUser(request: CreateBotInstallationUserRequest): Observable<CreateBotInstallationUserResponse>;

  /** Uninstall a bot */

  uninstallBot(request: UninstallBotRequest): Observable<UninstallBotResponse>;

  /** Compensation/rollback for bot installation user creation */

  rollbackBotInstallationUserCreation(
    request: RollbackBotInstallationUserRequest,
  ): Observable<RollbackBotInstallationUserResponse>;

  /** Compensation/rollback for bot uninstall */

  rollbackBotUninstall(request: RollbackBotUninstallRequest): Observable<RollbackBotUninstallResponse>;
}

/** Bot installation user service definition */

export interface BotInstallationUserServiceController {
  /** Create a new bot user for installation */

  createBotInstallationUser(
    request: CreateBotInstallationUserRequest,
  ):
    | Promise<CreateBotInstallationUserResponse>
    | Observable<CreateBotInstallationUserResponse>
    | CreateBotInstallationUserResponse;

  /** Uninstall a bot */

  uninstallBot(
    request: UninstallBotRequest,
  ): Promise<UninstallBotResponse> | Observable<UninstallBotResponse> | UninstallBotResponse;

  /** Compensation/rollback for bot installation user creation */

  rollbackBotInstallationUserCreation(
    request: RollbackBotInstallationUserRequest,
  ):
    | Promise<RollbackBotInstallationUserResponse>
    | Observable<RollbackBotInstallationUserResponse>
    | RollbackBotInstallationUserResponse;

  /** Compensation/rollback for bot uninstall */

  rollbackBotUninstall(
    request: RollbackBotUninstallRequest,
  ): Promise<RollbackBotUninstallResponse> | Observable<RollbackBotUninstallResponse> | RollbackBotUninstallResponse;
}

export function BotInstallationUserServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createBotInstallationUser",
      "uninstallBot",
      "rollbackBotInstallationUserCreation",
      "rollbackBotUninstall",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("BotInstallationUserService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("BotInstallationUserService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const BOT_INSTALLATION_USER_SERVICE_NAME = "BotInstallationUserService";

/** User Annotator service */

export interface UserAnnotatorClient {
  /** Field Metadata APIs */

  getUserFieldMetadata(request: GetUserFieldMetadataRequest): Observable<GetUserFieldMetadataResponse>;

  getUserSkillsFieldMetadata(
    request: GetUserSkillsFieldMetadataRequest,
  ): Observable<GetUserSkillsFieldMetadataResponse>;

  getUserBusinessHoursConfigFieldMetadata(
    request: GetUserBusinessHoursConfigFieldMetadataRequest,
  ): Observable<GetUserBusinessHoursConfigFieldMetadataResponse>;

  getTimeOffFieldMetadata(request: GetTimeOffFieldMetadataRequest): Observable<GetTimeOffFieldMetadataResponse>;

  /** Entity Data APIs */

  getUserData(request: GetUserDataRequest): Observable<GetUserDataResponse>;

  getUserSkillsData(request: GetUserSkillsDataRequest): Observable<GetUserSkillsDataResponse>;

  getUserBusinessHoursConfigData(
    request: GetUserBusinessHoursConfigDataRequest,
  ): Observable<GetUserBusinessHoursConfigDataResponse>;

  getTimeOffData(request: GetTimeOffDataRequest): Observable<GetTimeOffDataResponse>;
}

/** User Annotator service */

export interface UserAnnotatorController {
  /** Field Metadata APIs */

  getUserFieldMetadata(
    request: GetUserFieldMetadataRequest,
  ): Promise<GetUserFieldMetadataResponse> | Observable<GetUserFieldMetadataResponse> | GetUserFieldMetadataResponse;

  getUserSkillsFieldMetadata(
    request: GetUserSkillsFieldMetadataRequest,
  ):
    | Promise<GetUserSkillsFieldMetadataResponse>
    | Observable<GetUserSkillsFieldMetadataResponse>
    | GetUserSkillsFieldMetadataResponse;

  getUserBusinessHoursConfigFieldMetadata(
    request: GetUserBusinessHoursConfigFieldMetadataRequest,
  ):
    | Promise<GetUserBusinessHoursConfigFieldMetadataResponse>
    | Observable<GetUserBusinessHoursConfigFieldMetadataResponse>
    | GetUserBusinessHoursConfigFieldMetadataResponse;

  getTimeOffFieldMetadata(
    request: GetTimeOffFieldMetadataRequest,
  ):
    | Promise<GetTimeOffFieldMetadataResponse>
    | Observable<GetTimeOffFieldMetadataResponse>
    | GetTimeOffFieldMetadataResponse;

  /** Entity Data APIs */

  getUserData(
    request: GetUserDataRequest,
  ): Promise<GetUserDataResponse> | Observable<GetUserDataResponse> | GetUserDataResponse;

  getUserSkillsData(
    request: GetUserSkillsDataRequest,
  ): Promise<GetUserSkillsDataResponse> | Observable<GetUserSkillsDataResponse> | GetUserSkillsDataResponse;

  getUserBusinessHoursConfigData(
    request: GetUserBusinessHoursConfigDataRequest,
  ):
    | Promise<GetUserBusinessHoursConfigDataResponse>
    | Observable<GetUserBusinessHoursConfigDataResponse>
    | GetUserBusinessHoursConfigDataResponse;

  getTimeOffData(
    request: GetTimeOffDataRequest,
  ): Promise<GetTimeOffDataResponse> | Observable<GetTimeOffDataResponse> | GetTimeOffDataResponse;
}

export function UserAnnotatorControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getUserFieldMetadata",
      "getUserSkillsFieldMetadata",
      "getUserBusinessHoursConfigFieldMetadata",
      "getTimeOffFieldMetadata",
      "getUserData",
      "getUserSkillsData",
      "getUserBusinessHoursConfigData",
      "getTimeOffData",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("UserAnnotator", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("UserAnnotator", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const USER_ANNOTATOR_SERVICE_NAME = "UserAnnotator";

export interface UserServiceClient {
  getOrganizationAdmin(request: GetOrganizationAdminRequest): Observable<GetOrganizationAdminResponse>;

  fetchWorkflowBot(request: FetchWorkflowBotRequest): Observable<FetchWorkflowBotResponse>;

  checkUserAvailability(request: CheckUserAvailabilityRequest): Observable<CheckUserAvailabilityResponse>;

  /** Get details for multiple users */

  getUsersDetails(request: GetUsersDetailsRequest): Observable<GetUsersDetailsResponse>;
}

export interface UserServiceController {
  getOrganizationAdmin(
    request: GetOrganizationAdminRequest,
  ): Promise<GetOrganizationAdminResponse> | Observable<GetOrganizationAdminResponse> | GetOrganizationAdminResponse;

  fetchWorkflowBot(
    request: FetchWorkflowBotRequest,
  ): Promise<FetchWorkflowBotResponse> | Observable<FetchWorkflowBotResponse> | FetchWorkflowBotResponse;

  checkUserAvailability(
    request: CheckUserAvailabilityRequest,
  ): Promise<CheckUserAvailabilityResponse> | Observable<CheckUserAvailabilityResponse> | CheckUserAvailabilityResponse;

  /** Get details for multiple users */

  getUsersDetails(
    request: GetUsersDetailsRequest,
  ): Promise<GetUsersDetailsResponse> | Observable<GetUsersDetailsResponse> | GetUsersDetailsResponse;
}

export function UserServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getOrganizationAdmin",
      "fetchWorkflowBot",
      "checkUserAvailability",
      "getUsersDetails",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("UserService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("UserService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const USER_SERVICE_NAME = "UserService";
