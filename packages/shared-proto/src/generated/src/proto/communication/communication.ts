// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.2.7
//   protoc               v6.31.1
// source: communication/communication.proto

/* eslint-disable */
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "grpc.communication.v1";

export interface Empty {
}

export interface GetPreviousCommentRequest {
  commentId: string;
  entityType: string;
}

export interface CreateCommentRequest {
  content: string;
  contentHtml?: string | undefined;
  contentJson?: string | undefined;
  threadName?: string | undefined;
  entityType: string;
  entityId: string;
  parentCommentId?: string | undefined;
  commentVisibility?: string | undefined;
  commentType?: string | undefined;
  metadata?: string | undefined;
  attachmentIds: string[];
  customerEmail?: string | undefined;
  impersonatedUserEmail?: string | undefined;
  impersonatedUserName?: string | undefined;
  impersonatedUserAvatar?: string | undefined;
  shouldSendEmail?: boolean | undefined;
}

export interface ExternalStorageResponseDto {
  id: string;
  url: string;
  name: string;
  size: number;
  contentType: string;
  createdAt: string;
}

export interface EmailAddress {
  Name: string;
  Email: string;
  MailboxHash: string;
}

export interface EmailHeader {
  Name: string;
  Value: string;
}

export interface EmailData {
  to: EmailAddress[];
  cc: EmailAddress[];
  bcc: EmailAddress[];
  from: EmailAddress | undefined;
  headers: EmailHeader[];
  subject: string;
  isIgnoreSelf: boolean;
}

export interface CommentMetadata {
  reactions: { [key: string]: ReactionMetadata };
  replies: string[];
  mentions: string[];
  source?: string | undefined;
  integrationMetadata: IntegrationMetadata[];
  email: EmailData | undefined;
}

export interface CommentMetadata_ReactionsEntry {
  key: string;
  value: ReactionMetadata | undefined;
}

export interface ReactionMetadata {
  count: number;
  users: string[];
}

export interface IntegrationMetadata {
  source: string;
  externalId: string;
  channelId?: string | undefined;
  threadId?: string | undefined;
}

export interface CommentResponse {
  id: string;
  content: string;
  contentHtml: string;
  contentMarkdown: string;
  isEdited: boolean;
  threadName: string;
  commentVisibility: string;
  commentType: string;
  isPinned: boolean;
  sourceEmailId: string;
  metadata: CommentMetadata | undefined;
  parentCommentId: string;
  author: string;
  authorId: string;
  authorUserType: string;
  attachments: ExternalStorageResponseDto[];
  createdAt: string;
  updatedAt: string;
  customerContactId?: string | undefined;
  customerContactEmail?: string | undefined;
  impersonatedUserEmail?: string | undefined;
  impersonatedUserName?: string | undefined;
  impersonatedUserAvatar?: string | undefined;
}

export interface GetCommentsRequest {
  entityType: string;
  entityId: string;
  page?: number | undefined;
  limit?: number | undefined;
}

export interface GetCommentsResponse {
  comments: CommentResponse[];
}

export interface GetCommentByUserTypeRequest {
  entityType: string;
  entityId: string;
  userType: string;
  firstComment?: boolean | undefined;
}

export interface GetCommentRequest {
  commentId: string;
}

export interface UpdateCommentRequest {
  commentId: string;
  content?: string | undefined;
  threadName?: string | undefined;
  attachments: string[];
  metadata?: string | undefined;
}

export interface DeleteCommentRequest {
  commentId: string;
}

export interface GetCommentThreadsRequest {
  commentId: string;
  page?: number | undefined;
  limit?: number | undefined;
}

export interface GetCommentThreadsResponse {
  comments: CommentResponse[];
}

export interface AddReactionRequest {
  commentId: string;
  reactionName: string;
}

export interface RemoveReactionRequest {
  commentId: string;
  reactionName: string;
}

export interface ReactionResponse {
  success: boolean;
}

export interface GetEmojisResponse {
  emojis: EmojiResponse[];
}

export interface EmojiResponse {
  name: string;
  unicode: string;
  shortcode: string;
  category: string;
  keywords: string[];
}

export interface HasCustomerCommentRequest {
  entityType: string;
  entityId: string;
  since?: string | undefined;
  parentCommentId?: string | undefined;
}

export interface HasCustomerCommentResponse {
  hasComment: boolean;
}

export interface HasInternalMemberCommentRequest {
  entityType: string;
  entityId: string;
  since?: string | undefined;
  parentCommentId?: string | undefined;
  commentType?: string | undefined;
  commentVisibility?: string | undefined;
}

export interface HasInternalMemberCommentResponse {
  hasComment: boolean;
}

export interface GetCommentFieldMetadataRequest {
}

export interface GetCommentFieldMetadataResponse {
  fields: { [key: string]: CommentFieldMetadata };
}

export interface GetCommentFieldMetadataResponse_FieldsEntry {
  key: string;
  value: CommentFieldMetadata | undefined;
}

export interface GetCommentDataRequest {
  commentId: string;
  relations: string[];
}

export interface GetCommentDataResponse {
  data: string;
}

/** Field metadata structure for comments */
export interface CommentFieldMetadata {
  /** Type of the field */
  type: string;
  /** Label for displaying */
  label: string;
  /** Description for displaying */
  description?:
    | string
    | undefined;
  /** Key of the field in findEntity api response */
  expression: string;
  /** If the field is a standard field */
  standard: boolean;
  /** If the field is enabled for SLA */
  slaEnabled?:
    | boolean
    | undefined;
  /** Constraints for the field */
  constraints?:
    | CommentFieldConstraints
    | undefined;
  /** Supported operators for the field */
  supportedOperators: CommentFieldOperator[];
}

/** Field constraints for comments */
export interface CommentFieldConstraints {
  /** For choice type */
  dynamicOptions?:
    | boolean
    | undefined;
  /** Fixed options for choice type */
  options: CommentFieldOption[];
  /** For lookup type */
  lookupType?:
    | string
    | undefined;
  /** Related entity type for lookup */
  relatedEntityType?:
    | string
    | undefined;
  /** For number type */
  min?: number | undefined;
  max?:
    | number
    | undefined;
  /** For string/text type */
  minLength?: number | undefined;
  maxLength?:
    | number
    | undefined;
  /** For array type */
  itemsType?:
    | string
    | undefined;
  /** Relationship value for lookup type */
  relationshipValue?: string | undefined;
}

/** Option for comment choice fields */
export interface CommentFieldOption {
  label: string;
  value: string;
}

/** Operator definition for comment fields */
export interface CommentFieldOperator {
  name: string;
  value: string;
}

export const GRPC_COMMUNICATION_V1_PACKAGE_NAME = "grpc.communication.v1";

export interface CommunicationServiceClient {
  createCommentOnAnEntity(request: CreateCommentRequest): Observable<CommentResponse>;

  getCommentsOnAnEntity(request: GetCommentsRequest): Observable<CommentResponse>;

  getPreviousComment(request: GetPreviousCommentRequest): Observable<CommentResponse>;

  getComment(request: GetCommentRequest): Observable<CommentResponse>;

  getCommentByUserType(request: GetCommentByUserTypeRequest): Observable<CommentResponse>;

  getCommentThreads(request: GetCommentThreadsRequest): Observable<GetCommentThreadsResponse>;

  updateComment(request: UpdateCommentRequest): Observable<CommentResponse>;

  deleteComment(request: DeleteCommentRequest): Observable<Empty>;

  addReaction(request: AddReactionRequest): Observable<ReactionResponse>;

  removeReaction(request: RemoveReactionRequest): Observable<ReactionResponse>;

  getEmojis(request: Empty): Observable<GetEmojisResponse>;

  hasCustomerComment(request: HasCustomerCommentRequest): Observable<HasCustomerCommentResponse>;

  hasInternalMemberComment(request: HasInternalMemberCommentRequest): Observable<HasInternalMemberCommentResponse>;
}

export interface CommunicationServiceController {
  createCommentOnAnEntity(
    request: CreateCommentRequest,
  ): Promise<CommentResponse> | Observable<CommentResponse> | CommentResponse;

  getCommentsOnAnEntity(
    request: GetCommentsRequest,
  ): Promise<CommentResponse> | Observable<CommentResponse> | CommentResponse;

  getPreviousComment(
    request: GetPreviousCommentRequest,
  ): Promise<CommentResponse> | Observable<CommentResponse> | CommentResponse;

  getComment(request: GetCommentRequest): Promise<CommentResponse> | Observable<CommentResponse> | CommentResponse;

  getCommentByUserType(
    request: GetCommentByUserTypeRequest,
  ): Promise<CommentResponse> | Observable<CommentResponse> | CommentResponse;

  getCommentThreads(
    request: GetCommentThreadsRequest,
  ): Promise<GetCommentThreadsResponse> | Observable<GetCommentThreadsResponse> | GetCommentThreadsResponse;

  updateComment(
    request: UpdateCommentRequest,
  ): Promise<CommentResponse> | Observable<CommentResponse> | CommentResponse;

  deleteComment(request: DeleteCommentRequest): Promise<Empty> | Observable<Empty> | Empty;

  addReaction(request: AddReactionRequest): Promise<ReactionResponse> | Observable<ReactionResponse> | ReactionResponse;

  removeReaction(
    request: RemoveReactionRequest,
  ): Promise<ReactionResponse> | Observable<ReactionResponse> | ReactionResponse;

  getEmojis(request: Empty): Promise<GetEmojisResponse> | Observable<GetEmojisResponse> | GetEmojisResponse;

  hasCustomerComment(
    request: HasCustomerCommentRequest,
  ): Promise<HasCustomerCommentResponse> | Observable<HasCustomerCommentResponse> | HasCustomerCommentResponse;

  hasInternalMemberComment(
    request: HasInternalMemberCommentRequest,
  ):
    | Promise<HasInternalMemberCommentResponse>
    | Observable<HasInternalMemberCommentResponse>
    | HasInternalMemberCommentResponse;
}

export function CommunicationServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "createCommentOnAnEntity",
      "getCommentsOnAnEntity",
      "getPreviousComment",
      "getComment",
      "getCommentByUserType",
      "getCommentThreads",
      "updateComment",
      "deleteComment",
      "addReaction",
      "removeReaction",
      "getEmojis",
      "hasCustomerComment",
      "hasInternalMemberComment",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("CommunicationService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("CommunicationService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const COMMUNICATION_SERVICE_NAME = "CommunicationService";

export interface CommunicationAnnotatorServiceClient {
  getCommentFieldMetadata(request: GetCommentFieldMetadataRequest): Observable<GetCommentFieldMetadataResponse>;

  getCommentData(request: GetCommentDataRequest): Observable<GetCommentDataResponse>;
}

export interface CommunicationAnnotatorServiceController {
  getCommentFieldMetadata(
    request: GetCommentFieldMetadataRequest,
  ):
    | Promise<GetCommentFieldMetadataResponse>
    | Observable<GetCommentFieldMetadataResponse>
    | GetCommentFieldMetadataResponse;

  getCommentData(
    request: GetCommentDataRequest,
  ): Promise<GetCommentDataResponse> | Observable<GetCommentDataResponse> | GetCommentDataResponse;
}

export function CommunicationAnnotatorServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getCommentFieldMetadata", "getCommentData"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("CommunicationAnnotatorService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("CommunicationAnnotatorService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const COMMUNICATION_ANNOTATOR_SERVICE_NAME = "CommunicationAnnotatorService";
