/**
 * Converts a Unix timestamp (in milliseconds) to an ISO string date
 * @param timestamp - Unix timestamp in milliseconds (string or number)
 * @returns ISO string date or undefined if timestamp is not provided
 */
export const convertUnixToDate = (timestamp: string | number | undefined): string | undefined => {
  if (!timestamp) return undefined;
  const date = new Date(Number(timestamp)); // Convert milliseconds to Date
  return date.toISOString();
}; 
