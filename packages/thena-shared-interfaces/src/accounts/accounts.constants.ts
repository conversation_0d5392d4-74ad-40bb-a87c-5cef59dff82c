export enum AccountSinks {
  SLACK = "SLACK",
}

export interface AccountSinkMetadata<T extends AccountSinks> {
  /** The source of the sink. */
  source: T;

  /** The status of the sync. */
  syncStatus: "failed" | "success";

  /** The reason for the failure of the sync. */
  failureReason?: string;

  /** The date and time of the last sync. */
  lastSyncedAt: Date;

  /** The data of the sink. */
  data: Record<string, unknown>;
}
