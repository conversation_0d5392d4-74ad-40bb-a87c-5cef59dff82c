import { Type } from "class-transformer";
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from "class-validator";
import { z } from "zod";
import { AccountSinks, CustomerContactMetadata } from "../../accounts";

export const zCustomerContactSchema = z.object({
  firstName: z.string().describe("The first name of the user"),
  lastName: z.string().describe("The last name of the user"),
  email: z.string().describe("The email of the user"),
  phoneNumber: z.string().describe("The phone number of the user"),
  avatarUrl: z.string().describe("The avatar URL of the user"),
  externalId: z.string().describe("The external ID of the user"),
  contactType: z.string().describe("The contact type of the user"),
  accountIds: z.array(z.string()).describe("The account IDs of the user"),
  metadata: z.record(z.string(), z.any()).describe("The metadata of the user"),
});

export type zOneCustomerContactDTO = z.infer<typeof zCustomerContactSchema>;

export class OneCustomerContactDTO implements zOneCustomerContactDTO {
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsOptional()
  lastName: string;

  @IsString()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsOptional()
  phoneNumber: string;

  @IsString()
  @IsOptional()
  avatarUrl?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  accountIds?: string[];

  @IsString()
  @IsOptional()
  contactType?: string;

  @IsObject()
  @IsOptional()
  metadata?: CustomerContactMetadata;
}

export class IngestCustomerContactDTO {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OneCustomerContactDTO)
  users: OneCustomerContactDTO[];

  @IsString()
  @IsNotEmpty()
  @IsEnum(AccountSinks)
  sinkSource: AccountSinks;
}
