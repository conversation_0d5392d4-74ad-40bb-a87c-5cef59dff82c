import { MessageAttributeValue } from "@aws-sdk/client-sqs";
import {
  ContextUserType,
  RequiredMessageAttributes,
  SNSMessageAttributes,
} from "../interfaces/sns.interface";

enum EntityType {
  ORGANIZATION = "organization",
  USER = "user",
}

/**
 * Validates the format of an entity ID based on the entity type.
 * @param id - The ID to validate.
 * @param type - The type of entity to validate.
 * @returns `true` if the ID is valid, `false` otherwise.
 */
function isValidEntityId(id: string, type: EntityType): boolean {
  if (type === EntityType.ORGANIZATION) {
    return id.startsWith("E");
  }

  if (type === EntityType.USER) {
    return (
      id.startsWith("U") ||
      id.startsWith("C") ||
      id.startsWith("A") ||
      id.startsWith("O")
    );
  }

  return false;
}

// Helper function to validate Unix timestamp
function isValidUnixTimestamp(timestamp: string): boolean {
  const num = parseInt(timestamp);
  return (
    !Number.isNaN(num) &&
    num > 0 &&
    num <= Date.now() &&
    num.toString() === timestamp
  );
}

function validateUserType(type: string | undefined): ContextUserType {
  if (
    !type ||
    !Object.values(ContextUserType).includes(type as ContextUserType)
  ) {
    throw new Error(`Invalid context_user_type: ${type}`);
  }
  return type as ContextUserType;
}

// Function to transform and validate message attributes
export function transformToSNSMessageAttributes(
  attributes: RequiredMessageAttributes,
): SNSMessageAttributes {
  // Collect all validation errors
  const errors: string[] = [];
  if (!attributes.event_name) {
    errors.push("Event name is required");
  }
  if (!isValidEntityId(attributes.context_user_id, EntityType.USER)) {
    errors.push("Invalid user ID format");
  }

  if (
    !isValidEntityId(
      attributes.context_organization_id,
      EntityType.ORGANIZATION,
    )
  ) {
    errors.push("Invalid organization ID format");
  }

  if (!isValidUnixTimestamp(attributes.event_timestamp)) {
    errors.push("Invalid Unix timestamp format");
  }

  if (!Object.values(ContextUserType).includes(attributes.context_user_type)) {
    errors.push("Invalid context_user_type");
  }

  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.join(", ")}`);
  }

  // Transform to SNS format with validations
  const transformed: SNSMessageAttributes = {
    event_name: {
      DataType: "String",
      StringValue: attributes.event_name,
    },
    event_id: {
      DataType: "String",
      StringValue: attributes.event_id,
    },
    event_timestamp: {
      DataType: "String",
      StringValue: attributes.event_timestamp,
    },
    context_user_id: {
      DataType: "String",
      StringValue: attributes.context_user_id,
    },
    context_organization_id: {
      DataType: "String",
      StringValue: attributes.context_organization_id,
    },
    context_user_type: {
      DataType: "String",
      StringValue: attributes.context_user_type,
    },
  };

  // Add metadata if present
  if (attributes.event_metadata) {
    transformed.event_metadata = {
      DataType: "String",
      StringValue: attributes.event_metadata,
    };
  }

  return transformed;
}

export function parseMessageAttributes(
  messageAttributes: Record<string, MessageAttributeValue>,
): RequiredMessageAttributes {
  // Validate required fields
  const requiredFields = [
    "event_id",
    "event_name",
    "context_user_id",
    "context_user_type",
    "context_organization_id",
    "event_timestamp",
  ];

  const missingFields = requiredFields.filter(
    (field) => !messageAttributes[field]?.StringValue,
  );
  if (missingFields.length > 0) {
    throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
  }
  return {
    event_id: messageAttributes.event_id?.StringValue || "",
    event_name: messageAttributes.event_name?.StringValue || "",
    context_user_id: messageAttributes.context_user_id?.StringValue || "",
    context_user_type: validateUserType(
      messageAttributes.context_user_type?.StringValue,
    ),
    context_organization_id:
      messageAttributes.context_organization_id?.StringValue || "",
    event_timestamp: messageAttributes.event_timestamp?.StringValue || "",
  };
}

export function parseMessageBody(body: string): any {
  try {
    return JSON.parse(body);
  } catch (error: unknown) {
    if (error instanceof Error) {
      throw new Error(`Failed to parse message body as JSON: ${error.message}`);
    }
    throw new Error("Failed to parse message body as JSON");
  }
}
