import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from "npm:@supabase/supabase-js@2.44.0"
import { Client } from "npm:typesense@1.7.0"

// Types
interface TypesenseConfig {
  nodes: Array<{
    host: string
    port: number
    protocol: string
  }>
  apiKey: string
  connectionTimeoutSeconds: number
}

interface Ticket {
  id: number
  uid: string
  ticket_id: number
  title: string
  description: string
  team_id: number
  organization_id: number
  status_id: number
  status_name: string
  status_display_name: string
  priority_id: number
  priority_name: string
  priority_display_name: string
  type_id: number
  type_name: string
  assigned_agent_id: number
  assigned_agent_uid: string
  assigned_agent_email: string
  assigned_agent_name: string
  account_id: number
  account_name: string
  account_primary_domain: string
  account_website: string
  account_annual_revenue: number
  account_employees: number
  account_owner_id: number
  account_owner_email: string
  account_owner_name: string
  account_owner_user_type: string
  account_owner_status: string
  account_owner_timezone: string
  customer_contact_id: number
  contact_email: string
  contact_name: string
  contact_phone: string
  is_escalated: boolean
  is_private: boolean
  is_draft: boolean
  story_points: number
  source: string
  created_at: string
  updated_at: string
  due_date: string
  archived_at: string
  team_identifier: string
  deleted_at?: string
  [key: string]: any // For dynamic SLA fields
}

interface TypesenseDocument {
  id: string
  uid: string
  title: string
  ticket_id: string
  description: string
  ai_generated_summary: string
  ai_generated_title: string
  organization_id: number
  organization_uid: string
  form_id: number
  requestor_email: string
  submitter_email: string
  account_id: string
  account_name: string
  team_id: number
  team_uid: string
  team_name: string
  team_identifier: string
  sub_team_id: string
  assigned_agent_id: number
  assigned_agent_name: string
  assigned_agent_email: string
  status_id: number
  status_name: string
  priority_id: number
  priority_uid: string
  priority_name: string
  type_id: number
  type_name: string
  customer_contact_id: string
  is_escalated: boolean
  is_private: boolean
  is_draft: boolean
  sentiment_id: number
  source: string
  story_points: number
  created_at: number
  updated_at: number
  due_date: number
  archived_at: number
  deleted_at: number
  tags: any
  status_uid: string
  // Total Resolution Time SLA fields
  sla_total_resolution_time_status?: string | null
  sla_total_resolution_time_scheduled_at?: number
  sla_total_resolution_time_breached_at?: number
  sla_total_resolution_time_achieved_at?: number
  sla_total_resolution_time_paused_at?: number
  sla_total_resolution_time_resumed_at?: number
  sla_total_resolution_time_cancelled_at?: number
  sla_total_resolution_time_duration_to_breach_minutes?: number | null
  sla_total_resolution_time_paused_duration_minutes?: number | null
  sla_total_resolution_time_next_attempt_at?: number | null

  // First Time Response SLA fields
  sla_first_time_response_status?: string | null
  sla_first_time_response_scheduled_at?: number
  sla_first_time_response_breached_at?: number
  sla_first_time_response_achieved_at?: number
  sla_first_time_response_paused_at?: number
  sla_first_time_response_resumed_at?: number
  sla_first_time_response_cancelled_at?: number
  sla_first_time_response_duration_to_breach_minutes?: number | null
  sla_first_time_response_paused_duration_minutes?: number | null
  sla_first_time_response_next_attempt_at?: number | null

  // Next Time Response SLA fields
  sla_next_time_response_status?: string | null
  sla_next_time_response_scheduled_at?: number
  sla_next_time_response_breached_at?: number
  sla_next_time_response_achieved_at?: number
  sla_next_time_response_paused_at?: number
  sla_next_time_response_resumed_at?: number
  sla_next_time_response_cancelled_at?: number
  sla_next_time_response_duration_to_breach_minutes?: number | null
  sla_next_time_response_paused_duration_minutes?: number | null
  sla_next_time_response_next_attempt_at?: number | null

  // Update Time SLA fields
  sla_update_time_status?: string | null
  sla_update_time_scheduled_at?: number
  sla_update_time_breached_at?: number
  sla_update_time_achieved_at?: number
  sla_update_time_paused_at?: number
  sla_update_time_resumed_at?: number
  sla_update_time_cancelled_at?: number
  sla_update_time_duration_to_breach_minutes?: number | null
  sla_update_time_paused_duration_minutes?: number | null
  sla_update_time_next_attempt_at?: number | null

  full_text: string
  ticket_identifier: string
  metadata?: string | null
  type_icon?: string | null
  type_color?: string | null
  type_uid?: string | null
  form_uid?: string | null
  form_name?: string | null
  sentiment_uid?: string | null
  sentiment_name?: string | null
  sub_team_uid?: string | null
  sub_team_name?: string | null
  assigned_agent_uid?: string | null
  customer_contact_uid?: string | null
  customer_contact_name?: string | null
  customer_contact_email?: string | null
}

interface WebhookPayload {
  record: {
    id: number
    team_id: number
    deleted_at?: string
  }
  table: string
  type: string
}

// Configuration
const TYPESENSE_CONFIG: TypesenseConfig = {
  nodes: [{
    host: Deno.env.get('TYPESENSE_HOST')!,
    port: 443,
    protocol: 'https'
  }],
  apiKey: Deno.env.get('TYPESENSE_API_KEY')!,
  connectionTimeoutSeconds: 2
}

// Initialize clients
const typesenseClient = new Client(TYPESENSE_CONFIG)
const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
)

// Helper Functions
const createJsonResponse = (data: unknown, status = 200): Response => {
  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': 'application/json' }
  })
}

const toTimestamp = (dateStr: string | undefined): number | undefined => {
  if (!dateStr) return undefined
  return new Date(dateStr).getTime()
}

const getSlaValue = (slaData: any[], metric: string, field: string) => {
  const slas = slaData?.filter(s => s.metric === metric) || []
  if (slas.length === 0) return null

  // For numeric fields, return the maximum value
  if (typeof slas[0][field] === 'number') {
    return Math.max(...slas.map(s => s[field]))
  }

  // For non-numeric fields, return the first value
  return slas[0][field]
}

// Transform ticket data for Typesense
const transformToTypesenseDocument = (ticket: any, slaData: any[] = []): TypesenseDocument => {
  const allowedSlaMetrics = ['total_resolution_time', 'first_time_response', 'next_time_response', 'update_time']

  const slaFields = [
    { name: 'status', field: 'status' },
    { name: 'scheduled_at', field: 'scheduled_at', transform: toTimestamp },
    { name: 'breached_at', field: 'breached_at', transform: toTimestamp },
    { name: 'achieved_at', field: 'achieved_at', transform: toTimestamp },
    { name: 'paused_at', field: 'pause_at', transform: toTimestamp },
    { name: 'resumed_at', field: 'resumed_at', transform: toTimestamp },
    { name: 'cancelled_at', field: 'cancelled_at', transform: toTimestamp },
    { name: 'duration_to_breach_minutes', field: 'duration_to_breach_working_minutes' },
    { name: 'paused_duration_minutes', field: 'paused_duration_in_working_minutes' },
    { name: 'next_attempt_at', field: 'next_attempt_at', transform: toTimestamp }
  ]

  const processedSlaData: { [key: string]: { [key: string]: any } } = allowedSlaMetrics.reduce((acc, metric) => {
    const fields: { [key: string]: any } = slaFields.reduce((fieldAcc, { name, field, transform }) => {
      fieldAcc[`sla_${metric}_${name}`] = transform
        ? transform(getSlaValue(slaData, metric, field))
        : getSlaValue(slaData, metric, field)
      return fieldAcc
    }, {})

    acc[metric] = fields
    return acc
  }, {})


  // Extract tag names into an array
  const tagNames = (ticket.tags || []).map(tag => tag.name)

  // Create ticket identifier
  const ticketIdentifier = `${ticket.team?.identifier || ''}-${ticket.ticket_id || ''}`.trim()

  // Create full text field for search
  const fullText = [
    ticket.title,
    ticket.description,
    ticket.ai_generated_summary,
    ticket.requestor_email,
    ticket.submitter_email,
    ticket.assigned_agent?.name,
    ticket.assigned_agent?.email,
    ticket.status?.name,
    ticket.priority?.name,
    ticket.type?.name,
    ticket.account?.name,
    ticketIdentifier,
    ...tagNames
  ].filter(Boolean).join(' ')

  return {
    // Primary ticket fields
    id: ticket?.id?.toString() || '',
    uid: ticket.uid,
    ticket_id: ticket.ticket_id?.toString(),
    ticket_identifier: ticketIdentifier,
    title: ticket.title || '',
    description: ticket.description || '',
    ai_generated_summary: ticket.ai_generated_summary || '',
    ai_generated_title: ticket.ai_generated_title || '',
    metadata: ticket?.metadata ? JSON.stringify(ticket.metadata) : '',

    // Ticket metadata
    organization_id: ticket.organization_id,
    organization_uid: ticket.organization?.uid || '',
    form_id: ticket.form_id,
    requestor_email: ticket.requestor_email || '',
    submitter_email: ticket.submitter_email || '',
    account_id: ticket?.account_id?.toString(),
    account_name: ticket.account?.name || '',

    // Team & Agent information
    team_id: ticket.team_id,
    team_uid: ticket.team?.uid,
    team_name: ticket.team?.name || '',
    team_identifier: ticket.team?.identifier || '',
    sub_team_id: ticket?.sub_team_id?.toString(),
    assigned_agent_id: ticket.assigned_agent_id,
    assigned_agent_name: ticket.assigned_agent?.name || '',
    assigned_agent_email: ticket.assigned_agent?.email || '',

    // Status & Classification
    status_id: ticket.status_id,
    status_name: ticket.status?.name || '',
    status_uid: ticket.status?.uid || '',
    priority_id: ticket.priority_id,
    priority_uid: ticket.priority?.uid || '',
    priority_name: ticket.priority?.name || '',
    type_id: ticket.type_id,
    type_name: ticket.type?.name || '',
    customer_contact_id: ticket?.customer_contact_id?.toString(),
    is_escalated: ticket.is_escalated || false,
    is_private: ticket.is_private || false,
    is_draft: ticket.is_draft || false,
    sentiment_id: ticket.sentiment_id,
    source: ticket.source || '',
    story_points: ticket.story_points,

    // Dates as Unix timestamps
    created_at: toTimestamp(ticket.created_at)!,
    updated_at: toTimestamp(ticket.updated_at)!,
    due_date: toTimestamp(ticket.due_date)!,
    archived_at: toTimestamp(ticket.archived_at)!,
    deleted_at: toTimestamp(ticket.deleted_at)!,

    // Tags
    tags: { values: ticket.tags },

    ...(processedSlaData?.total_resolution_time || {}),
    ...(processedSlaData?.first_time_response || {}),
    ...(processedSlaData?.next_time_response || {}),
    ...(processedSlaData?.update_time || {}),
    // Search-specific fields
    full_text: fullText,

    type_icon: ticket.type?.icon || '',
    type_color: ticket.type?.color || '',
    type_uid: ticket.type?.uid || '',
    form_uid: ticket.form?.uid || '',
    form_name: ticket.form?.name || '',
    sentiment_uid: ticket.sentiment?.uid || '',
    sentiment_name: ticket.sentiment?.name || '',
    sub_team_uid: ticket?.sub_team_team?.uid || null,
    sub_team_name: ticket?.sub_team_team?.name || '',
    assigned_agent_uid: ticket?.assigned_agent?.uid || '',
    customer_contact_uid: ticket?.customer_contact?.uid || '',
    customer_contact_name: ticket?.customer_contact?.first_name + ' ' + ticket?.customer_contact?.last_name || '',
    customer_contact_email: ticket?.customer_contact?.email || '',

  }
}

// Main server handler
Deno.serve(async (req: Request) => {
  try {
    const body = await req.json()
    const { record, table } = body
    const type = body?.type?.toLowerCase()
    const uid = record?.job_id || record?.uid;

    console.log('ORIGIN Table:', table)
    // Fetch ticket data
    const { data: ticket, error } = await supabase
      .from('ticket')
      .select(`
        id,
        uid,
        organization_id,
        form_id,
        ticket_id,
        requestor_email,
        submitter_email,
        account_id,
        title,
        title_metadata,
        metadata,
        ai_generated_title,
        description,
        ai_generated_summary,
        sub_team_id,
        status_id,
        priority_id,
        type_id,
        assigned_agent_id,
        created_at,
        due_date,
        is_escalated,
        updated_at,
        is_private,
        story_points,
        is_draft,
        archived_at,
        source,
        deleted_at,
        customer_contact_id,
        team_id,
        sentiment_id,
        tags:tag!left(*),
        team:team_id(name,identifier,uid),
        organization:organization_id(uid),
        status:ticket_status(name,uid),
        priority:ticket_priority!left(name,uid),
        type:ticket_type(name,uid,color,icon),
        assigned_agent:assigned_agent_id!left(email, name, uid),
        account:account_id!left(name),
        form:form_id!left(id,uid,name),
        sentiment:ticket_sentiment!left(uid,name),
        sub_team_team:sub_team_id!left(name,uid),
        customer_contact:customer_contact_id!left(uid,email,first_name,last_name)
      `)
      .eq('uid', uid)
      .single()

    // Fetch SLA data
    const { data: sla, error: slaError } = await supabase
      .from('sla_scheduled')
      .select(`
        status,
        scheduled_at,
        breached_at,
        achieved_at,
        pause_at,
        resumed_at,
        cancelled_at,
        duration_to_breach_working_minutes,
        paused_duration_in_working_minutes,
        next_attempt_at,
        metric
      `)
      .eq('job_id', uid)

    if (error || slaError) {
      console.error('Error fetching ticket:', error, slaError)
      return createJsonResponse({ error: 'Failed to fetch ticket' }, 500)
    }

    if (!ticket) {
      console.error('Ticket not found:', uid)
      return createJsonResponse({ error: 'Ticket not found' }, 404)
    }

    // Handle ticket operations
    if ((type === 'insert' || type === 'update') && !record.deleted_at) {
      const payload = transformToTypesenseDocument(ticket, sla)
      await typesenseClient.collections('tickets').documents().upsert(payload)
    }

    if (type === 'delete' || record.deleted_at) {
      await typesenseClient.collections('tickets').documents().delete({
        filter_by: `uid:${record.uid}`
      })
    }

    return createJsonResponse('Ok')
  } catch (error) {
    console.error('Unexpected error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
})