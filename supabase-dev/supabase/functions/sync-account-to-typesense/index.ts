// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from "npm:@supabase/supabase-js@2.44.0"
import { Client } from "npm:typesense@1.7.0"

console.log("Hello from Functions!")

interface TypesenseConfig {
  nodes: Array<{
    host: string
    port: number
    protocol: string
  }>
  apiKey: string
  connectionTimeoutSeconds: number
}

interface Account {
  id: string
  uid: string
  organization_id: number
  name: string
  description: string | null
  is_active: boolean
  logo: string | null
  status: number | null
  classification: number | null
  health: number | null
  industry: number | null
  source: string | null
  primary_domain: string
  secondary_domain: string | null
  annual_revenue: number | null
  employees: number | null
  website: string | null
  billing_address: string | null
  shipping_address: string | null
  account_owner_id: number | null
  metadata: Record<string, any>
  created_at: string
  updated_at: string
  deleted_at: string | null
  account_owner?: {
    uid: string
    email: string
    name: string
    user_type: string
    status: string
    timezone: string
  }
  health_value?: {
    uid: string
    value: string
  }
  classification_value?: {
    uid: string
    value: string
  }
  status_value?: {
    uid: string
    value: string
  }
  industry_value?: {
    uid: string
    value: string
  }
}

interface TypesenseDocument {
  id?: string
  uid: string
  name: string
  description: string
  is_active: boolean
  logo: string
  status: number
  classification: number
  health: number
  industry: number
  source: string
  primary_domain: string
  secondary_domain: string
  annual_revenue: number
  employees: number
  website: string
  billing_address: string
  shipping_address: string
  account_owner_id: number
  account_owner_email: string
  account_owner_name: string
  account_owner_user_type: string
  account_owner_status: string
  account_owner_timezone: string
  metadata: string
  organization_id: number
  created_at: number
  updated_at: number
  deleted_at?: number | null
  account_owner_uid: string,
  // 
  health_uid?: string,
  health_value?: string,
  classification_uid?: string,
  classification_value?: string,
  status_uid?: string,
  status_value?: string,
  industry_uid?: string,
  industry_value?: string
}

interface WebhookPayload {
  record: {
    id: number
    organization_id: number
    deleted_at?: string
  }
  table: string
  type: string
}

const toTimestamp = (dateStr: string | null | undefined): number | undefined => {
  if (!dateStr) return undefined
  return new Date(dateStr).getTime()
}

// Typesense configuration
const TYPESENSE_CONFIG: TypesenseConfig = {
  nodes: [{
    host: Deno.env.get('TYPESENSE_HOST')!,
    port: 443,
    protocol: 'https'
  }],
  apiKey: Deno.env.get('TYPESENSE_API_KEY')!,
  connectionTimeoutSeconds: 2
}

// Initialize clients
const typesenseClient = new Client(TYPESENSE_CONFIG)
const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
)

// Helper function to create JSON response
const createJsonResponse = (data: unknown, status = 200): Response => {
  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': 'application/json' }
  })
}

// Transform account data for Typesense
const transformAccountToTypesenseDocument = (account: Account): TypesenseDocument => {
  const getDefaultValue = <T>(value: T | null | undefined, defaultValue: T): T => value || defaultValue

  return {
    id: getDefaultValue(account.id?.toString(), ''),
    uid: getDefaultValue(account.uid, ''),
    name: getDefaultValue(account.name, ''),
    description: getDefaultValue(account.description, ''),
    is_active: getDefaultValue(account.is_active, true),
    logo: getDefaultValue(account.logo, ''),
    status: getDefaultValue(account.status, 0),
    classification: getDefaultValue(account.classification, 0),
    health: getDefaultValue(account.health, 0),
    industry: getDefaultValue(account.industry, 0),
    source: getDefaultValue(account.source, ''),
    primary_domain: getDefaultValue(account.primary_domain, ''),
    secondary_domain: getDefaultValue(account.secondary_domain, ''),
    annual_revenue: getDefaultValue(account.annual_revenue, 0),
    employees: getDefaultValue(account.employees, 0),
    website: getDefaultValue(account.website, ''),
    billing_address: getDefaultValue(account.billing_address, ''),
    shipping_address: getDefaultValue(account.shipping_address, ''),
    account_owner_id: getDefaultValue(account.account_owner_id, 0),
    account_owner_email: getDefaultValue(account.account_owner?.email, ''),
    account_owner_name: getDefaultValue(account.account_owner?.name, ''),
    account_owner_user_type: getDefaultValue(account.account_owner?.user_type, ''),
    account_owner_status: getDefaultValue(account.account_owner?.status, ''),
    account_owner_timezone: getDefaultValue(account.account_owner?.timezone, ''),
    metadata: getDefaultValue(JSON.stringify(account.metadata), '{}'),
    organization_id: getDefaultValue(account.organization_id, 0),
    created_at: getDefaultValue(toTimestamp(account.created_at), new Date().getTime()),
    updated_at: getDefaultValue(toTimestamp(account.updated_at), new Date().getTime()),
    deleted_at: getDefaultValue(toTimestamp(account?.deleted_at), null),
    account_owner_uid: getDefaultValue(account.account_owner?.uid, ''),
    // 
    health_uid: getDefaultValue(account.health_value?.uid, undefined),
    health_value: getDefaultValue(account.health_value?.value, undefined),
    classification_uid: getDefaultValue(account.classification_value?.uid, undefined),
    classification_value: getDefaultValue(account.classification_value?.value, undefined),
    status_uid: getDefaultValue(account.status_value?.uid, undefined),
    status_value: getDefaultValue(account.status_value?.value, undefined),
    industry_uid: getDefaultValue(account.industry_value?.uid, undefined),
    industry_value: getDefaultValue(account.industry_value?.value, undefined)
  }
}

// Main server handler
Deno.serve(async (req: Request) => {
  try {
    const body = await req.json()
    const { record, table } = body
    const type = body?.type?.toLowerCase()

    // Fetch account data
    const { data: account, error } = await supabase
      .from('accounts')
      .select(`
        *,
        account_owner:account_owner_id!inner (
          uid,
          email,
          name,
          user_type,
          status,
          timezone
        ),
        health_value:health!left (
          id,
          uid,
          value
        ),
        classification_value:classification!left (
          id,
          uid,
          value
        ),
        status_value:status!left (
          id,
          uid,
          value
        ),
        industry_value:industry!left (
          id,
          uid,
          value
        )
      `)
      .eq('id', record.id)
      .single()

    if (error) {
      console.error('Error fetching account:', error)
      return createJsonResponse({ error: 'Failed to fetch account' }, 500)
    }

    if (!account) {
      console.error('Account not found:', record.id)
      return createJsonResponse({ error: 'Account not found' }, 404)
    }

    // Handle account operations
    if ((type === 'insert' || type === 'update') && !record.deleted_at) {
      const payload = transformAccountToTypesenseDocument(account)
      console.log('payload', { payload, account })
      const res = await typesenseClient.collections('accounts').documents().upsert(payload)
      console.log('res', res)
    }

    if (type === 'delete' || record.deleted_at) {
      const res = await typesenseClient.collections('accounts').documents().delete({
        filter_by: `psql_id:${record.id}`
      })
      console.log('res', res)
    }

    return createJsonResponse('Ok')
  } catch (error) {
    console.error('Unexpected error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/sync-account-to-typesense' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
