import { SupabaseClient } from "npm:@supabase/supabase-js@2.44.0"
import { Client } from "npm:typesense@1.7.0"

interface Ticket {
  id: number
  uid: string
  ticket_id: number
  title: string
  description: string
  team_id: number
  organization_id: number
  status_id: number
  status_name: string
  status_display_name: string
  priority_id: number
  priority_name: string
  priority_display_name: string
  type_id: number
  type_name: string
  assigned_agent_id: number
  assigned_agent_email: string
  assigned_agent_name: string
  account_id: number
  account_name: string
  account_primary_domain: string
  account_website: string
  account_annual_revenue: number
  account_employees: number
  account_owner_id: number
  account_owner_email: string
  account_owner_name: string
  account_owner_user_type: string
  account_owner_status: string
  account_owner_timezone: string
  customer_contact_id: number
  contact_email: string
  contact_name: string
  contact_phone: string
  is_escalated: boolean
  is_private: boolean
  is_draft: boolean
  story_points: number
  source: string
  created_at: string
  updated_at: string
  due_date: string
  archived_at: string
  team_identifier: string
  deleted_at?: string
  tags?: Array<{ name: string }>
  team?: {
    name: string
    uid: string
    identifier: string
  }
  organization?: {
    uid: string
  }
  status?: {
    name: string
    uid: string
  }
  priority?: {
    name: string
    uid: string
  }
  type?: {
    name: string
    uid: string
    icon: string
    color: string
  }
  assigned_agent?: {
    uid: string
    email: string
    name: string
  }
  account?: {
    name: string
  }
  form?: {
    uid: string
    name: string
  }
  sentiment?: {
    uid: string
    name: string
  }
  sub_team_team?: {
    uid: string
    name: string
  }
  [key: string]: any // For dynamic SLA fields
}

interface TypesenseDocument {
  id: string
  uid: string
  title: string
  ticket_id: string
  description: string
  ai_generated_summary: string
  ai_generated_title: string
  organization_id: number
  organization_uid: string
  form_id: number
  requestor_email: string
  submitter_email: string
  account_id: string
  account_name: string
  team_id: number
  team_uid: string | null
  team_name: string
  team_identifier: string
  sub_team_id: string
  sub_team_name: string
  assigned_agent_id: number
  assigned_agent_uid: string
  assigned_agent_name: string
  assigned_agent_email: string
  status_id: number
  status_name: string
  status_uid: string
  priority_id: number
  priority_uid: string
  priority_name: string
  type_id: number
  type_name: string
  customer_contact_id: string
  is_escalated: boolean
  is_private: boolean
  is_draft: boolean
  sentiment_id: number
  source: string
  story_points: number
  created_at: number
  updated_at: number
  due_date: number
  archived_at: number
  deleted_at: number
  tags: any
  metadata?: string | null
  type_icon?: string | null
  type_color?: string | null
  type_uid?: string | null
  form_uid?: string | null
  form_name?: string | null
  sentiment_uid?: string | null
  sentiment_name?: string | null
  sub_team_uid?: string | null

  // Total Resolution Time SLA fields
  sla_total_resolution_time_status?: string | null
  sla_total_resolution_time_scheduled_at?: number
  sla_total_resolution_time_breached_at?: number
  sla_total_resolution_time_achieved_at?: number
  sla_total_resolution_time_paused_at?: number
  sla_total_resolution_time_resumed_at?: number
  sla_total_resolution_time_cancelled_at?: number
  sla_total_resolution_time_duration_to_breach_minutes?: number | null
  sla_total_resolution_time_paused_duration_minutes?: number | null
  sla_total_resolution_time_next_attempt_at?: number | null

  // First Time Response SLA fields
  sla_first_time_response_status?: string | null
  sla_first_time_response_scheduled_at?: number
  sla_first_time_response_breached_at?: number
  sla_first_time_response_achieved_at?: number
  sla_first_time_response_paused_at?: number
  sla_first_time_response_resumed_at?: number
  sla_first_time_response_cancelled_at?: number
  sla_first_time_response_duration_to_breach_minutes?: number | null
  sla_first_time_response_paused_duration_minutes?: number | null
  sla_first_time_response_next_attempt_at?: number | null

  // Next Time Response SLA fields
  sla_next_time_response_status?: string | null
  sla_next_time_response_scheduled_at?: number
  sla_next_time_response_breached_at?: number
  sla_next_time_response_achieved_at?: number
  sla_next_time_response_paused_at?: number
  sla_next_time_response_resumed_at?: number
  sla_next_time_response_cancelled_at?: number
  sla_next_time_response_duration_to_breach_minutes?: number | null
  sla_next_time_response_paused_duration_minutes?: number | null
  sla_next_time_response_next_attempt_at?: number | null

  // Update Time SLA fields
  sla_update_time_status?: string | null
  sla_update_time_scheduled_at?: number
  sla_update_time_breached_at?: number
  sla_update_time_achieved_at?: number
  sla_update_time_paused_at?: number
  sla_update_time_resumed_at?: number
  sla_update_time_cancelled_at?: number
  sla_update_time_duration_to_breach_minutes?: number | null
  sla_update_time_paused_duration_minutes?: number | null
  sla_update_time_next_attempt_at?: number | null

  full_text: string
  ticket_identifier: string
  customer_contact_uid?: string | null
  customer_contact_name?: string | null
  customer_contact_email?: string | null
}

const toTimestamp = (dateStr: string | undefined): number | undefined => {
  if (!dateStr) return undefined
  return new Date(dateStr).getTime()
}

const getSlaValue = (slaData: any[], metric: string, field: string) => {
  const slas = slaData?.filter(s => s.metric === metric) || []
  if (slas.length === 0) return null

  // For numeric fields, return the maximum value
  if (typeof slas[0][field] === 'number') {
    return Math.max(...slas.map(s => s[field]))
  }

  // For non-numeric fields, return the first value
  return slas[0][field]
}

// Transform ticket data for Typesense
const transformTicketToTypesenseDocument = (ticket: Ticket, slaData: any[] = []): TypesenseDocument => {
  const allowedSlaMetrics = ['total_resolution_time', 'first_time_response', 'next_time_response', 'update_time']

  const slaFields = [
    { name: 'status', field: 'status' },
    { name: 'scheduled_at', field: 'scheduled_at', transform: toTimestamp },
    { name: 'breached_at', field: 'breached_at', transform: toTimestamp },
    { name: 'achieved_at', field: 'achieved_at', transform: toTimestamp },
    { name: 'paused_at', field: 'pause_at', transform: toTimestamp },
    { name: 'resumed_at', field: 'resumed_at', transform: toTimestamp },
    { name: 'cancelled_at', field: 'cancelled_at', transform: toTimestamp },
    { name: 'duration_to_breach_minutes', field: 'duration_to_breach_working_minutes' },
    { name: 'paused_duration_minutes', field: 'paused_duration_in_working_minutes' },
    { name: 'next_attempt_at', field: 'next_attempt_at', transform: toTimestamp }
  ]

  const processedSlaData: { [key: string]: { [key: string]: any } } = allowedSlaMetrics.reduce((acc, metric) => {
    const fields: { [key: string]: any } = slaFields.reduce((fieldAcc, { name, field, transform }) => {
      fieldAcc[`sla_${metric}_${name}`] = transform
        ? transform(getSlaValue(slaData, metric, field))
        : getSlaValue(slaData, metric, field)
      return fieldAcc
    }, {})

    acc[metric] = fields
    return acc
  }, {})

  // Extract tag names into an array
  const tagNames = (ticket.tags || []).map(tag => tag.name)

  // Create ticket identifier
  const ticketIdentifier = `${ticket.team?.identifier || ''}-${ticket.ticket_id || ''}`.trim()

  // Create full text field for search
  const fullText = [
    ticket.title,
    ticket.description,
    ticket.ai_generated_summary,
    ticket.requestor_email,
    ticket.submitter_email,
    ticket.assigned_agent?.name,
    ticket.assigned_agent?.email,
    ticket.status?.name,
    ticket.priority?.name,
    ticket.type?.name,
    ticket.account?.name,
    ticketIdentifier,
    ...tagNames
  ].filter(Boolean).join(' ')

  return {
    // Primary ticket fields
    id: ticket?.id?.toString() || '',
    uid: ticket.uid,
    ticket_id: ticket.ticket_id.toString() || '',
    ticket_identifier: ticketIdentifier,
    title: ticket.title || '',
    description: ticket.description || '',
    ai_generated_summary: ticket.ai_generated_summary || '',
    ai_generated_title: ticket.ai_generated_title || '',
    metadata: ticket?.metadata ? JSON.stringify(ticket.metadata) : '',

    // Ticket metadata
    organization_id: ticket.organization_id,
    organization_uid: ticket.organization?.uid || '',
    form_id: ticket.form_id,
    requestor_email: ticket.requestor_email || '',
    submitter_email: ticket.submitter_email || '',
    account_id: ticket?.account_id?.toString(),
    account_name: ticket.account?.name || '',

    // Team & Agent information
    team_id: ticket.team_id,
    team_uid: ticket.team?.uid || null,
    team_name: ticket.team?.name || '',
    team_identifier: ticket.team?.identifier || '',
    sub_team_id: ticket?.sub_team_id?.toString(),
    sub_team_name: ticket?.sub_team_team?.name || '',
    assigned_agent_id: ticket.assigned_agent_id,
    assigned_agent_name: ticket.assigned_agent?.name || '',
    assigned_agent_email: ticket.assigned_agent?.email || '',
    assigned_agent_uid: ticket.assigned_agent?.uid || '',

    // Status & Classification
    status_id: ticket.status_id,
    status_name: ticket.status?.name || '',
    status_uid: ticket.status?.uid || '',
    priority_id: ticket.priority_id,
    priority_uid: ticket.priority?.uid || '',
    priority_name: ticket.priority?.name || '',
    type_id: ticket.type_id,
    type_name: ticket.type?.name || '',
    customer_contact_id: ticket?.customer_contact_id?.toString(),
    is_escalated: ticket.is_escalated || false,
    is_private: ticket.is_private || false,
    is_draft: ticket.is_draft || false,
    sentiment_id: ticket.sentiment_id,
    source: ticket.source || '',
    story_points: ticket.story_points,

    // Dates as Unix timestamps
    created_at: toTimestamp(ticket.created_at)!,
    updated_at: toTimestamp(ticket.updated_at)!,
    due_date: toTimestamp(ticket.due_date)!,
    archived_at: toTimestamp(ticket.archived_at)!,
    deleted_at: toTimestamp(ticket.deleted_at)!,

    // Tags
    tags: { values: ticket.tags },

    ...(processedSlaData?.total_resolution_time || {}),
    ...(processedSlaData?.first_time_response || {}),
    ...(processedSlaData?.next_time_response || {}),
    ...(processedSlaData?.update_time || {}),

    // Search-specific fields
    full_text: fullText,

    type_icon: ticket.type?.icon || '',
    type_color: ticket.type?.color || '',
    type_uid: ticket.type?.uid || '',
    form_uid: ticket.form?.uid || '',
    form_name: ticket.form?.name || '',
    sentiment_uid: ticket.sentiment?.uid || '',
    sentiment_name: ticket.sentiment?.name || '',
    sub_team_uid: ticket?.sub_team_team?.uid || null,
    customer_contact_uid: ticket?.customer_contact?.uid || '',
    customer_contact_name: ticket?.customer_contact?.first_name + ' ' + ticket?.customer_contact?.last_name || '',
    customer_contact_email: ticket?.customer_contact?.email || '',
  }
}

// Helper function to wait between requests
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Retry wrapper with exponential backoff
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      console.log(`Attempt ${attempt} failed:`, error instanceof Error ? error.message : error)

      if (attempt === maxRetries) {
        throw lastError
      }

      // Exponential backoff: wait longer after each failure
      const delay = baseDelay * Math.pow(2, attempt - 1)
      console.log(`Waiting ${delay}ms before retry...`)
      await sleep(delay)
    }
  }

  throw lastError!
}

export async function syncTicketsToTypesense(
  typesenseClient: Client,
  supabase: SupabaseClient,
  team: { id: number; uid: string }
) {
  try {
    console.log('Syncing tickets to Typesense for team', team.uid)

    // Reduce page size to handle large datasets more reliably
    const PAGE_SIZE = 500 // Reduced from 1000
    const TYPESENSE_BATCH_SIZE = 100 // Import in smaller batches

    let currentPage = 0
    let hasMore = true
    let totalProcessed = 0
    let totalErrors = 0

    // Delete existing documents first
    // console.log('Deleting existing documents for team:', team.id)
    // try {
    //   await withRetry(async () => {
    //     return await typesenseClient.collections('tickets').documents().delete({
    //       filter_by: `team_id:${team.id}`
    //     })
    //   })
    //   console.log('Successfully deleted existing documents')
    // } catch (error) {
    //   console.warn('Failed to delete existing documents, continuing...', error)
    // }

    while (hasMore) {
      console.log(`\n--- Processing page ${currentPage + 1} ---`)

      // Fetch tickets with pagination
      const { data: tickets, error } = await supabase
        .from('ticket')
        .select(`
          id,
          uid,
          organization_id,
          form_id,
          ticket_id,
          requestor_email,
          submitter_email,
          account_id,
          title,
          title_metadata,
          metadata,
          ai_generated_title,
          description,
          ai_generated_summary,
          sub_team_id,
          status_id,
          priority_id,
          type_id,
          assigned_agent_id,
          created_at,
          due_date,
          is_escalated,
          updated_at,
          is_private,
          story_points,
          is_draft,
          archived_at,
          source,
          deleted_at,
          customer_contact_id,
          team_id,
          sentiment_id,
          tags:tag!left(*),
          team:team_id(name,identifier,uid),
          organization:organization_id(uid),
          status:ticket_status(name,uid),
          priority:ticket_priority!left(name,uid),
          type:ticket_type(name,uid,color,icon),
          assigned_agent:assigned_agent_id!left(email, name, uid),
          account:account_id!left(name),
          form:form_id!left(id,uid,name),
          sentiment:ticket_sentiment!left(uid,name),
          sub_team_team:sub_team_id!left(name,uid),
          customer_contact:customer_contact_id!left(uid,email,first_name,last_name)
        `)
        // .in('organization_id', [14])
        .is('deleted_at', null)
        // .in('organization_id', [14, 266, 59, 201])
        .eq('team_id', team.id)

        .order('id', { ascending: true })
        .range(currentPage * PAGE_SIZE, (currentPage + 1) * PAGE_SIZE - 1)


      if (error) {
        console.error('Error fetching tickets:', error)
        throw new Error(`Failed to fetch tickets: ${error.message}`)
      }

      if (!tickets || tickets.length === 0) {
        console.log('No more tickets to process')
        hasMore = false
        break
      }

      console.log(`Fetched ${tickets.length} tickets from Supabase`)

      // Transform tickets to Typesense documents
      const documents = await Promise.all(tickets.map(async ticket => {
        // Fetch SLA data for this specific ticket
        const { data: ticketSlaData, error: slaError } = await supabase
          .from('sla_scheduled')
          .select(`
            status,
            scheduled_at,
            breached_at,
            achieved_at,
            pause_at,
            resumed_at,
            cancelled_at,
            duration_to_breach_working_minutes,
            paused_duration_in_working_minutes,
            next_attempt_at,
            metric
          `)
          .eq('job_id', ticket.uid)

        if (slaError) {
          console.error(`Error fetching SLA data for ticket ${ticket.uid}:`, slaError)
          return null
        }

        return transformTicketToTypesenseDocument(ticket, ticketSlaData || [])
      }))

      // Filter out any null documents (from failed SLA fetches) and invalid documents
      const validDocuments = documents.filter((doc): doc is TypesenseDocument =>
        doc !== null && doc.id && doc.title
      )

      if (validDocuments.length === 0) {
        console.log('No valid documents to import, skipping...')
        currentPage++
        continue
      }

      console.log(`Transformed ${validDocuments.length} valid documents`)

      try {
        // Import documents in smaller batches
        const importResults = await importDocumentsInBatches(
          typesenseClient,
          validDocuments,
          TYPESENSE_BATCH_SIZE
        )

        // Check for errors in import results
        let batchErrors = 0
        importResults.forEach((result, batchIndex) => {
          if (Array.isArray(result)) {
            result.forEach((item, itemIndex) => {
              if (!item.success) {
                console.error(`Error in batch ${batchIndex}, item ${itemIndex}:`, item.error)
                batchErrors++
              }
            })
          }
        })

        totalErrors += batchErrors
        totalProcessed += validDocuments.length

        console.log(`Page ${currentPage + 1} complete:`)
        console.log(`  - Processed: ${validDocuments.length} documents`)
        console.log(`  - Errors in this batch: ${batchErrors}`)
        console.log(`  - Total processed: ${totalProcessed}`)
        console.log(`  - Total errors: ${totalErrors}`)

      } catch (error) {
        console.error(`Failed to import page ${currentPage + 1}:`, error)
        // Continue with next page instead of failing completely
        totalErrors += validDocuments.length
      }

      currentPage++

      // Add a longer delay between pages for large datasets
      await sleep(500)
    }

    console.log('\n=== Sync Complete ===')
    console.log(`Total documents processed: ${totalProcessed}`)
    console.log(`Total errors: ${totalErrors}`)
    console.log(`Success rate: ${((totalProcessed - totalErrors) / totalProcessed * 100).toFixed(2)}%`)

    if (totalErrors > 0) {
      console.warn(`Sync completed with ${totalErrors} errors. Consider investigating failed documents.`)
    }

  } catch (error) {
    console.error('Critical error during sync:', error)
    throw error
  }
}

// Helper function to import documents in batches
async function importDocumentsInBatches(
  typesenseClient: Client,
  documents: TypesenseDocument[],
  batchSize: number = 100
) {
  const results: any = []

  for (let i = 0; i < documents.length; i += batchSize) {
    const batch = documents.slice(i, i + batchSize)
    console.log(`Importing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(documents.length / batchSize)} (${batch.length} documents)`)

    const result = await withRetry(async () => {
      return await typesenseClient.collections('tickets').documents().import(batch, {
        action: 'upsert'
      })
    })

    results.push(result)

    // Add a small delay between batches to prevent overwhelming the server
    await sleep(100)
  }

  return results
}
