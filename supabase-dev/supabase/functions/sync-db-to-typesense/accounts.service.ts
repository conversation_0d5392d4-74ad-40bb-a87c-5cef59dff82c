import { Client, SupabaseClient } from "npm:typesense@1.7.0"

interface Account {
  id: string
  uid: string
  organization_id: number
  name: string
  description: string | null
  is_active: boolean
  logo: string | null
  status: number | null
  classification: number | null
  health: number | null
  industry: number | null
  source: string | null
  primary_domain: string
  secondary_domain: string | null
  annual_revenue: number | null
  employees: number | null
  website: string | null
  billing_address: string | null
  shipping_address: string | null
  account_owner_id: number | null
  metadata: Record<string, any>
  created_at: string
  updated_at: string
  deleted_at: string | null
  account_owner?: {
    email: string
    name: string
    user_type: string
    status: string
    timezone: string
  },
  health_value?: {
    uid: string
    value: string
  }
  classification_value?: {
    uid: string
    value: string
  }
  status_value?: {
    uid: string
    value: string
  }
  industry_value?: {
    uid: string
    value: string
  }
}

interface AccountTypesenseDocument {
  id?: string
  uid: string
  name: string
  description: string
  is_active: boolean
  logo: string
  status: number
  classification: number
  health: number
  industry: number
  source: string
  primary_domain: string
  secondary_domain: string
  annual_revenue: number
  employees: number
  website: string
  billing_address: string
  shipping_address: string
  account_owner_id: number
  account_owner_email: string
  account_owner_name: string
  account_owner_user_type: string
  account_owner_status: string
  account_owner_timezone: string
  metadata: string
  organization_id: number
  created_at: number
  updated_at: number
  deleted_at?: number | null
  account_owner_uid: string,
  health_uid: string,
  health_value: string,
  classification_uid: string,
  classification_value: string,
  status_uid: string,
  status_value: string,
  industry_uid: string,
  industry_value: string
}

const toTimestamp = (dateStr: string | null | undefined): number | undefined => {
  if (!dateStr) return undefined
  return new Date(dateStr).getTime()
}

// Transform account data for Typesense
const transformAccountToTypesenseDocument = (account: Account): AccountTypesenseDocument => {
  const getDefaultValue = <T>(value: T | null | undefined, defaultValue: T): T => value || defaultValue

  return {
    id: getDefaultValue(account.id?.toString(), ''),
    uid: getDefaultValue(account.uid, ''),
    name: getDefaultValue(account.name, ''),
    description: getDefaultValue(account.description, ''),
    is_active: getDefaultValue(account.is_active, true),
    logo: getDefaultValue(account.logo, ''),
    status: getDefaultValue(account.status, 0),
    classification: getDefaultValue(account.classification, 0),
    health: getDefaultValue(account.health, 0),
    industry: getDefaultValue(account.industry, 0),
    source: getDefaultValue(account.source, ''),
    primary_domain: getDefaultValue(account.primary_domain, ''),
    secondary_domain: getDefaultValue(account.secondary_domain, ''),
    annual_revenue: getDefaultValue(account.annual_revenue, 0),
    employees: getDefaultValue(account.employees, 0),
    website: getDefaultValue(account.website, ''),
    billing_address: getDefaultValue(account.billing_address, ''),
    shipping_address: getDefaultValue(account.shipping_address, ''),
    account_owner_id: getDefaultValue(account.account_owner_id, 0),
    account_owner_email: getDefaultValue(account.account_owner?.email, ''),
    account_owner_name: getDefaultValue(account.account_owner?.name, ''),
    account_owner_user_type: getDefaultValue(account.account_owner?.user_type, ''),
    account_owner_status: getDefaultValue(account.account_owner?.status, ''),
    account_owner_timezone: getDefaultValue(account.account_owner?.timezone, ''),
    metadata: getDefaultValue(JSON.stringify(account.metadata), '{}'),
    organization_id: getDefaultValue(account.organization_id, 0),
    created_at: getDefaultValue(toTimestamp(account.created_at), new Date().getTime()),
    updated_at: getDefaultValue(toTimestamp(account.updated_at), new Date().getTime()),
    deleted_at: getDefaultValue(toTimestamp(account?.deleted_at), null),
    account_owner_uid: getDefaultValue(account.account_owner?.uid, ''),
    // 
    health_uid: getDefaultValue(account.health_value?.uid, ''),
    health_value: getDefaultValue(account.health_value?.value, ''),
    classification_uid: getDefaultValue(account.classification_value?.uid, ''),
    classification_value: getDefaultValue(account.classification_value?.value, ''),
    status_uid: getDefaultValue(account.status_value?.uid, ''),
    status_value: getDefaultValue(account.status_value?.value, ''),
    industry_uid: getDefaultValue(account.industry_value?.uid, ''),
    industry_value: getDefaultValue(account.industry_value?.value, '')
  }
}

export async function syncAccountsToTypesense(typesenseClient: Client, supabase: SupabaseClient) {
  try {
    console.log('Syncing accounts to Typesense')
    const PAGE_SIZE = 1000
    let currentPage = 0
    let hasMore = true
    let totalProcessed = 0

    // Delete existing documents first
    // console.log('Deleting existing documents')
    // await typesenseClient.collections('accounts').documents().delete({
    //   filter_by: 'id:*'
    // })

    while (hasMore) {
      // Fetch accounts with pagination
      const { data: accounts, error } = await supabase
        .from('accounts')
        .select(`
          *,
          account_owner:account_owner_id!inner (
            uid,
            email,
            name,
            user_type,
            status,
            timezone
          ),
          health_value:health!left (
            id,
            uid,
            value
          ),
          classification_value:classification!left (
            id,
            uid,
            value
          ),
          status_value:status!left (
            id,
            uid,
            value
          ),
          industry_value:industry!left (
            id,
            uid,
            value
          )
        `)
        .range(currentPage * PAGE_SIZE, (currentPage + 1) * PAGE_SIZE - 1)

      if (error) {
        console.error('Error fetching accounts:', error)
        throw new Error('Failed to fetch accounts')
      }

      if (!accounts || accounts.length === 0) {
        hasMore = false
        continue
      }

      // Transform and upsert accounts to Typesense
      const documents = accounts.map(transformAccountToTypesenseDocument)

      console.log(`Upserting page ${currentPage + 1} with ${documents.length} documents`)
      // Upsert documents for current page
      const response = await typesenseClient.collections('accounts').documents().import(documents)

      totalProcessed += documents.length
      currentPage++

      console.log(`Processed ${totalProcessed} accounts so far`)
      console.log('Import response:', response)
    }

    console.log(`Successfully synced ${totalProcessed} accounts to Typesense`)
  } catch (error) {
    console.error('Error syncing accounts to Typesense:', error)
    throw error
  }
}
