import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MessageChangedEvent } from '@slack/types';
import { Repository } from 'typeorm';
import {
  CustomerContacts,
  GroupedSlackMessages,
  Installations,
  SlackMessages,
  Users,
} from '../../../../../database/entities';
import { ChannelsRepository } from '../../../../../database/entities/channels/repositories';
import { GroupedSlackMessagesRepository } from '../../../../../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { Person } from '../../../../../database/interfaces/person.interface';
import { ThenaPlatformApiProvider } from '../../../../../external/provider/thena-platform-api.provider';
import { CommentMetadata } from '../../../../../platform/type-system/events';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../../utils';
import { BaseSlackBlocksToHtml } from '../../../../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { SlackEventMap } from '../../../interface';

@Injectable()
export class MessageChangedHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Repositories
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
    @InjectRepository(CustomerContacts)
    private readonly customersRepository: Repository<CustomerContacts>,
    private readonly channelRepository: ChannelsRepository,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly groupedSlackMessagesRepository: GroupedSlackMessagesRepository,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,

    // Parsers
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,
  ) {}

  async handle(e: SlackEventMap['message']) {
    const { event, context } = e;
    const { installation } = context;

    // Validate that the event has a subtype and that it is a message_changed event
    if (event?.subtype !== 'message_changed') {
      const msg = `Subtype ${event.subtype} not supported, encountered for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    // If the message is a bot message, skip it
    if ('message' in event && event.message.subtype === 'bot_message') {
      this.logger.debug(
        `Bot message event received for ${installation.teamId}, skipping...`,
      );
      return;
    }

    // Validate that the event has a message
    if (!('message' in event)) {
      const msg = `Message not found in event, encountered for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    // Get top-level message details for slack
    const { channel, message } = event;
    const { ts } = message;

    let threadTs: string | undefined;
    if ('thread_ts' in message) {
      threadTs = message.thread_ts;
    }

    // Get the slack channel
    const slackChannel = await this.channelRepository.findByCondition({
      where: { channelId: channel, installation: { id: installation.id } },
    });

    // If the channel is not found, throw an error
    if (!slackChannel) {
      const msg = `Channel not found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    this.logger.log(
      `Message changed event received for ${installation.teamId}, channel: ${slackChannel.id}, threadTs: ${threadTs}, ts: ${ts}`,
    );

    // Get the related slack message
    const slackMessage = await this.getRelatedSlackMessage(
      installation,
      slackChannel.id,
      threadTs ?? ts,
    );

    // If no slack message is found, skip processing
    if (!slackMessage) {
      this.logger.debug(
        `No related slack message found for ${installation.teamId}, skipping message changed event`,
      );
      return;
    }

    this.logger.log(
      `Related slack message found for ${installation.teamId}, slackMessage: ${slackMessage.id}`,
    );

    // Get the updated platform comment ID
    const updatedCommentId = await this.getPlatformCommentId(
      installation,
      slackMessage,
      ts,
    );

    this.logger.log(
      `Updated platform comment ID found for ${installation.teamId}, updatedCommentId: ${updatedCommentId}`,
    );

    // Update the comment on the platform
    await this.updateComment(installation, updatedCommentId, event);

    this.logger.log(
      `Comment updated on the platform for ${installation.teamId}, updatedCommentId: ${updatedCommentId}`,
    );
  }

  /**
   * Update the comment on the platform
   * @param installation Installation
   * @param commentId Comment ID
   * @param event Message event
   * @returns Updated comment
   */
  private async updateComment(
    installation: Installations,
    commentId: string,
    event: MessageChangedEvent,
  ) {
    if (!('text' in event.message)) {
      throw new Error('Message event does not contain text');
    }

    if (!('blocks' in event.message)) {
      throw new Error('Blocks are required for comments');
    }

    let user: string | undefined;
    if ('user' in event.message) {
      user = event.message.user;
    }

    // Get the user
    const slackUser = await this.getUser(installation, user);

    // Initialize the converter with the blocks and installation
    this.baseSlackBlocksToHtml.initialize(event.message.blocks, installation);

    // Convert blocks to HTML content
    const htmlContent = await this.baseSlackBlocksToHtml.convert();

    // Update the comment on the platform
    const updatedComment = await this.thenaPlatformApiProvider.updateComment(
      installation,
      {
        content: event.message.text,
        htmlContent,
        commentId,
        commentAs: slackUser.slackProfileEmail,
      },
    );

    return updatedComment;
  }

  private async getUser(
    installation: Installations,
    userId: string,
  ): Promise<Person> {
    const [user, customer] = await Promise.all([
      // Get the user if its a user
      this.usersRepository.findOne({
        where: {
          slackId: userId,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      }),

      // Get the customer if its a customer
      this.customersRepository.findOne({
        where: { slackId: userId, installation: { id: installation.id } },
      }),
    ]);

    const person = customer ?? user;
    if (!person) {
      const msg = `User or customer not found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    return person;
  }

  /**
   * Get the related slack message
   * @param installation Installation
   * @param channelId Channel ID
   * @param threadTs Thread timestamp
   * @returns Slack message
   */
  private async getRelatedSlackMessage(
    installation: Installations,
    channelId: string,
    threadTs: string,
  ): Promise<SlackMessages | GroupedSlackMessages | null> {
    // Validate that the threadTs is provided
    if (!threadTs) {
      const msg = `Thread timestamp not found, encountered for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    const commonWhereClause = {
      channel: { id: channelId },
      installation: { id: installation.id },
    };

    // Get the slack message
    const slackMessage = await this.slackMessagesRepository.findByCondition({
      where: [
        // Check if its a direct message
        { ...commonWhereClause, slackMessageTs: threadTs },

        // Try to get the related top-level if this message is a threaded message
        { ...commonWhereClause, slackMessageThreadTs: threadTs },
      ],
    });

    // If a message is found, return it
    if (!slackMessage) {
      const msg = `No related slack message found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);

      const possibleGroupedMessage =
        await this.groupedSlackMessagesRepository.findByCondition({
          where: {
            slackMessageTs: threadTs,
            channel: { id: channelId },
          },
        });

      if (possibleGroupedMessage) {
        return possibleGroupedMessage;
      }

      throw new Error(msg);
    }

    return slackMessage;
  }

  /**
   * Get the platform comment ID
   * @param installation Installation
   * @param slackMessage Slack message
   * @param ts Thread timestamp
   * @returns Platform comment ID
   */
  private async getPlatformCommentId(
    installation: Installations,
    slackMessage: SlackMessages | GroupedSlackMessages,
    ts: string,
  ) {
    // Get the platform comment ID
    const parentCommentId =
      'platformCommentId' in slackMessage
        ? slackMessage.platformCommentId
        : slackMessage.parentCommentId;
    if (!parentCommentId) {
      const msg = `No platform comment ID found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    // If the threadTs is the same as the slack message timestamp, return the parent comment ID
    if (ts === slackMessage.slackMessageTs) {
      this.logger.debug(
        `Thread timestamp is the same as the slack message timestamp, returning parent comment ID: ${parentCommentId}`,
      );
      return parentCommentId;
    }

    // Get the thread comments on the platform thread
    const threads = await this.thenaPlatformApiProvider.getCommentThreads(
      installation,
      parentCommentId,
    );

    // Find the thread comment that matches the threadTs
    const commentFound = threads.find((th) => {
      const thMeta = th.metadata as CommentMetadata;
      const slackTs = thMeta?.external_sinks?.slack?.ts;
      return slackTs === ts;
    });

    // If no comment is found, throw an error
    if (!commentFound) {
      const msg = `No thread comment found for ${installation.teamId}, skipping...`;
      this.logger.debug(msg);
      throw new Error(msg);
    }

    return commentFound.id;
  }
}
