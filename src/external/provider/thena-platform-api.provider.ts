import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { FileShareMessageEvent } from '@slack/types';
import { WebClient } from '@slack/web-api';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { Installations, Organizations } from '../../database/entities';
import { Ticket } from '../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger, safeJsonStringify } from '../../utils';
import {
  Account,
  CreateAccount,
  CreateCustomField,
  CreateNewComment,
  CreateNewTicket,
  CustomField,
  SearchCustomField,
  UpdateAccount,
  UpdateTicketData,
} from './interfaces';
import { UpdateComment } from './interfaces/comments.interface';
import {
  AddCustomObjectField,
  CreateCustomObject,
  CreateCustomObjectRecord,
  CustomObjectField,
  CustomObjectRecord,
  SearchCustomObject,
  UpdateCustomObjectRecord,
} from './interfaces/custom-objects.interface';
import { CustomObject } from './interfaces/custom-objects.interface';
import {
  CreateCustomerContactDTO,
  IngestCustomerContactDTO,
  PlatformCustomerContact,
  UpdateCustomerContactDTO,
} from './interfaces/customer-contacts.interface';

// @ts-ignore
import Link from '@tiptap/extension-link';
// @ts-ignore
import Mention from '@tiptap/extension-mention';
// @ts-ignore
import Placeholder from '@tiptap/extension-placeholder';
// @ts-ignore
import { generateJSON } from '@tiptap/html';
// @ts-ignore
import StarterKit from '@tiptap/starter-kit';

const LOG_SPAN = '[THENA_PLATFORM_API_PROVIDER]';

interface LinkUsersToPlatformPayload {
  externalType: 'slack';
  details: {
    email: string;
    slackSinkDetails: {
      id: string;
      teamId: string;
    };
  }[];
}

@Injectable()
export class ThenaPlatformApiProvider {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Get API key
   * @param organization Organization
   * @returns API key
   */
  private getApiKey(organization: Organizations) {
    return organization.apiKey;
  }

  /**
   * Proxy request through Annotator API
   * @param organization Organization
   * @param method Method
   * @param path Path
   * @param body Body
   * @returns Response
   */
  private proxyAnnotator<T>(
    organization: Organizations,
    method: string,
    path: string,
    body?: T extends object ? T : never,
  ): Promise<Response> {
    // Get API key from organization
    const apiKey = this.getApiKey(organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Construct the base URL
    const baseUrl = this.configService.get(ConfigKeys.ANNOTATOR_API_URL);

    // Construct the payload
    const payload: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-org-id': organization.uid,
        'x-api-key': apiKey,
      },
    };

    // Add body if it exists
    if (body) {
      payload.body = JSON.stringify(body);
    } else if (method !== 'GET') {
      payload.body = JSON.stringify({});
    }

    // Ensure proper URL construction by removing trailing/leading slashes
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
    const cleanPath = path.replace(/^\/+/, '');
    const url = `${cleanBaseUrl}/${cleanPath}`;

    // Generate the request
    return fetch(url, payload);
  }

  /**
   * Proxy request
   * @param organization Organization
   * @param method Method
   * @param path Path
   * @param body Body
   * @returns Response
   */
  proxy<T>(
    organization: Organizations,
    method: string,
    path: string,
    body?: T extends object ? T : never,
  ): Promise<Response> {
    // Get API key from organization
    const apiKey = this.getApiKey(organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Construct the base URL
    const baseUrl = this.configService.get(ConfigKeys.PLATFORM_API_URL);

    // Construct the payload
    const payload: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-org-id': organization.uid,
        'x-api-key': apiKey,
        'x-request-source': 'slack',
      },
    };

    // Add body if it exists
    if (body) {
      payload.body = JSON.stringify(body);
    } else if (method !== 'GET') {
      payload.body = JSON.stringify({});
    }

    // Ensure proper URL construction by removing trailing/leading slashes
    const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
    const cleanPath = path.replace(/^\/+/, '');
    const url = `${cleanBaseUrl}/${cleanPath}`;

    this.logger.log(`${LOG_SPAN} Making ${method} request to ${url}`);

    // Generate the request with timeout
    return fetch(url, {
      ...payload,
      signal: AbortSignal.timeout(10000), // 10 second timeout
    }).catch((error) => {
      // Safely stringify the error with a fallback to prevent circular reference issues
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown network error';
      const errorDetails = safeJsonStringify(error, {
        fallback: `Network error: ${errorMessage}`,
        handleCircular: true,
        maxDepth: 5,
      });

      this.logger.error(
        `${LOG_SPAN} Network error for ${method} ${url}: ${errorDetails}`,
      );
      throw new Error(`Network error: ${errorMessage}`);
    });
  }

  /**
   * Create new ticket
   * @param installation Installation
   * @param data Create new ticket data
   */
  async createNewTicket(installation: Installations, data: CreateNewTicket) {
    try {
      // Get API key from organization
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      this.logger.log(
        `${LOG_SPAN} Creating ticket in platform with data: ${JSON.stringify(data)}`,
      );

      // Raw ticket response
      const ticketResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/tickets',
        {
          requestorEmail: data.requestorEmail,
          title: data.title,
          description: data.description,
          teamId: data.teamId,
          performRouting: !!data.subTeamId,
          priorityId: data.urgency,
          metadata: {
            slackTeamId: installation.teamId,
            slack: {
              channel: data.metadata.slack.channel,
              ts: data.metadata.slack.ts,
              user: data.metadata.slack.user,
            },
          },
        },
      );

      // If the ticket was not created, throw an error
      if (!ticketResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [createNewTicket] Failed to create ticket in platform!`,
        );
        const ticketJson = await ticketResponse.json();
        throw new Error(ticketJson.message);
      }

      // Parse the ticket response
      const ticket = await ticketResponse.json();
      return ticket.data as Ticket;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [createNewTicket] Error creating ticket: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Link users to platform
   * @param installation Installation
   * @param payload Link users to platform payload
   * @returns Linked users
   */
  async linkUsersToPlatform(
    installation: Installations,
    payload: LinkUsersToPlatformPayload,
  ) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const linkUsersToPlatformResponse = await this.proxy(
        installation.organization,
        'PATCH',
        '/v1/users/ingest/link-external',
        payload,
      );

      // If the request failed, throw an error
      if (!linkUsersToPlatformResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [linkUsersToPlatform] Failed to link users to platform!`,
        );
        const linkUsersToPlatformJson =
          await linkUsersToPlatformResponse.json();
        throw new Error(linkUsersToPlatformJson.message);
      }

      // Parse the response
      const linkUsersToPlatform = await linkUsersToPlatformResponse.json();
      return linkUsersToPlatform.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [linkUsersToPlatform] Error linking users to platform: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Get entity details
   * @param installation Installation
   * @param identifier Identifier
   * @param entityId Entity ID
   * @param entityType Entity type
   */
  async getEntityDetails(
    installation: Installations,
    identifier: string,
    entityId: string,
    entityType: string,
  ) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Get entity details
      const entityResponse = await this.proxyAnnotator(
        installation.organization,
        'POST',
        '/v1/annotator/data',
        {
          entityType,
          data: { [identifier]: entityId },
          relations: [
            'account',
            'team',
            'subTeam',
            'priority',
            'sentiment',
            'status',
            'type',
            'customFieldValues',
            'assignedAgent',
            'customerContact',
          ],
        },
      );

      // If the request failed, throw an error
      const entity = await entityResponse.json();
      if (!entityResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getEntityDetails] Failed to get entity details!`,
        );
        throw new Error(entity.message);
      }

      return entity.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getEntityDetails] Error getting entity details: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  private async uploadToPlatform(
    installation: Installations,
    files: FileShareMessageEvent['files'],
  ) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Create the slack web api client
      const slackClient = new WebClient(installation.botToken, {
        rejectRateLimitedCalls: true,
      });

      // Upload the files to the platform
      const uploadPromises = files
        .map(async (file) => {
          let fileInfo = file;

          // If the file is external we need to pull data
          // @see https://api.slack.com/events/message/file_share
          if ((file as any).file_access === 'check_file_info') {
            const fileInfoResponse = await slackClient.files.info({
              file: file.id,
            });

            if (fileInfoResponse.ok) {
              fileInfo = fileInfoResponse.file as any;
            }
          }

          // Get the file url
          const fileUrl = fileInfo.url_private_download;
          if (!fileUrl) {
            return null;
          }

          // Get the file data
          const fileDataResponse = await fetch(fileUrl, {
            headers: {
              Authorization: `Bearer ${installation.botToken}`,
            },
          });

          // If the file data was not fetched, return null
          if (!fileDataResponse.ok) {
            return null;
          }

          // Get the file blob
          const fileBlob = await fileDataResponse.blob();

          // Determine the correct MIME type based on file extension
          const mimeType = fileInfo.mimetype;

          // Create form data with proper MIME type
          const formData = new FormData();
          const fileName = fileInfo.name || 'slack_file';
          formData.append(
            'files',
            new File([fileBlob], fileName, { type: mimeType }),
            fileName,
          );

          const baseUrl = this.configService.get(ConfigKeys.PLATFORM_API_URL);
          const payload: RequestInit = {
            method: 'POST',
            headers: {
              'x-org-id': installation.organization.uid,
              'x-api-key': apiKey,
              'x-request-source': 'slack',
              accept: '*/*',
            },
            body: formData,
          };

          const cleanBaseUrl = baseUrl.replace(/\/+$/, '');
          const cleanPath = 'storage/upload-file';
          const url = `${cleanBaseUrl}/${cleanPath}`;

          const uploadResponse = await fetch(url, payload);

          const uploadResponseJson = await uploadResponse.json();
          return uploadResponseJson.data;
        })
        .filter(Boolean);

      const result = await Promise.all(uploadPromises);
      return result;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [uploadToPlatform] Error uploading files: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Create new comment
   * @param installation Installation
   * @param data Create new comment data
   */
  async createNewComment(installation: Installations, data: CreateNewComment) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Construct the slack thread link
      let slackThreadLink = `slack://channel?team=${installation.teamId}&id=${data.channelId}`;
      if (data.metadata.threadTs && data.metadata.ts) {
        slackThreadLink += `&message=${data.metadata.ts}&thread_ts=${data.metadata.threadTs}`;
      } else if (data.metadata.ts) {
        slackThreadLink += `&message=${data.metadata.ts}&thread_ts=${data.metadata.ts}`;
      }

      // Convert the html content to json
      const contentJson = generateJSON(data.content ?? '', [
        StarterKit.configure({
          heading: false,
        }),
        Link,
        Mention,
        Placeholder,
      ]);

      // Get the files from the event and upload them to the platform
      const files = data.files;
      const uploadedFiles = await this.uploadToPlatform(installation, files);

      const attachmentIds = uploadedFiles.map((data) => data?.data?.uid);

      // Construct the payload
      const payload = {
        content: data.content,
        contentHtml: data.htmlContent ?? data.content,
        attachmentIds,
        contentJson: JSON.stringify(contentJson),
        parentCommentId: data.parentCommentId,
        commentVisibility: data.commentVisibility,
        metadata: {
          external_sinks: {
            slack: {
              ignoreSelf: data.metadata.ignoreSelf,
              threadTs: data.metadata?.threadTs,
              ts: data.metadata.ts,
              slackThreadLink,
            },
          },
        },
        impersonatedUserEmail: data.impersonatedUserEmail,
        impersonatedUserName: data.impersonatedUserName,
        impersonatedUserAvatar: data.impersonatedUserAvatar,
      };

      // Raw comment response
      const commentResponse = await this.proxy(
        installation.organization,
        'POST',
        `/v1/tickets/${data.ticketId}/comment`,
        payload,
      );

      // If the comment was not created, throw an error
      if (!commentResponse.ok) {
        const commentJson = await commentResponse.json();
        this.logger.error(
          `${LOG_SPAN} [createNewComment] Failed to create comment in platform!`,
          commentJson,
        );
        throw new Error(commentJson.message);
      }

      // Parse the comment response
      const comment = await commentResponse.json();
      return comment as { data: { id: string } };
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [createNewComment] Error creating comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Set slack auth
   * @param installation Installation
   * @param userEmail User email
   * @returns Set slack auth response
   */
  async setSlackAuth(
    installation: Installations,
    userEmail: string,
    unset?: boolean,
  ) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const encodedEmail = userEmail.replace(' ', '+');

      // Construct the url
      let url = `/v1/users/metadata/slack-auth?userEmail=${encodedEmail}&teamId=${installation.teamId}`;
      if (unset) {
        url = `/v1/users/metadata/slack-auth?userEmail=${encodedEmail}&teamId=${installation.teamId}&unset=true`;
      }

      // Set slack auth
      const setSlackAuthResponse = await this.proxy(
        installation.organization,
        'PATCH',
        url,
      );

      // If the request failed, throw an error
      if (!setSlackAuthResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [setSlackAuth] Failed to set slack auth!`,
        );
        const setSlackAuthJson = await setSlackAuthResponse.json();
        throw new Error(setSlackAuthJson.message);
      }

      return setSlackAuthResponse.json();
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [setSlackAuth] Error setting slack auth: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Fetch forms for team
   *
   * NOTE: The API is paginated means you'll have a `results` array
   * @param installation Installation
   * @param teamId Team ID
   * @returns Forms
   */
  async fetchFormsForTeam(installation: Installations, teamId: string) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Get forms from platform
      const formsResponse = await this.proxy(
        installation.organization,
        'GET',
        `/v1/forms?onlyTeamForms=true&teamId=${teamId}`,
      );

      // If the request failed, throw an error
      if (!formsResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [fetchFormsForTeam] Failed to fetch forms for team!`,
        );
        const formsJson = await formsResponse.json();
        throw new Error(formsJson.message);
      }

      // Parse the response
      const forms = await formsResponse.json();
      return forms.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [fetchFormsForTeam] Error fetching forms: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Update comment
   * @param installation Installation
   * @param data Update comment data
   */
  async updateComment(installation: Installations, data: UpdateComment) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Construct the payload
      const payload = {
        content: data.content,
        contentHtml: data.htmlContent,
      };

      // Raw comment response
      const commentResponse = await this.proxy(
        installation.organization,
        'PATCH',
        `/v1/comments/${data.commentId}`,
        payload,
      );

      // If the comment was not created, throw an error
      if (!commentResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [updateComment] Failed to update comment in platform!`,
        );
        const commentJson = await commentResponse.json();
        throw new Error(commentJson.message);
      }

      // Parse the comment response
      const comment = await commentResponse.json();
      return comment as { data: { id: string } };
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [updateComment] Error updating comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Update comment with metadata
   * @param installation Installation
   * @param commentId Comment ID to update
   * @param metadata Metadata object containing external_sinks
   */
  async updateCommentWithMetadata(
    installation: Installations,
    commentId: string,
    metadata: {
      external_sinks: {
        slack: {
          ignoreSelf: boolean;
          threadTs: string;
          ts: string;
          slackThreadLink: string;
        };
      };
      skipSlackContentUpdate?: boolean;
    },
  ) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Construct the payload with metadata - ensure it matches the structure expected by the platform API
      const { ignoreSelf, threadTs, ts, slackThreadLink } =
        metadata.external_sinks.slack;
      const payload: any = {
        metadata: {
          external_sinks: {
            slack: {
              ignoreSelf,
              threadTs,
              ts,
              slackThreadLink,
            },
          },
        },
      };

      // Add skipSlackContentUpdate flag if provided
      if (metadata.skipSlackContentUpdate) {
        payload.metadata.skipSlackContentUpdate = true;
      }

      this.logger.debug(
        `${LOG_SPAN} [updateCommentWithMetadata] Updating comment ${commentId} with payload: ${JSON.stringify(payload)}`,
      );

      // Raw comment response
      const commentResponse = await this.proxy(
        installation.organization,
        'PATCH',
        `/v1/comments/${commentId}`,
        payload,
      );

      // If the comment metadata was not updated, throw an error
      if (!commentResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [updateCommentWithMetadata] Failed to update comment metadata in platform!`,
        );
        let errorMessage = 'Unknown error';
        try {
          const commentJson = await commentResponse.json();
          errorMessage =
            commentJson.message || `HTTP ${commentResponse.status}`;
        } catch {
          // If we can't parse the error response, use the status text
          errorMessage = `HTTP ${commentResponse.status}: ${commentResponse.statusText}`;
        }
        throw new Error(errorMessage);
      }

      // Parse the comment response
      const comment = await commentResponse.json();
      return comment as { data: { id: string } };
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [updateCommentWithMetadata] Error updating comment metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Delete comment
   * @param installation Installation
   * @param commentId Comment ID
   */
  async deleteComment(installation: Installations, commentId: string) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Get organization
      const organization = installation.organization;

      // Proxy the request
      const deleteCommentResponse = await this.proxy(
        organization,
        'DELETE',
        `/v1/comments/${commentId}`,
      );

      // If the request failed, throw an error
      if (!deleteCommentResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [deleteComment] Failed to delete comment in platform!`,
        );
        const deleteCommentJson = await deleteCommentResponse.json();
        throw new Error(deleteCommentJson.message);
      }

      // Parse the response
      return { ok: true };
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [deleteComment] Error deleting comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Get teams
   * @param installation Installation
   * @returns Teams
   */
  async getTeams(installation: Installations) {
    // Get API key from organization
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    // Get organization
    const organization = installation.organization;

    try {
      this.logger.log(
        `${LOG_SPAN} [getTeams] Fetching teams for organization ${organization.id}`,
      );

      // Fetch teams from platform
      const getTeamsResponse = await this.proxy<any>(
        organization,
        'GET',
        'v1/teams',
      );

      if (!getTeamsResponse.ok) {
        const errorText = await getTeamsResponse.text();
        this.logger.error(
          `${LOG_SPAN} [getTeams] API returned error status ${getTeamsResponse.status}: ${errorText}`,
        );
        throw new Error(`Teams API returned status ${getTeamsResponse.status}`);
      }

      const responseJson = await getTeamsResponse.json();

      if (!responseJson.data || !Array.isArray(responseJson.data)) {
        this.logger.error(
          `${LOG_SPAN} [getTeams] Unexpected API response format: ${safeJsonStringify(responseJson, { fallback: 'Invalid response format' })}`,
        );
        throw new Error('Teams API returned unexpected data format');
      }

      this.logger.log(
        `${LOG_SPAN} [getTeams] Successfully fetched ${responseJson.data.length} teams`,
      );
      const teams = responseJson.data.map((t: any) => ({
        id: t.id,
        name: t.name,
      }));

      return teams;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getTeams] Error fetching teams: ${safeJsonStringify(error, { fallback: 'Unknown error fetching teams' })}`,
      );
      throw new Error(
        `Failed to fetch teams: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get statuses for team
   * @param installation Installation
   * @param teamId Team ID
   * @returns Statuses
   */
  async getStatusesForTeam(installation: Installations, teamId: string) {
    try {
      // Get API key from organization
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const getStatusesResponse = await this.proxy(
        installation.organization,
        'GET',
        `/v1/tickets/status?teamId=${teamId}`,
      );

      // If the request failed, log error and return empty array
      if (!getStatusesResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getStatusesForTeam] Failed to get statuses for team!`,
        );
        const statusesJson = await getStatusesResponse.json();
        this.logger.error(
          `${LOG_SPAN} [getStatusesForTeam] Status API error: ${statusesJson.message}`,
        );
        return [];
      }

      // Parse the response
      const statuses = await getStatusesResponse.json();
      return statuses.data;
    } catch (error) {
      // Log the error but don't fail operations that depend on statuses
      this.logger.error(
        `${LOG_SPAN} [getStatusesForTeam] Error getting statuses for team: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      return [];
    }
  }

  /**
   * Get priorities for team
   * @param installation Installation
   * @param teamId Team ID
   * @returns Priorities
   */
  async getPrioritiesForTeam(installation: Installations, teamId: string) {
    try {
      // Get API key from organization
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const getPrioritiesResponse = await this.proxy(
        installation.organization,
        'GET',
        `/v1/tickets/priority?teamId=${teamId}`,
      );

      // If the request failed, log error and return empty array
      if (!getPrioritiesResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getPrioritiesForTeam] Failed to get priorities for team!`,
        );
        const prioritiesJson = await getPrioritiesResponse.json();
        this.logger.error(
          `${LOG_SPAN} [getPrioritiesForTeam] Priority API error: ${prioritiesJson.message}`,
        );
        return [];
      }

      // Parse the response
      const priorities = await getPrioritiesResponse.json();
      return priorities.data;
    } catch (error) {
      // Log the error but don't fail the ticket creation process
      this.logger.error(
        `${LOG_SPAN} [getPrioritiesForTeam] Error getting priorities for team: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      return [];
    }
  }

  /**
   * Update ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @param data Update ticket data
   * @returns Updated ticket
   */
  async updateTicket(
    installation: Installations,
    ticketId: string,
    data: Partial<UpdateTicketData>,
  ) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const updateTicketResponse = await this.proxy(
        installation.organization,
        'PATCH',
        `/v1/tickets/${ticketId}`,
        data,
      );

      // If the request failed, throw an error
      if (!updateTicketResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [updateTicket] Failed to update ticket!`,
        );
        const updateTicketJson = await updateTicketResponse.json();
        throw new Error(updateTicketJson.message);
      }

      // Parse the response
      const ticket = await updateTicketResponse.json();
      return ticket as { data: { id: string } };
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [updateTicket] Error updating ticket: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Get ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @returns Ticket
   */
  async getTicket(installation: Installations, ticketId: string) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const getTicketResponse = await this.proxy(
        installation.organization,
        'GET',
        `/v1/tickets/${ticketId}`,
      );

      if (!getTicketResponse.ok) {
        this.logger.error(`${LOG_SPAN} [getTicket] Failed to get ticket!`);
        const getTicketJson = await getTicketResponse.json();
        throw new Error(getTicketJson.message);
      }

      // Parse the response and return the ticket data
      const ticket = await getTicketResponse.json();
      return ticket.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getTicket] Error getting ticket: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Get team members
   * @param installation Installation
   * @param teamId Team ID
   * @param searchQuery Search query
   * @returns Team members
   */
  async getTeamMembers(
    installation: Installations,
    teamId: string,
    searchQuery?: string,
  ) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      const apiUrl = searchQuery
        ? `/v1/teams/${teamId}/members?searchQuery=${searchQuery}`
        : `/v1/teams/${teamId}/members`;
      // Proxy the request
      const getTeamMembersResponse = await this.proxy(
        installation.organization,
        'GET',
        apiUrl,
      );

      if (!getTeamMembersResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getTeamMembers] Failed to get team members!`,
        );
        const getTeamMembersJson = await getTeamMembersResponse.json();
        throw new Error(getTeamMembersJson.message);
      }

      // Parse the response
      const teamMembers = await getTeamMembersResponse.json();
      return teamMembers.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getTeamMembers] Error getting team members: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Close ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @param ticketTeamId Ticket Team ID
   * @returns Closed ticket
   */
  async closeTicket(
    installation: Installations,
    ticketId: string,
    ticketTeamId: string,
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      throw new Error('Organization has no API key');
    }

    try {
      // Get all the status
      const statuses = await this.getStatusesForTeam(
        installation,
        ticketTeamId,
      );

      // Get parent closed status
      const closedStatus = statuses.find(
        (s: any) => s.name === 'Closed' && !s.parentStatusId,
      );

      if (!closedStatus) {
        this.logger.error(
          `${LOG_SPAN} [closeTicket] No closed status found for team ${ticketTeamId}`,
        );
        throw new Error('No closed status found for team');
      }

      // Update the ticket status
      await this.updateTicket(installation, ticketId, {
        statusId: closedStatus.id,
      });
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [closeTicket] Failed to close ticket ${ticketId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error; // Re-throw as this is a critical operation
    }
  }

  /**
   * Archive ticket
   * @param installation Installation
   * @param ticketId Ticket ID
   * @returns Archived ticket
   */
  async archiveTicket(installation: Installations, ticketId: string) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const archiveTicketResponse = await this.proxy(
        installation.organization,
        'PATCH',
        `/v1/tickets/${ticketId}/archive`,
      );

      if (!archiveTicketResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [archiveTicket] Failed to archive ticket!`,
        );
        const archiveTicketJson = await archiveTicketResponse.json();
        throw new Error(archiveTicketJson.message);
      }

      // Parse the response
      const ticket = await archiveTicketResponse.json();
      return ticket as { data: { id: string } };
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [archiveTicket] Error archiving ticket: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Create custom field
   * @param installation Installation
   * @param data {@link CreateCustomField} Create custom field data
   * @returns Created custom field {@link CustomField}
   */
  async createCustomField(
    installation: Installations,
    data: CreateCustomField,
  ): Promise<CustomField> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const createCustomFieldResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/custom-field',
        data,
      );

      if (!createCustomFieldResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [createCustomField] Failed to create custom field!`,
        );
        const createCustomFieldJson = await createCustomFieldResponse.json();
        throw new Error(createCustomFieldJson.message);
      }

      // Parse the response
      const customField = await createCustomFieldResponse.json();
      return customField.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [createCustomField] Error creating custom field: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Search custom field
   * @param installation Installation
   * @param data {@link SearchCustomField} Search custom field data
   * @returns Searched custom field {@link CustomField}
   */
  async searchCustomField(
    installation: Installations,
    data: SearchCustomField,
  ): Promise<CustomField[]> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      let searchParams = `name=${data.name}`;
      if (data.teamId) {
        searchParams += `&teamId=${data.teamId}`;
      }

      // Proxy the request
      const searchCustomFieldResponse = await this.proxy(
        installation.organization,
        'GET',
        `v1/custom-field/search?${searchParams}`,
      );

      if (!searchCustomFieldResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [searchCustomField] Failed to search custom field!`,
        );
        const searchCustomFieldJson = await searchCustomFieldResponse.json();
        throw new Error(searchCustomFieldJson.message);
      }

      // Parse the response
      const customField = await searchCustomFieldResponse.json();
      return customField.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [searchCustomField] Error searching custom field: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Filter accounts by domains
   * @param installation Installation
   * @param domains Domains
   * @returns Accounts {@link Account[]}
   */
  async getAccountsByDomains(
    installation: Installations,
    domains: string[],
  ): Promise<Account[]> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const getAccountsByDomainsResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/accounts/filter/primary-domains',
        {
          primaryDomains: domains,
        },
      );

      if (!getAccountsByDomainsResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getAccountsByDomains] Failed to get accounts by domains!`,
        );
        const getAccountsByDomainsJson =
          await getAccountsByDomainsResponse.json();
        throw new Error(getAccountsByDomainsJson.message);
      }

      // Parse the response
      const accounts = await getAccountsByDomainsResponse.json();
      return accounts.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getAccountsByDomains] Error getting accounts by domains: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Filter accounts by IDs
   * @param installation Installation
   * @param ids IDs
   * @returns Accounts {@link Account[]}
   */
  async getAccountsByIds(
    installation: Installations,
    ids: string[],
  ): Promise<Account[]> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const getAccountsByIdsResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/accounts/filter/ids',
        { ids },
      );

      if (!getAccountsByIdsResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getAccountsByIds] Failed to get accounts by IDs!`,
        );
        const getAccountsByIdsJson = await getAccountsByIdsResponse.json();
        throw new Error(getAccountsByIdsJson.message);
      }

      // Parse the response
      const accounts = await getAccountsByIdsResponse.json();
      return accounts.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getAccountsByIds] Error getting accounts by IDs: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Get comment threads
   * @param installation Installation
   * @param commentId Comment ID
   * @returns Comment threads
   */
  async getCommentThreads(installation: Installations, commentId: string) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const getCommentThreadsResponse = await this.proxy(
        installation.organization,
        'GET',
        `/v1/comments/${commentId}/threads`,
      );

      // If the request failed, throw an error
      if (!getCommentThreadsResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getCommentThreads] Failed to get comment threads!`,
        );
        const getCommentThreadsJson = await getCommentThreadsResponse.json();

        // If the comment was not found, return an empty array
        if (getCommentThreadsJson.statusCode === HttpStatus.NOT_FOUND) {
          return [];
        }

        throw new Error(getCommentThreadsJson.message);
      }

      // Parse the response
      const threads = await getCommentThreadsResponse.json();
      return threads.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getCommentThreads] Error getting comment threads: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Create account
   * @param installation Installation
   * @param data {@link CreateAccount} Create account data
   * @returns Created account {@link Account}
   */
  async createAccount(
    installation: Installations,
    data: CreateAccount,
  ): Promise<Account> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const createAccountResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/accounts',
        data,
      );

      if (!createAccountResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [createAccount] Failed to create account!`,
        );
        const createAccountJson = await createAccountResponse.json();
        throw new Error(createAccountJson.message);
      }

      // Parse the response
      const account = await createAccountResponse.json();
      return account.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [createAccount] Error creating account: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Create accounts
   * @param installation Installation
   * @param data {@link CreateAccount[]} Create accounts data
   * @returns Created accounts {@link Account[]}
   */
  async createAccounts(
    installation: Installations,
    data: CreateAccount[],
  ): Promise<Account[]> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const createAccountsResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/accounts/bulk',
        data,
      );

      if (!createAccountsResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [createAccounts] Failed to create accounts!`,
        );
        const createAccountsJson = await createAccountsResponse.json();
        throw new Error(createAccountsJson.message);
      }

      // Parse the response
      const accounts = await createAccountsResponse.json();
      return accounts.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [createAccounts] Error creating accounts: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Update account
   * @param installation Installation
   * @param accountId Account ID
   * @param data {@link UpdateAccount} Update account data
   * @returns Updated account {@link Account}
   */
  async updateAccount(
    installation: Installations,
    accountId: string,
    data: UpdateAccount,
  ): Promise<Account> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const updateAccountResponse = await this.proxy(
        installation.organization,
        'PUT',
        `/v1/accounts/${accountId}`,
        data,
      );

      if (!updateAccountResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [updateAccount] Failed to update account!`,
        );
        const updateAccountJson = await updateAccountResponse.json();
        throw new Error(updateAccountJson.message);
      }

      // Parse the response
      const account = await updateAccountResponse.json();
      return account.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [updateAccount] Error updating account: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Create custom object
   * @param installation Installation
   * @param data {@link CreateCustomObject} Create custom object data
   * @returns Created custom object {@link CustomObject}
   */
  async createCustomObject(
    installation: Installations,
    data: CreateCustomObject,
  ): Promise<CustomObject> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const createCustomObjectResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/custom-object',
        data,
      );

      if (!createCustomObjectResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [createCustomObject] Failed to create custom object!`,
        );
        const createCustomObjectJson = await createCustomObjectResponse.json();
        throw new Error(createCustomObjectJson.message);
      }

      // Parse the response
      const customObject = await createCustomObjectResponse.json();
      return customObject;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [createCustomObject] Error creating custom object: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Search custom object
   * @param installation Installation
   * @param data {@link SearchCustomObject} Search custom object data
   * @returns Searched custom object {@link CustomObject}
   */
  async searchCustomObject(
    installation: Installations,
    data: SearchCustomObject,
  ): Promise<CustomObject[]> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const searchCustomObjectResponse = await this.proxy(
        installation.organization,
        'GET',
        `/v1/custom-object/search?term=${data.name}`,
      );

      if (!searchCustomObjectResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [searchCustomObject] Failed to search custom object!`,
        );
        const searchCustomObjectJson = await searchCustomObjectResponse.json();
        throw new Error(searchCustomObjectJson.message);
      }

      // Parse the response
      const customObject = await searchCustomObjectResponse.json();
      return customObject.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [searchCustomObject] Error searching custom object: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Add a custom field to a custom object
   * @param installation Installation
   * @param data {@link AddCustomObjectField} Add custom object field data
   * @returns Added custom object field {@link CustomObjectField}
   */
  async addCustomObjectField(
    installation: Installations,
    data: AddCustomObjectField,
  ): Promise<CustomObjectField> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      const { customObjectId, customFieldId, ...rest } = data;

      // Proxy the request
      const addCustomObjectFieldResponse = await this.proxy(
        installation.organization,
        'POST',
        `/v1/custom-object/${customObjectId}/fields/${customFieldId}`,
        rest,
      );

      if (!addCustomObjectFieldResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [addCustomObjectField] Failed to add custom object field!`,
        );
        const addCustomObjectFieldJson =
          await addCustomObjectFieldResponse.json();
        throw new Error(addCustomObjectFieldJson.message);
      }

      // Parse the response
      const customObjectField = await addCustomObjectFieldResponse.json();
      return customObjectField.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [addCustomObjectField] Error adding custom object field: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Create a custom object record
   * @param installation Installation
   * @param data {@link CreateCustomObjectRecord} Create custom object record data
   * @returns Created custom object record {@link CustomObjectRecord}
   */
  async createCustomObjectRecord(
    installation: Installations,
    data: CreateCustomObjectRecord,
  ): Promise<CustomObjectRecord> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const createCustomObjectRecordResponse = await this.proxy(
        installation.organization,
        'POST',
        `/v1/custom-object/${data.customObjectId}/records`,
        data,
      );

      if (!createCustomObjectRecordResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [createCustomObjectRecord] Failed to create custom object record!`,
        );
        const createCustomObjectRecordJson =
          await createCustomObjectRecordResponse.json();
        throw new Error(createCustomObjectRecordJson.message);
      }

      // Parse the response
      const customObjectRecord = await createCustomObjectRecordResponse.json();
      return customObjectRecord;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [createCustomObjectRecord] Error creating custom object record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Get custom object records by IDs
   * @param installation Installation
   * @param ids IDs
   * @returns Custom object records {@link CustomObjectRecord[]}
   */
  async getCustomObjectRecordsByIds(
    installation: Installations,
    customObjectId: string,
    ids: string[],
  ): Promise<CustomObjectRecord[]> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const getCustomObjectRecordsByIdsResponse = await this.proxy(
        installation.organization,
        'POST',
        `/v1/custom-object/${customObjectId}/records/fetchByIds`,
        { ids },
      );

      if (!getCustomObjectRecordsByIdsResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getCustomObjectRecordsByIds] Failed to get custom object records by IDs!`,
        );
        const getCustomObjectRecordsByIdsJson =
          await getCustomObjectRecordsByIdsResponse.json();
        throw new Error(getCustomObjectRecordsByIdsJson.message);
      }

      // Parse the response
      const customObjectRecords =
        await getCustomObjectRecordsByIdsResponse.json();
      return customObjectRecords.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getCustomObjectRecordsByIds] Error getting custom object records by IDs: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async updateCustomObjectRecord(
    installation: Installations,
    data: UpdateCustomObjectRecord,
  ): Promise<CustomObjectRecord> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      const { customObjectId, customObjectRecordId, customFieldValues } = data;

      // Proxy the request
      const updateCustomObjectRecordResponse = await this.proxy(
        installation.organization,
        'PATCH',
        `/v1/custom-object/${customObjectId}/records/${customObjectRecordId}`,
        { customFieldValues },
      );

      if (!updateCustomObjectRecordResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [updateCustomObjectRecord] Failed to update custom object record!`,
        );
        const updateCustomObjectRecordJson =
          await updateCustomObjectRecordResponse.json();
        throw new Error(updateCustomObjectRecordJson.message);
      }

      // Parse the response
      const customObjectRecord = await updateCustomObjectRecordResponse.json();
      return customObjectRecord.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [updateCustomObjectRecord] Error updating custom object record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Delete a custom object record
   * @param installation Installation
   * @param id ID
   * @returns Deleted custom object record {@link CustomObjectRecord}
   */
  async deleteCustomObjectRecord(
    installation: Installations,
    customObjectId: string,
    customObjectRecordId: string,
  ): Promise<CustomObjectRecord> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const deleteCustomObjectRecordResponse = await this.proxy(
        installation.organization,
        'DELETE',
        `/v1/custom-object/${customObjectId}/records/${customObjectRecordId}`,
      );

      if (!deleteCustomObjectRecordResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [deleteCustomObjectRecord] Failed to delete custom object record!`,
        );
        const deleteCustomObjectRecordJson =
          await deleteCustomObjectRecordResponse.json();
        throw new Error(deleteCustomObjectRecordJson.message);
      }

      // Parse the response
      const customObjectRecord = await deleteCustomObjectRecordResponse.json();
      return customObjectRecord.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [deleteCustomObjectRecord] Error deleting custom object record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Ingest customer contacts
   * @param installation Installation
   * @param data {@link IngestCustomerContactDTO} Ingest customer contact data
   * @returns Ingested customer contacts {@link PlatformCustomerContact[]}
   */
  async ingestCustomerContacts(
    installation: Installations,
    data: IngestCustomerContactDTO,
  ): Promise<PlatformCustomerContact[]> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const ingestCustomerContactsResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/accounts/contacts/ingest',
        data,
      );

      if (!ingestCustomerContactsResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [ingestCustomerContacts] Failed to ingest customer contacts!`,
        );
        const ingestCustomerContactsJson =
          await ingestCustomerContactsResponse.json();
        throw new Error(ingestCustomerContactsJson.message);
      }

      // Parse the response
      const ingestedCustomerContacts =
        await ingestCustomerContactsResponse.json();
      return ingestedCustomerContacts.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [ingestCustomerContacts] Error ingesting customer contacts: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Create customer contact
   * @param installation Installation
   * @param data {@link CreateCustomerContactDTO} Create customer contact data
   * @returns Created customer contact {@link PlatformCustomerContact}
   */
  async createCustomerContact(
    installation: Installations,
    data: CreateCustomerContactDTO,
  ): Promise<PlatformCustomerContact> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const createCustomerContactResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/accounts/contacts',
        data,
      );

      if (!createCustomerContactResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [createCustomerContact] Failed to create customer contact!`,
        );
        const createCustomerContactJson =
          await createCustomerContactResponse.json();
        throw new Error(createCustomerContactJson.message);
      }

      // Parse the response
      const customerContact = await createCustomerContactResponse.json();
      return customerContact.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [createCustomerContact] Error creating customer contact: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Get customer contacts by IDs
   * @param installation Installation
   * @param ids IDs
   * @returns Customer contacts {@link PlatformCustomerContact[]}
   */
  async getCustomerContactsByIds(
    installation: Installations,
    ids: string[],
  ): Promise<PlatformCustomerContact[]> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const getCustomerContactsByIdsResponse = await this.proxy(
        installation.organization,
        'POST',
        '/v1/accounts/contacts/filter/ids',
        { ids },
      );

      if (!getCustomerContactsByIdsResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [getCustomerContactsByIds] Failed to get customer contacts by IDs!`,
        );
        const getCustomerContactsByIdsJson =
          await getCustomerContactsByIdsResponse.json();
        throw new Error(getCustomerContactsByIdsJson.message);
      }

      // Parse the response
      const customerContacts = await getCustomerContactsByIdsResponse.json();
      return customerContacts.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [getCustomerContactsByIds] Error getting customer contacts by IDs: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Update customer contact
   * @param installation Installation
   * @param data {@link UpdateCustomerContactDTO} Update customer contact data
   * @returns Updated customer contact {@link PlatformCustomerContact}
   */
  async updateCustomerContact(
    installation: Installations,
    contactId: string,
    data: UpdateCustomerContactDTO,
  ): Promise<PlatformCustomerContact> {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const updateCustomerContactResponse = await this.proxy(
        installation.organization,
        'PUT',
        `/v1/accounts/contacts/${contactId}`,
        data,
      );

      if (!updateCustomerContactResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [updateCustomerContact] Failed to update customer contact!`,
        );
        const updateCustomerContactJson =
          await updateCustomerContactResponse.json();
        throw new Error(updateCustomerContactJson.message);
      }

      // Parse the response
      const customerContact = await updateCustomerContactResponse.json();
      return customerContact.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [updateCustomerContact] Error updating customer contact: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Add a reaction to a comment
   * @param installation Installation
   * @param commentId Comment ID
   * @param reactionName Reaction name
   * @returns Added reaction
   */
  async addReaction(
    installation: Installations,
    commentId: string,
    reactionName: string,
    impersonatedUserName: string,
    impersonatedUserEmail: string,
    impersonatedUserAvatar: string,
  ) {
    try {
      const apiKey = this.getApiKey(installation.organization);
      if (!apiKey) {
        throw new Error('Organization has no API key');
      }

      // Proxy the request
      const addReactionResponse = await this.proxy(
        installation.organization,
        'POST',
        `/v1/reactions/${commentId}`,
        {
          name: reactionName,
          impersonatedUserName,
          impersonatedUserEmail,
          impersonatedUserAvatar,
          metadata: { ignoreSelf: true },
        },
      );

      // If the request failed, throw an error
      if (!addReactionResponse.ok) {
        this.logger.error(
          `${LOG_SPAN} [addReaction] Failed to add reaction to comment!`,
        );
        const addReactionJson = await addReactionResponse.json();
        throw new Error(addReactionJson.message);
      }

      // Parse the response
      const reaction = await addReactionResponse.json();
      return reaction.data;
    } catch (error) {
      this.logger.error(
        `${LOG_SPAN} [addReaction] Error adding reaction: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  /**
   * Remove a reaction from a comment
   * @param installation Installation
   * @param commentId Comment ID
   * @param reactionName Reaction name
   * @returns Removed reaction response
   */
  async removeReaction(
    installation: Installations,
    commentId: string,
    reactionName: string,
    impersonatedUserEmail: string,
  ) {
    const apiKey = this.getApiKey(installation.organization);
    if (!apiKey) {
      this.logger.error(
        `[DEBUG-PLATFORM-API] [removeReaction] No API key found for organization ${installation?.organization?.uid}`,
      );
      throw new Error('Organization has no API key');
    }

    const apiUrl = `/v1/reactions/remove/${commentId}/${reactionName}?impersonatedUserEmail=${encodeURIComponent(impersonatedUserEmail)}`;

    try {
      // Proxy the request
      const removeReactionResponse = await this.proxy(
        installation.organization,
        'DELETE',
        apiUrl,
      );

      // Log the response status
      console.log(
        `[DEBUG-PLATFORM-API] [removeReaction] Remove reaction response status: ${removeReactionResponse.status}`,
      );

      // If the request failed, throw an error
      if (!removeReactionResponse.ok) {
        const removeReactionJson = await removeReactionResponse.json();
        this.logger.error(
          `[DEBUG-PLATFORM-API] [removeReaction] Failed to remove reaction: ${JSON.stringify(removeReactionJson)}`,
        );
        this.logger.error(
          `${LOG_SPAN} [removeReaction] Failed to remove reaction from comment!`,
        );
        throw new Error(
          removeReactionJson.message || 'Failed to remove reaction',
        );
      }

      // Parse the response
      const reaction = await removeReactionResponse.json();
      return reaction;
    } catch (error) {
      this.logger.error(
        `[DEBUG-PLATFORM-API] [removeReaction] Error removing reaction: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }
}
